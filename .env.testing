APP_NAME="Lightpost"
APP_ENV=local
APP_KEY=base64:enzDYEShh+3DwwgIkoD+QBeYPsd+bOs0A0t1wn4zN4Q=
APP_DEBUG=false
LOG_LEVEL=debug
LOG_CHANNEL=daily
APP_URL=https://admin.lightpost.test
COPYRIGHT_NAME="Tiny Bit Farm LLC"

PAPERTRAIL_URL=
PAPERTRAIL_PORT=

SESSION_SECURE_COOKIE=false

IOS_DOMAIN=ios.lightpost.test
APP_DOMAIN=app.lightpost.test
ADMIN_DOMAIN=admin.lightpost.test
EMAIL_DOMAIN=email.lightpost.test
EMAIL_TO_DOMAIN=email.lightpost.test
EMAIL_FROM_DOMAIN=message.lightpost.test
API_DOMAIN=api.lightpost.test
ATTENDANCE_CARDS_DOMAIN=open.lightpost.test

SENTRY_LARAVEL_DSN=

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lightpost_test
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_DRIVER=sync

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

MAIL_DEFAULT_FROM=<EMAIL>
MAIL_INBOUND_MAILGUN_API_KEY=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
