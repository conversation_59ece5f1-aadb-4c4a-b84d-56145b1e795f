@import 'tailwindcss';

@config "../../../../tailwind.app.config.js";

@import '../../../../vendor/livewire/flux/dist/flux.css';

/* Effectively disabled dark mode for FluxUI unless we add a custom variant. */
@custom-variant dark (&:where(.dark, .dark *));

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    :root {
        --color-accent: theme('colors.blue.500');
        --color-accent-content: theme('colors.blue.600');
        --color-accent-foreground: theme('colors.white');

        --color-zinc-50: var(--color-stone-50);
        --color-zinc-100: var(--color-stone-100);
        --color-zinc-200: var(--color-stone-200);
        --color-zinc-300: var(--color-stone-300);
        --color-zinc-400: var(--color-stone-400);
        --color-zinc-500: var(--color-stone-500);
        --color-zinc-600: var(--color-stone-600);
        --color-zinc-700: var(--color-stone-700);
        --color-zinc-800: var(--color-stone-800);
        --color-zinc-900: var(--color-stone-900);
        --color-zinc-950: var(--color-stone-950);
    }

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

/* GLOBAL */
::placeholder {
    @apply text-gray-300;
}

/* END GLOBAL */

@utility form-input {
    @apply block border-gray-300 rounded-md;

    &:focus {
        @apply border-blue-300 ring-3 ring-blue-200/50;
    }
}

@utility form-select {
    @apply block border-gray-300 rounded-md;

    &:focus {
        @apply border-blue-300 ring-3 ring-blue-200/50;
    }
}

@utility form-check-input {
    @apply border-gray-300 rounded-sm;

    &:focus {
        @apply ring-blue-500;
    }
}

@layer components {
    [x-cloak] {
        display: none;
    }
}

@layer components {
    h1 {
        @apply text-4xl font-medium;
    }

    h2 {
        @apply text-2xl font-medium;
    }

    h3 {
        @apply text-xl font-medium;
    }

    h4 {
        @apply text-lg font-medium;
    }

    a {
        @apply text-blue-500;
    }
}