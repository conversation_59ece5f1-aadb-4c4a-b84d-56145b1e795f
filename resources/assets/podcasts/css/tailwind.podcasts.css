@import 'tailwindcss';

/* purgecss start ignore */

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@layer components {
    /* purgecss end ignore */
    [x-cloak] {
        display: none;
    }

    a {
        @apply text-blue-600;
    }
}

/* @import "https://rsms.me/inter/inter.css"; */

