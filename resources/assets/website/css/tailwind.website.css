@import 'tailwindcss';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@custom-variant dark (&:where(.dark, .dark *));

@layer components {
    [x-cloak] {
        display: none;
    }

    body {
        @apply text-black;

        &.dark {
            @apply text-white;
        }
    }

    h1 {
        @apply text-4xl font-semibold mb-2;
    }

    h2 {
        @apply text-3xl font-semibold mb-2;
    }

    h3 {
        @apply text-2xl font-semibold mb-2;
    }

    h4 {
        @apply text-xl font-semibold mb-2;
    }

    h5 {
        @apply text-lg font-semibold mb-2;
    }

    p {
        @apply my-2;
    }

    a {
        @apply text-blue-500;

        &.dark {
            @apply text-blue-300;
        }
    }

    ul {
        @apply list-disc;

        li {
            @apply ml-6;
        }
    }

    .form-input,
    .form-select {
        @apply block border-gray-300 rounded-md;
    }

    .form-input:focus,
    .form-select:focus {
        @apply border-blue-300 ring-3 ring-blue-200/50;
    }

    .form-check-input {
        @apply border-gray-300 rounded-sm;
    }

    .form-check-input:focus {
        @apply ring-blue-500;
    }

    .page-content {
        @apply text-wrap;
    }

    .page-content ul {
        list-style-type: disc;
        padding-left: 1.5em;
        margin: 0.5em 0;
    }

    .page-content ol {
        list-style-type: decimal;
        padding-left: 1.5em;
        margin: 0.5em 0;
    }

    .page-content li {
        margin: 0.2em 0;
    }

    .page-content li p {
        margin: 0;
    }

    .page-content blockquote {
        border-left: #9eb0b7 4px solid;
        padding: 4px 0 4px 10px;
    }
}

