@import 'tailwindcss';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

/* GLOBAL */
::placeholder {
    @apply text-gray-300;
}

/* END GLOBAL */

@utility blaze-button {
    @apply text-white border border-blue-600 bg-linear-to-r from-blue-500 via-blue-600 to-blue-700 hover:bg-linear-to-br focus:ring-4 focus:outline-hidden focus:ring-blue-300 dark:focus:ring-blue-800 shadow-lg shadow-blue-500/50 hover:shadow-blue-400/50 dark:shadow-blue-800/80 font-light rounded-lg text-base px-5 py-2.5 text-center;
}

@utility pricing-table {
    & td.t-row {
        @apply px-6 py-4 whitespace-nowrap text-base leading-5 text-gray-700 text-center;
    }

    & td.t-row-feature {
        @apply px-6 py-4 whitespace-nowrap text-lg leading-5 font-medium text-gray-900 text-right;
    }
}

@utility t-row {
    .pricing-table & td {
        @apply px-6 py-4 whitespace-nowrap text-base leading-5 text-gray-700 text-center;
    }
}

@utility t-row-feature {
    .pricing-table & td {
        @apply px-6 py-4 whitespace-nowrap text-lg leading-5 font-medium text-gray-900 text-right;
    }
}

@utility form-input {
    @apply block border-gray-300 rounded-md;

    &:focus {
        @apply border-blue-300 ring-3 ring-blue-200/50;
    }
}

@utility form-select {
    @apply block border-gray-300 rounded-md;

    &:focus {
        @apply border-blue-300 ring-3 ring-blue-200/50;
    }
}

@utility form-check-input {
    @apply border-gray-300 rounded-sm;

    &:focus {
        @apply ring-blue-500;
    }
}

@layer components {

    h1 {
        @apply text-3xl font-medium text-stone-900;
    }

    h2 {
        @apply text-2xl font-medium;
    }

    h3 {
        @apply text-xl font-medium;
    }

    h4 {
        @apply text-lg font-medium;
    }

    ul {
        li {
            @apply list-disc ml-6;
        }
    }

    ol {
        li {
            @apply list-decimal ml-6;
        }
    }

    a {
        @apply text-blue-500;
    }

    ul li {
        list-style: none;
        padding-left: 0;
        margin-left: 0;
    }
}

