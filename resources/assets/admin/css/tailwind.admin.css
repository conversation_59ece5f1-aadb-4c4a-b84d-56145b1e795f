@import 'tailwindcss';


@config "../../../../tailwind.admin.config.js";

@import '../../../../node_modules/font-awesome/css/font-awesome.css';
@import '../../../../vendor/livewire/flux/dist/flux.css';

/* Effectively disabled dark mode for FluxUI unless we add a custom variant. */
@custom-variant dark (&:where(.dark, .dark *));


/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {

    /* https://fluxui.dev/themes?accent=blue&base=stone */
    :root {
        --color-accent: theme('colors.blue.500');
        --color-accent-content: theme('colors.blue.600');
        --color-accent-foreground: theme('colors.white');

        --color-zinc-50: var(--color-stone-50);
        --color-zinc-100: var(--color-stone-100);
        --color-zinc-200: var(--color-stone-200);
        --color-zinc-300: var(--color-stone-300);
        --color-zinc-400: var(--color-stone-400);
        --color-zinc-500: var(--color-stone-500);
        --color-zinc-600: var(--color-stone-600);
        --color-zinc-700: var(--color-stone-700);
        --color-zinc-800: var(--color-stone-800);
        --color-zinc-900: var(--color-stone-900);
        --color-zinc-950: var(--color-stone-950);
    }

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

/* GLOBAL */
::placeholder {
    @apply text-gray-300;
}

/* END GLOBAL */

@utility admin-heading-section {
    @apply md:flex md:justify-between space-y-2 md:space-y-0;

    div {
        @apply space-x-2;
    }
}
@utility admin-standard-ring {
    @apply focus:ring-1 focus:z-10 focus:outline-hidden focus:ring-blue-500 focus:border-blue-500;
}
@utility admin-standard-col-width {
    /* APP */
    @apply max-w-6xl;
}
@utility admin-full-col-width {
    @apply mx-auto;
}
@utility admin-border-color {
    /* BORDERS */
    @apply border-gray-300;
}
@utility admin-section-border {
    @apply admin-border-color border rounded-lg;
}
@utility admin-section {
    @apply admin-section-border bg-white;
}
@utility admin-button-base {
    @apply text-base inline-flex items-center px-3 py-1.5 cursor-pointer;
}
@utility admin-button-base-small {
    @apply text-sm inline-flex items-center px-2 py-1 cursor-pointer;
}
@utility admin-button-transparent {
    /* BuTTONS */
    @apply admin-standard-ring admin-button-base border border-gray-400 hover:bg-gray-100 rounded-sm text-gray-500 bg-transparent active:text-gray-800 active:bg-gray-50;
}
@utility admin-button-transparent-small {
    @apply admin-standard-ring admin-button-base-small border border-gray-400 hover:bg-gray-100 rounded-sm text-gray-500 bg-transparent active:text-gray-800 active:bg-gray-50;
}
@utility admin-button-blue {
    @apply admin-standard-ring admin-button-base border border-blue-600 hover:bg-blue-500 rounded-sm text-white bg-blue-600 active:bg-blue-50;
}
@utility admin-button-blue-small {
    @apply admin-standard-ring admin-button-base-small border border-blue-600 hover:bg-blue-500 rounded-sm text-white bg-blue-600 active:bg-blue-50;
}
@utility admin-button-red {
    @apply admin-standard-ring admin-button-base border border-red-600 hover:bg-red-500 rounded-sm text-white bg-red-600 active:bg-red-50;
}
@utility admin-button-red-small {
    @apply admin-standard-ring admin-button-base-small border border-red-600 hover:bg-red-500 rounded-sm text-white bg-red-600 active:bg-red-50;
}
@utility admin-button-red-transparent {
    @apply admin-button-red text-red-600 bg-transparent hover:text-white;
}
@utility admin-button-red-transparent-small {
    @apply admin-button-red-small text-red-600 bg-transparent hover:text-white;
}
@utility admin-nav-a {
    /* Admin Nav Bar */
    @apply flex items-center px-2 py-2 md:bg-blue-500 text-sm leading-5 font-medium rounded-md text-gray-700 transition ease-in-out duration-150;

    &:hover {
        @apply text-gray-900 bg-gray-50;
    }

    &:focus {
        @apply outline-hidden bg-gray-50;
    }
}
@utility btn-color-and-hover {
    @apply admin-standard-ring text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden transition duration-150 ease-in-out;
}

/* GLOBAL */
@layer components {
    h1 {
        @apply text-3xl font-medium text-stone-900 sm:truncate;
    }

    h2 {
        @apply text-2xl font-medium;
    }

    h3 {
        @apply text-xl font-medium;
    }

    h4 {
        @apply text-lg font-medium;
    }

    h5 {
        @apply text-base font-medium;
    }

    a {
        @apply text-blue-600;
    }

    /* FORMS */
    input[type='text'], input[type='password'], textarea {
        @apply admin-standard-ring admin-border-color border block w-full md:text-base rounded-md placeholder:text-gray-300 px-2 py-1.5;
    }

    select {
        @apply admin-standard-ring admin-border-color border block md:text-base rounded-md placeholder:text-gray-300 pl-2 pr-8 py-1.5;
    }

    input[type='date'], input[type='number'] {
        @apply admin-standard-ring admin-border-color border block md:text-base rounded-md placeholder:text-gray-300 px-2 py-1.5;
    }

    input[type='checkbox'] {
        @apply admin-standard-ring h-4 w-4 text-blue-600 border-gray-300 rounded-sm;
    }
}

/* ===============================
=            Choices            =
=============================== */
.choices {
    position: relative;
    overflow: hidden;
    margin-bottom: 24px;
    font-size: 16px;
}

.choices:focus {
    outline: none;
}

.choices:last-child {
    margin-bottom: 0;
}

.choices.is-open {
    overflow: visible;
}

.choices.is-disabled .choices__inner,
.choices.is-disabled .choices__input {
    background-color: #eaeaea;
    cursor: not-allowed;
    -webkit-user-select: none;
    user-select: none;
}

.choices.is-disabled .choices__item {
    cursor: not-allowed;
}

.choices [hidden] {
    display: none !important;
}

.choices[data-type*=select-one] {
    cursor: pointer;
}

.choices[data-type*=select-one] .choices__inner {
    padding-bottom: 7.5px;
}

.choices[data-type*=select-one] .choices__input {
    display: block;
    width: 100%;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    background-color: #fff;
    margin: 0;
}

.choices[data-type*=select-one] .choices__button {
    @apply rounded-md;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==");
    padding: 0;
    background-size: 8px;
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -10px;
    margin-right: 25px;
    height: 20px;
    width: 20px;
    opacity: 0.25;
}

.choices[data-type*=select-one] .choices__button:hover, .choices[data-type*=select-one] .choices__button:focus {
    opacity: 1;
}

.choices[data-type*=select-one] .choices__button:focus {
    box-shadow: 0 0 0 2px #00bcd4;
}

.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button {
    display: none;
}

.choices[data-type*=select-one]::after {
    content: "";
    height: 0;
    width: 0;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
    border-width: 5px;
    position: absolute;
    right: 11.5px;
    top: 50%;
    margin-top: -2.5px;
    pointer-events: none;
}

.choices[data-type*=select-one].is-open::after {
    border-color: transparent transparent #333 transparent;
    margin-top: -7.5px;
}

.choices[data-type*=select-one][dir=rtl]::after {
    left: 11.5px;
    right: auto;
}

.choices[data-type*=select-one][dir=rtl] .choices__button {
    right: auto;
    left: 0;
    margin-left: 25px;
    margin-right: 0;
}

.choices[data-type*=select-multiple] .choices__inner,
.choices[data-type*=text] .choices__inner {
    cursor: text;
}

.choices[data-type*=select-multiple] .choices__button,
.choices[data-type*=text] .choices__button {
    @apply rounded-md;
    position: relative;
    display: inline-block;
    margin-top: 0;
    margin-right: -4px;
    margin-bottom: 0;
    margin-left: 8px;
    padding-left: 16px;
    border-left: 1px solid #008fa1;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==");
    background-size: 8px;
    width: 8px;
    line-height: 1;
    opacity: 0.75;
}

.choices[data-type*=select-multiple] .choices__button:hover, .choices[data-type*=select-multiple] .choices__button:focus,
.choices[data-type*=text] .choices__button:hover,
.choices[data-type*=text] .choices__button:focus {
    opacity: 1;
}

.choices__inner {
    @apply rounded-md;
    display: inline-block;
    vertical-align: top;
    width: 100%;
    @apply bg-gray-50 rounded-lg;
    padding: 7.5px 7.5px 3.75px;
    border: 1px solid #ddd;
    font-size: 14px;
    min-height: 44px;
    overflow: hidden;
}

.is-focused .choices__inner, .is-open .choices__inner {
    @apply admin-border-color;
    /*border-color: #b7b7b7;*/
}

.is-open .choices__inner {
    border-radius: 2.5px 2.5px 0 0;
}

.is-flipped.is-open .choices__inner {
    border-radius: 0 0 2.5px 2.5px;
}

.choices__list {
    margin: 0;
    padding-left: 0;
    list-style: none;
}

.choices__list--single {
    display: inline-block;
    padding: 4px 16px 4px 4px;
    width: 100%;
}

[dir=rtl] .choices__list--single {
    padding-right: 4px;
    padding-left: 16px;
}

.choices__list--single .choices__item {
    width: 100%;
}

.choices__list--multiple {
    display: inline;
}

.choices__list--multiple .choices__item {
    @apply bg-blue-600 border border-blue-700 rounded-md;
    display: inline-block;
    vertical-align: middle;
    padding: 4px 10px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 3.75px;
    margin-bottom: 3.75px;
    color: #fff;
    word-break: break-all;
    box-sizing: border-box;
}

.choices__list--multiple .choices__item[data-deletable] {
    padding-right: 5px;
}

[dir=rtl] .choices__list--multiple .choices__item {
    margin-right: 0;
    margin-left: 3.75px;
}

.choices__list--multiple .choices__item.is-highlighted {
    @apply bg-blue-400 border border-blue-700;
}

.is-disabled .choices__list--multiple .choices__item {
    background-color: #aaaaaa;
    border: 1px solid #919191;
}

.choices__list--dropdown, .choices__list[aria-expanded] {
    visibility: hidden;
    z-index: 1;
    position: absolute;
    width: 100%;
    background-color: #fff;
    border: 1px solid #ddd;
    top: 100%;
    margin-top: -1px;
    border-bottom-left-radius: 2.5px;
    border-bottom-right-radius: 2.5px;
    overflow: hidden;
    word-break: break-all;
    will-change: visibility;
}

.is-active.choices__list--dropdown, .is-active.choices__list[aria-expanded] {
    visibility: visible;
}

.is-open .choices__list--dropdown, .is-open .choices__list[aria-expanded] {
    @apply admin-border-color;
    /*border-color: #b7b7b7;*/
}

.is-flipped .choices__list--dropdown, .is-flipped .choices__list[aria-expanded] {
    @apply rounded-md;
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: -1px;
}

.choices__list--dropdown .choices__list, .choices__list[aria-expanded] .choices__list {
    position: relative;
    max-height: 300px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position;
}

.choices__list--dropdown .choices__item, .choices__list[aria-expanded] .choices__item {
    position: relative;
    padding: 10px;
    font-size: 14px;
}

[dir=rtl] .choices__list--dropdown .choices__item, [dir=rtl] .choices__list[aria-expanded] .choices__item {
    text-align: right;
}

@media (min-width: 640px) {
    .choices__list--dropdown .choices__item--selectable, .choices__list[aria-expanded] .choices__item--selectable {
        padding-right: 100px;
    }

    .choices__list--dropdown .choices__item--selectable::after, .choices__list[aria-expanded] .choices__item--selectable::after {
        content: attr(data-select-text);
        font-size: 12px;
        opacity: 0;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    [dir=rtl] .choices__list--dropdown .choices__item--selectable, [dir=rtl] .choices__list[aria-expanded] .choices__item--selectable {
        text-align: right;
        padding-left: 100px;
        padding-right: 10px;
    }

    [dir=rtl] .choices__list--dropdown .choices__item--selectable::after, [dir=rtl] .choices__list[aria-expanded] .choices__item--selectable::after {
        right: auto;
        left: 10px;
    }
}

.choices__list--dropdown .choices__item--selectable.is-highlighted, .choices__list[aria-expanded] .choices__item--selectable.is-highlighted {
    background-color: #f2f2f2;
}

.choices__list--dropdown .choices__item--selectable.is-highlighted::after, .choices__list[aria-expanded] .choices__item--selectable.is-highlighted::after {
    opacity: 0.5;
}

.choices__item {
    cursor: default;
}

.choices__item--selectable {
    cursor: pointer;
}

.choices__item--disabled {
    cursor: not-allowed;
    -webkit-user-select: none;
    user-select: none;
    opacity: 0.5;
}

.choices__heading {
    font-weight: 600;
    font-size: 12px;
    padding: 10px;
    border-bottom: 1px solid #f7f7f7;
    color: gray;
}

.choices__button {
    text-indent: -9999px;
    -webkit-appearance: none;
    appearance: none;
    border: 0;
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
}

.choices__button:focus {
    outline: none;
}

.choices__input {
    display: inline-block;
    vertical-align: baseline;
    background-color: #f9f9f9;
    font-size: 14px;
    margin-bottom: 5px;
    border: 0;
    border-radius: 0;
    max-width: 100%;
    padding: 4px 0 4px 2px;
}

.choices__input:focus {
    outline: 0;
}

.choices__input::-webkit-search-decoration, .choices__input::-webkit-search-cancel-button, .choices__input::-webkit-search-results-button, .choices__input::-webkit-search-results-decoration {
    display: none;
}

.choices__input::-ms-clear, .choices__input::-ms-reveal {
    display: none;
    width: 0;
    height: 0;
}

[dir=rtl] .choices__input {
    padding-right: 2px;
    padding-left: 0;
}

.choices__placeholder {
    opacity: 0.5;
}

/* =====  End of Choices  ====== */


/*@import "https://rsms.me/inter/inter.css";*/
