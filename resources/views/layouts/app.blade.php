<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <link rel="apple-touch-icon" href="/favicon/favicon-180x180.png"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-196x196.png" sizes="196x196"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-96x96.png" sizes="96x96"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-32x32.png" sizes="32x32"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-16x16.png" sizes="16x16"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-128.png" sizes="128x128"/>
    <meta name="application-name" content="Lightpost"/>
    <meta name="msapplication-TileColor" content="#FFFFFF"/>
    <meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png"/>
    <meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png"/>
    <meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png"/>
    <meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png"/>
    <meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png"/>
    <link rel="manifest" href="/favicon/manifest.json">

    <meta name="theme-color" content="#ffffff">

    <title>@yield('title', 'Lightpost Admin')</title>

    <!-- Styles -->
    <link href="{{ asset('css/admin/app.css') }}" rel="stylesheet">
    <link href="{{ asset('css/selectize.css') }}" rel="stylesheet">
    <link href="{{ asset('webfonts/css/all.min.css') }}" rel="stylesheet">

    <script src="{{ asset('js/app.js') }}"></script>

    @if(config('app.env') == 'production')
        <script src="https://cdn.usefathom.com/script.js" data-site="TEPSZWEY" defer></script>
    @endif
</head>


<body>

<nav class="navbar navbar-expand-md sticky-top" style="background-color: #eff3f6">
    <div class="container p-0">
        <a class="navbar-brand pl-1" href="/">
            <img src="/img/lightpost-logo-icon.svg" height="34px" style="margin-top:-8px;"/>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <i class="fa fa-navicon"></i>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mr-auto nav-pills nav-fill">
                <li class="nav-item">
                    <a class="nav-link{{ request()->is('/') ? ' active' : null }}" href="/">Dashboard</a>
                </li>
                @can('retrieve', \App\Users\User::class)
                    <li class="nav-item">
                        <a class="nav-link{{ request()->is('users*') ? ' active' : null }}" href="{{ route('admin.users.index') }}">People</a>
                    </li>
                @endcan
                @if(\Illuminate\Support\Facades\Gate::check('index', \App\Attendance\Attendance::class) || \Illuminate\Support\Facades\Gate::check('manage', \App\Attendance\Attendance::class))
                    <li class="nav-item dropdown">
                        <a href="#" id="navbarUserDropdownLink2" class="nav-link dropdown-toggle{{ request()->is('attendance*') ? ' active' : null }}" data-toggle="dropdown" role="button" aria-expanded="false">
                            Attendance <span class="caret"></span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" role="menu" aria-labelledby="navbarUserDropdownLink2">
                            <a class="dropdown-item{{ request()->is('attendance/user*') ? ' active' : null }}" href="{{ route('admin.attendance.user.index') }}">Member Attendance</a>
                            <a class="dropdown-item{{ request()->is('attendance/general*') ? ' active' : null }}" href="{{ route('admin.attendance.general.index') }}">General Attendance</a>
                        </div>
                    </li>
                @endif
                @if(Auth::user()->account->hasFeature('feature.prayers'))
                    @can('index', App\Prayers\Prayer::class)
                        <li class="nav-item">
                            <a class="nav-link{{ request()->is('prayers*') ? ' active' : null }}" href="{{ route('admin.prayers.index') }}">Prayer List</a>
                        </li>
                    @endcan
                @endif
                @if(Auth::user()->account->hasFeature('feature.bible_classes'))
                    @can('index', \App\BibleClasses\BibleClass::class)
                        <li class="nav-item">
                            <a class="nav-link{{ request()->is('bible-class*') ? ' active' : null }}" href="{{ route('admin.bible-classes.index') }}">Bible Class</a>
                        </li>
                    @endcan
                @endif
                @if(Auth::user()->account->hasFeature('feature.sermons'))
                    @can('index', \App\Sermons\Sermon::class)
                        <li class="nav-item">
                            <a class="nav-link{{ request()->is('sermons*') ? ' active' : null }}" href="{{ route('admin.sermons.index') }}">Sermons</a>
                        </li>
                    @endcan
                @endif
                @if(Auth::user()->account->hasFeature('feature.files'))
                    @can('index', \App\AccountFiles\AccountFile::class)
                        <li class="nav-item">
                            <a class="nav-link{{ request()->is('account-files*') ? ' active' : null }}" href="{{ route('admin.account-files.index') }}">Files</a>
                        </li>
                    @endcan
                @endif
                @if(Auth::user()->account->hasFeature('feature.calendars'))
                    @can('manage', \App\Calendars\Calendar::class)
                        <li class="nav-item">
                            <a class="nav-link position-relative{{ request()->is('calendars*') ? ' active' : null }}" href="{{ route('admin.calendars.index') }}">
                                Calendars
                            </a>
                        </li>
                    @endcan
                @endif
                <li class="nav-item dropdown">
                    <a href="#" id="navbarUserDropdownLink" class="nav-link dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                        Account <span class="caret"></span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right" role="menu" aria-labelledby="navbarUserDropdownLink">
                        @php($divider = false)
                        @if(Auth::user()->account->hasFeature('feature.worship_assignments'))
                            @can('index', \App\WorshipAssignments\Group::class)
                                <a class="dropdown-item{{ request()->is('worship-assignments*') ? ' active' : null }}" href="{{ route('admin.worship-assignments.index') }}">Worship Assignments</a>
                                @php($divider = true)
                            @endcan
                        @endif
                        @if(Auth::user()->account->hasFeature('feature.involvement'))
                            @can('index', App\Involvement\Category::class)
                                <a class="dropdown-item{{ request()->is('involvement*') ? ' active' : null }}" href="{{ route('admin.involvement.index') }}">Involvement</a>
                                @php($divider = true)
                            @endcan
                        @endif
                        @if(Auth::user()->account->hasFeature('feature.podcasts'))
                            @can('index', App\Podcasts\Podcast::class)
                                <a class="dropdown-item{{ request()->is('podcasts*') ? ' active' : null }}" href="{{ route('admin.podcasts.index') }}">Podcasts</a>
                                @php($divider = true)
                            @endcan
                        @endif
                        @if($divider)
                            <div class="dropdown-divider"></div>
                        @endif
                        @can('index-reports')
                            <a class="dropdown-item{{ request()->is('reports*') ? ' active' : null }}" href="{{ route('admin.reports.index') }}">Reports</a>
                        @endcan
                        @can('index', \App\Users\Group::class)
                            <a class="dropdown-item{{ request()->is('groups*') ? ' active' : null }}" href="{{ route('admin.groups.index') }}">Groups</a>
                        @endcan
                        @can('index', \App\Users\Role::class)
                            <a class="dropdown-item{{ request()->is('roles*') ? ' active' : null }}" href="{{ route('admin.roles.index') }}">Roles</a>
                        @endcan
                        @if(Auth::user()->account->hasFeature('feature.grades'))
                            @can('manage', \App\Accounts\Grade::class)
                                <a class="dropdown-item{{ request()->is('grades*') ? ' active' : null }}" href="{{ route('admin.accounts.grades.index') }}">School Grades</a>
                            @endcan
                        @endif
                        @can('manage', \App\Accounts\Account::class)
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item{{ request()->is('account/settings*') ? ' active' : null }}" href="{{ route('admin.accounts.settings.index') }}">Settings</a>
                        @endcan
                        @if(Auth::user()->account->hasFeature('feature.online_giving'))
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item{{ request()->is('account/giving*') ? ' active' : null }}" href="{{ route('admin.finance.giving.index') }}">Online Giving</a>
                        @endif
                        <a class="dropdown-item{{ request()->is('crises*') ? ' active' : null }}" href="{{ route('admin.crises.index') }}">Crisis Check-in</a>
                        <div class="dropdown-divider"></div>
                        <a href="{{ route('logout') }}" class="dropdown-item" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            Logout
                        </a>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                            {{ csrf_field() }}
                        </form>
                    </div>
                </li>
            </ul>
            <!-- Right Side of Navbar -->
            <ul class="navbar-nav ml-auto">
                <!-- Authentication Links -->
                @guest
                    <li class="nav-item">
                        <a href="{{ route('login') }}" class="nav-link">Login</a>
                    </li>
                @endguest
            </ul>
            <form action="{{ route('admin.users.index') }}" method="get" class="form-inline my-2 my-lg-0">
                <?php
                $isAdminv2 = (optional(auth()->user())->account_id == 10
                    || optional(auth()->user())->account_id == 27
                    || optional(auth()->user())->account_id == 24);
                ?>
                <input class="form-control" autocomplete="off" type="search" name="{{ $isAdminv2 ? 'q' : 'query' }}" placeholder="Search..." aria-label="Search" value="{{ request('query') }}">
                {{--<button class="btn btn-outline-success my-2 my-sm-0" type="submit">Search</button>--}}
            </form>
        </div>
    </div>
</nav>

<div class="container p-0">
    <div class="row">
        <main class="col-sm-12 ml-sm-auto col-md-12 col-lg-12 col-xl-12 pt-3" role="main">

            @if(session('message.success'))
                <div class="alert alert-dismissible alert-success rounded-0 border-right-0 border-left-0" role="alert" onclick="$(this).hide();">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ session('message.success') }}
                </div>
            @endif

            @if(session('message.failure'))
                <div class="alert alert-dismissible alert-danger rounded-0 border-right-0 border-left-0" role="alert" onclick="$(this).hide();">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    {{ session('message.failure') }}
                </div>
            @endif

            @if(isset($errors) && $errors->count() > 0)
                <div class="alert alert-dismissible alert-danger rounded-0 border-right-0 border-left-0" role="alert" onclick="$(this).hide();">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    @foreach ($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                </div>
            @endif

            @yield('content')

        </main>
    </div>
</div>

<footer class="footer d-print-none">
    <div class="container p-0">
        <div class="row">
            <div class="col-12">
                <hr class="mb-2"/>
                <p class="text-muted">
                    <span class="float-right" style="color: #eee">
                        {{ gethostname() }} &dash; <?= number_format(microtime(true) - (defined('LARAVEL_START') ? LARAVEL_START : microtime(true)), 3); ?>s
                    </span>
                    @if(Auth::user())
                        <strong>{{ \Illuminate\Support\Facades\Auth::user()->account->name }}</strong>
                        <br>
                        @endif
                        &copy; {{ date('Y') }} {{ config('app.name') }}
                </p>
            </div>
        </div>
    </div>
</footer>

<br>

<!-- Scripts -->

@stack('scripts')

<script type="text/javascript">
    $(function () {
        // Enable tooltips on <a> tags
        $('[rel="tooltip"]').tooltip();
    });
</script>

<script type="text/javascript">
    // Toggle checkboxes
    $.fn.toggleCheckbox = function () {
        if (this.prop('checked')) {
            this.prop('checked', '');
        } else {
            this.prop('checked', 'checked');
        }
    }
</script>

<?php ##############  MODAL JAVASCRIPTS  ################ ?>
<script type="text/javascript">
    // Load a URL into our modal when we request a modal from the data-source attribute
    $(document).ready(function () {
        $('.modal').on('shown.bs.modal', function (event) {
            <?php // console.log(event.relatedTarget.attributes['data-source']);  # Debugging for getting the data-source value of the button/element that triggered this event ?>
            if ("relatedTarget" in event) {
                $(this).children('.modal-dialog').children('.modal-content').load(event.relatedTarget.attributes['data-source'].value);
            }
        });
        // Clear our modal after use - prevents ghosting when a new modal is opened.
        $('.modal').on('hidden.bs.modal', function (event) {
            $(this).children('.modal-dialog').children('.modal-content').empty();
        });
    });
</script>

<?php /* SAMPLE USAGE
        <button type="button" data-toggle="modal" data-target="#large-modal" class="btn btn-success pull-right" data-source="/orders/view-transaction/{ID}"></button>
    */ ?>

<?php # LARGE Modal HTML Template ?>
<div class="modal" id="large-modal" tabindex="-1" role="dialog" aria-labelledby="large-modal" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <?php # Content will go here from auto-insert JS above ?>
        </div>
    </div>
</div>
<?php # MEDIUM Modal HTML Template ?>
<div class="modal" id="medium-modal" tabindex="-1" role="dialog" aria-labelledby="medium-modal" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <?php # Content will go here from auto-insert JS above ?>
        </div>
    </div>
</div>
<?php # SMALL Modal HTML Template ?>
<div class="modal" id="small-modal" tabindex="-1" role="dialog" aria-labelledby="small-modal" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <?php # Content will go here from auto-insert JS above ?>
        </div>
    </div>
</div>
<?php ##############  END  MODAL JAVASCRIPTS  ################ ?>

</body>
</html>
