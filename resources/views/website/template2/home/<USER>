@extends('website.template1._layout._app')

@section('title', 'Home')

@section('content')
    <div class="relative max-w-4xl mx-auto" style="min-height: 475px;" x-data="carousel()" x-init="startAutoRotation()">
        <div class="overflow-hidden relative rounded-lg shadow-lg">
            <!-- Slides -->
            <div class="flex transition-transform duration-500 ease-in-out" :style="{ transform: `translateX(-${currentIndex * 100}%)` }">
                <template x-for="(slide, index) in slides" :key="index">
                    <div class="w-full shrink-0 relative">
                        <a :href="getSlideLink(slide)" class="block w-full h-full" style="max-height: 500px" @click.stop>
                            <img :src="slide.image" :alt="slide.alt" class="w-full object-cover">
                            <div x-show="currentIndex === index && (slide.title || slide.description)"
                                 class="absolute bottom-0 left-0 right-0 p-4 bg-black bg-opacity-50 text-white">
                                <h3 x-show="slide.title" x-text="slide.title" class="text-xl font-bold"></h3>
                                <p x-show="slide.description" x-text="slide.description" class="mt-2"></p>
                            </div>
                        </a>
                    </div>
                </template>
            </div>

            <!-- Navigation arrows -->
            <button @click.stop="prevSlide()" class="absolute top-1/2 left-2 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-2 focus:outline-hidden hover:bg-opacity-75 z-10">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button @click.stop="nextSlide()" class="absolute top-1/2 right-2 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-2 focus:outline-hidden hover:bg-opacity-75 z-10">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Indicators -->
            <div class="absolute bottom-4 left-0 right-0 z-10">
                <div class="flex items-center justify-center space-x-2">
                    <template x-for="(_, index) in slides" :key="index">
                        <div class="flex flex-col items-center">
                            <button
                                    @click.stop="goToSlide(index)"
                                    class="w-3 h-2 rounded-full focus:outline-hidden transition-colors duration-200 ease-in-out border border-white"
                                    :class="{ 'bg-white': currentIndex === index, 'bg-transparent': currentIndex !== index }"
                            ></button>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <script>
        function carousel() {
            return {
                currentIndex: 0,
                autoRotationTimer: null,
                slides: @json($carouselItems->map(function($item) {
                    return [
                        'image' => \App\Website\WebsiteFile::find($item['website_file_id'])?->getUrl(),
                        'alt' => $item['title'],
                        'title' => $item['title'],
                        'description' => $item['description'],
                        'link_type' => $item['link_type'],
                        'link' => $item['link'],
                        'website_page_id' => $item['website_page_id'],
                    ];
                })),
                getSlideLink(slide) {
                    if (slide.link_type === 'page' && slide.website_page_id) {
                        return '{{ url("/page") }}/' + slide.website_page_id;
                    }
                    return slide.link || '#';
                },
                nextSlide() {
                    this.goToSlide((this.currentIndex + 1) % this.slides.length);
                },
                prevSlide() {
                    this.goToSlide((this.currentIndex - 1 + this.slides.length) % this.slides.length);
                },
                goToSlide(index) {
                    this.currentIndex = index;
                    this.resetAutoRotation();
                },
                startAutoRotation() {
                    this.autoRotationTimer = setInterval(() => {
                        this.nextSlide();
                    }, 7000);
                },
                resetAutoRotation() {
                    clearInterval(this.autoRotationTimer);
                    this.startAutoRotation();
                },
            }
        }
    </script>

    <div class="">
        <div class="md:flex md:items-center md:justify-between">
            <div class="mt-6">
                <h1>Welcome!</h1>
                <p>If you are living in our area, or you are considering a move to this area, we hope that you will choose to come visit us in person.
                <p>This is a congregation of people from diverse walks of life, all ages and many interests. It is a congregation seeking to worship after the pattern set forth in the scriptures, with no head but Christ, overseen by elders meeting scriptural qualifications and served by deacons who lead in the many tasks and works undertaken by a congregation of this size. It is a congregation which has a great desire for greater knowledge of the word of God and in which the study of the Bible is encouraged in numerous ways.
                <p>If you visit us, we encourage you to linger for a little while after our classes and worship assembly so that we can meet you. If you have questions, do not hesitate to ask!
            </div>
        </div>
    </div>
@endsection