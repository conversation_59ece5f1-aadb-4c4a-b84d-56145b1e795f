@extends('website.template1._layout._app')

@section('title', 'Files')

@section('content')

    <div x-data class="md:flex md:items-center md:justify-between">
        <div class="grow">
            <h1>Files</h1>

            <div class="max-w-lg mx-auto sm:max-w-none py-4">
                <div class="bg-white overflow-hidden rounded-md border border-gray-200">
                    <ul class="divide-y">
                        @forelse($files as $file)
                            @if($file->is_folder)
                                <li>
                                    <a href="{{ route('app.account-files.folder.view', $file) }}" class="block hover:bg-gray-50 hover:cursor-pointer focus:outline-hidden focus:bg-gray-50 transition duration-150 ease-in-out">
                                        <div class="px-4 py-4 sm:px-4">
                                            <div class="flex items-center justify-between">
                                                <div class="inline-flex items-center text-base font-medium truncate">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6 text-blue-400">
                                                        <path d="M19.5 21a3 3 0 003-3v-4.5a3 3 0 00-3-3h-15a3 3 0 00-3 3V18a3 3 0 003 3h15zM1.5 10.146V6a3 3 0 013-3h5.379a2.25 2.25 0 011.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 013 3v1.146A4.483 4.483 0 0019.5 9h-15a4.483 4.483 0 00-3 1.146z"/>
                                                    </svg>
                                                    <div class="ml-2 text-blue-600">
                                                        {{ $file->title }}
                                                    </div>
                                                </div>
                                                <span class="inline-flex rounded-md shadow-xs">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-black">
                                          <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12h15m0 0l-6.75-6.75M19.5 12l-6.75 6.75"/>
                                        </svg>
                                    </span>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                            @else
                                <li>
                                    <div class="block hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 transition duration-150 ease-in-out">
                                        <div class="px-2 py-2 sm:px-4">
                                            <div class="flex items-center justify-between">
                                                <div class="text-base font-normal truncate">
                                                    {{ $file->title }}
                                                </div>
                                                <a href="{{ $file->getTempUrl(240) }}" class="ml-2 shrink-0 flex">
                                                    <button type="button" class="inline-flex items-center px-4 py-1 border border-transparent text-sm leading-5 rounded-md text-white bg-blue-600 hover:bg-blue-500 focus:outline-hidden focus:border-blue-700 focus:ring-blue active:bg-blue-700 transition ease-in-out duration-150">
                                                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                        </svg>
                                                        Download
                                                    </button>
                                                </a>
                                            </div>
                                            <div class="mt-1 sm:flex sm:justify-between">
                                                <div class="sm:flex">
                                                    <div class="mt-2 mr-2 flex items-center text-sm leading-5 text-gray-500 sm:mt-0">
                                                <span class="px-2 inline-flex text-xs font-medium rounded-sm bg-gray-100 text-gray-600">
                                                    {{ $file->type }}
                                                </span>
                                                    </div>
                                                    <div class="flex items-center text-sm leading-5 text-gray-500">
                                                        {{ $file->getFileSize() }}<span class="ml-0.5">{{ $file->getFileSize(get_unit_type: true) }}</span>
                                                    </div>
                                                </div>
                                                <div class="flex items-center text-gray-400">
                                            <span class="text-xs">
                                                Last updated
                                                <time datetime="{{ $file->updated_at->format('Y-m-d') }}">{{ $file->updated_at->diffForHumans() }}</time>
                                            </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            @endif
                        @empty
                            <div class="flex">
                                <div class="text-center px-6 py-4 mx-auto bg-gray-100 my-12 rounded-sm">
                                    No files exist yet!
                                </div>
                            </div>
                        @endforelse
                    </ul>
                </div>
            </div>


            <!-- Player -->
            <div x-cloak x-show="$store.AudioPlayer.showPlayer" class="fixed inset-x-0 bottom-0 z-10 max-w-(--breakpoint-md) mx-auto">
                <div class="flex items-center gap-6 bg-black/90 px-4 py-4 shadow-sm shadow-slate-200/80 ring-1 ring-slate-900/5 backdrop-blur-xs md:px-6 md:rounded-t-lg">
                    <div class="hidden md:block">
                        {{-- PAUSE BUTTON --}}
                        <button x-cloak x-show="$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-700 focus:ring-offset-2 md:h-14 md:w-14"
                                aria-label="Pause">
                            <div class="absolute -inset-3 md:hidden"></div>
                            <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                <path d="M8.5 4C7.67157 4 7 4.67157 7 5.5V30.5C7 31.3284 7.67157 32 8.5 32H11.5C12.3284 32 13 31.3284 13 30.5V5.5C13 4.67157 12.3284 4 11.5 4H8.5ZM24.5 4C23.6716 4 23 4.67157 23 5.5V30.5C23 31.3284 23.6716 32 24.5 32H27.5C28.3284 32 29 31.3284 29 30.5V5.5C29 4.67157 28.3284 4 27.5 4H24.5Z"></path>
                            </svg>
                        </button>
                        {{-- PLAY BUTTON --}}
                        <button x-cloak x-show="!$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-200 focus:ring-offset-2 md:h-14 md:w-14"
                                aria-label="Play">
                            <div class="absolute -inset-3 md:hidden"></div>
                            <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                <path d="M33.75 16.701C34.75 17.2783 34.75 18.7217 33.75 19.299L11.25 32.2894C10.25 32.8668 9 32.1451 9 30.9904L9 5.00962C9 3.85491 10.25 3.13323 11.25 3.71058L33.75 16.701Z"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="mb-[env(safe-area-inset-bottom)] flex flex-1 flex-col gap-3 overflow-hidden p-1">
                        <a x-text="Alpine.store('AudioPlayer').title" class="truncate text-white text-center text-base font-medium md:text-left" title="" href=""></a>
                        <div class="flex justify-around gap-6">
                            <div class="flex flex-none items-center gap-4">
                                {{-- REWIND BUTTON --}}
                                <button x-on:click="$store.AudioPlayer.skipBackward()" type="button" class="group relative rounded-full focus:outline-hidden" aria-label="Rewind 10 seconds">
                                    <div class="absolute -inset-4 -right-2 md:hidden"></div>
                                    <svg aria-hidden="true" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 stroke-slate-300 group-hover:stroke-slate-500">
                                        <path d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"></path>
                                        <path d="M5 15V19"></path>
                                        <path d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523 10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"></path>
                                    </svg>
                                </button>
                                <div class="md:hidden">
                                    {{-- PAUSE BUTTON --}}
                                    <button x-cloak x-show="$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                            class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-200 focus:ring-offset-2 md:h-14 md:w-14"
                                            aria-label="Pause">
                                        <div class="absolute -inset-3 md:hidden"></div>
                                        <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                            <path d="M8.5 4C7.67157 4 7 4.67157 7 5.5V30.5C7 31.3284 7.67157 32 8.5 32H11.5C12.3284 32 13 31.3284 13 30.5V5.5C13 4.67157 12.3284 4 11.5 4H8.5ZM24.5 4C23.6716 4 23 4.67157 23 5.5V30.5C23 31.3284 23.6716 32 24.5 32H27.5C28.3284 32 29 31.3284 29 30.5V5.5C29 4.67157 28.3284 4 27.5 4H24.5Z"></path>
                                        </svg>
                                    </button>
                                    {{-- PLAY BUTTON --}}
                                    <button x-cloak x-show="!$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                            class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-200 focus:ring-offset-2 md:h-14 md:w-14"
                                            aria-label="Play">
                                        <div class="absolute -inset-3 md:hidden"></div>
                                        <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                            <path d="M33.75 16.701C34.75 17.2783 34.75 18.7217 33.75 19.299L11.25 32.2894C10.25 32.8668 9 32.1451 9 30.9904L9 5.00962C9 3.85491 10.25 3.13323 11.25 3.71058L33.75 16.701Z"></path>
                                        </svg>
                                    </button>
                                </div>
                                {{-- FORWARD BUTTON --}}
                                <button x-on:click="$store.AudioPlayer.skipForward()" type="button" class="group relative rounded-full focus:outline-hidden" aria-label="Fast-forward 10 seconds">
                                    <div class="absolute -inset-4 -left-2 md:hidden"></div>
                                    <svg aria-hidden="true" viewBox="0 0 24 24" fill="none" class="h-6 w-6 stroke-slate-300 group-hover:stroke-slate-500">
                                        <path d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M13 15V19" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18 19H17C16.4477 19 16 18.5523 16 18Z" stroke-width="1.5" stroke-linecap="round"
                                              stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </div>
                            <div role="group" id="react-aria7471806458-:r0:" aria-labelledby="react-aria7471806458-:r1:" class="absolute inset-x-0 bottom-full flex flex-auto touch-none items-center gap-6 md:relative">
                                <label class="sr-only" id="react-aria7471806458-:r1:">Current time</label>
                                <div style="position: relative; touch-action: none;" class="relative w-full bg-slate-400 md:rounded-full">
                                    {{-- TODO LATER -- this controls the background of the progress bar --}}
                                    <div class="h-2 md:rounded-l-xl md:rounded-r-md bg-slate-700" style="width: calc(0% - 0.25rem);"></div>
                                    <div id="progressBar" class="absolute top-1/2 -translate-x-1/2" style="left: 13.125%;">
                                        <div style="position: absolute; transform: translate(-50%, -50%); touch-action: none; left: 3.125%;" class="h-4 rounded-full w-1 bg-white">
                                            <div style="border: 0px; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(50%); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; white-space: nowrap;">
                                                <input tabindex="0" id="AudioPlayerSlider" aria-labelledby="Audio Player Slider" min="0" max="64" step="1" aria-orientation="horizontal" aria-valuetext="0 hours, 0 minutes, 0 seconds"
                                                       type="range" value="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="hidden items-center gap-2 md:flex">
                                    <output x-text="Alpine.store('AudioPlayer').currentTime" for="AudioPlayerSlider" aria-live="off" class="hidden rounded-md px-1 py-0.5 font-mono text-sm leading-6 md:block text-slate-100">00:02</output>
                                    <span class="text-sm leading-6 text-slate-300" aria-hidden="true">/</span>
                                    <span x-text="Alpine.store('AudioPlayer').duration" class="hidden rounded-md px-1 py-0.5 font-mono text-sm leading-6 text-slate-100 md:block">01:04</span>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            </section>
        </div>
    </div>

    <audio src="" id="podcastAudio" preload="none"></audio>

@endsection
