<!doctype html>
<html lang="{{ app()->getLocale() }}" class="dark h-full" x-data="{ darkMode: false }" :class="{ 'dark': darkMode }">
<head>
    <title>@yield('title', isset($title) ? $title . ' - ' : '') - {{ $account->name }}</title>

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <meta name="theme-color" content="#ffffff">

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="Description" content="Welcome to the church of Christ that meets in {{ $account->city }}, {{ $account->state }}! Explore our website, learn about who we are and come in for a visit! We would love to meet you."/>
    <meta name="keywords" content="Church, church in {{ $account->city }} {{ $account->state }}, church in {{ $account->city }}, {{ $account->city }}, {{ $account->state }}, church of Christ, elders, deacons, leadership, knowledge of the Word, Bible, baptizing them, truth, first century church, baptism, Lord's church, Lords church, Scripture, God's Word, bible study, worship, faith, community, believers">

    {{-- See: https://rsms.me/inter/ --}}
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">

    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
    <link href="" rel="stylesheet">
    <script src="" defer></script>
</head>

<body class="bg-transparent dark:bg-gray-700 h-full max-w-6xl mx-auto">

<header class="hidden md:flex min-h-24 flex-row justify-content-between mb-4 mx-2">
    <div class="shrink">
        <div class="hidden mt-auto text-4xl font-semibold">
            {{ $account->name }}
        </div>
        <a href="/">
            <img class="h-28 mt-2" src="https://katychurchofchrist.com/wp-content/themes/katy-church-bootstrap/img/katy-church-logo-2.svg" alt="Church of Christ">
        </a>
    </div>
    <div class="h-28 relative flex flex-col grow">
        <div class="absolute right-0 flex flex-row space-x-2 ml-auto">
            <div class="my-auto font-medium text-sm px-3 py-1 rounded-b text-white bg-stone-600">Join us this Sunday @ 9:00AM</div>
            {{--            <button--}}
            {{--                    x-data="{ darkMode: false }"--}}
            {{--                    @click="darkMode = !darkMode; document.documentElement.classList.toggle('dark')"--}}
            {{--                    class="px-1 rounded-md text-gray-800 dark:text-white"--}}
            {{--            >--}}
            {{--                <svg x-show="darkMode" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">--}}
            {{--                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"/>--}}
            {{--                </svg>--}}
            {{--                <svg x-show="!darkMode" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">--}}
            {{--                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"/>--}}
            {{--                </svg>--}}
            {{--            </button>--}}
        </div>
        <div class="my-auto flex">
            <div class="mt-4 flex gap-x-2 ml-auto bg-transparent">
                <a href="" class="my-auto text-gray-800 px-2 py-1.5 border border-transparent hover:border-gray-400 rounded-md">
                    About Us
                </a>
                <a href="" class="my-auto text-gray-800 px-2 py-1.5 border border-transparent hover:border-gray-400 rounded-md">
                    Service Times
                </a>
                <a href="" class="my-auto rounded-md bg-red-600 px-3 py-1.5 font-medium text-white hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                    Watch Live
                </a>
                <a href="" class="my-auto rounded-md bg-blue-600 px-3 py-1.5 font-medium text-white hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                    Plan a Visit
                    <x-heroicon-s-chevron-right class="w-4 h-4 mb-0.5 -mr-1 inline-block"/>
                </a>
            </div>
        </div>
    </div>
</header>

<div x-data="mobileMenu()" @keydown.escape="close">
    <!-- Sticky header for mobile -->
    <div class="sticky top-0 z-40 w-full bg-white shadow-xs md:hidden">
        <div class="flex items-center gap-x-6 px-4 py-4 sm:px-6">
            <button @click="open" type="button" class="-m-2.5 p-2.5 text-gray-700">
                <span class="sr-only">Open sidebar</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/>
                </svg>
            </button>
            <div class="flex-1 text-sm font-semibold leading-6 text-gray-900">Dashboard</div>
        </div>
    </div>

    <!-- Off-canvas menu for mobile -->
    <div x-show="isOpen" x-cloak class="fixed inset-0 z-50 md:hidden" role="dialog" aria-modal="true">
        <div x-show="isOpen"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="close"
             class="fixed inset-0 bg-gray-900/30"></div>

        <div class="fixed inset-0 flex">
            <div x-show="isOpen"
                 x-transition:enter="transition ease-in-out duration-300 transform"
                 x-transition:enter-start="-translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transition ease-in-out duration-300 transform"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="-translate-x-full"
                 class="p-2 relative mr-16 flex w-full max-w-xs flex-1"
                 @click.away="close"
                 x-ref="menu">
                <div x-show="isOpen"
                     x-transition:enter="ease-in-out duration-300"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in-out duration-300"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button @click="close" type="button" class="-my-2.5 -ml-6 p-1.5 border border-white rounded-md hover:bg-white/30">
                        <span class="sr-only">Close sidebar</span>
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <!-- Sidebar component for mobile -->
                <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-white rounded-lg px-6 pb-4">
                    <div class="flex shrink-0 items-center">
                        <img class="w-auto max-h-16" src="https://katychurchofchrist.com/wp-content/themes/katy-church-bootstrap/img/katy-church-logo-2.svg" alt="{{ $account->name}}">
                    </div>
                    <nav class="flex flex-1 flex-col">
                        <ul role="list" class="-mx-2 space-y-1">
                            <li>
                                <a href="#" class="bg-gray-50 text-blue-500 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                                    <svg class="h-6 w-6 shrink-0 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
                                    </svg>
                                    Home
                                </a>
                            </li>
                            <!-- Add more menu items here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-transparent grid grid-cols-12 mx-2">
        <!-- Your existing desktop sidebar -->
        <div class="hidden md:col-span-3 md:flex md:flex-col">
            <!-- Sidebar for desktop -->
            <div class="flex grow flex-col gap-y-5 overflow-y-auto">
                <nav class="flex flex-1 flex-col">
                    {{--                <div class="flex bg-red-500 text-white rounded-md text-center py-2 mb-2">--}}
                    {{--                    <div class="flex flex-row mx-auto space-x-2">--}}
                    {{--                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6">--}}
                    {{--                            <path stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z"/>--}}
                    {{--                        </svg>--}}
                    {{--                        <div>Watch Livestream</div>--}}
                    {{--                    </div>--}}
                    {{--                </div>--}}
                    @php
                        $active = 'bg-blue-600 text-white group flex gap-x-3 rounded-md p-2 leading-6 font-semibold';
                        $inactive = 'text-gray-700 dark:text-white hover:text-blue-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 leading-6 font-semibold';
                        $icon_active = 'h-6 w-6 shrink-0 text-white';
                        $icon_inactive = 'h-6 w-6 shrink-0 text-gray-400 group-hover:text-blue-600';
                    @endphp
                    <ul role="list" class="flex flex-1 flex-col gap-y-7">
                        <li>
                            <ul role="list" class="space-y-1">
                                <li>
                                    <a href="{{ route('websites.home', [], false) }}" class="{{ request()->routeIs('websites.home') ? $active : $inactive }}">
                                        <x-heroicon-s-home class="{{ request()->routeIs('websites.home') ? $icon_active : $icon_inactive }}"/>
                                        Home
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('websites.calendar', [], false) }}" class="{{ request()->routeIs('websites.calendar') ? $active : $inactive }}">
                                        <x-heroicon-s-calendar-days class="{{ request()->routeIs('websites.calendar') ? $icon_active : $icon_inactive }}"/>
                                        Calendar
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('websites.media', [], false) }}" class="{{ request()->routeIs('websites.sermons') ? $active : $inactive }}">
                                        <x-heroicon-s-speaker-wave class="{{ request()->routeIs('website.sermons') ? $icon_active : $icon_inactive }}"/>
                                        Sermons / Media
                                    </a>
                                </li>
                                <li>
                                    <a href="#" class="{{ request()->routeIs('websites.about') ? $active : $inactive }}">
                                        <x-heroicon-s-question-mark-circle class="{{ request()->routeIs('website.about') ? $icon_active : $icon_inactive }}"/>
                                        About Us
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('websites.files') }}" class="{{ request()->routeIs('websites.files') ? $active : $inactive }}">
                                        <svg class="{{ request()->routeIs('website.files') ? $icon_active : $icon_inactive }}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"/>
                                        </svg>
                                        Files
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('websites.leadership', [], false) }}" class="{{ request()->routeIs('websites.leadership') ? $active : $inactive }}">
                                        <x-heroicon-s-user-group class="{{ request()->routeIs('website.leadership') ? $icon_active : $icon_inactive }}"/>
                                        Leadership
                                    </a>
                                </li>
                                <li>
                                    <a href="#" class="{{ request()->routeIs('websites.prayers.request') ? $active : $inactive }}">
                                        <x-heroicon-s-bookmark class="{{ request()->routeIs('website.prayers.*') ? $icon_active : $icon_inactive }}"/>
                                        Request Prayers
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <div class="text-xs font-semibold leading-6 text-gray-400">Pages</div>
                            <ul role="list" class="mt-2 space-y-1">
                                <li>
                                    <!-- Current: "bg-gray-50 text-blue-600", Default: "text-gray-700 hover:text-blue-600 hover:bg-gray-50" -->
                                    <a href="#" class="text-gray-700 hover:text-blue-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                                        <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border text-[0.625rem] font-medium bg-white text-gray-400 border-gray-200 group-hover:border-blue-600 group-hover:text-blue-600">Y</span>
                                        <span class="truncate">Yellow Brick Road</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#" class="text-gray-700 hover:text-blue-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                                        <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border text-[0.625rem] font-medium bg-white text-gray-400 border-gray-200 group-hover:border-blue-600 group-hover:text-blue-600">M</span>
                                        <span class="truncate">Missions</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#" class="text-gray-700 hover:text-blue-600 hover:bg-gray-50 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                                        <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border text-[0.625rem] font-medium bg-white text-gray-400 border-gray-200 group-hover:border-blue-600 group-hover:text-blue-600">B</span>
                                        <span class="truncate">Bible Studies</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <main class="h-full col-span-12 md:col-span-9 mt-14 md:mt-0">
            <div class="pl-0 md:pl-6 mb-12">
                @yield('content')
            </div>
        </main>
    </div>
</div>

<footer class="bg-transparent" aria-labelledby="footer-heading">
    <h2 id="footer-heading" class="sr-only">Footer</h2>
    <div class="border-t border-gray-300 dark:border-gray-500 mx-auto max-w-6xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-12">
        <div class="xl:grid xl:grid-cols-3 xl:gap-8">
            <div class="space-y-8">
                <img class="h-16" src="https://katychurchofchrist.com/wp-content/themes/katy-church-bootstrap/img/katy-church-logo-no-background.svg" alt="Church Name">
                <div class="flex flex-col text-sm leading-6 text-gray-600">
                    <a href="https://www.google.com/maps/place/Church+of+Christ+In+Katy/@29.7903542,-95.8213647,17z/data=!3m1!4b1!4m5!3m4!1s0x86412428b62795ff:0x3aa9ba54fbd81a9a!8m2!3d29.7903542!4d-95.819176" class="text-black leading-snug">
                        {{ $account->address1 }}
                        {{ $account->address2 ? '<br/>' . $account->address2 : '' }}
                        <br/>
                        {{ $account->city }}, {{ $account->state }} {{ $account->postal_code }}
                    </a>
                    @if($account->phone_work)
                        <a href="tel:{{ $account->phone_work }}">{{ \App\Users\Phone::format($account->phone_work, '-') }}</a>
                    @endif
                </div>
                <div class="flex space-x-6">
                    <a href="https://www.facebook.com/katychurchofchrist" class="text-blue-500 hover:text-blue-700">
                        <span class="sr-only">Facebook</span>
                        <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"/>
                        </svg>
                    </a>
                    {{--                    <a href="#" class="text-purple-400 hover:text-purple-500">--}}
                    {{--                        <span class="sr-only">Instagram</span>--}}
                    {{--                        <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">--}}
                    {{--                            <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd"/>--}}
                    {{--                        </svg>--}}
                    {{--                    </a>--}}
                    {{--                    <a href="#" class="text-black hover:text-black">--}}
                    {{--                        <span class="sr-only">X</span>--}}
                    {{--                        <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">--}}
                    {{--                            <path d="M13.6823 10.6218L20.2391 3H18.6854L12.9921 9.61788L8.44486 3H3.2002L10.0765 13.0074L3.2002 21H4.75404L10.7663 14.0113L15.5685 21H20.8131L13.6819 10.6218H13.6823ZM11.5541 13.0956L10.8574 12.0991L5.31391 4.16971H7.70053L12.1742 10.5689L12.8709 11.5655L18.6861 19.8835H16.2995L11.5541 13.096V13.0956Z"/>--}}
                    {{--                        </svg>--}}
                    {{--                    </a>--}}
                    <a href="https://www.youtube.com/katychurchofchrist" class="text-red-500 hover:text-red-500">
                        <span class="sr-only">YouTube</span>
                        <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                            <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd"/>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900">About Us</h3>
                        <ul role="list" class="mt-4 space-y-1">
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Service Times</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Driving Directions</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Who Are We?</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Congregation History</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Our Leadership</a>
                            </li>
                        </ul>
                    </div>
                    <div class="mt-10 md:mt-0">
                        <h3 class="text-sm font-semibold text-gray-900">Media</h3>
                        <ul role="list" class="mt-4 space-y-1">
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Livestream</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Sermons &amp; Lessons</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Podcasts</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Files</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Facebook</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">YouTube</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900">Activities</h3>
                        <ul role="list" class="mt-4 space-y-1">
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Calendar Events</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Blog</a>
                            </li>
                        </ul>
                    </div>
                    <div class="mt-10 md:mt-0">
                        <h3 class="text-sm font-semibold text-gray-900">Visitors</h3>
                        <ul role="list" class="mt-4 space-y-1">
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Contact Us</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-600 hover:text-blue-500">Record of your visit</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-row justify-between text-sm mt-6 text-gray-500">
            <div>&copy; {{ date('Y') }} {{ $account->name }}. All rights reserved.</div>
            <div>
                <a href="{{ route('websites.privacy-policy', [], false) }}" class="text-gray-500 hover:text-gray-900">Privacy Policy</a>
                <span class=""> | </span>
                <a href="{{ route('websites.terms-of-service', [], false) }}" class="text-gray-500 hover:text-gray-900">Terms of Service</a>
            </div>
        </div>
    </div>
</footer>

@if(config('app.env') == 'production')
    <script src="https://cdn.usefathom.com/script.js" data-site="GHNHFDFW" defer></script>
@endif

@stack('scripts')

<script>
    function mobileMenu() {
        return {
            isOpen: false,
            open() {
                this.isOpen = true
            },
            close() {
                this.isOpen = false
            },
            init() {
                this.$watch('isOpen', (value) => {
                    if (value) {
                        document.body.classList.add('overflow-hidden')
                    } else {
                        document.body.classList.remove('overflow-hidden')
                    }
                })
            }
        }
    }
</script>

</body>
</html>