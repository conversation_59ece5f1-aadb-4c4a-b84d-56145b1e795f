@extends('website.template1._layout._app')

@section('title', 'Leadership')

@section('content')

    <div class="md:flex md:items-center md:justify-between">
        <div class="grow">
            <h1>Leadership</h1>
            <hr class="mb-2">
            <div class="mt-6">
                @foreach($leadership as $leader)
                    <div class="mb-16">
                        <h2>{{ $leader->plural_name  }}</h2>
                        <hr class="mb-2"/>
                        <div class="grid grid-cols-4 gap-x-4 gap-y-6">
                            @foreach($leader->users as $user)
                                <div class="flex flex-col text-center">
                                    @if($user->avatar)
                                        <img src="{{ $user->avatar->small_thumbnail_url }}" alt="{{ $user->name }}" class="max-w-48 rounded-lg mb-2"/>
                                    @else
                                        <div class="max-w-48 aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg mb-2">
                                            <x-heroicon-s-photo class="w-12 h-12 m-auto text-gray-400"/>
                                        </div>
                                    @endif
                                    <div class="text-lg font-medium">{{ $user->name }}</div>
                                    <div class="text-sm text-gray-400 uppercase">{{ $user->metadata->subtitle ?: $leader->name }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

@endsection