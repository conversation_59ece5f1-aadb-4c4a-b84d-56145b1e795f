@extends('website.template1._layout._app')

@section('title', 'Bible Class Registration')

@section('content')
    @if(request()->query('r') === 's')
        <div class="rounded-md bg-green-50 px-4 py-2 mb-6">
            <div class="flex items-center">
                <div class="shrink-0">
                    <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="font-medium text-green-800">
                        Your registration has been sent!
                    </p>
                </div>
            </div>
        </div>
    @endif

    <style>
        .blue-acorn {
            display: none;
        }
    </style>

    <div>
        <h1>
            Bible Class Registration
        </h1>

        <div class="">
            <form action="{{ route('websites.bible-class.registration.submit', [], false) }}" class="space-y-6" method="POST">
                @csrf

                <input type="hidden" name="sub_19493" value="sub_11492"/>

                <div class="blue-acorn">
                    <label for="first_name" class="block text-sm font-medium text-gray-700">First Name</label>
                    <div class="mt-1">
                        <textarea id="first_name" name="first_name"></textarea>
                    </div>
                </div>

                <div class="space-y-4">
                    <div>
                        <label for="lightpost_name" class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" name="lightpost_name" id="lightpost_name"
                               class="mt-1 block w-full rounded-md border p-2 border-gray-400 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <input x-cloak type="text" name="last_name" id="last_name" class="hidden"/>
                    </div>
                    <div>
                        <label for="lightpost_email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="text" name="lightpost_email" id="lightpost_email"
                               class="mt-1 block w-full rounded-md border p-2 border-gray-400 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>

                <div class="space-y-6">
                    @foreach($attendance_types as $attendance_type)
                        <div class="bg-gray-50 border border-gray-400 rounded-lg p-4">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">{{ $attendance_type->name }}</h2>
                            <div class="min-w-full">
                                <!-- Header -->
                                <div class="bg-gray-100 grid grid-cols-12 gap-4 px-3 py-3">
                                    <div class="col-span-1">&nbsp;</div>
                                    <div class="col-span-7 text-left text-sm font-semibold text-gray-900">Title</div>
                                    <div class="col-span-4 text-left text-sm font-semibold text-gray-900">Location</div>
                                </div>

                                <!-- Body -->
                                <fieldset class="bg-white divide-y divide-gray-200">
                                    <legend class="sr-only">{{ $attendance_type->name }}</legend>
                                    @foreach($bible_class_group->classes()->where('user_attendance_type_id', $attendance_type->id)->get() as $class)
                                        <div class="grid grid-cols-12 gap-4 px-3 py-4 hover:bg-gray-50 items-center">
                                            <div class="col-span-1 text-center">
                                                <input type="radio"
                                                       name="selected_classes_ids[{{ $attendance_type->id }}][]"
                                                       value="{{ $class->id }}"
                                                       id="class-{{ $class->id }}"
                                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                            </div>
                                            <div class="col-span-7">
                                                <label for="class-{{ $class->id }}" class="font-medium text-gray-900 cursor-pointer">
                                                    {{ $class->title }}
                                                </label>
                                            </div>
                                            <div class="col-span-4 text-gray-500">
                                                {{ $class->location_name }}
                                            </div>
                                        </div>
                                    @endforeach
                                </fieldset>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-6">
                    <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-xs text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Submit Registration
                    </button>
                    <div class="cf-turnstile mt-6"
                         data-sitekey="{{ config('services.cloudflare.site_key') }}"
                    ></div>
                </div>
            </form>
        </div>
        <div class="space-y-6 mt-12">
            @foreach($attendance_types as $attendance_type)
                <h2 class="font-medium">{{ $attendance_type->name }}</h2>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="overflow-x-auto space-y-4">
                        @foreach($bible_class_group->classes()->where('user_attendance_type_id', $attendance_type->id)->get() as $class)
                            <div>
                                <h4>
                                    {{ $class->title }}
                                </h4>
                                <div>
                                    {{ $class->description }}
                                </div>
                                <div class="flex flex-row space-x-2 text-sm mt-2">
                                    @foreach($class->teachers as $teacher)
                                        <div class="bg-gray-700 text-white rounded-sm px-2 py-0.5">
                                            {{ $teacher->name }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@endsection