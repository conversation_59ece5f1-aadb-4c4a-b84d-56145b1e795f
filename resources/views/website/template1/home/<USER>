@extends('website.template1._layout._app')

@section('title', 'Home')

@section('content')

    @if($carousel)
        <div class="mb-8 relative max-w-4xl mx-auto" style="max-height: 475px;" x-data="carousel()" x-init="startAutoRotation()">
            <div class="overflow-hidden relative rounded-lg shadow-lg border border-gray-200">
                <!-- Slides -->
                <div class="flex transition-transform duration-500 ease-in-out" 
                     :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
                     @touchstart="handleTouchStart($event)"
                     @touchend="handleTouchEnd($event)">
                    <template x-for="(slide, index) in slides" :key="index">
                        <div class="w-full shrink-0 relative">
                            <a :href="slide.link" class="block w-full h-full" style="max-height: 500px" @click.stop>
                                <img :src="slide.image" :alt="slide.alt" class="w-full object-cover">
                                <div x-show="currentIndex === index && (slide.title || slide.description)"
                                     class="absolute bottom-0 left-0 right-0 p-4 bg-black bg-opacity-50 text-white">
                                    <h3 x-show="slide.title" x-html="slide.title" class="text-xl font-bold"></h3>
                                    <p x-show="slide.description" x-html="slide.description" class="mt-2"></p>
                                </div>
                            </a>
                        </div>
                    </template>
                </div>

                <!-- Navigation arrows -->
                <button @click.stop="prevSlide()" class="absolute top-1/2 left-2 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-2 focus:outline-hidden hover:bg-opacity-75 z-10">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button @click.stop="nextSlide()" class="absolute top-1/2 right-2 transform -translate-y-1/2 bg-white bg-opacity-50 rounded-full p-2 focus:outline-hidden hover:bg-opacity-75 z-10">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Indicators -->
                <div class="absolute bottom-4 left-0 right-0 z-10">
                    <div class="flex items-center justify-center space-x-2">
                        <template x-for="(_, index) in slides" :key="index">
                            <div class="flex flex-col items-center">
                                <button
                                        @click.stop="goToSlide(index)"
                                        class="w-3 h-2 rounded-full focus:outline-hidden transition-colors duration-200 ease-in-out border border-white"
                                        :class="{ 'bg-white': currentIndex === index, 'bg-transparent': currentIndex !== index }"
                                ></button>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        @if($featured_sermon)
            <div class="mb-6 mx-auto">
                <div class="bg-gray-50 rounded-lg shadow-xs border border-gray-300">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center px-4 py-3 sm:py-4 gap-2 sm:gap-2">
                        <!-- Play Button -->
                        <div class="hidden sm:flex shrink-0">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center text-black">
                                <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.3" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.3" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </div>

                        <!-- Featured Label & Date -->
                        <div class="shrink-0 border-transparent sm:border-r sm:border-gray-400 pr-4">
                            <div class="text-sm font-semibold text-gray-600 uppercase tracking-wider">Featured Sermon</div>
                            <div class="text-sm text-gray-600">{{ $featured_sermon->date_sermon?->format('F j, Y') }}</div>
                        </div>

                        <!-- Title -->
                        <div class="grow sm:ml-2">
                            <h3 class="text-lg font-medium text-gray-900 my-auto">{{ $featured_sermon->title }}</h3>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center gap-3 shrink-0">
                            <a href="{{ route('websites.media.sermon', $featured_sermon->id, false) }}" 
                               class="inline-flex items-center text-white bg-blue-600 hover:bg-blue-800 px-3 py-2 rounded-xl font-medium">
                                <span>Watch</span>
                                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z" />
                                </svg>
                            </a>
                            <a href="{{ route('websites.media', [], false) }}" 
                               class="inline-flex items-center text-gray-600 border border-gray-600 hover:bg-gray-100 rounded-xl px-3 py-2 hover:text-gray-800 font-medium">
                                Sermons
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <script>
            function carousel() {
                return {
                    currentIndex: 0,
                    autoRotationTimer: null,
                    touchStartX: 0,
                    touchEndX: 0,
                    slides: [
                        @foreach($carousel as $item)
                            @php
                                $image = \App\Website\WebsiteFile::find($item['website_file_id']);
                                if ($item['link_type'] === 'page' && !empty($item['website_page_id'])) {
                                    $page = \App\Website\WebsitePage::find($item['website_page_id']);
                                    $link = $page ? $page->getUrl() : '#';
                                } else {
                                    $link = $item['link'] ?? '#';
                                }
                            @endphp
                        {
                            image: '{{ $image?->getUrl() }}', 
                            alt: '{{ $item['title'] }}', 
                            title: '{{ $item['title'] }}', 
                            description: '{{ $item['description'] }}', 
                            link: '{{ $link }}'
                        },
                        @endforeach
                    ],
                    handleTouchStart(event) {
                        this.touchStartX = event.touches[0].clientX;
                    },
                    handleTouchEnd(event) {
                        this.touchEndX = event.changedTouches[0].clientX;
                        const swipeDistance = this.touchEndX - this.touchStartX;
                        
                        // Minimum distance for a swipe
                        if (Math.abs(swipeDistance) > 50) {
                            if (swipeDistance > 0) {
                                this.prevSlide();
                            } else {
                                this.nextSlide();
                            }
                        }
                    },
                    nextSlide() {
                        this.goToSlide((this.currentIndex + 1) % this.slides.length);
                    },
                    prevSlide() {
                        this.goToSlide((this.currentIndex - 1 + this.slides.length) % this.slides.length);
                    },
                    goToSlide(index) {
                        this.currentIndex = index;
                        this.resetAutoRotation();
                    },
                    startAutoRotation() {
                        this.autoRotationTimer = setInterval(() => {
                            this.nextSlide();
                        }, 7000);
                    },
                    resetAutoRotation() {
                        clearInterval(this.autoRotationTimer);
                        this.startAutoRotation();
                    },
                }
            }
        </script>
    @endif

    @if($page_content)
        {!! $page_content !!}
    @endif

@endsection