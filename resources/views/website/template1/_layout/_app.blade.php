<!doctype html>
<html lang="{{ app()->getLocale() }}" class="h-full" x-data="{ darkMode: false }" :class="{ 'dark': darkMode }">
<head>
    <title>@yield('title', isset($title) ? $title . ' - ' : '') - {{ $account->name }}</title>

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <meta name="theme-color" content="#ffffff">

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="Description" content="Welcome to the church of Christ that meets in {{ $account->city }}, {{ $account->state }}! Explore our website, learn about who we are and come in for a visit! We would love to meet you."/>
    <meta name="keywords" content="Church, church in {{ $account->city }} {{ $account->state }}, church in {{ $account->city }}, {{ $account->city }}, {{ $account->state }}, church of Christ, elders, deacons, leadership, knowledge of the Word, Bible, baptizing them, truth, first century church, baptism, Lord's church, Lords church, Scripture, God's Word, bible study, worship, faith, community, believers">

    <meta name="generator" content="Lightpost Website v1.0"/>
    <meta property="og:locale" content="en_US"/>
    <meta property="og:site_name" content="{{ $account->name }}"/>
    <meta property="og:type" content="article"/>
    <meta property="og:title" content="@yield('title', isset($title) ? $title . ' - ' : '') - {{ $account->name }}"/>
    <meta property="og:description" content=""/>
    <meta property="og:url" content="{{ request()->getUri() }}"/>
    <meta property="article:published_time" content=""/>
    <meta property="article:modified_time" content=""/>
    <meta name="twitter:card" content="summary_large_image"/>
    <meta name="twitter:title" content="@yield('title', isset($title) ? $title . ' - ' : '') - {{ $account->name }}"/>
    <meta name="twitter:description" content=""/>

    {{-- See: https://rsms.me/inter/ --}}
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">

    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
    <link href="{{ mix('/static/website/css/tailwind.website.css') }}" rel="stylesheet">
    <script src="{{ mix('static/website/js/website.js') }}" defer></script>

    @php
        $quick_links = \App\Website\WebsiteSettingValue::query()
            ->where('account_id', request()->get('account_id'))
            ->whereHas('setting', function ($query) {
                $query->where('key', 'quick_links');
            })
            ->first()
            ?->value;

        $quick_links = json_decode($quick_links, true);
    @endphp

    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" defer></script>
</head>

@php
    $active = 'last-of-type:rounded-b-lg first-of-type:rounded-t-lg bg-blue-600 text-white group flex gap-x-3 px-2 py-2.5 leading-6 font-semibold';
    $inactive = 'last-of-type:rounded-b-lg first-of-type:rounded-t-lg text-gray-700 dark:text-white hover:text-blue-600 hover:bg-gray-100 group flex gap-x-3 px-2 py-2.5 leading-6 font-semibold';
    $icon_active = 'h-6 w-6 shrink-0 text-white';
    $icon_inactive = 'h-6 w-6 shrink-0 text-gray-400 group-hover:text-blue-500';
@endphp

<body class="bg-transparent dark:bg-gray-700 h-full max-w-6xl mx-auto">

<header class="hidden md:flex min-h-24 flex-row justify-content-between mb-4 mx-2">
    <div class="shrink my-auto">
        <div class="hidden mt-auto text-4xl font-semibold">
            {{ $account->name }}
        </div>
        <a href="/">
            @if((new \App\Website\Services\GetWebsiteSettingValue())->get('logo.main.light.website_file_id'))
                <img class="h-28 mt-2 font-semibold text-2xl text-gray-800" src="{{ (new \App\Website\Services\GetWebsiteSettingValue())->get('logo.main.light.website_file_id')?->getUrl() }}" alt="{{ $account->name }}">
            @else
                <h1 class="mt-auto text-gray-800">{{ $account->name }}</h1>
            @endif
        </a>
    </div>
    <div class="h-28 relative flex flex-col grow">
        <div class="absolute right-0 flex flex-row space-x-2 ml-auto">
            @if($service_times_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.service_times'))
                <a href="{{ \Illuminate\Support\Arr::get($service_times_link, 'url') }}" class="my-auto font-medium text-sm px-3 py-1 text-stone-800">Service Times</a>
            @endif
            <a href="{{ route('app.home.index') }}" class="my-auto font-medium text-sm px-3 py-1 rounded-b text-white bg-stone-600">Member Login</a>
            {{--            <button--}}
            {{--                    x-data="{ darkMode: false }"--}}
            {{--                    @click="darkMode = !darkMode; document.documentElement.classList.toggle('dark')"--}}
            {{--                    class="px-1 rounded-md text-gray-800 dark:text-white"--}}
            {{--            >--}}
            {{--                <svg x-show="darkMode" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">--}}
            {{--                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"/>--}}
            {{--                </svg>--}}
            {{--                <svg x-show="!darkMode" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4">--}}
            {{--                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"/>--}}
            {{--                </svg>--}}
            {{--            </button>--}}
        </div>
        <div class="flex-1 flex">
            <div class="mt-auto flex gap-x-3 ml-auto bg-transparent">
                @if($watch_live_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.livestream'))
                    <a href="{{ $watch_live_link['url'] }}" class="my-auto rounded-md bg-red-600 px-3 py-1.5 font-medium border border-red-500 text-white hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                        Watch Live
                    </a>
                @endif
                @if($plan_a_visit_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.plan_a_visit'))
                    <a href="{{ $plan_a_visit_link['url'] }}" class="my-auto rounded-md bg-blue-600 px-3 py-1.5 font-medium border border-blue-500 text-white hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                        Plan a Visit
                        <x-heroicon-s-chevron-right class="w-4 h-4 mb-0.5 -mr-1 inline-block"/>
                    </a>
                @endif
            </div>
        </div>
    </div>
</header>

<div x-data="mobileMenu()" @keydown.escape="close">
    {{--    <!-- Sticky header for mobile -->--}}
    <div class="sticky top-0 z-40 w-full bg-white shadow-xs md:hidden" style="position: -webkit-sticky;">
        <div class="relative flex items-center justify-center px-4 py-4 sm:px-6 font-semibold text-lg text-gray-900">
            <button @click="open" type="button" class="absolute left-4 p-2 text-gray-800 border border-gray-600 rounded-lg">
                <span class="sr-only">Open sidebar</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/>
                </svg>
            </button>
            <a href="/" class="grow">
                @if((new \App\Website\Services\GetWebsiteSettingValue())->get('logo.main.light.website_file_id'))
                    <img class="mx-auto max-h-28 max-w-48" src="{{ (new \App\Website\Services\GetWebsiteSettingValue())->get('logo.main.light.website_file_id')?->getUrl() }}" alt="{{ $account->name}}"/>
                @else
                    <div class="text-center text-gray-900">{{ $account->name }}</div>
                @endif
            </a>
        </div>
    </div>

    {{--    <!-- Off-canvas menu for mobile -->--}}
    <div x-show="isOpen" x-cloak class="fixed inset-0 z-50 md:hidden" role="dialog" aria-modal="true">
        <div x-show="isOpen"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="close"
             class="fixed inset-0 bg-gray-900/30"></div>

        <div class="fixed inset-0 flex">
            <div x-show="isOpen"
                 x-transition:enter="transition ease-in-out duration-300 transform"
                 x-transition:enter-start="-translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transition ease-in-out duration-300 transform"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="-translate-x-full"
                 class="p-2 relative mr-16 flex w-full max-w-xs flex-1"
                 @click.away="close"
                 x-ref="menu">
                <div x-show="isOpen"
                     x-transition:enter="ease-in-out duration-300"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in-out duration-300"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button @click="close" type="button" class="-my-2.5 -ml-6 p-1.5 border border-white rounded-md hover:bg-white/30">
                        <span class="sr-only">Close sidebar</span>
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                {{--                <!-- Sidebar component for mobile -->--}}
                <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-white rounded-lg px-6 pb-4">
                    <div class="flex shrink-0 items-center">
                        @if((new \App\Website\Services\GetWebsiteSettingValue())->get('logo.main.light.website_file_id'))
                            <img class="mx-auto mt-4 max-h-16" src="{{ (new \App\Website\Services\GetWebsiteSettingValue())->get('logo.main.light.website_file_id')?->getUrl() }}" alt="{{ $account->name}}">
                        @else
                            <h3 class="mt-4 text-gray-800">{{ $account->name }}</h3>
                        @endif
                    </div>
                    <nav class="flex flex-1 flex-col -mx-2 space-y-6">
                        <item>
                            <nav role="list" class="border border-gray-300 rounded-lg">
                                <a href="{{ route('websites.home', [], false) }}" class="{{ request()->routeIs('websites.home') ? $active : $inactive }}">
                                    <x-heroicon-s-home class="{{ request()->routeIs('websites.home') ? $icon_active : $icon_inactive }}"/>
                                    Home
                                </a>
                                @if(\App\Website\WebsiteSettingValue::query()
                                   ->where('account_id', request()->get('account_id'))
                                   ->whereHas('setting', function ($query) {
                                       $query->where('key', 'calendars.enabled');
                                   })
                                   ->first()
                                   ?->value)
                                    <a href="{{ route('websites.calendar', [], false) }}" class="{{ request()->routeIs('websites.calendar') ? $active : $inactive }}">
                                        <x-heroicon-s-calendar-days class="{{ request()->routeIs('websites.calendar') ? $icon_active : $icon_inactive }}"/>
                                        Calendar
                                    </a>
                                @endif
                                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('sermons.enabled'))
                                    <a href="{{ route('websites.media', [], false) }}" class="{{ request()->routeIs('websites.media*') ? $active : $inactive }}">
                                        <x-heroicon-s-speaker-wave class="{{ request()->routeIs('websites.media*') ? $icon_active : $icon_inactive }}"/>
                                        Sermons / Media
                                    </a>
                                @endif
                                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('files.enabled'))
                                    <a href="{{ route('websites.files', [], false) }}" class="{{ request()->routeIs('websites.files') ? $active : $inactive }}">
                                        <svg class="{{ request()->routeIs('websites.files') ? $icon_active : $icon_inactive }}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"/>
                                        </svg>
                                        Files
                                    </a>
                                @endif
                            </nav>
                        </item>
                        <item>
                            @if((new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.livestream') || (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.plan_a_visit'))
                                <div class="flex w-full gap-x-2">
                                    @if($watch_live_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.livestream'))
                                        <a href="{{ $watch_live_link['url'] }}" class="flex-1 text-center rounded-md bg-white px-3 py-1.5 font-medium text-red-600 hover:text-white border border-red-600 hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                                            Watch Live
                                        </a>
                                    @endif
                                    @if($plan_a_visit_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.plan_a_visit'))
                                        <a href="{{ $plan_a_visit_link['url'] }}" class="flex-1 text-center rounded-md bg-white px-3 py-1.5 font-medium text-blue-600 hover:text-white border border-blue-600 hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                                            Plan a Visit
                                            <x-heroicon-s-chevron-right class="w-4 h-4 mb-0.5 -mr-1 inline-block"/>
                                        </a>
                                    @endif
                                </div>
                            @endif
                            @if((new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.about') || (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.service_times'))
                                <div class="mt-2 flex w-full gap-x-2">
                                    @if($about_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.about'))
                                        <a href="{{ \Illuminate\Support\Arr::get($about_link, 'url') }}" class="flex-1 text-center my-auto text-gray-800 px-2 py-1.5 border border-gray-300 hover:border-gray-400 rounded-md">
                                            About Us
                                        </a>
                                    @endif
                                    @if($service_times_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.service_times'))
                                        <a href="{{ \Illuminate\Support\Arr::get($service_times_link, 'url') }}" class="flex-1 text-center my-auto text-gray-800 px-2 py-1.5 border border-gray-300 hover:border-gray-400 rounded-md">
                                            Service Times
                                        </a>
                                    @endif
                                </div>
                            @endif
                        </item>
                        @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('bible_class_registration.enabled'))
                            <item>
                                <nav role="list" class="border border-gray-300 rounded-lg">
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('bible_class_registration.enabled'))
                                        <a href="{{ route('websites.bible-class.registration', [], false) }}" class="{{ request()->routeIs('websites.bible-class.registration') ? $active : $inactive }}">
                                            <x-heroicon-s-check-badge class="{{ request()->routeIs('websites.bible-class.registration') ? $icon_active : $icon_inactive }}"/>
                                            Class Registration
                                        </a>
                                    @endif
                                </nav>
                            </item>
                        @endif
                        @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('leadership.enabled')
                        || (new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('prayer_requests.enabled')
                        || (new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('contact.enabled'))
                            <item>
                                <nav role="list" class="border border-gray-300 rounded-lg">
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('leadership.enabled'))
                                        <a href="{{ route('websites.leadership', [], false) }}" class="{{ request()->routeIs('websites.leadership') ? $active : $inactive }}">
                                            <x-heroicon-s-user-group class="{{ request()->routeIs('websites.leadership') ? $icon_active : $icon_inactive }}"/>
                                            Leadership
                                        </a>
                                    @endif
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('prayer_requests.enabled'))
                                        <a href="{{ route('websites.prayer-request', [], false) }}" class="last-of-type:rounded-b-lg {{ request()->routeIs('websites.prayer-request') ? $active : $inactive }}">
                                            <x-heroicon-s-bookmark class="{{ request()->routeIs('websites.prayer-request*') ? $icon_active : $icon_inactive }}"/>
                                            Request Prayers
                                        </a>
                                    @endif
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('contact.enabled'))
                                        <a href="{{ route('websites.contact', [], false) }}" class="last-of-type:rounded-b-lg {{ request()->routeIs('websites.contact') ? $active : $inactive }}">
                                            <x-heroicon-s-at-symbol class="{{ request()->routeIs('websites.contact') ? $icon_active : $icon_inactive }}"/>
                                            Contact Us
                                        </a>
                                    @endif
                                </nav>
                            </item>
                        @endif
                        <item>
                            <nav role="list" class="text-center">
                                <a href="{{ route('app.home.index') }}" class="text-sm rounded-lg bg-stone-600 hover:bg-stone-500 text-white font-medium px-4 py-1.5">
                                    Member Login
                                </a>
                            </nav>
                        </item>
                        @if($quick_links)
                            <item class="mb-6">
                                <div class="text-sm font-semibold leading-6 text-gray-400">Quick Links</div>
                                <nav role="list" class="mt-2 space-y-1">
                                    @foreach($quick_links as $link)
                                        <item>
                                            <a href="{{ @$link['website_page_id'] ? \App\Website\WebsitePage::find($link['website_page_id'])?->getURL() : @$link['link'] }}"
                                               class="text-gray-700 hover:text-blue-600 hover:bg-gray-100 group flex gap-x-3 rounded-md p-2 leading-6 font-semibold">
                                                    <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border text-xs font-medium bg-white text-gray-400 border-gray-200 group-hover:border-blue-600 group-hover:text-blue-600">
                                                        {{ \Illuminate\Support\Str::upper(substr($link['title'], 0, 1)) }}
                                                    </span>
                                                <span class="truncate">{{ @$link['title'] }}</span>
                                            </a>
                                        </item>
                                    @endforeach
                                </nav>
                            </item>
                        @endif
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-transparent grid grid-cols-12 mx-2">
        {{--        <!-- Your existing desktop sidebar -->--}}
        <div class="hidden md:col-span-3 md:flex md:flex-col">
            {{--            <!-- Sidebar for desktop -->--}}
            <div class="flex grow flex-col gap-y-5 overflow-y-auto">
                <nav class="flex flex-1 flex-col">
                    {{--                <div class="flex bg-red-500 text-white rounded-md text-center py-2 mb-2">--}}
                    {{--                    <div class="flex flex-row mx-auto space-x-2">--}}
                    {{--                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6">--}}
                    {{--                            <path stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z"/>--}}
                    {{--                        </svg>--}}
                    {{--                        <div>Watch Livestream</div>--}}
                    {{--                    </div>--}}
                    {{--                </div>--}}
                    <nav role="list" class="flex flex-1 flex-col gap-y-7">
                        <item>
                            <nav role="list" class="border border-gray-300 rounded-lg">
                                <a href="{{ route('websites.home', [], false) }}" class="{{ request()->routeIs('websites.home') ? $active : $inactive }}">
                                    <x-heroicon-s-home class="{{ request()->routeIs('websites.home') ? $icon_active : $icon_inactive }}"/>
                                    Home
                                </a>
                                @if(\App\Website\WebsiteSettingValue::query()
                                   ->where('account_id', request()->get('account_id'))
                                   ->whereHas('setting', function ($query) {
                                       $query->where('key', 'calendars.enabled');
                                   })
                                   ->first()
                                   ?->value)
                                    <a href="{{ route('websites.calendar', [], false) }}" class="{{ request()->routeIs('websites.calendar') ? $active : $inactive }}">
                                        <x-heroicon-s-calendar-days class="{{ request()->routeIs('websites.calendar') ? $icon_active : $icon_inactive }}"/>
                                        Calendar
                                    </a>
                                @endif
                                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('sermons.enabled'))
                                    <a href="{{ route('websites.media', [], false) }}" class="{{ request()->routeIs('websites.media*') ? $active : $inactive }}">
                                        <x-heroicon-s-speaker-wave class="{{ request()->routeIs('websites.media*') ? $icon_active : $icon_inactive }}"/>
                                        Sermons / Media
                                    </a>
                                @endif
                                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('files.enabled'))
                                    <a href="{{ route('websites.files', [], false) }}" class="{{ request()->routeIs('websites.files') ? $active : $inactive }}">
                                        <svg class="{{ request()->routeIs('websites.files') ? $icon_active : $icon_inactive }}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"/>
                                        </svg>
                                        Files
                                    </a>
                                @endif
                            </nav>
                        </item>
                        @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('bible_class_registration.enabled'))
                            <item>
                                <nav role="list" class="border border-gray-300 rounded-lg">
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('bible_class_registration.enabled'))
                                        <a href="{{ route('websites.bible-class.registration', [], false) }}" class="{{ request()->routeIs('websites.bible-class.registration') ? $active : $inactive }}">
                                            <x-heroicon-s-check-badge class="{{ request()->routeIs('websites.bible-class.registration') ? $icon_active : $icon_inactive }}"/>
                                            Class Registration
                                        </a>
                                    @endif
                                </nav>
                            </item>
                        @endif
                        @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('leadership.enabled')
                            || (new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('prayer_requests.enabled')
                            || (new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('contact.enabled'))
                            <item>
                                <nav role="list" class="border border-gray-300 rounded-lg">
                                    @if($about_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.about'))
                                        <a href="{{ \Illuminate\Support\Arr::get($about_link, 'url') }}" class="rounded-t-lg {{ request()->routeIs('websites.about') ? $active : $inactive }}">
                                            <x-heroicon-s-question-mark-circle class="{{ request()->routeIs('websites.about') ? $icon_active : $icon_inactive }}"/>
                                            About Us
                                        </a>
                                    @endif
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('leadership.enabled'))
                                        <a href="{{ route('websites.leadership', [], false) }}" class="{{ request()->routeIs('websites.leadership') ? $active : $inactive }}">
                                            <x-heroicon-s-user-group class="{{ request()->routeIs('websites.leadership') ? $icon_active : $icon_inactive }}"/>
                                            Leadership
                                        </a>
                                    @endif
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('prayer_requests.enabled'))
                                        <a href="{{ route('websites.prayer-request', [], false) }}" class="last-of-type:rounded-b-lg {{ request()->routeIs('websites.prayer-request') ? $active : $inactive }}">
                                            <x-heroicon-s-bookmark class="{{ request()->routeIs('websites.prayer-request*') ? $icon_active : $icon_inactive }}"/>
                                            Request Prayers
                                        </a>
                                    @endif
                                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('contact.enabled'))
                                        <a href="{{ route('websites.contact', [], false) }}" class="last-of-type:rounded-b-lg {{ request()->routeIs('websites.contact') ? $active : $inactive }}">
                                            <x-heroicon-s-at-symbol class="{{ request()->routeIs('websites.contact') ? $icon_active : $icon_inactive }}"/>
                                            Contact Us
                                        </a>
                                    @endif
                                </nav>
                            </item>
                        @endif
                        @if($quick_links)
                            <div class="mb-6">
                                <div class="text-sm font-semibold leading-6 text-gray-400">Quick Links</div>
                                <nav role="list" class="mt-2 space-y-1">
                                    @foreach($quick_links as $link)
                                        <item>
                                            <a href="{{ @$link['website_page_id'] ? \App\Website\WebsitePage::find($link['website_page_id'])?->getURL() : @$link['link'] }}"
                                               class="text-gray-700 hover:text-blue-600 hover:bg-gray-100 group flex gap-x-3 rounded-md p-2 leading-6 font-semibold">
                                                <span class="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border text-xs font-medium bg-white text-gray-400 border-gray-200 group-hover:border-blue-600 group-hover:text-blue-600">
                                                    {{ \Illuminate\Support\Str::upper(substr($link['title'], 0, 1)) }}
                                                </span>
                                                <span class="truncate">{{ @$link['title'] }}</span>
                                            </a>
                                        </item>
                                    @endforeach
                                </nav>
                            </div>
                        @endif
                    </nav>
                </nav>
            </div>
        </div>

        <!-- Main content -->
        <main class="h-full col-span-12 md:col-span-9 mt-4 md:mt-0">
            <div class="pl-0 md:pl-6 mb-12">
                @yield('content')
            </div>
        </main>
    </div>
</div>

<footer class="bg-transparent" aria-labelledby="footer-heading">
    <h2 id="footer-heading" class="sr-only">Footer</h2>
    <div class="border-t border-gray-300 dark:border-gray-500 mx-auto max-w-6xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-12">
        <div class="lg:grid lg:grid-cols-3 lg:gap-8">
            <div class="space-y-8">
                @if($footer_logo = (new \App\Website\Services\GetWebsiteSettingValue())->get('logo.footer.light.website_file_id'))
                    <img class="h-16" src="{{ $footer_logo->getUrl() }}" alt="{{ $account->name }}">
                @endif
                <div class="flex flex-col text-sm leading-6 text-gray-600">
                    <h4 class="my-auto text-base font-semibold">{{ $account->name }}</h4>
                    <a href="https://www.google.com/maps/place/{{ $account->getAddressString(true) }}" class="text-black leading-snug" target="_blank">
                        {{ $account->address1 }}
                        {{ $account->address2 ? '<br/>' . $account->address2 : '' }}
                        <br/>
                        {{ $account->city }}, {{ $account->state }} {{ $account->postal_code }}
                    </a>
                    @if($account->phone_work)
                        <a href="tel:{{ $account->phone_work }}">{{ \App\Users\Phone::format($account->phone_work, '-') }}</a>
                    @endif
                </div>
                <div class="flex flex-row space-x-6">
                    @if($links_facebook = \App\Website\WebsiteSettingValue::query()
                       ->where('account_id', request()->get('account_id'))
                       ->whereHas('setting', function ($query) {
                           $query->where('key', 'links.facebook');
                       })
                       ->first()
                       ?->value)
                        <a href="{{ $links_facebook }}" class="text-blue-500 hover:text-blue-700">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    @endif
                    @if($links_vimeo = \App\Website\WebsiteSettingValue::query()
                       ->where('account_id', request()->get('account_id'))
                       ->whereHas('setting', function ($query) {
                           $query->where('key', 'links.vimeo');
                       })
                       ->first()
                       ?->value)
                        <a href="{{ $links_vimeo }}" class="text-gray-400 hover:text-gray-500">
                            <span class="sr-only">Vimeo</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="currentColor" viewBox="-0.001 0.001 1158.763 999.998">
                                <path d="M1158.195 231.332c-5.193 112.53-83.965 266.609-236.312 462.239C764.34 897.856 631.034 999.999 521.967 999.999c-67.519 0-124.648-62.324-171.393-186.973l-93.486-342.784c-34.625-124.648-71.847-186.973-111.665-186.973-8.656 0-38.953 18.178-90.891 54.534L-.001 267.688a14811.618 14811.618 0 0 0 168.795-150.617C244.968 51.284 302.099 16.66 340.186 13.197 430.21 4.541 485.609 66 506.384 197.573c22.506 141.961 38.087 230.254 46.742 264.879 25.969 117.724 54.534 176.586 85.696 176.586 24.237 0 60.594-38.087 109.068-114.261 48.475-76.175 74.443-134.171 77.905-173.989 6.925-65.787-19.043-98.68-77.905-98.68-27.7 0-56.266 6.06-85.696 18.178C719.325 85.044 828.393-4.98 989.398.214c119.455 3.463 175.721 80.503 168.795 231.12l.002-.002z"/>
                            </svg>
                        </a>
                    @endif
                    @if($links_twitter = \App\Website\WebsiteSettingValue::query()
                       ->where('account_id', request()->get('account_id'))
                       ->whereHas('setting', function ($query) {
                           $query->where('key', 'links.twitter');
                       })
                       ->first()
                       ?->value)
                        <a href="{{ $links_twitter }}" class="text-black hover:text-black">
                            <span class="sr-only">X</span>
                            <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M13.6823 10.6218L20.2391 3H18.6854L12.9921 9.61788L8.44486 3H3.2002L10.0765 13.0074L3.2002 21H4.75404L10.7663 14.0113L15.5685 21H20.8131L13.6819 10.6218H13.6823ZM11.5541 13.0956L10.8574 12.0991L5.31391 4.16971H7.70053L12.1742 10.5689L12.8709 11.5655L18.6861 19.8835H16.2995L11.5541 13.096V13.0956Z"/>
                            </svg>
                        </a>
                    @endif
                    @if($links_youtube = \App\Website\WebsiteSettingValue::query()
                        ->where('account_id', request()->get('account_id'))
                        ->whereHas('setting', function ($query) {
                            $query->where('key', 'links.youtube');
                        })
                        ->first()
                        ?->value)
                        <a href="{{ $links_youtube }}" class="text-red-500 hover:text-red-500">
                            <span class="sr-only">YouTube</span>
                            <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    @endif
                </div>
            </div>
            <div class="mt-16 grid grid-cols-2 gap-8 lg:col-span-2 lg:mt-0">
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900">About Us</h3>
                        <nav role="list" class="flex flex-col mt-4 space-y-1">
                            @if($service_times_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.service_times'))
                                <item>
                                    <a href="{{ \Illuminate\Support\Arr::get($service_times_link, 'url') }}" class="text-sm text-gray-600 hover:text-blue-500">Service Times</a>
                                </item>
                            @endif
                            @if($about_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.about'))
                                <item>
                                    <a href="{{ \Illuminate\Support\Arr::get($about_link, 'url') }}" class="text-sm text-gray-600 hover:text-blue-500">About Us</a>
                                </item>
                            @endif
                            @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('leadership.enabled'))
                                <item>
                                    <a href="{{ route('websites.leadership', [], false) }}" class="text-sm text-gray-600 hover:text-blue-500">Our Leadership</a>
                                </item>
                            @endif
                        </nav>
                    </div>
                    <div class="mt-10 md:mt-0">
                        <h3 class="text-sm font-semibold text-gray-900">Media</h3>
                        <nav role="list" class="flex flex-col mt-4 space-y-1">
                            @if($livestream_link = (new \App\Website\Services\WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.livestream'))
                                <item>
                                    <a href="{{ \Illuminate\Support\Arr::get($livestream_link, 'url') }}" class="text-sm text-gray-600 hover:text-blue-500">Livestream</a>
                                </item>
                            @endif
                            @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('sermons.enabled'))
                                <item>
                                    <a href="{{ route('websites.media', [], false) }}" class="text-sm text-gray-600 hover:text-blue-500">Sermons &amp; Lessons</a>
                                </item>
                            @endif
                            @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('files.enabled'))
                                <item>
                                    <a href="{{ route('websites.files', [], false) }}" class="text-sm text-gray-600 hover:text-blue-500">Files</a>
                                </item>
                            @endif
                            @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('links.facebook'))
                                <item>
                                    <a href="{{ (new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('links.facebook') }}" class="text-sm text-gray-600 hover:text-blue-500">Facebook</a>
                                </item>
                            @endif
                            @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('links.youtube'))
                                <item>
                                    <a href="{{ (new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('links.youtube') }}" class="text-sm text-gray-600 hover:text-blue-500">YouTube</a>
                                </item>
                            @endif
                        </nav>
                    </div>
                </div>
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('calendars.enabled'))
                        <div>
                            <h3 class="text-sm font-semibold text-gray-900">Activities</h3>
                            <nav role="list" class="flex flex-col mt-4 space-y-1">
                                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('calendars.enabled'))
                                    <item>
                                        <a href="{{ route('websites.calendar', [], false) }}" class="text-sm text-gray-600 hover:text-blue-500">Calendar Events</a>
                                    </item>
                                @endif
                            </nav>
                        </div>
                    @endif
                    @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('contact.enabled') && (new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('prayer_requests.enabled'))
                        <div class="mt-10 md:mt-0">
                            <h3 class="text-sm font-semibold text-gray-900">Visitors</h3>
                            <nav role="list" class="flex flex-col mt-4 space-y-1">
                                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('contact.enabled'))
                                    <item>
                                        <a href="{{ route('websites.contact', [], false) }}" class="text-sm text-gray-600 hover:text-blue-500">Contact Us</a>
                                    </item>
                                @endif
                                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount(request()->get('account_id'))->get('prayer_requests.enabled'))
                                    <item>
                                        <a href="{{ route('websites.prayer-request', [], false) }}" class="text-sm text-gray-600 hover:text-blue-500">Request Prayers</a>
                                    </item>
                                @endif
                            </nav>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="flex space-y-6 md:space-y-0 md:flex-row justify-between text-sm mt-6 text-gray-500">
            <div>&copy; {{ date('Y') }} {{ $account->name }}. All rights reserved.</div>
            <div class="text-gray-400">
                Created with <a href="https://lightpost.app?ref=lightpost-website" class="text-orange-400 hover:text-orange-500" target="_blank">Lightpost</a>
            </div>
            <div class="hidden">
                <a href="{{ route('websites.privacy-policy', [], false) }}" class="text-gray-500 hover:text-gray-900">Privacy Policy</a>
                <span class=""> | </span>
                <a href="{{ route('websites.terms-of-service', [], false) }}" class="text-gray-500 hover:text-gray-900">Terms of Service</a>
            </div>
        </div>
    </div>
</footer>

@if(config('app.env') == 'production')
    <script src="https://cdn.usefathom.com/script.js" data-site="GHNHFDFW" defer></script>
@endif

@stack('scripts')

<script>
    function mobileMenu() {
        return {
            isOpen: false,
            open() {
                this.isOpen = true
            },
            close() {
                this.isOpen = false
            },
            init() {
                this.$watch('isOpen', (value) => {
                    if (value) {
                        document.body.classList.add('overflow-hidden')
                    } else {
                        document.body.classList.remove('overflow-hidden')
                    }
                })
            }
        }
    }
</script>

</body>
</html>