@extends('website.template1._layout._app')

@section('title', 'Media')

@section('content')

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.store('AudioPlayer', {
                audioElement: null,
                progressElement: null,
                src: '',
                playing: false,
                currentTime: '00:00',
                duration: '00:00',
                playbackRate: 1,
                percentProgress: 0,
                title: '',
                showPlayer: false,
                pickAndPlay(src, title) {
                    {{-- Set our local data, and the SRC on the <Audio> element --}}
                        this.audioElement.src = src;

                    this.src = src;
                    this.title = title;
                    this.showPlayer = true;

                    {{-- Play the track --}}
                        this.play();

                    {{-- Setup our interval to update our track info as it plays --}}
                    setInterval(function () {
                        {{-- If we're paused, stop doing any work --}}
                        if (Alpine.store('AudioPlayer').audioElement.paused) {
                            return;
                        }
                        {{-- Otherwise, update our time, progress bar and duration (set duration here because setting it at Play Time is too fast to know the duration) --}}
                        Alpine.store('AudioPlayer').currentTime = secondsToHms(Alpine.store('AudioPlayer').audioElement.currentTime);
                        Alpine.store('AudioPlayer').progressElement.style['left'] = (Alpine.store('AudioPlayer').audioElement.currentTime / Alpine.store('AudioPlayer').audioElement.duration * 100) + '%';
                        Alpine.store('AudioPlayer').duration = secondsToHms(Alpine.store('AudioPlayer').audioElement.duration);
                    }, 400);

                },
                play() {
                    {{-- Only play if we're paused --}}
                    if (this.audioElement.paused) {
                        this.audioElement.play();
                        this.playing = true;
                    } else {
                        this.audioElement.pause();
                        this.playing = false;
                    }
                },
                skipForward() {
                    this.audioElement.currentTime += 10;
                },
                skipBackward() {
                    this.audioElement.currentTime -= 10;
                },
                changePlaybackRate() {
                    if (this.playbackRate === 1) {
                        this.playbackRate = 1.25;
                        this.audioElement.playbackRate = 1.25;
                    } else if (this.playbackRate === 1.25) {
                        this.playbackRate = 1.5;
                        this.audioElement.playbackRate = 1.5;
                    } else if (this.playbackRate === 1.5) {
                        this.playbackRate = 1.75;
                        this.audioElement.playbackRate = 1.75;
                    } else if (this.playbackRate === 1.75) {
                        this.playbackRate = 1;
                        this.audioElement.playbackRate = 1;
                    }
                },
                init() {
                    this.audioElement = document.getElementById('podcastAudio');
                    this.progressElement = document.getElementById('progressBar');
                },
            });

        });

        function secondsToHms(seconds) {
            if (isNaN(seconds)) return '00:00';

            seconds = Number(seconds);
            var h = Math.floor(seconds / 3600);
            var m = Math.floor(seconds % 3600 / 60);
            var s = Math.floor(seconds % 3600 % 60);

            var hDisplay = h > 0 ? h.toString().padStart(2, '0') + ':' : '';
            var mDisplay = m.toString().padStart(2, '0') + ':';
            var sDisplay = s.toString().padStart(2, '0');

            return hDisplay + mDisplay + sDisplay;
        }
    </script>

    <div x-data class="md:flex md:items-center md:justify-between">
        <div class="grow">
            <h1>Media</h1>
            <hr class="mb-4"/>
            <form action="" method="get">
                <input name="search" type="text"
                       class="border border-gray-300 rounded-sm p-2 placeholder-gray-400 mb-2 w-64"
                       value="{{ request('search') }}"
                       placeholder="Search by title or speaker..."/>
                <select name="type" class="border border-gray-300 rounded-sm p-2 placeholder-gray-400 mb-2 w-32">
                    <option value="title">Title</option>
                    <option value="speaker">Speaker</option>
                </select>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-sm">Search</button>
                @if(request()->has('search'))
                    <a href="{{ route('websites.media') }}" class="bg-white border border-gray-300 text-black px-4 py-2.5 rounded-sm">Clear Search</a>
                @endif
            </form>
            @if(request()->has('search'))
                <div class="">
                    <span class="text-purple-600">Showing search results for "{{ request('search') }}"</span>
                </div>
            @endif
            <section aria-labelledby="episodes" class="mt-2">
                <div class="border border-gray-200 overflow-hidden sm:rounded-md">
                    <div role="list" class="divide-y divide-gray-200">
                        @foreach($sermons as $sermon)
                            <div class="hover:bg-gray-50 dark:hover:bg-gray-600">
                                <div class="block">
                                    <div class="flex items-start px-2 pt-4 pb-2 sm:px-4">
                                        <div class="min-w-0 flex-1 flex items-start">
                                            <div class="min-w-0 flex-1">
                                                <div class="text-sm text-gray-500 font-medium truncate">
                                                    {{ \Illuminate\Support\Str::upper($sermon->date_sermon?->format('M d, Y')) }}
                                                </div>
                                                <div class="text-xl font-medium dark:text-white truncate">
                                                    <a href="{{ route('websites.media.sermon', $sermon->id, false) }}">
                                                        {{ $sermon->title }}
                                                    </a>
                                                </div>
                                                @if($sermon->speaker)
                                                    <div class="mt-1 flex items-center text-sm text-gray-500">
                                                        {{ $sermon->speaker }}
                                                    </div>
                                                @endif
                                                @if($sermon->summary)
                                                    <div class="mt-1 flex items-center text-sm text-gray-500">
                                                        {{ $sermon->summary }}
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="">
                                            <div class="flex flex-col text-sm gap-y-2" id="tempPlayButton{{ $sermon->id }}">
                                                @if($sermon->getAudioFile())
                                                    <button x-on:click="$store.AudioPlayer.pickAndPlay('{{ $sermon->getAudioFile()->getCdnUrl() }}', '{{ $sermon->title }}')" type="button"
                                                            class="w-fit my-auto ml-auto bg-white border border-gray-500 inline-flex flex-row items-center px-3 py-1 shadow-xs leading-4 font-medium rounded-md text-gray-700 hover:bg-gray-200 hover:cursor-pointer focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                        <x-heroicon-s-play class="w-5 text-gray-700 mr-1.5"/>
                                                        PLAY
                                                    </button>
                                                @endif
                                                @if($sermon->hasYouTube())
                                                    <a href="{{ $sermon->getYouTubeLink() }}" target="_blank" class="text-sm bg-white inline-flex flex-row items-center px-2 py-1 border border-red-600 shadow-xs leading-4 font-medium rounded-md text-red-600 hover:text-white hover:bg-red-500 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                                        <x-heroicon-s-play class="w-5 mr-1.5"/>
                                                        YouTube
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <div class="mt-6">
                    {{ $sermons->onEachSide(0)->links() }}
                </div>

                <!-- Player -->
                <div x-cloak x-show="$store.AudioPlayer.showPlayer" class="fixed inset-x-0 bottom-0 z-10 max-w-(--breakpoint-md) mx-auto">
                    <div class="flex items-center gap-6 bg-black/90 px-4 py-4 shadow-sm shadow-slate-200/80 ring-1 ring-slate-900/5 backdrop-blur-xs md:px-6 md:rounded-t-lg">
                        <div class="hidden md:block">
                            {{-- PAUSE BUTTON --}}
                            <button x-cloak x-show="$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                    class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-700 focus:ring-offset-2 md:h-14 md:w-14"
                                    aria-label="Pause">
                                <div class="absolute -inset-3 md:hidden"></div>
                                <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                    <path d="M8.5 4C7.67157 4 7 4.67157 7 5.5V30.5C7 31.3284 7.67157 32 8.5 32H11.5C12.3284 32 13 31.3284 13 30.5V5.5C13 4.67157 12.3284 4 11.5 4H8.5ZM24.5 4C23.6716 4 23 4.67157 23 5.5V30.5C23 31.3284 23.6716 32 24.5 32H27.5C28.3284 32 29 31.3284 29 30.5V5.5C29 4.67157 28.3284 4 27.5 4H24.5Z"></path>
                                </svg>
                            </button>
                            {{-- PLAY BUTTON --}}
                            <button x-cloak x-show="!$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                    class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-200 focus:ring-offset-2 md:h-14 md:w-14"
                                    aria-label="Play">
                                <div class="absolute -inset-3 md:hidden"></div>
                                <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                    <path d="M33.75 16.701C34.75 17.2783 34.75 18.7217 33.75 19.299L11.25 32.2894C10.25 32.8668 9 32.1451 9 30.9904L9 5.00962C9 3.85491 10.25 3.13323 11.25 3.71058L33.75 16.701Z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="mb-[env(safe-area-inset-bottom)] flex flex-1 flex-col gap-3 overflow-hidden p-1">
                            <a x-text="Alpine.store('AudioPlayer').title" class="truncate text-white text-center text-base font-medium md:text-left" title="" href=""></a>
                            <div class="flex justify-around gap-6">
                                {{--                                    <div class="flex items-center md:hidden">--}}
                                {{--                                        --}}{{-- MUTE BUTTON --}}
                                {{--                                        <button x-on:click="$store.AudioPlayer.mute()" type="button" class="group relative rounded-md hover:bg-slate-100 focus:outline-hidden focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 md:order-none" aria-label="Mute">--}}
                                {{--                                            <div class="absolute -inset-4 md:hidden"></div>--}}
                                {{--                                            <svg aria-hidden="true" viewBox="0 0 24 24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 fill-slate-500 stroke-slate-500 group-hover:fill-slate-700 group-hover:stroke-slate-700">--}}
                                {{--                                                <path d="M12 6L8 10H6C5.44772 10 5 10.4477 5 11V13C5 13.5523 5.44772 14 6 14H8L12 18V6Z"></path>--}}
                                {{--                                                <path d="M17 7C17 7 19 9 19 12C19 15 17 17 17 17" fill="none"></path>--}}
                                {{--                                                <path d="M15.5 10.5C15.5 10.5 16 10.9998 16 11.9999C16 13 15.5 13.5 15.5 13.5" fill="none"></path>--}}
                                {{--                                            </svg>--}}
                                {{--                                        </button>--}}
                                {{--                                    </div>--}}
                                <div class="flex flex-none items-center gap-4">
                                    {{-- REWIND BUTTON --}}
                                    <button x-on:click="$store.AudioPlayer.skipBackward()" type="button" class="group relative rounded-full focus:outline-hidden" aria-label="Rewind 10 seconds">
                                        <div class="absolute -inset-4 -right-2 md:hidden"></div>
                                        <svg aria-hidden="true" viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 stroke-slate-300 group-hover:stroke-slate-500">
                                            <path d="M8 5L5 8M5 8L8 11M5 8H13.5C16.5376 8 19 10.4624 19 13.5C19 15.4826 18.148 17.2202 17 18.188"></path>
                                            <path d="M5 15V19"></path>
                                            <path d="M8 18V16C8 15.4477 8.44772 15 9 15H10C10.5523 15 11 15.4477 11 16V18C11 18.5523 10.5523 19 10 19H9C8.44772 19 8 18.5523 8 18Z"></path>
                                        </svg>
                                    </button>
                                    <div class="md:hidden">
                                        {{-- PAUSE BUTTON --}}
                                        <button x-cloak x-show="$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                                class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-200 focus:ring-offset-2 md:h-14 md:w-14"
                                                aria-label="Pause">
                                            <div class="absolute -inset-3 md:hidden"></div>
                                            <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                                <path d="M8.5 4C7.67157 4 7 4.67157 7 5.5V30.5C7 31.3284 7.67157 32 8.5 32H11.5C12.3284 32 13 31.3284 13 30.5V5.5C13 4.67157 12.3284 4 11.5 4H8.5ZM24.5 4C23.6716 4 23 4.67157 23 5.5V30.5C23 31.3284 23.6716 32 24.5 32H27.5C28.3284 32 29 31.3284 29 30.5V5.5C29 4.67157 28.3284 4 27.5 4H24.5Z"></path>
                                            </svg>
                                        </button>
                                        {{-- PLAY BUTTON --}}
                                        <button x-cloak x-show="!$store.AudioPlayer.playing" x-on:click="$store.AudioPlayer.play()" type="button"
                                                class="group relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-slate-500 hover:bg-slate-600 focus:outline-hidden focus:ring-2 focus:ring-slate-200 focus:ring-offset-2 md:h-14 md:w-14"
                                                aria-label="Play">
                                            <div class="absolute -inset-3 md:hidden"></div>
                                            <svg viewBox="0 0 36 36" aria-hidden="true" class="h-5 w-5 fill-white group-active:fill-white/80 md:h-7 md:w-7">
                                                <path d="M33.75 16.701C34.75 17.2783 34.75 18.7217 33.75 19.299L11.25 32.2894C10.25 32.8668 9 32.1451 9 30.9904L9 5.00962C9 3.85491 10.25 3.13323 11.25 3.71058L33.75 16.701Z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    {{-- FORWARD BUTTON --}}
                                    <button x-on:click="$store.AudioPlayer.skipForward()" type="button" class="group relative rounded-full focus:outline-hidden" aria-label="Fast-forward 10 seconds">
                                        <div class="absolute -inset-4 -left-2 md:hidden"></div>
                                        <svg aria-hidden="true" viewBox="0 0 24 24" fill="none" class="h-6 w-6 stroke-slate-300 group-hover:stroke-slate-500">
                                            <path d="M16 5L19 8M19 8L16 11M19 8H10.5C7.46243 8 5 10.4624 5 13.5C5 15.4826 5.85204 17.2202 7 18.188" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M13 15V19" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                            <path d="M16 18V16C16 15.4477 16.4477 15 17 15H18C18.5523 15 19 15.4477 19 16V18C19 18.5523 18.5523 19 18 19H17C16.4477 19 16 18.5523 16 18Z" stroke-width="1.5" stroke-linecap="round"
                                                  stroke-linejoin="round"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div role="group" id="react-aria7471806458-:r0:" aria-labelledby="react-aria7471806458-:r1:" class="absolute inset-x-0 bottom-full flex flex-auto touch-none items-center gap-6 md:relative">
                                    <label class="sr-only" id="react-aria7471806458-:r1:">Current time</label>
                                    <div style="position: relative; touch-action: none;" class="relative w-full bg-slate-400 md:rounded-full">
                                        {{-- TODO LATER -- this controls the background of the progress bar --}}
                                        <div class="h-2 md:rounded-l-xl md:rounded-r-md bg-slate-700" style="width: calc(0% - 0.25rem);"></div>
                                        <div id="progressBar" class="absolute top-1/2 -translate-x-1/2" style="left: 13.125%;">
                                            <div style="position: absolute; transform: translate(-50%, -50%); touch-action: none; left: 3.125%;" class="h-4 rounded-full w-1 bg-white">
                                                <div style="border: 0px; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(50%); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; white-space: nowrap;">
                                                    <input tabindex="0" id="AudioPlayerSlider" aria-labelledby="Audio Player Slider" min="0" max="64" step="1" aria-orientation="horizontal" aria-valuetext="0 hours, 0 minutes, 0 seconds"
                                                           type="range" value="0">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="hidden items-center gap-2 md:flex">
                                        <output x-text="Alpine.store('AudioPlayer').currentTime" for="AudioPlayerSlider" aria-live="off" class="hidden rounded-md px-1 py-0.5 font-mono text-sm leading-6 md:block text-slate-100">00:02</output>
                                        <span class="text-sm leading-6 text-slate-300" aria-hidden="true">/</span>
                                        <span x-text="Alpine.store('AudioPlayer').duration" class="hidden rounded-md px-1 py-0.5 font-mono text-sm leading-6 text-slate-100 md:block">01:04</span>
                                    </div>
                                </div>

                                {{--                                    <div class="flex items-center gap-4">--}}
                                {{--                                        <div class="flex items-center">--}}
                                {{--                                            --}}{{-- PLAYBACK RATE BUTTON --}}
                                {{--                                            <button x-on:click="$store.AudioPlayer.changePlaybackRate()" type="button" class="relative flex h-6 w-6 items-center justify-center rounded-md text-slate-500 hover:bg-slate-100 hover:text-slate-700 focus:outline-hidden focus:ring-2 focus:ring-slate-400 focus:ring-offset-2" aria-label="Playback rate">--}}
                                {{--                                                <div class="absolute -inset-4 md:hidden"></div>--}}
                                {{--                                                <svg aria-hidden="true" viewBox="0 0 16 16" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">--}}
                                {{--                                                    <path d="M13 1H3C1.89543 1 1 1.89543 1 3V13C1 14.1046 1.89543 15 3 15H13C14.1046 15 15 14.1046 15 13V3C15 1.89543 14.1046 1 13 1Z" fill="currentColor" stroke="currentColor" stroke-width="2"></path>--}}
                                {{--                                                    <path d="M3.75 7.25L5.25 5.77539V11.25"></path>--}}
                                {{--                                                    <path d="M8.75 7.75L11.25 10.25"></path>--}}
                                {{--                                                    <path d="M11.25 7.75L8.75 10.25"></path>--}}
                                {{--                                                </svg>--}}
                                {{--                                            </button>--}}
                                {{--                                        </div>--}}

                                {{--                                        <div class="hidden items-center md:flex">--}}
                                {{--                                            --}}{{-- MUTE BUTTON --}}
                                {{--                                            <button type="button" class="group relative rounded-md hover:bg-slate-100 focus:outline-hidden focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 md:order-none" aria-label="Mute">--}}
                                {{--                                                <div class="absolute -inset-4 md:hidden"></div>--}}
                                {{--                                                <svg aria-hidden="true" viewBox="0 0 24 24" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 fill-slate-500 stroke-slate-500 group-hover:fill-slate-700 group-hover:stroke-slate-700">--}}
                                {{--                                                    <path d="M12 6L8 10H6C5.44772 10 5 10.4477 5 11V13C5 13.5523 5.44772 14 6 14H8L12 18V6Z"></path>--}}
                                {{--                                                    <path d="M17 7C17 7 19 9 19 12C19 15 17 17 17 17" fill="none"></path>--}}
                                {{--                                                    <path d="M15.5 10.5C15.5 10.5 16 10.9998 16 11.9999C16 13 15.5 13.5 15.5 13.5" fill="none"></path>--}}
                                {{--                                                </svg>--}}
                                {{--                                            </button>--}}
                                {{--                                        </div>--}}
                                {{--                                    </div>--}}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <audio src="" id="podcastAudio" preload="none"></audio>

@endsection
