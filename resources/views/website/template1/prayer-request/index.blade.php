@extends('website.template1._layout._app')

@section('title', 'Prayer Request')

@section('content')
    <div class="">
        @if(request()->query('r') === 's')
            <div class="rounded-md bg-green-50 px-4 py-2 mb-6">
                <div class="flex items-center">
                    <div class="shrink-0">
                        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium text-green-800">
                            Your prayer request has been received. We will be praying and reach out with any updates.
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <style>
            .blue-acorn {
                display: none;
            }
        </style>

        <div class="overflow-hidden">
            <div class="px-4 pb-5 sm:px-6">
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Prayer Request
                </h1>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Share your prayer request with us. We would be honored to pray for you.
                </p>
            </div>

            <div class="border-t border-gray-200">
                <form action="{{ route('websites.prayer-request.submit', [], false) }}" method="POST" class="px-4 py-5 sm:p-6">
                    @csrf

                    {{-- Hidden field for spam detection --}}
                    <input type="hidden" name="address" value="">

                    <div class="blue-acorn">
                        <label for="first_name" class="block text-sm font-medium text-gray-700">First Name</label>
                        <div class="mt-1">
                            <input type="text" id="first_name" name="first_name"/>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <div class="max-w-lg">
                            <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                            <div class="mt-1">
                                <input type="text" name="name" id="name" required
                                       class="shadow-xs border p-2 border-gray-400 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm rounded-md">
                            </div>
                        </div>

                        <div class="max-w-lg">
                            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                            <div class="mt-1">
                                <input type="email" name="email" id="email" required
                                       class="shadow-xs border p-2 border-gray-400 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm rounded-md">
                            </div>
                        </div>

                        <div class="max-w-lg">
                            <label for="phone" class="flex items-center text-sm">
                                <span class="font-medium text-gray-700">Phone Number</span>
                                <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-500">Optional</span>
                            </label>
                            <div class="mt-1">
                                <input type="tel" name="phone" id="phone"
                                       class="shadow-xs border p-2 border-gray-400 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm rounded-md">
                            </div>
                        </div>

                        <div>
                            <label for="comment" class="block text-sm font-medium text-gray-700">Prayer Request</label>
                            <div class="mt-1">
                                <textarea id="comment" name="comment" rows="4" required
                                          class="shadow-xs border p-2 border-gray-400 focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm rounded-md"
                                          placeholder="Please share your prayer request here. Your request will be kept confidential unless requested otherwise."></textarea>
                            </div>
                        </div>

                        <div class="flex flex-col justify-start">
                            <button type="submit"
                                    class="inline-flex mr-auto justify-center py-2 px-4 border border-transparent shadow-xs text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Submit Prayer Request
                            </button>
                            <div class="cf-turnstile mt-6"
                                 data-sitekey="{{ config('services.cloudflare.site_key') }}"
                            ></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection