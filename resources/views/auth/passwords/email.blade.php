@extends('app._layout.no-header')

@section('content')

    <style>
        html,
        body {
            height: 100%;
        }

        body {
            display: -ms-flexbox;
            display: flex;
            -ms-flex-align: center;
            align-items: center;
            padding-top: 40px;
            padding-bottom: 40px;
            background-color: #f5f5f5;
        }

        .form-signin {
            width: 100%;
            max-width: 330px;
            padding: 15px;
            margin: 15% auto auto auto;
        }

        .form-signin .checkbox {
            font-weight: 400;
        }

        .form-signin .form-control {
            position: relative;
            box-sizing: border-box;
            height: auto;
            padding: 10px;
            font-size: 16px;
        }

        .form-signin .form-control:focus {
            z-index: 2;
        }

        .form-signin input[type="email"] {
            margin-bottom: -1px;
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }

        .form-signin input[type="password"] {
            margin-bottom: 10px;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }

        .bd-placeholder-img {
            font-size: 1.125rem;
            text-anchor: middle;
        }

        @media (min-width: 768px) {
            .bd-placeholder-img-lg {
                font-size: 3.5rem;
            }
        }
    </style>

    <script>
        $(document).ready(function () {
            $('#login-form').submit(function (e) {
                e.preventDefault();
                $('#general_error').addClass('d-none');

                axios({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    method: 'post',
                    url: '{{ route('password.email') }}',
                    data: $('#login-form').serialize(),
                }).then(function (response) {
                    $('#general_success').removeClass('d-none');
                    if (Turbolinks.supported) {
                        // Turbolinks.clearCache();
                        // window.location.replace('/');
                        // Turbolinks.visit('/', {action: "replace"});
                    } else if (!Turbolinks.supported) {
                        // window.location = '/';
                    }
                }).catch(function (error) {
                    console.log('Failed.');
                    $('#general_error').removeClass('d-none');
                    // if (error.response.status == 422) {
                    //     showStaticError(error.response.data.message);
                    // } else {
                    //     showStaticError(error.response.data);
                    // }
                });
            });
        });
    </script>

    <form action="{{ route('password.email') }}" method="post" class="form-signin text-center" id="login-form">
        {{ csrf_field() }}

        <div class="text-center">
            <img src="{{ asset('img/lightpost-logo.svg') }}" alt="Lightpost" height="65em" class="mb-4">
        </div>

        @if(session('message.success'))
            <div class="alert alert-dismissible alert-success" role="alert" onclick="$(this).hide();">
                {{ session('message.success') }}
            </div>
        @endif

        @if(session('message.failure'))
            <div class="alert alert-dismissible alert-danger" role="alert" onclick="$(this).hide();">
                {{ session('message.failure') }}
            </div>
        @endif

        @if(isset($errors) && $errors->count() > 0)
            <div class="alert alert-dismissible alert-danger" role="alert" onclick="$(this).hide();">
                @foreach ($errors->all() as $error)
                    {{ $error }}<br>
                @endforeach
            </div>
        @endif

        <p>Enter your email for a reset link to be sent.</p>

        <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }}">

            <div class="alert alert-dismissible alert-danger d-none text-center" id="general_error" role="alert" onclick="$(this).hide();">
                Invalid input.
            </div>
            <div class="alert alert-dismissible alert-success d-none text-center" id="general_success" role="alert" onclick="$(this).hide();">
                Password reset email sent.
            </div>

            <label for="input_username" class="sr-only">Email</label>
            <input type="email" name="email" id="input_username" class="form-control" placeholder="Email" required autofocus autocomplete="off">

            <br>

            <button class="btn btn-primary btn-lg btn-block" type="submit">Send Password Reset Link</button>

        </div>

        <p class="mt-4 mb-3 text-muted d-none">
            <small>&copy; {{ date('Y') }} {{ config('app.copyright_name') }}</small>
        </p>
    </form>

@endsection
