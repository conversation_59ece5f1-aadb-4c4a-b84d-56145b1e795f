<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <link rel="apple-touch-icon-precomposed" sizes="57x57" href="/favicon/apple-touch-icon-57x57.png"/>
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="/favicon/apple-touch-icon-114x114.png"/>
    <link rel="apple-touch-icon-precomposed" sizes="72x72" href="/favicon/apple-touch-icon-72x72.png"/>
    <link rel="apple-touch-icon-precomposed" sizes="144x144" href="/favicon/apple-touch-icon-144x144.png"/>
    <link rel="apple-touch-icon-precomposed" sizes="60x60" href="/favicon/apple-touch-icon-60x60.png"/>
    <link rel="apple-touch-icon-precomposed" sizes="120x120" href="/favicon/apple-touch-icon-120x120.png"/>
    <link rel="apple-touch-icon-precomposed" sizes="76x76" href="/favicon/apple-touch-icon-76x76.png"/>
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="/favicon/apple-touch-icon-152x152.png"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-196x196.png" sizes="196x196"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-96x96.png" sizes="96x96"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-32x32.png" sizes="32x32"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-16x16.png" sizes="16x16"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-128.png" sizes="128x128"/>

    <meta name="application-name" content="Lightpost"/>
    <meta name="msapplication-TileColor" content="#FFFFFF"/>
    <meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png"/>
    <meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png"/>
    <meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png"/>
    <meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png"/>
    <meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png"/>
    <link rel="manifest" href="/favicon/manifest.json">

    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <meta name="theme-color" content="#ffffff">

    <title>@yield('title', isset($title) ? $title : config('app.name', 'Lightpost'))</title>

    <!-- Styles -->
    <link href="{{ asset('static/app/css/tailwind.app.css') }}" rel="stylesheet">
    <link href="{{ asset('webfonts/css/all.min.css') }}" rel="stylesheet">

    @if(App::environment('production'))
        <script src="https://browser.sentry-cdn.com/4.6.4/bundle.min.js" crossorigin="anonymous"></script>
        <script>
            Sentry.init({dsn: 'https://<EMAIL>/1408589'});
        </script>
    @endif

    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <script src="{{ asset('static/app/js/app.js') }}" defer></script>
</head>

<body>

<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <img class="mx-auto h-16 w-auto" src="{{ asset('img/lightpost-logo.svg') }}" alt="Lightpost"/>
        @if($account)
            <h2 class="mt-4 text-center text-2xl font-semibold text-gray-900">
                {{ $account->name }}
            </h2>
        @endif
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md" x-data="{ showForgotPasswordForm: false, email_field: '' }">
        <div class="bg-white py-8 px-4 border border-gray-300 sm:rounded-lg sm:px-10">

            @if(session('message.success'))
                <div class="px-3 py-1 mb-4 text-green-700 bg-green-50 border border-green-500 rounded-sm" role="alert" onclick="$(this).hide();">
                    {{ session('message.success') }}
                </div>
            @endif

            @if(session('message.failure'))
                <div class="px-3 py-1 mb-4 text-red-700 bg-red-50 border border-red-500 rounded-sm" role="alert" onclick="$(this).hide();">
                    {{ session('message.failure') }}
                </div>
            @endif

            @if(isset($errors) && $errors->count() > 0)
                <div class="px-3 py-1 mb-4 text-red-700 bg-red-50 border border-red-500 rounded-sm" role="alert" onclick="$(this).hide();">
                    @foreach ($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                </div>
            @endif

            <form x-show="!showForgotPasswordForm" action="{{ route('login.process') }}" class="space-y-6" method="post">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">
                        Email
                    </label>
                    <div class="mt-1">
                        <input x-model="email_field" autofocus id="email" name="user_name" type="text" autocomplete="email" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        Password
                    </label>
                    <div class="mt-1">
                        <input id="password" name="password" type="password" autocomplete="current-password" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember_me" name="remember_me" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-sm">
                        <label for="remember_me" class="ml-2 block text-sm text-gray-900">
                            Remember me
                        </label>
                    </div>

                    <div class="text-sm">
                        <a @click="showForgotPasswordForm = !showForgotPasswordForm" class="font-medium text-blue-600 hover:text-blue-500 cursor-pointer">
                            Forgot your password?
                        </a>
                    </div>
                </div>

                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-xs font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Sign in
                    </button>
                </div>
                {{ csrf_field() }}
            </form>

            <form x-show="showForgotPasswordForm" action="{{ route('password.email') }}" class="space-y-6" method="post" style="display:none;" id="password-reset-form">
                <div>
                    <label for="email_for_password_reset" class="block text-sm font-medium text-gray-700">
                        Email
                    </label>
                    <div class="mt-1">
                        <input x-model="email_field" id="email_for_password_reset" name="email" type="text" autocomplete="email" required class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-xs placeholder-gray-400 focus:outline-hidden focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>

                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-xs font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Send Password Reset Link
                    </button>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-sm">
                        <a @click="showForgotPasswordForm = !showForgotPasswordForm" class="font-medium text-blue-600 hover:text-blue-500 cursor-pointer">
                            &leftarrow; Go Back to Login
                        </a>
                    </div>
                </div>
                {{ csrf_field() }}
            </form>

        </div>

        <p class="mt-4 mb-3 text-gray-300 text-center">
            <small>&copy; {{ date('Y') }} {{ config('app.copyright_name') }}</small>
        </p>
    </div>
</div>


</body>
</html>
