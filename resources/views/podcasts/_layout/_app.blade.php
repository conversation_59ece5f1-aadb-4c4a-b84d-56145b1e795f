<!doctype html>
<html lang="{{ app()->getLocale() }}">
<head>
    <title>@yield('title', isset($title) ? $title : 'Podcast - Powered by Lightpost')</title>

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <link rel="apple-touch-icon" href="/favicon/favicon-180x180.png"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-196x196.png" sizes="196x196"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-96x96.png" sizes="96x96"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-32x32.png" sizes="32x32"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-16x16.png" sizes="16x16"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-128.png" sizes="128x128"/>
    <meta name="application-name" content="Lightpost"/>
    <meta name="msapplication-TileColor" content="#FFFFFF"/>
    <meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png"/>
    <meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png"/>
    <meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png"/>
    <meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png"/>
    <meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png"/>
    <link rel="manifest" href="/favicon/manifest.json">

    <meta name="theme-color" content="#ffffff">

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="Description" content=""/>

    <link rel="canonical" href="@yield('link', isset($link) ? $link : null)">

    <link href="{{ mix('/static/podcasts/css/tailwind.podcasts.css') }}" rel="stylesheet">
</head>

<body>

<div class="flex min-h-full bg-gray-50">
    <div class="pb-8 max-w-(--breakpoint-2xl) min-h-screen mx-auto">
        @yield('content')
    </div>
    {{--  This is some TailwindCSS that we need for our Tailwind Pagination that is not yet rendered, and PostCSS doesn't pick up  --}}
    <div class=" hidden sm:hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"></div>
</div>

<script src="{{ mix('/static/podcasts/js/app.js') }}" defer></script>

@if(config('app.env') == 'production')
    <script src="https://cdn.usefathom.com/script.js" data-site="WLWDAFGW" defer></script>
@endif

@stack('scripts')

</body>
</html>
