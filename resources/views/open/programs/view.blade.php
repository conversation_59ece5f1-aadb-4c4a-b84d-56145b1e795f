@extends('open._layout._app.program')

@section('title', $program->name)

@section('content')

    <div class="min-h-screen bg-radial-[at_30%_30%] from-white via-blue-50 to-gray-50 to-90% px-6 lg:px-8">
        @if($program->cover_image_url)
            <div class="max-w-4xl mx-auto">
                <img src="{{ $program->cover_image_url }}"
                     alt="{{ $program->account->name }}"
                     class="w-full sm:rounded-b-lg shadow-lg"
                />
            </div>
        @endif
        <div class="max-w-4xl mx-auto mt-6 pb-10">
            @if(!$program->cover_image_url)
                @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount($program->account)->get('logo.main.light.website_file_id'))
                    <img src="{{ (new \App\Website\Services\GetWebsiteSettingValue())->forAccount($program->account)->get('logo.main.light.website_file_id')?->getUrl() }}"
                         alt="{{ $program->account->name}}"
                         class="mx-auto max-h-22 text-3xl font-medium tracking-tight text-gray-900 text-center"
                    />
                @else
                    <h2 class="text-2xl font-medium tracking-tight text-gray-900 text-center">
                        {{ $program->account->name }}
                    </h2>
                @endif
            @endif
            <div class="flex flex-row justify-between">
                <div>
                    <div class="flex font-medium text-gray-500">
                        {{ $program->start_at->format('l, F j, Y') }} &dash; {{ $program->end_at->format('l, F j, Y') }}
                    </div>
                    @if($program->start_at_time)
                        <div class="text-base font-medium text-gray-500">
                            <span>
                                {{ \Carbon\Carbon::createFromFormat('H:i:s', $program->start_at_time)->format('g:ia') }}
                            </span>
                            <span> &dash; </span>
                            <span>
                                {{ \Carbon\Carbon::createFromFormat('H:i:s', $program->end_at_time)->format('g:ia') }}
                            </span>
                        </div>
                    @endif
                    <div>
                        <h2 class="mt-2 text-4xl font-semibold tracking-tight text-gray-900 text-left">{{ $program->name }}</h2>
                        @if($program->location)
                            <div class="mt-2">
                                <a href="http://maps.google.com/?daddr={{ urlencode($program->location) }}" class="text-gray-500">
                                    {{ $program->location }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="flex flex-row items-center gap-2 mb-auto">
                    <div class="flex-initial block rounded-xl shadow-xl overflow-hidden bg-white text-center w-20">
                        <div class="text-white py-1 text-xs font-semibold bg-blue-500">
                            {{ $program->start_at->format('M') }}
                        </div>
                        <div class="py-1 border-b border-transparent rounded-b-xl">
                            <span class="text-4xl font-medium">
                                {{ $program->start_at->format('j') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-6 gap-y-4 sm:gap-4 mt-4">
                <div class="col-span-4 order:last sm:order-first p-4 bg-white border border-gray-200 overflow-hidden rounded-md">
                    <h2 class="mb-2 text-xl font-semibold tracking-tight text-gray-900 text-left">Details</h2>
                    <p class="text-base text-gray-800">{!! nl2br((new VStelmakh\UrlHighlight\UrlHighlight())->highlightUrls($program->description ?: '')) !!}</p>
                </div>
                <div class="mb-auto col-span-2 order-first sm:order-last p-4 space-y-4 bg-white border border-gray-200 overflow-hidden rounded-md">
                    @foreach($program->forms as $index => $form)
                        <div>
                            @if($program->forms()->count() > 1)
                                <h2 class="mb-1 text-lg font-medium tracking-tight text-gray-900 text-left">{{ $form->name }}</h2>
                            @endif
                            <a href="{{ route('open.programs.forms.view', [$program->getSquid(), $form->getSquid()]) }}"
                               class="flex flex-row justify-center py-2 rounded-md text-center text-white font-semibold text-base bg-blue-600 hover:bg-blue-500">
                                <x-heroicon-s-clipboard-document-list class="mr-1.5 w-4 h-4 my-auto"/>
                                Register
                                <x-heroicon-s-arrow-right class="ml-2 w-4 h-4 my-auto"/>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>

            @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount($program->account)->get('logo.main.light.website_file_id'))
                <img src="{{ (new \App\Website\Services\GetWebsiteSettingValue())->forAccount($program->account)->get('logo.main.light.website_file_id')?->getUrl() }}"
                     alt="{{ $program->account->name}}"
                     class="mt-8 mx-auto max-h-14 text-base font-medium tracking-tight text-gray-900 text-center"
                />
            @else
                <h2 class="mt-8 text-base font-medium tracking-tight text-gray-900 text-center">
                    {{ $program->account->name }}
                </h2>
            @endif

        </div>
    </div>

@endsection
