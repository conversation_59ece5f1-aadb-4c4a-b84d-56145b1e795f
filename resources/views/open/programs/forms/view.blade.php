@extends('open._layout._app.form')

@section('title', $form->name . ' - ' . $program->name)

@section('content')

    <div class="min-h-screen bg-radial-[at_30%_30%] from-white via-blue-50 to-gray-50 to-90% px-6 pt-6 lg:px-8">
        <div class="max-w-4xl mx-auto pb-12">
            @if((new \App\Website\Services\GetWebsiteSettingValue())->forAccount($program->account)->get('logo.main.light.website_file_id'))
                <img src="{{ (new \App\Website\Services\GetWebsiteSettingValue())->forAccount($program->account)->get('logo.main.light.website_file_id')?->getUrl() }}"
                     alt="{{ $program->account->name}}"
                     class="mx-auto max-h-22 text-3xl font-medium tracking-tight text-gray-900 text-center"
                />
            @else
                <h2 class="text-2xl font-medium tracking-tight text-gray-900 text-center">
                    {{ $program->account->name }}
                </h2>
            @endif

            <div class="mt-6 flex flex-row justify-between">
                <div>
                    <div class="flex font-medium text-gray-500">
                        {{ $program->start_at->format('l, F j, Y') }} &dash; {{ $program->end_at->format('l, F j, Y') }}
                    </div>
                    <div>
                        <h2 class="mt-2 text-4xl font-semibold tracking-tight text-gray-900 text-left">{{ $program->name }}</h2>
                        @if($program->location)
                            <div class="mt-2">
                                <a href="http://maps.google.com/?daddr={{ urlencode($program->location) }}" class="text-gray-500">
                                    {{ $program->location }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="flex flex-row items-center gap-2 mb-auto">
                    <div class="flex-initial block rounded-xl shadow-xl overflow-hidden bg-white text-center w-20">
                        <div class="text-white py-1 text-xs font-semibold bg-blue-500">
                            {{ $program->start_at->format('M') }}
                        </div>
                        <div class="py-1 border-b border-transparent rounded-b-xl">
                            <span class="text-4xl font-medium">
                                {{ $program->start_at->format('j') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <livewire:open.programs.forms.view-form :form="$form" :program="$program"/>

        </div>
    </div>

@endsection
