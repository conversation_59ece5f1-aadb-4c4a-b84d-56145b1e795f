<div x-data>
    <div class="relative">
        <input type="hidden" name="selected_user_id" value="{{ $selected_user_id }}">
        <input type="text" wire:model.live="search_name"
               x-on:focus="$wire.showDropdown()"
               x-on:keydown.escape="$wire.hideDropdown()"
               x-on:click.away="$wire.hideDropdown()"
               wire:click.self="showDropdown"
               wire:keydown.up="decrementHighlight"
               wire:keydown.down="incrementHighlight"
               wire:keydown.enter.prevent="selectUser"
               placeholder="{{ $empty_option_text }}"
               class="hover:cursor-pointer w-full rounded border-1 border-gray-300 bg-white py-1 pl-3 pr-12 text-black ring-0 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 text-sm md:text-base"
               role="combobox" aria-controls="options" aria-expanded="false">
        @if($selected_user_id)
            <span wire:click="clearSelectedUser"
                  class="absolute cursor-pointer z-0 inset-y-0 right-6 flex text-gray-400 items-center rounded-r-md px-1 focus:outline-hidden hover:text-red-500">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"/>
                </svg>
            </span>
        @endif
        <span class="pointer-events-none absolute z-0 inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-hidden">
            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z" clip-rule="evenodd"/>
            </svg>
        </span>

        @if($show_dropdown)
            <ul role="listbox"
                class="absolute z-10 mt-1 max-h-56 w-full overflow-auto border border-gray-300 rounded-md bg-white py-1 shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-hidden text-sm md:text-base"
            >
                @forelse($this->users() as $index => $user)
                    <li wire:key="{{ $user->id }}"
                        wire:click="selectUser({{ $user->id }}, {{ $index }})"
                        class="relative cursor-pointer select-none py-2 pl-3 pr-9 text-gray-900 hover:bg-blue-600 hover:text-white {{ $highlight_index === $index ? 'bg-blue-600 text-white' : '' }}"
                        role="option" tabindex="0">
                        <div class="flex items-center">
                            {{--                            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="" class="h-6 w-6 shrink-0 rounded-full">--}}
                            <span class="truncate">{{ $user->name }}</span>
                        </div>
                        @if($user->id === $selected_user_id)
                            <span class="absolute inset-y-0 right-0 flex items-center pr-4">
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"/>
                                </svg>
                            </span>
                        @endif
                    </li>
                @empty
                    <li class="relative select-none py-2 pl-3 pr-9 text-gray-500"
                        role="option" tabindex="0">
                        <div class="flex items-center">
                            <span class="truncate">No users match your search.</span>
                        </div>
                    </li>
                @endforelse

                @if($this->users()->count() > 49)
                    <li class="relative select-none py-2 pl-3 pr-9 text-gray-400 hover:text-white hover:bg-gray-600"
                        role="option" tabindex="0">
                        <div class="flex text-left">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-8 h-8">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3"/>
                            </svg>
                            <span class="ml-2"> More users available... start typing to narrow your list.</span>
                        </div>
                    </li>
                @endif
            </ul>
        @endif
    </div>
</div>
