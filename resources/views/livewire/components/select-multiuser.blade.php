<div x-data
     class="mb-2">
    <div class="relative {{ $select_classes }}"
         x-on:click.away="$wire.hideDropdown()">
        <input type="text" wire:model.live="search_name"
               {{--               x-on:focus="$wire.showDropdown()"--}}
               x-on:keydown.escape="$wire.hideDropdown()"
               wire:click.self="toggleDropdown"
               wire:keydown.up="decrementHighlight"
               wire:keydown.down="incrementHighlight"
               wire:keydown.enter.prevent="selectUser"
               placeholder="{{ $empty_option_text }}"
               class="hover:cursor-pointer w-full rounded border-1 border-gray-300 bg-white py-1.5 pl-3 pr-12 text-black ring-0 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-blue-600 text-sm md:text-base"
               role="combobox" aria-controls="options" aria-expanded="false">
        <span class="pointer-events-none absolute z-0 inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-hidden">
            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z" clip-rule="evenodd"/>
            </svg>
        </span>

        @if($show_dropdown)
            <ul role="listbox"
                class="absolute z-10 mt-1 max-h-56 w-full overflow-auto border border-gray-300 rounded-md bg-white py-1 shadow-xl focus:outline-hidden text-sm"
            >
                @forelse($this->users() as $index => $user)
                    <li wire:key="{{ $user->id }}"
                        wire:click="selectUser({{ $user->id }}, {{ $index }})"
                        class="flex flex-row cursor-pointer select-none py-1.5 pl-3 pr-9 text-gray-900 hover:bg-blue-600 hover:text-white {{ in_array($user->id, $selected_user_ids) ? 'bg-green-200' : '' }}"
                        role="option" tabindex="0">
                        @if(in_array($user->id, $selected_user_ids))
                            <span class="flex items-center pr-1">
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"/>
                                </svg>
                            </span>
                        @endif
                        <div class="flex items-center">
                            <span class="truncate">{{ $user->name }}</span>
                        </div>
                    </li>
                @empty
                    <li class="relative select-none py-2 pl-3 pr-9 text-gray-500"
                        role="option" tabindex="0">
                        <div class="flex items-center">
                            <span class="truncate">No users match your search.</span>
                        </div>
                    </li>
                @endforelse

                @if($this->users()->count() > 29)
                    <li class="relative select-none py-2 pl-3 pr-9 text-gray-400 hover:text-white hover:bg-gray-600"
                        role="option" tabindex="0">
                        <div class="flex text-left">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3"/>
                            </svg>
                            <span class="ml-2 my-auto">More users available... start typing to narrow your list.</span>
                        </div>
                    </li>
                @endif
            </ul>
        @endif
    </div>

    <div class="text-black text-base {{ $selected_classes }}">
        @foreach($this->getSelectedUsers() as $user)
            <div class="flex flex-row justify-between border border-blue-500 rounded-md bg-white hover:bg-blue-50">
                <input type="hidden" name="{{ $input_array_name }}[]" value="{{ $user->id }}">
                <div class="pl-2 py-0.5 truncate">{{ $user->name }}</div>
                <div class="cursor-pointer px-1 border-l border-blue-500 hover:bg-blue-100 rounded-r align-middle"
                     wire:click="removeUser({{ $user->id }})">
                    <x-heroicon-m-x-mark class="h-5 w-5 my-1"/>
                </div>
            </div>
        @endforeach
    </div>
</div>
