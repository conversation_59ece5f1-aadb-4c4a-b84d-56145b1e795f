<div x-data>
    <h4 class="font-medium text-base mb-1">
        {{ $setting?->name }}
    </h4>

    @if(!auth()->user()->hasPermission('account.manage'))
        Oops!  You need have the "Manage Account" permission in a role to manage account settings.
    @else

        <div class="ml-4 space-y-2">
            @if($setting?->type == 'bool')
                @if($setting->allow_account_to_enable && !$only_show_members_option)
                    <div>
                        <label>
                            <input type="checkbox" value="1" wire:model="setting_set_value" wire:change="saveSettingValue"/> Enable for your Account
                        </label>
                    </div>
                @endif
                @if($setting->allow_account_to_select_permissions)
                    @if(!$only_show_members_option)
                        <div>
                            <label>
                                <input type="checkbox" wire:model="setting_value.enable_for_admin" wire:change="saveSettingOption"/> Enable for Admins
                            </label>
                        </div>
                    @endif
                    <div>
                        <label>
                            <input type="checkbox" wire:model="setting_value.enable_for_member" wire:change="saveSettingOption"/> Enable for Members
                        </label>
                    </div>
                @endif
            @elseif($setting?->type == 'string')
                string
            @elseif($setting?->type == 'integer')
                integer
            @endif
        </div>

    @endif
</div>
