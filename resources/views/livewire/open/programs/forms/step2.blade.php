<div>
    <div class="mx-auto mt-6">

        <!-- Step Indicator -->
        <div class="flex justify-between mb-2">
            <div class="text-base font-semibold text-gray-900">
                Parent/Guardian Contacts
            </div>
            <div class="text-base text-gray-600">
                Step {{ $step }} of 3
            </div>
        </div>

        <!-- Step 2: Add Contacts -->
        @if($step === 2)
            <div class="grid grid-cols-1 gap-y-4">
                @forelse($contacts as $uniqueId => $contact)
                    <div class="border border-gray-300 rounded-md bg-white">
                        <div class="flex justify-between items-center bg-gray-100 rounded-t-md px-4 py-2">
                            <h5 class="font-medium text-gray-900">
                                Contact # {{ $loop->index + 1 }}
                                @if(!empty($contact['user_fields']['first_name']) || !empty($contact['user_fields']['last_name']))
                                    <span class="font-normal">
                                &dash; {{ $contact['user_fields']['first_name'] ?? '' }}
                                        {{ $contact['user_fields']['last_name'] ?? '' }}
                               </span>
                                @endif
                            </h5>
                            @if(count($contacts) > 0)
                                {{-- Show remove button only if there's at least one contact to remove, ideally you'd require at least one contact though --}}
                                <button wire:click="removeContact('{{ $uniqueId }}')" class="cursor-pointer text-sm text-red-600 hover:text-red-800">
                                    Remove This Contact
                                </button>
                            @endif
                        </div>
                        <div class="p-4">
                            @foreach($contactFields as $field_key)
                                @php
                                    $field_id = 'contact_' . $uniqueId . '_' . $field_key;
                                @endphp

                                <div>
                                    <label for="{{ $field_id }}" class="block text-sm/6 font-medium text-gray-900">
                                        {{ str_replace('_', ' ', ucwords($field_key)) }}
                                        @if(in_array($field_key, $form->required_contact_fields ?? []))
                                            {{-- First name always required for UI indication --}}
                                            <span class="text-red-600">*</span>
                                        @endif
                                    </label>
                                    @if(in_array($field_key, ['first_name', 'middle_name', 'last_name']))
                                        <flux:input wire:model.blur="contacts.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                    clearable
                                                    id="{{ $field_id }}"/>
                                    @endif
                                    @if(in_array($field_key, ['email', 'mobile_phone']))
                                        <flux:input wire:model.blur="contacts.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                    type="{{ $field_key === 'email' ? 'email' : 'tel' }}"
                                                    clearable
                                                    id="{{ $field_id }}"/>
                                    @endif
                                    @if(in_array($field_key, ['allergies', 'special_needs']))
                                        <flux:textarea wire:model.live.debounce.1000ms="contacts.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                       id="{{ $field_id }}"
                                                       rows="2"
                                        />
                                    @endif
                                    @if($field_key === 'birthdate')
                                        <flux:date-picker wire:model.live="contacts.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                          selectable-header
                                        />
                                    @elseif($field_key === 'gender')
                                        <flux:select wire:model.blur="contacts.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                     id="{{ $field_id }}"
                                                     class="w-fit!"
                                                     clearable
                                        >
                                            <flux:select.option value="">- Gender -</flux:select.option>
                                            <flux:select.option value="male">Male</flux:select.option>
                                            <flux:select.option value="female">Female</flux:select.option>
                                        </flux:select>
                                    @elseif($field_key === 'marital_status')
                                        <flux:select wire:model.blur="contacts.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                     id="{{ $field_id }}"
                                                     class="w-fit!"
                                                     clearable
                                        >
                                            <flux:select.option value="">- Marital Status -</flux:select.option>
                                            <flux:select.option value="single">Single</flux:select.option>
                                            <flux:select.option value="married">Married</flux:select.option>
                                            <flux:select.option value="divorced">Divorced</flux:select.option>
                                            <flux:select.option value="widowed">Widowed</flux:select.option>
                                        </flux:select>
                                    @elseif($field_key === 'family_role')
                                        <flux:select wire:model.blur="contacts.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                     id="{{ $field_id }}"
                                                     class="w-fit!"
                                                     clearable
                                        >
                                            <flux:select.option value="">- Family Role -</flux:select.option>
                                            @foreach(\App\Programs\ProgramUser::$family_roles as $key => $value)
                                                <flux:select.option value="{{ $key }}">{{ ucfirst($value) }}</flux:select.option>
                                            @endforeach
                                        </flux:select>
                                    @endif
                                    @error('contacts.'.$uniqueId.'.user_fields.'.$field_key) <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                            @endforeach

                            <div class="mt-4 col-span-2 grid grid-cols-1 sm:grid-cols-3 gap-4">
                                <flux:checkbox wire:model.live="contacts.{{ $uniqueId }}.is_emergency_contact" label="Emergency Contact"/>
                                <flux:checkbox wire:model.live="contacts.{{ $uniqueId }}.is_primary_contact" label="Primary Contact"/>
                                <flux:checkbox wire:model.live="contacts.{{ $uniqueId }}.can_pickup" label="Authorized to Pick Up"/>
                            </div>


                            {{-- Custom Form Fields --}}
                            @if($form->contactFields->isNotEmpty())
                                <div class="grid grid-cols-1 gap-y-4 mt-4">
                                    @foreach($form->contactFields as $fieldIndex => $customField)
                                        <div>
                                            <div class="mb-2">
                                                <input type="hidden" wire:model.live="contacts.{{ $uniqueId }}.fields.{{ $customField->id }}.field_id" value="{{ $customField->id }}">
                                                <label for="field_{{ $uniqueId }}_{{ $customField->id }}" class="block text-sm/6 font-medium text-gray-900">
                                                    {{ $customField->title }} @if($customField->is_required)
                                                        <span class="text-red-600">*</span>
                                                    @endif
                                                </label>
                                                @if(false && $customField->description)
                                                    <p class="text-sm text-gray-600">{{ $customField->description }}</p>
                                                @endif
                                                @error('contacts.'.$uniqueId.'.fields.'.$customField->id.'.response') <p class="text-sm text-red-600">{{ $message }}</p> @enderror
                                                @error('contacts.'.$uniqueId.'.fields.'.$customField->id.'.multi_response') <p class="text-sm text-red-600">{{ $message }}</p> @enderror
                                            </div>

                                            <div class="ml-4">
                                                @if($customField->is_text_answer)
                                                    <flux:textarea wire:model.blur="contacts.{{ $uniqueId }}.fields.{{ $customField->id }}.response"
                                                                   id="field_{{ $uniqueId }}_{{ $customField->id }}"
                                                                   rows="2"
                                                    />
                                                @elseif($customField->is_select_program_group)
                                                    <flux:select wire:model.live="contacts.{{ $uniqueId }}.fields.{{ $customField->id }}.response"
                                                                 id="field_{{ $uniqueId }}_{{ $customField->id }}"
                                                                 class="w-fit!"
                                                                 clearable
                                                    >
                                                        <flux:select.option value="">- Select -</flux:select.option>
                                                        @foreach($program->groups()->orderBy('sort_id', 'asc')->whereIn('id', $customField->select_from_program_group_ids ?: [])->get() as $group)
                                                            <flux:select.option value="{{ $group->id }}">{{ $group->name }}</flux:select.option>
                                                        @endforeach
                                                    </flux:select>
                                                @elseif($customField->is_question_answer && !$customField->allow_multiple_answers)
                                                    <flux:select wire:model.live="contacts.{{ $uniqueId }}.fields.{{ $customField->id }}.response"
                                                                 id="field_{{ $uniqueId }}_{{ $customField->id }}"
                                                                 class="w-fit!"
                                                                 clearable
                                                    >
                                                        <flux:select.option value="">- Select -</flux:select.option>
                                                        @foreach($customField->question_answer_options as $optionIndex => $option)
                                                            <flux:select.option value="{{ $option }}">{{ $option }}</flux:select.option>
                                                        @endforeach
                                                    </flux:select>
                                                @elseif($customField->is_question_answer && $customField->allow_multiple_answers)
                                                    <flux:checkbox.group wire:model.live="contacts.{{ $uniqueId }}.fields.{{ $customField->id }}.multi_response">
                                                        @foreach($customField->question_answer_options as $option)
                                                            <flux:checkbox value="{{ $option }}"
                                                                           label="{{ $option }}"
                                                                           id="field_{{ $uniqueId }}_{{ $customField->id }}_opt_{{ $customField->id }}"/>
                                                        @endforeach
                                                    </flux:checkbox.group>
                                                @elseif($customField->is_true_false || $customField->is_yes_no)
                                                    <flux:radio.group wire:model.live="contacts.{{ $uniqueId }}.fields.{{ $customField->id }}.response">
                                                        @foreach(['Yes', 'No'] as  $option)
                                                            <flux:radio value="{{ $option }}"
                                                                        label="{{ $option }}"
                                                                        id="field_{{ $uniqueId }}_{{ $customField->id }}_opt_{{ $customField->id }}"/>
                                                        @endforeach
                                                    </flux:radio.group>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4 text-gray-500">
                        <p>Please add at least one parent/guardian contact.</p>
                    </div>
                @endforelse

                <div class="mt-2">
                    <button wire:click="addContact" type="button" class="flex flex-row cursor-pointer items-center gap-2 rounded-md bg-green-600 px-3.5 py-2 text-sm font-semibold text-white hover:bg-green-500">
                        Add Another Contact
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="mt-6 flex justify-between">
                <button wire:click="previousStep" type="button" class="flex flex-row cursor-pointer items-center gap-2 rounded-md bg-gray-200 px-3.5 py-2.5 text-base font-semibold text-gray-900 hover:bg-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"/>
                    </svg>
                    Back: Registrant Details
                </button>
                <button wire:click="nextStep" type="button" class="flex flex-row cursor-pointer items-center gap-2 rounded-md bg-blue-600 px-3.5 py-2.5 text-base font-semibold text-white hover:bg-blue-500">
                    Next: Review & Submit
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"/>
                    </svg>
                </button>
            </div>
        @endif

        {{-- Show general error if specific validation errors for this step exist --}}
        @if($errors->any() && $step === 2)
            <div class="flex">
                <div class="ml-auto mt-4 rounded-md bg-red-50 border border-red-300 p-2">
                    <h3 class="text-sm font-medium text-red-700">
                        Please correct the issues listed above.
                    </h3>
                </div>
            </div>
        @endif
    </div>
</div>