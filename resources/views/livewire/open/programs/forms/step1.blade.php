<div>
    <div class="mx-auto mt-6">

        <!-- Step Indicator -->
        <div class="flex justify-between mb-2">
            <div class="text-base font-semibold text-gray-900">
                Registrant Details
            </div>
            <div class="text-base text-gray-600">
                Step {{ $step }} of {{ $form->require_contact_information ? 3 : 2 }}
            </div>
        </div>

        <div class="space-y-4">
            @foreach($registrants as $uniqueId => $registrant)
                <div class="border border-gray-300 rounded-md bg-white">
                    <div class="flex justify-between items-center bg-gray-100 rounded-t-md px-4 py-2">
                        <h5 class="font-medium text-gray-900">
                            Registrant # {{ $loop->index + 1 }}
                            @if(!empty($registrant['user_fields']['first_name']) || !empty($registrant['user_fields']['last_name']))
                                <span class="font-normal">
                                &dash; {{ $registrant['user_fields']['first_name'] ?? '' }}
                                    {{ $registrant['user_fields']['last_name'] ?? '' }}
                               </span>
                            @endif
                        </h5>
                        @if(count($registrants) > 1)
                            <button wire:click="removeRegistrant('{{ $uniqueId }}')" class="cursor-pointer text-sm text-red-600 hover:text-red-800">
                                Remove This Registrant
                            </button>
                        @endif
                    </div>

                    <div class="p-4">
                        {{-- User Fields --}}
                        <div class="flex flex-col gap-4">
                            @foreach($userFields as $field_key)
                                @php
                                    $field_id = 'registrant_' . $uniqueId . '_' . $field_key;
                                @endphp

                                <div>
                                    <label for="{{ $field_id }}" class="block text-sm/6 font-medium text-gray-900">
                                        {{ str_replace('_', ' ', ucwords($field_key)) }}
                                        @if(in_array($field_key, $form->required_user_fields ?? []) || $field_key === 'first_name')
                                            {{-- First name always required for UI indication --}}
                                            <span class="text-red-600">*</span>
                                        @endif
                                    </label>
                                    @if(in_array($field_key, ['first_name', 'middle_name', 'last_name']))
                                        <flux:input wire:model.blur="registrants.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                    clearable
                                                    id="{{ $field_id }}"/>
                                    @endif
                                    @if(in_array($field_key, ['email', 'mobile_phone']))
                                        <flux:input wire:model.blur="registrants.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                    type="{{ $field_key === 'email' ? 'email' : 'tel' }}"
                                                    clearable
                                                    id="{{ $field_id }}"/>
                                    @endif
                                    @if(in_array($field_key, ['allergies', 'special_needs']))
                                        <flux:textarea wire:model.live.debounce.1000ms="registrants.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                       id="{{ $field_id }}"
                                                       rows="2"
                                        />
                                    @endif
                                    @if($field_key === 'birthdate')
                                        <flux:date-picker wire:model.live="registrants.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                          selectable-header
                                        />
                                    @elseif($field_key === 'gender')
                                        <flux:select wire:model.blur="registrants.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                     id="{{ $field_id }}"
                                                     class="w-fit!"
                                                     clearable
                                        >
                                            <flux:select.option value="">- Gender -</flux:select.option>
                                            <flux:select.option value="male">Male</flux:select.option>
                                            <flux:select.option value="female">Female</flux:select.option>
                                        </flux:select>
                                    @elseif($field_key === 'marital_status')
                                        <flux:select wire:model.blur="registrants.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                     id="{{ $field_id }}"
                                                     class="w-fit!"
                                                     clearable
                                        >
                                            <flux:select.option value="">- Marital Status -</flux:select.option>
                                            <flux:select.option value="single">Single</flux:select.option>
                                            <flux:select.option value="married">Married</flux:select.option>
                                            <flux:select.option value="divorced">Divorced</flux:select.option>
                                            <flux:select.option value="widowed">Widowed</flux:select.option>
                                        </flux:select>
                                    @elseif($field_key === 'family_role')
                                        <flux:select wire:model.blur="registrants.{{ $uniqueId }}.user_fields.{{ $field_key }}"
                                                     id="{{ $field_id }}"
                                                     class="w-fit!"
                                                     clearable
                                        >
                                            <flux:select.option value="">- Family Role -</flux:select.option>
                                            @foreach(\App\Programs\ProgramUser::$family_roles as $key => $value)
                                                <flux:select.option value="{{ $key }}">{{ ucfirst($value) }}</flux:select.option>
                                            @endforeach
                                        </flux:select>
                                    @endif
                                    @error('registrants.'.$uniqueId.'.user_fields.'.$field_key) <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                            @endforeach
                        </div>

                        {{-- Custom Form Fields --}}
                        @if($form->registrantFields->isNotEmpty())
                            <div class="grid grid-cols-1 gap-y-4 mt-4">
                                @foreach($form->registrantFields as $fieldIndex => $customField)
                                    <div>
                                        <div class="mb-2">
                                            <input type="hidden" wire:model.live="registrants.{{ $uniqueId }}.fields.{{ $customField->id }}.field_id" value="{{ $customField->id }}">
                                            <label for="field_{{ $uniqueId }}_{{ $customField->id }}" class="block text-sm/6 font-medium text-gray-900">
                                                {{ $customField->title }} @if($customField->is_required)
                                                    <span class="text-red-600">*</span>
                                                @endif
                                            </label>
                                            @if(false && $customField->description)
                                                <p class="text-sm text-gray-600">{{ $customField->description }}</p>
                                            @endif
                                            @error('registrants.'.$uniqueId.'.fields.'.$customField->id.'.response') <p class="text-sm text-red-600">{{ $message }}</p> @enderror
                                            @error('registrants.'.$uniqueId.'.fields.'.$customField->id.'.multi_response') <p class="text-sm text-red-600">{{ $message }}</p> @enderror
                                        </div>

                                        <div class="ml-4">
                                            @if($customField->is_text_answer)
                                                <flux:textarea wire:model.blur="registrants.{{ $uniqueId }}.fields.{{ $customField->id }}.response"
                                                               id="field_{{ $uniqueId }}_{{ $customField->id }}"
                                                               rows="2"
                                                />
                                            @elseif($customField->is_select_program_group)
                                                <flux:select wire:model.live="registrants.{{ $uniqueId }}.fields.{{ $customField->id }}.response"
                                                             id="field_{{ $uniqueId }}_{{ $customField->id }}"
                                                             class="w-fit!"
                                                             clearable
                                                >
                                                    <flux:select.option value="">- Select -</flux:select.option>
                                                    @foreach($program->groups()->orderBy('sort_id', 'asc')->whereIn('id', $customField->select_from_program_group_ids ?: [])->get() as $group)
                                                        <flux:select.option value="{{ $group->id }}">{{ $group->name }}</flux:select.option>
                                                    @endforeach
                                                </flux:select>
                                            @elseif($customField->is_question_answer && !$customField->allow_multiple_answers)
                                                <flux:select wire:model.live="registrants.{{ $uniqueId }}.fields.{{ $customField->id }}.response"
                                                             id="field_{{ $uniqueId }}_{{ $customField->id }}"
                                                             class="w-fit!"
                                                             clearable
                                                >
                                                    <flux:select.option value="">- Select -</flux:select.option>
                                                    @foreach($customField->question_answer_options as $optionIndex => $option)
                                                        <flux:select.option value="{{ $option }}">{{ $option }}</flux:select.option>
                                                    @endforeach
                                                </flux:select>
                                            @elseif($customField->is_question_answer && $customField->allow_multiple_answers)
                                                <flux:checkbox.group wire:model.live="registrants.{{ $uniqueId }}.fields.{{ $customField->id }}.multi_response">
                                                    @foreach($customField->question_answer_options as $option)
                                                        <flux:checkbox value="{{ $option }}"
                                                                       label="{{ $option }}"
                                                                       id="field_{{ $uniqueId }}_{{ $customField->id }}_opt_{{ $customField->id }}"/>
                                                    @endforeach
                                                </flux:checkbox.group>
                                            @elseif($customField->is_true_false || $customField->is_yes_no)
                                                <flux:radio.group wire:model.live="registrants.{{ $uniqueId }}.fields.{{ $customField->id }}.response">
                                                    @foreach(['Yes', 'No'] as  $option)
                                                        <flux:radio value="{{ $option }}"
                                                                    label="{{ $option }}"
                                                                    id="field_{{ $uniqueId }}_{{ $customField->id }}_opt_{{ $customField->id }}"/>
                                                    @endforeach
                                                </flux:radio.group>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div> {{-- End border per registrant --}}
            @endforeach
        </div>

        {{-- Add Another Registrant Button --}}
        @if(count($registrants) < $registration_limit_per_registration)
            <div class="mt-4">
                <button wire:click="addRegistrant" type="button" class="flex flex-row cursor-pointer items-center gap-2 rounded-md bg-green-600 px-3.5 py-2 text-sm font-semibold text-white hover:bg-green-500">
                    Add Another Registrant
                    <x-heroicon-o-plus class="w-4 h-4"/>
                </button>
            </div>
        @endif

        {{-- Navigation Buttons for Step 1 --}}
        @if($form->require_contact_information)
            <div class="mt-4 flex justify-between">
                <button wire:click="nextStep" type="button" class="flex flex-row cursor-pointer items-center gap-2 rounded-md bg-blue-600 px-3.5 py-2.5 text-base font-semibold text-white hover:bg-blue-500">
                    Continue to Contacts/Parents
                    <x-heroicon-o-arrow-right class="w-4 h-4"/>
                </button>
            </div>
        @else
            <div class="mt-4 flex justify-between">
                <button wire:click="submit" type="button" class="flex flex-row cursor-pointer items-center gap-2 rounded-md bg-blue-600 px-3.5 py-2.5 text-base font-semibold text-white hover:bg-blue-500">
                    Review Registration
                    <x-heroicon-o-check class="w-4 h-4"/>
                </button>
            </div>
        @endif
        {{-- End of Step 1 Content --}}


        {{-- Show general error if specific validation errors for this step exist --}}
        @if($errors->any() && $step === 1)
            <div class="flex">
                <div class="mr-auto mt-4 rounded-md bg-red-50 border border-red-300 p-2">
                    <h3 class="text-sm font-medium text-red-700">Please correct the issues listed above.</h3>
                </div>
            </div>
        @endif
    </div>
</div>