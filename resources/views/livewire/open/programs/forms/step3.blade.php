<div>
    <div class="mx-auto mt-6">

        <!-- Step Indicator -->
        <div class="flex justify-between mb-2">
            <div class="text-base font-semibold text-gray-900">
                Review Registration
            </div>
            <div class="text-base text-gray-600">
                Step {{ $step }} of 3
            </div>
        </div>

        <!-- Step 3: Review Registration -->
        <div class="space-y-6">
            <div>
                <h3 class="text-xl font-semibold text-gray-900 border-b pb-2 mb-3">Registrants</h3>
                @foreach($registrants as $index => $registrant)
                    <div class="border border-gray-200 rounded-md p-4 mb-3 bg-white">
                        <h4 class="text-lg font-medium text-gray-800">Registrant: {{ $registrant['user_fields']['first_name'] ?? 'N/A' }} {{ $registrant['user_fields']['last_name'] ?? 'N/A' }}</h4>
                        <p class="text-sm text-gray-600">Email: {{ $registrant['user_fields']['email'] ?? 'N/A' }}</p>
                        <p class="text-sm text-gray-600">{{ count(array_filter($registrant['fields'] ?? [])) }} form fields completed.</p>
                    </div>
                @endforeach
            </div>

            <div>
                <h3 class="text-xl font-semibold text-gray-900 border-b pb-2 mb-3">Contacts</h3>
                @if(count($contacts) > 0)
                    @foreach($contacts as $index => $contact)
                        <div class="border border-gray-200 rounded-md p-4 mb-3 bg-white">
                            <h4 class="text-lg font-medium text-gray-800">Contact: {{ $contact['user_fields']['first_name'] ?? 'N/A' }} {{ $contact['user_fields']['last_name'] ?? 'N/A' }}</h4>
                            <p class="text-sm text-gray-600">Email: {{ $contact['user_fields']['email'] ?? 'N/A' }}</p>
                            <div class="hidden mt-1 text-xs text-gray-500">
                                @if(isset($contact['is_primary_contact']) && $contact['is_primary_contact'])
                                    <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10 mr-1">Primary</span>
                                @endif
                                @if(isset($contact['is_emergency_contact']) && $contact['is_emergency_contact'])
                                    <span class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 font-medium text-red-700 ring-1 ring-inset ring-red-700/10 mr-1">Emergency</span>
                                @endif
                                @if(isset($contact['can_pickup']) && $contact['can_pickup'])
                                    <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 font-medium text-green-700 ring-1 ring-inset ring-green-700/10">Can Pickup</span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                @else
                    <p class="text-sm text-gray-600">No contacts provided.</p>
                @endif
            </div>
        </div>

        <div class="mt-8 flex justify-between">
            @if($form->require_contact_information)
                <button wire:click="previousStep" class="cursor-pointer rounded-md bg-gray-200 px-3.5 py-2.5 text-base font-semibold text-gray-900 hover:bg-gray-300">
                    Back: Contacts
                </button>
            @else
                <button wire:click="previousStep" class="cursor-pointer rounded-md bg-gray-200 px-3.5 py-2.5 text-base font-semibold text-gray-900 hover:bg-gray-300">
                    Back: Registrations
                </button>
            @endif
            <button wire:click="submit" class="cursor-pointer rounded-md bg-blue-600 px-3.5 py-2.5 text-base font-semibold text-white hover:bg-blue-500">
                Submit Registration
            </button>
        </div>

        @if($has_error)
            <div class="mt-4 rounded-md bg-red-50 border border-red-200 p-4">
                <h3 class="text-xl font-medium text-red-800">Oops!</h3>
                <div class="mt-2 text-base text-red-700">
                    <p>There was an error submitting your registration.<br>{{ $has_error }}</p>
                </div>
            </div>
        @endif

    </div>
</div>