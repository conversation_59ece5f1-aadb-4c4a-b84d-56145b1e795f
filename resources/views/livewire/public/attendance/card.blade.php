<div>
    <!-- Contact form -->
    <div class="relative isolate px-6 pt-6 lg:px-8">
        <div class="absolute inset-0 -z-10 overflow-hidden">
            <svg viewBox="0 0 1155 678" aria-hidden="true" class="absolute bottom-full -left-40 -mb-48 w-[72.1875rem] transform-gpu blur-3xl sm:left-auto sm:right-1/2 sm:-mb-64">
                <path fill="url(#d5d4d369-6b90-4398-a6aa-ab9a062d6e3c)" fill-opacity=".25"
                      d="M317.219 518.975 203.852 678 0 438.341l317.219 80.634 204.172-286.402c1.307 132.337 45.083 346.658 209.733 145.248C936.936 126.058 882.053-94.234 1031.02 41.331c119.18 108.451 130.68 295.337 121.53 375.223L855 299l21.173 362.054-558.954-142.079Z"/>
                <defs>
                    <linearGradient id="d5d4d369-6b90-4398-a6aa-ab9a062d6e3c" x1="1155.49" x2="-78.208" y1=".177" y2="474.645" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2986cc"/>
                        <stop offset="1" stop-color="#9089FC"/>
                    </linearGradient>
                </defs>
            </svg>
        </div>
        <div class="mx-auto max-w-xl">
            <h2 class="text-3xl font-medium tracking-tight text-gray-900 text-center">{{ $account->name }}</h2>
            <h2 class="mt-6 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">Welcome!</h2>
            <p class="mt-1 text-lg leading-8 text-gray-600">We'd love to have a record of your attendance.</p>
        </div>
        @if(!$submit_success)
            <form wire:submit.prevent="submit" class="mx-auto mt-6 max-w-xl">
                <div class="grid grid-cols-1 gap-y-2 gap-x-8 sm:grid-cols-2">
                    @if(auth()->user())
                        <div class="sm:col-span-2">
                            <label for="visited_at" class="block text-sm font-semibold leading-6 text-gray-900">Date of Visit</label>
                            <div class="mt-0">
                                <input type="date" name="visited_at" id="visited_at" autocomplete="none" wire:model.live="form_fields.visited_at"
                                       class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                            </div>
                        </div>
                    @endif
                    <div class="sm:col-span-2">
                        <label for="name" class="block text-sm font-semibold leading-6 text-gray-900">Name(s)</label>
                        <div class="mt-0">
                            <input type="text" name="name" id="name" autocomplete="none" wire:model.live="form_fields.name"
                                   class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                        </div>
                    </div>
                    <div class="sm:col-span-2">
                        <div class="grid grid-cols-6 gap-x-4">
                            <div class="col-span-3 sm:col-span-4">
                                <label for="email" class="block text-sm font-semibold leading-6 text-gray-900">Email</label>
                                <div class="mt-0">
                                    <input type="email" name="email" wire:model.live="form_fields.email" autocomplete="email"
                                           class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                                </div>
                            </div>
                            <div class="col-span-3 sm:col-span-2">
                                <label for="phone-number" class="block text-sm font-semibold leading-6 text-gray-900">Phone number</label>
                                <div class="mt-0">
                                    <input type="tel" name="phone-number" wire:model.live="form_fields.phone" autocomplete="tel"
                                           class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sm:col-span-2">
                        <div class="">
                            <label for="phone-number" class="block text-sm font-semibold leading-6 text-gray-900">Address</label>
                            <div class="mt-0">
                                <input type="text" name="phone-number" wire:model.live="form_fields.address1" autocomplete="none"
                                       class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                            </div>
                        </div>
                        <div class="grid grid-cols-3 gap-x-4">
                            <div class="col-span-3 sm:col-span-1">
                                <label for="phone-number" class="block text-sm font-semibold leading-6 text-gray-900">City</label>
                                <div class="mt-0">
                                    <input type="text" name="phone-number" wire:model.live="form_fields.city" autocomplete="none"
                                           class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                                </div>
                            </div>
                            <div class="col-span-2 sm:col-span-1">
                                <label for="phone-number" class="block text-sm font-semibold leading-6 text-gray-900">State</label>
                                <div class="mt-0">
                                    <input type="text" name="phone-number" wire:model.live="form_fields.state" autocomplete="none"
                                           class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                                </div>
                            </div>
                            <div class="col-span-1 sm:col-span-1">
                                <label for="phone-number" class="block text-sm font-semibold leading-6 text-gray-900">Zip Code</label>
                                <div class="mt-0">
                                    <input type="text" name="phone-number" wire:model.live="form_fields.zip" autocomplete="none"
                                           class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-base leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sm:col-span-2">
                        <label for="comments" class="block text-sm font-semibold leading-6 text-gray-900">Guest of</label>
                        <input name="comments" wire:model.live="form_fields.guest_of" rows="1" autocomplete="none"
                               class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-sm leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600"/>
                    </div>
                    <div class="sm:col-span-2">
                        <label for="comments" class="block text-sm font-semibold leading-6 text-gray-900">Comments</label>
                        <textarea name="comments" wire:model.live="form_fields.comments" rows="1"
                                  class="w-full bg-white rounded-md border-0 py-2 px-3.5 text-sm leading-6 text-gray-900 shadow-2xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600"></textarea>
                        <span class="text-sm text-gray-400">Interested in a Bible study? Or placing membership? Let us know!</span>
                    </div>
                    {{--                <div class="flex gap-x-4 sm:col-span-2">--}}
                    {{--                    <div class="flex h-6 items-center">--}}
                    {{--                        <!-- Enabled: "bg-blue-600", Not Enabled: "bg-gray-200" -->--}}
                    {{--                        <button type="button"--}}
                    {{--                                class="bg-gray-200 flex w-8 flex-none cursor-pointer rounded-full p-px ring-1 ring-inset ring-gray-900/5 transition-colors duration-200 ease-in-out focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"--}}
                    {{--                                role="switch" aria-checked="false" aria-labelledby="switch-1-label">--}}
                    {{--                            <span class="sr-only">Agree to policies</span>--}}
                    {{--                            <!-- Enabled: "translate-x-3.5", Not Enabled: "translate-x-0" -->--}}
                    {{--                            <span aria-hidden="true" class="translate-x-0 h-4 w-4 transform rounded-full bg-white shadow-2xs ring-1 ring-gray-900/5 transition duration-200 ease-in-out"></span>--}}
                    {{--                        </button>--}}
                    {{--                    </div>--}}
                    {{--                    <label class="text-sm leading-6 text-gray-600" id="switch-1-label">--}}
                    {{--                        By selecting this, you agree to our--}}
                    {{--                        <a href="#" class="font-semibold text-blue-600">privacy&nbsp;policy</a>.--}}
                    {{--                    </label>--}}
                    {{--                </div>--}}

                    {{--                <div class="sm:col-span-2">--}}
                    {{--                    <fieldset>--}}
                    {{--                        <legend class="text-sm font-medium text-gray-900">Attendance Check-in</legend>--}}

                    {{--                        <div class="isolate mt-1 -space-y-px rounded-md bg-white shadow-2xs">--}}
                    {{--                            @foreach((new \App\Attendance\Services\AttendanceService())->forUser(auth()->user())->getCheckinAttendanceTypesForNow() as $type)--}}

                    {{--                                <!-- Checked: "z-10 border-blue-200 bg-blue-50", Not Checked: "border-gray-200" -->--}}
                    {{--                                <label class="{{ $loop->first ? 'rounded-tl-md rounded-tr-md' : '' }} {{ $loop->last ? 'rounded-bl-md rounded-br-md' : '' }}  relative flex cursor-pointer border p-4 focus:outline-hidden">--}}
                    {{--                                    <input type="checkbox" name="attendance_types[]" value="Public access" class="mt-0.5 rounded shrink-0 cursor-pointer text-blue-600 border-gray-300 focus:ring-blue-500">--}}
                    {{--                                    <span class="ml-3 flex flex-col">--}}
                    {{--                                    <!-- Checked: "text-blue-900", Not Checked: "text-gray-900" -->--}}
                    {{--                                    <span id="type-{{ $type->id }}-label" class="block text-sm font-medium">{{ $type->name }}</span>--}}
                    {{--                                        <!-- Checked: "text-blue-700", Not Checked: "text-gray-500" -->--}}
                    {{--                                    <span id="type-{{ $type->id }}-description" class="block text-sm">This project would be available to anyone who has the link</span>--}}
                    {{--                                </span>--}}
                    {{--                                </label>--}}
                    {{--                            @endforeach--}}
                    {{--                        </div>--}}
                    {{--                    </fieldset>--}}
                    {{--                </div>--}}

                </div>
                @if($has_error)
                    <div class="mx-auto mt-4 max-w-xl">
                        <div class="rounded-md bg-red-50 border border-red-200 p-4">
                            <h3 class="text-xl font-medium text-red-800">Oops!</h3>
                            <div class="mt-2 text-base text-red-700">
                                <p>
                                    There was an error submitted your information.
                                    <br>
                                    <span class="text-sm">Please make sure basic information is filled in. Only the name field is required.</span>
                                    <br>
                                    {{ $has_error }}
                                </p>
                            </div>
                        </div>
                    </div>
                @endif
                <div class="mt-6">
                    <button type="submit"
                            class="w-full hover:cursor-pointer rounded-md bg-blue-600 px-3.5 py-2.5 text-center text-base font-semibold text-white shadow-2xs hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                        Submit
                    </button>
                </div>
            </form>
        @endif
        @if($submit_success)
            <div class="mx-auto mt-6 pb-20 max-w-xl">
                <div class="rounded-md bg-green-50 border border-green-200 p-4">
                    <div class="flex">
                        <div class="shrink-0">
                            <svg class="h-10 w-10 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-xl font-medium text-green-800">Success!</h3>
                            <div class="mt-2 text-base text-green-700">
                                <p>Great to hear from you.<br>Your information was successfully submitted.</p>
                            </div>
                            @if($account->church_website)
                                <div class="mt-4 mb-2">
                                    <div class="flex">
                                        <a href="{{ $account->church_website }}"
                                           class="rounded-md bg-green-200 px-4 py-2.5 text-base font-medium text-green-800 hover:bg-green-100 focus:outline-hidden focus:ring-2 focus:ring-green-600 focus:ring-offset-2 focus:ring-offset-green-50">
                                            Visit Church Website
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

</div>
