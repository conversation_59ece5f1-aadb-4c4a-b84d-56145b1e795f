<div id="new-checkin-modal"
     x-cloak
     style="display:none"
     x-data="{ ExistingUserMode: true }"
     x-init=""
     x-show="$store.NewCheckinModal.open"
     @keydown.escape="$store.NewCheckinModal.open = false"
     @click.away="$store.NewCheckinModal.open = false"
     class="fixed top-0 sm:bottom-auto mt-10 inset-x-0 px-4 pb-6 sm:inset-0 sm:p-0 sm:flex sm:items-center sm:justify-center z-50"
     x-transition:enter="ease-out duration-100" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-100" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
    <!--
      Background overlay, show/hide based on modal state.
    -->
    <div @click="$store.NewCheckinModal.open = false" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-100" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="absolute inset-0 transition-opacity">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
    </div>

    <!--
      Modal panel, show/hide based on modal state.
    -->
    <div id="new-checkin-modal-content"
         class="relative z-10 bg-white rounded-lg px-4 pt-5 pb-4 overflow-hidden shadow-xl transform transition-all sm:max-w-4xl sm:w-full sm:p-6"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-100" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
         role="dialog" aria-modal="true" aria-labelledby="modal-headline">
        <form method="post" action="">
            {{ csrf_field() }}
            <div class="sm:flex sm:items-start">
                <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
                    </svg>
                </div>
                <div class="grow mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 class="text-3xl leading-6 mt-1 font-medium text-gray-900" id="new-checkin-modal-title">
                        New Check-in
                    </h3>
                </div>
            </div>
            <div class="mt-4">
                <div class="mx-20">
                    <nav class="rounded-lg flex divide-x divide-gray-200 border border-gray-200" aria-label="Tabs">
                        <a x-on:click="ExistingUserMode = true" :class="ExistingUserMode ? 'bg-indigo-600 text-white' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'" class="cursor-pointer text-base rounded-l-lg min-w-0 flex-1 overflow-hidden py-2 px-4 font-medium text-center focus:z-10" aria-current="page">
                            <span>Existing Member / Visitor</span>
                            <span aria-hidden="true" class="bg-indigo-500 absolute inset-x-0 bottom-0 h-0.5"></span>
                        </a>
                        <a x-on:click="ExistingUserMode = false" :class="!ExistingUserMode ? 'bg-indigo-600 text-white' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'" class="cursor-pointer text-base rounded-r-lg min-w-0 flex-1 overflow-hidden py-2 px-4 font-medium text-center focus:z-10">
                            <span>New Visitor</span>
                            <span aria-hidden="true" class="bg-transparent absolute inset-x-0 bottom-0 h-0.5"></span>
                        </a>
                    </nav>
                </div>
            </div>
            <div x-show="ExistingUserMode">
                <div class="flex mt-6">
                    <input type="text" id="checkin_search_string" wire:model.live="search_string" placeholder="Search..." class="rounded-sm border-gray-600 border mx-auto" autofocus/>
                </div>
                <div class="mt-6">
                    <div class="bg-white border border-gray-300 overflow-hidden sm:rounded-md">
                        <ul role="list" class="divide-y divide-gray-200">
                            @forelse($children as $child)
                                <li class="block hover:bg-gray-50 cursor-pointer" onclick="Livewire.dispatch('checkinViaLivewire', {child_user_id: {{ $child->id }}}); Alpine.store('NewCheckinModal').toggle();">
                                    <div class="flex items-center px-4 py-4 sm:px-6">
                                        <div class="min-w-0 flex-1 flex items-center">
                                            <div class="shrink-0">
                                                @if($child->avatar)
                                                    <img class="h-10 w-10 rounded-full" src="{{ $child->avatar->getCdnUrl(256) }}" alt="">
                                                @else
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                @endif
                                            </div>
                                            <div class="min-w-0 flex-1 px-4 md:grid md:grid-cols-2 md:gap-2">
                                                <div>
                                                    <p class="text-lg font-medium text-indigo-600 truncate">{{ $child->name }}</p>
                                                    <p class="mt-1 flex items-center text-gray-500 text-sm">
                                                        <span class="truncate">
                                                            Parents:
                                                            <span class="font-semibold">
                                                                @foreach($child->parents as $parent)
                                                                    {{ $parent->first_name }}
                                                                    {!! $loop->last ? null : ' &amp; ' !!}
                                                                @endforeach
                                                            </span>
                                                        </span>
                                                    </p>
                                                </div>
                                                <div class="hidden md:block my-auto">
                                                    <p class="text-base text-green-600 font-medium text-right">
                                                        Check-in
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <!-- Heroicon name: solid/chevron-right -->
                                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                    </div>
                                </li>
                            @empty
                                @if(empty($search_string) || strlen($search_string) < 2)
                                    <li class="bg-indigo-50 border border-indigo-500 rounded-sm">
                                        <div class="p-4 text-center text-indigo-600 px-6 py-3">
                                            Start typing to search...
                                        </div>
                                    </li>
                                @else
                                    <li class="bg-amber-50 border border-amber-500 rounded-sm">
                                        <div class="p-4 text-center text-amber-600 px-6 py-3">
                                            No search results.
                                        </div>
                                    </li>
                                @endif
                            @endforelse
                        </ul>
                    </div>
                </div>
            </div>
            <div x-show="!ExistingUserMode">
                <div class="bg-white mt-4 px-4 py-5 sm:rounded-lg sm:p-6 border border-gray-300">
                    <div class="md:grid md:grid-cols-5 md:gap-6">
                        <div class="md:col-span-1">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Parent Information</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                Enter the information of one parent.
                            </p>
                        </div>
                        <div class="mt-5 md:mt-0 md:col-span-4">
                            <form action="#" method="POST">
                                @if($selected_existing_parent)
                                    <li class="py-4 px-4 flex bg-green-50 cursor-pointer border border-green-300 rounded-sm hover:bg-red-50 hover:border-red-300"
                                        wire:click="$dispatch('selectExistingParent', null)">
                                        @if($existing_parent_selected->avatar)
                                            <img class="h-10 w-10 rounded-full" src="{{ $existing_parent_selected->avatar->getCdnUrl(256) }}" alt="">
                                        @else
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        @endif
                                        <div class="grow ml-3">
                                            <p class="text-base font-medium text-gray-900">{{ $existing_parent_selected->name }}</p>
                                            <p class="text-sm text-gray-500">{{ $existing_parent_selected->getBestPhone()?->formattedNumber() }}</p>
                                        </div>
                                        <div class="shrink truncate text-sm text-gray-500">
                                            {{ $existing_parent_selected->getFamilyAddress()?->address1 }}
                                        </div>
                                    </li>
                                @else
                                    <div class="grid grid-cols-6 gap-2">
                                        <div class="col-span-6 sm:col-span-3">
                                            <label for="first-name" class="block text-sm font-medium text-gray-700">First name</label>
                                            <input type="text" name="first-name" wire:model.live="parent_search_first_name" id="first-name" autocomplete="off" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                                        </div>

                                        <div class="col-span-6 sm:col-span-3">
                                            <label for="last-name" class="block text-sm font-medium text-gray-700">Last name</label>
                                            <input type="text" name="last-name" wire:model.live="parent_search_last_name" id="last-name" autocomplete="off" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                                        </div>

                                        <div class="col-span-6 sm:col-span-4">
                                            <label for="mobile-number" class="block text-sm font-medium text-gray-700">Mobile number</label>
                                            <input type="text" name="mobile-number" wire:model.live="parent_search_mobile_number" id="mobile-number" autocomplete="email" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                                        </div>
                                    </div>
                                    @if($parents)
                                        <div class="mt-4">
                                            <strong>Did you mean?</strong>
                                            <ul role="list" class="divide-y divide-gray-200 rounded-sm border border-gray-300">
                                                @forelse($parents as $parent)
                                                    <li class="py-4 px-4 flex hover:bg-gray-100 cursor-pointer"
                                                        wire:click="$dispatch('selectExistingParent', { selected_existing_parent: {{ $parent->id }} })">
                                                        @if($parent->avatar)
                                                            <img class="h-10 w-10 rounded-full" src="{{ $parent->avatar->getCdnUrl(256) }}" alt="">
                                                        @else
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                            </svg>
                                                        @endif
                                                        <div class="grow ml-3">
                                                            <p class="text-base font-medium text-gray-900">{{ $parent->name }}</p>
                                                            <p class="text-sm text-gray-500">{{ $parent->getBestPhone()?->formattedNumber() }}</p>
                                                        </div>
                                                        <div class="shrink truncate text-sm text-gray-500">
                                                            {{ $parent->getFamilyAddress()?->address1 }}
                                                        </div>
                                                    </li>
                                                @empty
                                                    <li class="py-4 px-4 flex bg-gray-50 cursor-pointer">
                                                        <div class="grow ml-3">
                                                            <p class="text-sm text-gray-500">No suggestions found...</p>
                                                        </div>
                                                    </li>
                                                @endforelse
                                            </ul>
                                        </div>
                                    @endif
                                @endif
                            </form>
                        </div>
                    </div>
                </div>
                <div class="bg-white mt-4 px-4 py-5 sm:rounded-lg sm:p-6 border border-gray-300">
                    <div class="md:grid md:grid-cols-5 md:gap-6">
                        <div class="md:col-span-1">
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Child Information</h3>
                        </div>
                        <div class="mt-5 md:mt-0 md:col-span-4">
                            <form action="#" method="POST">
                                <div class="grid grid-cols-6 gap-2">
                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="child-first-name" class="block text-sm font-medium text-gray-700">First name</label>
                                        <input type="text" name="child-first-name" wire:model.live="child_first_name" id="child-first-name" autocomplete="off" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="child-last-name" class="block text-sm font-medium text-gray-700">Last name</label>
                                        <input type="text" name="child-last-name" wire:model.live="child_last_name" id="child-last-name" autocomplete="off" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                                    </div>

                                    <div class="col-span-6">
                                        <label for="allergies" class="block text-sm font-medium text-gray-700">Allergies</label>
                                        <input type="text" name="allergies" wire:model.live="child_allergies" id="allergies" autocomplete="street-address" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                    <div class="col-span-6">
                                        <label for="special_needs" class="block text-sm font-medium text-gray-700">Special Needs</label>
                                        <input type="text" name="special_needs" wire:model.live="child_special_needs" id="special_needs" autocomplete="street-address" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="mx-6">
                    <button type="button" wire:click="$dispatch('checkinNewVisitor')" class="mt-4 inline-flex items-center justify-center py-2 border border-transparent shadow-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 xl:w-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-1 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                        </svg>
                        Create new record &amp; check-in
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
