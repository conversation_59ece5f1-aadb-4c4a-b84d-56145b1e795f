<div class="bg-gray-50 pr-4 sm:pr-6 lg:pr-8 lg:shrink-0 lg:border-l lg:border-gray-200 xl:pr-0">
    <div class="pl-6 lg:w-80">
        <div class="pt-6 pb-2">
            <h2 class="text-sm font-semibold">Activity</h2>
        </div>
        <div>
            <ul role="list" class="divide-y divide-gray-200">
                @foreach($activity as $event)
                    @if($event->child)
                        <li class="py-4">
                            <div class="flex space-x-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div class="flex-1 space-y-1">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-sm font-medium">{{ $event->child->name }}</h3>
                                        <p class="text-xs text-gray-400" id="{{ uniqid() }}" x-data="TimeAgo('{{ $event->created_at->setTimezone(auth()->user()->account->timezone)->format('c') }}')">
                                            <span class="relative hover:text-gray-900" x-text="getTimeAgo()"></span>
                                        </p>
                                    </div>
                                    @if($event->type == 'checkin')
                                        <div class="flex flex-row items-center text-sm text-gray-500">
                                            <svg class="mr-1 w-4 h-4 text-green-500" style="transform: rotate(180deg);" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                                            </svg>
                                            <span>{{ \App\ChildCheckins\Activity::$types[$event->type] }}</span>
                                        </div>
                                    @elseif($event->type == 'checkout')
                                        <div class="flex flex-row items-center text-sm text-gray-500">
                                            <span>{{ \App\ChildCheckins\Activity::$types[$event->type] }}</span>
                                            <svg class="ml-1 w-4 h-4 text-amber-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                            </svg>
                                        </div>
                                    @else
                                        <p class="text-sm text-gray-500">{{ \App\ChildCheckins\Activity::$types[$event->type] }}</p>
                                    @endif
                                </div>
                            </div>
                        </li>
                    @endif
                @endforeach
            </ul>
            <div class="hidden py-4 text-sm border-t border-gray-200">
                <a href="#" class="text-indigo-600 font-semibold hover:text-indigo-900">View all activity <span aria-hidden="true">&rarr;</span></a>
            </div>
        </div>
    </div>
</div>