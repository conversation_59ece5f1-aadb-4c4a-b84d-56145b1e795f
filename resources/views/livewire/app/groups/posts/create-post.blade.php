<div>
    @error('files.*')
    <div class="text-red-500 text-sm">{{ $message }}</div>
    @enderror

    <div class="px-5 py-4 mb-6 bg-white rounded-lg max-w-3xl border border-gray-200" x-data="{ editing: false }">
        <div class="flex">
            @if(auth()->user()->avatar)
                <img class="w-10 h-10 rounded-full mr-2" src="{{ auth()->user()->avatar->getCdnUrl(256) }}"/>
            @else
                <svg class="w-11 h-11 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            @endif
            <div class="flex flex-col gap-2 w-full">
                <div class="textarea-grow-wrap w-full bg-gray-100 rounded-sm focus:ring-0 resize-none overflow-hidden">
                    <textarea id="new_post_text_{{$group->id}}" name="content"
                              placeholder="Create a new post..." onInput="this.parentNode.dataset.replicatedValue = this.value"
                              wire:model="content"
                              x-on:blur="editing = false" x-on:focus="editing = true"
                              class="w-full border border-gray-300 bg-gray-100 rounded-sm placeholder-gray-400 focus:ring-0 py-2 resize-none overflow-hidden"
                              rows="1"></textarea>
                </div>
                <div class="flex justify-between gap-2">
                    <div class="flex flex-col space-y-1">
                        <div class="flex flex-row">
                            <input type="file" id="file-upload"
                                   wire:model="files" multiple class="hidden">
                            <label for="file-upload"
                                   class="w-fit text-gray-600 border border-gray-300 px-3 py-1 rounded-sm hover:bg-blue-600 hover:text-white cursor-pointer text-sm mb-auto">
                                Add Files
                            </label>

                            <div x-cloak wire:loading.remove wire:target="files" x-show="editing" class="ml-2 text-xs text-gray-400">
                                Press "Enter" to create new lines for paragraphs.
                            </div>
                            <div wire:loading wire:target="files" class="flex flex-row! border border-blue-500 rounded-sm px-2 py-1 text-sm text-blue-500 ml-2 my-auto">
                                <x-heroicon-m-arrow-path class="inline w-4 h-4 mr-0.5 animate-spin"/>
                                <span class="inline animate-pulse">Uploading...</span>
                            </div>
                        </div>
                        @if($uploaded_files)
                            <div class="w-full">
                                <div class="p-2 border border-gray-300 rounded-md text-gray-700 mt-2 text-sm">
                                    @foreach($uploaded_files as $file)
                                        <div class="flex flex-row">
                                            <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto mr-1"/>
                                            <span>
                                            {{ $file['name'] }}
                                            <span class="text-xs text-gray-400 ml-1">{{ number_format($file['size'] / 1024 / 1024, 1) }}MB</span>
                                        </span>
                                        </div>
                                    @endforeach
                                </div>
                                <span class="text-red-600 text-xs cursor-pointer" wire:click="clearFiles">Clear Files</span>
                            </div>
                        @endif
                    </div>
                    <button wire:loading.remove wire:target="files" class="mb-auto text-gray-600 border border-gray-400 px-3 py-2 rounded-sm hover:bg-blue-600 hover:text-white bg-gray-100 cursor-pointer" wire:click="submit" wire:loading.attr="disabled">Send</button>
                    <button wire:loading wire:target="files" class="mb-auto text-gray-300 border border-gray-400 px-3 py-2 rounded-sm bg-gray-100 cursor-pointer">Send</button>
                </div>
            </div>
        </div>
    </div>
</div>