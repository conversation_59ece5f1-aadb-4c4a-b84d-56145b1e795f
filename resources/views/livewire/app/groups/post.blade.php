<div class="relative p-4 mb-6 bg-white rounded-lg max-w-3xl border border-gray-200">
    @if($post->is_pinned)
        <flux:tooltip content="This post is pinned to the top.">
        <span class="absolute top-0 right-0 flex flex-row bg-purple-100 text-purple-800 rounded-bl-md rounded-tr-md text-xs font-medium mb-auto px-2 py-1.5">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"
                 class="w-3 h-3 mr-1 my-auto text-purple-800">
                <path class="fill-current" d="M32 32C32 14.3 46.3 0 64 0L320 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-29.5 0 11.4 148.2c36.7 19.9 65.7 53.2 79.5 94.7l1 3c3.3 9.8 1.6 20.5-4.4 28.8s-15.7 13.3-26 13.3L32 352c-10.3 0-19.9-4.9-26-13.3s-7.7-19.1-4.4-28.8l1-3c13.8-41.5 42.8-74.8 79.5-94.7L93.5 64 64 64C46.3 64 32 49.7 32 32zM160 384l64 0 0 96c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-96z"/>
            </svg>
            Pinned
        </span>
        </flux:tooltip>
    @endif
    <div class="flex border-b border-gray-300 pb-2 items-center">
        @if($post->creator->avatar)
            <img class="w-10 h-10 rounded-full mr-2" src="{{ $post->creator->avatar->getCdnUrl(512) }}"/>
        @else
            <svg class="w-11 h-11 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
        @endif
        <div class="flex-1">
            <div class="flex flex-row justify-between" x-data="{ open: false }">
                <div>
                    <span class="block font-medium text-base leading-snug text-black">{{ $post->creator->name }}</span>
                    <span class="block text-sm text-gray-500 font-light leading-snug">{{ $post->getPublishedAtForHumans(auth()->user()->account->timezone) }}</span>
                </div>
                <div @click.away="open = false">
                    @if(auth()->user()->can('edit', $post))
                        <div class="relative inline-block text-left">
                            <div class="flex flex-row gap-2">
                                <button type="button" @click="open = !open" class="rounded-full p-1 flex items-center text-gray-500 hover:bg-gray-100 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-indigo-500" id="menu-button" aria-expanded="true" aria-haspopup="true">
                                    <span class="sr-only">Open options</span>
                                    <x-heroicon-s-ellipsis-horizontal class="w-6 text-gray-500"/>
                                </button>
                            </div>
                            <div x-cloak x-show="open" class="origin-top-right absolute right-0 border border-gray-300 mt-2 w-32 rounded-md shadow-lg bg-white focus:outline-hidden" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
                                <div class="py-1" role="none">
                                    @if(!$post->is_pinned)
                                        <a wire:click="pinPost"
                                           x-on:click="open = false"
                                           class="cursor-pointer text-gray-700 my-auto flex flex-row px-4 py-2 text-sm hover:bg-gray-100"
                                           role="menuitem" tabindex="-1" id="menu-item-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"
                                                 class="w-4 h-4 mr-2 my-auto">
                                                <path d="M32 32C32 14.3 46.3 0 64 0L320 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-29.5 0 11.4 148.2c36.7 19.9 65.7 53.2 79.5 94.7l1 3c3.3 9.8 1.6 20.5-4.4 28.8s-15.7 13.3-26 13.3L32 352c-10.3 0-19.9-4.9-26-13.3s-7.7-19.1-4.4-28.8l1-3c13.8-41.5 42.8-74.8 79.5-94.7L93.5 64 64 64C46.3 64 32 49.7 32 32zM160 384l64 0 0 96c0 17.7-14.3 32-32 32s-32-14.3-32-32l0-96z"/>
                                            </svg>
                                            Pin
                                        </a>
                                    @else
                                        <a wire:click="unpinPost"
                                           x-on:click="open = false"
                                           class="cursor-pointer text-gray-700 my-auto flex flex-row px-4 py-2 text-sm hover:bg-gray-100"
                                           role="menuitem" tabindex="-1" id="menu-item-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"
                                                 class="w-4 h-4 mr-2 my-auto">
                                                <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.8-.4 18.9-5.3 24.6-13.3c6-8.3 7.7-19.1 4.4-28.8l-1-3c-13.8-41.5-42.8-74.8-79.5-94.7L418.5 64 448 64c17.7 0 32-14.3 32-32s-14.3-32-32-32L192 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l29.5 0-6.1 79.5L38.8 5.1zM324.9 352L177.1 235.6c-20.9 18.9-37.2 43.3-46.5 71.3l-1 3c-3.3 9.8-1.6 20.5 4.4 28.8s15.7 13.3 26 13.3l164.9 0zM288 384l0 96c0 17.7 14.3 32 32 32s32-14.3 32-32l0-96-64 0z"/>
                                            </svg>
                                            Unpin
                                        </a>
                                    @endif
                                    <a wire:click="deletePostMode('on')"
                                       x-on:click="open = false"
                                       class="cursor-pointer flex px-4 py-2 text-sm hover:bg-red-50 text-red-500"
                                       role="menuitem" tabindex="-1" id="menu-item-2">
                                        <x-heroicon-s-trash class="w-5 mr-2"/>
                                        Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="mt-3">
        @if($delete_mode)
            <div class="flex-1 py-4 text-center">
                <span class="p-2 bg-red-100 text-red-800 rounded-lg">
                    Are you sure you want to delete this post?
                </span>
                <div class="mt-4">
                    <button wire:click="deletePost" type="button" class="inline-flex items-center px-3 py-2 border border-red-500 shadow-2xs text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Yes, Delete</button>
                    <button wire:click="deletePostMode('off')" type="button" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-2xs text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Cancel</button>
                </div>
            </div>
        @else
            {!! nl2br((new VStelmakh\UrlHighlight\UrlHighlight())->highlightUrls($post->content)) !!}
        @endif
    </div>

    @if($post->images()->exists() || $post->documents()->exists())
        <div class="flex flex-col gap-2 mt-2">
            @if($post->images()->exists() && !$delete_mode)
                @php
                    $files = $post->images;
                    $files_count = $files->count();
                @endphp
                <div x-cloak x-data="{ open: false, image: [], count: {{ $files_count }} }" class="bg-gray-100 rounded-md">
                    @foreach($files as $image)
                        <img x-on:click="open = true; image[{{ $image->id }}] = true;" class="inline w-20 aspect-square" src="{{ $image->getUrl(256) }}"/>
                    @endforeach
                    <div x-show="open"
                         x-on:keydown.escape.prevent.stop="open = false; image = [];"
                         role="dialog"
                         aria-modal="true"
                         class="fixed inset-0 overflow-y-auto">
                        <!-- Overlay -->
                        <div x-show="open" x-transition.opacity class="fixed inset-0 bg-black bg-opacity-50"></div>

                        <!-- Panel -->
                        <div x-show="open" x-transition
                             x-on:click="open = false; image = [];"
                             class="relative min-h-screen flex items-center justify-center p-4">
                            <div x-on:click.stop
                                 x-trap.noscroll.inert="open"
                                 class="relative max-w-4xl bg-white border border-blue-500 p-2 rounded overflow-y-auto">
                                <div class="mb-4 flex">
                                    <button type="button" x-on:click="open = false; image = [];" class="bg-white rounded mx-auto border border-black px-4 py-2 focus:outline-hidden focus:ring-4 focus:ring-aqua-400">
                                        close
                                    </button>
                                </div>
                                @foreach($files as $image)
                                    <div class="flex">
                                        <img x-show="image[{{ $image->id }}]" class="inline max-h-full aspect-square mx-auto" src="{{ $image->getUrl() }}"/>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endif
            @if($post->documents()->exists() && !$delete_mode)
                <div class="grid grid-cols-1 divide-y border border-gray-300 rounded ">
                    @foreach($post->documents as $file)
                        <a href="{{ $file->getTemporaryUrl() }}">
                            <div class="flex flex-row text-black px-2 py-2 hover:bg-gray-100">
                                <x-heroicon-o-document-arrow-down class="w-6 mr-1"/>
                                <div>{{ $file->file_name_original }}</div>
                            </div>
                        </a>
                    @endforeach
                </div>
            @endif
        </div>
    @endif

    <div class="flex justify-between mt-2 pb-2">
        <div class="flex flex-row">
            @if($post->reactions_count)
                @if($post->loves_count)
                    <img class="w-6 h-6 border-2 border-white rounded-full" src="/static/app/img/reactions/love-128.png"/>
                @endif
                @if($post->likes_count)
                    <img class="w-6 h-6 border-2 border-white rounded-full -ml-1" src="/static/app/img/reactions/like-128.png"/>
                @endif
                @if($post->cares_count)
                    <img class="w-6 h-6 border-2 border-white rounded-full -ml-1" src="/static/app/img/reactions/care-128.png"/>
                @endif
                @if($post->laughs_count)
                    <img class="w-6 h-6 border-2 border-white rounded-full -ml-1" src="/static/app/img/reactions/haha-128.png"/>
                @endif
                @if($post->prays_count)
                    <img class="w-6 h-6 border-2 border-white rounded-full -ml-1" src="/static/app/img/reactions/pray-128.png"/>
                @endif
                <div class="ml-1 text-gray-500">
                    {{ $post->reactions_count }}
                </div>
            @endif
        </div>
        <div class="whitespace-nowrap text-gray-500 font-light">
            <a wire:click="$refresh" class="flex text-gray-600">
                <span id="comment_count_{{ $post->id }}">{{ $post->comments_count }}</span>&nbsp;comments
                <svg class="h-4 w-4 bg-white my-auto hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
            </a>
        </div>
    </div>
    <div x-data="{ open: false, editing: false }">
        <div class="grid grid-cols-2 border border-gray-300 rounded-md">
            <a class="hover:bg-gray-100 rounded py-2 rounded-l-md cursor-pointer">
                <div wire:click="unlikePost" class="flex justify-center items-center {{ !$post->reactions->contains('user_id', auth()->user()->id) ? 'hidden' : null }}">
                    <svg class="text-center w-4 h-4 mr-2 fill-current text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <path d="M104 224H24c-13.255 0-24 10.745-24 24v240c0 13.255 10.745 24 24 24h80c13.255 0 24-10.745 24-24V248c0-13.255-10.745-24-24-24zM64 472c-13.255 0-24-10.745-24-24s10.745-24 24-24 24 10.745 24 24-10.745 24-24 24zM384 81.452c0 42.416-25.97 66.208-33.277 94.548h101.723c33.397 0 59.397 27.746 59.553 58.098.084 17.938-7.546 37.249-19.439 49.197l-.11.11c9.836 23.337 8.237 56.037-9.308 79.469 8.681 25.895-.069 57.704-16.382 74.757 4.298 17.598 2.244 32.575-6.148 44.632C440.202 511.587 389.616 512 346.839 512l-2.845-.001c-48.287-.017-87.806-17.598-119.56-31.725-15.957-7.099-36.821-15.887-52.651-16.178-6.54-.12-11.783-5.457-11.783-11.998v-213.77c0-3.2 1.282-6.271 3.558-8.521 39.614-39.144 56.648-80.587 89.117-113.111 14.804-14.832 20.188-37.236 25.393-58.902C282.515 39.293 291.817 0 312 0c24 0 72 8 72 81.452z"/>
                    </svg>
                    <span class="text-blue-500 font-semibold">Like</span>
                </div>
                <div wire:click="likePost" class="flex justify-center items-center {{ $post->reactions->contains('user_id', auth()->user()->id) ? 'hidden' : null }}">
                    <svg class="text-center w-4 h-4 mr-2 fill-current text-gray-500 group-hover:text-gray-500 group-focus:text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <path d="M496.656 285.683C506.583 272.809 512 256 512 235.468c-.001-37.674-32.073-72.571-72.727-72.571h-70.15c8.72-17.368 20.695-38.911 20.695-69.817C389.819 34.672 366.518 0 306.91 0c-29.995 0-41.126 37.918-46.829 67.228-3.407 17.511-6.626 34.052-16.525 43.951C219.986 134.75 184 192 162.382 203.625c-2.189.922-4.986 1.648-8.032 2.223C148.577 197.484 138.931 192 128 192H32c-17.673 0-32 14.327-32 32v256c0 17.673 14.327 32 32 32h96c17.673 0 32-14.327 32-32v-8.74c32.495 0 100.687 40.747 177.455 40.726 5.505.003 37.65.03 41.013 0 59.282.014 92.255-35.887 90.335-89.793 15.127-17.727 22.539-43.337 18.225-67.105 12.456-19.526 15.126-47.07 9.628-69.405zM32 480V224h96v256H32zm424.017-203.648C472 288 472 336 450.41 347.017c13.522 22.76 1.352 53.216-15.015 61.996 8.293 52.54-18.961 70.606-57.212 70.974-3.312.03-37.247 0-40.727 0-72.929 0-134.742-40.727-177.455-40.727V235.625c37.708 0 72.305-67.939 106.183-101.818 30.545-30.545 20.363-81.454 40.727-101.817 50.909 0 50.909 35.517 50.909 61.091 0 42.189-30.545 61.09-30.545 101.817h111.999c22.73 0 40.627 20.364 40.727 40.727.099 20.363-8.001 36.375-23.984 40.727zM104 432c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z"/>
                    </svg>
                    <span class="text-gray-500">Like</span>
                </div>
            </a>
            <a class="flex justify-center items-center hover:bg-gray-100 rounded py-2 rounded-r-md cursor-pointer" x-on:click="open = !open; setTimeout(function() { document.getElementById('comment_text_{{ $post->id  }}').focus(); }, 100);">
                <svg class="text-center w-4 h-4 mr-2 fill-current text-gray-500 group-hover:text-gray-500 group-focus:text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                    <path d="M448 0H64C28.7 0 0 28.7 0 64v288c0 35.3 28.7 64 64 64h96v84c0 7.1 5.8 12 12 12 2.4 0 4.9-.7 7.1-2.4L304 416h144c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64zm32 352c0 17.6-14.4 32-32 32H293.3l-8.5 6.4L192 460v-76H64c-17.6 0-32-14.4-32-32V64c0-17.6 14.4-32 32-32h384c17.6 0 32 14.4 32 32v288zM280 240H136c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h144c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8zm96-96H136c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8h240c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8z"/>
                </svg>
                <span class="text-gray-500">Comment</span>
            </a>
        </div>
        <div class="flex-auto mt-2" x-cloak x-show="open">
            @if($post->allow_comments && $post->group->allow_members_to_comment)
                <form @submit.prevent="$wire.newComment(); open = false" id="comment_form_{{ $post->id  }}" class="flex">
                    <div class="mr-2 textarea-grow-wrap w-full bg-gray-100 rounded focus:ring-0 resize-none overflow-hidden">
                        <textarea id="comment_text_{{ $post->id  }}" wire:model.defer="newCommentContent" onInput="this.parentNode.dataset.replicatedValue = this.value"
                                  name="comment" placeholder="Write a comment..."
                                  x-on:blur="editing = false" x-on:focus="editing = true"
                                  class="w-full border border-gray-300 bg-gray-100 py-2 rounded placeholder-gray-400 focus:ring-0 resize-none overflow-hidden"
                                  rows="1"></textarea>
                    </div>
                    <button class="text-gray-600 border border-gray-400 px-3 py-2 rounded hover:bg-blue-600 hover:text-white bg-gray-100 cursor-pointer">Send</button>
                </form>
                <div x-cloak x-show="editing" class="mt-1 text-xs text-gray-400">
                    Press "Enter" to create new lines for paragraphs.
                </div>
                <span id="comment_error_box_{{ $post->id }}" class="hidden bg-red-100 text-red-600 py-1 px-2 rounded-b"></span>
            @else
                <div class="text-gray-600 bg-gray-200 rounded py-2 w-full text-center text-sm">Comments are disabled.</div>
            @endif
        </div>
    </div>
    @if($post->comments()->exists())
        <div class="flex flex-col mt-4" id="comments_{{ $post->id }}">
            @foreach($post->comments()->with(['creator', 'reactions'])->withReactionCounts()->orderBy('id', 'desc')->get() as $comment)
                <div class="flex comment">
                    @if($comment->creator && $comment->creator->avatar)
                        <img class="w-7 h-7 mt-1 rounded-full mr-2" src="{{ $comment->creator->avatar->getCdnUrl(512) }}"/>
                    @else
                        <svg class="w-7 h-7 mt-1 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    @endif
                    <div>
                        <div class="bg-gray-100 py-2 px-2 rounded-lg mt-1 mb-px">
                            <div class="text-xs font-semibold">
                                {{ $comment?->creator?->name }}
                            </div>
                            <div class="relative comment-content">
                                {!! nl2br((new VStelmakh\UrlHighlight\UrlHighlight())->highlightUrls($comment->content ?: '')) !!}

                                @if($comment->reactions_count)
                                    <div class="absolute flex -bottom-4 -right-4 bg-white rounded-full shadow-md border border-gray-100 {{ $comment->reactions_count > 1 ? 'pr-2' : null }}">
                                        @if($comment->likes_count)
                                            <img class="w-5 h-5 border-2 border-white rounded-full z-50" src="/static/app/img/reactions/like-128.png"/>
                                        @endif
                                        @if($comment->loves_count)
                                            <img class="w-5 h-5 border-2 border-white rounded-full {{ $comment->reactions_count > 1 ? '-ml-1.5' : null }} z-40" src="/static/app/img/reactions/love-128.png"/>
                                        @endif
                                        @if($comment->cares_count)
                                            <img class="w-5 h-5 border-2 border-white rounded-full {{ $comment->reactions_count > 1 ? '-ml-1.5' : null }} z-30" src="/static/app/img/reactions/care-128.png"/>
                                        @endif
                                        @if($comment->laughs_count)
                                            <img class="w-5 h-5 border-2 border-white rounded-full {{ $comment->reactions_count > 1 ? '-ml-1.5' : null }} z-20" src="/static/app/img/reactions/haha-128.png"/>
                                        @endif
                                        @if($comment->prays_count)
                                            <img class="w-5 h-5 border-2 border-white rounded-full {{ $comment->reactions_count > 1 ? '-ml-1.5' : null }} z-10" src="/static/app/img/reactions/pray-128.png"/>
                                        @endif
                                        @if($comment->reactions_count > 1)
                                            <div class="ml-1 text-sm text-gray-500">
                                                {{ $comment->reactions_count }}
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="flex items-center text-xs mb-1 mt-1">
                            @if(!$comment->reactions->contains('user_id', auth()->user()->id))
                                <a wire:click="likeComment({{ $comment->id }})" class="cursor-pointer font-semibold text-gray-400 mr-1 border border-gray-300 rounded px-1 py-0.5 hover:text-blue-500 hover:border-blue-500">
                                    Like
                                </a>
                            @else
                                <a wire:click="likeComment({{ $comment->id }}, 'delete')" class="cursor-pointer font-semibold text-blue-500 mr-1 bg-blue-50 border border-blue-300 rounded px-1 py-0.5">
                                    Like
                                </a>
                            @endif
                            <span class="font-light text-gray-500">
                            {{ $comment->getCreatedAtForHumansRelative(auth()->user()->account->timezone) }}
                        </span>
                            @if(auth()->user()->can('delete', $comment))
                                <span wire:click="deleteCommentMode('{{ $comment->id }}')" class="text-gray-400 mx-1 font-light cursor-pointer hover:text-red-500">
                                &middot; Delete
                            </span>
                            @endif
                        </div>
                        @if($this->delete_comment_id == $comment->id && auth()->user()->can('delete', $comment))
                            <div class="flex-1 pb-4">
                            <span class="p-2 bg-red-100 text-red-800 rounded-md text-sm">
                                Are you sure?
                                <button wire:click="deleteComment" type="button" class="inline-flex items-center px-3 py-1 border border-red-500 shadow-2xs text-xs leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Yes, Delete</button>
                                <button wire:click="deleteCommentMode()" type="button" class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-2xs text-xs leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Cancel</button>
                            </span>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>