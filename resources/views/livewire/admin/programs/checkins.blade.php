<div class="flex-1 divide-y divide-gray-200" x-data="{ openFilters: false }">
    <div class="py-6 px-4">
        <div class="flex flex-row justify-between">
                <h3 class="flex flex-row text-3xl font-medium leading-6 text-gray-900 my-auto">
                    Check-ins
                </h3>
                <span class="text-base my-auto font-medium border border-green-600 bg-green-50 text-green-600 rounded-md px-2 py-1">{{ $totalCheckedIn }} checked in</span>
        </div>
    </div>

    <div class="sm:flex-row sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live.debounce.300ms="query_term" autocomplete="off" class="w-full pl-10 pr-3 py-2 rounded-md text-sm bg-white border border-gray-400 text-gray-700 focus:text-gray-900" placeholder="Search users..." type="search">
                    <div wire:loading.flex wire:target="query_term" class="hidden absolute inset-y-0 right-0 pt-1.5 pr-1.5">
                        <flux:icon.loading/>
                    </div>
                </div>
                <div class="relative z-0 inline-flex shadow-2xs rounded-md gap-x-2">
                    <button x-on:click="openFilters = !openFilters" type="button" class="{{ count($groups_filter) > 0 || $show_checked_in_only ? 'bg-purple-600 text-white' : 'bg-white text-gray-600' }} cursor-pointer admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border border-gray-400 text-sm font-medium">
                        <x-heroicon-o-funnel class="-ml-1 mr-2 h-5 w-5 {{ count($groups_filter) > 0 || $show_checked_in_only ? 'text-white' : 'text-gray-600' }}"/>
                        Filters
                        @if(count($groups_filter) > 0 || $show_checked_in_only)
                            <span class="font-semibold {{ count($groups_filter) > 0 || $show_checked_in_only ? 'text-white' : 'text-gray-600' }}">&nbsp;({{ count($groups_filter) + ($show_checked_in_only ? 1 : 0) }})</span>
                        @endif
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4 my-4 text-sm" x-show="openFilters" x-cloak>
            <div class="flex-1">
                <label for="">Filter Options:</label>
                <div class="flex flex-col space-y-0.5 mt-1">
                    <flux:checkbox wire:model.live="show_checked_in_only"
                                   label="Show checked in only"
                                   value="1"
                    />
                </div>
            </div>
            <div class="flex-1">
                <label for="">Groups:</label>
                <div class="flex flex-col space-y-0.5 mt-1 max-h-48 overflow-y-auto">
                    @foreach($groups as $group)
                        <flux:checkbox wire:model.live="groups_filter"
                                       label="{{ $group->name }} ({{ $group->current_checkins_count }}/{{ $group->users_count }})"
                                       value="{{ $group->id }}"
                        />
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        @if(count($groups_filter) === 0 && !$show_checked_in_only)
            {{-- Group by program groups when no filters --}}
            @php
                $groupedUsers = $users->groupBy(function($user) {
                    return $user->groups->pluck('id')->sort()->implode('-') ?: 'no-group';
                });
            @endphp
            
            @foreach($groups as $group)
                @php
                    $groupUsers = collect();
                    foreach($groupedUsers as $key => $userGroup) {
                        if(str_contains($key, $group->id)) {
                            $groupUsers = $groupUsers->merge($userGroup);
                        }
                    }
                @endphp
                
                @if($groupUsers->count() > 0)
                    <div class="">
                        <div class="bg-gray-100 px-4 py-2 border-t border-b border-gray-300">
                            <div class="flex flex-row justify-between">
                                <h4 class="text-lg font-medium text-gray-900">
                                    {{ $group->name }}
                                </h4>
                                <span class="text-sm text-gray-800 float-right my-auto">{{ $group->current_checkins_count }} of {{ $groupUsers->count() }} checked in</span>
                            </div>
                        </div>
                        
                        <table class="min-w-full divide-y divide-gray-300 border-collapse">
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($groupUsers->sortBy('last_name') as $user)
                                    @include('livewire.admin.programs.partials.checkin-user-row', ['user' => $user])
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            @endforeach
            
            {{-- Users without groups --}}
            @if(isset($groupedUsers['no-group']) && $groupedUsers['no-group']->count() > 0)
                <div class="border-t border-gray-200">
                    <div class="bg-gray-100 px-4 py-2">
                        <h4 class="text-lg font-medium text-gray-900">No Group Assigned</h4>
                    </div>
                    
                    <table class="min-w-full divide-y divide-gray-300 border-collapse">
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($groupedUsers['no-group']->sortBy('last_name')->sortBy('first_name') as $user)
                                @include('livewire.admin.programs.partials.checkin-user-row', ['user' => $user])
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        @else
            {{-- Filtered view - just show users in a table --}}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-300 border-collapse">
                    <thead class="bg-gray-700 border-b border-gray-300 text-gray-200">
                        <tr>
                            <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Group</th>
                            <th scope="col" class="py-2 px-2 text-xs text-center font-semibold">Status</th>
                            <th scope="col" class="py-2 px-2 pr-4 text-xs text-center font-semibold">Action</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($users as $user)
                            @include('livewire.admin.programs.partials.checkin-user-row', ['user' => $user])
                        @empty
                            <tr>
                                <td colspan="4" class="text-center p-5">
                                    <span class="">No users found.</span>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        @endif
        
        <div class="border-t border-gray-200 p-4">
            {{ $users->withQueryString()->links() }}
        </div>
    </div>

    @push('scripts')

        {{-- Pusher Scripts --}}
        <script type="text/javascript" defer>
            document.addEventListener('DOMContentLoaded', function() {
                var pusher = new Pusher('{{ config('broadcasting.connections.pusher.key') }}', {
                cluster: '{{ config('broadcasting.connections.pusher.options.cluster') }}',
                authEndpoint: '/broadcasting/auth',
                forceTLS: true,
                disableStats: true,
            });
                // Subscribe to the program checkins channel
                var channelProgramCheckins = pusher.subscribe('private-{{ auth()->user()->account_id }}.programs.{{ $program->id }}.checkins');
                
                // Listen for checkin events
                channelProgramCheckins.bind('user.checkin', function(data) {
                    console.log('User checked in:', data);
                    @this.dispatch('userCheckedIn', { userId: data.program_user_id });
                });
                
                channelProgramCheckins.bind('user.checkout', function(data) {
                    console.log('User checked out:', data);
                    @this.dispatch('userCheckedOut', { userId: data.program_user_id });
                });
            });
        </script>
    @endpush
</div>