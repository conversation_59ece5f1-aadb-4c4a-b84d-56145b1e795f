<div class="flex-1 divide-y divide-gray-200" x-data="{ openFilters: false }">
    <div class="py-6 px-4">
        <div class="flex flex-row justify-between">
            <h3 class="flex flex-row text-3xl font-medium leading-6 text-gray-900 my-auto">
                {{ $registrants_only ? 'Registrations' : 'All Users' }}
            </h3>

            <div>
                <a href="{{ route('admin.programs.users.create', [$program]) }}" class="admin-button-blue">
                    Add User
                </a>
            </div>
        </div>
    </div>

    <div class="sm:flex-row sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live="query_term" autocomplete="off" class=" w-full pl-10 pr-3 py-2 rounded-md text-sm bg-white text-gray-700 focus:text-gray-900" placeholder="Search..." type="search">
                    <div wire:loading class="hidden absolute inset-y-0 right-0 pt-1.5 pr-1.5">
                        <flux:icon.loading/>
                    </div>
                </div>
                <div class="relative z-0 inline-flex shadow-2xs rounded-md gap-x-2">
                    <button x-on:click="openFilters = !openFilters" type="button" class="{{ $filter_count > 0 ? 'bg-purple-600 text-white' : 'bg-white text-gray-600' }} cursor-pointer admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border  text-sm font-medium">
                        <x-heroicon-o-funnel class="-ml-1 mr-2 h-5 w-5 {{ $filter_count > 0 ? 'text-white' : 'text-gray-600' }}"/>
                        Filters
                        @if($filter_count > 0)
                            <span class="font-semibold {{ $filter_count > 0 ? 'text-white' : 'text-gray-600' }}">&nbsp;({{ $filter_count }})</span>
                        @endif
                    </button>
                    <button wire:click="downloadCSV" type="button" class="bg-white text-gray-600 cursor-pointer admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border  text-sm font-medium">
                        CSV
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-x-4 my-4 text-sm" x-show="openFilters" x-cloak>
            <div class="flex-1">
                <label for="">Roles to include:</label>
                <div class="flex flex-col space-y-0.5 mt-1">
                    @foreach (\App\Programs\ProgramUser::$family_roles as $key => $value)
                        <flux:checkbox wire:model.live="family_roles"
                                       label="{{ $value }}"
                                       value="{{ $key }}"
                        />
                    @endforeach
                </div>
            </div>
            <div class="flex-1">
                <label for="">Column Checks:</label>
                <div class="flex flex-col space-y-0.5 mt-1">
                    <flux:checkbox wire:model.live="columns"
                                   label="Registered Users"
                                   value="is_registered"
                    />
                    <flux:checkbox wire:model.live="columns"
                                   label="Contacts Only"
                                   value="is_contact"
                    />
                </div>
            </div>
            <div class="flex-1">
                <label for="">Groups:</label>
                <div class="flex flex-col space-y-0.5 mt-1">
                    @foreach($program->groups as $group)
                        <flux:checkbox wire:model.live="groups_filter"
                                       label="{{ $group->name }}"
                                       value="{{ $group->id }}"
                        />
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        <div class="overflow-x-auto">
            <div class="overflow-hidden overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-300 border-collapse">
                    <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                    <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                        <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
                        <th scope="col" class="w-1/3 text-right"></th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($users as $user)
                        <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                            <td class="py-3 px-2 pl-4 whitespace-nowrap text-gray-900">
                                <a href="{{ route('admin.programs.users.edit', [$program, $user]) }}">
                                    {{ $user->last_name }}, {{ $user->first_name }}
                                </a>
                                @if($user->groups()->count() > 0)
                                    <br>
                                    @foreach($user->groups as $group)
                                        <span class="bg-blue-500 text-white rounded-sm px-2 py-0.5 text-xs">{{ $group->name }}</span>
                                    @endforeach
                                @endif
                            </td>
                            <td class="pr-4 text-right">
                                <div class="flex flex-wrap gap-1 justify-end">
                                    @if($user->currentCheckin)
                                        <span class="bg-green-500 text-white rounded py-0.5 px-2 text-xs">Checked In</span>
                                    @endif
                                    @if($user->hasAllergies())
                                        <span class="bg-orange-500 text-white rounded py-0.5 px-2 text-xs">Allergies</span>
                                    @endif
                                    @if($user->isSpecialNeeds())
                                        <span class="bg-purple-500 text-white rounded py-0.5 px-2 text-xs">Special Needs</span>
                                    @endif
                                    @if($user->isRegisteredUser())
                                        <span class="bg-blue-500 text-white rounded py-0.5 px-2 text-xs">
                                            Registered
                                        </span>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="100%" class="text-center p-5">
                                <span class="">No results found.</span>
                            </td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>
            <div class="border-t border-gray-200 p-4">
                {{ $users->withQueryString()->links() }}
            </div>
        </div>
    </div>


    <script>

    </script>
</div>