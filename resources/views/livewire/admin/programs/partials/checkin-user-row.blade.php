<tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
    <td class="py-1 px-2 pl-4 whitespace-nowrap text-gray-900">
        <a href="{{ route('admin.programs.users.edit', [$program, $user]) }}" class="hover:underline">
            {{ $user->preferred_first_name ? $user->preferred_first_name : $user->first_name }} {{ $user->last_name }}
        </a>
        <div class="flex flex-wrap gap-1 mt-1">
            @if($user->hasAllergies())
                <span class="bg-orange-500 text-white rounded py-0.5 px-2 text-xs">Allergies</span>
            @endif
            @if($user->isSpecialNeeds())
                <span class="bg-purple-500 text-white rounded py-0.5 px-2 text-xs">Special Needs</span>
            @endif
        </div>
    </td>
    @if(isset($groupedUsers))
        {{-- Don't show group column when grouped view --}}
    @else
        <td class="py-1 px-2 whitespace-nowrap text-gray-700 text-sm">
            @foreach($user->groups as $group)
                <span class="bg-blue-500 text-white rounded-sm px-2 py-0.5 text-xs">{{ $group->name }}</span>
            @endforeach
        </td>
    @endif
    <td class="py-1 px-2 text-right w-1/4">
        @if($user->currentCheckin)
            <div class="flex flex-row items-center gap-2 justify-end">
                <div class="text-xs text-gray-500">
                    {{ $user->currentCheckin->checkin_at->format('g:i A') }}
                </div>
                <span class="bg-green-500 text-white rounded py-1 px-3 text-sm font-medium">
                    Checked In
                </span>
            </div>
        @else
            <span class="bg-gray-50 text-gray-300 border border-gray-300 rounded py-1 px-3 text-sm">
                Checked In
            </span>
        @endif
    </td>
    <td class="py-1 px-2 pr-4 text-right w-40">
        <button 
            wire:click="toggleCheckin({{ $user->id }})" 
            wire:loading.attr="disabled"
            wire:loading.class="opacity-50 cursor-not-allowed"
            wire:target="toggleCheckin({{ $user->id }})"
            class="inline-flex items-center px-3 py-1.5 border text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 cursor-pointer focus:ring-offset-2 {{ $user->currentCheckin ? 'border-red-300 text-red-700 bg-white hover:bg-red-50 focus:ring-red-500' : 'border-green-300 text-green-700 bg-white hover:bg-green-50 focus:ring-green-500' }}"
        >
            <span wire:loading.class="hidden" wire:target="toggleCheckin({{ $user->id }})">
                @if($user->currentCheckin)
                    <x-heroicon-o-arrow-right-on-rectangle class="h-4 w-4 mr-1 inline"/>
                    Check Out
                @else
                    <x-heroicon-o-arrow-left-on-rectangle class="h-4 w-4 mr-1 inline"/>
                    Check In
                @endif
            </span>
            <span wire:loading.flex wire:target="toggleCheckin({{ $user->id }})" class="hidden items-center">
                <flux:icon.loading class="h-4 w-4 inline"/>
            </span>
        </button>
    </td>
</tr>