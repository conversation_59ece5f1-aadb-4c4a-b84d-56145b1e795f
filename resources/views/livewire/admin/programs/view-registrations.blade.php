<div class="flex-1 divide-y divide-gray-200" x-data="{ openFilters: false }">
    <div class="py-6 px-4">
        <div class="flex flex-row justify-between">
            <h3 class="flex flex-row text-3xl font-medium leading-6 text-gray-900 my-auto">
                Registrations
            </h3>

            <div>
                <a href="{{ route('admin.programs.users.create', [$program]) }}" class="admin-button-blue">
                    Add User
                </a>
            </div>
        </div>
    </div>

    {{-- ADD USER DIV --}}
    <div class="{{ $add_users ? '' : 'hidden' }} bg-gray-500">
        <div class="py-4 px-4 md:px-12 bg-blue-50">
            <h3 class="font-semibold">
                Search Users to Add
            </h3>
            <div class="flex-1 relative rounded-md">
                <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <input wire:model.live="add_user_query_term" autocomplete="off" class="standard-input admin-border-color w-full pl-10 pr-3 py-2 rounded-md text-sm font-light bg-white text-gray-700 focus:text-gray-900" placeholder="Type to search..." type="search">
                <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                    <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                        <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                        Loading
                    </kbd>
                </div>
            </div>
        </div>
        <div class="py-4"></div>
    </div>

    <div class="sm:flex-row sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live="query_term" autocomplete="off" class="admin-border-color w-full pl-10 pr-3 py-2 rounded-md text-sm bg-white text-gray-700 focus:text-gray-900" placeholder="Search..." type="search">
                    <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                        <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                            <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                            Loading
                        </kbd>
                    </div>
                </div>
                <div class="relative z-0 inline-flex shadow-2xs rounded-md">
                    <button x-on:click="openFilters = !openFilters" type="button" class="{{ $filter_count > 0 ? 'bg-purple-600 text-white' : 'bg-white text-gray-600' }} admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color text-sm font-medium">
                        <x-heroicon-o-funnel class="-ml-1 mr-2 h-5 w-5 {{ $filter_count > 0 ? 'text-white' : 'text-gray-600' }}"/>
                        Filters
                        @if($filter_count > 0)
                            <span class="font-semibold {{ $filter_count > 0 ? 'text-white' : 'text-gray-600' }}">&nbsp;({{ $filter_count }})</span>
                        @endif
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-x-4 mx-4 mb-4 mt-4 text-sm" x-show="openFilters" x-cloak>
            <div class="flex-1">
                <label for="">Roles to include:</label>
                <hr class="my-1"/>
                @foreach (\App\Programs\ProgramUser::$family_roles as $key => $value)
                    <div class="form-check form-check-inline">
                        <input wire:model.live="family_roles" id="family_roles[{{ $key }}]" type="checkbox" value="{{ $key }}"/>
                        <label for="family_roles[{{ $key }}]">{{ $value }}</label>
                    </div>
                @endforeach
            </div>
            <div class="flex-1">
                <label for="">Column Checks:</label>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[is_registered]" type="checkbox" value="is_registered" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_registered'))>
                    <label for="columns[is_registered]">Registered Users</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[is_contact]" type="checkbox" value="is_contact" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_contact'))>
                    <label for="columns[is_contact]">Contacts Only</label>
                </div>
            </div>
            <div class="flex-1">
                <label for="">Groups:</label>
                @foreach($program->groups as $group)
                    <hr class="my-1"/>
                    <div class="form-check form-check-inline">
                        <input wire:model.live="groups_filter" id="columns[groups][{{ $group->id }}]" type="checkbox" value="{{ $group->id }}" @isChecked(request()->has('groups_filter') && in_array($group->id, request()->get('groups_filter'))))>
                        <label for="columns[groups][{{ $group->id }}]">{{ $group->name }}</label>
                        <span class="float-right text-gray-500">({{ $group->users()->count() }})</span>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        <div class="overflow-x-auto">
            <div class="overflow-hidden overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-300 border-collapse">
                    <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                    <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                        <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
                        <th scope="col" class="w-1/3 text-right"></th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($users as $user)
                        <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                            <td class="py-3 px-2 pl-4 whitespace-nowrap text-gray-900">
                                <a href="{{ route('admin.programs.users.edit', [$program, $user]) }}">
                                    {{ $user->last_name }}, {{ $user->first_name }}
                                </a>
                            </td>
                            <td class="pr-4 text-right">
                                <div class="flex-row space-x-2">
                                    @if($user->currentCheckin)
                                        <span class="mr-1 bg-green-500 text-white rounded my-auto py-0.5 px-2 text-sm">Checked In</span>
                                    @endif
                                    @if($user->hasAllergies())
                                        <span class="bg-orange-500 text-white rounded my-auto py-0.5 px-2 text-sm">Allergies</span>
                                    @endif
                                    @if($user->isSpecialNeeds())
                                        <span class="bg-purple-500 text-white rounded my-auto py-0.5 px-2 text-sm">Special Needs</span>
                                    @endif
                                    @if($user->isRegisteredUser())
                                        <span class="bg-blue-500 text-white rounded my-auto py-0.5 px-2 text-sm">
                                            Registered
                                        </span>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="100%" class="text-center p-5">
                                <span class="">No results found.</span>
                            </td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>
            <div class="border-t admin-border-color p-4">
                {{ $users->withQueryString()->links() }}
            </div>
        </div>
    </div>


    <script>

    </script>
</div>