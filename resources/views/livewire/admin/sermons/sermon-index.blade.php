<div class="admin-section mt-4" x-data="{ openFilters: false }">

    <div class="sm:flex sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live="query_term" wire:keydown="$dispatch('queryChanged')" autocomplete="off" class="standard-input admin-border-color w-full pl-10 pr-3 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900" placeholder="Search..." type="search">
                    <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                        <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                            <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                            Loading
                        </kbd>
                    </div>
                </div>
                <div class="hidden relative z-0 inline-flex shadow-2xs rounded-md">
                    <button wire:click="initExcelDownload" type="button" class="admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-arrow-down-tray class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        <div class="overflow-x-auto">
            <div class="min-w-full">
                <div class="border-t admin-border-color px-4 py-2">
                    {{ $sermons->withQueryString()->onEachSide(1)->links() }}
                </div>
                <div class="overflow-hidden overflow-x-auto">
                    <table class="table-auto min-w-full divide-y divide-gray-300 border-collapse">
                        <thead class="sticky top-0 bg-white bg-opacity-90 uppercase">
                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                            <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold text-white">Title</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold text-white">Speaker</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold text-white">Date</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold text-white">Files</th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($sermons as $sermon)
                            <tr class="overflow-hidden bg-white">
                                <td class="py-3 flex flex-row justify-between my-auto pl-4 whitespace-nowrap font-medium text-gray-900">
                                    <a href="{{ route('admin.sermons.edit', $sermon) }}">
                                        {{ $sermon->title }}
                                    </a>
                                    <div class="flex space-x-1">
                                        @if($sermon->isHidden())
                                            <span class="bg-gray-50 border border-gray-300 text-gray-500 rounded text-sm px-2 py-0 pull-right">hidden</span>
                                        @endif
                                        @if($sermon->files->count() == 0)
                                            <span class="bg-amber-50 border border-amber-300 text-amber-500 rounded text-sm px-2 py-0 pull-right">No file</span>
                                        @endif
                                        @if($sermon->podcasts()->exists())
                                            <span class="bg-purple-50 border border-purple-300 text-purple-500 rounded text-sm px-2 py-0 pull-right">Podcast</span>
                                        @endif
                                    </div>
                                </td>
                                <td class="py-3 pl-2 whitespace-nowrap font-medium text-gray-900">
                                    {{ $sermon->speaker }}
                                </td>
                                <td class="py-3 pl-2 whitespace-nowrap font-medium text-gray-900">
                                    {{ $sermon->date_sermon->format('M d, Y') }}
                                </td>
                                <td class="py-3 pl-2 whitespace-nowrap font-medium text-gray-900">
                                    {{ $sermon->files->count() }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-center p-5">
                                    <span class="">No results found.</span>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="border-t admin-border-color p-4">
                {{ $sermons->withQueryString()->onEachSide(1)->links() }}
            </div>
        </div>
    </div>

</div>