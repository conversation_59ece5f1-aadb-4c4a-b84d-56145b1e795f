<div>

    <div class="admin-standard-col-width">

        <div class="mb-4 md:flex-1 md:justify-between">
            <div class="flex flex-row justify-between">
                <h1>
                    Involvement Areas
                </h1>
                <div>
                    <a class="admin-button-transparent" href="{{ route('app.involvement.selections') }}" target="_blank">
                        View Volunteers
                        <i class="fas fa-external-link ml-2 text-gray-500"></i>
                    </a>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="overflow-hidden">
                <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">
                    <aside class="lg:col-span-3">
                        <div class="flex px-3 py-3 align-middle justify-between border-b admin-border-color">
                            <h2 class="text-lg font-medium text-gray-900">
                                Categories
                            </h2>
                            <button type="button" class="admin-button-blue-small" onclick="openSidebar('{{ route('admin.involvement.categories.create') }}')">
                                <x-heroicon-s-plus class="w-4 mr-1"/>
                                New
                            </button>
                        </div>
                        <nav id="categories-list" class="space-y-1">
                            @php
                                $cat_active = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-1 py-2 flex items-center text-base font-medium';
                                $cat_normal = 'cursor-pointer border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-1 py-2 flex items-center text-base font-medium';
                            @endphp
                            @forelse($categories as $category)
                                <a href="{{ route('admin.involvement.categories.view', $category) }}" data-categoryid="{{ $category->id }}" data-sortid="{{ $category->sort_id }}" class="{{ $selected_category->id == $category->id ? $cat_active : $cat_normal }}" wire2:click="selectCategory({{ $category->id }})">
                                    <x-heroicon-s-bars-3 class="w-3 h-5 handle mr-1 cursor-move text-gray-500"/>
                                    <span class="truncate">{{ $category->name }}</span>
                                </a>
                            @empty
                                <div class="flex-1 text-center text-sm text-gray-400 mt-4">
                                    <span class="mx-auto border border-gray-300 rounded px-4 py-1">No categories yet!</span>
                                </div>
                            @endforelse
                        </nav>
                    </aside>

                    <div class="divide-y divide-gray-200 lg:col-span-9">
                        @if($selected_category)
                            <!-- Profile section -->
                            <div class="py-6 lg:pb-8">
                                <div class="px-6">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            @if($edit_category?->id == $selected_category->id)
                                                <form wire:submit.prevent="saveEditCategory" class="bg-yellow-100 px-6 py-2 rounded">
                                                    <div class="flex justify-between py-4">
                                                        <div class="w-3/5">
                                                            <input type="text" name="name" placeholder="Name" wire:model="edit_category.name" autocomplete="off"/>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="ml-8">
                                                            <label><strong>Restrictions</strong></label>
                                                            <div class="custom-control custom-switch">
                                                                <input name="men_only" wire:model="edit_category.men_only" value="1" type="checkbox" id="men_only_{{ $edit_category->id }}" @isChecked($edit_category->men_only)/>
                                                                <label for="men_only_{{ $edit_category->id }}">Show for <strong>men</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="women_only" wire:model="edit_category.women_only" value="1" type="checkbox" id="women_only_{{ $edit_category->id }}" @isChecked($edit_category->women_only)/>
                                                                <label for="women_only_{{ $edit_category->id }}">Show for <strong>women</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="baptized_only" wire:model="edit_category.baptized_only" value="1" type="checkbox" id="baptized_only_{{ $edit_category->id }}" @isChecked($edit_category->baptized_only)/>
                                                                <label for="baptized_only_{{ $edit_category->id }}">Show for <strong>baptized users only</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="approved_to_teach_only" wire:model="edit_category.approved_to_teach_only" value="1" type="checkbox" id="approved_to_teach_only_{{ $edit_category->id }}" @isChecked($edit_category->approved_to_teach_only)/>
                                                                <label for="approved_to_teach_only_{{ $edit_category->id }}">Show for <strong>"approved to teach" users only</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="completed_background_check_only" wire:model="edit_category.completed_background_check_only" value="1" type="checkbox" id="completed_background_check_only_{{ $edit_category->id }}" @isChecked($edit_category->completed_background_check_only)/>
                                                                <label for="completed_background_check_only_{{ $edit_category->id }}">Show for <strong>"completed a background check" users only</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="is_hidden" wire:model="edit_category.is_hidden" value="1" type="checkbox" id="is_hidden_{{ $edit_category->id }}" @isChecked($edit_category->is_hidden)/>
                                                                <label for="is_hidden_{{ $edit_category->id }}">Is Hidden (from members)</label>
                                                            </div>
                                                        </div>
                                                        <div class="flex justify-between py-4">
                                                            <div class="">
                                                                <button type="submit" class="admin-button-blue">
                                                                    <x-heroicon-s-check class="w-3 mr-1"/>
                                                                    <span>Save Category</span>
                                                                </button>
                                                                <button type="button" class="admin-button-transparent" wire:click="cancelEditCategory">
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                            <div>
                                                                <button type="button" class="admin-button-red-transparent" onclick="document.getElementById('delete_selected_category').submit();">Delete</button>
                                                            </div>
                                                        </div>
                                                        <div class="alert alert-info pb-2" role="alert">
                                                            <strong>Note:</strong> Selecting a restriction will remove users' selections for this area and subareas! This can not be undone.
                                                            <br>
                                                            <small>For example: Selecting "baptized users only" will remove all unbaptized users that have selected anything in this area.</small>
                                                        </div>
                                                    </div>
                                                </form>
                                                <form method="post" id="delete_selected_category" action="{{ route('admin.involvement.categories.destroy', $selected_category) }}">
                                                    @method('delete')
                                                    @csrf
                                                </form>
                                            @else
                                                <h2 class="text-3xl leading-6 font-bold text-gray-900">{{ $selected_category?->name }}</h2>
                                                <div class="flex items-center justify-between py-3">
                                                    <div class="flex-1">
                                                        <div class="flex justify-between">
                                                            <div class="shrink-0 flex items-center text-sm">
                                                                @if(!$selected_category->isMenAndWomen())
                                                                    @if($selected_category->isMenOnly())
                                                                        <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-primary">Men Only</span>
                                                                    @elseif($selected_category->isWomenOnly())
                                                                        <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-primary">Women Only</span>
                                                                    @endif
                                                                @endif
                                                                @if($selected_category->isBaptizedOnly())
                                                                    <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Baptized Only</span>
                                                                @endif
                                                                @if($selected_category->approved_to_teach_only)
                                                                    <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Teachers Only</span>
                                                                @endif
                                                                @if($selected_category->completed_background_check_only)
                                                                    <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Background ✅</span>
                                                                @endif
                                                                @if($selected_category->is_hidden)
                                                                    <span class="px-1 py-0.5 border border-blue-400 bg-red-100 rounded mr-2 badge-info">Hidden 🚫</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="flex">
                                            @if(!$edit_category)
                                                <button type="button" class="admin-button-transparent my-auto" wire:click="editCategory({{ $selected_category->id }})">
                                                    <x-heroicon-s-pencil-square class="w-4 mr-1"/>
                                                    Edit Category
                                                </button>
                                                <button type="button" class="ml-2 admin-button-blue my-auto" onclick="openSidebar('{{ route('admin.involvement.areas.create', $selected_category) }}')">
                                                    <x-heroicon-s-plus class="w-4 mr-1"/>
                                                    Area
                                                </button>
                                            @endif
                                        </div>
                                    </div>

                                    <div>
                                        <h2 class="mt-4 mb-2 text-xl leading-6 font-medium text-gray-900">Areas &amp; Subareas</h2>
                                    </div>
                                </div>

                                <div class="border-t border-gray-300 overflow-hidden">
                                    <ul role="list" class="divide-y divide-gray-200" id="areas-list">
                                        @forelse($selected_category?->areas as $area)
                                            @if($edit_area?->id == $area->id)
                                                <form wire:submit.prevent="saveEditArea" class="bg-yellow-100 px-6 py-2 rounded">
                                                    <div class="flex justify-between py-4">
                                                        <div class="w-3/5">
                                                            <input type="text" name="name" placeholder="Name" wire:model="edit_area.name" autocomplete="off"/>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="ml-8">
                                                            <label><strong>Restrictions</strong></label>
                                                            <div class="custom-control custom-switch">
                                                                <input name="men_only" wire:model="edit_area.men_only" value="1" type="checkbox" id="men_only_{{ $edit_area->id }}" @isChecked($edit_area->men_only)/>
                                                                <label for="men_only_{{ $edit_area->id }}">Show for <strong>men</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="women_only" wire:model="edit_area.women_only" value="1" type="checkbox" id="women_only_{{ $edit_area->id }}" @isChecked($edit_area->women_only)/>
                                                                <label for="women_only_{{ $edit_area->id }}">Show for <strong>women</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="baptized_only" wire:model="edit_area.baptized_only" value="1" type="checkbox" id="baptized_only_{{ $edit_area->id }}" @isChecked($edit_area->baptized_only)/>
                                                                <label for="baptized_only_{{ $edit_area->id }}">Show for <strong>baptized users only</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="approved_to_teach_only" wire:model="edit_area.approved_to_teach_only" value="1" type="checkbox" id="approved_to_teach_only_{{ $edit_area->id }}" @isChecked($edit_area->approved_to_teach_only)/>
                                                                <label for="approved_to_teach_only_{{ $edit_area->id }}">Show for <strong>"approved to teach" users only</strong></label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="completed_background_check_only" wire:model="edit_area.completed_background_check_only" value="1" type="checkbox" id="completed_background_check_only_{{ $edit_area->id }}" @isChecked($edit_area->completed_background_check_only)/>
                                                                <label for="completed_background_check_only_{{ $edit_area->id }}">Show for <strong>"completed a background check" users only</strong></label>
                                                            </div>
                                                            <label><strong>Options</strong></label>
                                                            <div class="custom-control custom-switch">
                                                                <input name="is_hidden" wire:model="edit_area.is_hidden" value="1" type="checkbox" id="is_hidden_{{ $edit_area->id }}" @isChecked($edit_area->is_hidden)/>
                                                                <label for="is_hidden_{{ $edit_area->id }}">Is Hidden (from members)</label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="auto_approve_for_assignments" wire:model="edit_area.auto_approve_for_assignments" value="1" type="checkbox" id="auto_approve_for_assignments_{{ $edit_area->id }}" @isChecked($edit_area->auto_approve_for_assignments)/>
                                                                <label for="auto_approve_for_assignments_{{ $edit_area->id }}">Automatically Approve for Assignments</label>
                                                            </div>
                                                            <div class="custom-control custom-switch">
                                                                <input name="auto_show_in_volunteer_list" wire:model="edit_area.auto_show_in_volunteer_list" value="1" type="checkbox" id="auto_show_in_volunteer_list_{{ $edit_area->id }}" @isChecked($edit_area->auto_show_in_volunteer_list)/>
                                                                <label for="auto_show_in_volunteer_list_{{ $edit_area->id }}">Automatically Show in Volunteers List</label>
                                                            </div>
                                                        </div>
                                                        <div class="flex justify-between py-4">
                                                            <div class="">
                                                                <button type="submit" class="admin-button-blue">
                                                                    <x-heroicon-s-check class="w-3 mr-1"/>
                                                                    <span>Save Area</span>
                                                                </button>
                                                                <button type="button" class="admin-button-transparent" wire:click="cancelEditArea">
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                            <div>
                                                                <button type="button" wire:click="deleteArea" class="admin-button-red-transparent">Delete</button>
                                                            </div>
                                                        </div>
                                                        <div class="alert alert-info pb-2" role="alert">
                                                            <strong>Note:</strong> Selecting a restriction will remove users' selections for this area and subareas! This can not be undone.
                                                            <br>
                                                            <small>For example: Selecting "baptized users only" will remove all unbaptized users that have selected anything in this area.</small>
                                                        </div>
                                                    </div>
                                                </form>
                                            @else
                                                <li data-areaid="{{ $area->id }}" data-sortid="{{ $area->sort_id }}">
                                                    <div>
                                                        <div class="flex items-center justify-between hover:bg-gray-50 px-4 py-3 sm:px-6 cursor-pointer">
                                                            <div class="flex-1" wire:click="editArea({{ $area->id }})">
                                                                <div class="flex justify-between">
                                                                    <div class="flex font-medium text-gray-800 items-center truncate">
                                                                        <x-heroicon-s-bars-3 class="w-3 handle mr-2 cursor-move text-gray-500"/>
                                                                        {{ $area->name }}
                                                                    </div>
                                                                    <div class="shrink-0 flex items-center text-xs">
                                                                        @if(!$area->isMenAndWomen())
                                                                            @if($area->isMenOnly())
                                                                                <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-primary">Men Only</span>
                                                                            @elseif($area->isWomenOnly())
                                                                                <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-primary">Women Only</span>
                                                                            @endif
                                                                        @endif
                                                                        @if($area->isBaptizedOnly())
                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Baptized Only</span>
                                                                        @endif
                                                                        @if($area->approved_to_teach_only)
                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Teachers Only</span>
                                                                        @endif
                                                                        @if($area->completed_background_check_only)
                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Background ✅</span>
                                                                        @endif
                                                                        @if($area->is_hidden)
                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-red-100 rounded mr-2 badge-info">Hidden 🚫</span>
                                                                        @endif
                                                                        @if(!$area->auto_approve_for_assignments)
                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-red-100 rounded mr-2 badge-info">🚫Assignments</span>
                                                                        @endif
                                                                        @if(!$area->auto_show_in_volunteer_list)
                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-red-100 rounded mr-2 badge-info">🚫Volunteer List</span>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <button type="button" class="admin-button-blue-small text-xs my-auto" onclick="openSidebar('{{ route('admin.involvement.subareas.create', [$selected_category, $area]) }}')">
                                                                <x-heroicon-s-plus class="w-4 mr-1"/>
                                                                Subarea
                                                            </button>
                                                        </div>

                                                        @foreach($area->subareas as $subarea)
                                                            <ul role="list" class="divide-y divide-gray-200 border-t border-gray-200">
                                                                @if($edit_subarea?->id == $subarea->id)
                                                                    <form wire:submit.prevent="saveEditSubarea" class="bg-yellow-100 px-6 py-2 rounded">
                                                                        <div class="flex justify-between py-4">
                                                                            <div class="w-3/5">
                                                                                <input type="text" name="name" placeholder="Name" wire:model="edit_subarea.name" autocomplete="off"/>
                                                                            </div>
                                                                        </div>
                                                                        <div>
                                                                            <div class="ml-8">
                                                                                <label><strong>Restrictions</strong></label>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="men_only" wire:model="edit_subarea.men_only" value="1" type="checkbox" id="men_only_{{ $edit_subarea->id }}" @isChecked($edit_subarea->men_only)/>
                                                                                    <label for="men_only_{{ $edit_subarea->id }}">Show for <strong>men</strong></label>
                                                                                </div>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="women_only" wire:model="edit_subarea.women_only" value="1" type="checkbox" id="women_only_{{ $edit_subarea->id }}" @isChecked($edit_subarea->women_only)/>
                                                                                    <label for="women_only_{{ $edit_subarea->id }}">Show for <strong>women</strong></label>
                                                                                </div>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="baptized_only" wire:model="edit_subarea.baptized_only" value="1" type="checkbox" id="baptized_only_{{ $edit_subarea->id }}" @isChecked($edit_subarea->baptized_only)/>
                                                                                    <label for="baptized_only_{{ $edit_subarea->id }}">Show for <strong>baptized users only</strong></label>
                                                                                </div>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="approved_to_teach_only" wire:model="edit_subarea.approved_to_teach_only" value="1" type="checkbox" id="approved_to_teach_only_{{ $edit_subarea->id }}" @isChecked($edit_subarea->approved_to_teach_only)/>
                                                                                    <label for="approved_to_teach_only_{{ $edit_subarea->id }}">Show for <strong>"approved to teach" users only</strong></label>
                                                                                </div>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="completed_background_check_only" wire:model="edit_subarea.completed_background_check_only" value="1" type="checkbox" id="completed_background_check_only_{{ $edit_subarea->id }}" @isChecked($edit_subarea->completed_background_check_only)/>
                                                                                    <label for="completed_background_check_only_{{ $edit_subarea->id }}">Show for <strong>"completed a background check" users only</strong></label>
                                                                                </div>
                                                                                <label><strong>Options</strong></label>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="is_hidden" wire:model="edit_subarea.is_hidden" value="1" type="checkbox" id="is_hidden_{{ $edit_subarea->id }}" @isChecked($edit_subarea->is_hidden)/>
                                                                                    <label for="is_hidden_{{ $edit_subarea->id }}">Is Hidden (from members)</label>
                                                                                </div>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="auto_approve_for_assignments" wire:model="edit_subarea.auto_approve_for_assignments" value="1" type="checkbox" id="auto_approve_for_assignments_{{ $edit_subarea->id }}" @isChecked($edit_subarea->auto_approve_for_assignments)/>
                                                                                    <label for="auto_approve_for_assignments_{{ $edit_subarea->id }}">Automatically Approve for Assignments</label>
                                                                                </div>
                                                                                <div class="custom-control custom-switch">
                                                                                    <input name="auto_show_in_volunteer_list" wire:model="edit_subarea.auto_show_in_volunteer_list" value="1" type="checkbox" id="auto_show_in_volunteer_list_{{ $edit_subarea->id }}" @isChecked($edit_subarea->auto_show_in_volunteer_list)/>
                                                                                    <label for="auto_show_in_volunteer_list_{{ $edit_subarea->id }}">Automatically Show in Volunteers List</label>
                                                                                </div>
                                                                            </div>
                                                                            <div class="flex justify-between py-4">
                                                                                <div class="">
                                                                                    <button type="submit" class="admin-button-blue">
                                                                                        <x-heroicon-s-check class="w-3 mr-1"/>
                                                                                        <span>Save Subarea</span>
                                                                                    </button>
                                                                                    <button type="button" class="admin-button-transparent" wire:click="cancelEditSubarea">
                                                                                        Cancel
                                                                                    </button>
                                                                                </div>
                                                                                <div>
                                                                                    <button type="button" wire:click="deleteSubarea" class="admin-button-red-transparent">Delete</button>
                                                                                </div>
                                                                            </div>
                                                                            <div class="alert alert-info pb-2" role="alert">
                                                                                <strong>Note:</strong> Selecting a restriction will remove users' selections for this area and subareas! This can not be undone.
                                                                                <br>
                                                                                <small>For example: Selecting "baptized users only" will remove all unbaptized users that have selected anything in this area.</small>
                                                                            </div>
                                                                        </div>
                                                                    </form>
                                                                @else
                                                                    <li wire:click="editSubarea({{ $subarea->id }})">
                                                                        <div class="block bg-gray-100 hover:bg-gray-50 cursor-pointer">
                                                                            <div class="pr-4 pl-12 py-2">
                                                                                <div class="flex items-center justify-between">
                                                                                    <p class="flex text-base text-gray-500 font-medium truncate">
                                                                                        <x-heroicon-s-chevron-right class="w-5 mr-1 text-gray-400"/>
                                                                                        <span class="text-gray-600 text-sm">{{ $subarea->name }}</span>
                                                                                    </p>
                                                                                    <div class="ml-2 shrink-0 flex text-xs">
                                                                                        @if(!$subarea->isMenAndWomen())
                                                                                            @if($subarea->isMenOnly())
                                                                                                <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-primary">Men Only</span>
                                                                                            @elseif($subarea->isWomenOnly())
                                                                                                <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-primary">Women Only</span>
                                                                                            @endif
                                                                                        @endif
                                                                                        @if($subarea->isBaptizedOnly())
                                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Baptized Only</span>
                                                                                        @endif
                                                                                        @if($subarea->approved_to_teach_only)
                                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Teachers Only</span>
                                                                                        @endif
                                                                                        @if($subarea->completed_background_check_only)
                                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-blue-100 rounded mr-2 badge-info">Background ✅</span>
                                                                                        @endif
                                                                                        @if($subarea->is_hidden)
                                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-red-100 rounded mr-2 badge-info">Hidden 🚫</span>
                                                                                        @endif
                                                                                        @if(!$subarea->auto_approve_for_assignments)
                                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-red-100 rounded mr-2 badge-info">🚫Assignments</span>
                                                                                        @endif
                                                                                        @if(!$subarea->auto_show_in_volunteer_list)
                                                                                            <span class="px-1 py-0.5 border border-blue-400 bg-red-100 rounded mr-2 badge-info">🚫Volunteer List</span>
                                                                                        @endif
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                @endif
                                                            </ul>
                                                        @endforeach
                                                    </div>
                                                </li>
                                            @endif
                                        @empty
                                            <div class="flex mt-12">
                                                <div class="mx-auto rounded border border-gray-300 text-gray-400 bg-gray-100 px-3 py-2">
                                                    No Areas found!
                                                    <a class="cursor-pointer text-blue-500" onclick="openSidebar('{{ route('admin.involvement.areas.create', [$selected_category]) }}')">Create One</a>
                                                </div>
                                            </div>
                                        @endforelse
                                    </ul>
                                </div>
                            </div>
                        @else
                            <div class="py-20 text-center text-gray-400">
                                No category selected!
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </main>

    </div>

</div>


@push('scripts')
    <script src=""></script>
    <script>
        new Sortable(document.getElementById('categories-list'), {
            handle: '.handle', // handle's class
            animation: 150,
            onEnd: function (event) {
                // console.log('new id: ' + event.newIndex);
                // console.log('old id: ' + event.oldIndex);

                fetch('/involvement/categories/' + event.item.dataset.categoryid + '/update-sort-id', {
                    method: 'POST',
                    mode: 'cors', // no-cors, *cors, same-origin
                    cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
                    credentials: 'same-origin', // include, *same-origin, omit
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        new_sort_id: event.newIndex,
                        old_sort_id: event.oldIndex
                    })
                })
                    .then(response => {
                        console.log(response);
                    })
                    .then(data => {
                    })
                    .catch(error => {
                        alert(error);
                    });

                // var itemEl = event.item;  // dragged HTMLElement
                // event.to;    // target list
                // event.from;  // previous list
                // event.oldIndex;  // element's old index within old parent
                // event.newIndex;  // element's new index within new parent
                // event.oldDraggableIndex; // element's old index within old parent, only counting draggable elements
                // event.newDraggableIndex; // element's new index within new parent, only counting draggable elements
                // event.clone // the clone element
                // event.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
            },
        });
        new Sortable(document.getElementById('areas-list'), {
            handle: '.handle', // handle's class
            animation: 150,
            onEnd: function (event) {
                // console.log('new id: ' + event.newIndex);
                // console.log('old id: ' + event.oldIndex);

                fetch('/involvement/areas/' + event.item.dataset.areaid + '/update-sort-id', {
                    method: 'POST',
                    mode: 'cors', // no-cors, *cors, same-origin
                    cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
                    credentials: 'same-origin', // include, *same-origin, omit
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        new_sort_id: event.newIndex,
                        old_sort_id: event.oldIndex
                    })
                })
                    .then(response => {
                        console.log(response);
                    })
                    .then(data => {
                    })
                    .catch(error => {
                        alert(error);
                    });

                // var itemEl = event.item;  // dragged HTMLElement
                // event.to;    // target list
                // event.from;  // previous list
                // event.oldIndex;  // element's old index within old parent
                // event.newIndex;  // element's new index within new parent
                // event.oldDraggableIndex; // element's old index within old parent, only counting draggable elements
                // event.newDraggableIndex; // element's new index within new parent, only counting draggable elements
                // event.clone // the clone element
                // event.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
            },
        });
    </script>
@endpush