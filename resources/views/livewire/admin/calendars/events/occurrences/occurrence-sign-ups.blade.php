<div class="">

    <div class="admin-section-border bg-white px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:gap-6">
            <div class="mt-5 md:mt-0">
                @if($this->general_error)
                    <div class="col-span-4 lg:col-span-2 mb-4 text-red-600 text-sm px-2 py-1 rounded-md border border-red-300 bg-red-50">
                        {{ $this->general_error }}
                    </div>
                @endif
                <flux:checkbox.group label="Sign-up Options" variant="cards" class="flex-col">
                    <flux:checkbox value="1" wire:model.live="event_enable_responses">
                        <flux:checkbox.indicator/>

                        <div class="flex-1">
                            <flux:heading class="leading-4">Enable Sign-ups</flux:heading>
                            <flux:text size="sm" class="mt-2">Allow members to sign-up themselves and their family members to this event.</flux:text>
                        </div>
                    </flux:checkbox>
                    <flux:checkbox value="1" wire:model.live="event_show_responses">
                        <flux:checkbox.indicator/>

                        <div class="flex-1">
                            <flux:heading class="leading-4">Allow Members to View Sign-ups</flux:heading>
                            <flux:text size="sm" class="mt-2">This will allow members to view the names ofpeople who have signed up to this event.</flux:text>
                        </div>
                    </flux:checkbox>
                </flux:checkbox.group>

                @if($occurrence->enable_responses)
                    <div class="grid grid-cols-1 sm:grid-cols-3 mt-4 border border-gray-300 rounded-lg">
                        <div class="flex flex-col">
                            <div class="text-center font-medium py-2 bg-gray-100 rounded-tl-lg">
                                Going ({{ $occurrence->responses()->isGoing()->count() }})
                            </div>
                            @if($occurrence->show_responses)
                                <div class="flex flex-col text-center">
                                    @foreach($occurrence->responses()->isGoing()->get() as $response)
                                        <div class="py-1">
                                            <a href="{{ route('app.directory.view.family', $response->user->family_id) }}">
                                                @if($response->user)
                                                    {{ $response->user->name }}
                                                @else
                                                    {{ $response->name }}
                                                @endif
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        <div class="flex flex-col">
                            <div class="text-center font-medium py-2 bg-gray-100">
                                Not Going ({{ $occurrence->responses()->isNotGoing()->count() }})
                            </div>
                            @if($occurrence->show_responses)
                                <div class="flex flex-col text-center">
                                    @foreach($occurrence->responses()->isNotGoing()->get() as $response)
                                        <div class="py-1">
                                            <a href="{{ route('app.directory.view.family', $response->user->family_id) }}">
                                                @if($response->user)
                                                    {{ $response->user->name }}
                                                @else
                                                    {{ $response->name }}
                                                @endif
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        <div class="flex flex-col">
                            <div class="text-center font-medium py-2 bg-gray-100 rounded-tr-lg">
                                Interested ({{ $occurrence->responses()->isMaybe()->count() }})
                            </div>
                            @if($occurrence->show_responses)
                                <div class="flex flex-col text-center">
                                    @foreach($occurrence->responses()->isMaybe()->get() as $response)
                                        <div class="py-1">
                                            <a href="{{ route('app.directory.view.family', $response->user->family_id) }}">
                                                @if($response->user)
                                                    {{ $response->user->name }}
                                                @else
                                                    {{ $response->name }}
                                                @endif
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
