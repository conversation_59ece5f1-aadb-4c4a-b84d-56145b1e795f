<div class="">

    <div class="admin-section-border bg-white py-5 sm:rounded-lg">
        <div class="">

            <div class="px-4 sm:px-4 lg:px-6">
                <div class="sm:flex sm:items-center">
                    <div class="sm:flex-auto">
                        <h1 class="text-base font-semibold text-gray-900">Recurring Event Occurrences</h1>
                        <p class="mt-2 text-sm text-gray-700">This is a full list of all the occurrences of this recurring event. Click "Edit" on any occurrence to update details on that occurrence.</p>
                    </div>
                </div>
                <div class="mt-8 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-4 lg:-mx-6">
                        <div class="inline-block min-w-full py-2 align-middle">
                            <table class="min-w-full divide-y divide-gray-300">
                                <thead>
                                <tr>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-500 uppercase sm:pl-6 lg:pl-8">Date</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-500 uppercase">Time</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-500 uppercase">Title</th>
                                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-500 uppercase">Sign-ups</th>
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6 lg:pr-8">
                                        <span class="sr-only">Edit</span>
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 bg-white">
                                @foreach($occurrence->event->occurrences as $current_occurrence)
                                    <tr class="{{ $current_occurrence->id == $occurrence->id ? 'bg-blue-50' : null }}">
                                        <td class="whitespace-nowrap py-4 pl-4 pr-2 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8">
                                            {{ $current_occurrence->start_at->format('F j') }}
                                        </td>
                                        <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-500">
                                            @if($current_occurrence->event->is_all_day)
                                                All Day
                                            @else
                                                {{ $current_occurrence->start_at->format('g:ia') }}-{{ $current_occurrence->end_at->format('g:ia') }}
                                            @endif
                                        </td>
                                        <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-500">
                                            {{ $current_occurrence->title }}
                                        </td>
                                        <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-500">
                                            {{ $current_occurrence->responses()->count() }}
                                        </td>
                                        <td class="relative whitespace-nowrap py-4 pl-2 pr-4 text-right text-sm font-medium sm:pr-6 lg:pr-8">
                                            <a href="{{ route('admin.calendars.event.occurrence.view', $current_occurrence) }}"
                                               class="admin-button-transparent-small">
                                                Edit<span class="sr-only">{{ $current_occurrence->start_at->format('F j') }}</span>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach

                                <!-- More people... -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

</div>
