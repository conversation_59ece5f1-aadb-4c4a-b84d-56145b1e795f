<div class="">

    <div class="admin-section-border bg-white px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:gap-6">
            <div class="mt-5 md:mt-0">
                @if(!$calendars)
                    <div class="bg-red-50 text-red-800 border border-red-300 rounded p-4 text-base mb-4" role="alert">
                        <strong>Oops!</strong> You have no calendars yet.
                        <br>Please create a calendar first and then you can create events.
                    </div>
                @endif
                @if($this->general_error)
                    <div class="col-span-4 lg:col-span-2 mb-4 text-red-600 text-sm px-2 py-1 rounded-md border border-red-300 bg-red-50">
                        {{ $this->general_error }}
                    </div>
                @endif
                <div class="grid grid-cols-4 gap-2">

                    @if($occurrence->event->occurrences->count() > 1)
                        <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                            <label for="event_title" class="col-span-6 md:col-span-1 my-auto block text-sm font-medium text-gray-700"></label>
                            <h2 class="col-span-6 md:col-span-5 block w-full">Edit Single Event Occurrence</h2>
                        </div>
                        <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                            <label for="event_title" class="col-span-6 md:col-span-1 my-auto hidden md:block text-sm font-medium text-gray-700">&nbsp;</label>
                            <div class="col-span-6 md:col-span-5 flex flex-col md:flex-row justify-between gap-2">
                                <div class="flex flex-row gap-2">
                                    @if($occurrence->previousOccurrence())
                                        <a href="{{ route('admin.calendars.event.occurrence.view', $occurrence->previousOccurrence()) }}" class="admin-button-transparent">
                                            <x-heroicon-m-arrow-left class="w-5 h-5 text-gray-700"/>
                                        </a>
                                    @endif
                                    <div class="admin-button-transparent text-black cursor-default">
                                        {{ $occurrence->start_at->format('F j, Y') }}
                                    </div>
                                    @if($occurrence->nextOccurrence())
                                        <a href="{{ route('admin.calendars.event.occurrence.view', $occurrence->nextOccurrence()) }}" class="admin-button-transparent">
                                            <x-heroicon-m-arrow-right class="w-5 h-5 text-gray-700"/>
                                        </a>
                                    @endif
                                </div>
                                <div class="col-span-6 md:col-span-5 flex flex-row gap-2">
                                    <span class="px-2 py-1 bg-purple-50 text-purple-600 border border-purple-300 rounded-sm text-sm mb-auto">
                                        You are editing occurrence <strong>{{ $occurrence->event->occurrences()->where('start_at', '<=', $occurrence->start_at)->count() }} of {{ $occurrence->event->occurrences->count() }}</strong> of a repeating event.
                                    </span>
                                </div>
                            </div>
                        </div>
                    @endif
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                        <label for="event_title" class="col-span-6 md:col-span-1 my-auto block text-sm font-medium text-gray-700">Title</label>
                        <input type="text"
                               name="event_title"
                               id="event_title"
                               autocomplete="off"
                               placeholder="Friday Night Devotional"
                               wire:model.live.debounce.200ms="event_title"
                               class="col-span-6 md:col-span-5 mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                        <label for="selected_calendar_id" class="col-span-6 md:col-span-1 my-auto text-sm font-medium text-gray-700">Calendar</label>

                        <div class="col-span-5 md:col-span-2">
                            {{-- This is stupid, but we can't put this inline because Blade freaks out. "disabled" is the only difference here --}}
                            @if($occurrence->event->occurrences->count() > 1)
                                <flux:select variant="listbox"
                                             searchable
                                             disabled
                                             placeholder="Choose a calendar..."
                                             wire:model.live="event_calendar_id"
                                             id="selected_calendar_id">
                                    @foreach($calendars as $calendar)
                                        <flux:select.option value="{{ $calendar->id }}">
                                            <div class="flex gap-2">
                                                <span class="w-5 h-5 rounded-md my-auto" style="background-color: #{{ $calendar->background_color }}"></span>
                                                {{ $calendar->name }}
                                            </div>
                                        </flux:select.option>
                                    @endforeach
                                </flux:select>
                            @else
                                <flux:select variant="listbox"
                                             searchable
                                             placeholder="Choose a calendar..."
                                             wire:model.live="event_calendar_id"
                                             id="selected_calendar_id">
                                    @foreach($calendars as $calendar)
                                        <flux:select.option value="{{ $calendar->id }}">
                                            <div class="flex gap-2">
                                                <span class="w-5 h-5 rounded-md my-auto" style="background-color: #{{ $calendar->background_color }}"></span>
                                                {{ $calendar->name }}
                                            </div>
                                        </flux:select.option>
                                    @endforeach
                                </flux:select>
                            @endif
                        </div>
                    </div>
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                        <label for="event_location" class="col-span-6 md:col-span-1 block text-sm font-medium text-gray-700 my-auto">Location</label>
                        <input type="text"
                               name="event_location"
                               id="event_location"
                               autocomplete="off"
                               placeholder="Room 102"
                               wire:model.live.debounce.200ms="event_location"
                               class="col-span-5 md:col-span-2 mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6">
                        <div class=" col-span-6 md:col-span-1"></div>
                        <div class="relative flex col-span-6 md:col-span-5">
                            <flux:checkbox.group variant="cards" class="flex-col">
                                <flux:checkbox value="1" wire:model.live="event_is_all_day" class="py-2.5">
                                    <flux:checkbox.indicator/>

                                    <div class="flex-1">
                                        <flux:heading class="leading-4">All Day Event?</flux:heading>
                                    </div>
                                </flux:checkbox>
                            </flux:checkbox.group>
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6">
                        <label for="event_start_at_date" class="col-span-6 md:col-span-1 my-auto block text-sm font-medium text-gray-700">Start Date</label>
                        <div class="col-span-6 md:col-span-5 mt-1 flex">
                            <div class="flex-1 grid grid-cols-4 gap-4">
                                <div class="col-span-4 flex flex-row space-x-2">
                                    <flux:date-picker wire:model.live="event_start_at_date"
                                                      wire:change="setDateTime()"
                                                      fixed-weeks selectable-header with-today
                                    />
                                    {{--                                    <input type="date" name="event_start_at_date" wire:model.live="event_start_at_date" wire:change="setDateTime()" autocomplete="off"/>--}}
                                    @if(!$event_is_all_day)

                                        <flux:select wire:model.live="event_start_at_hour" wire:change="setDateTime()" class="w-fit!">
                                            @for($i=0;$i<=23;$i++)
                                                @php($i_value = str_pad($i, 2, "0", STR_PAD_LEFT))
                                                <flux:select.option value="{{ $i_value }}">
                                                    {{ ($i == 0 ? '12 am' : ($i <= 11 ? $i . ' am' : ($i == 12 ? '12 pm' : $i - 12 . ' pm'))) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                        <flux:select wire:model.live="event_start_at_minute" wire:change="setDateTime()" class="w-fit!">
                                            @for($i=0;$i<=55;$i = $i+5)
                                                @php($i_value = str_pad($i, 2, "0", STR_PAD_LEFT))
                                                <flux:select.option value="{{ $i_value }}">
                                                    {{ ($i == 0 ? '00' : ($i == 5 ? '05' : $i)) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-4 col-start-1 grid grid-cols-6">
                        <label for="event_end_at_date" class="col-span-6 md:col-span-1 my-auto block text-sm font-medium text-gray-700">End Date</label>
                        <div class="col-span-6 md:col-span-5 mt-1 flex">
                            <div class="flex-1 grid grid-cols-4 gap-4">
                                <div class="col-span-4 flex flex-row space-x-2">
                                    <flux:date-picker wire:model.live="event_end_at_date"
                                                      wire:change="setDateTime()"
                                                      fixed-weeks selectable-header with-today
                                                      class="{{ $this->dates_warning ? 'border border-red-500 bg-red-200' : '' }}"
                                    />
                                    {{--                                    <input type="date" class="{{ $this->dates_warning ? 'border border-red-500 bg-red-200' : '' }}" name="event_end_at_date" wire:model.live="event_end_at_date" wire:change="setDateTime()" autocomplete="off"/>--}}
                                    @if(!$event_is_all_day)

                                        <flux:select wire:model.live="event_end_at_hour" wire:change="setDateTime()" class="w-fit!">
                                            @for($i=0;$i<=23;$i++)
                                                @php($i_value = str_pad($i, 2, "0", STR_PAD_LEFT))
                                                <flux:select.option value="{{ $i_value }}">
                                                    {{ ($i == 0 ? '12 am' : ($i <= 11 ? $i . ' am' : ($i == 12 ? '12 pm' : $i - 12 . ' pm'))) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                        <flux:select wire:model.live="event_end_at_minute" wire:change="setDateTime()" class="w-fit!">
                                            @for($i=0;$i<=55;$i = $i+5)
                                                @php($i_value = str_pad($i, 2, "0", STR_PAD_LEFT))
                                                <flux:select.option value="{{ $i_value }}">
                                                    {{ ($i == 0 ? '00' : ($i == 5 ? '05' : $i)) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6">
                        <div class=" col-span-6 md:col-span-1"></div>
                        <div class="relative flex flex-col space-y-2 col-span-6 md:col-span-5">
                            @if(!empty($this->dates_warning))
                                <div class="col-span-4 mr-auto lg:col-span-2 text-orange-600 text-sm px-2 py-1 rounded-md border border-orange-300 bg-orange-50">
                                    {{ $this->dates_warning[0] }}
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6">
                        <div class=" col-span-6 md:col-span-1">
                            <label for="event_description" class="block text-sm font-medium text-gray-700">
                                Details
                            </label>
                        </div>
                        <div class="relative flex col-span-6 md:col-span-5">
                            <div class="w-full">
                                <div class="mt-1 flex rounded-md shadow-2xs">
                                    <textarea name="event_description" id="event_description"
                                              class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                              placeholder="" rows="3"
                                              wire:model.live.debounce.200ms="event_description"></textarea>
                                </div>
                                <span class="font-light text-gray-500 text-xs">Full details of this event</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6 mt-4">
                        <div class="col-span-6 md:col-span-1">

                        </div>
                        <div class="relative flex flex-col sm:flex-row gap-2 justify-between col-span-6 md:col-span-5">
                            <div class="flex flex-row gap-4">
                                <button type="button"
                                        wire:click="saveChanges"
                                        class="admin-button-blue">
                                    {{ !$calendars ? 'disabled="disabled"' : null }}
                                    <x-heroicon-s-check class="w-4 mr-1"/>
                                    Save Changes
                                </button>
                                <a href="{{ route('admin.calendars.index') }}"
                                   class="admin-button-transparent">
                                    Cancel
                                </a>
                            </div>
                            <button type="button"
                                    wire:click="deleteOccurrence"
                                    class="admin-button-red-transparent-small w-fit mt-6 ml-auto sm:ml-0 sm:mt-0">
                                {{ !$calendars ? 'disabled="disabled"' : null }}
                                <x-heroicon-s-trash class="w-4 mr-1"/>
                                Delete {{ $occurrence->event->occurrences->count() > 1 ? 'Occurrence' : null }}
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
