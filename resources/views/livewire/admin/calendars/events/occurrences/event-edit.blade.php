<div class="">

    <div class="admin-section-border bg-white px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:gap-6">
            <div class="mt-4 md:mt-0">
                @if(!$calendars)
                    <div class="bg-red-50 text-red-800 border border-red-300 rounded p-4 text-base mb-4" role="alert">
                        <strong>Oops!</strong> You have no calendars yet.
                        <br>Please create a calendar first and then you can create events.
                    </div>
                @endif
                @if($this->general_error)
                    <div class="col-span-4 lg:col-span-2 mb-4 text-red-600 text-sm px-2 py-1 rounded-md border border-red-300 bg-red-50">
                        {{ $this->general_error }}
                    </div>
                @endif
                <div class="grid grid-cols-4 gap-2">
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                        <label for="event_title" class="col-span-6 md:col-span-1 my-auto block text-sm font-medium text-gray-700"></label>
                        <h2 class="col-span-6 md:col-span-5 block w-full">Edit All Event Occurrences</h2>
                    </div>
                    @if($occurrence->event->occurrences->count() > 1)
                        <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                            <label for="event_title" class="col-span-6 md:col-span-1 my-auto hidden md:block text-sm font-medium text-gray-700">&nbsp;</label>
                            <div class="col-span-6 md:col-span-5 flex flex-col md:flex-row justify-between gap-2">
                                <div class="col-span-6 md:col-span-5 flex flex-row gap-2">
                                    <span class="px-2 py-1 bg-purple-50 text-purple-600 border border-purple-300 rounded-sm text-sm mb-auto">
                                        Changes to these fields will affect <strong>all occurrences</strong> of this repeating event.
                                        <br>
                                        This will <strong>overwrite</strong> any changes made to specific occurrences.
                                    </span>
                                </div>
                            </div>
                        </div>
                    @endif
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                        <label for="event_title" class="col-span-6 md:col-span-1 my-auto block text-sm font-medium text-gray-700">Title</label>
                        <input type="text"
                               name="event_title"
                               id="event_title"
                               autocomplete="off"
                               placeholder="Friday Night Devotional"
                               wire:model.live.debounce.200ms="event_title"
                               class="col-span-6 md:col-span-5 mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                        <label for="selected_calendar_id" class="col-span-6 md:col-span-1 my-auto text-sm font-medium text-gray-700">Calendar</label>

                        <div class="col-span-5 md:col-span-2">
                            <flux:select variant="listbox"
                                         searchable
                                         placeholder="Choose a calendar..."
                                         wire:model.live="event_calendar_id"
                                         id="selected_calendar_id">
                                @foreach($calendars as $calendar)
                                    <flux:select.option value="{{ $calendar->id }}">
                                        <div class="flex gap-2">
                                            <span class="w-5 h-5 rounded-md my-auto" style="background-color: #{{ $calendar->background_color }}"></span>
                                            {{ $calendar->name }}
                                        </div>
                                    </flux:select.option>
                                @endforeach
                            </flux:select>
                        </div>
                    </div>
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-6">
                        <label for="event_location" class="col-span-6 md:col-span-1 block text-sm font-medium text-gray-700 my-auto">Location</label>
                        <input type="text"
                               name="event_location"
                               id="event_location"
                               autocomplete="off"
                               placeholder="Room 102"
                               wire:model.live.debounce.200ms="event_location"
                               class="col-span-5 md:col-span-2 mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6">
                        <div class=" col-span-6 md:col-span-1"></div>
                        <div class="relative flex flex-col space-y-2 col-span-6 md:col-span-5">
                            @if(!empty($this->dates_warning))
                                <div class="col-span-4 mr-auto lg:col-span-2 text-orange-600 text-sm px-2 py-1 rounded-md border border-orange-300 bg-orange-50">
                                    {{ $this->dates_warning[0] }}
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6">
                        <div class=" col-span-6 md:col-span-1">
                            <label for="event_description" class="block text-sm font-medium text-gray-700">
                                Details
                            </label>
                        </div>
                        <div class="relative flex col-span-6 md:col-span-5">
                            <div class="w-full">
                                <div class="mt-1 flex rounded-md shadow-2xs">
                                    <textarea name="event_description" id="event_description"
                                              class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                              placeholder="" rows="3"
                                              wire:model.live.debounce.200ms="event_description"></textarea>
                                </div>
                                <span class="font-light text-gray-500 text-xs">Full details of this event</span>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-6 mt-4">
                        <div class="col-span-6 md:col-span-1">

                        </div>
                        <div class="relative flex flex-col sm:flex-row gap-2 justify-between col-span-6 md:col-span-5">
                            <div class="flex flex-row gap-4">
                                <button type="button"
                                        wire:click="saveChanges"
                                        class="admin-button-blue">
                                    {{ !$calendars ? 'disabled="disabled"' : null }}
                                    <x-heroicon-s-check class="w-4 mr-1"/>
                                    Save Changes
                                </button>
                                <a href="{{ route('admin.calendars.index') }}"
                                   class="admin-button-transparent">
                                    Cancel
                                </a>
                            </div>
                            <flux:tooltip content="This will delete all occurrences of this event!"
                                          class="mt-6 ml-auto sm:ml-0 sm:mt-0">
                                <button type="button"
                                        wire:click="deleteAllOccurrences"
                                        class="admin-button-red-transparent">
                                    {{ !$calendars ? 'disabled="disabled"' : null }}
                                    <x-heroicon-s-trash class="w-4 mr-1"/>
                                    Delete ALL Occurrences
                                </button>
                            </flux:tooltip>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
