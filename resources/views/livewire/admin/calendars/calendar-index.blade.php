<div x-data="{ 
    hiddenCalendars: $persist([]).as('admin_hidden_calendars'),
    allCalendarCount: {{ count($all_calendars) }},
    calendarView: null,
    calendarDate: null,
    toggleCalendar(calId) {
        const index = this.hiddenCalendars.indexOf(calId);
        if (index > -1) {
            this.hiddenCalendars.splice(index, 1);
        } else {
            this.hiddenCalendars.push(calId);
        }
        window.dispatchEvent(new CustomEvent('hiddenCalendarsChanged', { detail: { hidden: this.hiddenCalendars } }));
    }
}">
    <style>
        .fc-view-harness {
            background-color: #ffffff;
        }

        .fc .fc-daygrid-day-frame {
            min-height: 140px;
        }
    </style>

    <div class="admin-heading-section">
        <h1>
            Calendars
        </h1>
    </div>

    <div class="my-4 flex flex-row justify-between gap-2 mt-4">
        <div class="flex flex-row gap-2">
            <flux:button
                    variant="primary" icon="plus"
                    href="{{ route('admin.calendars.event.create') }}">
                New Event
            </flux:button>
            <flux:dropdown>
                <flux:button icon-trailing="chevron-down">
                    Calendars
                    <span class="font-normal" x-text="`(${allCalendarCount - hiddenCalendars.length}/${allCalendarCount})`"></span>
                </flux:button>

                <flux:menu class="px-4">
                    <a class="admin-button-blue-small w-full mt-3 mb-2"
                       onclick="openModal('{{ route('admin.calendars.create') }}', false)"
                    >
                        <div class="flex justify-center mx-auto">
                            <x-heroicon-s-plus class="w-5 mr-1"/>
                            New Calendar
                        </div>
                    </a>

                    <div class="my-2 font-medium text-xs uppercase text-gray-400">Calendars</div>

                    @forelse($calendars as $calendar)
                        <div class="flex flex-row justify-between items-center mb-2">
                            <flux:checkbox label="{{ $calendar->name }}"
                                           class="my-auto"
                                           id="cal_switch_{{ $calendar->ulid }}"
                                           value="1"
                                           x-bind:checked="!hiddenCalendars.includes('{{ $calendar->ulid }}')"
                                           @change="toggleCalendar('{{ $calendar->ulid }}')"
                            />
                            <div class="flex flex-row gap-1 items-center ml-4">
                                <span class="my-auto w-4 h-4 rounded-full mr-2" style="{{ $calendar->getBackgroundColorForCss() }}"></span>
                                <a onclick="openModal('{{ route('admin.calendars.edit', $calendar) }}', false)"
                                   class="text-xs admin-button-transparent-small">Edit</a>
                            </div>
                        </div>
                    @empty
                        <div class="text-gray-400 text-sm bg-gray-100 rounded-md p-2">No calendars found</div>
                    @endforelse

                    <div class="my-2 font-medium text-xs uppercase text-gray-400">Group Calendars</div>
                    @forelse($group_calendars as $calendar)
                        <div class="flex flex-row justify-between items-center mb-2">
                            <flux:checkbox label="{{ $calendar->name }}"
                                           class="my-auto"
                                           id="cal_switch_{{ $calendar->ulid }}"
                                           value="1"
                                           x-bind:checked="!hiddenCalendars.includes('{{ $calendar->ulid }}')"
                                           @change="toggleCalendar('{{ $calendar->ulid }}')"
                            />
                            <div class="flex flex-row gap-1 items-center ml-4">
                                <span class="my-auto w-4 h-4 rounded-full mr-2" style="{{ $calendar->getBackgroundColorForCss() }}"></span>
                                <a onclick="openModal('{{ route('admin.calendars.edit', $calendar) }}', false)"
                                   class="text-xs admin-button-transparent-small">Edit</a>
                            </div>
                        </div>
                    @empty
                        <div class="text-gray-400 text-sm bg-gray-100 rounded-md p-2">No group calendars found</div>
                    @endforelse
                </flux:menu>
            </flux:dropdown>
        </div>
        @if(auth()->user()->account->calendars()->isPublic()->count() > 0)
            <div class="hidden md:block">
                <flux:button onclick="openModal('{{ route('admin.calendars.public.calendar-snippet') }}', false)">Website Code Snippet</flux:button>
            </div>
        @endif
    </div>

    <div id="calendar" class="print:block"></div>

    @push('scripts')
        <script src='/static/global/js/moment.js'></script>
        <script src='/static/global/js/fullcalendar.js'></script>

        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const calendarEl = document.getElementById('calendar');
                const allCalendarSources = {!! $all_calendars_json !!};

                const initialHiddenCalendars = JSON.parse(localStorage.getItem('admin_hidden_calendars') || '[]');

                let initialView = localStorage.getItem('admin_calendar_view');
                let initialDate = localStorage.getItem('admin_calendar_date');

                let activeCalendarSources = allCalendarSources.filter(source =>
                    !initialHiddenCalendars.includes(source.id)
                );

                var calendar = new FullCalendar.Calendar(calendarEl, {
                    height: 'auto',
                    schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek,listMonth'
                    },
                    timeZone: 'local',
                    initialView: initialView || 'dayGridMonth',
                    initialDate: initialDate || new Date().toISOString().slice(0, 10),
                    refetchResourcesOnNavigate: true,
                    eventSources: activeCalendarSources,
                    eventClick: function (info) {
                        info.jsEvent.preventDefault();
                        if (info.event.id) {
                            window.location.href = '{{ url('calendars/event/occurrence') }}/' + info.event.id;
                            //  openModal('{{ url('calendars/event/occurrence') }}/' + info.event.id + '/edit', true);
                        } else {
                            console.error("Event click failed: Event ID is missing.", info.event);
                        }
                    },
                    eventDidMount: function (info) {
                        if (info.event.extendedProps.isRecurring) {
                            info.el.insertAdjacentHTML('beforeend', '<span class="ml-1 mr-0.5"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3" /></svg></span>');
                        }
                    },
                    datesSet: function (dateInfo) {
                        localStorage.setItem('admin_calendar_view', dateInfo.view.type);
                        localStorage.setItem('admin_calendar_date', dateInfo.view.currentStart.toISOString().slice(0, 10));
                    }
                });

                calendar.render();

                window.addEventListener('hiddenCalendarsChanged', (event) => {
                    const newHiddenList = event.detail.hidden;
                    console.log('Received hiddenCalendarsChanged event, updating sources:', newHiddenList);

                    calendar.getEventSources().forEach(source => source.remove());

                    let newActiveSources = allCalendarSources.filter(source =>
                        !newHiddenList.includes(source.id)
                    );
                    newActiveSources.forEach(source => calendar.addEventSource(source));
                });
            });
        </script>
    @endpush
</div> 