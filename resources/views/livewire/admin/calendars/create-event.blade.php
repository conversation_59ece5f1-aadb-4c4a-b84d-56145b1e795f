<div class="">

    <div class="admin-section-border bg-white px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:gap-6">
            <div class="mt-5 md:mt-0">
                @if(!$calendars)
                    <div class="bg-red-50 text-red-800 border border-red-300 rounded p-4 text-base mb-4" role="alert">
                        <strong>Oops!</strong> You have no calendars yet.
                        <br>Please create a calendar first and then you can create events.
                    </div>
                @endif
                @if($this->general_error)
                    <div class="col-span-4 lg:col-span-2 mb-4 text-red-600 text-sm px-2 py-1 rounded-md border border-red-300 bg-red-50">
                        {{ $this->general_error }}
                    </div>
                @endif
                <div class="grid grid-cols-4 gap-2">
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-5">
                        <label for="event_title" class="col-span-5 md:col-span-1 my-auto block text-sm font-medium text-gray-700">Title</label>
                        <input type="text"
                               name="event_title"
                               id="event_title"
                               autocomplete="off"
                               placeholder="Friday Night Devotional"
                               wire:model.live="event_title"
                               class="col-span-5 md:col-span-4 mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-5">
                        <label for="selected_calendar_id" class="col-span-5 md:col-span-1 my-auto text-sm font-medium text-gray-700">Calendar</label>

                        <div class="col-span-5 md:col-span-2">
                            <flux:select variant="listbox"
                                         searchable
                                         placeholder="Choose a calendar..."
                                         wire:model.live="selected_calendar_id"
                                         id="selected_calendar_id">
                                @foreach($calendars as $calendar)
                                    <flux:select.option value="{{ $calendar->id }}">
                                        <div class="flex gap-2">
                                            <span class="w-5 h-5 rounded-md my-auto" style="background-color: #{{ $calendar->background_color }}"></span>
                                            {{ $calendar->name }}
                                        </div>
                                    </flux:select.option>
                                @endforeach
                            </flux:select>
                        </div>
                    </div>
                    <div class="col-span-4 sm:col-span-4 col-start-1 grid grid-cols-5">
                        <label for="event_location" class="col-span-5 md:col-span-1 block text-sm font-medium text-gray-700 my-auto">Location</label>
                        <input type="text"
                               name="event_location"
                               id="event_location"
                               autocomplete="off"
                               placeholder="Room 102"
                               wire:model.live="event_location"
                               class="col-span-5 md:col-span-2 mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-5">
                        <div class=" col-span-5 md:col-span-1"></div>
                        <div class="relative flex col-span-5 md:col-span-4">
                            <flux:checkbox.group variant="cards" class="flex-col">
                                <flux:checkbox value="1" wire:model.live="event_is_all_day" class="py-2.5">
                                    <flux:checkbox.indicator/>

                                    <div class="flex-1">
                                        <flux:heading class="leading-4">All Day Event?</flux:heading>
                                    </div>
                                </flux:checkbox>
                            </flux:checkbox.group>
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-5">
                        <label for="event_start_at_date" class="col-span-5 md:col-span-1 my-auto block text-sm font-medium text-gray-700">Start Date</label>
                        <div class="col-span-5 md:col-span-4 mt-1 flex">
                            <div class="flex-1 grid grid-cols-4 gap-4">
                                <div class="col-span-4 flex flex-row space-x-2">
                                    <flux:date-picker wire:model.live="event_start_at_date"
                                                      wire:change="setStartAt()"
                                                      fixed-weeks selectable-header with-today
                                    />
                                    {{--                                    <input type="date" name="event_start_at_date" wire:model.live="event_start_at_date" wire:change="setStartAt()" autocomplete="off"/>--}}
                                    @if(!$event_is_all_day)

                                        <flux:select wire:model.live="event_start_at_hour" placeholder="" class="w-fit!">
                                            @for($i=0;$i<=23;$i++)
                                                <flux:select.option value="{{ $i }}">
                                                    {{ ($i == 0 ? '12 am' : ($i <= 11 ? $i . ' am' : ($i == 12 ? '12 pm' : $i - 12 . ' pm'))) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                        <flux:select wire:model.live="event_start_at_minute" placeholder="" class="w-fit!">
                                            @for($i=0;$i<=55;$i = $i+5)
                                                <flux:select.option value="{{ $i }}">
                                                    {{ ($i == 0 ? '00' : ($i == 5 ? '05' : $i)) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-4 col-start-1 grid grid-cols-5">
                        <label for="event_end_at_date" class="col-span-5 md:col-span-1 my-auto block text-sm font-medium text-gray-700">End Date</label>
                        <div class="col-span-5 md:col-span-4 mt-1 flex">
                            <div class="flex-1 grid grid-cols-4 gap-4">
                                <div class="col-span-4 flex flex-row space-x-2">
                                    <flux:date-picker wire:model.live="event_end_at_date"
                                                      wire:change="setEndAt()"
                                                      fixed-weeks selectable-header with-today
                                                      class="{{ $this->dates_warning ? 'border border-red-500 bg-red-200' : '' }}"
                                    />
                                    {{--                                    <input type="date" class="{{ $this->dates_warning ? 'border border-red-500 bg-red-200' : '' }}" name="event_end_at_date" wire:model.live="event_end_at_date" wire:change="setEndAt()" autocomplete="off"/>--}}
                                    @if(!$event_is_all_day)

                                        <flux:select wire:model.live="event_end_at_hour" placeholder="" class="w-fit!">
                                            @for($i=0;$i<=23;$i++)
                                                <flux:select.option value="{{ $i }}">
                                                    {{ ($i == 0 ? '12 am' : ($i <= 11 ? $i . ' am' : ($i == 12 ? '12 pm' : $i - 12 . ' pm'))) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                        <flux:select wire:model.live="event_end_at_minute" placeholder="" class="w-fit!">
                                            @for($i=0;$i<=55;$i = $i+5)
                                                <flux:select.option value="{{ $i }}">
                                                    {{ ($i == 0 ? '00' : ($i == 5 ? '05' : $i)) }}
                                                </flux:select.option>
                                            @endfor
                                        </flux:select>

                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-4 col-start-1 grid grid-cols-5">
                        <div class=" col-span-5 md:col-span-1"></div>
                        <div class="relative flex flex-col space-y-2 col-span-5 md:col-span-4">
                            @if($event_start_at->diffInHours($event_end_at) >= 18)
                                <div class="col-span-4 mr-auto lg:col-span-2 text-purple-600 text-sm px-2 py-1 rounded-md border border-purple-300 bg-purple-50">
                                    This event is <strong>{{ (int) $event_start_at->diffInHours($event_end_at) }} hours long</strong>.
                                </div>
                            @endif
                            @if($this->dates_warning)
                                <div class="col-span-4 mr-auto lg:col-span-2 text-orange-600 text-sm px-2 py-1 rounded-md border border-orange-300 bg-orange-50">
                                    {{ $this->dates_warning }}
                                </div>
                            @endif
                        </div>
                    </div>


                    <div class="col-span-4 col-start-1 grid grid-cols-5">
                        <div class=" col-span-5 md:col-span-1"></div>
                        <div class="relative flex col-span-5 md:col-span-4">
                            <flux:checkbox.group variant="cards" class="flex-col">
                                <flux:checkbox value="1" wire:model.live="event_is_recurring" class="py-2.5">
                                    <flux:checkbox.indicator/>

                                    <div class="flex-1">
                                        <flux:heading class="leading-4">Recurring Event?</flux:heading>
                                    </div>
                                </flux:checkbox>
                            </flux:checkbox.group>
                        </div>
                    </div>

                    @if($event_is_recurring)
                        <div class="col-span-4 col-start-1 grid grid-cols-5">
                            <div class=" col-span-5 md:col-span-1"></div>
                            <div class="relative flex col-span-5 md:col-span-4">
                                <div class="border border-purple-400 w-full rounded-lg px-4 pt-3 pb-4">
                                    <div class="col-span-4 sm:col-span-4 lg:col-span-4">
                                        <label for="event_recurring_frequency" class="block text-sm font-medium text-gray-700">Frequency</label>
                                        <div class="mt-1 flex">
                                            <div class="flex-1">
                                                <div class="col-span-4 flex flex-row space-x-2">


                                                    <flux:select variant="listbox" wire:model.live="event_recurring_frequency" class="w-fit!">
                                                        <flux:select.option value="DAILY">Daily</flux:select.option>
                                                        <flux:select.option value="WEEKLY">Weekly</flux:select.option>
                                                        <flux:select.option value="MONTHLY">Monthly</flux:select.option>
                                                        <flux:select.option value="YEARLY">Yearly</flux:select.option>
                                                    </flux:select>

                                                    @if($event_recurring_frequency == 'DAILY')
                                                        <div class="my-auto">every</div>
                                                        <div class="w-16">
                                                            <flux:input wire:model.live="event_recurring_interval" class="text-center font-bold mx-auto"/>
                                                        </div>
                                                        <div class="my-auto">day(s)</div>
                                                    @endif
                                                    @if($event_recurring_frequency == 'WEEKLY')
                                                        <div class="my-auto">every</div>
                                                        <div class="w-16">
                                                            <input type="text" class="text-center font-bold" wire:model.live="event_recurring_interval" name="event_recurring_interval"/>
                                                        </div>
                                                        <div class="my-auto">week(s)</div>
                                                    @endif
                                                    @if($event_recurring_frequency == 'MONTHLY')
                                                        <div class="my-auto">every</div>
                                                        <div class="w-16">
                                                            <input type="text" class="text-center font-bold" wire:model.live="event_recurring_interval" name="event_recurring_interval"/>
                                                        </div>
                                                        <div class="my-auto">month(s)</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if($event_recurring_frequency == 'WEEKLY')
                                        <div class="mt-4">
                                            <div class="mt-1 flex">
                                                @php
                                                    $checked = 'border border-gray-300 bg-blue-600 text-white mx-0';
                                                    $unchecked = 'border border-gray-300 bg-white hover:bg-gray-50';
                                                @endphp
                                                <span class="flex-1 rounded-md">
                                            <button type="button" wire:click="setRecurringDay('SU')" class="{{ in_array('SU', $event_recurring_days) ? $checked : $unchecked }} relative inline-flex items-center rounded-l-md px-4 py-2 text-sm font-medium text-gray-700">
                                                Sun
                                            </button><button type="button" wire:click="setRecurringDay('MO')" class="{{ in_array('MO', $event_recurring_days) ? $checked : $unchecked }} relative inline-flex -ml-px items-center px-4 py-2 text-sm font-medium text-gray-700">
                                                Mon
                                            </button><button type="button" wire:click="setRecurringDay('TU')" class="{{ in_array('TU', $event_recurring_days) ? $checked : $unchecked }} relative inline-flex -ml-px items-center px-4 py-2 text-sm font-medium text-gray-700">
                                                Tues
                                            </button><button type="button" wire:click="setRecurringDay('WE')" class="{{ in_array('WE', $event_recurring_days) ? $checked : $unchecked }} relative inline-flex -ml-px items-center px-4 py-2 text-sm font-medium text-gray-700">
                                                Wed
                                            </button><button type="button" wire:click="setRecurringDay('TH')" class="{{ in_array('TH', $event_recurring_days) ? $checked : $unchecked }} relative inline-flex -ml-px items-center px-4 py-2 text-sm font-medium text-gray-700">
                                                Thurs
                                            </button><button type="button" wire:click="setRecurringDay('FR')" class="{{ in_array('FR', $event_recurring_days) ? $checked : $unchecked }} relative inline-flex -ml-px items-center px-4 py-2 text-sm font-medium text-gray-700">
                                                Fri
                                            </button><button type="button" wire:click="setRecurringDay('SA')" class="{{ in_array('SA', $event_recurring_days) ? $checked : $unchecked }} relative inline-flex -ml-px items-center rounded-r-md px-4 py-2 text-sm font-medium text-gray-700">
                                                Sat
                                            </button>
                                        </span>
                                            </div>
                                        </div>
                                    @endif
                                    @if($event_recurring_frequency == 'MONTHLY')
                                        <div class="mt-4">
                                            <div class="flex-1 grid grid-cols-4 gap-4">
                                                <div class="col-span-4 flex flex-row space-x-2">
                                                    <input type="radio" id="r1" class="my-auto" name="event_recurring_monthly_type" wire:model.live="event_recurring_monthly_type" value="day_of_month" wire:click="setRecurringType('monthly_by_day_of_month')"/>
                                                    <label for="r1" class="my-auto">on day</label>
                                                    <select name="event_recurring_by_day_of_month" wire:model.live="event_recurring_by_day_of_month">
                                                        @for($i=1;$i<=31;$i++)
                                                            <option value="{{ $i }}">
                                                                {{ $i }}
                                                            </option>
                                                        @endfor
                                                    </select>
                                                </div>
                                                <div class="col-span-4 flex flex-row space-x-2">
                                                    <input type="radio" id="r2" class="my-auto" name="event_recurring_monthly_type" wire:model.live="event_recurring_monthly_type" value="step_position" wire:click="setRecurringType('month_by_step_position')"/>
                                                    <label for="r2" class="my-auto">on the</label>
                                                    <select name="event_recurring_step_position" wire:model.live="event_recurring_step_position">
                                                        @foreach(\App\Calendars\Event::$recur_step_positions as $value => $name)
                                                            <option value="{{ $value }}">
                                                                {{ $name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <select name="event_recurring_by_day" wire:change="setRecurringDaysByCSV()" wire:model.live="event_recurring_by_day">
                                                        @foreach(\App\Calendars\Event::$recur_by_day as $value => $name)
                                                            <option value="{{ $value }}" {{ $value == implode(',', $event_recurring_days) ? 'selected="selected"' : '' }}>
                                                                {{ $name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                    <div class="mt-4">
                                        <label for="event_recur_end_at_date" class="block text-sm font-medium text-gray-700">End Repeat On</label>
                                        <div class="mt-1 flex">
                                            <div class="flex-1 grid grid-cols-4 gap-4">
                                                <div class="col-span-4 flex flex-row space-x-2">
                                                    <flux:date-picker wire:model.live="event_recur_end_at_date"
                                                                      fixed-weeks selectable-header with-today
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <p class="mt-2 text-sm">
                                            @if($rrule_human_readable)
                                                {{-- <span class="text-purple-600">{{ ucfirst($rrule_human_readable) }}</span><br>--}}
                                                @if($rrule_occurrences)
                                                    {{-- We +1 a day here because for SOME REASON, we're one occurrence short without it. Lots of debugging to find this, and still not sure why it's this way. --}}
                                                    <span class="text-purple-600 font-medium">({{ count($rrule_occurrences) }} occurrences will be created)</span>
                                                    <br>
                                                @endif
                                            @endif
                                            <span class="text-gray-400">Recurring events can reoccur for a maximum of 18 months.</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="col-span-4 col-start-1 grid grid-cols-5">
                        <div class=" col-span-5 md:col-span-1">
                            <label for="event_description" class="block text-sm font-medium text-gray-700">
                                Details
                                <br>
                                <span class="font-light text-gray-500 text-xs">(Full details of this event)</span>
                            </label>
                        </div>
                        <div class="relative flex col-span-5 md:col-span-4">
                            <div class="w-full">
                                <div class="mt-1 flex rounded-md shadow-2xs">
                                    <textarea name="event_description" id="event_description"
                                              class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                              placeholder="" rows="3"
                                              wire:model.live="event_description"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-4 sm:col-span-4 mt-4">
                        <div class="flex justify-start">
                            <button type="button"
                                    wire:click="createEvent"
                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-2xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                {{ !$calendars ? 'disabled="disabled"' : null }}
                                <x-heroicon-s-check class="w-4 mr-1"/>
                                Create Event
                            </button>
                            <a href="{{ route('admin.calendars.index') }}"
                               class="ml-3 rounded-md border border-gray-300 bg-white py-2 px-4 text-base font-medium text-gray-700 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                Cancel
                            </a>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
