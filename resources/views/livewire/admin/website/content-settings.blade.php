<div>
    @if ($notification)
        <div class="{{ $notification['type'] === 'success' ? 'bg-green-200' : 'bg-red-200' }} rounded-lg mb-4" id="notification">
            <div class="max-w-7xl mx-auto py-3 px-3 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between flex-wrap">
                    <div class="w-0 flex-1 flex items-center">
                        <span class="flex p-2 rounded-lg {{ $notification['type'] === 'success' ? 'text-green-600' : 'text-red-600' }}">
                            @if ($notification['type'] === 'success')
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            @else
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            @endif
                        </span>
                        <p class="ml-3 font-medium {{ $notification['type'] === 'success' ? 'text-green-700' : 'text-red-700' }} truncate">
                            {{ $notification['message'] }}
                        </p>
                    </div>
                    <div class="order-2 shrink-0 sm:order-3 sm:ml-3">
                        <button wire:click="dismissNotification" type="button" class="-mr-1 flex p-2 rounded-md hover:bg-{{ $notification['type'] === 'success' ? 'green' : 'red' }}-500 {{ $notification['type'] === 'success' ? 'text-green-600' : 'text-red-600' }} hover:text-white focus:outline-hidden focus:ring-2 focus:ring-white sm:-mr-2">
                            <span class="sr-only">Dismiss</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if ($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4 rounded relative" role="alert">
            <strong class="font-bold">Oops!</strong>
            <span class="block sm:inline">{{ $errors->first() }}</span>
        </div>
    @endif

    <form wire:submit.prevent="save">
        <div class="space-y-6">
            <!-- Leadership Settings -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Leadership</h3>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="leadershipEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Leadership Page</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="leadershipShowPhotos" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Show Leadership Photos</span>
                    </label>
                </div>
            </div>

            <!-- Prayer Requests Settings -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Prayer Requests</h3>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="prayerRequestsEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Prayer Requests Page</span>
                    </label>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Prayer Requests Email</label>
                        <div class="mt-1">
                            <input type="email" wire:model="prayerRequestsEmail" class="w-full px-3 py-2 border rounded" placeholder="<EMAIL>">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Prayer requests will be sent to this email address.</p>
                    </div>
                </div>
            </div>

            <!-- Contact Settings -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Form</h3>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="contactEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Contact Form</span>
                    </label>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Contact Form Email</label>
                        <div class="mt-1">
                            <input type="email" wire:model="contactEmail" class="w-full px-3 py-2 border rounded" placeholder="<EMAIL>">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Contact form submissions will be sent to this email address.</p>
                    </div>
                </div>
            </div>

            <!-- Bible Class Registration Settings -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Bible Class Registration</h3>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="bibleClassRegistrationEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Bible Class Registration</span>
                    </label>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Registration Email</label>
                        <div class="mt-1">
                            <input type="email" wire:model="bibleClassRegistrationEmail" class="w-full px-3 py-2 border rounded" placeholder="<EMAIL>">
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Bible class registration notifications will be sent to this email address.</p>
                    </div>
                </div>
            </div>

            <!-- Additional Features -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Features</h3>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="sermonsEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Sermons Page</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="filesEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Files Page</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="calendarsEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Calendars Page</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="podcastsEnabled" class="rounded border-gray-300 text-blue-600 shadow-2xs focus:border-blue-300 focus:ring-3 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2">Enable Podcasts Page</span>
                    </label>
                </div>
            </div>

            <div>
                <button type="submit" class="admin-button-blue">
                    Save Changes
                </button>
            </div>
        </div>
    </form>
</div> 