<div>
    @if ($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4 rounded relative" role="alert">
            <strong class="font-bold">Oops!</strong>
            <span class="block sm:inline">{{ $errors->first() }}</span>
        </div>
    @endif

    <div class="flex justify-between items-center mb-6">
        <h2>Redirects</h2>
        @if($editingIndex === null)
            <button wire:click="$set('showForm', true)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                <i class="far fa-plus-circle mr-2"></i> Add New Redirect
            </button>
        @endif
    </div>

    <div class="text-gray-600 mb-4 text-base">
        Redirects are used to redirect users from one URL to somewhere else. This is helpful if migrating from an old site and you want to keep the old links working.
    </div>

    @if($showForm || $editingIndex !== null)
        <div class="mb-8 p-6 bg-gray-50 rounded-lg border">
            <h3 class="text-xl font-semibold mb-4">{{ $editingIndex !== null ? 'Edit' : 'Add' }} Redirect</h3>
            <form wire:submit.prevent="{{ $editingIndex !== null ? 'updateItem' : 'addItem' }}">
                <!-- Path Preview -->
                <div class="mb-4">
                    <label for="redirect_entry_path" class="block text-sm font-medium text-gray-700">Path</label>
                    <div class="mt-1 flex rounded-md">
                        <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 sm:text-sm">
                            {{ auth()->user()->account->domain }}/
                        </span>
                        <input
                                type="text"
                                id="redirect_entry_path"
                                wire:model.live="newItem.redirect_entry_path"
                                class="text-gray-900 block w-full min-w-0 flex-1 sm:text-sm rounded-l-none! rounded-r-md border-r-0"
                        >
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Link Type</label>
                    <select wire:model.live="newItem.link_type" class="w-full px-3 py-2 border rounded">
                        <option value="custom">Custom URL</option>
                        <option value="page">Website Page</option>
                    </select>
                </div>

                @if($newItem['link_type'] === 'custom')
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Custom URL</label>
                        <input type="text" wire:model.live="newItem.redirect_exit_url" class="w-full px-3 py-2 border rounded">
                    </div>
                @else
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Select Page</label>
                        <select wire:model.live="newItem.redirect_exit_website_page_id" class="w-full px-3 py-2 border rounded">
                            <option value="">Select a page...</option>
                            @foreach($websitePages as $page)
                                <option value="{{ $page->id }}">{{ $page->title }}</option>
                            @endforeach
                        </select>
                    </div>
                @endif

                <div class="flex gap-2">
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        {{ $editingIndex !== null ? 'Update' : 'Add' }} Redirect
                    </button>
                    <button type="button" wire:click="cancelEdit" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    @endif

    <div id="redirect-links">
        @foreach ($redirects as $index => $item)
            <div data-id="{{ $index }}" class="mb-4 px-4 py-2 border rounded">
                <div class="flex items-start justify-between gap-4">
                    <div class="grow">
                        <div class="flex items-center gap-2 cursor-move handle">
                            <code class="text-base font-medium bg-stone-100 px-2 py-0.5 rounded">/{{ $item->redirect_entry_path }}</code>
                        </div>
                        <div class="mt-1">
                            Directs to:
                            <a href="{{ $item->redirect_exit_website_page_id ? \App\Website\WebsitePage::find($item->redirect_exit_website_page_id)?->getURL() : $item->redirect_exit_url }}"
                               class="text-blue-500 hover:underline"
                               target="_blank">
                                {{ $item->redirect_exit_website_page_id ? \App\Website\WebsitePage::find($item->redirect_exit_website_page_id)?->title : $item->redirect_exit_url }}
                            </a>
                        </div>
                    </div>
                    <div class="flex gap-2 shrink-0 my-auto">
                        {{-- <button wire:click="editItem({{ $index }})" class="px-3 py-1 admin-button-transparent-small">Edit</button> --}}
                        <button wire:click="deleteItem({{ $item->id }})" class="px-3 py-1 admin-button-red-small">Delete</button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
