<div>
    @if ($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4 rounded relative" role="alert">
            <strong class="font-bold">Oops!</strong>
            <span class="block sm:inline">{{ $errors->first() }}</span>
        </div>
    @endif

    <div class="mb-4 flex justify-between items-center">
        <h2>Home Page Carousel</h2>
        <button wire:click="$set('showForm', true)" class="px-4 py-2 admin-button-blue">
            <i class="far fa-plus-circle mr-2"></i> Add New Item
        </button>
    </div>

    @if($showForm || $editingIndex !== null)
        <div class="mb-8 p-6 bg-gray-50 rounded-lg border">
            <h3 class="text-xl font-semibold mb-4">{{ $editingIndex !== null ? 'Edit' : 'Add' }} Carousel Item</h3>
            <form wire:submit.prevent="{{ $editingIndex !== null ? 'updateItem' : 'addItem' }}">
                <div class="mb-4">
                    <label class="block mb-2">Title</label>
                    <input type="text" wire:model="newItem.title" class="w-full px-3 py-2 border rounded">
                </div>
                <div class="mb-4">
                    <label class="block mb-2">Image</label>
                    <input type="file" wire:model="tempImage">
                    <div class="mt-2 ml-4 text-sm font-semibold">
                        Required size: 1920x1080
                        <br>
                        JPEG or PNG
                    </div>
                    @if ($tempImage)
                        <img src="{{ $tempImage->temporaryUrl() }}" alt="Preview" class="mt-2 max-w-xs max-h-xs">
                    @elseif ($newItem['website_file_id'])
                        <img src="{{ \App\Website\WebsiteFile::find($newItem['website_file_id'])?->getUrl() }}" alt="Current Image" class="mt-2 max-w-xs max-h-xs">
                    @endif
                </div>
                <div class="mb-4">
                    <label class="block mb-2">Description</label>
                    <textarea wire:model="newItem.description" class="w-full px-3 py-2 border rounded"></textarea>
                </div>

                <div class="mb-4">
                    <label class="block mb-2">Link Type</label>
                    <select wire:model.live="newItem.link_type" class="w-full px-3 py-2 border rounded">
                        <option value="custom">Custom URL</option>
                        <option value="page">Website Page</option>
                    </select>
                </div>

                @if($newItem['link_type'] === 'custom')
                    <div class="mb-4">
                        <label class="block mb-2">Custom URL</label>
                        <input type="url"
                               wire:model="newItem.link"
                               class="w-full px-3 py-2 border rounded"
                               placeholder="https://...">
                    </div>
                @else
                    <div class="mb-4">
                        <label class="block mb-2">Select Page</label>
                        <select wire:model="newItem.website_page_id" class="w-full px-3 py-2 border rounded">
                            <option value="">Select a page...</option>
                            @foreach($websitePages as $page)
                                <option value="{{ $page->id }}">{{ $page->title }}</option>
                            @endforeach
                        </select>
                    </div>
                @endif

                <div class="flex gap-2">
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        {{ $editingIndex !== null ? 'Update' : 'Add' }} Item
                    </button>
                    <button type="button" wire:click="cancelEdit" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    @endif

    <div id="carousel-items" class="space-y-4">
        @foreach($carouselItems as $index => $item)
            <div data-id="{{ $index }}" class="bg-white border border-gray-300 rounded-lg p-6">
                <div class="flex justify-between items-start gap-6">
                    <div class="grow">
                        <div class="flex items-center gap-4 mb-4">
                            <i class="far fa-bars handle cursor-move text-gray-400 hover:text-gray-600"></i>
                            <h3 class="text-lg font-medium">{{ $item['title'] }}</h3>
                        </div>
                        <p class="text-gray-600 mb-4">{{ $item['description'] }}</p>
                        <div class="text-sm text-gray-500 mb-4">
                            @php
                                $linkType = $item['link_type'] ?? (!empty($item['link']) ? 'custom' : 'page');
                            @endphp
                            @if($linkType === 'custom' && !empty($item['link']))
                                Link: <a href="{{ $item['link'] }}" target="_blank" class="text-blue-500 hover:underline">{{ $item['link'] }}</a>
                            @elseif($linkType === 'page' && !empty($item['website_page_id']))
                                Page: {{ \App\Website\WebsitePage::find($item['website_page_id'])?->title }}
                            @else
                                No link set
                            @endif
                        </div>
                        <div class="flex gap-2">
                            <button wire:click="editItem({{ $index }})" class="admin-button-blue-small">
                                <i class="far fa-edit mr-1.5"></i> Edit
                            </button>
                            <button wire:click="deleteItem({{ $index }})" class="admin-button-red-transparent-small">
                                <i class="far fa-trash-alt mr-1.5"></i> Delete
                            </button>
                        </div>
                    </div>
                    @if($item['website_file_id'])
                        <div class="w-64 shrink-0">
                            <img src="{{ \App\Website\WebsiteFile::find($item['website_file_id'])?->getUrl() }}"
                                 alt="{{ $item['title'] }}"
                                 class="w-full h-auto rounded">
                        </div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>
</div>

@push('scripts')
    <script src=""></script>
    <script>
        document.addEventListener('livewire:init', () => {
            let el = document.getElementById('carousel-items');
            new Sortable(el, {
                animation: 150,
                handle: '.handle',
                ghostClass: 'bg-gray-100',
                onEnd: function (evt) {
                    let itemIds = Array.from(el.children).map(child => child.dataset.id);
                    @this.
                    call('reorder', itemIds);
                },
            });
        });
    </script>
@endpush 