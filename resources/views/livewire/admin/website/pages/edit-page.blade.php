<div>
    <!-- Single form for everything -->
    <form action="{{ route('admin.website.pages.update', $page) }}" method="POST" class="space-y-8">
        @csrf
        @method('PUT')
        
        <div class="max-w-[800px] mr-auto space-y-4">
            <!-- Title Input -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700">Page Title</label>
                <input type="text" 
                    id="title" 
                    name="title"
                    wire:model.live="title" 
                    class="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
            </div>

            <!-- URL Preview -->
            <div class="flex flex-row space-x-6">
                <div class="grow" x-data="{ isEditable: false }">
                    <label for="slug" class="block text-sm font-medium text-gray-700">URL</label>
                    <div class="mt-1 flex rounded-md">
                        <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 sm:text-sm">
                            {{ auth()->user()->account->domain }}/page/
                        </span>
                        <input
                            type="text"
                            id="slug"
                            name="url_title"
                            x-ref="slug"
                            wire:model.live="slug"
                            :class="{
                                'text-gray-400': !isEditable,
                                'text-gray-900': isEditable,
                                'border-red-300 focus:ring-red-500 focus:border-red-500': '{{ $slugError }}',
                                'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500': '{{ !$slugError }}'
                            }"
                            class="block w-full min-w-0 flex-1 sm:text-sm rounded-l-none! rounded-r-none! border-r-0"
                            :readonly="!isEditable"
                        >
                        <button
                            type="button"
                            class="inline-flex items-center px-3 border border-l-0 border-gray-300 bg-gray-50 text-xs font-medium rounded-r-md text-gray-700 hover:bg-gray-100 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            @click="isEditable = !isEditable"
                        >
                            <span x-text="isEditable ? 'Lock' : 'Edit'"></span>
                        </button>
                    </div>
                    @if($slugError)
                        <p class="mt-1 text-sm text-red-600">{{ $slugError }}</p>
                    @endif
                </div>

                <!-- Status Dropdown -->
                <div>
                    <h3 class="text-sm font-medium text-gray-700">Page Status</h3>
                    <select 
                        name="status"
                        wire:model="status" 
                        class="mt-1 block flex-1 rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <option value="public">Public</option>
                        <option value="draft">Draft</option>
                        <option value="archived">Archived</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="flex justify-start mt-4">
            <button type="submit" class="admin-button-blue" onclick="handleContentSubmit(event)">
                Save Page
            </button>
        </div>

        <div wire:ignore>
            <input id="x" type="hidden" name="content">
            <input type="hidden" name="json_content">
            <trix-editor input="x" class="min-h-[300px] border border-gray-400 rounded-lg"></trix-editor>
        </div>
    </form>
</div>

<script>
    function handleContentSubmit(event) {
        event.preventDefault();
        const form = event.target.closest('form');
        const content = document.getElementById('x').value;
        const json_content = JSON.stringify(document.querySelector('trix-editor').editor);
        
        form.querySelector('input[name="content"]').value = content;
        form.querySelector('input[name="json_content"]').value = json_content;
        form.submit();
    }

    // Override the default heading level
    addEventListener('trix-before-initialize', function (event) {
        const {config} = Trix;

        config.blockAttributes.heading2 = {
            tagName: "h2",
            terminal: true,
            breakOnReturn: true,
            group: false
        };
    });

    addEventListener('trix-initialize', function (event) {
        setTimeout(() => {
            try {
                const trixData = {!! $page->trix ?: 'null' !!};
                if (trixData) {
                    event.target.editor.loadJSON(trixData);
                }
            } catch (error) {
                console.error('Failed to load Trix editor content:', error);
            }
        }, 1000); // 1000ms delay
    });

    // Your existing Trix initialization code...
</script> 