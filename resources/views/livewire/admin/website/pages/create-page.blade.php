<div>
    <form
            x-data
            {{--        wire:submit="save"--}}
            x-on:submit.prevent="
            const content = document.getElementById('x').value;
            const json_content = JSON.stringify(document.querySelector('trix-editor').editor);
            await $wire.set('content', content);
            await $wire.set('json_content', json_content);
            setTimeout(() => {
                $wire.save();
            }, 1000);
        "
            class="mt-4"
    >
        <div class="max-w-[800px] mr-auto space-y-4">
            <!-- Title Input -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700">Page Title</label>
                <input
                        type="text"
                        id="title"
                        wire:model.live="title"
                        class="mt-1 block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        placeholder="Enter page title"
                >
            </div>

            <div class="flex flex-row space-x-6">
                <!-- URL Preview -->
                <div class="grow" x-data="{ isEditable: false }">
                    <label for="slug" class="block text-sm font-medium text-gray-700">URL</label>
                    <div class="mt-1 flex rounded-md">
                        <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 px-3 text-gray-500 sm:text-sm">
                            {{ auth()->user()->account->domain }}/page/
                        </span>
                        <input
                                type="text"
                                id="slug"
                                x-ref="slug"
                                wire:model.live="slug"
                                :class="{
                                'text-gray-400': !isEditable,
                                'text-gray-900': isEditable,
                                'border-red-300 focus:ring-red-500 focus:border-red-500': '{{ $slugError }}',
                                'border-gray-300 focus:ring-indigo-500 focus:border-indigo-500': '{{ !$slugError }}'
                            }"
                                class="block w-full min-w-0 flex-1 sm:text-sm rounded-l-none! rounded-r-none! border-r-0"
                                :readonly="!isEditable"
                        >
                        <button
                                type="button"
                                class="inline-flex items-center px-3 border border-l-0 border-gray-300 bg-gray-50 text-xs font-medium rounded-r-md text-gray-700 hover:bg-gray-100 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                @click="isEditable = !isEditable"
                        >
                            <span x-text="isEditable ? 'Lock' : 'Edit'"></span>
                        </button>
                    </div>
                    @if($slugError)
                        <p class="mt-1 text-sm text-red-600">{{ $slugError }}</p>
                    @endif
                </div>

                <!-- Status Dropdown -->
                <div>
                    <h3 class="text-sm font-medium text-gray-700">Page Status</h3>
                    <select
                            wire:model="status"
                            class="mt-1 block flex-1 rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    >
                        <option value="public" selected>Public</option>
                        <option value="draft">Draft</option>
                        <option value="archived">Archived</option>
                    </select>
                </div>
            </div>

            <!-- Save Button -->
            <div class="flex justify-start py-4">
                <button
                        type="submit"
                        class="admin-button-blue"
                        wire:loading.attr="disabled"
                        wire:loading.class="opacity-75"
                >
                    <span wire:loading.remove wire:target="save">Create Page</span>
                </button>
            </div>
            <!-- Trix editor -->
            <div wire:ignore>
                <input
                        id="x"
                        type="hidden"
                        name="html_content"
                        wire:model.live.debounce.2000ms="content"
                >
                <trix-editor
                        input="x"
                        class="min-h-[300px] border border-gray-400 rounded-lg"
                ></trix-editor>
            </div>

        </div>
    </form>

    <script>
        // For auto-updating while typing
        addEventListener("trix-change", function (event) {
            const content = document.getElementById('x').value;
            const hiddenInput = document.getElementById('x');
            hiddenInput.value = content;
            hiddenInput.dispatchEvent(new Event('input', {bubbles: true}));
        });

        // Override the default heading level
        addEventListener('trix-before-initialize', function (event) {
            const {config} = Trix;

            config.blockAttributes.heading2 = {
                tagName: "h2",
                terminal: true,
                breakOnReturn: true,
                group: false
            };

            const toolbar = config.toolbar.getDefaultHTML();
            config.toolbar.getDefaultHTML = function () {
                return toolbar.replace('data-trix-attribute="heading1"', 'data-trix-attribute="heading2"');
            };
        });

        // Handle image uploads
        addEventListener("trix-attachment-add", function (event) {
            if (event.attachment.file) {
                const wireId = document.querySelector('[wire\\:id]').getAttribute('wire:id');

                Livewire.find(wireId).upload(
                    'tempImage',
                    event.attachment.file,
                    (uploadedUrl) => {
                        event.attachment.setAttributes({
                            url: uploadedUrl,
                            href: uploadedUrl
                        });
                    },
                    () => {
                        console.error('Upload failed');
                        event.attachment.remove();
                    },
                    (progress) => {
                        event.attachment.setUploadProgress(progress.detail.progress);
                    }
                );
            }
        });
    </script>
</div> 