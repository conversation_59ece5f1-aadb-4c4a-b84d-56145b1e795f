<div>
    @if ($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4 rounded relative" role="alert">
            <strong class="font-bold">Oops!</strong>
            <span class="block sm:inline">{{ $errors->first() }}</span>
        </div>
    @endif

    <div class="flex justify-between items-center mb-6">
        <h2 class="">Quick Links</h2>
        @if($editingIndex === null)
            <button wire:click="$set('showForm', true)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                <i class="far fa-plus-circle mr-2"></i> Add New Link
            </button>
        @endif
    </div>

    <div class="text-gray-600 mb-4 text-base">
        These links will appear in the sidebar of your website that will appear on every page.
    </div>

    @if($showForm || $editingIndex !== null)
        <div class="mb-8 p-6 bg-gray-50 rounded-lg border">
            <h3 class="text-xl font-semibold mb-4">{{ $editingIndex !== null ? 'Edit' : 'Add' }} Link</h3>
            <form wire:submit.prevent="{{ $editingIndex !== null ? 'updateItem' : 'addItem' }}">
                <div class="mb-4">
                    <label class="block mb-2">Title</label>
                    <input type="text" wire:model="newItem.title" class="w-full px-3 py-2 border rounded">
                </div>

                <div class="mb-4">
                    <label class="block mb-2">Link Type</label>
                    <select wire:model.live="newItem.link_type" class="w-full px-3 py-2 border rounded">
                        <option value="custom">Custom URL</option>
                        <option value="page">Website Page</option>
                    </select>
                </div>

                @if($newItem['link_type'] === 'custom')
                    <div class="mb-4">
                        <label class="block mb-2">Custom URL</label>
                        <input type="url" wire:model.live="newItem.link" class="w-full px-3 py-2 border rounded">
                    </div>
                @else
                    <div class="mb-4">
                        <label class="block mb-2">Select Page</label>
                        <select wire:model.live="newItem.website_page_id" class="w-full px-3 py-2 border rounded">
                            <option value="">Select a page...</option>
                            @foreach($websitePages as $page)
                                <option value="{{ $page->id }}">{{ $page->title }}</option>
                            @endforeach
                        </select>
                    </div>
                @endif

                <div class="flex gap-2">
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        {{ $editingIndex !== null ? 'Update' : 'Add' }} Link
                    </button>
                    <button type="button" wire:click="cancelEdit" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    @endif

    <div id="sidebar-links">
        @foreach ($sidebarLinks as $index => $item)
            <div data-id="{{ $index }}" class="mb-4 p-4 border rounded">
                <div class="flex items-start justify-between">
                    <div class="grow">
                        <div class="flex items-center gap-2 cursor-move handle">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                            <h4 class="text-lg font-semibold">{{ $item['title'] }}</h4>
                        </div>
                        <a href="{{ $item['website_page_id'] ? \App\Website\WebsitePage::find($item['website_page_id'])?->getURL() : $item['link'] }}"
                           class="text-blue-500 hover:underline"
                           target="_blank">
                            {{ $item['website_page_id'] ? 'Page: ' . \App\Website\WebsitePage::find($item['website_page_id'])?->title : $item['link'] }}
                        </a>
                        <div class="mt-4">
                            <button wire:click="editItem({{ $index }})" class="px-3 py-1 admin-button-transparent-small">Edit</button>
                            <button wire:click="deleteItem({{ $index }})" class="px-3 py-1 admin-button-red-small ml-2">Delete</button>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>

@push('scripts')
    <script src=""></script>
    <script>
        var el = document.getElementById('sidebar-links');
        var sortable = new Sortable(el, {
            animation: 150,
            handle: '.handle',
            ghostClass: 'bg-gray-100',
            onEnd: function (evt) {
                var itemIds = Array.from(el.children).map(child => child.dataset.id);
                @this.
                call('reorder', itemIds);
            },
        });
    </script>
@endpush 