<div>
    @if ($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4 rounded relative" role="alert">
            <strong class="font-bold">Oops!</strong>
            <span class="block sm:inline">{{ $errors->first() }}</span>
        </div>
    @endif

    <div class="text-gray-600 mb-4">
        Configure special links that will appear prominently on your website. Each link can be either a custom URL or a website page.
    </div>

    <div class="space-y-6">
        @foreach($links as $key => $link)
            <div class="bg-gray-50 rounded-lg p-6 border">
                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $link['title'] }}</h3>
                <div class="text-gray-600 mb-4 text-sm">{{ $link['description'] }}</div>

                <div class="space-y-4">
                    <div class="mb-4">
                        <label class="block mb-2 text-sm">Link Type</label>
                        <select wire:model.live="links.{{ $key }}.link_type" class="w-full px-3 py-2 border rounded">
                            <option value="custom">Custom URL</option>
                            <option value="page">Website Page</option>
                        </select>
                    </div>

                    @if($link['link_type'] === 'custom')
                        <div class="mb-4">
                            <label class="block mb-2 text-sm">Custom URL</label>
                            <input type="url"
                                   wire:model="links.{{ $key }}.link"
                                   class="w-full px-3 py-2 border rounded"
                                   placeholder="https://...">
                        </div>
                    @else
                        <div class="mb-4">
                            <label class="block mb-2 text-sm">Select Page</label>
                            <select wire:model="links.{{ $key }}.website_page_id" class="w-full px-3 py-2 border rounded">
                                <option value="">Select a page...</option>
                                @foreach($websitePages as $page)
                                    <option value="{{ $page->id }}">{{ $page->title }}</option>
                                @endforeach
                            </select>
                        </div>
                    @endif

                    <div class="text-sm text-gray-500 mb-4">
                        @if($link['link_type'] === 'custom' && $link['link'])
                            Current URL: <a href="{{ $link['link'] }}" target="_blank" class="text-blue-500 hover:underline">{{ $link['link'] }}</a>
                        @elseif($link['link_type'] === 'page' && $link['website_page_id'])
                            Current Page: {{ \App\Website\WebsitePage::find($link['website_page_id'])?->title }}
                        @else
                            No link set
                        @endif
                    </div>

                    @error($key)
                    <div class="text-red-500 text-sm">{{ $message }}</div>
                    @enderror

                    <div class="flex gap-2">
                        <button type="button"
                                wire:click="saveLink('{{ $key }}')"
                                class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            Save {{ $link['title'] }}
                        </button>
                        <button type="button"
                                wire:click="clearLink('{{ $key }}')"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                            Clear
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div> 