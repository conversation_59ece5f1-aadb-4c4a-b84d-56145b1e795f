<div>
    @if ($notification)
        <div class="{{ $notification['type'] === 'success' ? 'bg-green-200' : 'bg-red-200' }} rounded-lg mb-4" id="notification">
            <div class="max-w-7xl mx-auto py-3 px-3 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between flex-wrap">
                    <div class="w-0 flex-1 flex items-center">
                        <span class="flex p-2 rounded-lg {{ $notification['type'] === 'success' ? 'text-green-600' : 'text-red-600' }}">
                            @if ($notification['type'] === 'success')
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            @else
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            @endif
                        </span>
                        <p class="ml-3 font-medium {{ $notification['type'] === 'success' ? 'text-green-700' : 'text-red-700' }} truncate">
                            {{ $notification['message'] }}
                        </p>
                    </div>
                    <div class="order-2 shrink-0 sm:order-3 sm:ml-3">
                        <button wire:click="dismissNotification" type="button" class="-mr-1 flex p-2 rounded-md hover:bg-{{ $notification['type'] === 'success' ? 'green' : 'red' }}-500 {{ $notification['type'] === 'success' ? 'text-green-600' : 'text-red-600' }} hover:text-white focus:outline-hidden focus:ring-2 focus:ring-white sm:-mr-2">
                            <span class="sr-only">Dismiss</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if ($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-4 rounded relative" role="alert">
            <strong class="font-bold">Oops!</strong>
            <span class="block sm:inline">{{ $errors->first() }}</span>
        </div>
    @endif

    <form wire:submit.prevent="save">
        <div class="space-y-6">
            <div>
                These are links to your social media profiles. These will create shortcuts on the website for people to see. Leave blank if you don't want to display them.
            </div>
            <hr/>
            <div>
                <label class="block text-sm font-medium text-gray-700">Facebook URL</label>
                <div class="mt-1">
                    <input type="url" wire:model="facebook" class="w-full px-3 py-2 border rounded">
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">YouTube URL</label>
                <div class="mt-1">
                    <input type="url" wire:model="youtube" class="w-full px-3 py-2 border rounded">
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Vimeo URL</label>
                <div class="mt-1">
                    <input type="url" wire:model="vimeo" class="w-full px-3 py-2 border rounded">
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700">Twitter URL</label>
                <div class="mt-1">
                    <input type="url" wire:model="twitter" class="w-full px-3 py-2 border rounded">
                </div>
            </div>

            <div>
                <button type="submit" class="admin-button-blue">
                    Save Changes
                </button>
            </div>
        </div>
    </form>
</div> 