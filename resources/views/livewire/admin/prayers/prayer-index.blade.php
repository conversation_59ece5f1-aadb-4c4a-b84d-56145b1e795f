<div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">
    <aside class="lg:col-span-3">

        <div class="flex px-3 py-3 align-middle justify-between border-b admin-border-color">
            <div class="text-xl my-auto font-medium text-gray-900">
                Folders
            </div>
            <button type="button" class="admin-button-blue-small text-sm" onclick="openSidebar('{{ route('admin.prayers.folders.create') }}')">
                <x-heroicon-s-plus class="w-4 mr-1"/>
                Folder
            </button>
        </div>
        @php
            $requests_active = 'bg-green-50 border-green-500 text-green-700 hover:bg-green-50 hover:text-green-700 group border-l-4 px-1 py-3 flex items-center text-base font-medium';
            $requests_normal = 'cursor-pointer border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-1 py-3 flex items-center text-base font-medium';
            $cat_active = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-1 py-3 flex items-center text-base font-medium';
            $cat_normal = 'cursor-pointer border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-1 py-3 flex items-center text-base font-medium';
        @endphp
        @if(auth()->user()->account->hasFeature('account.setting.prayers.allow_requests'))
            <a href="{{ route('admin.prayers.index') }}?view_requests=true"
               class="flex flex-row justify-between {{ $view_requests ? $requests_active : $requests_normal }}"
            >
                <div class="flex flex-row truncate">
                    <span class="truncate  flex flex-row {{ \App\Prayers\Prayer::visibleTo(auth()->user())->isNotExpired()->isNotApproved()->isARequest()->count() > 0 ? 'text-gray-800 font-medium' : 'text-gray-500 font-light' }}">
                        <x-heroicon-o-clock class="w-5 h-5 my-auto mr-2"/> Pending Member Requests
                    </span>
                </div>
                @if(\App\Prayers\Prayer::visibleTo(auth()->user())->isARequest()->isNotApproved()->isActive()->isNotExpired()->count() == 0)
                    <div class="mr-2 bg-gray-200 text-white rounded-full px-1.5 py-0.5 text-sm">
                        0
                    </div>
                @else
                    <div class="mr-2 bg-green-300 rounded-full px-1.5 py-0.5 text-sm">
                        {{ \App\Prayers\Prayer::visibleTo(auth()->user())->isARequest()->isNotApproved()->isNotExpired()->isActive()->count() }}
                    </div>
                @endif
            </a>
        @endif
        <a href="{{ route('admin.prayers.index') }}"
           class="flex flex-row justify-between {{ !$selected_folder && !$view_requests ? $cat_active : $cat_normal }}"
        >
            <div class="flex flex-row pl-2">
                <span class="truncate text-gray-500 font-light">(No Folder)</span>
            </div>
            <div class="mr-2 bg-blue-100 rounded-full px-1.5 py-0.5 text-sm">
                {{ \App\Prayers\Prayer::visibleTo(auth()->user())->IsViewableToMembers()->NoFolder()->count() }}
            </div>
        </a>
        <nav id="folders-list" class="">
            @forelse($folders as $folder)
                <div onclick="window.location.href='{{ route('admin.prayers.folders.index', $folder) }}'"
                     data-prayerFolderId="{{ $folder->id }}"
                     class="handle cursor-pointer flex flex-row justify-between pl-2 {{ ($selected_folder && $selected_folder->id == $folder->id) ? $cat_active : $cat_normal }} {{ $loop->last ? 'rounded-bl-md' : null }}"
                     id="droppable-{{ $folder->id }}"
                >
                    <div class="flex flex-col truncate">
                        <div class="flex flex-row truncate">
                            <span class="truncate text-black">
                                {{ $folder->name }}
                            </span>
                        </div>
                        @if($folder->is_hidden)
                            <div>
                                <span class="bg-gray-200 text-xs px-2 py-0.5 rounded-lg">
                                    Hidden
                                </span>
                            </div>
                        @endif
                    </div>
                    <div class="flex flex-row">
                        @if($selected_folder && $selected_folder->id == $folder->id)
                            <button onclick="event.stopPropagation(); openSidebar('{{ route('admin.prayers.folders.edit', $folder) }}')"
                                    class="cursor-pointer mr-2 bg-white text-gray-600 rounded-md border border-gray-300 px-1.5 py-0.5 text-sm shrink">
                                Edit
                            </button>
                        @endif
                        <div class="mr-2 bg-blue-100 rounded-full px-1.5 py-0.5 text-sm">
                            {{ $folder->prayers()->IsViewableToMembers()->count() }}
                        </div>
                    </div>
                </div>
            @empty
                <div class="flex-1 mt-2 sm:mt-12">
                    <div class="mx-4 text-center text-sm mb-20 text-gray-400">
                        Use folders to organize prayer requests into categories, types or anything else.
                        <br><br>
                        Folders are <strong>not</strong> required.
                    </div>
                </div>
            @endforelse

            @if($folders?->count() > 0)
                <div class="flex-1 mt-2 py-6 border-t border-gray-300">
                    <div class="mx-4 text-center text-sm text-gray-400">
                        You can drag &amp; drop folders to change their sort order.
                    </div>
                </div>
            @endif
        </nav>

    </aside>


    <div class="lg:col-span-9">

        {{-- We'll show this later after we remove the "welcome to the new feature" note.  Having two purple notes when viewing a page feels cluttered. --}}
        @if(false && !$selected_folder && !$view_requests)
            <div class="flex flex-row bg-purple-50 border border-purple-500 rounded-md text-purple-600 text-sm justify-between mt-4 mx-4 px-4 py-2">
                Prayer requests not in a folder will appear listed below folders in the mobile and web app.
            </div>
        @endif

        @if(!$view_requests)
            <div class="sm:flex sm:items-center p-4">
                <div class="flex-1">
                    <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                        <div class="flex-1 relative rounded-md">
                            <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <input wire:model.live="query_term" wire:keydown="$dispatch('queryChanged')" autocomplete="off" class="standard-input admin-border-color w-full pl-10 pr-3 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900" placeholder="Search..." type="search">
                            <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                                <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                                    <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                                    Loading
                                </kbd>
                            </div>
                        </div>
                    </div>
                    <div class="mt-1">
                        <label>
                            <input type="checkbox" name="show_expired" id="show_expired" wire:model.live="show_expired">
                            Show expired requests
                        </label>
                    </div>
                </div>
            </div>
        @endif

        {{-- Show specific table depending on if we're searching or not --}}

        @if($view_requests)
            <div class="my-4 bg-purple-50 border border-purple-500 rounded-md text-purple-600 text-sm mx-4 px-4 py-2">
                You can allow members to submit prayer requests in the <a href="{{ route('admin.prayers.settings') }}">prayer list settings</a>.
            </div>
        @endif


        @if($prayers->count() >= 50)
            <div class="px-4 py-4 border-t border-gray-300">
                {{ $prayers->withQueryString()->onEachSide(0)->links() }}
            </div>
        @endif

        <table class="table min-w-full">
            <thead class=" bg-gray-800 bg-opacity-90 uppercase">
            <tr class=" text-white">
                <th scope="col" class="py-2 px-3 text-sm text-left font-semibold backdrop-blur-xs backdrop-filter">Title</th>
                {{-- If we're searching, show the folder this prayer belongs to --}}
                @if($query_term)
                    <th scope="col" class="py-2 px-2 text-sm text-left font-semibold backdrop-blur-xs backdrop-filter">Folder</th>
                @endif
                {{-- If we're viewing Prayer Requests from members, only show the created at date --}}
                @if($view_requests)
                    <th scope="col" class="py-2 px-2 text-sm text-left font-semibold backdrop-blur-xs backdrop-filter">Requested By</th>
                    <th scope="col" class="py-2 px-2 text-sm text-left font-semibold backdrop-blur-xs backdrop-filter">Requested On</th>
                @else
                    <th scope="col" class="py-2 px-2 text-sm text-left font-semibold backdrop-blur-xs backdrop-filter">Last Updated ↓</th>
                    <th scope="col" class="py-2 px-2 text-sm text-left font-semibold backdrop-blur-xs backdrop-filter">Expires</th>
                @endif
            </tr>
            </thead>
            <tbody id="prayers-list" class="bg-white divide-y divide-gray-200">
            @forelse($prayers as $prayer)
                <tr data-prayerId="{{ $prayer->id }}" data-sortid="{{ $prayer->sort_id }}"
                    class="w-full hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}"
                    draggable="true">
                    <td class="py-3 px-3">
                        <x-heroicon-s-bars-3 class="hidden w-5 h-5 draggable-item mr-2 cursor-move text-gray-500"
                                             data-prayerId="{{ $prayer->id }}"/>
                        <a class="text-base" href="{{ route('admin.prayers.edit', $prayer) }}">
                            {{ $prayer->title }}
                            <div>
                                @if($prayer->updates()->count())
                                    <span class="text-sm text-white bg-purple-500 rounded-md px-2 py-0.5">{{ $prayer->updates()->count() }} Updates</span>
                                @else
                                    <span class="text-sm text-gray-300 bg-gray-50 rounded-md px-2 py-0.5">0 Updates</span>
                                @endif
                                @if($prayer->users()->count())
                                    <span class="text-sm text-white bg-blue-500 rounded-md px-2 py-0.5">{{ $prayer->users()->count() }} User Tags</span>
                                @else
                                    <span class="text-sm text-gray-300 bg-gray-50 rounded-md px-2 py-0.5">0 User Tags</span>
                                @endif
                            </div>
                        </a>
                    </td>
                    {{-- If we're searching, show the folder this prayer belongs to --}}
                    @if($query_term)
                        <td class="py-3 px-2 truncate" style="max-width: 10em;">
                            <span class="px-2 py-0.5 rounded-md {{ $prayer->folder ? 'text-black bg-gray-200' : 'text-gray-400 bg-gray-100' }}">
                                {{ $prayer->folder?->name ?: 'No Folder' }}
                            </span>
                        </td>
                    @endif
                    {{-- If we're viewing Prayer Requests from members, only show the created at date --}}
                    @if($view_requests)
                        <td class="py-3 px-2 whitespace-nowrap text-black text-base">
                            {{ $prayer->createdBy?->name }}
                        </td>
                        <td class="py-3 px-2 whitespace-nowrap text-black text-base">
                            {{ $prayer->created_at->format('M d, Y') }}
                        </td>
                    @else
                        <td class="py-3 px-2 whitespace-nowrap text-black text-base">
                            {{ $prayer->last_updated_at?->format('M d, Y') }}
                        </td>
                        <td class="py-2 px-2 whitespace-nowrap text-black text-base">
                            @if($prayer->expires_at)
                                @if($prayer->expires_at < now())
                                    <span class="px-2 py-1 rounded-md bg-red-500 text-white text-xs">
                                        expired
                                    </span>
                                @else
                                    <span class="px-2 py-1 rounded-md {{ $prayer->expires_at < now() ? 'bg-red-200' : null }}">
                                        {{ $prayer->expires_at->format($prayer->expires_at->format('Y') == now()->format('Y') ? 'M d' : 'M d, Y') }}
                                    </span>
                                @endif
                            @else
                                <span class="bg-gray-100 rounded-md px-2 py-1 text-gray-400">never</span>
                            @endif
                        </td>
                    @endif
                </tr>
            @empty
                <tr>
                    <td colspan="100%" class="text-center p-5">
                        <span class="">No results found.</span>
                    </td>
                </tr>
            @endforelse
            </tbody>
        </table>

        <div class="p-4 border-t border-gray-300">
            {{ $prayers->withQueryString()->onEachSide(0)->links() }}
        </div>
    </div>
</div>

<script src=""></script>

@script

<script>

    new Sortable(document.getElementById('folders-list'), {
        group: 'shared', // set both lists to same group
        handle: '.handle', // handle's class
        animation: 150,
        onEnd: function (event) {
            console.log('new id: ' + event.newIndex);
            console.log('old id: ' + event.oldIndex);
            console.log(' id: ' + event.item.dataset.prayerfolderid);

            $wire.updateFolderSortOrder(event.item.dataset.prayerfolderid, event.newIndex, event.oldIndex);

            // var itemEl = event.item;  // dragged HTMLElement
            // event.to;    // target list
            // event.from;  // previous list
            // event.oldIndex;  // element's old index within old parent
            // event.newIndex;  // element's new index within new parent
            // event.oldDraggableIndex; // element's old index within old parent, only counting draggable elements
            // event.newDraggableIndex; // element's new index within new parent, only counting draggable elements
            // event.clone // the clone element
            // event.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
        },
        onAdd: function (event) {
            console.log(event);
        }
    });
</script>
@endscript
