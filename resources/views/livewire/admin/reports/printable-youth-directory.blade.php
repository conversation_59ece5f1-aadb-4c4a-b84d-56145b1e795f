<div>
    <div class="" x-data="">

        <h1>
            📘 Youth Directory
        </h1>
        <hr class="mb-4">

        <div class="print:hidden">
            <div class="grid grid-cols-2 md:grid-cols-3 space-x-4 my-4">
                <div class="col col-span-1">
                    <div class="relative rounded-md border-gray-300 border px-3 py-2 bg-white hover:bg-gray-100">
                        <div wire:loading class="hidden absolute inset-y-0 right-0 py-1 pr-1.5">
                            <kbd class="inline-flex items-center bg-green-100 border border-gray-300 rounded px-2 py-1 text-sm font-medium text-gray-600">
                                <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                                Loading
                            </kbd>
                        </div>
                        <span class="font-medium">Users Selected</span>
                        <h3 class="text-3xl font-bold">{{ $users->count() }}</h3>
                        <span class="text-base">{{ $users->unique('family_id')->count() }} Family Units</span>
                    </div>
                </div>
            </div>

            <div wire:loading.remove class="flex flex-row space-x-4 mb-4">
                <button wire:click="download('pdf')" class="admin-button-blue">
                    <x-heroicon-s-document-arrow-down class="w-5 mr-2"/>
                    Download PDF
                </button>
            </div>
            <div wire:loading class="hidden flex flex-row space-x-4 mb-4">
                <button class="admin-button-blue opacity-25">
                    <x-heroicon-s-arrow-path class="w-5 mr-2 animate-spin"/>
                    Download PDF
                </button>
            </div>

            <div class="bg-purple-100 text-black border border-purple-500 rounded px-2 py-2 mb-4 text-sm">
                Select the filters below to narrow down your user selection for this report.
            </div>

            <div class="grid grid-cols-2 gap-x-4 border border-gray-400 rounded-md p-4">
                <div>
                    <div class="custom-control custom-switch">
                        <input type="checkbox" id="asterisk_unbaptized" wire:model.live="asterisk_unbaptized" value="1" class="custom-control-input type_checkbox" {{ $asterisk_unbaptized ? 'checked="checked"' : null }}/>
                        <label class="font-semibold" for="asterisk_unbaptized"> Add asterisk next to names of <em>unbaptized</em> users.</label>
                    </div>
                    <div class="custom-control custom-switch">
                        <input type="checkbox" id="asterisk_baptized" wire:model.live="asterisk_baptized" value="1" class="custom-control-input type_checkbox" {{ $asterisk_baptized ? 'checked="checked"' : null }}/>
                        <label class="font-semibold" for="asterisk_baptized"> Add asterisk next to names of <em>baptized</em> users.</label>
                    </div>
                </div>

            </div>


            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4 mt-4 border border-gray-400 rounded-md p-4">
                <div>
                    <div class="flex-1" wire:ignore>
                        <span class="font-semibold">Include Groups</span>
                        <select name="groups[]" id="groups" class="js-choice1" multiple="multiple" autocomplete=off>
                            @foreach ($groups as $group)
                                <option value="{{ $group->id }}" {{ in_array($group->id, $filter_groups) ? 'selected' : null }}>{{ $group->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-1 mt-2" wire:ignore>
                        <span class="font-semibold">Exclude Groups</span>
                        <select name="exclude_groups[]" id="exclude_groups" class="js-choice2" multiple="multiple" autocomplete=off>
                            @foreach ($groups as $group)
                                <option value="{{ $group->id }}" {{ in_array($group->id, $filter_exclude_groups) ? 'selected' : null }}>{{ $group->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-1 mt-2" wire:ignore>
                        <span class="font-semibold">Grades to Include</span>
                        <select name="grades[]" id="grades" class="js-choice3" multiple="multiple" autocomplete=off>
                            @foreach ($grades as $grade)
                                <option value="{{ $grade->id }}" {{ in_array($grade->id, $filter_grades) ? 'selected' : null }}>{{ $grade->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex-1 mt-2" wire:ignore>
                        <span class="font-semibold">Age by birthdate, between:</span>
                        <br>
                        <div class="flex flex-row">
                            <input wire:model.live="options.birthdate_start" id="options[birthdate_start]" type="date"/>
                            <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('birthdate_start')">Clear</span>
                        </div>
                        <div class="flex flex-row">
                            <input wire:model.live="options.birthdate_end" id="options[birthdate_end]" type="date" class="mt-2"/>
                            <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('birthdate_end')">Clear</span>
                        </div>
                    </div>
                    <div class="flex-1 mt-4" wire:ignore>
                        <span class="font-semibold">Membership Date, between:</span>
                        <br>
                        <div class="flex flex-row">
                            <input wire:model.live="options.membership_start" id="options[membership_start]" type="date"/>
                            <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('membership_start')">Clear</span>
                        </div>
                        <div class="flex flex-row">
                            <input wire:model.live="options.membership_end" id="options[membership_end]" type="date" class="mt-2"/>
                            <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('membership_end')">Clear</span>
                        </div>
                    </div>
                    <div class="flex-1 mt-4" wire:ignore>
                        <span class="font-semibold">Baptism Date, between:</span>
                        <br>
                        <div class="flex flex-row">
                            <input wire:model.live="options.baptism_start" id="options[baptism_start]" type="date"/>
                            <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('baptism_start')">Clear</span>
                        </div>
                        <div class="flex flex-row">
                            <input wire:model.live="options.baptism_end" id="options[baptism_end]" type="date" class="mt-2"/>
                            <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('baptism_end')">Clear</span>
                        </div>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="">
                        <label for="" class="font-semibold">Other Filters:</label>

                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[adults_only]" type="checkbox" value="adults_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'adults_only'))>
                            <label for="columns[adults_only]">Adults Only</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[children_only]" type="checkbox" value="children_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'children_only'))>
                            <label for="columns[children_only]">Children Only</label>
                        </div>
                        <hr class="my-1"/>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[men_only]" type="checkbox" value="men_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'men_only'))>
                            <label for="columns[men_only]">Men Only</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[women_only]" type="checkbox" value="women_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'women_only'))>
                            <label for="columns[women_only]">Women Only</label>
                        </div>
                        <hr class="my-1"/>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[is_baptized]" type="checkbox" value="is_baptized" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_baptized'))>
                            <label for="columns[is_baptized]">Is Baptized</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[is_not_baptized]" type="checkbox" value="is_not_baptized" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_not_baptized'))>
                            <label for="columns[is_not_baptized]">Is <em>not</em> Baptized</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[date_background_check]" type="checkbox"
                                   value="date_background_check" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'date_background_check'))>
                            <label for="columns[date_background_check]">Completed Background Check</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[can_teach]" type="checkbox" value="can_teach" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'can_teach'))>
                            <label for="columns[can_teach]">Approved to Teach</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[can_lead]" type="checkbox" value="can_lead" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'can_lead'))>
                            <label for="columns[can_lead]">Approved to Lead</label>
                        </div>
                        <hr class="my-1"/>
                        <div class="form-check form-check-inline">
                            <input wire:model.live="columns" id="columns[head_of_household_only]" type="checkbox"
                                   value="head_of_household_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'head_of_household_only'))>
                            <label for="columns[head_of_household_only]">Head of Household Only</label>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label for="" class="font-semibold">Family roles to include:</label>
                        <hr class="my-1"/>
                        @foreach (\App\Users\User::$family_roles as $key => $value)
                            <div class="form-check form-check-inline">
                                <input wire:model.live="family_roles" id="family_roles[{{ $key }}]" type="checkbox" value="{{ $key }}"/>
                                <label for="family_roles[{{ $key }}]">{{ $value }}</label>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <script>
            var initChoiceJs = function () {
                jschoice1 = new Choices('.js-choice1', {
                    removeItemButton: true,
                    allowHTML: false,
                    items: {!! json_encode($filter_groups_js) !!},
                });
                jschoice2 = new Choices('.js-choice2', {
                    removeItemButton: true,
                    allowHTML: false,
                    items: {!! json_encode($filter_exclude_groups_js) !!},
                });
                jschoice3 = new Choices('.js-choice3', {
                    removeItemButton: true,
                    allowHTML: false,
                    items: {!! json_encode($filter_grades_js) !!},
                });

                jschoice1.passedElement.element.addEventListener(
                    'addItem',
                    function (event) {
                        Livewire.dispatch('addGroupFilter', {group_id: event.detail.value});
                        // do something creative here...
                        // console.log('item added');
                        // console.log(event.detail.id);
                        // console.log(event.detail.value);
                        // console.log(event.detail.label);
                        // console.log(event.detail.customProperties);
                        // console.log(event.detail.groupValue);
                    },
                    false,
                );
                jschoice1.passedElement.element.addEventListener(
                    'removeItem',
                    function (event) {
                        Livewire.dispatch('removeGroupFilter', {remove_group_id: event.detail.value});
                    },
                    false,
                );

                jschoice2.passedElement.element.addEventListener(
                    'addItem',
                    function (event) {
                        Livewire.dispatch('addGroupExcludeFilter', {group_id: event.detail.value});
                    },
                    false,
                );
                jschoice2.passedElement.element.addEventListener(
                    'removeItem',
                    function (event) {
                        Livewire.dispatch('removeGroupExcludeFilter', {remove_group_id: event.detail.value});
                    },
                    false,
                );

                jschoice3.passedElement.element.addEventListener(
                    'addItem',
                    function (event) {
                        Livewire.dispatch('addGrade', {grade_id: event.detail.value});
                    },
                    false,
                );
                jschoice3.passedElement.element.addEventListener(
                    'removeItem',
                    function (event) {
                        Livewire.dispatch('removeGrade', {grade_id: event.detail.value});
                    },
                    false,
                );
            }
            document.addEventListener('contentChanged', (event) => {
                console.log('changed');
                initChoiceJs();
            });
            document.addEventListener('DOMContentLoaded', event => {
                initChoiceJs();
            });
        </script>
    </div>
</div>
