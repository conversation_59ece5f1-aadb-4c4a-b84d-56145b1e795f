{{--
    TailwindCSS classes that we need for the Visitor class.
    For <PERSON><PERSON><PERSON> to pick up during compilation.

    text-black-800
    hover:bg-black-100
    bg-black-600
    bg-black-100
    text-black-900
    border-black-200
    border-black-500

    text-green-800
    hover:bg-green-100
    bg-green-600
    bg-green-100
    text-green-900
    border-green-200
    border-green-500

    text-red-800
    hover:bg-red-100
    bg-red-600
    bg-red-100
    text-red-900
    border-red-200
    border-red-500

    text-yellow-800
    hover:bg-yellow-100
    bg-yellow-600
    bg-yellow-100
    text-yellow-900
    border-yellow-200
    border-yellow-500

    text-orange-800
    hover:bg-orange-100
    bg-orange-600
    bg-orange-100
    text-orange-900
    border-orange-200
    border-orange-500
    `
    text-blue-800
    hover:bg-blue-100
    bg-blue-600
    bg-blue-100
    text-blue-900
    border-blue-200
    border-blue-500

    text-purple-800
    hover:bg-purple-100
    bg-purple-600
    bg-purple-100
    text-purple-900
    border-purple-200
    border-purple-500
    --}}
<div class="lg:mt-6 lg:grid lg:grid-cols-12 lg:gap-x-5">
    <aside class="py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-2">
        <nav class="space-y-1">
            <!-- Current: "bg-gray-50 text-blue-700 hover:text-blue-700 hover:bg-white", Default: "text-gray-900 hover:text-gray-900 hover:bg-gray-50" -->
            <a class="bg-gray-50 text-blue-600 hover:text-blue-700 hover:bg-white group rounded-md px-3 py-2 flex items-center text-sm font-medium" aria-current="page">
                <svg class="text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                <span class="truncate"> Categories </span>
            </a>
        </nav>
    </aside>

    <div class="space-y-6 sm:px-6 lg:px-0 lg:col-span-10">
        <div class="admin-section-border sm:rounded-md sm:overflow-hidden">
            <div class="bg-white py-6 px-4 space-y-6 sm:p-6">
                <div class="flex flex-row justify-between">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Visitor Categories</h3>
                        <p class="mt-1 text-sm text-gray-500">You can change and re-sort the visitor categories.</p>
                    </div>
                    <div>
                        <button wire:click="add" class="admin-button-blue text-sm">
                            <x-heroicon-o-plus-small class="w-5 mr-1"/>
                            Add
                        </button>
                    </div>
                </div>

                <div class="admin-section-border overflow-hidden sm:rounded-md">
                    <ul role="list" class="divide-y divide-gray-200">

                        @foreach($statuses as $index => $status)
                            <li>
                                <a class="block hover:bg-gray-50">
                                    <div class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-2 text-base font-medium truncate">
                                                @if($status->hasColor())
                                                    <span class="rounded-full p-1.5 bg-{{ $status->tw_color }}-600 border-4 border-{{ $status->tw_color }}-200 mr-2"></span>
                                                @endif
                                                <input type="text" wire:model.debounce.500ms="statuses.{{ $index }}.name" class="{{ $status->hasColor() ? 'text-' . $status->tw_color . '-900' : null }}"/>
                                                <select wire:model.live="statuses.{{ $index }}.tw_color">
                                                    @foreach(\App\Visitors\Status::$tw_color_options as $color)
                                                        <option value="{{ $color }}">{{ $color }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="ml-2 shrink-0 flex">
                                                <div class="inline-flex shadow-2xs rounded-md">
                                                    @if(!$loop->last)
                                                        <button wire:click="sortDown({{ $status->id }})" type="button" class="-ml-px relative inline-flex items-center px-4 py-2 {{ $loop->first ? 'rounded-r-md' : null }} rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-10 focus:outline-hidden focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                            <x-heroicon-s-arrow-down class="w-3"/>
                                                        </button>
                                                    @endif
                                                    @if(!$loop->first)
                                                        <button wire:click="sortUp({{ $status->id }})" type="button" class="-ml-px relative inline-flex items-center px-4 py-2 {{ $loop->last ? 'rounded-l-md' : null }}  rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-10 focus:outline-hidden focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                            <x-heroicon-s-arrow-up class="w-3"/>
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-2 sm:flex sm:justify-between">
                                            <div class="sm:flex">
                                                <p class="flex items-center text-sm text-gray-500">
                                                    <svg class="shrink-0 mr-1.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                                                    </svg>
                                                    {{ \App\Visitors\Visitor::visibleTo(auth()->user())->withStatusId($status->id)->count() }} Visitors Currently
                                                </p>
                                            </div>
                                            <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                                <button wire:click="delete({{ $status->id }})" type="button" class="admin-button-red-small items-center px-4 py-2 text-sm text-gray-700 bg-transparent hover:bg-red-500 text-red-500 hover:text-white focus:z-10 focus:outline-hidden focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                                    <x-heroicon-s-trash class="w-3"/>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
            {{--            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">--}}
            {{--                <button type="submit" class="bg-blue-600 border border-transparent rounded-md shadow-2xs py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Save</button>--}}
            {{--            </div>--}}
        </div>
    </div>
</div>
