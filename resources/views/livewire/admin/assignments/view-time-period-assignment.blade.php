<tr class="hover:bg-gray-50">
    <td class="w-full max-w-0 py-4 pl-4 pr-3 text-base font-light text-gray-700 sm:w-auto sm:max-w-none sm:pl-6">
        <span>{{ $pick->position->name }}</span>
        <div class="text-xs text-gray-400">
            {{ $pick->getSimpleTimePeriodSentenceForHumans() }}
        </div>
        <dl class="font-normal lg:hidden">
            <dt class="sr-only">Assignee</dt>
            <dd class="mt-1 truncate text-gray-700">{{ $pick->user?->name ?: 'none' }}</dd>
            <dt class="sr-only sm:hidden">Response</dt>
            <dd class="mt-1 truncate text-gray-500 sm:hidden"></dd>
        </dl>
        @if($edit_notes_mode)
            <textarea wire:model.live="notes" class="my-2"></textarea>
            <button class="admin-button-blue-small mt-1" wire:click="saveNotes()">Save Changes</button>
            <button class="admin-button-transparent-small mt-1" wire:click="exitEditNotesMode()">Cancel</button>
        @endif
    </td>
    <td class="hidden px-3 py-4 text-base text-gray-800 lg:table-cell">
        @if(!$pick->notes)
            <span wire:click="enterEditNotesMode()" class="text-sm align-top text-gray-400 cursor-pointer border border-gray-300 hover:bg-gray-300 hover:text-white px-2 py-1 rounded">
                No Notes
            </span>
        @else
            <span wire:click="enterEditNotesMode()" class="cursor-pointer border border-transparent hover:border-gray-300 hover:bg-green-50 inline-block bg-green-100 text-green-700 text-sm px-2 py-1 rounded">
                <span class="mx-auto flex flex-row my-auto">
                    <x-heroicon-m-check class="w-4 h-4 my-auto mr-1"/>
                    <span>Notes</span>
                </span>
            </span>
        @endif
    </td>
    <td class="flex flex-col pr-4 text-base text-gray-500 text-right align-content-end justify-content-end my-3">
        <div class="mb-2">
            <livewire:components.select-user :key="$pick->id"
                                             :selected_user_id="$pick->user_id"
                                             :listener_name="'user-selected-for-pick-'.$pick->id"
                                             :restrict_to_users_for_assignment_position_id="$pick->wa_position_id"
                                             :empty_option_text="'Unassigned'"/>
        </div>
        <div>
            @if($pick->user)
                <span x-data="{ sentNotification: false }">
                    <span wire:click="sendNotification"
                          x-show="!sentNotification"
                          class="inline-flex mr-2 text-sm text-blue-600 hover:text-blue-900 border border-blue-500 rounded py-1 px-2 cursor-pointer hover:bg-blue-400 hover:text-white"
                          @click="sentNotification = true; setTimeout(() => sentNotification = false, 8000)"
                    >
                        <x-heroicon-o-bell class="h-4 w-4 mr-1 my-auto"/>
                        <span>
                            {{ $pick->hasSentNotification() ? 'Remind' : 'Notify' }}
                            <span class="sr-only">, {{ $pick->user?->name ?: 'none' }}</span>
                        </span>
                    </span>
                    <span x-cloak
                          x-show="sentNotification"
                          class="inline-flex mr-2 text-sm text-green-600 border border-green-500 bg-green-50 rounded py-1 px-2 cursor-pointer"
                    >
                        <x-heroicon-o-check class="h-4 w-4 mr-1 my-auto"/>
                        <span>
                            Sent!
                        </span>
                    </span>
                </span>
            @endif
            <div class="relative inline-block text-left" x-data="{ open: false }" @click.outside="open = false">
                <div>
                    <button type="button" @click="open = !open"
                            xclass="hover:bg-green-400 hover:bg-red-400 text-green-700 text-red-700"
                            class="{{ $pick->getTailwindClassesForBadgeColor() }} inline-flex w-full justify-center rounded border border-gray-300 bg-white pl-2 pr-0.5 py-1 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100"
                            id="menu-button" aria-expanded="true" aria-haspopup="true">
                        @if($pick->getShortTextStatus() == 'confirmed')
                            <x-heroicon-m-check class="h-4 w-4 my-auto mr-1"/>
                            <span>Confirmed</span>
                        @elseif($pick->getShortTextStatus() == 'declined')
                            <x-heroicon-m-x-circle class="h-4 w-4 my-auto mr-1"/>
                            <span>Declined</span>
                        @elseif($pick->getShortTextStatus() == 'no_response')
                            <x-heroicon-m-bars-2 class="h-4 w-4 my-auto mr-1"/>
                            <span class="whitespace-nowrap">No Response</span>
                        @endif
                        <x-heroicon-m-chevron-up-down class="h-5 w-5 ml-0.5 text-gray-400"/>
                    </button>
                </div>

                <div x-cloak x-show="open" class="absolute right-0 border border-gray-300 z-10 mt-2 w-32 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="0">
                    <div class="cursor-pointer first:rounded-t-md last:rouned-b-md" role="none">
                        <a wire:click="changeResponse('confirmed')" @click="open = false" class="text-gray-700 block px-4 py-2 hover:bg-green-200 text-sm" role="menuitem" tabindex="-1" id="menu-item-0">Confirmed</a>
                        <a wire:click="changeResponse('declined')" @click="open = false" class="text-gray-700 block px-4 py-2 hover:bg-red-200 text-sm" role="menuitem" tabindex="-1" id="menu-item-1">Declined</a>
                        <a wire:click="changeResponse('no_response')" @click="open = false" class="text-gray-700 block px-4 py-2 hover:bg-gray-200 text-sm" role="menuitem" tabindex="-1" id="menu-item-2">No Response</a>
                    </div>
                </div>
            </div>
        </div>
    </td>
</tr>