<div class="admin-section mt-4" x-data="{ openFilters: false }">
    {{--    <div class="relative px-4 border-b border-gray-200">--}}
    {{--        <div class="sm:hidden">--}}
    {{--            <label for="tabs" class="sr-only">Select a tab</label>--}}
    {{--            <!-- Use an "onChange" listener to redirect the user to the selected tab URL. -->--}}
    {{--            <select id="tabs" name="tabs" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">--}}
    {{--                <option>All</option>--}}

    {{--                <option>Members</option>--}}

    {{--                <option selected>Visitors</option>--}}
    {{--            </select>--}}
    {{--        </div>--}}
    {{--        <div class="hidden sm:block">--}}
    {{--            <div class="">--}}
    {{--                <nav class="-mb-px flex space-x-2" aria-label="Tabs">--}}
    {{--                    <!-- Current: "border-blue-500 text-blue-600", Default: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" -->--}}
    {{--                    <a href="#" class="border-blue-500 text-blue-600 whitespace-nowrap py-3 px-3 border-b-4 font-medium text-sm" aria-current="page">--}}
    {{--                        All--}}
    {{--                    </a>--}}
    {{--                    <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-3 px-3 border-b-4 font-medium text-sm">--}}
    {{--                        Members--}}
    {{--                    </a>--}}
    {{--                    <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-3 px-3 border-b-4 font-medium text-sm">--}}
    {{--                        Visitors--}}
    {{--                    </a>--}}
    {{--                </nav>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}

    <div class="sm:flex sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live="query_term" autocomplete="off" class="standard-input admin-border-color w-full pl-10 pr-3 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900" placeholder="Filter users..." type="search">
                    <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                        <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                            <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                            Loading
                        </kbd>
                    </div>
                </div>
                <div class="relative z-0 inline-flex shadow-2xs rounded-md">
                    <button x-on:click="openFilters = !openFilters" type="button" class="mr-2 admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-funnel class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Filters
                        @if($filter_count > 0)
                            <span class="font-semibold text-black">&nbsp;({{ $filter_count }})</span>
                        @endif
                    </button>
                    <button wire:click="initExcelDownload" type="button" class="admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-arrow-down-tray class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Excel
                    </button>
                    {{--                    <button type="button" class="-ml-px admin-standard-ring relative inline-flex items-center px-4 py-2 border admin-border-color bg-white text-sm font-medium text-gray-700">--}}
                    {{--                        <x-heroicon-o-view-boards class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>--}}
                    {{--                        Columns--}}
                    {{--                    </button>--}}
                    {{--                    <button type="button" class="-ml-px admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-r-md border admin-border-color bg-white text-sm font-medium text-gray-700">--}}
                    {{--                        <x-heroicon-o-switch-vertical class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>--}}
                    {{--                        Sort--}}
                    {{--                    </button>--}}
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-4 mx-4 mb-4" x-show="openFilters" x-cloak>
        <div>
            <div class="flex-1" wire:ignore>
                Include Groups
                <select name="groups[]" id="groups" class="js-choice1" multiple="multiple" autocomplete=off>
                    @foreach ($groups as $group)
                        <option value="{{ $group->id }}" {{ in_array($group->id, $filter_groups) ? 'selected' : null }}>{{ $group->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex-1 mt-2" wire:ignore>
                Exclude Groups
                <select name="exclude_groups[]" id="exclude_groups" class="js-choice2" multiple="multiple" autocomplete=off>
                    @foreach ($groups as $group)
                        <option value="{{ $group->id }}" {{ in_array($group->id, $filter_exclude_groups) ? 'selected' : null }}>{{ $group->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex-1" wire:ignore>
                Include Roles
                <select name="roles[]" id="roles" class="js-choice4" multiple="multiple" autocomplete=off>
                    @foreach ($roles as $role)
                        <option value="{{ $role->id }}" {{ in_array($role->id, $filter_roles) ? 'selected' : null }}>{{ $role->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex-1 mt-2" wire:ignore>
                Grades to Include
                <select name="grades[]" id="grades" class="js-choice3" multiple="multiple" autocomplete=off>
                    @foreach ($grades as $grade)
                        <option value="{{ $grade->id }}" {{ in_array($grade->id, $filter_grades) ? 'selected' : null }}>{{ $grade->name }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div>
            <div class="flex-1" wire:ignore>
                Age by birthdate, between:
                <br>
                <div class="flex flex-row">
                    <input wire:model.live="options.birthdate_start" id="options[birthdate_start]" type="date"/>
                    <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('birthdate_start')">Clear</span>
                </div>
                <div class="flex flex-row">
                    <input wire:model.live="options.birthdate_end" id="options[birthdate_end]" type="date" class="mt-2"/>
                    <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('birthdate_end')">Clear</span>
                </div>
            </div>
            <div class="flex-1 mt-4" wire:ignore>
                Membership Date, between:
                <br>
                <div class="flex flex-row">
                    <input wire:model.live="options.membership_start" id="options[membership_start]" type="date"/>
                    <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('membership_start')">Clear</span>
                </div>
                <div class="flex flex-row">
                    <input wire:model.live="options.membership_end" id="options[membership_end]" type="date" class="mt-2"/>
                    <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('membership_end')">Clear</span>
                </div>
            </div>
            <div class="flex-1 mt-4" wire:ignore>
                Baptism Date, between:
                <br>
                <div class="flex flex-row">
                    <input wire:model.live="options.baptism_start" id="options[baptism_start]" type="date"/>
                    <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('baptism_start')">Clear</span>
                </div>
                <div class="flex flex-row">
                    <input wire:model.live="options.baptism_end" id="options[baptism_end]" type="date" class="mt-2"/>
                    <span class="text-gray-400 text-xs my-auto py-1 ml-1 border border-gray-400 rounded px-1 cursor-pointer" wire:click="clearOption('baptism_end')">Clear</span>
                </div>
            </div>
        </div>
        <div class="flex-1">
            <div class="form-group col-md-12">
                <label for="">Family roles to include:</label>
                <hr class="my-1"/>
                @foreach (\App\Users\User::$family_roles as $key => $value)
                    <div class="form-check form-check-inline">
                        <input wire:model.live="family_roles" id="family_roles[{{ $key }}]" type="checkbox" value="{{ $key }}"/>
                        <label for="family_roles[{{ $key }}]">{{ $value }}</label>
                    </div>
                @endforeach
            </div>
            <hr class="my-1"/>
            <div class="form-group col-md-12">
                <label for="">Marital statuses to include:</label>
                <hr class="my-1"/>
                @foreach (\App\Users\User::$marital_statuses as $key => $value)
                    <div class="form-check form-check-inline">
                        <input wire:model.live="marital_statuses" id="marital_statuses[{{ $key }}]" type="checkbox" value="{{ $key }}"/>
                        <label for="marital_statuses[{{ $key }}]">{{ $value }}</label>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="shrink">
            <div class="form-group col-md-12">
                <label for="">Other Filters:</label>
                <div class="form-check form-check-inline" style="display:none">
                    <input wire:model.live="columns" id="columns[name]" type="checkbox" value="name" disabled checked>
                    <label for="columns[name]">Name</label>
                </div>
                <div class="form-check form-check-inline" style="display:none">
                    <input wire:model.live="columns" id="columns[best_email]" type="checkbox" value="best_email" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'best_email'))>
                    <label for="columns[best_email]">Best Email</label>
                </div>
                <div class="form-check form-check-inline" style="display:none">
                    <input wire:model.live="columns" id="columns[home_address]" type="checkbox" value="home_address" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'home_address'))>
                    <label for="columns[home_address]">Home Address</label>
                </div>
                <div class="form-check form-check-inline" style="display:none">
                    <input wire:model.live="columns" id="columns[mobile_phone]" type="checkbox" value="mobile_phone" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'mobile_phone'))>
                    <label for="columns[mobile_phone]">Mobile Phone</label>
                </div>
                <div class="form-check form-check-inline" style="display:none">
                    <input wire:model.live="columns" id="columns[home_phone]" type="checkbox" value="home_phone" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'home_phone'))>
                    <label for="columns[home_phone]">Home Phone</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[is_baptized]" type="checkbox" value="is_baptized" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_baptized'))>
                    <label for="columns[is_baptized]">Is Baptized</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[is_not_baptized]" type="checkbox" value="is_not_baptized" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_not_baptized'))>
                    <label for="columns[is_not_baptized]">Is <em>not</em> Baptized</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[date_background_check]" type="checkbox" value="date_background_check" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'date_background_check'))>
                    <label for="columns[date_background_check]">Completed Background Check</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[can_teach]" type="checkbox" value="can_teach" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'can_teach'))>
                    <label for="columns[can_teach]">Approved to Teach</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[can_lead]" type="checkbox" value="can_lead" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'can_lead'))>
                    <label for="columns[can_lead]">Approved to Lead</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[head_of_household_only]" type="checkbox" value="head_of_household_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'head_of_household_only'))>
                    <label for="columns[head_of_household_only]">Head of Household Only</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[adults_only]" type="checkbox" value="adults_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'adults_only'))>
                    <label for="columns[adults_only]">Adults Only</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[children_only]" type="checkbox" value="children_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'children_only'))>
                    <label for="columns[children_only]">Children Only</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[men_only]" type="checkbox" value="men_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'men_only'))>
                    <label for="columns[men_only]">Men Only</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[women_only]" type="checkbox" value="women_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'women_only'))>
                    <label for="columns[women_only]">Women Only</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[no_email]" type="checkbox" value="no_email" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'no_email'))>
                    <label for="columns[no_email]">No Email</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[has_email]" type="checkbox" value="has_email" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'has_email'))>
                    <label for="columns[has_email]">Has Email</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[no_password]" type="checkbox" value="no_password" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'no_password'))>
                    <label for="columns[no_password]">Password <strong>not</strong> set</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[has_password]" type="checkbox" value="has_password" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'has_password'))>
                    <label for="columns[has_password]">Password <strong>is</strong> set</label>
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        <div class="overflow-x-auto">
            <div class="min-w-full">
                @if($users->hasPages())
                    <div class="border-t admin-border-color px-4 py-2">
                        {{ $users->withQueryString()->onEachSide(1)->links() }}
                    </div>
                @endif
                @if(count($selected_users_array) > 0)
                    <div class="flex flex-row border-t admin-border-color px-4 py-2 space-x-4">
                        <div class="my-auto">
                            <strong>
                                {{ count($selected_users_array) }}
                            </strong> users selected
                        </div>
                        <button wire:click="clearSelectedUsers" onclick="document.querySelectorAll('[id^=u_s_cb_]').forEach(el => el.checked = false);" class="admin-button-transparent-small hover:bg-red-50">
                            <x-heroicon-s-x-mark class="w-4 mr-1 my-auto"/>
                            Clear
                        </button>
                        @if($show_selected_users_only)
                            <button wire:click="toggleShowSelectedUsersOnly" class="admin-button-transparent-small bg-green-50 text-green-600 border-green-500 hover:bg-green-50">
                                <x-heroicon-s-check class="w-4 mr-1 my-auto text-green-600"/>
                                Show Selected Only
                            </button>
                        @else
                            <button wire:click="toggleShowSelectedUsersOnly" class="admin-button-transparent-small">
                                Show Selected Only
                            </button>
                        @endif
                    </div>
                @endif
                <div class="overflow-hidden overflow-x-auto">
                    <table class="table-auto min-w-full divide-y divide-gray-300 border-collapse">
                        <thead class="sticky top-0 bg-white bg-opacity-90 uppercase">
                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                            <th scope="col" style="width:20px;">&nbsp;</th>
                            <th scope="col" class="py-2 pr-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Name</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Role</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Info</th>
                            <th scope="col" style="width: 24px"></th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($users as $user)
                            <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                @if(in_array($user->id, $selected_users_array))
                                    <td wire:click="removeSelectedUser('{{ $user->id }}')"
                                        onclick="document.getElementById('u_s_cb_{{ $user->id }}').checked = false"
                                        class="px-2"
                                        style="width:20px;">
                                        {{-- We need an ID on this element, otherwise the DOM loses track of which checkbox we uncheck and remove. --}}
                                        <input type="checkbox" class="h-4 w-4 my-auto -mt-1" id="u_s_cb_{{ $user->id }}" checked/>
                                    </td>
                                @else
                                    <td wire:click="addSelectedUser('{{ $user->id }}')"
                                        class="px-2"
                                        onclick="document.getElementById('u_s_cb_{{ $user->id }}').checked = true"
                                        style="width:20px;">
                                        <input type="checkbox" class="h-4 w-4 my-auto -mt-1" id="u_s_cb_{{ $user->id }}"/>
                                    </td>
                                @endif
                                <td class="py-3 pr-2 whitespace-nowrap font-medium text-gray-900">
                                    <a href="{{ route('admin.users.view', $user) . (request()->getQueryString() ? '?index_filters=' . base64_encode(request()->getQueryString()) : null) }}"
                                       class="flex flex-row justify-between cursor-pointer">
                                        <span class="text-blue-600">{{ $user->last_name }}, {{ $user->display_first_name }}</span>
                                        @if($user->primaryPhotoOptimized)
                                            {!! $user->primaryPhotoOptimized?->first()?->getImgTag(50, 'rounded mr-2" style="max-width: 32px; max-height: 30px"') !!}
                                        @endif
                                    </a>
                                </td>
                                <td class="w-20 py-3 px-2 whitespace-nowrap text-gray-500 {{ $user->isHeadOfFamily() ? 'font-bold' : null }}">
                                    {{ $user->family_role }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-gray-500">
                                    <div class="flex flex-row space-x-2">
                                        @if($user->isMember())
                                            <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs">Member</span>
                                        @endif
                                        @if($user->is_baptized)
                                            <span class="bg-purple-500 text-white px-2 py-1 rounded text-xs">Baptized</span>
                                        @endif
                                        <div class="flex flex-row">
                                            @if($user->isVisitor())
                                                <span class="border border-gray-700 text-gray-700 px-2 py-1 rounded text-xs">Visitor</span>
                                            @endif
                                            @if($user->isInVisitorTracking())
                                                <a href="{{ $user->getVisitorTrackingRecord() ? route('app.visitors.view', $user->getVisitorTrackingRecord()) : null }}" title="Visitor Tracking Enabled" class="border border-green-500 px-1 py-1 rounded text-xs ml-1">
                                                    <x-heroicon-s-cursor-arrow-rays class="w-4 text-green-700"/>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3 px-2 relative whitespace-nowrap text-right">
                                    <x-heroicon-o-chevron-right class="h-4 w-4"/>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-center p-5">
                                    <span class="">No results found.</span>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($users->hasPages())
                <div class="border-t admin-border-color p-4">
                    {{ $users->withQueryString()->onEachSide(1)->links() }}
                </div>
            @endif
        </div>
    </div>


    <script>
        var initChoiceJs = function () {
            jschoice1 = new Choices('.js-choice1', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($filter_groups_js) !!},
            });
            jschoice2 = new Choices('.js-choice2', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($filter_exclude_groups_js) !!},
            });
            jschoice3 = new Choices('.js-choice3', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($filter_grades_js) !!},
            });
            jschoice4 = new Choices('.js-choice4', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($filter_roles_js) !!},
            });

            jschoice1.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addGroupFilter', {group_id: event.detail.value});
                    // do something creative here...
                    // console.log('item added');
                    // console.log(event.detail.id);
                    // console.log(event.detail.value);
                    // console.log(event.detail.label);
                    // console.log(event.detail.customProperties);
                    // console.log(event.detail.groupValue);
                },
                false,
            );
            jschoice1.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeGroupFilter', {remove_group_id: event.detail.value});
                },
                false,
            );

            jschoice2.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addGroupExcludeFilter', {group_id: event.detail.value});
                },
                false,
            );
            jschoice2.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeGroupExcludeFilter', {remove_group_id: event.detail.value});
                },
                false,
            );

            jschoice3.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addGrade', {grade_id: event.detail.value});
                },
                false,
            );
            jschoice3.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeGrade', {grade_id: event.detail.value});
                },
                false,
            );

            jschoice4.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addRoleFilter', {role_id: event.detail.value});
                },
                false,
            );
            jschoice4.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeRoleFilter', {role_id: event.detail.value});
                },
                false,
            );
        }
        document.addEventListener('contentChanged', (event) => {
            initChoiceJs();
        });
        document.addEventListener('DOMContentLoaded', event => {
            initChoiceJs();
        });
    </script>
</div>