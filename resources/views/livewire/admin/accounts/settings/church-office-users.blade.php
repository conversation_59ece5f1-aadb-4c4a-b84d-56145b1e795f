<div>
    <div class="flex flex-col mt-4">
        <div class="overflow-x-auto admin-section rounded">
            <div class="inline-block min-w-full">
                <div class="overflow-hidden overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-300 border-collapse">
                        <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                            <th scope="col" class="w-4">&nbsp;</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Name</th>
                            <th scope="col" class="py-2 px-2 text-center text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Subtitle</th>
                            <th scope="col" class="w-12"></th>
                        </tr>
                        </thead>
                        <tbody id="offices-table" class="bg-white divide-y divide-gray-200">
                        @forelse($users as $user)
                            <tr data-officeid="{{ $user->id }}" data-sortid="{{ $user->sort_id }}" class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                <td class="py-2 px-2 cursor-move">
                                    <x-heroicon-s-bars-3 class="w-5 handle"/>
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap font-medium text-gray-900">
                                    {{ $user->name }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-center">
                                    {{ $user->subtitle }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap space-x-2">

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-center p-5">
                                    <span class="">No results found.</span>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        addEventListener("DOMContentLoaded", (event) => {
            console.log('loaded');
            new Sortable(document.getElementById('offices-table'), {
                handle: '.handle', // handle's class
                animation: 150,
                onEnd: function (event) {
                    // console.log('new id: ' + event.newIndex);
                    // console.log('old id: ' + event.oldIndex);
                    console.log(event.item.dataset.officeid);

                    @this.
                    updateSortId(event.item.dataset.officeid, event.newIndex);

                    // var itemEl = event.item;  // dragged HTMLElement
                    // event.to;    // target list
                    // event.from;  // previous list
                    // event.oldIndex;  // element's old index within old parent
                    // event.newIndex;  // element's new index within new parent
                    // event.oldDraggableIndex; // element's old index within old parent, only counting draggable elements
                    // event.newDraggableIndex; // element's new index within new parent, only counting draggable elements
                    // event.clone // the clone element
                    // event.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
                },
            });
        });
    </script>
</div>
