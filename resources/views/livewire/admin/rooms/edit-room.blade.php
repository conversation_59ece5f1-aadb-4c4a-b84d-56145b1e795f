<div class="space-y-6">
    <form wire:submit.prevent="save" class="space-y-6">
        <div class="bg-white shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                    {{-- Basic Information --}}
                    <div class="sm:col-span-4">
                        <label for="name" class="block text-sm font-medium text-gray-700">Room Name</label>
                        <div class="mt-1">
                            <input type="text" wire:model="name" id="name" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-2">
                        <label for="short_name" class="block text-sm font-medium text-gray-700">Short Name</label>
                        <div class="mt-1">
                            <input type="text" wire:model="short_name" id="short_name" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('short_name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-3">
                        <label for="type" class="block text-sm font-medium text-gray-700">Room Type</label>
                        <div class="mt-1">
                            <select wire:model="type" id="type" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                <option value="">Select Type</option>
                                <option value="conference">Conference Room</option>
                                <option value="classroom">Classroom</option>
                                <option value="auditorium">Auditorium</option>
                                <option value="office">Office</option>
                                <option value="lab">Lab</option>
                            </select>
                        </div>
                        @error('type') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-3">
                        <label for="capacity" class="block text-sm font-medium text-gray-700">Capacity</label>
                        <div class="mt-1">
                            <input type="number" wire:model="capacity" id="capacity" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('capacity') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-6">
                        <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                        <div class="mt-1">
                            <input type="text" wire:model="location" id="location" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('location') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    {{-- Description and Instructions --}}
                    <div class="sm:col-span-6">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <div class="mt-1">
                            <textarea wire:model="description" id="description" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"></textarea>
                        </div>
                        @error('description') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-6">
                        <label for="instructions" class="block text-sm font-medium text-gray-700">Instructions</label>
                        <div class="mt-1">
                            <textarea wire:model="instructions" id="instructions" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"></textarea>
                        </div>
                        @error('instructions') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-6">
                        <label for="cancellation_policy" class="block text-sm font-medium text-gray-700">Cancellation Policy</label>
                        <div class="mt-1">
                            <textarea wire:model="cancellation_policy" id="cancellation_policy" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"></textarea>
                        </div>
                        @error('cancellation_policy') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    {{-- Features --}}
                    <div class="sm:col-span-6">
                        <label class="block text-sm font-medium text-gray-700">Features</label>
                        <div class="mt-2 space-y-2">
                            <div class="flex items-center">
                                <input type="checkbox" wire:model="features" value="{{ App\Rooms\Room::FEATURE_PROJECTOR }}" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label class="ml-2 text-sm text-gray-700">Projector</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" wire:model="features" value="{{ App\Rooms\Room::FEATURE_WHITEBOARD }}" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label class="ml-2 text-sm text-gray-700">Whiteboard</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" wire:model="features" value="{{ App\Rooms\Room::FEATURE_WIFI }}" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label class="ml-2 text-sm text-gray-700">WiFi</label>
                            </div>
                        </div>
                        @error('features') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    {{-- Booking Settings --}}
                    <div class="sm:col-span-3">
                        <label for="minimum_booking_time_in_minutes" class="block text-sm font-medium text-gray-700">Minimum Booking Time (minutes)</label>
                        <div class="mt-1">
                            <input type="number" wire:model="minimum_booking_time_in_minutes" id="minimum_booking_time_in_minutes" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('minimum_booking_time_in_minutes') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-3">
                        <label for="maximum_booking_time_in_minutes" class="block text-sm font-medium text-gray-700">Maximum Booking Time (minutes)</label>
                        <div class="mt-1">
                            <input type="number" wire:model="maximum_booking_time_in_minutes" id="maximum_booking_time_in_minutes" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('maximum_booking_time_in_minutes') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-3">
                        <label for="pre_reservation_buffer_in_minutes" class="block text-sm font-medium text-gray-700">Pre-reservation Buffer (minutes)</label>
                        <div class="mt-1">
                            <input type="number" wire:model="pre_reservation_buffer_in_minutes" id="pre_reservation_buffer_in_minutes" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('pre_reservation_buffer_in_minutes') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    <div class="sm:col-span-3">
                        <label for="post_reservation_buffer_in_minutes" class="block text-sm font-medium text-gray-700">Post-reservation Buffer (minutes)</label>
                        <div class="mt-1">
                            <input type="number" wire:model="post_reservation_buffer_in_minutes" id="post_reservation_buffer_in_minutes" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        @error('post_reservation_buffer_in_minutes') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>

                    {{-- Status Toggles --}}
                    <div class="sm:col-span-6">
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" wire:model="is_hidden" id="is_hidden" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="is_hidden" class="ml-2 text-sm text-gray-700">Hidden</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" wire:model="is_bookable" id="is_bookable" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="is_bookable" class="ml-2 text-sm text-gray-700">Bookable</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" wire:model="is_available" id="is_available" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="is_available" class="ml-2 text-sm text-gray-700">Available</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" wire:model="is_maintenance" id="is_maintenance" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="is_maintenance" class="ml-2 text-sm text-gray-700">Under Maintenance</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Changes
                </button>
            </div>
        </div>
    </form>

    @if (session()->has('message'))
        <div class="rounded-md bg-green-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">
                        {{ session('message') }}
                    </p>
                </div>
            </div>
        </div>
    @endif
</div> 