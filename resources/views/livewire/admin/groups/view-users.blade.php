<div class="flex-1 divide-y divide-gray-200" x-data="{ openFilters: false }">
    <div class="py-6 px-4">
        <div class="flex flex-row justify-between">
            <h3 class="flex flex-row text-2xl font-medium leading-6 text-gray-900 my-auto">
                @switch($group_user_mode)
                    @case('members')
                        <x-heroicon-o-users class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                        <div class="my-auto">Members</div>
                        @break

                    @case('admins')
                        <x-heroicon-o-key class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                        <div class="my-auto">Admins</div>
                        @break

                    @case('senders')
                        <x-heroicon-o-paper-airplane class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                        <div class="my-auto">Authorized Senders</div>
                        @break

                    @default
                        [Unknown]
                @endswitch
            </h3>

            <div>
                <button class="admin-button-transparent" wire:click="toggleCopyUsers">
                    @switch($group_user_mode)
                        @case('members')
                            Copy Members...
                            @break

                        @case('admins')
                            Copy Admins...
                                @break

                        @case('senders')
                            Copy Authorized Senders...
                                @break

                        @default
                            [Unknown]
                    @endswitch
                </button>
                @if($add_users)
                    <button class="admin-button-transparent" wire:click="toggleAddUser">
                        Close
                    </button>
                @else
                    <button class="admin-button-blue" wire:click="toggleAddUser">
                        @switch($group_user_mode)
                            @case('members')
                                Add Members
                                @break

                            @case('admins')
                                Add Admins
                                    @break

                            @case('senders')
                                Add Authorized Senders
                                    @break

                            @default
                                [Unknown]
                        @endswitch
                    </button>
                @endif
            </div>
        </div>
    </div>

    {{-- ADD USER DIV --}}
    <div class="{{ $add_users ? '' : 'hidden' }} bg-gray-500">
        <div class="py-4 px-4 md:px-12 bg-blue-50">
            <h3 class="font-semibold">
                Search Users to Add
            </h3>
            <div class="flex-1 relative rounded-md">
                <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <input wire:model.live="add_user_query_term" autocomplete="off" class="standard-input admin-border-color w-full pl-10 pr-3 py-2 rounded-md text-sm font-light bg-white text-gray-700 focus:text-gray-900" placeholder="Type to search..." type="search">
                <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                    <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                        <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                        Loading
                    </kbd>
                </div>
            </div>
            @if($group_user_mode != 'members')
                <div class="text-gray-400 text-sm">
                    Only members of this group can be added as {{ $group_user_mode }}.
                </div>
            @endif
            @if($add_user_users)
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4">
                    @foreach($add_user_users as $user)
                        <div wire:click="addUser({{ $user->id }})" class="flex flex-row justify-between bg-white border border-gray-300 rounded-md pl-2 pr-1 py-0.5 cursor-pointer hover:bg-blue-100">
                            <div>{{ $user->name }}</div>
                            <x-heroicon-m-plus class="h-5 w-5 text-gray-700 my-auto"/>
                        </div>
                    @endforeach
                </div>
            @endif
            @if($user_added)
                <span class="block mt-3 mb-2 px-2 py-0.5 bg-green-100 border border-green-300 text-green-600 rounded">
                    {{ $user_added }} added!
                </span>
            @endif
        </div>
        <div class="py-4"></div>
    </div>

    {{-- COPY GROUP DIV --}}
    <div class="{{ $copy_users_view ? '' : 'hidden' }} bg-gray-500">
        <div class="py-4 px-4 md:px-12 bg-blue-50">
            <h3 class="font-semibold">
                Select a group to copy {{ $group_user_mode }} from.
            </h3>
            <div class="flex-row">
                <select name="group_to_copy" wire:model.live="group_to_copy_id">
                    <option></option>
                    @foreach(\App\Users\Group::visibleTo(auth()->user())->get() as $group)
                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                    @endforeach
                </select>
                <button wire:click="copyUsers" class="admin-button-blue mt-2">Copy {{ \Illuminate\Support\Str::ucfirst($group_user_mode) }}</button>
            </div>
            @if($group_user_mode != 'members')
                <div class="text-gray-400 text-sm">
                    Only members of this group can be added as {{ $group_user_mode }}.
                </div>
            @endif
            @if($add_user_users)
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4">
                    @foreach($add_user_users as $user)
                        <div wire:click="addUser({{ $user->id }})" class="flex flex-row justify-between bg-white border border-gray-300 rounded-md pl-2 pr-1 py-0.5 cursor-pointer hover:bg-blue-100">
                            <div>{{ $user->name }}</div>
                            <x-heroicon-m-plus class="h-5 w-5 text-gray-700 my-auto"/>
                        </div>
                    @endforeach
                </div>
            @endif
            @if($user_added)
                <span class="block mt-3 mb-2 px-2 py-0.5 bg-green-100 border border-green-300 text-green-600 rounded">
                    {{ $user_added }} added!
                </span>
            @endif
        </div>
        <div class="py-4"></div>
    </div>

    <div class="sm:flex-row sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live="query_term" autocomplete="off" class="admin-border-color w-full pl-10 pr-3 py-2 rounded-md text-sm bg-white text-gray-700 focus:text-gray-900" placeholder="Filter {{ $group_user_mode }}..." type="search">
                    <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                        <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                            <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                            Loading
                        </kbd>
                    </div>
                </div>
                <div class="relative z-0 inline-flex shadow-2xs rounded-md">
                    <button x-on:click="openFilters = !openFilters" type="button" class="mr-2 admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-funnel class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Filters
                        @if($filter_count > 0)
                            <span class="font-semibold text-black">&nbsp;({{ $filter_count }})</span>
                        @endif
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-x-4 mx-4 mb-4 mt-4 text-sm" x-show="openFilters" x-cloak>
            <div class="flex-1">
                <label for="">Roles to include:</label>
                <hr class="my-1"/>
                @foreach (\App\Users\User::$family_roles as $key => $value)
                    <div class="form-check form-check-inline">
                        <input wire:model.live="family_roles" id="family_roles[{{ $key }}]" type="checkbox" value="{{ $key }}"/>
                        <label for="family_roles[{{ $key }}]">{{ $value }}</label>
                    </div>
                @endforeach
            </div>
            <div class="flex-1">
                <label for="">Column Checks:</label>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[head_of_household_only]" type="checkbox" value="head_of_household_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'head_of_household_only'))>
                    <label for="columns[head_of_household_only]">Head of Household Only</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[adults_only]" type="checkbox" value="adults_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'adults_only'))>
                    <label for="columns[adults_only]">Adults Only</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[children_only]" type="checkbox" value="children_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'children_only'))>
                    <label for="columns[children_only]">Children Only</label>
                </div>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[men_only]" type="checkbox" value="men_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'men_only'))>
                    <label for="columns[men_only]">Men Only</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[women_only]" type="checkbox" value="women_only" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'women_only'))>
                    <label for="columns[women_only]">Women Only</label>
                </div>
            </div>
            <div class="shrink">
                <label for="">Column Checks:</label>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[is_baptized]" type="checkbox" value="is_baptized" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_baptized'))>
                    <label for="columns[is_baptized]">Is Baptized</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[is_not_baptized]" type="checkbox" value="is_not_baptized" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'is_not_baptized'))>
                    <label for="columns[is_not_baptized]">Is <em>not</em> Baptized</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[date_background_check]" type="checkbox" value="date_background_check" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'date_background_check'))>
                    <label for="columns[date_background_check]">Completed Background Check</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[can_teach]" type="checkbox" value="can_teach" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'can_teach'))>
                    <label for="columns[can_teach]">Approved to Teach</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="columns" id="columns[can_lead]" type="checkbox" value="can_lead" @isChecked(request()->has('columns') && Illuminate\Support\Arr::exists(request()->get('columns'), 'can_lead'))>
                    <label for="columns[can_lead]">Approved to Lead</label>
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        <div class="overflow-x-auto">
            <div class="overflow-hidden overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-300 border-collapse">
                    <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                    <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                        <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
                        <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Role</th>
                        <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Baptized</th>
                        <th scope="col" class="w-0"></th>
                    </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($users as $user)
                        <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                            <td class="py-3 px-2 pl-4 whitespace-nowrap text-gray-900">
                                @if($user->primaryPhotoOptimized)
                                    {!! $user->primaryPhotoOptimized?->first()?->getImgTag(50, 'rounded float-right mr-2" style="max-width: 32px; max-height: 30px"') !!}
                                @endif
                                <a href="{{ route('admin.users.view', $user) }}">
                                    {{ $user->last_name }}, {{ $user->display_first_name }}
                                </a>
                            </td>
                            <td class="py-3 px-2 whitespace-nowrap text-gray-500 {{ $user->isHeadOfFamily() ? 'font-bold' : null }}">
                                {{ $user->family_role }}
                            </td>
                            <td class="py-3 px-2 whitespace-nowrap text-gray-500 pl-6">
                                @if($user->is_baptized)
                                    <x-heroicon-s-check class="w-5"/>
                                @endif
                            </td>
                            <td class="pr-4">
                                <button wire:click="removeUser({{ $user->id }})" class="admin-button-red-transparent-small my-0 py-0.5 text-xs">Remove</button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="100%" class="text-center p-5">
                                <span class="">No results found.</span>
                            </td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>
            <div class="border-t admin-border-color p-4">
                {{ $users->withQueryString()->links() }}
            </div>
        </div>
    </div>


    <script>

    </script>
</div>