<div>
    <h1>
        {{ auth()->user()->account->name }}
    </h1>

    @if(auth()->user()->account->hasFeature('feature.prayers')
        && auth()->user()->account->hasFeature('account.setting.prayers.allow_requests')
        && \App\Prayers\Prayer::visibleTo(auth()->user())->isNotExpired()->isARequest()->isNotApproved()->isActive()->count() > 0)
        <div class="my-4  md:grid-cols-3 md:divide-y-0 md:divide-x grid grid-cols-1 divide-y divide-gray-200 rounded-lg overflow-hidden ">
            <dl class="rounded-lg bg-white border border-gray-300">
                <div class="px-4 py-5 sm:px-6 sm:py-4 rounded-lg hover:bg-green-50 hover:cursor-pointer" onclick="window.location = '{{ route('admin.prayers.index') }}?view_requests=true'">
                    <dt class="text-base font-normal text-gray-900">
                        Member Prayer Requests
                    </dt>
                    <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                        <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                            {{ \App\Prayers\Prayer::visibleTo(auth()->user())->isNotExpired()->isARequest()->isNotApproved()->isActive()->count() }}
                        </div>

                        <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-gray-200 text-gray-800 md:mt-2 lg:mt-0">
                            Pending Approval
                        </div>
                    </dd>
                </div>
            </dl>
        </div>
    @endif

    <div class="my-4">
        <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white border border-gray-300 md:grid-cols-3 md:divide-y-0 md:divide-x">
            <div class="px-4 py-5 sm:p-6 hover:bg-gray-50 hover:cursor-pointer" onclick="window.location = '{{ route('admin.users.index') }}'">
                <dt class="text-base font-normal text-gray-900">Total Members</dt>
                <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                    <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                        {{ \App\Users\User::visibleTo(auth()->user())->MembersOnly()->count() }}
                    </div>

                    <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-gray-200 text-gray-800 md:mt-2 lg:mt-0">
                        {{ \App\Users\User::visibleTo(auth()->user())->MembersOnly()->HeadsOfFamily()->count() }} Families
                    </div>
                </dd>
            </div>

            <div class="px-4 py-5 sm:p-6 hover:bg-gray-50 hover:cursor-pointer" onclick="window.location = '{{ route('admin.groups.index') }}'">
                <dt class="text-base font-normal text-gray-900">Groups</dt>
                <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                    <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                        {{ \App\Users\Group::visibleTo(auth()->user())->count() }}
                    </div>

                    <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-gray-100 text-gray-800 md:mt-2 lg:mt-0">
                        {{ \App\Users\Group::visibleTo(auth()->user())->HasPostsEnabled()->count() }} Group Posts Enabled
                    </div>
                </dd>
            </div>

            <div class="px-4 py-5 sm:p-6 hover:bg-gray-50 hover:cursor-pointer" onclick="window.location = '{{ route('admin.involvement.index') }}'">
                <dt class="text-base font-normal text-gray-900">Involvement Categories</dt>
                <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                    <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                        {{ \App\Involvement\Category::visibleTo(auth()->user())->count() }}
                    </div>
                </dd>
            </div>
        </dl>
    </div>

    <div class="grid grid-cols-2 gap-4">

        <div class="admin-section col-span-2">
            <div class="mb-4">
                <div class="text-center my-2">Users Per Day</div>
                {{--                    <canvas id="activityChart" class="px-4" height="45" style="border: 0px solid #777"></canvas>--}}
                <div id="activityChartContainer" style="width: 100%; height:180px;"></div>
            </div>
        </div>


        <div class="admin-section col-span-2 sm:col-span-1">
            <div class="px-2 py-3 border-b border-gray-200 sm:px-3">
                <h3 class="text-lg leading-6 font-medium">
                    <i class="far fa-ban mr-2"></i>Spam Complaints
                </h3>
            </div>

            @livewire('admin.dashboard.spam-complaints')
        </div>

        <div class="admin-section col-span-2 sm:col-span-1">
            <div class="px-2 py-3 border-b border-gray-200 sm:px-3">
                <h3 class="text-lg leading-6 font-medium">
                    <i class="far fa-photo mr-2"></i>Pending Photo Approvals
                </h3>
            </div>

            @livewire('admin.dashboard.pending-photo-approvals')
        </div>

    </div>

</div>
