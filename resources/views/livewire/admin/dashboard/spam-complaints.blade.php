<div>

    <table class="min-w-full divide-y divide-gray-300">
        <tbody class="divide-y divide-gray-200">
        @forelse ($spam_complaints as $email)
            <tr>
                <td class="p-4 bg-red-50 border-l-4 border-red-600">
                    <span class="pull-right text-muted">{{ $email->reported_spam_complaint->setTimezone('America/Chicago')->format('M d @ g:ia') }}</span>
                    <a href="{{ route('admin.users.view', $email->user) }}">
                        <strong>{{ $email->user->display_first_name }} {{ $email->user->last_name }}</strong>
                    </a>
                    @if($email->spam_complaint_subject_line)
                        <br>
                        <span class="text-muted">For email: <strong>{{ $email->spam_complaint_subject_line }}</strong></span>
                    @endif
                </td>
            </tr>
        @empty
            <div class="text-center p-8">
                <span class="bg-green-50 px-4 py-2 rounded-md"><i class="far fa-check mr-2"></i>No spam complaints.</span>
            </div>
        @endforelse
        </tbody>
    </table>

    @if($spam_complaints && $spam_complaints->count() > 0)
        <div class="text-gray-500 p-6 text-sm space-y-2">
            <p>Spam complaints come from a user reporting an email from Lightpost as spam to their email provider.</p>
            <p>This action blocks a user from receiving <strong>any</strong> Lightpost emails.</p>
            <p class="">Please <strong>contact support</strong> for information on how to resolve spam complaints.</p>
        </div>
    @endif

</div>
