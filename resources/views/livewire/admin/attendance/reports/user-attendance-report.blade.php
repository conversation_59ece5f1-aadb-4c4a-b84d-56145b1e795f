<div wire:key="user-attendance">
    <h3 class="text-center my-4">User Attendance</h3>
    <div class="flex flex-col sm:flex-row mx-4 mb-4 space-x-0 sm:space-x-4 space-y-2 sm:space-y-0">
        <select wire:model.live="attendance_type_id" class="block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-sm sm:text-base">
            @foreach(auth()->user()->account->attendanceTypes as $type)
                <option value="{{ $type->id }}">{{ $type->name }}</option>
            @endforeach
        </select>
        <input
                type="date"
                wire:model.live="start_date"
                class="block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500  text-sm sm:text-base"
        >
        <input
                type="date"
                wire:model.live="end_date"
                class="block w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500  text-sm sm:text-base"
        >
    </div>
    <div class="mb-4 mx-2">
        <div wire:ignore id="UserAttendanceChartContainer" style="width: 100%; height:250px;"></div>
    </div>
</div>

@push('scripts')
    <script>
        let userAttendanceChart;

        function updateUserAttendanceChart(data) {
            const dataArray = Array.isArray(data[0]) ? data[0] : data;
            const chartData = dataArray.map(item => ({
                x: item.attendance_at,
                y: parseInt(item.total_count || 0)
            }));

            const options = {
                chart: {
                    type: 'column',
                    animation: false,
                },
                xAxis: {
                    categories: chartData.map(point => point.x),
                    labels: {
                        rotation: -45
                    }
                },
                yAxis: {
                    floor: 0,
                    allowDecimals: false,
                    title: false,
                    labels: {
                        enabled: true
                    }
                },
                legend: {
                    enabled: false
                },
                tooltip: {
                    formatter: function () {
                        return `<b>${this.category}</b><br/>
                                Attendance: ${this.y}`;
                    }
                },
                title: {
                    text: null
                },
                plotOptions: {
                    column: {
                        pointPadding: 0.1,
                        borderWidth: 0,
                        grouping: true,
                        dataLabels: {
                            enabled: false
                        }
                    }
                },
                series: [{
                    name: 'Attendance',
                    data: chartData.map(point => point.y)
                }]
            };

            if (userAttendanceChart) {
                userAttendanceChart.update(options);
            } else {
                userAttendanceChart = Highcharts.chart('UserAttendanceChartContainer', options);
            }
        }

        document.addEventListener('livewire:init', function () {
            updateUserAttendanceChart(@json($attendance));

            Livewire.on('refreshUserAttendanceChart', (data) => {
                updateUserAttendanceChart(data);
            });
        });
    </script>
@endpush 