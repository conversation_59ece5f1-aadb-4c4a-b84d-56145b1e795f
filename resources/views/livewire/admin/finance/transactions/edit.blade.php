<div class="py-6">

    <div class="bg-white px-4 py-5 border border-gray-300 sm:rounded-lg sm:p-6">
        <div class="md:grid md:grid-cols-5 md:gap-6">
            <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Transaction Information</h3>
                <p class="mt-1 text-sm text-gray-500"></p>

                <div class="bg-gray-50 border border-gray-300 p-1 text-sm rounded font-semibold">
                    <span class="text-xs font-normal">Created:</span>
                    <br>
                    {{ $transaction->created_at->format('M d, Y') }}
                    <br>
                    <span class="text-xs font-normal">Modified:</span>
                    <br>
                    {{ $transaction->updated_at?->format('M d, Y') }}
                    <br>
                </div>
                @if($transaction->check_account_number)
                    <div class="text-base text-purple-500 mt-4 font-semibold">Check Scanned</div>
                    <div class="bg-purple-50 border border-purple-300 p-1 text-sm rounded font-semibold">
                        <span class="text-xs font-medium">Account:</span>
                        <br>
                        Ending {{ $transaction->check_account_number_last4 }}
                        <br>
                        <span class="text-xs font-medium">Routing:</span>
                        <br>
                        {{ $transaction->check_routing_number }}
                        <br>
                        <span class="text-xs font-medium">Check #:</span>
                        <br>
                        {{ $transaction->check_number }}
                    </div>
                @endif
                <div class="mt-4 text-purple-600 text-sm leading-tight">
                    <strong>NOTE:</strong> You cannot edit information marked in purple.
                    <br>If you need to change key transaction information, please delete the transaction and create a new one.
                </div>
            </div>
            <div class="mt-5 md:col-span-4 md:mt-0">
                <div class="grid grid-cols-4 gap-6">
                    <div class="col-span-4 sm:col-span-4 relative" x-data>
                        <div class="col-span-4 sm:col-span-4 relative" x-data>
                            @if($transaction->user)
                                <label for="name" class="block text-sm font-medium text-purple-700">User</label>
                                <div class="flex-1 relative mt-1">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none rounded-l border-t border-l border-b border-purple-300 bg-purple-50">
                                        <x-heroicon-s-check class="w-5 h-5 text-purple-500"/>
                                    </div>
                                    <input disabled="disabled" value="{{ $transaction->user?->name }}" class="bg-purple-50 border-t border-b border-purple-300 admin-border-color w-full pl-10 pr-3 py-2 rounded-md font-light text-gray-700 focus:text-gray-900"/>
                                </div>
                            @else
                                <label for="name" class="block text-sm font-medium text-purple-700">User</label>
                                <div class="flex-1 relative mt-1">
                                    <input disabled="disabled" placeholder="none" value="{{ $transaction->user?->name }}" class="bg-gray-50 border border-purple-300 admin-border-color w-full px-3 py-2 rounded-md font-light text-gray-700 focus:text-gray-900"/>
                                </div>
                            @endif

                            @if(!empty($user_search_results))
                                <ul class="absolute z-10 mt-1 max-h-80 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden sm:text-sm" id="options" role="listbox">
                                    @foreach($user_search_results as $user)
                                        <li wire:click="selectUser({{ $user->id }})"
                                            class="relative flex flex-row select-none py-2 pl-3 pr-9 text-gray-900 hover:bg-blue-100 cursor-pointer"
                                            role="option">
                                        <span class="px-1.5 py-0 mr-2 text-blue-600 border border-blue-600 rounded">
                                          {{ $loop->index + 1 }}
                                        </span>
                                            <span class="truncate my-auto text-base">{{ $user->name }}</span>
                                            <!-- Active: "text-blue-200", Not Active: "text-gray-500" -->
                                            <span class="ml-2 my-auto truncate text-gray-400 text-xs">{{ $user->getFamilyAddress()?->address1 }}</span>
                                    @endforeach
                                </ul>
                            @endif
                        </div>

                    </div>
                    <div class="col-span-4 sm:col-span-4 relative" x-data>
                        <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                        <input type="text"
                               name="title"
                               id="title"
                               autocomplete="off"
                               class="mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                               wire:model.live="transaction.title">
                    </div>

                    <div class="col-span-4 lg:col-span-1 lg:col-start-1">
                        <label for="posted_at" class="block text-sm font-medium text-gray-700">Date Posted</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <input type="date" name="posted_at" id="posted_at"
                                   class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                   placeholder=""
                                   wire:model.live="posted_at">
                        </div>
                    </div>

                    <div class="col-span-4 xl:col-span-1 lg:col-span-2">
                        <label for="" class="block text-sm font-medium text-purple-700">Type</label>
                        <select id=""
                                name="" autocomplete="off"
                                disabled="disabled"
                                class="mt-1 block w-full text-purple-700 rounded-md border border-purple-300 bg-white py-2 px-3 shadow-2xs focus:border-blue-500 focus:outline-hidden focus:ring-blue-500 sm:text-base">
                            <option value="check" @isChecked(($transaction->source == 'check' || $transaction->source == 'user_check'))>Check</option>
                            <option value="cash" @isChecked($transaction->source == 'user_payment')>User Payment</option>
                            <option value="cash" @isChecked($transaction->source == 'cash')>Cash</option>
                            <option value="other" @isChecked($transaction->source == 'other')>Other</option>
                        </select>
                    </div>
                    @if($transaction->source == 'check' || $transaction->source == 'user_check')
                        <div class="col-span-4 xl:col-span-1 xl:col-start-3 lg:col-span-2 lg:col-start-3">
                            <label for="check_number" class="block text-sm font-medium text-gray-700">Check #</label>
                            <div class="mt-1 flex rounded-md shadow-2xs">
                                <input type="text" name="check_number" id="check_number"
                                       class="block w-full flex-1 rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                       placeholder=""
                                       wire:model.live="transaction.check_number">
                            </div>
                        </div>
                    @else
                        <div class="col-span-4 xl:col-span-1 xl:col-start-3 lg:col-span-2 lg:col-start-1"></div>
                    @endif
                    <div class="col-span-4 xl:col-span-1 xl:col-start-4 lg:col-span-1">
                        <label for="amount" class="block text-sm font-medium text-gray-700">Amount</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 pl-2 pr-3 text-base font-medium text-gray-500">$</span>
                            <input type="text" name="amount" id="amount"
                                   class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-semibold"
                                   placeholder="100.00"
                                   wire:model.live="amount_input">
                        </div>
                    </div>

                    <div class="col-span-4 sm:col-span-2 sm:col-start-1">
                        <label for="selected_bucket_id" class="block text-sm font-medium text-gray-700">Bucket</label>
                        <select id="selected_bucket_id" autocomplete="off"
                                wire:model.live="selected_bucket_id"
                                class="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2.5 px-3 shadow-2xs focus:border-blue-500 focus:outline-hidden focus:ring-blue-500 sm:text-base">
                            @foreach($buckets as $bucket)
                                <option value="{{ $bucket->id }}">{{ $bucket->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-span-4 sm:col-span-3 sm:col-start-1">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <textarea name="notes" id="notes"
                                      class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                      placeholder="" rows="2"
                                      wire:model.live="transaction.notes"></textarea>
                        </div>
                    </div>

                    <div class="col-span-4 sm:col-span-4">
                        <div class="flex justify-between">
                            <div class="flex justify-start">
                                <button type="button"
                                        wire:click="saveTransaction"
                                        class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-2xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <x-heroicon-s-check class="w-4 mr-1"/>
                                    Save Changes
                                </button>
                                <button type="button"
                                        onclick="window.history.back()"
                                        class="ml-3 rounded-md border border-gray-300 bg-white py-2 px-4 text-base font-medium text-gray-700 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    Cancel
                                </button>
                            </div>
                            <button type="button"
                                    wire:click="deleteTransaction"
                                    class="inline-flex justify-center rounded-md border border-red-500 bg-white py-2 px-4 text-base font-medium text-red-500 shadow-2xs hover:bg-red-500 hover:text-white focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <x-heroicon-s-trash class="w-4 mr-1"/>
                                Delete
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
