<div class="py-6">

    <div class="bg-white px-4 py-5 border border-gray-300 sm:rounded-lg sm:p-6">
        <div class="md:grid md:grid-cols-5 md:gap-6">
            <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Bucket Information</h3>
                <p class="mt-1 text-sm text-gray-500">
                    Buckets are primarily used as a way to segment funds.
                </p>
                <p class="mt-4 text-sm text-gray-500">
                    e.g. You may have a "Regular Contribution" and "Special Contribution" bucket for members to select when giving a contribution.
                </p>
                <p class="mt-4 text-sm text-gray-500">
                    You can set a budget for this bucket to track expected contributions with actuals after you have created this bucket.
                </p>
            </div>
            <div class="mt-5 md:col-span-4 md:mt-0">
                <div class="grid grid-cols-4 gap-6">
                    <div class="col-span-4 sm:col-span-4 col-start-1" x-data>
                        <label for="name" class="block text-sm font-medium text-gray-700">Bucket Name</label>
                        <input type="text"
                               name="name"
                               id="name"
                               autocomplete="off"
                               placeholder="Special Evangelism Fund"
                               wire:model.live="name"
                               class="mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>
                    <div class="col-span-4 sm:col-span-2 col-start-1" x-data>
                        <label for="account_code" class="block text-sm font-medium text-gray-700">Accounting Code</label>
                        <input type="text"
                               name="account_code"
                               id="account_code"
                               autocomplete="off"
                               placeholder="5000-101"
                               wire:model.live="account_code"
                               class="mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                    </div>
                    <div class="col-span-4 sm:col-span-2 lg:col-span-1 col-start-1" x-data>
                        <label for="current_year_budget" class="block text-sm font-medium text-gray-700">{{ now()->format('Y') }} Year Budget</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 pl-2 pr-3 text-base font-medium text-gray-500">$</span>
                            <input type="text" name="current_year_budget" id="current_year_budget"
                                   class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-semibold"
                                   placeholder="1200.00"
                                   wire:model.live="current_year_budget"/>
                        </div>
                        <span class="text-gray-400 text-sm font-light">(optional)</span>
                    </div>

                    {{--                    <div class="col-span-4 xl:col-span-1 xl:col-start-1 lg:col-span-2 lg:col-start-1">--}}
                    {{--                        <label for="amount" class="block text-sm font-medium text-gray-700">Monthly Budget <span class="text-gray-400">(optional)</span></label>--}}
                    {{--                        <div class="mt-1 flex rounded-md shadow-2xs">--}}
                    {{--                            <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 pl-2 pr-3 text-base font-medium text-gray-500">$</span>--}}
                    {{--                            <input type="text" name="amount" id="amount"--}}
                    {{--                                   class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-semibold"--}}
                    {{--                                   placeholder="3000.00"--}}
                    {{--                                   wire:model.live="monthly_budget">--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}

                    {{--                    <div class="col-span-4 xl:col-span-1 lg:col-start-1 lg:col-span-2">--}}
                    {{--                        <label for="amount" class="block text-sm font-medium text-gray-700">Yearly Budget <span class="text-gray-400">(optional)</span></label>--}}
                    {{--                        <div class="mt-1 flex rounded-md shadow-2xs">--}}
                    {{--                            <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 pl-2 pr-3 text-base font-medium text-gray-500">$</span>--}}
                    {{--                            <input type="text" name="amount" id="amount"--}}
                    {{--                                   class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-semibold"--}}
                    {{--                                   placeholder="36000.00"--}}
                    {{--                                   wire:model.live="yearly_budget">--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}

                    {{--                    <div class="col-span-4" x-data="{}">--}}
                    {{--                                                <div class="mt-3 relative flex items-start">--}}
                    {{--                                                    <div class="flex h-5 items-center">--}}
                    {{--                                                        <input id="candidates" aria-describedby="candidates-description" name="candidates" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">--}}
                    {{--                                                    </div>--}}
                    {{--                                                    <div class="ml-3 text-sm">--}}
                    {{--                                                        <label for="candidates" class="text-base font-medium text-gray-700">Reimbursement Bucket?</label>--}}
                    {{--                                                        <p id="candidates-description" class="text-gray-500">Selecting this will show this bucket to members as a bucket they can submit a reimbursement request towards.</p>--}}
                    {{--                                                    </div>--}}
                    {{--                                                </div>--}}
                    {{--                    </div>--}}
                    <div class="col-span-4" x-data="{}">
                        <div class="relative flex">
                            <div class="flex h-5 items-center">
                                <input wire:model.live="is_income" wire:click="checkProperty('is_income')" id="is_income" aria-describedby="is_income-description"
                                       name="is_income" type="checkbox"
                                       class="h-5 w-5 rounded-md mt-1 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="is_income" class="text-base font-medium text-gray-700">Income Bucket?</label>
                                <p id="is_income-description" class="text-gray-500">Should this bucket represent money coming in?</p>
                            </div>
                        </div>
                        <div class="relative flex mt-2 ml-7">
                            <div class="flex h-5 items-center">
                                <input wire:model.live="is_contribution" wire:click="checkProperty('is_contribution')" id="is_contribution" aria-describedby="is_contribution-description"
                                       name="is_contribution" type="checkbox"
                                       class="h-5 w-5 rounded-md mt-1 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="is_contribution" class="text-base font-medium text-gray-700">Contribution Bucket?</label>
                                <p id="is_contribution-description" class="text-gray-500">Selecting this will show this bucket to members as a bucket they can submit a contribution towards with Online Giving.</p>
                            </div>
                        </div>
                        <div class="relative flex mt-4">
                            <div class="flex h-5 items-center">
                                <input wire:model.live="is_expense" wire:click="checkProperty('is_expense')" id="is_expense" aria-describedby="is_expense-description"
                                       name="is_expense" type="checkbox"
                                       class="h-5 w-5 rounded-md mt-1 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="is_expense" class="text-base font-medium text-gray-700">Expense Bucket?</label>
                                <p id="is_expense-description" class="text-gray-500">Should this bucket represent money going out?</p>
                            </div>
                        </div>
                        <div class="relative flex mt-2 ml-7">
                            <div class="flex h-5 items-center">
                                <input wire:model.live="is_reimbursement" wire:click="checkProperty('is_reimbursement')" id="is_reimbursement" aria-describedby="is_reimbursement-description"
                                       name="is_reimbursement" type="checkbox"
                                       class="h-5 w-5 rounded-md mt-1 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="is_reimbursement" class="text-base font-medium text-gray-700">Reimbursement Bucket?</label>
                                <p id="is_reimbursement-description" class="text-gray-500">Selecting this will show this bucket to members as a bucket they can submit reimbursement requests to.</p>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-4 sm:col-span-3 sm:col-start-1">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <textarea name="description" id="description"
                                      class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                      placeholder="" rows="2"
                                      wire:model.live="description"></textarea>
                        </div>
                    </div>

                    <div class="col-span-4 sm:col-span-4">
                        <div class="flex justify-start">
                            <button type="button"
                                    wire:click="createBucket"
                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-2xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <x-heroicon-s-check class="w-4 mr-1"/>
                                Save Changes
                            </button>
                            <button type="button"
                                    onclick="window.history.back()"
                                    class="ml-3 rounded-md border border-gray-300 bg-white py-2 px-4 text-base font-medium text-gray-700 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                Cancel
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
