<div class="admin-section mt-4" x-data="{ openFilters: {{ $filter_count > 0 ? 'true' : 'false' }} }">
    <div class="sm:flex sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live="query_term" wire:keydown="$dispatch('queryChanged')" autocomplete="off"
                           class="standard-input text-sm admin-border-color w-full pl-10 pr-3 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900"
                           placeholder="Filter..."
                           type="search">
                    <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                        <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded-md px-2 py-1.5 text-sm font-medium text-gray-400">
                            <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                            Loading
                        </kbd>
                    </div>
                </div>
                <div class="relative z-0 inline-flex shadow-2xs rounded-md">
                    <button x-on:click="openFilters = !openFilters"
                            type="button"
                            class="mr-2 admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-funnel class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Filters
                        @if($filter_count > 0)
                            <span class="font-semibold text-black">&nbsp;({{ $filter_count }})</span>
                        @endif
                    </button>
                    <button wire:click="initExcelDownload"
                            type="button"
                            class="hidden admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-arrow-down-tray class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-4 gap-x-4 mx-4 mb-4" x-show="openFilters" {{ $filter_count > 0 ? 'x-cloak' : null }}>
        <div class="flex-1">
            Minimum Amount
            <input wire:model.live="filter_min_amount" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900" placeholder="" type="search">
            <br>
            Maximum Amount
            <input wire:model.live="filter_max_amount" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900" placeholder="" type="search">
        </div>
        <div class="flex-1">
            Budget Year
            <input wire:model.live="filter_year" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900"
                   placeholder="{{ date('Y') }}" type="text">
        </div>
        {{--        <div class="flex-1">--}}
        {{--            Start Date--}}
        {{--            <input wire:model.live="filter_start_at" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900"--}}
        {{--                   placeholder="" type="date">--}}
        {{--            End Date--}}
        {{--            <input wire:model.live="filter_end_at" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900"--}}
        {{--                   placeholder="" type="date">--}}
        {{--        </div>--}}
        <div class="flex-1 col-span-2">
            <div class="form-group">
                <label for="">Options:</label>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="filter_is_expense" id="filter_is_expense" type="checkbox" value="1">
                    <label for="filter_is_expense">Expense bucket</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="filter_is_income" id="filter_is_income" type="checkbox" value="1">
                    <label for="filter_is_income">Income bucket</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="filter_is_contribution" id="filter_is_contribution" type="checkbox" value="1">
                    <label for="filter_is_contribution">Contribution bucket</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="filter_is_reimbursement" id="filter_is_reimbursement" type="checkbox" value="1">
                    <label for="filter_is_reimbursement">Reimbursement bucket</label>
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        <div class="overflow-x-auto">
            <div class="inline-block min-w-full">
                <div class="overflow-hidden overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-300 border-collapse">
                        <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Name</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">{{ date('Y') }} Budget</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Type</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left"></th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($buckets as $bucket)
                            <tr class="cursor-pointer hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}" onclick="window.location='{{ route('admin.finance.buckets.view', $bucket) }}'">
                                <td class="py-3 px-2 whitespace-nowrap text-black w-1/4">
                                    {{ $bucket->name }}
                                </td>
                                <td class="py-3 px-2 pr-4 whitespace-nowrap text-base">
                                    @if($bucket->currentBudget)
                                        ${{ number_format($bucket->currentBudget->formatAmount()) }}
                                    @else
                                        <span class="px-2 py-1 rounded-sm bg-gray-100 text-gray-400 text-xs">None</span>
                                    @endif
                                </td>
                                <td class="py-3 px-2 relative whitespace-nowrap">
                                    @if($bucket->is_income)
                                        <span class="px-2 py-1 rounded-sm bg-green-100 text-green-600 text-xs">Income</span>
                                    @endif
                                    @if($bucket->is_contribution)
                                        <span class="px-2 py-1 rounded-sm bg-green-100 text-green-600 text-xs">Contribution</span>
                                    @endif
                                    @if($bucket->is_expense)
                                        <span class="px-2 py-1 rounded-sm bg-purple-100 text-purple-600 text-xs">Expense</span>
                                    @endif
                                    @if($bucket->is_reimbursement)
                                        <span class="px-2 py-1 rounded-sm bg-purple-100 text-purple-600 text-xs">Reimbursement</span>
                                    @endif
                                </td>
                                <td class="py-3 px-2 relative whitespace-nowrap" width="5%">
                                    <x-heroicon-o-chevron-right class="h-4 w-4 mx-auto"/>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-center p-5">
                                    <span class="">No results found.</span>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="border-t admin-border-color p-4">
                {{ $buckets->withQueryString()->links() }}
            </div>
        </div>
    </div>


    <script>
    </script>
</div>