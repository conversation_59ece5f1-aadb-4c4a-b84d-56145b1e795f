<div class="admin-section mt-4" x-data="{ openFilters: {{ $filter_count > 0 ? 'true' : 'false' }} }">
    <div class="sm:flex sm:items-center p-4">
        <div class="flex-1">
            <div class="flex space-x-2 text-gray-500 focus-within:text-gray-500">
                <div class="flex-1 relative rounded-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input wire:model.live="query_term" autocomplete="off" class="standard-input admin-border-color w-full pl-10 pr-3 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900"
                           placeholder="Filter..."
                           type="search">
                    <div wire:loading class="hidden absolute inset-y-0 right-0 flex py-1 pr-1.5">
                        <kbd class="inline-flex items-center bg-green-50 border border-gray-200 rounded px-2 py-1.5 text-sm font-medium text-gray-400">
                            <x-heroicon-o-arrow-path class=" mr-1 h-5 w-5"/>
                            Loading
                        </kbd>
                    </div>
                </div>
                <div class="relative z-0 space-x-2 inline-flex rounded-md">
                    <button x-on:click="openFilters = !openFilters" type="button" class="admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-funnel class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Filters
                        @if($filter_count > 0)
                            <span class="font-semibold text-black">&nbsp;({{ $filter_count }})</span>
                        @endif
                    </button>
                    <button wire:click="initExcelDownload" type="button" class="admin-standard-ring relative inline-flex items-center px-4 py-2 rounded-md border admin-border-color bg-white text-sm font-medium text-gray-600">
                        <x-heroicon-o-arrow-down-tray class="-ml-1 mr-2 h-5 w-5 text-gray-400"/>
                        Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-4 gap-x-4 mx-4 mb-4" x-show="openFilters" {{ $filter_count > 0 ? 'x-cloak' : null }}>
        <div class="flex-1" wire:ignore>
            Buckets
            <select name="filter_buckets[]" id="filter_buckets" class="js-choice1" multiple="multiple" autocomplete=off>
                @foreach ($buckets as $bucket)
                    <option value="{{ $bucket->id }}" {{ in_array($bucket->id, $filter_buckets) ? 'selected' : null }}>{{ $bucket->name }}</option>
                @endforeach
            </select>
        </div>
        <div class="flex-1">
            Minimum Amount
            <input wire:model.live="filter_min_amount" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900" placeholder="" type="search">
            <br>
            Maximum Amount
            <input wire:model.live="filter_max_amount" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900" placeholder="" type="search">
        </div>
        <div class="flex-1">
            Start Date
            <input wire:model.live="filter_start_at" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900"
                   placeholder="" type="date">
            End Date
            <input wire:model.live="filter_end_at" autocomplete="off" class="standard-input admin-border-color w-full px-2 py-2 rounded-md font-light bg-white text-gray-700 focus:text-gray-900"
                   placeholder="" type="date">
        </div>
        <div class="flex-1">
            <div class="form-group">
                <label for="">Options:</label>
                <hr class="my-1"/>
                <div class="form-check form-check-inline">
                    <input wire:model.live="filter_show_txn_with_no_user" id="filter_show_txn_with_no_user" type="checkbox" value="1">
                    <label for="filter_show_txn_with_no_user">Show transactions with no user</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="filter_cash_only" id="filter_cash_only" type="checkbox" value="1">
                    <label for="filter_cash_only">Cash only</label>
                </div>
                <div class="form-check form-check-inline">
                    <input wire:model.live="filter_check_only" id="filter_check_only" type="checkbox" value="1">
                    <label for="filter_check_only">Check only</label>
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col">
        <div class="overflow-x-auto">
            @if($contributions->hasPages())
                <div class="border-t admin-border-color p-4">
                    {{ $contributions->withQueryString()->links() }}
                </div>
            @endif
            <div class="inline-block min-w-full">
                <div class="overflow-hidden overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-300 border-collapse">
                        <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                            <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Posted Date</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Name</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Title</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Bucket</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-xs backdrop-filter">Amount</th>
                            <th scope="col" class=""></th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($contributions as $contribution)
                            <tr class="cursor-pointer hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}" onclick="window.location='{{ route('admin.finance.transactions.edit', $contribution) }}'">
                                <td class="py-3 px-2 whitespace-nowrap text-black w-1/12">
                                    {{ $contribution->posted_at?->format('M d, Y') }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-black w-1/4">
                                    {{ $contribution->user ? $contribution->user?->name : null }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-black text-base">
                                    {{ $contribution->title }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-base">
                                    <span class="border border-gray-500 bg-gray-50 px-2 py-0.5 text-sm rounded-md">{{ $contribution->bucket?->name }}</span>
                                </td>
                                <td class="py-3 px-2 pr-4 whitespace-nowrap text-base">
                                    @if(auth()->user()->hasPermission('finance.contributions.view-amounts'))
                                        ${{ $contribution->formatAmount() }}
                                    @endif
                                </td>
                                <td class="py-3 px-2 relative whitespace-nowrap text-right">
                                    <x-heroicon-o-chevron-right class="h-4 w-4"/>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-center p-5">
                                    <span class="">No results found.</span>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            @if($contributions->hasPages())
                <div class="border-t admin-border-color p-4">
                    {{ $contributions->withQueryString()->links() }}
                </div>
            @endif
        </div>
    </div>


    <script>
        var initChoiceJs = function () {
            jschoice1 = new Choices('.js-choice1', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($filter_buckets_js) !!},
            });

            jschoice1.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addBucketFilter', {bucket_id: event.detail.value});
                    console.log('item added');
                    // console.log(event.detail.id);
                    // console.log(event.detail.value);
                    // console.log(event.detail.label);
                    // console.log(event.detail.customProperties);
                    // console.log(event.detail.groupValue);
                },
                false,
            );
            jschoice1.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeBucketFilter', {bucket_id: event.detail.value});
                    console.log('item added');
                },
                false,
            );
        }
        document.addEventListener('contentChanged', (event) => {
            console.log('changed');
            initChoiceJs();
        });
        document.addEventListener('DOMContentLoaded', event => {
            initChoiceJs();
        });
    </script>
</div>