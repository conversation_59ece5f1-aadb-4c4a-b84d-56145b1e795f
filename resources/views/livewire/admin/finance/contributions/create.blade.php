<div class="py-4">

    <div class="admin-section-border bg-white px-4 py-5 sm:rounded-lg sm:p-6">
        <div class="md:grid md:grid-cols-5 md:gap-6">
            <div class="mt-5 md:col-span-4 md:mt-0">
                <div class="grid grid-cols-4 gap-4">
                    <div class="col-span-4 relative" x-data>
                        <label for="name" class="block text-sm font-medium text-gray-700">User <span class="text-gray-300 text-xs">(optional)</span></label>
                        @if(!$selected_user)
                            <input type="text"
                                   name="name"
                                   id="name"
                                   autocomplete="off"
                                   placeholder="{{ $this->selected_user ? $this->selected_user->name : '' }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                   wire:model.live="user_search_query"
                                   wire:keydown.escape="clickAwayFromSearch"
                                   wire:keydown.tab="clickAwayFromSearch"
                                   x-on:click.away="$wire.clickAwayFromSearch()">
                        @else
                            <div class="flex-1 relative mt-1">
                                <div class="absolute inset-y-0 left-0 pl-3 flex text-gray-500 items-center pointer-events-none rounded-l border-t border-l border-b border-purple-300 bg-purple-50">
                                    <x-heroicon-s-check class="w-5 h-5 text-purple-500"/>
                                </div>
                                <input disabled="disabled" value="{{ $selected_user->name }}" class="bg-purple-50 border-t border-b border-purple-300 admin-border-color w-full pl-10 pr-3 py-2 rounded-md font-light text-gray-700 focus:text-gray-900" placeholder="Filter users..." type="search">
                                <div class="absolute inset-y-0 right-0 flex py-1 pr-1.5 bg-purple-50 rounded-r border-t border-r border-b border-purple-300"
                                     wire:click="resetAllFields">
                                    <kbd class="inline-flex items-center bg-red-50 border border-red-300 rounded px-2 py-1.5 text-xs font-medium text-red-400 cursor-pointer">
                                        <x-heroicon-o-x-mark class="mr-1 h-4 w-4"/>
                                        Clear
                                    </kbd>
                                </div>
                            </div>
                        @endif

                        @if($check_account_number && !$selected_user)
                            <div class="text-base p-2 mt-2 rounded bg-red-50 border border-red-300 text-red-600">
                                Please select a user to associate this check with.<br>
                                <span class="text-sm text-gray-400 -mt-1">This bank and account has not been associated with anyone before.</span>
                            </div>
                        @endif

                        @if(!empty($user_search_results))
                            <ul class="absolute z-10 mt-1 max-h-80 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-hidden sm:text-sm"
                                id="options"
                                role="listbox">
                                @foreach($user_search_results as $user)
                                    <li wire:click="selectUser({{ $user->id }}, false)"
                                        class="relative flex flex-row select-none py-2 pl-3 pr-9 text-gray-900 hover:bg-blue-100 cursor-pointer"
                                        role="option">
                                        <span class="px-1.5 py-0 mr-2 text-blue-600 border border-blue-600 rounded">
                                          {{ $loop->index + 1 }}
                                        </span>
                                        <span class="truncate my-auto text-base">{{ $user->name }}</span>
                                        <!-- Active: "text-blue-200", Not Active: "text-gray-500" -->
                                        <span class="ml-2 my-auto truncate text-gray-400 text-xs">{{ $user->getFamilyAddress()?->address1 }}</span>
                                    </li>
                                @endforeach
                                <li class="bg-purple-50 px-4 py-1 text-purple-500">
                                    Type a number next to a name to quick select that person with the keyboard.
                                </li>
                            </ul>
                        @endif
                    </div>
                    <div class="col-span-4 relative" x-data>
                        <label for="title" class="block text-sm font-medium text-gray-700">Title / Description</label>
                        <input type="text"
                               name="title"
                               id="title"
                               autocomplete="off"
                               placeholder="Regular Contribution"
                               class="mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-2xs focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                               wire:model.live="title">
                    </div>


                    <div class="col-span-4 xl:col-span-1">
                        <label for="amount" class="block text-sm font-medium text-gray-700">Amount</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 pl-2 pr-3 text-base font-medium text-gray-500">$</span>
                            <input type="text" name="amount" id="amount"
                                   class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-semibold"
                                   placeholder="100.00"
                                   wire:model.live="amount_input">
                        </div>
                    </div>

                    <div class="col-span-4 lg:col-span-1">
                        <label for="posted_at" class="block text-sm font-medium text-gray-700">Date Posted</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <input type="date" name="posted_at" id="posted_at"
                                   class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                   placeholder=""
                                   value="{{ $posted_at ?: now()->format('Y-m-d') }}"
                                   wire:model.live="posted_at">
                        </div>
                    </div>

                    <div class="col-span-4 xl:col-span-1">
                        <label for="contribution_type" class="block text-sm font-medium text-gray-700">Type</label>
                        <select id="contribution_type"
                                name="contribution_type" autocomplete="off"
                                wire:model.live="contribution_type"
                                class="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-2xs focus:border-blue-500 focus:outline-hidden focus:ring-blue-500 sm:text-base">
                            <option value="check" selected>Check</option>
                            <option value="cash">Cash</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    @if($contribution_type == 'check')
                        <div class="col-span-4 xl:col-span-1">
                            <label for="check_number" class="block text-sm font-medium text-gray-700">Check #</label>
                            <div class="mt-1 flex rounded-md shadow-2xs">
                                <input type="text" name="check_number" id="check_number"
                                       class="block w-full flex-1 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                       placeholder="1234"
                                       wire:model.live="check_number">
                            </div>
                        </div>
                    @else
                        <div class="col-span-4 xl:col-span-1"></div>
                    @endif

                    <div class="col-span-4 sm:col-span-2 sm:col-start-1">
                        <label for="budget_id" class="block text-sm font-medium text-gray-700">Bucket <span class="font-normal text-gray-400">(contribution only)</span></label>
                        <select id="budget_id"
                                name="selected_bucket_id" autocomplete="off"
                                wire:model.live="selected_bucket_id"
                                class="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2.5 px-3 shadow-2xs focus:border-blue-500 focus:outline-hidden focus:ring-blue-500 sm:text-base">
                            @foreach($buckets as $bucket)
                                <option value="{{ $bucket->id }}" {{ $selected_bucket_id == $bucket->id ? 'selected' : null }}>{{ $bucket->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-span-4 col-start-1">
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                        <div class="mt-1 flex rounded-md shadow-2xs">
                            <textarea name="notes" id="notes"
                                      class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                      placeholder="" rows="1"
                                      wire:model.live="notes"></textarea>
                        </div>
                    </div>

                    <div class="col-span-4">
                        <div class="flex justify-start">
                            <button type="button"
                                    wire:click="createContribution"
                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-2xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <x-heroicon-s-check class="w-4 mr-1"/>
                                Create Contribution
                            </button>
                            <button type="button"
                                    wire:click="resetAllFields"
                                    class="ml-3 rounded-md border border-gray-300 bg-white py-2 px-4 text-base font-medium text-gray-700 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                Clear All Fields
                            </button>
                        </div>
                    </div>

                </div>
            </div>
            <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">
                    Contribution Information
                </h3>

                <div class="flex flex-col space-y-2 mt-2 text-sm text-gray-500">
                    <div>Enter the information about the contributor.</div>
                    <div>A selected user is <strong>not</strong> required to record a contribution.</div>
                    <div>You can scan a check with a check reader to quickly enter check numbers and link users to contributions.</div>
                </div>

                @if($check_account_number)
                    <div class="flex-1 text-base text-purple-500 mt-4 font-semibold">
                        Check Scanned
                        <span class="text-xs text-right text-blue-400" wire:click="clearScannedCheck">
                            Clear
                        </span>
                    </div>
                    <div class="bg-purple-50 border border-purple-300 p-1 text-sm rounded font-semibold">
                        <span class="text-xs font-medium">Account:</span>
                        <br>
                        {{ $check_account_number }}
                        <br>
                        <span class="text-xs font-medium">Routing:</span>
                        <br>
                        {{ $check_routing_number }}
                        <br>
                        <span class="text-xs font-medium">Check #:</span>
                        <br>
                        {{ $check_number }}
                    </div>
                @endif
            </div>
        </div>
    </div>


    <h3 class="text-2xl mt-6">Recent Contributions</h3>

    <div class="admin-section-border admin-section mt-2">

        <div class="flex flex-col">
            <div class="overflow-x-auto">
                <div class="overflow-hidden overflow-x-auto">

                    <table class="table-auto min-w-full divide-y divide-gray-300 border-collapse">
                        <thead class="uppercase">
                        <tr class="border-b border-gray-300 text-gray-200">
                            <th scope="col" class="bg-gray-700 rounded-tl-lg py-2 px-2 pl-4 text-xs text-left font-semibold">Date</th>
                            <th scope="col" class="bg-gray-700 py-2 px-2 pl-2 text-xs text-left font-semibold">Name</th>
                            <th scope="col" class="bg-gray-700 py-2 px-2 text-xs text-left font-semibold">Title</th>
                            <th scope="col" class="bg-gray-700 w-12 py-2 px-2 text-xs text-left font-semibold">Bucket</th>
                            <th scope="col" class="bg-gray-700 rounded-tr-lg w-20 py-2 px-2 text-xs text-left font-semibold">Amount</th>
                        </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($recent_contributions as $contribution)
                            <tr class="hover:bg-blue-50 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                <td class="py-3 px-2 pl-4 whitespace-nowrap text-black w-1/12">
                                    {{ $contribution->created_at->format('M d, Y') }}
                                </td>
                                <td class="py-3 px-2 pl-2 whitespace-nowrap text-black w-1/4">
                                    {{ $contribution->user?->name }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-black text-base">
                                    {{ $contribution->title }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-center text-base">
                                    <span class="border border-gray-500 bg-gray-50 px-2 py-0.5 text-sm rounded-md">{{ $contribution->bucket?->name }}</span>
                                </td>
                                <td class="py-3 px-2 pr-4 whitespace-nowrap text-base">
                                    ${{ $contribution->formatAmount() }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-center p-5">
                                    <span class="">No results found.</span>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>

                </div>
            </div>
        </div>

    </div>
</div>
