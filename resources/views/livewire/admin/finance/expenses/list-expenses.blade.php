<div>
    <table class="table-auto min-w-full divide-y divide-gray-300 border-collapse">
        <thead class="uppercase">
        <tr class="border-b border-gray-300 text-gray-200">
            <th scope="col" class="bg-gray-700 rounded-tl-lg py-2 px-2 pl-4 text-xs text-left font-semibold">Date</th>
            <th scope="col" class="bg-gray-700 py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
            <th scope="col" class="bg-gray-700 py-2 px-2 pl-4 text-xs text-left font-semibold">Title</th>
            <th scope="col" class="bg-gray-700 w-12 py-2 px-2 text-xs text-left font-semibold">Bucket</th>
            <th scope="col" class="bg-gray-700 rounded-tr-lg w-20 py-2 px-2 text-xs text-left font-semibold">Amount</th>
        </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
        @forelse($recent_expenses as $expense)
            <tr class="hover:bg-blue-50 {{ $loop->odd ? 'bg-gray-50' : null }}">
                <td class="py-3 px-2 pl-4 whitespace-nowrap text-black w-1/12">
                    {{ $expense->created_at->format('M d, Y') }}
                </td>
                <td class="py-3 px-2 pl-4 whitespace-nowrap text-black w-1/4">
                    {{ $expense->user?->name }}
                </td>
                <td class="py-3 px-2 whitespace-nowrap text-black text-base">
                    {{ $expense->title }}
                </td>
                <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-center text-base">
                    <span class="border border-gray-500 bg-gray-50 px-2 py-0.5 text-sm rounded-md">{{ $expense->bucket?->name }}</span>
                </td>
                <td class="py-3 px-2 pr-4 whitespace-nowrap text-base">
                    ${{ $expense->formatAmount() }}
                </td>
            </tr>
        @empty
            <tr>
                <td colspan="100%" class="text-center p-5">
                    <span class="">No results found.</span>
                </td>
            </tr>
        @endforelse
        </tbody>
    </table>
</div>
