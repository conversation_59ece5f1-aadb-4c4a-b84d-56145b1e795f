<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta content="telephone=no" name="format-detection"/>
    <title></title>
    <style type="text/css" data-premailer="ignore">
        @import url(https://fonts.googleapis.com/css?family=Open+Sans:300,400,500,600,700);
    </style>
    <style data-premailer="ignore">
        @media screen and (max-width: 600px) {
            u + .body {
                width: 100vw !important;
            }
        }

        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }
    </style>
    <!--[if mso]>
    <style type="text/css">
        body, table, td {
            font-family: Arial, Helvetica, sans-serif !important;
        }

        img {
            -ms-interpolation-mode: bicubic;
        }

        .box {
            border-color: #eee !important;
        }
    </style>
    <![endif]-->

    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f5f7fb;
            font-size: 15px;
            line-height: 160%;
            mso-line-height-rule: exactly;
            color: #444444;
            width: 100%;
        }

        body {
            font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;
        }

        img {
            border: 0 none;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            vertical-align: baseline;
            font-size: 0;
        }

        a:hover {
            text-decoration: underline;
        }

        .btn:hover {
            text-decoration: none;
        }

        .btn.bg-bordered:hover {
            background-color: #f9fbfe !important;
        }

        a.bg-blue:hover {
            background-color: #3a77cc !important;
        }

        a.bg-azure:hover {
            background-color: #37a3f1 !important;
        }

        a.bg-indigo:hover {
            background-color: #596ac9 !important;
        }

        a.bg-purple:hover {
            background-color: #9d50e8 !important;
        }

        a.bg-pink:hover {
            background-color: #f55f91 !important;
        }

        a.bg-red:hover {
            background-color: #c01e1d !important;
        }

        a.bg-orange:hover {
            background-color: #fd8e35 !important;
        }

        a.bg-yellow:hover {
            background-color: #e3b90d !important;
        }

        a.bg-lime:hover {
            background-color: #73cb2d !important;
        }

        a.bg-green:hover {
            background-color: #56ab00 !important;
        }

        a.bg-teal:hover {
            background-color: #28beae !important;
        }

        a.bg-cyan:hover {
            background-color: #1596aa !important;
        }

        a.bg-gray:hover {
            background-color: #95a9b0 !important;
        }

        a.bg-secondary:hover {
            background-color: #ecf0f2 !important;
        }

        .img-hover:hover img {
            opacity: .64;
        }

        .theme-dark a.bg-secondary:hover {
            background-color: #354258 !important;
        }

        .theme-dark .btn.bg-bordered:hover {
            background-color: #467fcf !important;
            color: #fff !important;
        }

        .theme-dark .btn.bg-bordered:hover .btn-span {
            color: #fff !important;
        }

        @media only screen and (max-width: 560px) {
            body {
                font-size: 14px !important;
            }

            .content {
                padding: 24px !important;
            }

            .content-image-text {
                padding: 24px !important;
            }

            .content-image {
                height: 100px !important;
            }

            .content-image-text {
                padding-top: 96px !important;
            }

            h1 {
                font-size: 24px !important;
            }

            .h1 {
                font-size: 24px !important;
            }

            h2 {
                font-size: 20px !important;
            }

            .h2 {
                font-size: 20px !important;
            }

            h3 {
                font-size: 18px !important;
            }

            .h3 {
                font-size: 18px !important;
            }

            .col {
                display: table !important;
                width: 100% !important;
            }

            .col-spacer {
                display: table !important;
                width: 100% !important;
            }

            .col-spacer-xs {
                display: table !important;
                width: 100% !important;
            }

            .col-spacer-sm {
                display: table !important;
                width: 100% !important;
            }

            .col-hr {
                display: table !important;
                width: 100% !important;
            }

            .row {
                display: table !important;
                width: 100% !important;
            }

            .col-hr {
                border: 0 !important;
                height: 24px !important;
                width: auto !important;
                background: transparent !important;
            }

            .col-spacer {
                width: 100% !important;
                height: 24px !important;
            }

            .col-spacer-sm {
                height: 16px !important;
            }

            .col-spacer-xs {
                height: 8px !important;
            }

            .chart-cell-spacer {
                width: 4px !important;
            }

            .text-mobile-center {
                text-align: center !important;
            }

            .d-mobile-none {
                display: none !important;
            }
        }
    </style>
</head>

<body class="bg-body" style="font-size: 15px; margin: 0; padding: 0; line-height: 160%; mso-line-height-rule: exactly; color: #444444; width: 100%; font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;" bgcolor="#f5f7fb">
<center>
    <table class="main bg-body" width="100%" cellspacing="0" cellpadding="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;" bgcolor="#f5f7fb">
        <tr>
            <td align="center" valign="top" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;">
                <!--[if (gte mso 9)|(IE)]>
                <table border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td align="center" valign="top" width="640">
                <![endif]-->
                <span class="preheader" style="font-size: 0; padding: 0; display: none; max-height: 0; mso-hide: all; line-height: 0; color: transparent; height: 0; max-width: 0; opacity: 0; overflow: hidden; visibility: hidden; width: 0;">You have a new worship assignment!  {{ $pick->position->name }}</span>
                <table class="wrap" cellspacing="0" cellpadding="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left;">
                    <tr>
                        <td class="p-sm" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding: 8px;">
                            <table cellpadding="0" cellspacing="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%;">
                                <tr>
                                    <td class="py-lg" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding-top: 24px; padding-bottom: 24px;">
                                        <table cellspacing="0" cellpadding="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%;">
                                            <tr>
                                                <td style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; text-align: center;">
                                                    <a href="https://app.lightpost.app" style="color: #467fcf; text-decoration: none;"><img src="https://lightpost-email-static.nyc3.cdn.digitaloceanspaces.com/tabler/assets/lightpost-logo.png" width="200" alt="" style="line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none;"/></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            <div class="main-content">
                                <table class="box" cellpadding="0" cellspacing="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%; border-radius: 3px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #f0f0f0;" bgcolor="#ffffff">
                                    <tr>
                                        <td style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;">
                                            <table cellpadding="0" cellspacing="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%;">
                                                <tr>
                                                    <td class="content" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding: 40px 48px; text-align: center">
                                                        <h1 class="text-center" style="font-weight: 300; font-size: 28px; line-height: 130%; margin: 0 0 .5em;" align="center">An assignment has been <strong>declined</strong>.</h1>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="content text-center pt-0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding: 0 48px 40px;" align="center">
                                                        <img src="https://lightpost-email-static.nyc3.cdn.digitaloceanspaces.com/fa/png/clipboard-list-check.png" class=" avatar " width="64" alt="" style="line-height: 100%; border: 0 none; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0;"/>
                                                        <div class="h3 mb-0 mt-sm font-strong" style="font-weight: 600; font-size: 24px; line-height: 120%; margin: 8px 0 0;">
                                                            {{ $pick->user ? $pick->user?->name : 'Unassigned' }}
                                                        </div>
                                                        <div class="h3 mb-0 mt-sm font-strong" style="font-weight: 600; font-size: 24px; line-height: 120%; margin: 8px 0 0;">
                                                            {{ $pick->position->name }}
                                                        </div>
                                                        <p class="text-muted" style="color: #777; margin: 0 0 1em;">
                                                            {!! $pick->getTimePeriodSentenceForHumans() !!}
                                                        </p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="content pt-0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding: 0 48px 40px;">
                                                        <table class="row" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed;" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td class="col" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;" valign="top">
                                                                    <table cellspacing="0" cellpadding="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%;">
                                                                        <tr>
                                                                            <td align="center" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif;">
                                                                                <table cellpadding="0" cellspacing="0" border="0" class="bg-secondary rounded" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: separate; width: 100%; color: #ffffff; border-radius: 3px;" bgcolor="#f5f7f8">
                                                                                    <tr>
                                                                                        <td align="center" valign="top" class="lh-1" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; line-height: 100%;">
                                                                                            <a href="{{ route('admin.worship-assignments.periods.view', [$pick->group, $pick->period]) }}" class="btn bg-secondary border-secondary" style="color: #9eb0b7; padding: 12px 32px; border: 1px solid #f5f7f8; text-decoration: none; white-space: nowrap; font-weight: 600; font-size: 16px; border-radius: 3px; line-height: 100%; display: block; -webkit-transition: .3s background-color; transition: .3s background-color; background-color: #f5f7f8;">
                                                                                                <span class="btn-span" style="color: #000000; font-size: 16px; text-decoration: none; white-space: nowrap; font-weight: 600; line-height: 100%;">
                                                                                                    View Assignments in Admin ➡
                                                                                                </span>
                                                                                            </a>
                                                                                        </td>
                                                                                    </tr>
                                                                                </table>
                                                                            </td>
                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                @if($pick->notes)
                                                    <tr>
                                                        <td class="content text-center pt-0" style=" font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding: 0 48px 40px;" align="center">
                                                            <div class="mb-0 mt-sm font-strong" style="font-weight: 500; color:#6B21A8; font-size: 16px; line-height: 120%; margin: 8px 0 0;">
                                                                <strong>Special instructions:</strong>
                                                            </div>
                                                            <div class="mb-0 mt-sm font-strong" style="border: 1px solid #6B21A8; padding-top: 10px; padding-bottom: 10px; font-weight: 500; color:#6B21A8; font-size: 16px; line-height: 120%; margin: 8px 0 0;">
                                                                {!! nl2br($pick->notes) !!}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                                @if($pick->position->notes)
                                                    <tr>
                                                        <td class="content text-center pt-0" style=" font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding: 0 48px 40px;" align="center">
                                                            <div class="mb-0 mt-sm font-strong" style="font-weight: 500; color:#666666; font-size: 16px; line-height: 120%; margin: 8px 0 0;">
                                                                <strong>Notes about this assignments:</strong>
                                                            </div>
                                                            <div class="mb-0 mt-sm font-strong" style="border: 1px solid #555555; padding-top: 10px; padding-bottom: 10px; font-weight: 500; color:#666666; font-size: 16px; line-height: 120%; margin: 8px 0 0;">
                                                                {!! nl2br($pick->position->notes) !!}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                                <tr>
                                                    <td class="content border-top font-sm" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; font-size: 13px; border-top-width: 1px; border-top-color: #f0f0f0; border-top-style: solid; padding: 40px 48px;">
                                                        <p style="margin: 0">

                                                        </p>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <table cellspacing="0" cellpadding="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%;">
                                <tr>
                                    <td class="py-xl" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding-top: 18px; padding-bottom: 48px;">
                                        <table class="font-sm text-center text-muted" cellspacing="0" cellpadding="0" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; border-collapse: collapse; width: 100%; color: #9eb0b7; text-align: center; font-size: 13px;">
                                            <tr>
                                                <td class="pt-md" style="font-family: Open Sans, -apple-system, BlinkMacSystemFont, Roboto, Helvetica Neue, Helvetica, Arial, sans-serif; padding-top: 16px;">
                                                    You are receiving this email because you are an active member of the {{ $pick->account->name }} and your name is included in the list of people to be notified of declined assignments.
                                                    <a href="#" class="text-muted" style="color: #9eb0b7; text-decoration: none;"></a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
                <!--[if (gte mso 9)|(IE)]>
                </td>
                </tr>
                </table>
                <![endif]-->
            </td>
        </tr>
    </table>
</center>
</body>

</html>