<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        @if(array_key_exists('first_name', $included_fields))
            <th>{{ $included_fields['first_name'] }}</th>
        @endif
        @if(array_key_exists('last_name', $included_fields))
            <th>{{ $included_fields['last_name'] }}</th>
        @endif
        @if(array_key_exists('email', $included_fields))
            <th>{{ $included_fields['email'] }}</th>
        @endif
        @if(array_key_exists('home_phone', $included_fields))
            <th>{{ $included_fields['home_phone'] }}</th>
        @endif
        @if(array_key_exists('mobile_phone', $included_fields))
            <th>{{ $included_fields['mobile_phone'] }}</th>
        @endif
        @if(array_key_exists('home_address', $included_fields))
            <th>{{ $included_fields['home_address'] }}</th>
        @endif
        @if(array_key_exists('mailing_address', $included_fields))
            <th>{{ $included_fields['mailing_address'] }}</th>
        @endif
        @if(array_key_exists('date_baptism', $included_fields))
            <th>{{ $included_fields['date_baptism'] }}</th>
        @endif
        @if(array_key_exists('family_role', $included_fields))
            <th>{{ $included_fields['family_role'] }}</th>
        @endif
        @if(array_key_exists('marital_status', $included_fields))
            <th>{{ $included_fields['marital_status'] }}</th>
        @endif
        @if($category)
            <th>Category</th>
        @endif
        @if($area)
            <th>Area</th>
        @endif
        @if($subarea)
            <th>Subarea</th>
        @endif
    </tr>
    </thead>
    <tbody>
    @foreach($users as $user)
        <tr>
            @if(array_key_exists('first_name', $included_fields))
                <td>
                    {{ $user->display_first_name }}
                </td>
            @endif
            @if(array_key_exists('last_name', $included_fields))
                <td>
                    {{ $user->last_name }}
                </td>
            @endif
            @if(array_key_exists('email', $included_fields))
                <td>
                    @if($user->getBestEmail())
                        <a href="mailto:{{ optional($user->getBestEmail())->email }}">{{ optional($user->getBestEmail())->email }}</a>
                    @endif
                </td>
            @endif
            @if(array_key_exists('home_phone', $included_fields))
                <td class="text-center">
                    {{ optional($user->getFamilyPhone())->formattedNumber() }}
                </td>
            @endif
            @if(array_key_exists('mobile_phone', $included_fields))
                <td class="text-center">
                    {{ optional($user->getMobilePhone())->formattedNumber() }}
                </td>
            @endif
            @if(array_key_exists('home_address', $included_fields))
                <td class="text-center">
                    {{ optional($user->getFamilyAddress())->getAddressString() }}
                </td>
            @endif
            @if(array_key_exists('mailing_address', $included_fields))
                <td class="text-center">
                    {{ optional($user->getMailingAddress())->getAddressString() }}
                </td>
            @endif
            @if(array_key_exists('date_baptism', $included_fields))
                <td>
                    {{ $user->is_baptized ? 'Yes' : 'No' }}
                </td>
            @endif
            @if(array_key_exists('family_role', $included_fields))
                <td>
                    {{ $user->family_role }}
                </td>
            @endif
            @if(array_key_exists('marital_status', $included_fields))
                <td>
                    {{ $user->marital_status }}
                </td>
            @endif
            @if($category)
                <td>
                    {{ $category->name }}
                </td>
            @endif
            @if($area)
                <td>
                    {{ $area->name }}
                </td>
            @endif
            @if($subarea)
                <td>
                    {{ $subarea->name }}
                </td>
            @endif
        </tr>
    @endforeach
    </tbody>
</table>