@extends('app._layout._app')

@section('title', 'Submit Prayer Request')

@section('content')

    <div class="mb-4 md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <h1 class="flex">
            <svg class="my-auto w-9 h-9 mr-3 text-gray-900" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="praying-hands" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                <path fill="currentColor" d="M272 191.91c-17.6 0-32 14.4-32 32v80c0 8.84-7.16 16-16 16s-16-7.16-16-16v-76.55c0-17.39 4.72-34.47 13.69-49.39l77.75-129.59c9.09-15.16 4.19-34.81-10.97-43.91-14.45-8.67-32.72-4.3-42.3 9.21-.2.23-.62.21-.79.48l-117.26 175.9C117.56 205.9 112 224.31 112 243.29v80.23l-90.12 30.04A31.974 31.974 0 0 0 0 383.91v96c0 10.82 8.52 32 32 32 2.69 0 5.41-.34 8.06-1.03l179.19-46.62C269.16 449.99 304 403.8 304 351.91v-128c0-17.6-14.4-32-32-32zm346.12 161.73L528 323.6v-80.23c0-18.98-5.56-37.39-16.12-53.23L394.62 14.25c-.18-.27-.59-.24-.79-.48-9.58-13.51-27.85-17.88-42.3-9.21-15.16 9.09-20.06 28.75-10.97 43.91l77.75 129.59c8.97 14.92 13.69 32 13.69 49.39V304c0 8.84-7.16 16-16 16s-16-7.16-16-16v-80c0-17.6-14.4-32-32-32s-32 14.4-32 32v128c0 51.89 34.84 98.08 84.75 112.34l179.19 46.62c2.66.69 5.38 1.03 8.06 1.03 23.48 0 32-21.18 32-32v-96c0-13.77-8.81-25.99-21.88-30.35z"></path>
            </svg>
            Submit a Prayer Request
        </h1>
    </div>

    <div class="max-w-lg sm:max-w-none rounded-sm border border-gray-300">
        <form action="{{ route('app.prayers.request.submit') }}" method="post" id="save-form">
            @csrf
            <div class="bg-white p-4 pb-12 rounded-lg">
                <div class="" id="general-info-tab">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            Prayer Request
                        </h3>
                        <p class="mt-1 max-w-2xl leading-5 text-gray-500">
                            Give your request a title and enter some details.
                        </p>
                    </div>
                    <hr class="border-gray-300 my-3">
                    <div class="">
                        <div class="">
                            <label for="title" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                Title
                            </label>
                            <div class="mt-1 sm:mt-0 sm:col-span-2">
                                <div class="max-w-lg rounded-md shadow-xs sm:max-w-md">
                                    <input type="text" id="title" name="title" value="" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <label for="details" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                Request Details
                            </label>
                            <div class="mt-1 sm:mt-0 sm:col-span-2">
                                <div class="max-w-lg rounded-md shadow-xs sm:max-w-md">
                                    <textarea rows="3" type="text" id="details" name="details" value="" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex mt-8">
                    <span class="rounded-md shadow-xs">
                        <button type="submit" class=" flex w-full justify-center py-2 px-8 border border-transparent leading-5 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-500 focus:outline-hidden focus:border-blue-700 focus:ring-blue active:bg-blue-700 transition duration-150 ease-in-out">
                            <svg class="h-5 w-5 " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            &nbsp;Submit Request
                        </button>
                    </span>
                </div>
            </div>
        </form>
    </div>

@endsection
