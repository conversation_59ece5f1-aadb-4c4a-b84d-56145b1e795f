@extends('app._layout._app')

@section('title', 'Prayer')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Prayer Request
            </h1>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none py-4">
        <div class="bg-white overflow-hidden sm:rounded-lg border border-gray-200">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-xl leading-6 font-medium text-gray-900">
                    {{ $prayer->title }}
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Requested on:
                    <time datetime="{{ $prayer->updated_at->format('Y-m-d') }}">{{ $prayer->created_at->format('F d, Y') }}</time>
                    <br>
                    Last updated:
                    <time datetime="{{ $prayer->last_updated_at->format('Y-m-d') }}">{{ $prayer->last_updated_at->diffForHumans() }}</time>
                </p>
            </div>
            <div class="border-t border-gray-200 px-4 py-6 sm:px-6">
                <div class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                    <div>
                        <div class="sm:col-span-2">
                            <dt class="text-lg font-medium text-gray-500">
                                Original Request
                            </dt>
                            <dd class="mt-1 text-gray-900">
                                @if($prayer->details)
                                    {{ $prayer->details }}
                                @else
                                    <div class="mt-4">
                                <span class="bg-yellow-100 px-6 py-2 rounded-sm">
                                    No details.
                                </span>
                                    </div>
                                @endif
                            </dd>
                        </div>
                        @if($prayer->updates->isNotEmpty())
                            <div class="sm:col-span-2 mt-6">
                                <dt class="text-lg font-medium text-gray-500">
                                    Updates
                                </dt>
                                <dd class="mt-1 text-gray-900">
                                    <div class="flow-root">
                                        <ul class="-mb-8">
                                            @foreach($prayer->updates as $update)
                                                <li>
                                                    <div class="relative pb-8">
                                                        @if(!$loop->last)
                                                            <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                                        @endif
                                                        <div class="relative flex items-start space-x-3">
                                                            <div>
                                                                <div class="relative px-1">
                                                                    <div class="h-8 w-8 bg-gray-100 rounded-full ring-8 ring-white flex items-center justify-center">
                                                                        <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"/>
                                                                        </svg>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="min-w-0 flex-1">
                                                                <div>
                                                                    <p class="mt-0.5 text-sm text-gray-500">
                                                                        <time datetime="{{ $update->created_at->format('Y-m-d') }}">{{ $update->created_at->format('M d, Y') }}</time>
                                                                        &dash;
                                                                        <time class="text-gray-400" datetime="{{ $update->created_at->format('Y-m-d') }}">{{ $update->created_at->diffForHumans() }}</time>
                                                                    </p>
                                                                </div>
                                                                <div class="mt-2 text-gray-700">
                                                                    <p>
                                                                        {{ $update->details }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </dd>
                            </div>
                        @endif
                    </div>
                    <div class="lg:col-span-1">
                        @if($prayer->userTags->isNotEmpty())
                            <dt class="text-lg font-medium text-gray-500">
                                Member Tags
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <ul class="divide-y divide-gray-200">
                                    @foreach($prayer->userTags as $user)
                                        <?php $user = App\Users\User::find($user->user_id); ?>
                                        <li class="py-4 flex">
                                            <a href="{{ route('app.directory.view.family', $user) }}">
                                                @if($user->primaryPhoto(true))
                                                    {!! $user->primaryPhoto(true)->getImgTag(50, ' h-10 bg-gray-300 rounded-sm shrink-0') !!}
                                                @else
                                                    <svg class="h-10 w-10 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                @endif
                                            </a>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">
                                                    <a href="{{ route('app.directory.view.family', $user) }}">
                                                        {{ $user->name }}
                                                    </a>
                                                </p>
                                                @if($user->getBestEmail())
                                                    <p class="text-sm text-gray-500">
                                                        {{ $user->getBestEmail()->email }}
                                                    </p>
                                                @endif
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </dd>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection