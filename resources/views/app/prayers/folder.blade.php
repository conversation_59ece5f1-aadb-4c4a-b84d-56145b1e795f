@extends('app._layout._app')

@section('title', 'Prayers List')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                <svg class="text-center w-9 h-9 mr-3 text-gray-900" aria-hidden="true" focusable="false" data-prefix="fas" data-icon="praying-hands" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                    <path fill="currentColor" d="M272 191.91c-17.6 0-32 14.4-32 32v80c0 8.84-7.16 16-16 16s-16-7.16-16-16v-76.55c0-17.39 4.72-34.47 13.69-49.39l77.75-129.59c9.09-15.16 4.19-34.81-10.97-43.91-14.45-8.67-32.72-4.3-42.3 9.21-.2.23-.62.21-.79.48l-117.26 175.9C117.56 205.9 112 224.31 112 243.29v80.23l-90.12 30.04A31.974 31.974 0 0 0 0 383.91v96c0 10.82 8.52 32 32 32 2.69 0 5.41-.34 8.06-1.03l179.19-46.62C269.16 449.99 304 403.8 304 351.91v-128c0-17.6-14.4-32-32-32zm346.12 161.73L528 323.6v-80.23c0-18.98-5.56-37.39-16.12-53.23L394.62 14.25c-.18-.27-.59-.24-.79-.48-9.58-13.51-27.85-17.88-42.3-9.21-15.16 9.09-20.06 28.75-10.97 43.91l77.75 129.59c8.97 14.92 13.69 32 13.69 49.39V304c0 8.84-7.16 16-16 16s-16-7.16-16-16v-80c0-17.6-14.4-32-32-32s-32 14.4-32 32v128c0 51.89 34.84 98.08 84.75 112.34l179.19 46.62c2.66.69 5.38 1.03 8.06 1.03 23.48 0 32-21.18 32-32v-96c0-13.77-8.81-25.99-21.88-30.35z"></path>
                </svg>
                Prayer Requests
            </h1>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none py-4">
        <div class="bg-white overflow-hidden sm:rounded-md border border-gray-200">
            <ul class="divide-y divide-gray-200">
                <li>
                    <div class="block bg-gray-50 text-black">
                        <div class="px-4 py-4 flex items-center sm:px-6">
                            <div class="min-w-0 flex-1 flex flex-row justify-between">
                                <div class="min-w-0 flex flex-row">
                                    <x-heroicon-s-folder class="h-6 w-6 text-blue-400 mr-3"/>
                                    <div class="flex text-lg font-medium truncate">
                                        <p>{{ $folder->name }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                @forelse($prayers as $prayer)
                    <li>
                        <a href="{{ route('app.prayers.view', $prayer) }}" class="block hover:bg-gray-50 text-black">
                            <div class="px-4 py-2 flex items-center sm:px-6">
                                <div class="min-w-0 flex-1 sm:flex sm:items-center sm:justify-between">
                                    <div>
                                        <div class="flex text-lg truncate">
                                            <p>{{ $prayer->title }}</p>
                                        </div>
                                        <div class="flex">
                                            <div class="flex items-center text-sm text-gray-400">
                                                <p>
                                                    <span class="text-gray-300">Updated</span>
                                                    <time datetime="{{ $prayer?->updated_at?->format('Y-m-d') }}">{{ $prayer?->updated_at?->diffForHumans() }}</time>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-4 shrink-0 sm:mt-0">
                                    <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium {{ $prayer?->updates?->count() == 0 ? 'bg-gray-50 text-gray-300' : 'bg-blue-100 text-blue-800' }}">
                                        {{ $prayer?->updates?->count() }} Updates
                                    </span>
                                    </div>
                                </div>
                                <div class="ml-5 shrink-0">
                                    <!-- Heroicon name: chevron-right -->
                                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                            </div>
                        </a>
                    </li>
                @empty
                    <div class="text-center">
                        <div class="px-4 py-2 bg-yellow-100 my-6 rounded-sm"><strong>No prayers yet!</strong><br><span class="text-gray-600">Please check back later.</span></div>
                    </div>
                @endforelse
            </ul>
        </div>

        <div class="mt-6">
            {{ $prayers->links() }}
        </div>
    </div>

@endsection