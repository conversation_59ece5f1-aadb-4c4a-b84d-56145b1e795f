@extends('app._layout._app')

@section('title', $group->name . ' Group')

@section('content')

    <style type="text/css">
        .textarea-grow-wrap {
            /* easy way to plop the elements on top of each other and have them both sized based on the tallest one's height */
            display: grid;
        }

        .textarea-grow-wrap::after {
            /* Note the weird space! Needed to preventy jumpy behavior */
            content: attr(data-replicated-value) " ";

            /* This is how textarea text behaves */
            white-space: pre-wrap;

            /* Hidden from view, clicks, and screen readers */
            visibility: hidden;
        }

        .textarea-grow-wrap > textarea {
            /* You could leave this, but after a user resizes, then it ruins the auto sizing */
            resize: none;

            /* Firefox shows scrollbar on growth, you can hide like this. */
            overflow: hidden;
        }

        .textarea-grow-wrap > textarea,
        .textarea-grow-wrap::after {
            /* Identical styling required!! */

            /* Place on top of each other */
            grid-area: 1 / 1 / 2 / 2;
        }
    </style>

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                <div class="bg-green-100 rounded-sm py-1 px-2 mr-2">
                    <svg class="h-9 w-9 text-gray-800" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                </div>
                {{ $group->name }}
            </h1>
        </div>
        <div class="mt-2 md:mt-0">
            <a href="{{ route('app.groups.settings', $group) }}" class="flex items-center text-gray-600 px-3 py-2 rounded-sm hover:bg-blue-600 hover:text-white bg-gray-200">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                &nbsp;Settings
            </a>
        </div>
    </div>

    <div class="flex py-4 xl:grid xl:grid-cols-12 lg:gap-4">
        <div class="w-full md:col-span-8">

            @livewire('app.groups.posts.create-post', ['group' => $group])

            @foreach($posts as $post)

                @livewire('app.groups.post', ['post' => $post], key('user-group-post-' . $post->id))

            @endforeach

            @if($posts->count() == 0)
                <div class="flex-1 my-12">
                    <div class="px-8 py-6 border border-gray-300 bg-white rounded-sm text-center">No posts exist yet! 😬</div>
                </div>
            @endif

            {{ $posts->links() }}

        </div>
        <div class="hidden md:block md:col-span-4">
            <div class="px-5 py-4 mb-2 ml-4 bg-white rounded-sm border border-gray-200">
                <h3 class="text-xl font-medium">About</h3>
                <div class="font-normal my-4">
                    {{ $group->description }}
                </div>
                <div class="flex mb-3">
                    <div class="pr-2 py-1">
                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                    </div>
                    <div class="font-semibold">{{ $group->users()->count() }} {{ \Illuminate\Support\Str::plural('member', $group->users()->count()) }}</div>
                </div>
                @if(!$group->allow_members_to_post)
                    <div class="flex mb-3">
                        <div class="pr-2 py-1">
                            <x-heroicon-o-no-symbol class="w-4"/>
                        </div>
                        <div>
                            <div class="font-semibold">No Member Posting</div>
                            <div class="text-sm">Only admins can create new posts.</div>
                        </div>
                    </div>
                @endif
                @if(!$group->allow_members_to_comment)
                    <div class="flex mb-3">
                        <div class="pr-2 py-1">
                            <x-heroicon-o-no-symbol class="w-4"/>
                        </div>
                        <div>
                            <div class="font-semibold">No Comments</div>
                            <div class="text-sm">Comments are disabled.</div>
                        </div>
                    </div>
                @endif
                @if($group->is_hidden)
                    <div class="flex mb-3">
                        <div class="pr-2 py-1">
                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"/>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold">Hidden</div>
                            <div class="text-sm">Only members of this group can find this group.</div>
                        </div>
                    </div>
                @else
                    <div class="flex mb-3">
                        <div class="pr-2 py-1">
                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        </div>
                        <div>
                            <div class="font-semibold">Public</div>
                            <div class="text-sm">Any church member can see this group exists.</div>
                        </div>
                    </div>
                @endif
                {{--                @if(!$group->allow_individual_to_toggle)--}}
                {{--                    <div class="flex mb-3">--}}
                {{--                        <div class="pr-2 py-1">--}}
                {{--                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">--}}
                {{--                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z"/>--}}
                {{--                            </svg>--}}
                {{--                        </div>--}}
                {{--                        <div>--}}
                {{--                            <div class="font-semibold">Open Membership</div>--}}
                {{--                            <div class="text-sm">Any member that can see this group can join or leave this group.</div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                @else--}}
                {{--                    <div class="flex mb-3">--}}
                {{--                        <div class="pr-2 py-1">--}}
                {{--                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">--}}
                {{--                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>--}}
                {{--                            </svg>--}}
                {{--                        </div>--}}
                {{--                        <div>--}}
                {{--                            <div class="font-semibold">Closed Membership</div>--}}
                {{--                            <div class="text-sm">Only admins can add/remove members.</div>--}}
                {{--                        </div>--}}
                {{--                    </div>--}}
                {{--                @endif--}}
                @if(false)
                    @if($group->allow_members_to_post)
                        <div class="flex mb-3">
                            <div class="pr-2 py-1">
                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold">Open Posting</div>
                                <div class="text-sm">Any member can create a post.</div>
                            </div>
                        </div>
                    @else
                        <div class="flex mb-3">
                            <div class="pr-2 py-1">
                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold">Closed Posting</div>
                                <div class="text-sm">Only admins can create new posts.</div>
                            </div>
                        </div>
                    @endif
                    @if(!$group->allow_members_to_comment)
                        <div class="flex mb-3">
                            <div class="pr-2 py-1">
                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold">Open Commenting</div>
                                <div class="text-sm">Any member can comment.</div>
                            </div>
                        </div>
                    @else
                        <div class="flex mb-3">
                            <div class="pr-2 py-1">
                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="font-semibold">Closed Commenting</div>
                                <div class="text-sm">Comments are turned off.</div>
                            </div>
                        </div>
                    @endif
                @endif

                <div class="mt-10 mb-6">
                    <form method="post" action="{{ route('app.groups.leave', $group) }}" id="leave-group-{{ $group->id }}">@csrf()</form>
                    <span onclick="document.getElementById('leave-group-{{ $group->id }}').submit()" class="cursor-pointer flex flex-row justify-center px-3 py-1 border border-red-300 rounded-sm bg-white hover:border-red-700 text-red-400 hover:text-red-700 hover:bg-red-100">
                        <x-heroicon-s-arrow-right-on-rectangle class="mr-2 my-auto w-4 h-4"/>
                        <span class="text-sm">Leave Group</span>
                    </span>
                </div>

                @if($group->allow_members_to_view_membership || auth()->user()->isGroupAdmin($group))
                    <h3 class="pt-2 text-xl font-medium">Members</h3>

                    <div>
                        <div class="flow-root my-6">
                            <ul class="-my-5 divide-y divide-gray-200">
                                @foreach($group->users()->get() as $user)
                                    <li class="py-2">
                                        <div class="flex items-center space-x-2">
                                            @if($user->avatar)
                                                <div class="shrink-0">
                                                    <img class="h-8 w-8 rounded-full" src="{{ $user->avatar->getCdnUrl(512) }}" alt="">
                                                </div>
                                            @endif
                                            <div class="flex-1 min-w-0">
                                                <p class="font-medium text-gray-900 truncate">
                                                    {{ $user->name }}
                                                </p>
                                            </div>
                                            <div>
                                                <a href="{{ route('app.directory.view.family', $user) }}" class="inline-flex items-center shadow-xs px-2.5 py-0.5 border border-gray-300 text-sm leading-5 font-medium rounded-full text-gray-700 bg-white hover:bg-gray-50">
                                                    View
                                                </a>
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('scripts')
        <script type="text/javascript" defer>
            var channelGroupPostComments = pusher.subscribe('private-{{ auth()->user()->account_id }}.groups.{{ $group->id }}.posts.comments');
            var channelGroupPosts = pusher.subscribe('private-{{ auth()->user()->account_id }}.groups.{{ $group->id }}.posts');

            pusher.connection.bind('error', function (error) {
                console.error('connection error', error)
            });
            channelGroupPostComments.bind('pusher:error', function (err) {
                console.log('general error', err);
            });
            channelGroupPostComments.bind('pusher:subscription_error', function (err) {
                console.error('subscription error', err);
            });

            // Channel Listening
            channelGroupPostComments.bind('comment.created', function (data) {
                console.log('A new comment was posted to Post # ' + data.user_group_post_id);
                Livewire.dispatch('refreshPost.' + data.user_group_post_id);
            });
            channelGroupPosts.bind('comment.created', function (data) {
                console.log('A new comment was posted to Post # ' + data.user_group_post_id);
                Livewire.dispatch('refreshPost.' + data.user_group_post_id);
            });
        </script>
    @endpush

@endsection