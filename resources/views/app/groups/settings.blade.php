@extends('app._layout._app')

@section('title', 'Group Settings')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                <svg class="text-center w-8 h-8 mr-3 text-gray-900" aria-hidden="true" focusable="false" data-prefix="fal" data-icon="cog" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                    <path fill="currentColor" d="M482.696 299.276l-32.61-18.827a195.168 195.168 0 0 0 0-48.899l32.61-18.827c9.576-5.528 14.195-16.902 11.046-27.501-11.214-37.749-31.175-71.728-57.535-99.595-7.634-8.07-19.817-9.836-29.437-4.282l-32.562 18.798a194.125 194.125 0 0 0-42.339-24.48V38.049c0-11.13-7.652-20.804-18.484-23.367-37.644-8.909-77.118-8.91-114.77 0-10.831 2.563-18.484 12.236-18.484 23.367v37.614a194.101 194.101 0 0 0-42.339 24.48L105.23 81.345c-9.621-5.554-21.804-3.788-29.437 4.282-26.36 27.867-46.321 61.847-57.535 99.595-3.149 10.599 1.47 21.972 11.046 27.501l32.61 18.827a195.168 195.168 0 0 0 0 48.899l-32.61 18.827c-9.576 5.528-14.195 16.902-11.046 27.501 11.214 37.748 31.175 71.728 57.535 99.595 7.634 8.07 19.817 9.836 29.437 4.283l32.562-18.798a194.08 194.08 0 0 0 42.339 24.479v37.614c0 11.13 7.652 20.804 18.484 23.367 37.645 8.909 77.118 8.91 114.77 0 10.831-2.563 18.484-12.236 18.484-23.367v-37.614a194.138 194.138 0 0 0 42.339-24.479l32.562 18.798c9.62 5.554 21.803 3.788 29.437-4.283 26.36-27.867 46.321-61.847 57.535-99.595 3.149-10.599-1.47-21.972-11.046-27.501zm-65.479 100.461l-46.309-26.74c-26.988 23.071-36.559 28.876-71.039 41.059v53.479a217.145 217.145 0 0 1-87.738 0v-53.479c-33.621-11.879-43.355-17.395-71.039-41.059l-46.309 26.74c-19.71-22.09-34.689-47.989-43.929-75.958l46.329-26.74c-6.535-35.417-6.538-46.644 0-82.079l-46.329-26.74c9.24-27.969 24.22-53.869 43.929-75.969l46.309 26.76c27.377-23.434 37.063-29.065 71.039-41.069V44.464a216.79 216.79 0 0 1 87.738 0v53.479c33.978 12.005 43.665 17.637 71.039 41.069l46.309-26.76c19.709 22.099 34.689 47.999 43.929 75.969l-46.329 26.74c6.536 35.426 6.538 46.644 0 82.079l46.329 26.74c-9.24 27.968-24.219 53.868-43.929 75.957zM256 160c-52.935 0-96 43.065-96 96s43.065 96 96 96 96-43.065 96-96-43.065-96-96-96zm0 160c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z"></path>
                </svg>
                Settings - {{ $group->name }}
            </h1>
        </div>
    </div>

    <div class="bg-white border border-gray-200 max-w-lg sm:max-w-none mt-4 rounded-sm">
        <form class="divide-y divide-gray-200 lg:col-span-9" action="{{ route('app.groups.settings.save', $group) }}" method="POST">
            @method('post')
            @csrf()

            <div class="p-3 pl-6">
                <div class="text-xl font-semibold">
                    Mobile Notifications
                </div>
                <div class="text-sm text-gray-500">
                    New group post activity options.
                </div>
            </div>

            <div class="pt-1 divide-y divide-gray-200">
                <div class="px-4 space-y-2 sm:px-6">
                    <ul class="divide-y divide-gray-200">
                        <li class="py-4 flex items-center space-x-4" x-data="{ on:  {{ $settings->receive_group_post_mobile_notifications ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="receive_group_post_mobile_notifications" type="checkbox" name="receive_group_post_mobile_notifications" value="1" x-bind:checked="on" class="hidden" @isChecked($settings->receive_group_post_mobile_notifications)/>
                                    </span>
                            <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-11" class=" leading-5 font-medium text-gray-900">
                                            New posts
                                        </span>
                                <p id="privacy-option-description-11" class=" leading-5 text-gray-500">
                                    Would you like to receive notifications of new Group Posts?
                                </p>
                            </div>
                        </li>
                        <li class="py-4 flex items-center space-x-4" x-data="{ on: {{ $settings->receive_all_group_post_comment_mobile_notifications ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="receive_all_group_post_comment_mobile_notifications" type="checkbox" name="receive_all_group_post_comment_mobile_notifications" value="1" x-bind:checked="on" class="hidden" @isChecked($settings->receive_all_group_post_comment_mobile_notifications)/>
                                    </span>
                            <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-12" class=" leading-5 font-medium text-gray-900">
                                            Comments to <strong>all</strong> posts
                                        </span>
                                <p id="privacy-option-description-12" class=" leading-5 text-gray-500">
                                    Enabling this will notify you every time someone comments on <strong>any</strong> post.
                                </p>
                            </div>
                        </li>
                        <li class="py-4 flex items-center space-x-4" x-data="{ on: {{ $settings->receive_group_own_post_comment_mobile_notifications ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="receive_group_own_post_comment_mobile_notifications" type="checkbox" name="receive_group_own_post_comment_mobile_notifications" value="1" x-bind:checked="on" class="hidden" @isChecked($settings->receive_group_own_post_comment_mobile_notifications)/>
                                    </span>
                            <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-12" class=" leading-5 font-medium text-gray-900">
                                            Comments to <strong>your</strong> posts
                                        </span>
                                <p id="privacy-option-description-12" class=" leading-5 text-gray-500">
                                    Enabling this will notify you every time someone comments on a post <strong>you</strong> created.
                                </p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="p-3 pl-6">
                <div class="text-xl font-semibold">
                    Group Messages
                </div>
                <div class="text-sm text-gray-500">
                    These relate to one-way messages sent to the entire group.
                </div>
            </div>

            <div class="pt-1 divide-y divide-gray-200">
                <div class="px-4 space-y-2 sm:px-6">
                    <ul class="divide-y divide-gray-200">
                        <li class="py-4 flex items-center space-x-4" x-data="{ on:  {{ $settings->receive_group_emails ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="receive_group_emails" type="checkbox" name="receive_group_emails" value="1" x-bind:checked="on" class="hidden" @isChecked($settings->receive_group_emails)/>
                                    </span>
                            <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-1" class=" leading-5 font-medium text-gray-900">
                                            Receive group emails?
                                        </span>
                                <p id="privacy-option-description-1" class="hidden leading-5 text-gray-500">
                                    Admins can send emails to your primary email address.
                                </p>
                            </div>
                        </li>
                        <li class="py-4 flex items-center space-x-4" x-data="{ on: {{ $group->receive_group_sms ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="receive_group_sms" type="checkbox" name="receive_group_sms" value="1" x-bind:checked="on" class="hidden" @isChecked($group->receive_group_sms)/>
                                    </span>
                            <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-2" class=" leading-5 font-medium text-gray-900">
                                            Receive group SMS messages?
                                        </span>
                                <p id="privacy-option-description-2" class="hidden leading-5 text-gray-500">
                                    Admins can send SMS messages to your primary phone number.
                                </p>
                            </div>
                        </li>
                        <li class="py-4 flex items-center space-x-4" x-data="{ on: {{ $settings->receive_group_voice ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="receive_group_voice" type="checkbox" name="receive_group_voice" value="1" x-bind:checked="on" class="hidden" @isChecked($settings->receive_group_voice)/>
                                    </span>
                            <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-3" class=" leading-5 font-medium text-gray-900">
                                            Receive group voice messages?
                                        </span>
                                <p id="privacy-option-description-3" class="hidden leading-5 text-gray-500">
                                    Admins can make phone calls to your primary phone number with a voice message.
                                </p>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="py-4 px-4 flex justify-between sm:px-6">
                    <div class="space-x-5 flex">
                        <div class="inline-flex rounded-md shadow-xs">
                            <button type="submit" class="bg-blue-700 border border-transparent rounded-md py-2 px-4 inline-flex justify-center leading-5 font-medium text-white hover:bg-blue-600 focus:outline-hidden focus:border-blue-800 focus:ring-blue active:bg-blue-800 transition duration-150 ease-in-out">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                &nbsp;Save Changes
                            </button>
                        </div>
                        <div class="inline-flex rounded-md shadow-xs">
                            <a href="{{ route('app.groups.index') }}" class="bg-white border border-gray-300 rounded-md py-2 px-4 inline-flex justify-center  leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-hidden focus:border-light-blue-300 focus:ring-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out">
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
