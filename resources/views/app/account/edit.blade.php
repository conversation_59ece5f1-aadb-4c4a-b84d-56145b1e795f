@extends('app._layout._app')

@section('title', 'Account')

@section('content')

    @include('app.account._account-header')

    <div class="max-w-lg sm:max-w-none rounded-sm border border-gray-300">
        <form action="{{ route('app.account.save', $user) }}" method="post" id="save-form">
            @csrf
            @method('put')
            <div class="bg-white p-4 pb-12 rounded-b-lg grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <div class="" id="general-info-tab">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                General Information
                            </h3>
                            <p class="mt-1 max-w-2xl leading-5 text-gray-500">
                                Be sure to click the "Save" button to save your changes.
                            </p>
                        </div>
                        <div class="mt-6 sm:mt-5">
                            <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="first_name" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    First name
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:max-w-xs">
                                        <input type="text" id="first_name" name="first_name" value="{{ $user->first_name }}" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="first_name" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Preferred First name
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:max-w-xs">
                                        <input type="text" id="preferred_first_name" name="preferred_first_name" value="{{ $user->preferred_first_name }}" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="last_name" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Last name
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:max-w-xs">
                                        <input type="text" id="last_name" name="last_name" value="{{ $user->last_name }}" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="birthdate" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Birthdate
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-56">
                                        <input id="birthdate" name="birthdate" value="{{ optional($user->birthdate)->format('Y-m-d') }}" type="date" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="marital_status" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Marital Status
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-40">
                                        <select id="marital_status" name="marital_status" class="block form-select w-full transition duration-150 ease-in-out sm:leading-5">
                                            @foreach(\App\Users\User::$marital_statuses as $index => $value)
                                                <option value="{{ $index }}" {{ old('marital_status') == $index ? 'selected' : ($user->marital_status == $index ? 'selected' : null) }}>{{ $value }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="date_married" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Married on
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-56">
                                        <input id="date_married" name="date_married" value="{{ optional($user->date_married)->format('Y-m-d') }}" type="date" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="country" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Gender
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-40">
                                        <select id="gender" name="gender" class="block form-select w-full transition duration-150 ease-in-out sm:leading-5">
                                            @foreach(\App\Users\User::$genders as $index => $value)
                                                <option value="{{ $index }}" {{ old('gender') == $index ? 'selected' : ($user->gender == $index ? 'selected' : null) }}>{{ $value }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="password" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Password
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-56">
                                        <input id="password" name="password" type="password" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                    <div class="text-gray-400">
                                        Leave blank for no change.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8 border-t border-gray-200 pt-8 sm:mt-5 sm:pt-10 hidden">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Notifications
                            </h3>
                            <p class="mt-1 max-w-2xl leading-5 text-gray-500">
                                We'll always let you know about important changes, but you pick what else you want to hear about.
                            </p>
                        </div>
                        <div class="mt-6 sm:mt-5">
                            <div class="sm:border-t sm:border-gray-200 sm:pt-5">
                                <div role="group" aria-labelledby="label-email">
                                    <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                                        <div>
                                            <div class="text-base leading-6 font-medium text-gray-900 sm:leading-5 sm:text-gray-700" id="label-email">
                                                By Email
                                            </div>
                                        </div>
                                        <div class="mt-4 sm:mt-0 sm:col-span-2">
                                            <div class="max-w-lg">
                                                <div class="relative flex items-start">
                                                    <div class="flex items-center h-5">
                                                        <input id="comments" name="comments" type="checkbox" class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                                                    </div>
                                                    <div class="ml-3 leading-5">
                                                        <label for="comments" class="font-medium text-gray-700">Comments</label>
                                                        <p class="text-gray-500">Get notified when someones posts a comment on a posting.</p>
                                                    </div>
                                                </div>
                                                <div class="mt-4">
                                                    <div class="relative flex items-start">
                                                        <div class="flex items-center h-5">
                                                            <input id="candidates" name="candidates" type="checkbox" class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                                                        </div>
                                                        <div class="ml-3 leading-5">
                                                            <label for="candidates" class="font-medium text-gray-700">Candidates</label>
                                                            <p class="text-gray-500">Get notified when a candidate applies for a job.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="mt-4">
                                                    <div class="relative flex items-start">
                                                        <div class="flex items-center h-5">
                                                            <input id="offers" name="offers" type="checkbox" class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                                                        </div>
                                                        <div class="ml-3 leading-5">
                                                            <label for="offers" class="font-medium text-gray-700">Offers</label>
                                                            <p class="text-gray-500">Get notified when a candidate accepts or rejects an offer.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
                                <div role="group" aria-labelledby="label-notifications">
                                    <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                                        <div>
                                            <div class="text-base leading-6 font-medium text-gray-900 sm:leading-5 sm:text-gray-700" id="label-notifications">
                                                Push Notifications
                                            </div>
                                        </div>
                                        <div class="sm:col-span-2">
                                            <div class="max-w-lg">
                                                <p class=leading-5 text-gray-500">These are delivered via SMS to your mobile phone.</p>
                                                <div class="mt-4">
                                                    <div class="flex items-center">
                                                        <input id="push_everything" name="push_everything" name="push_notifications" type="radio" class="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                                                        <label for="push_everything" class="ml-3">
                                                            <span class="block leading-5 font-medium text-gray-700">Everything</span>
                                                        </label>
                                                    </div>
                                                    <div class="mt-4 flex items-center">
                                                        <input id="push_email" name="push_email" name="push_notifications" type="radio" class="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                                                        <label for="push_email" class="ml-3">
                                                            <span class="block leading-5 font-medium text-gray-700">Same as email</span>
                                                        </label>
                                                    </div>
                                                    <div class="mt-4 flex items-center">
                                                        <input id="push_nothing" name="push_nothing" name="push_notifications" type="radio" class="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                                                        <label for="push_nothing" class="ml-3">
                                                            <span class="block leading-5 font-medium text-gray-700">No push notifications</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="">
                    <div class="">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                Extended Information
                            </h3>
                        </div>
                        <div class="mt-6 sm:mt-5">
                            <div class="mt-6 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="school_attending" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    School Attending
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:max-w-xs">
                                        <input type="text" id="school_attending" name="school_attending" value="{{ $user->school_attending }}" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6 sm:mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="user_grade_id" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    School Grade
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-40">
                                        <select id="user_grade_id" name="user_grade_id" class="block form-select w-full transition duration-150 ease-in-out sm:leading-5">
                                            <option value=""> --</option>
                                            @foreach(\App\Accounts\Grade::visibleTo($user)->orderBy('sort_id', 'asc')->get() as $grade)
                                                <option value="{{ $grade->id }}" {{ old('user_grade_id') ? 'selected' : ($user->user_grade_id == $grade->id ? 'selected' : null) }}>{{ $grade->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="employer" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Employer
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:max-w-xs">
                                        <input type="text" id="employer" name="employer" value="{{ $user->employer }}" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="job_title" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Job Title
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:max-w-xs">
                                        <input type="text" id="job_title" name="job_title" value="{{ $user->job_title }}" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-3">
                                <label for="job_keywords" class="block font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                    Job Keywords
                                </label>
                                <div class="mt-1 sm:mt-0 sm:col-span-2">
                                    <div class="max-w-lg rounded-md shadow-xs sm:max-w-xs">
                                        <input type="text" id="job_keywords" name="job_keywords" value="{{ $user->job_keywords }}" class="form-input block w-full transition duration-150 ease-in-out sm:leading-5">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-8 border-t border-gray-300">
                        <div class="w-full">
                        <span class="ml-3 rounded-md shadow-xs">
                            <button type="submit" class=" flex w-full justify-center py-2 px-6 border border-transparent leading-5 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-500 focus:outline-hidden focus:border-blue-700 focus:ring-blue active:bg-blue-700 transition duration-150 ease-in-out">
                                <svg class="h-5 w-5 " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                &nbsp;Save
                            </button>
                        </span>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
