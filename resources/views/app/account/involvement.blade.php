@extends('app._layout._app')

@section('title', 'Involvement')

@section('content')

    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Involvement Areas
            </h1>
        </div>
    </div>

    <div class="rounded-md bg-blue-50 p-2 mt-4 mb-0 border-blue-800 border">
        <div class="flex">
            <div class="shrink-0 my-auto">
                <!-- Heroicon name: information-circle -->
                <svg class="h-7 w-7 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="ml-3 flex-1 md:flex md:justify-between">
                <p class="text-blue-800">
                    Please check those activities for which you may have an interest or willingness to work. It is understood that you will not be expected to participate in all of the selected activities all of the time.
                </p>
            </div>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none py-4">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-4">
            @forelse($categories as $category)
                <div class="bg-white px-4 py-5 sm:rounded-lg sm:p-6 border border-gray-200">
                    <div class="text-center text-lg font-semibold pb-2">
                        {{ $category->name }}
                    </div>
                    <table width="100%">
                        <tbody>
                        @forelse($category->areas()->applyRestrictions(auth()->user())->get() as $area)
                            <tr>
                                <td class="pl-2">
                                    <div class="form-check flex">
                                        <div class="flex-initial pr-1">
                                            <input class="form-check-input" type="checkbox" onclick="saveSelection('checkbox_area_{{ $area->id }}', 'area', {{ $area->id }}, this.checked)"
                                                   type="checkbox" id="checkbox_area_{{ $area->id }}"
                                                   name="involvement[{{ $category->id }}][{{ $area->id }}][0]"
                                                   value="true"
                                                    {{ $involvement_records->whereNull('involvement_subarea_id')->contains('involvement_area_id', $area->id) ? 'checked' : null }}
                                            />
                                        </div>
                                        <div>
                                            <label class="form-check-label" for="checkbox_area_{{ $area->id }}">
                                                {{ $area->name }}
                                            </label>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @foreach($area->subareas()->applyRestrictions(auth()->user())->get() as $subarea)
                                <tr>
                                    <td class="pl-6">
                                        <div class="form-check flex">
                                            <div class="flex-initial pr-2">
                                                <input class="form-check-input" type="checkbox" onclick="saveSelection('checkbox_subarea_{{ $subarea->id }}', 'subarea', {{ $subarea->id }}, this.checked)" id="checkbox_subarea_{{ $subarea->id }}" name="involvement[{{ $category->id }}][{{ $area->id }}][{{ $subarea->id }}]" value="true" {{ $involvement_records->contains('involvement_subarea_id', $subarea->id) ? 'checked' : null }}>
                                            </div>
                                            <div>
                                                <label class="form-check-label" for="checkbox_subarea_{{ $subarea->id }}">
                                                    {{ $subarea->name }}
                                                </label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @empty
                            <tr>
                                <td class="text-center">
                                    <div class="px-4 py-2 bg-yellow-100 my-6 rounded-sm">No Areas</div>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            @empty
                <div class="text-center">
                    <div class="px-4 py-2 bg-yellow-100 my-6 rounded-sm"><strong>No Categories exist yet!</strong><br><span class="text-gray-600">Please check back later.</span></div>
                </div>
            @endforelse
        </div>
    </div>

    <div class="p-4 m-4 bg-gray-200 rounded-lg">
        <div>
            This information is intended to identify areas of work in which you are willing to participate as your time and talents allow. The elders, deacons and coordinators will use this information to identify those members who have indicated an interest in participating in those various works of the congregation for which they are responsible. It is understood that over time your interests may change and you may update your selections at such times.
        </div>
    </div>

    @push('scripts')

        <script>
            function saveSelection(checkbox_id, type, id, new_value) {

                var form_data = {
                    '_method': 'put',
                    'area_id': (type == 'area' ? id : null),
                    'subarea_id': (type == 'subarea' ? id : null),
                    'new_value': new_value,
                }

                axios({
                    headers: {
                        'X-CSRF-TOKEN': document.head.querySelector('meta[name="csrf-token"]').content
                    },
                    method: 'post',
                    url: "{{ route('app.account.involvement.save-selection') }}",
                    data: form_data,
                })
                    .then(function (response) {
                        if (response.data == 'OK') {
                            console.log('SAVED');
                        }
                    })
                    .catch(function (error) {
                        console.log('Failed.');
                        Sentry.captureException(error);
                        // Uncheck our box if there was an error.
                        document.getElementById(checkbox_id).checked = false;
                    });
            }
        </script>

    @endpush

@endsection