@extends('app._layout._app')

@section('title', 'Edit Phone')

@section('content')

    @include('app.account._account-header')

    <div class="max-w-lg sm:max-w-none rounded-sm border border-gray-300">
        <div class="bg-white p-4 rounded-b-lg">
            <div>
                <div class="bg-white border border-gray-200 overflow-hidden sm:rounded-md">
                    <form class="divide-y divide-gray-200 lg:col-span-9" action="{{ route('app.account.phone.save', $phone) }}" method="POST">
                        @method('put')
                        @csrf()
                        <div class="py-6 px-4 space-y-6 sm:p-6 lg:pb-8">
                            <div>
                                <h2 class="text-lg leading-6 font-medium text-gray-900">Edit Phone</h2>
                            </div>

                            <div class="grid grid-cols-12 gap-6">
                                <div class="col-span-12 sm:col-span-6">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-56">
                                        <label for="user_id" class="block  font-medium leading-5 text-gray-700">Owner</label>
                                        <select name="user_id" class="block form-select mt-1 w-full transition duration-150 ease-in-out  sm:leading-5">
                                            @foreach(Auth::user()->family as $user)
                                                <option value="{{ $user->id }}" {{ $user->id === $phone->user_id ? 'selected' : '' }}>{{ $user->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-12 gap-6">
                                <div class="col-span-12 sm:col-span-6">
                                    <label for="number" class="block  font-medium leading-5 text-gray-700">Phone Number</label>
                                    <input id="number" name="number" value="{{ $phone->number }}" class="form-input mt-1 block w-full border border-gray-300 rounded-md shadow-xs py-2 px-3 focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out  sm:leading-5">
                                </div>

                                <div class="col-span-12 sm:col-span-6">
                                    <div class="max-w-lg rounded-md shadow-xs sm:w-40">
                                        <label for="type" class="block  font-medium leading-5 text-gray-700">Type</label>
                                        <select name="type" class="block form-select mt-1 w-full transition duration-150 ease-in-out  sm:leading-5">
                                            @foreach(\App\Users\Phone::$types as $type => $name)
                                                <option value="{{ $type }}" {{ $type === $phone->type ? 'selected' : '' }}>{{ $name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="pt-1 divide-y divide-gray-200">
                            <div class="px-4 space-y-2 sm:px-6">
                                <ul class="divide-y divide-gray-200">
                                    <li class="py-4 flex items-center space-x-4" x-data="{ on:  {{ $phone->is_primary ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="is_primary" type="checkbox" name="is_primary" value="1" x-bind:checked="on" class="hidden" @isChecked($phone->is_primary)/>
                                    </span>
                                        <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-1" class=" leading-5 font-medium text-gray-900">
                                            Primary phone?
                                        </span>
                                            <p id="privacy-option-description-1" class=" leading-5 text-gray-500">
                                                Is this your primary phone number to be used? It is recommended that you have one primary phone number.
                                            </p>
                                        </div>
                                    </li>
                                    <li class="py-4 flex items-center space-x-4" x-data="{ on: {{ $phone->messages_opt_out ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="messages_opt_out" type="checkbox" name="messages_opt_out" value="1" x-bind:checked="on" class="hidden" @isChecked($phone->messages_opt_out)/>
                                    </span>
                                        <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-1" class=" leading-5 font-medium text-gray-900">
                                            Messages Opt-out?
                                        </span>
                                            <p id="privacy-option-description-1" class=" leading-5 text-gray-500">
                                                Never receive an SMS message or voice call from Lightpost.
                                            </p>
                                        </div>
                                    </li>
                                    <li class="py-4 flex items-center space-x-4" x-data="{ on: {{ $phone->is_family ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="is_family" type="checkbox" name="is_family" value="1" x-bind:checked="on" class="hidden" @isChecked($phone->is_family)/>
                                    </span>
                                        <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-1" class=" leading-5 font-medium text-gray-900">
                                            Belongs to the whole family?
                                        </span>
                                            <p id="privacy-option-description-1" class=" leading-5 text-gray-500">
                                                Family phone numbers show up for all family members, but cannot receive group text messages.
                                            </p>
                                        </div>
                                    </li>
                                    <li class="py-4 flex items-center space-x-4" x-data="{ on: {{ $phone->is_hidden ? 'true' : 'false' }} }">
                                    <span role="checkbox" tabindex="0" x-on:click="on = !on" @keydown.space.prevent="on = !on" :aria-checked="on.toString()" aria-checked="false" x-bind:class="{ 'bg-gray-200': !on, 'bg-blue-600': on }" class="relative inline-flex shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-hidden focus:ring-3 bg-gray-200">
                                        <span aria-hidden="true" x-bind:class="{ 'translate-x-5': on, 'translate-x-0': !on }" class="inline-block h-5 w-5 rounded-full bg-white shadow-sm transform transition ease-in-out duration-200 translate-x-0"></span>
                                        <input id="is_hidden" type="checkbox" name="is_hidden" value="1" x-bind:checked="on" class="hidden" @isChecked($phone->is_hidden)/>
                                    </span>
                                        <div class="flex flex-col cursor-pointer" x-on:click="on = !on">
                                        <span id="privacy-option-label-1" class=" leading-5 font-medium text-gray-900">
                                            Hidden from public?
                                        </span>
                                            <p id="privacy-option-description-1" class=" leading-5 text-gray-500">
                                                Hiding this phone number will keep it from showing up in the member directory or reports.
                                            </p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="py-4 px-4 flex justify-between space-x-5 sm:px-6">
                                <div class="space-x-5 flex">
                                    <div class="inline-flex rounded-md shadow-xs">
                                        <button type="submit" class="bg-blue-700 border border-transparent rounded-md py-2 px-4 inline-flex justify-center  leading-5 font-medium text-white hover:bg-blue-600 focus:outline-hidden focus:border-blue-800 focus:ring-blue active:bg-blue-800 transition duration-150 ease-in-out">
                                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                            </svg>
                                            &nbsp;Save Changes
                                        </button>
                                    </div>
                                    <div class="inline-flex rounded-md shadow-xs">
                                        <a href="{{ route('app.account.phones') }}" class="bg-white border border-gray-300 rounded-md py-2 px-4 inline-flex justify-center  leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-hidden focus:border-light-blue-300 focus:ring-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out">
                                            Cancel
                                        </a>
                                    </div>
                                </div>
                                <div class="inline-flex rounded-md shadow-xs">
                                    <a onclick="document.getElementById('delete-this-item').submit()" class="cursor-pointer bg-red-700 border border-transparent rounded-md py-2 px-4 inline-flex justify-center  leading-5 font-medium text-white hover:bg-red-600 focus:outline-hidden focus:border-red-800 focus:ring-blue active:bg-blue-800 transition duration-150 ease-in-out">
                                        Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                    <form action="{{ route('app.account.phone.delete', $phone) }}" method="post" id="delete-this-item">
                        @method('delete')
                        @csrf()
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection
