@extends('app._layout._app')

@section('title', 'Account')

@section('content')

    @include('app.account._account-header')

    <div class="max-w-lg sm:max-w-none rounded-sm border border-gray-300">
        <div class="bg-white p-4 rounded-b-lg">
            <div>
                <div class="mb-4">
                    <div class="flex flex-wrap justify-between">
                        <div class="rounded-md shadow-xs">
                            <a href="{{ route('app.account.email.create') }}" class=" flex w-full justify-center py-2 px-6 border border-transparent  leading-5 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-500 focus:outline-hidden focus:border-blue-700 focus:ring-blue active:bg-blue-700 transition duration-150 ease-in-out">
                                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                                &nbsp;Add New Email
                            </a>
                        </div>
                        <div class="ml-0 sm:ml-4 mt-2 sm:mt-0">
                            <div class="flex border border-blue-300 px-4 py-2 rounded-sm bg-blue-100  text-blue-800">
                                <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <strong>NEW!</strong> &nbsp;You can now manage emails for your whole family!
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white border border-gray-300 overflow-hidden sm:rounded-md">
                    <ul>
                        @forelse($user->emails()->includeAllFamilyEmails($user)->get() as $email)
                            <li class="{{ $loop->first ? null : 'border-t border-gray-200' }}">
                                <a href="{{ auth()->user()->can('update', $email) ? route('app.account.email.edit', $email) : null }}" class="block hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 transition duration-150 ease-in-out">
                                    <div class="px-4 py-4 sm:px-6">
                                        <div class="flex items-center justify-between">
                                            <div class="text-mono font-medium text-blue-600 truncate">
                                                {{ $email->email }}
                                            </div>
                                            <div class="ml-2 shrink-0 flex">
                                            <span class="px-3 py-1 inline-flex  leading-5 font-semibold rounded-sm bg-gray-200 text-gray-800">
                                                {{ $email->type }}
                                            </span>
                                            </div>
                                        </div>
                                        <div class="mt-2 sm:flex sm:justify-between">
                                            <div class="sm:flex">
                                                <div class="mr-6 flex font-medium items-center  leading-5 text-gray-600">
                                                    <svg class="shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                    </svg>
                                                    {{ $email->user->name }}
                                                </div>
                                            </div>
                                            <div class="mt-2 flex items-center  leading-5 text-gray-500 sm:mt-0 font-medium">
                                                @if($email->is_primary)
                                                    <span class="px-2 text-xs rounded-full bg-green-200 text-green-800">
                                                    <i class="fa fa-heart" rel="tooltip" title="Is Primary Email" aria-hidden="true"></i> &nbsp;Primary
                                                </span>
                                                @else
                                                    <span class="px-2 text-xs rounded-full bg-gray-100 text-gray-300 line-through">
                                                    <i class="fa fa-heart" rel="tooltip" title="Is Primary Email" aria-hidden="true"></i> &nbsp;Primary
                                                </span>
                                                @endif
                                                @if($email->is_family)
                                                    <span class="ml-2 px-2 text-xs rounded-full bg-blue-200 text-blue-800">
                                                    <i class="fa fa-home" rel="tooltip" title="Is a Family Email"></i> &nbsp;Family
                                                </span>
                                                @else
                                                    <span class="ml-2 px-2 text-xs rounded-full bg-gray-100 text-gray-300 line-through">
                                                    <i class="fa fa-home" rel="tooltip" title="Is a Family Email"></i> &nbsp;Family
                                                </span>
                                                @endif
                                                @if($email->receives_group_emails)
                                                    <span class="ml-2 px-2 text-xs rounded-full bg-teal-200 text-teal-800">
                                                    <i class="fa fa-envelope" rel="tooltip" title="Gets Group Messages"></i> &nbsp;Group Messages
                                                </span>
                                                @else
                                                    <span class="ml-2 px-2 text-xs rounded-full bg-gray-100 text-gray-300 line-through">
                                                    <i class="fa fa-envelope" rel="tooltip" title="Gets Group Messages"></i> &nbsp;Group Messages
                                                </span>
                                                @endif
                                                @if($email->is_hidden)
                                                    <span class="ml-2 px-2 text-xs rounded-full bg-gray-200 text-gray-800">
                                                    <i class="fa fa-eye-slash" rel="tooltip" title="Hidden from public"></i> &nbsp;Hidden
                                                </span>
                                                @else
                                                    <span class="ml-2 px-2 text-xs rounded-full bg-gray-100 text-gray-300 line-through">
                                                    <i class="fa fa-eye-slash" rel="tooltip" title="Hidden from public"></i> &nbsp;Hidden
                                                </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </li>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-muted text-center">
                                    <span class="badge badge-secondary badge-large">None</span>
                                </td>
                            </tr>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>
    </div>

@endsection
