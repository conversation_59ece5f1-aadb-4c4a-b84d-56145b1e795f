<div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
    <div class="flex items-center">
        <h1 class="text-4xl font-semibold pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
            <svg class="text-center w-8 h-8 mr-3 text-gray-900" aria-hidden="true" focusable="false" data-prefix="fal" data-icon="cog" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                <path fill="currentColor" d="M482.696 299.276l-32.61-18.827a195.168 195.168 0 0 0 0-48.899l32.61-18.827c9.576-5.528 14.195-16.902 11.046-27.501-11.214-37.749-31.175-71.728-57.535-99.595-7.634-8.07-19.817-9.836-29.437-4.282l-32.562 18.798a194.125 194.125 0 0 0-42.339-24.48V38.049c0-11.13-7.652-20.804-18.484-23.367-37.644-8.909-77.118-8.91-114.77 0-10.831 2.563-18.484 12.236-18.484 23.367v37.614a194.101 194.101 0 0 0-42.339 24.48L105.23 81.345c-9.621-5.554-21.804-3.788-29.437 4.282-26.36 27.867-46.321 61.847-57.535 99.595-3.149 10.599 1.47 21.972 11.046 27.501l32.61 18.827a195.168 195.168 0 0 0 0 48.899l-32.61 18.827c-9.576 5.528-14.195 16.902-11.046 27.501 11.214 37.748 31.175 71.728 57.535 99.595 7.634 8.07 19.817 9.836 29.437 4.283l32.562-18.798a194.08 194.08 0 0 0 42.339 24.479v37.614c0 11.13 7.652 20.804 18.484 23.367 37.645 8.909 77.118 8.91 114.77 0 10.831-2.563 18.484-12.236 18.484-23.367v-37.614a194.138 194.138 0 0 0 42.339-24.479l32.562 18.798c9.62 5.554 21.803 3.788 29.437-4.283 26.36-27.867 46.321-61.847 57.535-99.595 3.149-10.599-1.47-21.972-11.046-27.501zm-65.479 100.461l-46.309-26.74c-26.988 23.071-36.559 28.876-71.039 41.059v53.479a217.145 217.145 0 0 1-87.738 0v-53.479c-33.621-11.879-43.355-17.395-71.039-41.059l-46.309 26.74c-19.71-22.09-34.689-47.989-43.929-75.958l46.329-26.74c-6.535-35.417-6.538-46.644 0-82.079l-46.329-26.74c9.24-27.969 24.22-53.869 43.929-75.969l46.309 26.76c27.377-23.434 37.063-29.065 71.039-41.069V44.464a216.79 216.79 0 0 1 87.738 0v53.479c33.978 12.005 43.665 17.637 71.039 41.069l46.309-26.76c19.709 22.099 34.689 47.999 43.929 75.969l-46.329 26.74c6.536 35.426 6.538 46.644 0 82.079l46.329 26.74c-9.24 27.968-24.219 53.868-43.929 75.957zM256 160c-52.935 0-96 43.065-96 96s43.065 96 96 96 96-43.065 96-96-43.065-96-96-96zm0 160c-35.29 0-64-28.71-64-64s28.71-64 64-64 64 28.71 64 64-28.71 64-64 64z"></path>
            </svg>
            Account
        </h1>
    </div>
</div>

<div class="max-w-lg mx-auto sm:max-w-none mt-4">
    <div class="sm:hidden mb-2">
        <select aria-label="Selected tab" class="form-select block w-full font-bold">
            <option {{ request()->is('account') ? 'selected' : null }}>User Information</option>
            <option {{ request()->is('account/emails') ? 'selected' : null }}>Emails</option>
            <option {{ request()->is('account/addresses') ? 'selected' : null }}>Addresses</option>
            <option {{ request()->is('account/phones') ? 'selected' : null }}>Phones</option>
        </select>
    </div>
    <?php
    $active        = ' group inline-flex items-center py-3 px-1 border-b-4 border-blue-500 font-medium leading-5 text-blue-600 focus:outline-hidden focus:text-blue-800 focus:border-blue-700"  aria-current="page';
    $inactive      = ' group inline-flex items-center py-3 px-1 border-b-4 border-transparent font-medium leading-5 text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-hidden focus:text-gray-700 focus:border-gray-300';
    $icon_active   = '-ml-0.5 mr-2 h-5 w-5 text-blue-500 group-focus:text-blue-600';
    $icon_inactive = '-ml-0.5 mr-2 h-5 w-5 text-gray-400 group-hover:text-gray-500 group-focus:text-gray-600';
    ?>
    <div class="hidden sm:block">
        <div>
            <nav class="flex -mb-px">
                <a href="{{ route('app.account.edit') }}"
                   class="{{ request()->is('account') ? $active : $inactive }}">
                    <svg class="{{ request()->is('account') ? $icon_active : $icon_inactive }}" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                    <span class="font-semibold">User Information</span>
                </a>
                <a href="{{ route('app.account.emails') }}"
                   class="ml-6 {{ request()->is('account/emails*') ? $active : $inactive }}">
                    <svg class="{{ request()->is('account/emails*') ? $icon_active : $icon_inactive }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                    </svg>
                    <span class="font-semibold">Emails</span>
                </a>
                <a href="{{ route('app.account.addresses') }}"
                   class="ml-6 {{ request()->is('account/addresses*') ? $active : $inactive }}">
                    <svg class="{{ request()->is('account/addresses*') ? $icon_active : $icon_inactive }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    <span class="font-semibold">Addresses</span>
                </a>
                <a href="{{ route('app.account.phones') }}"
                   class="ml-6 {{ request()->is('account/phones*') ? $active : $inactive }}">
                    <svg class="{{ request()->is('account/phones*') ? $icon_active : $icon_inactive }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                    </svg>
                    <span class="font-semibold">Phones</span>
                </a>
            </nav>
        </div>
    </div>
</div>