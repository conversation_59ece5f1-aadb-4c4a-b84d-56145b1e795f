@extends('app._layout._app')

@section('title', 'Online Giving')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200 mb-4">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Online Giving
            </h1>
        </div>
        <div class="space-x-4">
            @if(auth()->user()->account->stripe_account_id)
                @if(auth()->user()->can('create', \App\Users\PaymentSchedule::class))
                    <a href="{{ route('app.giving.create-payment-schedule') }}" class="align-middle pl-2 pr-4 py-2 bg-blue-600 text-white rounded-sm hover:bg-blue-500">
                        <svg class="w-6 h-6 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        Add Recurring Giving
                    </a>
                @endif
                <a href="{{ route('app.giving.add-payment-method') }}" class="align-middle pl-2 pr-4 py-2 bg-blue-600 text-white rounded-sm hover:bg-blue-500">
                    <svg class="w-6 h-6 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    Add Payment Method
                </a>
            @endif
        </div>
    </div>

    <div class="space-y-8">

        @if($payment_schedules && auth()->user()->can('viewExisting', \App\Users\PaymentSchedule::class))
            <div class="bg-white border border-gray-300 rounded-md max-w-xl">
                <h2 class="p-4 bg-gray-50 rounded-t-md border-b border-gray-300">
                    Recurring Contributions
                </h2>
                <ul class=" gap-4 divide-y divide-gray-300">
                    @foreach($payment_schedules as $schedule)
                        <li class=" flex rounded-b-lg">
                            <div class="flex-1 flex items-center justify-between break-words">
                                <div class="flex-1 p-4 break-words">
                                    <div class="flex flex-row justify-between">
                                        <div class="text-black text-base">
                                            Occurs
                                            <code class="bg-gray-100 font-semibold px-1 rounded-sm py-1">{{ $schedule->recur_frequency }}</code>
                                            every
                                            <code class="font-semibold bg-gray-100 px-1 rounded-sm py-1">
                                                {{ \App\Users\PaymentSchedule::$days_of_week[$schedule->recur_day_of_week] }}
                                            </code>
                                            <br>
                                            <div class="text-sm mt-1 font-semibold">
                                                {{ $schedule->paymentMethod->getBankName() }} -
                                                {{ $schedule->paymentMethod->last4 }}
                                            </div>
                                        </div>
                                        <div class="text-2xl font-medium">
                                            {{ \Brick\Money\Money::ofMinor($schedule->amount, 'USD')->formatTo('en_US') }}
                                        </div>
                                    </div>
                                    <div class="mt-2 flex flex-row justify-between">
                                        <div class="px-2 py-0.5 bg-gray-200 rounded-sm">
                                            {{ $schedule->financeBucket?->name }}
                                        </div>
                                    </div>
                                    <div class="mt-2 flex flex-row justify-between">
                                        <div class="my-auto">
                                            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                                <x-heroicon-s-arrow-path class="w-5 mr-1"/> Recurring
                                            </span>
                                        </div>
                                        <a href="#" onclick="document.getElementById('delete-schedule-{{ $schedule->id }}').submit()" class="flex flex-row align-middle px-3 py-1 text-sm  mt-auto bg-white border border-red-500 text-red-500 rounded-sm hover:bg-red-100">
                                            <x-heroicon-s-trash class="w-4 mr-1"/>
                                            Delete
                                        </a>
                                    </div>
                                </div>
                                <form method="post" id="delete-schedule-{{ $schedule->id }}" action="{{ route('app.giving.payment-schedule.delete.submit', $schedule) }}">
                                    @csrf
                                    @method('delete')
                                </form>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="bg-white border border-gray-300 rounded-md max-w-xl">
            <h2 class="p-4 bg-gray-50 rounded-t-md border-b border-gray-300">
                Payment Methods
            </h2>
            <div>
                <ul class="gap-5 divide-y divide-gray-300">
                    @forelse($payment_methods as $method)
                        <li class="max-w-xl flex rounded-b-lg">
                            <div class="flex-1 flex items-center justify-between break-words">
                                <div class="flex-1 p-4 break-words">
                                    <a class="text-gray-900 font-semibold hover:text-gray-600 transition ease-in-out duration-150 text-lg">
                                        {{ $method->getBankName() }}
                                    </a>

                                    @if($method->type == 'card')
                                        <div class="text-gray-600 text-sm">
                                            Card ending in <code class="bg-gray-100 px-1 rounded-sm py-1">{{ $method->last4 }}</code>, expires on
                                            <code class="font-medium bg-gray-100 px-1 rounded-sm py-1">{{ $method->expire_month }}/{{ $method->expire_year }}</code>
                                        </div>
                                    @else
                                        <div class="text-gray-600 text-sm">
                                            Bank account for <code class="bg-gray-100 px-1 rounded-sm py-1">{{ $method?->getStripeObject()?->account_holder_name }}</code> ending in
                                            <code class="font-medium bg-gray-100 px-1 rounded-sm py-1">{{ $method->last4 }}</code>
                                        </div>
                                    @endif
                                    <div class="my-2 flex flex-row justify-between">
                                        @if(($method->getStripeObject()?->status ?? null) == 'new')
                                            <div class="my-auto">
                                                <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-gray-200 text-gray-600">
                                                    <x-heroicon-s-clock class="w-5 mr-1"/> Pending Verification
                                                </span>
                                            </div>
                                        @else
                                            <div class="my-auto">
                                                <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                                    <x-heroicon-s-check-circle class="w-5 mr-1"/> Verified
                                                </span>
                                            </div>
                                        @endif
                                        @if(($method->getStripeObject()?->status ?? null) != 'new')
                                            <a href="{{ route('app.giving.give', $method) }}" class="flex flex-row align-middle px-4 py-2 bg-blue-600 text-white rounded-sm hover:bg-blue-500">
                                                <x-heroicon-s-heart class="w-5 mr-1"/>
                                                Give
                                            </a>
                                        @endif
                                    </div>

                                    <form method="post" action="{{ route('app.giving.delete-payment-method.submit', $method) }}" class="" x-data="{showActualButton:false}">
                                        @csrf
                                        @method('delete')
                                        <button type="button" class="flex flex-row align-middle px-2 py-1 text-sm bg-white border border-red-500 text-red-500 hover:text-white rounded-sm hover:bg-red-500" @click="showActualButton = !showActualButton">Delete</button>
                                        <div x-cloak x-show="showActualButton">
                                            <button type="submit" class="mt-1 flex flex-row align-middle px-2 py-1 text-sm bg-red-500 border border-red-500 text-white rounded-sm hover:bg-red-500">Confirm, Delete This</button>
                                            <span class="text-sm">This action <strong>cannot</strong> be undone!</span>
                                        </div>
                                    </form>

                                    @if(false && auth()->user()->id == $method->user_id && ($method->share_with_spouse || auth()->user()->spouse))
                                        <form action="{{ route('app.giving.payment-method.update.submit', $method) }}"
                                              method="post"
                                              id="update-payment-method-{{ $method->id }}">
                                            @csrf
                                            <label class="text-sm">
                                                <input type="hidden" name="share_with_spouse" value="0"/>
                                                <input type="checkbox" name="share_with_spouse"
                                                       value="1"
                                                       @isChecked($method->share_with_spouse)
                                                       onclick="document.getElementById('update-payment-method-{{ $method->id }}').submit()"/>
                                                Share this payment method with my spouse.
                                            </label>
                                        </form>
                                    @endif

                                    @if(($method->getStripeObject()?->status ?? null) == 'new')
                                        <form method="post" action="{{ route('app.giving.add-payment-method.verify', $method) }}" id="verify-form-{{ $method->id }}">
                                            @csrf
                                            @method('post')
                                            <div class="flex">
                                                <div class="flex-initial mt-1 relative rounded-md shadow-xs" style="width: 120px">
                                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-700 text-sm sm:text-lg sm:leading-5">
                                                        $0.
                                                        </span>
                                                    </div>
                                                    <input type="text" name="deposit_1" class="form-input placeholder-gray-300 block w-full pl-10 pr-12 text-sm sm:text-lg sm:leading-5" placeholder="00" aria-describedby="price-currency" maxlength="2" oninput="this.value = this.value.replace(/[^0-9]/g, ''); this.value = this.value.replace(/(.*)\./g, '$1');"/>
                                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-500 text-sm sm:leading-5" id="price-currency">
                                                            USD
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-2 flex-initial mt-1 relative rounded-md shadow-xs" style="width: 120px">
                                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-700 text-sm sm:text-lg sm:leading-5">
                                                        $0.
                                                        </span>
                                                    </div>
                                                    <input type="text" name="deposit_2" class="form-input placeholder-gray-300 block w-full pl-10 pr-12 text-sm sm:text-lg sm:leading-5" placeholder="00" aria-describedby="price-currency" maxlength="2" oninput="this.value = this.value.replace(/[^0-9]/g, ''); this.value = this.value.replace(/(.*)\./g, '$1');"/>
                                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                        <span class="text-gray-500 text-sm sm:leading-5" id="price-currency">
                                                            USD
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="my-auto">
                                                    <button class="flex flex-row ml-2 py-2 text-white px-3 bg-green-500 rounded-sm" onclick="document.getElementById('verify-form-{{ $method->id }}').submit()">
                                                        <x-heroicon-s-check class="w-5 mr-1"/>
                                                        Verify
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="pt-2 text-xs break-words">
                                                Before you can give with a bank account, you must verify it by confirming two micro-deposits sent to your account.
                                                <br>
                                                It may take 1-2 business days for these deposits to show in your account.
                                            </div>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </li>
                        @if($method->type == 'card1')
                            <li class="flex rounded-b-lg">
                                <div class="flex-1 flex items-center justify-between break-words">
                                    <div class="flex-1 px-4 py-2 break-words">
                                        <a class="text-gray-900 font-semibold hover:text-gray-600 transition ease-in-out duration-150 text-lg">
                                            {{ \Illuminate\Support\Str::ucfirst($method->getStripeObject()->brand) }}
                                        </a>
                                        <div class="text-gray-600 text-xs">
                                            Card ending in <code class="bg-gray-100 px-1 rounded-sm py-1">{{ $method->last4 }}</code>, expires on
                                            <code class="font-medium bg-gray-100 px-1 rounded-sm py-1">{{ $method->expire_month }}/{{ $method->expire_year }}</code>
                                        </div>
                                        <div class="my-1">
                                            <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-badge-check"></i> &nbsp; Verified
                                            </span>
                                        </div>
                                    </div>
                                    <div class="shrink-0 pr-2 hidden">
                                        <button class="w-8 h-8 inline-flex items-center justify-center text-gray-500 rounded-full bg-transparent hover:text-gray-700 focus:outline-hidden focus:text-gray-700 focus:bg-gray-100 transition ease-in-out duration-150">
                                            <!-- Heroicon name: dots-vertical -->
                                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <form method="post" action="{{ route('app.giving.delete-payment-method.submit', $method) }}" class="hidden">
                                        @csrf
                                        @method('delete')
                                        <button type="submit" class="card-link float-right btn btn-outline-danger btn-sm">Delete</button>
                                    </form>
                                </div>
                                <a href="{{ route('app.giving.give', $method) }}" class="shrink-0 flex font-medium items-center justify-center w-24 bg-blue-600 text-white rounded-r-md hover:bg-blue-500 transition ease-in-out duration-150">
                                    <i class="fas fa-hand-holding-heart"></i> &nbsp;Give
                                </a>
                            </li>
                        @endif
                    @empty
                        <li class="col-span-1 flex rounded-lg">
                            <div class="flex-1 border border-gray-300 bg-white rounded-lg py-4">
                                <div class="alert alert-warning text-center" role="alert">
                                    <div class="bg-yellow-100 py-2">No credit/debit cards or bank accounts on file.</div>
                                    <div class="mt-4 mb-2">
                                        <a href="{{ route('app.giving.add-payment-method') }}" class="px-4 py-2 bg-blue-600 text-white rounded-sm hover:bg-blue-500">Add one now</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @endforelse
                </ul>
            </div>

        </div>

        <div class="bg-white border border-gray-300 rounded-md max-w-3xl">
            <h2 class="p-4 bg-gray-50 rounded-t-md border-b border-gray-300">
                Giving History
            </h2>
            <div class="flex flex-col">
                <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                        <div class="overflow-hidden sm:rounded-b-lg">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead>
                                <tr>
                                    <th class="px-6 py-3 bg-gray-100 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th class="px-6 py-3 bg-gray-100 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                                        Destination
                                    </th>
                                    <th class="px-6 py-3 bg-gray-100 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                @forelse($contribution_history as $contrib)
                                    <tr class="{{ $loop->odd ? 'bg-white' : 'bg-gray-100' }}">
                                        <td class="px-6 py-2 whitespace-nowrap leading-5 font-medium text-gray-900" width="15%">
                                            {{ $contrib->created_at->format('M d, Y') }}
                                        </td>
                                        <td class="px-6 py-2 whitespace-nowrap leading-5 text-gray-800">
                                            <div class=" grid grid-cols-1 sm:grid-cols-2">
                                                <div class="col-span-1 flex">
                                                    {{ $contrib->bucket->name ?: $contrib->title }}
                                                </div>
                                                @if($contrib->status != 'available')
                                                    <div class="col-span-1 flex">
                                                        <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-gray-200 text-gray-600">
                                                            {{ $contrib->status }}
                                                        </span>
                                                    </div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-2 whitespace-nowrap leading-5 text-gray-500" width="15%">
                                            {{ \Brick\Money\Money::ofMinor($contrib->amount, 'USD')->formatTo('en_US') }}
                                        </td>
                                    </tr>
                                @empty
                                    <tr class="bg-white">
                                        <td colspan=" 100%" class="text-center">
                                            <div class="bg-yellow-100 text-center py-2" role="alert">
                                                <span>No contribution history yet.</span>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection