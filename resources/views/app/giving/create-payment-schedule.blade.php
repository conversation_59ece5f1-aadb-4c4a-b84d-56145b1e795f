@extends('app._layout._app')

@section('title', 'Online Giving')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Online Giving
            </h1>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none py-4">
        <div class="">
            <div class="bg-white border border-gray-300 rounded-md">
                <div class="border-b border-gray-300 p-4 bg-gray-50 rounded-t-md">
                    <div class="px-4 sm:px-0">
                        <h3 class="text-2xl font-medium leading-6 text-gray-900">Schedule a Recurring Contribution</h3>
                        <p class="mt-2 text-base text-gray-600">
                            <strong>Note:</strong> Recurring contributions can currently only be scheduled to happen once a week on Sunday.
                        </p>
                    </div>
                </div>
                <form method="post" class="" action="{{ route('app.giving.create-payment-schedule.submit') }}">
                    @csrf
                    @method('post')
                    <div class="overflow-hidden sm:rounded-md">
                        <div class="px-4 py-4 bg-white sm:px-8">
                            <div class="space-y-4 max-w-lg">
                                <div class="">
                                    <label for="user_payment_method_id" class="block font-medium text-gray-700">Payment Method</label>
                                    <select id="user_payment_method_id" name="user_payment_method_id" class="block form-select w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out  sm:leading-5">
                                        @foreach(auth()->user()->paymentMethods as $payment_method)
                                            <option value="{{ $payment_method->id }}">
                                                {{ $payment_method->getStripeObject()->bank_name ?: \Illuminate\Support\Str::ucfirst($payment_method->getStripeObject()->brand) }}
                                                - {{ $payment_method->last4 }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="">
                                    <label for="account_finance_bucket_id" class="block  font-medium text-gray-700">Contribution Bucket</label>
                                    <select id="account_finance_bucket_id" name="account_finance_bucket_id" class="block form-select w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out  sm:leading-5">
                                        @foreach($account_finance_buckets as $bucket)
                                            <option value="{{ $bucket->id }}">{{ $bucket->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="w-48">
                                    <label for="amount" class="block  font-medium text-gray-700">Amount</label>
                                    <div class="relative rounded-md shadow-xs">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500  sm:leading-5">$</span>
                                        </div>
                                        <input type="text" id="amount" name="amount" maxlength="4" class="form-input placeholder-gray-300 block w-full pl-7 pr-12  sm:leading-5" placeholder="0" oninput="this.value = this.value.replace(/[^0-9]/g, ''); this.value = this.value.replace(/(.*)\./g, '$1');">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500  sm:leading-5" id="price-currency">.00 &nbsp; USD</span>
                                        </div>
                                    </div>
                                    <div class="text-sm text-gray-400">
                                        Full dollar amounts only.
                                    </div>
                                </div>
                                <div class="">
                                    <label for="interval" class="block  font-medium text-gray-700">Frequency</label>
                                    <div class="flex flex-row my-auto">
                                        <select id="interval" name="interval" class="w-32 form-select py-2 px-3 border border-gray-300 bg-white rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out  sm:leading-5">
                                            <option value="WEEKLY">WEEKLY</option>
                                            @foreach(\App\Users\PaymentSchedule::$recur_frequencies as $interval)
                                            @endforeach
                                        </select>
                                        <div class="my-auto mx-2 font-medium">every</div>
                                        <select id="day_of_week" name="day_of_week" class="w-32 form-select py-2 px-3 border border-gray-300 bg-white rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out  sm:leading-5">
                                            <option value="7">Sunday</option>
                                            @foreach(\App\Users\PaymentSchedule::$days_of_week as $interval)
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="text-sm text-gray-400">
                                        Currently limited to every week on Sunday.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex px-4 py-4 bg-gray-50 sm:px-4 border-t border-gray-300">
                            <button class="py-2 px-4 flex flex-row font-medium rounded-md text-white bg-blue-600 shadow-xs hover:bg-blue-500 focus:outline-hidden focus:ring-blue active:bg-blue-600 transition duration-150 ease-in-out">
                                <x-heroicon-s-check class="w-5 mr-1"/>
                                Setup Recurring Giving
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-10 sm:mt-8 hidden">
            <div class="gap-6">
                <div class="md:col-span-1">
                    <div class="px-4 sm:px-0">
                        <h3 class="text-2xl font-medium leading-6 text-gray-900">Add Debit / Credit Card</h3>
                        <p class="text-sm text-gray-600">
                            Enter your information here to add a card as a payment method.
                        </p>
                    </div>
                </div>
                <div class="mt-5 md:mt-0 md:col-span-2">
                    <form id="payment-form" method="post" data-secret="{!! isset($intent) ? $intent->client_secret : '' !!}" action="{{ route('app.giving.add-payment-method.submit') }}">
                        @csrf
                        @method('post')
                        <div class="overflow-hidden sm:rounded-md">
                            <div class="px-4 py-5 bg-white sm:px-6 sm:pt-5 sm:pb-3">
                                <div class="hidden text-red-600" id="card-errors" role="alert"></div>
                                <div id="card-element" style="border: 1px solid #777"></div>
                            </div>
                            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                                <button id="card-button" class="py-2 px-4 border border-transparent font-medium rounded-md text-white bg-blue-600 shadow-xs hover:bg-blue-500 focus:outline-hidden focus:ring-blue active:bg-blue-600 transition duration-150 ease-in-out">
                                    <i class="fas fa-credit-card"></i> &nbsp;Add Debit/Credit Card
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="mt-10 sm:mt-8">
            <div class="gap-6">
                <div class="mt-5 md:mt-0 md:col-span-2">
                    <div class="mb-4">
                        <div class="text-lg font-medium mb-1">
                            When does a recurring payment happen?
                        </div>
                        <p class="ml-2">
                            Recurring contributions will be processed the morning of the day the schedule happens.
                            <br>
                            For example: a scheduled recurring contribution that happens every week on Sunday will be processed Sunday morning.
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="text-lg font-medium mb-1">
                            Will I be notified when a payment is processed?
                        </div>
                        <p class="ml-2">
                            A contribution given through a recurring schedule will not send a notification. You will see the contribution in your contribution history immediately when it is processed.
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="text-lg font-medium mb-1">
                            How do I change or cancel a recurring payment?
                        </div>
                        <p class="ml-2">
                            You can cancel a recurring contribution at any time by deleting it. To change the amount of a recurring contribution, simply delete it and create a new one.
                        </p>
                    </div>
                </div>
            </div>
        </div>

@endsection