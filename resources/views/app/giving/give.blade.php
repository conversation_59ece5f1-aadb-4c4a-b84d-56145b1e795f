@extends('app._layout._app')

@section('title', 'Online Giving')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Online Giving
            </h1>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none py-4">
        <div>
            <div class="px-4 sm:px-0">
                <p class="mt-1  leading-5 text-gray-600">
                    Enter an amount, select a contribution bucket and click to submit a contribution.
                </p>
            </div>
        </div>

        <div class="">
            <div class="mt-5">
                <form class="form-inline" method="post" action="{{ route('app.giving.give.post', $payment_method) }}" id="giveForm">
                    <input type="hidden" name="user_payment_method_id" value="{{ $payment_method->id }}"/>
                    @csrf
                    @method('post')
                    <div class="overflow-hidden sm:rounded-md border border-gray-300">
                        <div class="px-4 py-5 bg-white sm:px-6 sm:pt-5 sm:pb-3">
                            <div class="mt-5 md:mt-0 md:col-span-2">
                                <div class="grid grid-cols-4 gap-6">
                                    <div class="col-span-4">
                                        <label for="user_payment_method_id" class="block  font-medium leading-5 text-gray-700">Payment Method</label>
                                        <input type="hidden" name="user_payment_method_id" value="{{ $payment_method->id }}"/>
                                        <select class="mt-1 block form-select w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out  sm:leading-5">
                                            <option>{{ $payment_method->getBankName() }} - {{ $payment_method->last4 }}</option>
                                        </select>
                                    </div>
                                    <div class="col-span-4 sm:col-span-2 lg:col-span-1">
                                        <label for="amount" class="block  font-medium leading-5 text-gray-700">Amount</label>
                                        <div class="mt-1 relative rounded-md shadow-xs">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500  sm:leading-5">$</span>
                                            </div>
                                            <input type="text" id="amount" name="amount" class="form-input placeholder-gray-300 block w-full pl-7 pr-12  sm:leading-5" placeholder="0" oninput="this.value = this.value.replace(/[^0-9]/g, ''); this.value = this.value.replace(/(.*)\./g, '$1');">
                                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                <span class="text-gray-500  sm:leading-5" id="price-currency">.00 &nbsp; USD</span>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            Full dollar amounts only.
                                        </div>
                                    </div>
                                    <div class="col-span-4 sm:col-span-2">
                                        <label for="account_finance_bucket_id" class="block  font-medium leading-5 text-gray-700">Bucket</label>
                                        <select id="account_finance_bucket_id" name="account_finance_bucket_id" class="mt-1 block form-select w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out  sm:leading-5">
                                            @foreach($account_finance_buckets as $bucket)
                                                <option value="{{ $bucket->id }}">{{ $bucket->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="px-4 py-4 bg-gray-50 border-t border-gray-300 text-left sm:px-6">
                            <button id="contribute-button" onclick="this.disabled = true; this.innerHTML = 'Processing...'; document.getElementById('giveForm').submit()" class="align-middle py-2 pl-2 pr-4 border border-transparent  font-medium rounded-md text-white bg-blue-600 shadow-xs hover:bg-blue-500 focus:outline-hidden focus:ring-blue active:bg-blue-600 transition duration-150 ease-in-out">
                                <svg class="w-6 h-6 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                Submit Contribution
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

@endsection