@extends('app._layout._app')

@section('title', 'Online Giving')

@section('content')

    <script src="https://js.stripe.com/v3/"></script>

    <style>
        .StripeElement {
            box-sizing: border-box;

            height: 40px;

            padding: 10px 12px;

            border: 1px solid transparent;
            border-radius: 4px;
            background-color: white;

            box-shadow: 0 1px 3px 0 #e6ebf1;
            -webkit-transition: box-shadow 150ms ease;
            transition: box-shadow 150ms ease;
        }

        .StripeElement--focus {
            box-shadow: 0 1px 3px 0 #cfd7df;
        }

        .StripeElement--invalid {
            border-color: #fa755a;
        }

        .StripeElement--webkit-autofill {
            background-color: #fefde5 !important;
        }

        ::placeholder {
            color: #e2e3e5 !important;
        }
    </style>

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Online Giving
            </h1>
        </div>
    </div>

    @if(auth()->user()->account->hasFeatureForMember('account.setting.giving.allow_card.create'))
        <div class="max-w-lg mx-auto sm:max-w-none py-4">
            <div class="gap-6 bg-white border border-gray-300 rounded-md">
                <div class="border-b border-gray-300 p-4 bg-gray-100 rounded-t-md">
                    <div class="px-4 sm:px-0">
                        <h3 class="flex flex-row text-2xl font-medium text-gray-900">
                            <x-heroicon-s-credit-card class="w-7 mr-2"/>
                            Add Debit / Credit Card
                        </h3>
                        <p class="mt-1 text-black">
                            Enter your information here to add a debit or credit card as a payment method.
                        </p>
                    </div>
                </div>
                <div class="md:mt-0 rounded-sm">
                    <form id="card-form" method="post" data-secret="{!! isset($intent) ? $intent->client_secret : '' !!}" action="{{ route('app.giving.add-payment-method.submit') }}">
                        @csrf
                        @method('post')
                        <input type="hidden" name="is_card" value="1"/>
                        <div class="overflow-hidden sm:rounded-md">
                            <div class="px-4 py-4 bg-white sm:px-6">
                                <div class="py-4 bg-white max-w-lg">
                                    <div class="hidden text-red-600" id="card-errors" role="alert"></div>
                                    <div id="card-element" style="border: 1px solid #777"></div>
                                </div>
                            </div>
                            @if(false && auth()->user()->spouse)
                                <div class="px-4 pb-4 pt-0 bg-white sm:px-6">
                                    <label>
                                        <input type="hidden" name="share_with_spouse" value="0"/>
                                        <input type="checkbox" name="share_with_spouse" value="1"/> Share this payment method with my spouse.
                                    </label>
                                </div>
                            @endif
                            <div class="flex px-4 py-4 border-t border-gray-300 bg-gray-50 sm:px-6">
                                <div class="shrink-0">
                                    <button id="card-button" class="flex flex-row py-2 px-4 font-medium rounded-md text-white bg-blue-600 shadow-xs hover:bg-blue-500 focus:outline-hidden focus:ring-blue active:bg-blue-600 transition duration-150 ease-in-out">
                                        <x-heroicon-s-check class="w-5 mr-1"/>
                                        Add Debit/Credit Card
                                    </button>
                                </div>
                                <div class="grow">
                                    <div class="hidden px-3 py-2 bg-red-200 text-red-600 rounded-lg" id="card-errors" role="alert">
                                        <i class="fas fa-exclamation-triangle"></i>&nbsp;
                                        <span id="card-errors-message"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    <div class="max-w-lg mx-auto sm:max-w-none py-4">
        <div class="gap-6 bg-white border border-gray-300 rounded-md">
            <div class="border-b border-gray-300 p-4 bg-gray-100 rounded-t-md">
                <div class="px-4 sm:px-0">
                    <h3 class="flex flex-row text-2xl font-medium text-gray-900">
                        <x-heroicon-s-building-library class="w-7 mr-2"/>
                        Add Bank Account
                    </h3>
                    <p class="mt-1 text-black">
                        Enter your information here to add a bank account as a payment method.
                    </p>
                    <p class="mt-1 text-black">
                        <strong>Note:</strong> Adding a bank account requires verifying your account with micro-deposits.
                    </p>
                </div>
            </div>
            <div class="border-b border-gray-300 md:mt-0">
                <form id="bank-form" method="post" data-secret="{!! isset($intent) ? $intent->client_secret : '' !!}" action="{{ route('app.giving.add-payment-method.submit') }}">
                    @csrf
                    @method('post')
                    <input type="hidden" name="is_bank_account" value="1"/>
                    <div class="overflow-hidden sm:rounded-md">
                        <div class="px-4 py-4 bg-white sm:px-6">
                            <div class="grid grid-cols-9 gap-6">
                                <div class="col-span-6 sm:col-span-3">
                                    <label for="account_holder_name" class="block font-medium text-gray-700">Account Holder Name</label>
                                    <input id="account_holder_name" name="account_holder_name" placeholder="John Smith" class="mt-1 form-input block w-full py-2 px-3 border border-gray-300 rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out sm sm:leading-5">
                                </div>

                                <div class="col-span-6 sm:col-span-3">
                                    <label for="routing_number" class="block font-medium text-gray-700">Routing Number</label>
                                    <input id="routing_number" placeholder="*********" class="mt-1 form-input block w-full py-2 px-3 border border-gray-300 rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out sm sm:leading-5">
                                </div>

                                <div class="col-span-6 sm:col-span-3">
                                    <label for="account_number" class="block font-medium text-gray-700">Account Number</label>
                                    <input id="account_number" placeholder="************" class="mt-1 form-input block w-full py-2 px-3 border border-gray-300 rounded-md shadow-xs focus:outline-hidden focus:ring-blue focus:border-blue-300 transition duration-150 ease-in-out sm sm:leading-5">
                                </div>
                            </div>
                        </div>
                        @if(false && auth()->user()->spouse)
                            <div class="px-4 pb-4 pt-0 bg-white sm:px-6">
                                <label>
                                    <input type="hidden" name="share_with_spouse" value="0"/>
                                    <input type="checkbox" name="share_with_spouse" value="1"/> Share this payment method with my spouse.
                                </label>
                            </div>
                        @endif
                        <div class="flex px-4 py-4 border-t border-gray-300 bg-gray-50 sm:px-6 space-x-4">
                            <div class="shrink-0">
                                <button id="bank-button" class="flex flex-row py-2 px-4 font-medium rounded-md text-white bg-blue-600 shadow-xs hover:bg-blue-500 focus:outline-hidden focus:ring-blue active:bg-blue-600 transition duration-150 ease-in-out">
                                    <x-heroicon-s-check class="w-5 mr-1"/>
                                    Add Bank Account
                                </button>
                            </div>
                            <div class="grow">
                                <div class="hidden px-3 py-2 bg-red-200 text-red-600 rounded-lg" id="bank-errors" role="alert">
                                    <i class="fas fa-exclamation-triangle"></i>&nbsp;
                                    <span id="bank-errors-message"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="gap-6 px-4 mt-4">
                <div class="mt-5 md:mt-0 md:col-span-2">
                    @if(!auth()->user()->account->hasFeatureForMember('account.setting.giving.allow_card.create'))
                        <div class="mb-4">
                            <div class="text-lg font-medium mb-1">
                                Can I use a credit/debit card?
                            </div>
                            <p class="ml-2 text-base">
                                ACH transactions are the only supported method for your congregation.
                            </p>
                        </div>
                    @endif
                    <div class="mb-4">
                        <div class="text-lg font-medium mb-1">
                            What happens next?
                        </div>
                        <p class="ml-2 text-base">
                            Within 1-2 business days, two micro-deposits will be put in your bank account.
                        </p>
                        <p class="ml-2">
                            Once these arrive, you will come back here to verify your bank account by confirming the micro-deposit amounts.
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="text-lg font-medium mb-1">
                            What do I do with a micro-deposit?
                        </div>
                        <p class="ml-2 text-base">
                            You should receive 2 micro-deposits to your bank account. Once you see these, please come back here and enter the amounts above to verify your bank account.
                        </p>
                    </div>
                    <div class="mb-4">
                        <div class="text-lg font-medium mb-1">
                            Why is this necessary?
                        </div>
                        <p class="ml-2 text-base">
                            Micro-deposits are a way to verify ownership of a bank account before funds can be withdrawn from it.
                        </p>
                    </div>
                </div>
            </div>
        </div>

    </div>

    @push('scripts')
        <script type="application/javascript">
            setTimeout(function () {
                // Create a Stripe client.
                var stripe = Stripe('{{ config('services.stripe.connect.key') }}', {
                    stripeAccount: '{{ auth()->user()->account->stripe_account_id }}'
                });

                // Create an instance of Elements.
                var elements = stripe.elements({
                    mode: 'setup',
                    currency: 'usd',
                });

                // Custom styling can be passed to options when creating an Element.
                // (Note that this demo uses a wider set of styles than the guide below.)
                var style = {
                    base: {
                        color: '#32325d',
                        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                        fontSmoothing: 'antialiased',
                        fontSize: '16px',
                        '::placeholder': {
                            color: '#aab7c4'
                        }
                    },
                    invalid: {
                        color: '#fa755a',
                        iconColor: '#fa755a'
                    }
                };

                @if(auth()->user()->account->hasFeatureForMember('account.setting.giving.allow_card.create'))

                // Create an instance of the card Element.
                var card = elements.create('card', {
                    style: style
                });

                // Add an instance of the card Element into the `card-element` <div>.
                card.mount('#card-element');

                // Handle real-time validation errors from the card Element.
                card.addEventListener('change', function (event) {
                    document.getElementById('card-errors').classList.add('hidden');
                    var displayError = document.getElementById('card-errors');
                    if (event.error) {
                        displayError.textContent = event.error.message;
                        displayError.classList.remove('hidden');
                    } else {
                        displayError.textContent = '';
                    }
                });

                // Handle CARD form submission.
                var cardForm = document.getElementById('card-form');
                cardForm.addEventListener('submit', function (event) {
                    event.preventDefault();
                    document.getElementById('card-errors').classList.add('hidden');
                    console.log('Card form submitted');

                    stripe.createToken(card).then(function (result) {
                        if (result.error) {
                            // Inform the user if there was an error.
                            var errorElement = document.getElementById('card-errors');
                            errorElement.textContent = result.error.message;
                            errorElement.classList.remove('hidden');
                            document.getElementById('card-button').disabled = false;
                        } else {
                            // Send the token to your server.
                            console.log(result);
                            document.getElementById('card-button').disabled = true;
                            stripeCardTokenHandler(result.token);
                        }
                    });
                });

                @endif

                // Handle BANK ACCOUNT form submission.
                var bankForm = document.getElementById('bank-form');
                bankForm.addEventListener('submit', function (event) {
                    event.preventDefault();
                    document.getElementById('bank-errors').classList.add('hidden');
                    console.log('Bank Account form submitted');
                    stripe.createToken('bank_account', {
                        country: 'US',
                        currency: 'usd',
                        routing_number: document.getElementById('routing_number').value,
                        account_number: document.getElementById('account_number').value,
                        account_holder_name: document.getElementById('account_holder_name').value,
                        account_holder_type: 'individual',
                    }).then(function (result) {
                        if (result.error) {
                            console.log(result.error);
                            // Inform the user if there was an error.
                            console.log(result.error.message);
                            var errorElement = document.getElementById('bank-errors');
                            errorElement.textContent = result.error.message;
                            errorElement.classList.remove('hidden');

                            if (result.error.code == 'parameter_invalid_empty') {
                                errorMessageElement.textContent = 'One of the required fields is empty.  Please check your information and try again.';
                            } else {
                                errorMessageElement.textContent = result.error.message;
                            }

                            document.getElementById('bank-button').disabled = false;
                            errorElement.classList.remove('hidden');
                        } else {
                            // Send the token to your server.
                            console.log(result);
                            document.getElementById('bank-button').disabled = true;
                            stripeBankTokenHandler(result.token);
                        }
                    });
                });

                // Submit the form with the token ID.
                function stripeCardTokenHandler(token) {
                    // Insert the token ID into the form so it gets submitted to the server
                    var form = document.getElementById('card-form');
                    var hiddenInput = document.createElement('input');
                    hiddenInput.setAttribute('type', 'hidden');
                    hiddenInput.setAttribute('name', 'stripeToken');
                    hiddenInput.setAttribute('value', token.id);
                    form.appendChild(hiddenInput);

                    // Submit the form
                    form.submit();
                }

                // Submit the form with the token ID.
                function stripeBankTokenHandler(token) {
                    // Insert the token ID into the form so it gets submitted to the server
                    var form = document.getElementById('bank-form');
                    var hiddenInput = document.createElement('input');
                    hiddenInput.setAttribute('type', 'hidden');
                    hiddenInput.setAttribute('name', 'stripeToken');
                    hiddenInput.setAttribute('value', token.id);
                    form.appendChild(hiddenInput);
                    console.log('token is ' + token.id);
                    // Submit the form
                    form.submit();
                }
            }, 500);
        </script>
    @endpush

@endsection