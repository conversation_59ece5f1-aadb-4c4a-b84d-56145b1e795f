@extends('app._layout._app')

@section('title', 'Visitors')

@section('content')

    {{--
    TailwindCSS classes that we need for the Visitor class.
    For Tail<PERSON> to pick up during compilation.

    text-black-800
    hover:bg-black-100
    bg-black-600
    bg-black-500
    bg-black-100
    text-black-900
    border-black-200
    border-black-500

    text-green-800
    hover:bg-green-100
    bg-green-600
    bg-green-500
    bg-green-100
    text-green-900
    border-green-200
    border-green-500

    text-red-800
    hover:bg-red-100
    bg-red-600
    bg-red-500
    bg-red-100
    text-red-900
    border-red-200
    border-red-500

    text-yellow-800
    hover:bg-yellow-100
    bg-yellow-600
    bg-yellow-500
    bg-yellow-100
    text-yellow-900
    border-yellow-200
    border-yellow-500

    text-orange-800
    hover:bg-orange-100
    bg-orange-600
    bg-orange-500
    bg-orange-100
    text-orange-900
    border-orange-200
    border-orange-500
    `
    text-blue-800
    hover:bg-blue-100
    bg-blue-600
    bg-blue-500
    bg-blue-100
    text-blue-900
    border-blue-200
    border-blue-500

    text-purple-800
    hover:bg-purple-100
    bg-purple-600
    bg-purple-500
    bg-purple-100
    text-purple-900
    border-purple-200
    border-purple-500
    --}}

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center justify-between">
            <h1 class="text-4xl text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center pb-1">
                <svg aria-hidden="true" focusable="false" data-prefix="far" data-icon="address-card" class="text-center w-9 h-9 mr-3 text-gray-900" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
                    <path fill="currentColor" d="M528 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zm0 400H48V80h480v352zM208 256c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm-89.6 128h179.2c12.4 0 22.4-8.6 22.4-19.2v-19.2c0-31.8-30.1-57.6-67.2-57.6-10.8 0-18.7 8-44.8 8-26.9 0-33.4-8-44.8-8-37.1 0-67.2 25.8-67.2 57.6v19.2c0 10.6 10 19.2 22.4 19.2zM360 320h112c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8zm0-64h112c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8zm0-64h112c4.4 0 8-3.6 8-8v-16c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v16c0 4.4 3.6 8 8 8z"></path>
                </svg>
                Visitor Tracking
            </h1>
        </div>
        <div class="flex items-center">
            <a href="{{ route('app.visitors.create') }}" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                </svg>
                Add a Visitor
            </a>
        </div>
    </div>

    {{--    <div class="hidden max-w-lg mx-auto md:grid-cols-3 md:max-w-none pt-4">--}}
    {{--        <div class="sm:rounded-sm">--}}
    {{--            <!-- This example requires Tailwind CSS v2.0+ -->--}}
    {{--            <span class="relative z-0 inline-flex shadow-xs rounded-md">--}}
    {{--                <button type="button" class="relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-10 focus:outline-hidden focus:ring-1 focus:ring-blue-500 focus:border-blue-500">--}}
    {{--                New Visitors (3)--}}
    {{--                </button>--}}
    {{--                <button type="button" class="-ml-px relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-10 focus:outline-hidden focus:ring-1 focus:ring-blue-500 focus:border-blue-500">--}}
    {{--                Follow-up--}}
    {{--                </button>--}}
    {{--                <button type="button" class="-ml-px relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-10 focus:outline-hidden focus:ring-1 focus:ring-blue-500 focus:border-blue-500">--}}
    {{--                All--}}
    {{--                </button>--}}
    {{--            </span>--}}

    {{--            <div class="relative inline-block text-left ml-2" x-data="{ open: false }" @click.outside="open = false" @keyup.escape="open = false">--}}
    {{--                <div>--}}
    {{--                    <button @click="open = !open" type="button" class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-xs px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-100 focus:ring-blue-500" id="menu-button" aria-expanded="true" aria-haspopup="true">--}}
    {{--                        Filters--}}
    {{--                        <!-- Heroicon name: solid/chevron-down -->--}}
    {{--                        <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>--}}
    {{--                        </svg>--}}
    {{--                    </button>--}}
    {{--                </div>--}}

    {{--                <div x-cloak x-show="open" class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 divide-y divide-gray-100 focus:outline-hidden" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95">--}}
    {{--                    <div class="py-1" role="none">--}}
    {{--                        <!-- Active: "bg-gray-100 text-gray-900", Not Active: "text-gray-700" -->--}}
    {{--                        <a href="#" class="text-gray-700 group flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-0">--}}
    {{--                            <!-- Heroicon name: solid/pencil-alt -->--}}
    {{--                            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                                <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"/>--}}
    {{--                                <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"/>--}}
    {{--                            </svg>--}}
    {{--                            Edit--}}
    {{--                        </a>--}}
    {{--                        <a href="#" class="text-gray-700 group flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-1">--}}
    {{--                            <!-- Heroicon name: solid/duplicate -->--}}
    {{--                            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                                <path d="M7 9a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9z"/>--}}
    {{--                                <path d="M5 3a2 2 0 00-2 2v6a2 2 0 002 2V5h8a2 2 0 00-2-2H5z"/>--}}
    {{--                            </svg>--}}
    {{--                            Duplicate--}}
    {{--                        </a>--}}
    {{--                    </div>--}}
    {{--                    <div class="py-1" role="none">--}}
    {{--                        <a href="#" class="text-gray-700 group flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-2">--}}
    {{--                            <!-- Heroicon name: solid/archive -->--}}
    {{--                            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                                <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>--}}
    {{--                                <path fill-rule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clip-rule="evenodd"/>--}}
    {{--                            </svg>--}}
    {{--                            Archive--}}
    {{--                        </a>--}}
    {{--                        <a href="#" class="text-gray-700 group flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-3">--}}
    {{--                            <!-- Heroicon name: solid/arrow-circle-right -->--}}
    {{--                            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd"/>--}}
    {{--                            </svg>--}}
    {{--                            Move--}}
    {{--                        </a>--}}
    {{--                    </div>--}}
    {{--                    <div class="py-1" role="none">--}}
    {{--                        <a href="#" class="text-gray-700 group flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-4">--}}
    {{--                            <!-- Heroicon name: solid/user-add -->--}}
    {{--                            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>--}}
    {{--                            </svg>--}}
    {{--                            Share--}}
    {{--                        </a>--}}
    {{--                        <a href="#" class="text-gray-700 group flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-5">--}}
    {{--                            <!-- Heroicon name: solid/heart -->--}}
    {{--                            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>--}}
    {{--                            </svg>--}}
    {{--                            Add to favorites--}}
    {{--                        </a>--}}
    {{--                    </div>--}}
    {{--                    <div class="py-1" role="none">--}}
    {{--                        <a href="#" class="text-gray-700 group flex items-center px-4 py-2 text-sm" role="menuitem" tabindex="-1" id="menu-item-6">--}}
    {{--                            <!-- Heroicon name: solid/trash -->--}}
    {{--                            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
    {{--                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>--}}
    {{--                            </svg>--}}
    {{--                            Delete--}}
    {{--                        </a>--}}
    {{--                    </div>--}}
    {{--                </div>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--    </div>--}}

    <div class="flex flex-col sm:flex-row max-w-lg mx-auto md:grid-cols-3 md:max-w-none py-6">
        <div class="flex flex-col sm:w-1/3 mr-2">
            @if(!$selected_status)
                <a class="flex justify-content-between rounded-sm bg-gray-500 hover:text-gray-700 group inline-flex items-center px-3 py-3 font-semibold">
                    <span class="flex-1 text-sm text-white">
                        All
                    </span>
                    <span class="bg-gray-100 text-gray-900 border border-gray-500 ml-2 px-2 rounded-full text-xs font-semibold md:inline-block">
                        {{ \App\Visitors\Visitor::visibleTo(auth()->user())->isNotArchived()->count() }}
                    </span>
                </a>
            @else
                <a href="{{ route('app.visitors.index') }}" class="flex justify-content-between hover:bg-white border-transparent text-gray-500 hover:text-gray-700 group inline-flex items-center px-3 py-3 font-semibold">
                    <span class="flex-1 text-sm text-gray-800">
                        All
                    </span>
                    <span class="bg-gray-100 text-gray-900 border border-gray-500 ml-2 px-2 rounded-full text-xs font-semibold md:inline-block">
                        {{ \App\Visitors\Visitor::visibleTo(auth()->user())->isNotArchived()->count() }}
                    </span>
                </a>
            @endif
            @foreach($statuses as $status)
                @if($status->id == $selected_status)
                    <a class="flex justify-content-between bg-{{ $status->tw_color }}-500 rounded-sm border-{{ $status->tw_color }}-500 text-gray-500 hover:text-gray-700 group inline-flex items-center px-3 py-3 font-semibold">
                        <span class="flex-1 text-sm text-white">
                            {{ $status->name }}
                        </span>
                        <span class="bg-{{ $status->tw_color }}-100 text-{{ $status->tw_color }}-900 border border-{{ $status->tw_color }}-500 ml-2 px-2 rounded-full text-xs font-semibold md:inline-block">
                            {{ $status->visitors_count }}
                        </span>
                    </a>
                @else
                    <a href="{{ route('app.visitors.index') }}?status={{ $status->id }}" class="flex justify-content-between hover:bg-white border-transparent text-gray-500 hover:text-gray-700 group inline-flex items-center px-3 py-3 font-semibold">
                        <span class="flex-1 text-sm text-{{ $status->tw_color }}-800">
                            {{ $status->name }}
                        </span>
                        <span class="bg-{{ $status->tw_color }}-100 text-{{ $status->tw_color }}-900 border border-{{ $status->tw_color }}-500 ml-2 px-2 rounded-full text-xs font-semibold md:inline-block">
                            {{ $status->visitors_count }}
                        </span>
                    </a>
                @endif
            @endforeach
        </div>
        <div class="w-full bg-white border border-gray-200 overflow-hidden sm:rounded-sm">
            <table class="w-full">
                <thead class="divide-y divide-gray-200 bg-gray-500 text-white">
                <tr>
                    <th class="py-2 pr-3 text-left text-sm font-medium sm:pl-6">
                        Name
                    </th>
                    <th class="px-3 py-2 text-left text-sm font-medium">
                        Last Contact
                    </th>
                    <th class="relative py-2 px-4 sm:pr-6">
                        <span class="sr-only">Edit</span>
                    </th>
                </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                @forelse($visitors as $visitor_record)
                    <tr class="hover:bg-gray-50 cursor-pointer" onclick="window.location='{{ route('app.visitors.view', $visitor_record) }}'">
                        <td class="whitespace-nowrap py-4 pl-4 pr-3 text-base font-medium text-gray-900 sm:pl-6">
                            @if($visitor_record->isFamily())
                                <span class="float-right ml-2 text-xs px-2 py-px rounded-full font-normal border border-blue-500 bg-blue-50 text-blue-700">Family</span>
                            @else
                                <span class="float-right ml-2 text-xs px-2 py-px rounded-full font-normal border border-gray-500 bg-gray-100 text-gray-800">Individual</span>
                            @endif
                            @if($visitor_record->userIsSubscribed(auth()->user()))
                                <span class="float-right ml-2 text-xs px-px py-px rounded-full font-normal border border-green-500 bg-green-50 text-green-500" title="Subscribed"><x-heroicon-s-check class="w-4"/></span>
                            @endif
                            @if($visitor_record->isFamily())
                                {!! $visitor_record->family?->getFamilyName() !!}
                            @else
                                {{ $visitor_record->user?->name }}
                            @endif
                        </td>
                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            @if($visitor_record->last_contact_at)
                                <time datetime="{{ $visitor_record->last_contact_at?->format('Y-m-d') }}">{{ $visitor_record->last_contact_at?->format('M d, Y') }}</time>
                                <span class="text-gray-400 text-xs">(<span class="text-xs text-gray-400">{{ $visitor_record->last_contact_at?->diffForHumans(null, false, false, 1) }}</span>)</span>
                            @else
                                <span class="text-sm text-gray-400">unknown</span>
                            @endif
                        </td>
                        <td class="relative whitespace-nowrap py-4 pl-3 text-right text-sm font-medium">
                            <svg class="h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </td>
                    </tr>
                @empty
                    <tr class="">
                        <td colspan="3" class="whitespace-nowrap py-6 pl-4 pr-3 text-sm text-center text-gray-400 sm:pl-6">
                            No visitors found for this status.
                        </td>
                    </tr>
                @endforelse

                <!-- More people... -->
                </tbody>
            </table>

            <hr>

            <div class="mx-4 mt-4">
                {{ $visitors->links() }}
            </div>
        </div>
    </div>

@endsection