@extends('app._layout._app')

@section('title', 'Volunteers')

@section('content')

    <div class="flex flex-row items-center justify-between">
        <div class="flex">
            <a onclick="history.back()" class="cursor-pointer px-3 py-1 text-lg border border-gray-400 text-gray-600 rounded-sm hover:bg-gray-100">&leftarrow;</a>
            <span class="flex-row flex text-3xl ml-4 font-semibold items-center">
                Volunteers - {{ $category->name }}
            </span>
        </div>
        <div class=" space-x-3">

        </div>
    </div>

    <div class="rounded-md bg-blue-50 p-2 mt-2 mb-0 border-blue-800 border">
        <div class="flex">
            <div class="shrink-0">
                <!-- Heroicon name: information-circle -->
                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="ml-3 flex-1 md:flex md:justify-between">
                <p class="text-blue-800">
                    Click an Area or Subarea to view volunteers.
                </p>
            </div>
        </div>
    </div>

    <div class="overflow-hidden bg-white sm:rounded-md mt-4 border border-gray-300">
        <ul role="list" class="divide-y divide-gray-200">
            @forelse($areas as $area)
                <li class="hover:bg-gray-50">
                    <div class="flex items-center px-2 py-3">
                        <div class="sm:flex flex-1 min-w-0 px-4">
                            <div class="grow">
                                <p onclick="document.getElementById('area-list-{{ $area->id }}').classList.contains('hidden') ? document.getElementById('area-list-{{ $area->id }}').classList.remove('hidden') : document.getElementById('area-list-{{ $area->id }}').classList.add('hidden')"
                                   class="flex cursor-pointer truncate text-base font-medium text-blue-600">
                                    {{ $area->name }}
                                </p>
                                <p class="flex items-center text-base text-gray-500">
                                    <x-heroicon-o-user-group class="w-5 mr-1"/>
                                    <span class="truncate">{{ $area->users()->membersOnly()->distinct()->count() }} Volunteers</span>
                                </p>
                                {{--                                <div class="flex space-x-2 my-auto mt-1">--}}
                                {{--                                    <div class="block text-sm text-gray-900">--}}
                                {{--                                        <a href="{{ route('app.involvement.selections.category.area.csv', [$category, $area]) }}"--}}
                                {{--                                           class="block flex border border-gray-500 px-2 py-1 rounded-sm hover:bg-gray-200">--}}
                                {{--                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">--}}
                                {{--                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>--}}
                                {{--                                            </svg>--}}
                                {{--                                            Copy Emails to Clipboard--}}
                                {{--                                        </a>--}}
                                {{--                                    </div>--}}
                                {{--                                </div>--}}
                            </div>
                            <div class="flex mb-auto space-x-2">
                                <div class="text-sm text-gray-900">
                                    <a href="{{ route('app.involvement.selections.category.area.csv', [$category, $area]) }}"
                                       class="flex block border border-gray-500 px-2 py-1 rounded-sm hover:bg-gray-200">
                                        <x-heroicon-o-document-arrow-down class="w-5 mr-1"/>
                                        CSV
                                    </a>
                                </div>
                                <div class="text-sm text-gray-900 cursor-pointer">
                                    <a onclick="document.getElementById('area-list-{{ $area->id }}').classList.contains('hidden') ? document.getElementById('area-list-{{ $area->id }}').classList.remove('hidden') : document.getElementById('area-list-{{ $area->id }}').classList.add('hidden')"
                                       class="flex block border border-gray-500 px-2 py-1 rounded-sm hover:bg-gray-200">
                                        <x-heroicon-o-clipboard-document-list class="w-5 mr-1"/>
                                        View Volunteers
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-5 hidden pl-12 p-4 border-t border-gray-200" id="area-list-{{ $area->id }}">
                        @foreach($area->users()->membersOnly()->distinct()->orderBy('last_name')->get() as $user)
                            <div class="col-span-1 flex">
                                <a href="{{ route('app.directory.view.family', $user) }}">
                                    <strong>{{ $user->last_name }}</strong>, {{ $user->display_first_name }}
                                </a>
                            </div>
                        @endforeach
                    </div>
                </li>
                @foreach($area->subareas as $subarea)
                    <li class="hover:bg-gray-50">
                        <div class="flex items-center px-2 py-3">
                            <div class="sm:flex flex-1 min-w-0 px-4">
                                <div class="grow ml-8">
                                    <p onclick="document.getElementById('subarea-list-{{ $subarea->id }}').classList.contains('hidden') ? document.getElementById('subarea-list-{{ $subarea->id }}').classList.remove('hidden') : document.getElementById('subarea-list-{{ $subarea->id }}').classList.add('hidden')"
                                       class="flex cursor-pointer truncate text-base font-medium text-blue-600">
                                        <x-heroicon-s-arrow-right-circle class="w-5 mr-1"/>{{ $subarea->name }}
                                    </p>
                                    <p class="flex items-center text-base text-gray-500">
                                        <x-heroicon-o-user-group class="w-5 mr-1"/>
                                        <span class="truncate">{{ $subarea->users()->membersOnly()->distinct()->count() }} Volunteers</span>
                                    </p>
                                    {{--                                    <div class="flex space-x-2 my-auto mt-1">--}}
                                    {{--                                    <div class="block text-sm text-gray-900">--}}
                                    {{--                                        <a href="{{ route('app.involvement.selections.category.area.csv', [$category, $area]) }}"--}}
                                    {{--                                           class="block flex border border-gray-500 px-2 py-1 rounded-sm hover:bg-gray-200">--}}
                                    {{--                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">--}}
                                    {{--                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>--}}
                                    {{--                                            </svg>--}}
                                    {{--                                            Copy Emails to Clipboard--}}
                                    {{--                                        </a>--}}
                                    {{--                                    </div>--}}
                                    {{--                                    </div>--}}
                                </div>
                                <div class="flex mb-auto space-x-2">
                                    <div class="text-sm text-gray-900">
                                        <a href="{{ route('app.involvement.selections.category.area.subarea.csv', [$category, $area, $subarea]) }}"
                                           class="flex block border border-gray-500 px-2 py-1 rounded-sm hover:bg-gray-200">
                                            <x-heroicon-o-document-arrow-down class="w-5 mr-1"/>
                                            CSV
                                        </a>
                                    </div>
                                    <div class="text-sm text-gray-900 cursor-pointer">
                                        <a onclick="document.getElementById('subarea-list-{{ $subarea->id }}').classList.contains('hidden') ? document.getElementById('subarea-list-{{ $subarea->id }}').classList.remove('hidden') : document.getElementById('subarea-list-{{ $subarea->id }}').classList.add('hidden')"
                                           class="flex block border border-gray-500 px-2 py-1 rounded-sm hover:bg-gray-200">
                                            <x-heroicon-o-clipboard-document-list class="w-5 mr-1"/>
                                            View Volunteers
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-5 hidden pl-12 p-4 border-t border-gray-200" id="subarea-list-{{ $subarea->id }}">
                            @foreach($subarea->users()->membersOnly()->distinct()->orderBy('last_name')->get() as $user)
                                <div class="col-span-1 flex">
                                    <a href="{{ route('app.directory.view.family', $user) }}">
                                        <strong>{{ $user->last_name }}</strong>, {{ $user->display_first_name }}
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </li>
                @endforeach
            @empty
                <div class="text-center">
                    <div class="px-4 py-2 bg-yellow-100 my-6 rounded-sm"><strong>No Areas or Subareas exist yet!</strong><br><span class="text-gray-600">Please check back later.</span></div>
                </div>
            @endforelse
        </ul>
    </div>

@endsection