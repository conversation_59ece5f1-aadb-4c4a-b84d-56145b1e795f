@extends('app._layout._app')

@section('title', 'Volunteers')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Volunteer Categories
            </h1>
        </div>
    </div>

    <div class="rounded-md bg-blue-50 p-2 mt-4 mb-0 border-blue-800 border">
        <div class="flex">
            <div class="shrink-0">
                <!-- Heroicon name: information-circle -->
                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="ml-3 flex-1 md:flex md:justify-between">
                <p class="text-blue-800">
                    Click a category to view areas and volunteers.
                </p>
            </div>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none py-4">
        <div class="bg-white border border-gray-300 overflow-hidden sm:rounded-md">
            <ul role="list" class="divide-y divide-gray-200">
                @forelse($categories as $category)
                    <li>
                        <div class="block hover:bg-gray-50">
                            <div class="px-4 py-3 flex items-center sm:px-6">
                                <div class="min-w-0 flex-1 sm:flex sm:items-center sm:justify-between">
                                    <a href="{{ route('app.involvement.selections.category', $category) }}" class="truncate">
                                        <div class="flex text-base">
                                            <p class="font-medium text-blue-600 truncate">{{ $category->name }}</p>
                                        </div>
                                        <div class="mt-1 flex">
                                            <div class="flex items-center text-sm text-gray-500">
                                                <x-heroicon-o-user-group class="w-4 mr-1"/>
                                                <span class="truncate">{{ $category->users()->membersOnly()->distinct()->get()->count() }} Volunteers Total</span>
                                            </div>
                                        </div>
                                    </a>
                                    <div class="mt-4 shrink-0 sm:mt-0 sm:ml-5">
                                    </div>
                                </div>
                                <div class="ml-5 shrink-0">
                                    <div class="flex flex-row">
                                        <div class="text-sm text-gray-900">
                                            <a href="{{ route('app.involvement.selections.category.csv', [$category]) }}"
                                               class="flex block border border-gray-500 px-2 py-1 rounded-sm hover:bg-gray-200">
                                                <x-heroicon-o-document-arrow-down class="w-5 mr-1"/>
                                                CSV
                                            </a>
                                        </div>
                                        <svg class="ml-5 h-5 w-5 my-auto text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                @empty
                    <div class="text-center">
                        <div class="px-4 py-2 bg-yellow-100 my-6 rounded-sm"><strong>No Categories exist yet!</strong><br><span class="text-gray-600">Please check back later.</span></div>
                    </div>
                @endforelse
            </ul>
        </div>

    </div>

@endsection