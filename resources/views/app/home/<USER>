@php
    $assignments = \App\WorshipAssignments\Pick::visibleTo(auth()->user())
                    ->forUser(auth()->user())
                    ->isActiveOrFuture()
                    ->isPublished()
                    ->orderBy('start_at')
                    ->get()
@endphp

@if($assignments->count() > 0)
    <div class="mt-4 max-w-lg mx-auto lg:max-w-none">
        <div class="flex flex-col col-span-3">
            <div class="pb-2 flex flex-col">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                    </svg>
                    <h3 class="ml-1 text-lg font-semibold text-gray-900 uppercase">
                        Assignments
                    </h3>
                </div>
            </div>
            <div class="bg-white flex flex-col rounded-lg border border-gray-300 divide-y divide-gray-300 overflow-hidden bg-white">
                @foreach($assignments as $assignment)
                    <div class="flex justify-between px-6 py-4">
                        <div>
                            <div class="text-lg font-medium text-gray-900">
                                {{ $assignment->position->name }}
                            </div>
                            <div class="text-base text-gray-500">
                                {{ $assignment->start_at->format('M d, Y') }}
                            </div>
                        </div>
                        <div class="my-auto">
                            @if($assignment->isConfirmed())
                                <span class="flex px-2 py-1 border border-green-500 bg-green-50 text-green-500 rounded-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 my-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                &nbsp;Confirmed
                            </span>
                            @elseif($assignment->isDeclined())
                                <span class="flex px-2 py-1 border border-red-500 bg-red-50 text-red-500 rounded-sm">
                                Declined
                            </span>
                            @elseif(!$assignment->hasReplied())
                                <div class="flex flex-row space-x-2">
                                    <a href="{{ route('app.worship-assignments.picks.userConfirm', [$assignment, $assignment->token]) }}"
                                       target="_blank"
                                       class="flex px-2 py-1 border border-green-500 bg-green-500 hover:bg-green-600 text-white rounded-sm">
                                        Confirm
                                    </a>
                                    <a href="{{ route('app.worship-assignments.picks.userDecline', [$assignment, $assignment->token]) }}"
                                       target="_blank"
                                       class="flex px-2 py-1 border border-red-500 hover:bg-red-500 text-red-500 hover:text-white rounded-sm">
                                        Decline
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endif