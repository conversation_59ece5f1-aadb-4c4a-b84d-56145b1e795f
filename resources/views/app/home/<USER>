@php($crisis = Auth::user()->account->getActiveCrisis())
@php($checkin = Auth::user()->getCheckinForCrisis($crisis->id))

<div class="max-w-lg mx-auto py-4">
    <div class="flex flex-col rounded-lg border border-gray-300 overflow-hidden bg-white">
        <div class="flex bg-white px-4 py-3 flex flex-col">
            <h3 class="mt-2 text-xl leading-7 font-semibold text-red-600 justify-center inline-flex">
                <svg class="w-7 h-7 inline text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
                Emergency Check-in
            </h3>
        </div>
        <div class="flex flex-col">
            <div class="text-center">
                <div class="justify-center px-8">
                    <div class="text-2xl font-semibold rounded-sm">{{ $crisis->name }}</div>
                </div>
                @if($checkin && $checkin->type != 'not_responded')
                    <div class="justify-center">
                        <div class="rounded-sm">
                            You have checked in as:
                        </div>
                        @if($checkin->type == 'ok')
                            <div class="inline-flex bg-green-500 text-white mt-1 px-3 py-2 rounded-sm">
                                <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                                </svg>
                                OK
                            </div>
                        @elseif($checkin->type == 'help')
                            <div class="inline-flex whitespace-nowrap bg-yellow-500 text-white mt-1 px-3 py-2 rounded-sm">
                                <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"/>
                                </svg>
                                Help
                            </div>
                        @elseif($checkin->type == 'urgent_help')
                            <div class="inline-flex whitespace-nowrap bg-red-500 text-white mt-1 px-3 py-2 rounded-sm">
                                <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                </svg>
                                Urgent
                            </div>
                        @elseif($checkin->type == 'not_responded')
                            <div class="text-gray-400 whitespace-nowrap py-1">
                                <i class="fa fa-minus"></i> Unresponded
                            </div>
                        @endif
                    </div>
                    <div class="flex justify-center mt-3">
                        <a href="{{ route('app.crises.user-checkin') }}" class="px-4 py-1 bg-gray-200 text-gray-700 rounded-sm inline-flex hover:bg-gray-400 hover:text-white">
                            Change Response
                        </a>
                    </div>
                @else
                    <div class="justify-center">
                        <div class="rounded-sm">
                            Let us know your status!
                        </div>
                    </div>
                    <div class="flex justify-center mt-3">
                        <a href="{{ route('app.crises.user-checkin') }}?type=ok" class="px-10 py-3 bg-green-500 text-white rounded-sm inline-flex hover:bg-green-400">
                            <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                            </svg>
                            Check-in
                        </a>
                        <a href="{{ route('app.crises.user-checkin') }}?type=help" class="ml-4 px-3 py-3 border-2 text-red-500 border-red-500 text-white rounded-sm inline-flex hover:bg-red-200">
                            <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"/>
                            </svg>
                            Request Help
                        </a>
                    </div>
                @endif
            </div>
        </div>
        <div class="flex justify-around mt-6 border-t border-gray-300 pt-4 pb-3">
            <div>
                <div class="inline-flex text-green-500">
                    <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                    </svg>
                    <span>
                    <strong>{{ $crisis->getOkResponses()->count() }}</strong> OK
                </span>
                </div>
            </div>
            <div>
                <div class="inline-flex text-yellow-600">
                    <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"/>
                    </svg>
                    <span>
                        <strong>{{ $crisis->getHelpResponses()->count() }}</strong> Need Help
                    </span>
                </div>
            </div>
            <div>
                <div class="inline-flex text-red-500">
                    <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                    <span>
                        <strong>{{ $crisis->getUrgentHelpResponses()->count() }}</strong> Urgent Help
                    </span>
                </div>
            </div>
        </div>
        <div>
            <a href="{{ route('app.crises.view', $crisis) }}" class="flex justify-center border-t border-gray-300 py-3 hover:bg-gray-100">
                <div class="inline-flex font-semibold">
                    View Responses
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
            </a>
        </div>
    </div>
</div>