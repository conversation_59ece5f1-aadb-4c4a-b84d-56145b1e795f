@php($currentSignupBCGroup = Auth::user()->account->activeSignupBibleClassGroup())
@php($registeredClasses = Auth::user()->getRegisteredClassesForGroup($currentBCGroup->id))


<div class="mt-4 flex flex-col">
    <div class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
        </svg>
        <h3 class="ml-1 text-lg font-semibold text-gray-900 uppercase">
            Bible Class Registration
        </h3>
    </div>
</div>

<div class="bg-white border border-gray-300 overflow-hidden rounded-sm mt-2 mb-4">
    <div class="px-4 py-4 sm:px-6 flex items-center justify-between">
        @if(Auth::user()->isRegisteredForBibleClassGroup($currentBCGroup->id))
            <span class="flex px-2 py-1 border border-green-500 bg-green-50 text-green-500 rounded-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 my-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                &nbsp;Registered
            </span>
        @else
            <span class="flex px-2 py-1 border border-yellow-500 bg-yellow-50 text-yellow-500 rounded-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="flex-1 align-content-center h-5 w-5 my-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
                &nbsp;Not Registered
            </span>
        @endif
        <div>
            <p class="text-gray-500 text-base">
                {{ $currentBCGroup?->start_date?->format('F d, Y') }} ﹣ {{ $currentBCGroup?->end_date?->format('F d, Y') }}
            </p>
        </div>

    </div>
    <div class="border-t border-gray-200">
        <div class="flex flex-col">
            <div class="overflow-x-auto">
                <div class="align-middle inline-block min-w-full">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="pl-6 pr-3 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Day
                            </th>
                            <th scope="col" class="pl-3 pr-3 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Title
                            </th>
                            <th scope="col" class="pl-3 pr-6 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Room
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        @if(Auth::user()->isRegisteredForBibleClassGroup($currentBCGroup->id))
                            @foreach($registeredClasses as $bible_class)
                                <tr class="{{ $loop->odd ? 'bg-white' : 'bg-gray-50' }}">
                                    <td class="pl-6 pr-3 py-3 whitespace-nowrap font-medium text-gray-900">
                                        {{ $bible_class->dayOfWeek() }}
                                    </td>
                                    <td class="pl-3 pr-3 py-3 whitespace-nowrap text-gray-700">
                                        {{ $bible_class->title }}
                                    </td>
                                    <td class="pl-3 pr-6 py-3 whitespace-nowrap text-gray-500">
                                        {{ $bible_class->location_name }}
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="">
                                    <div class="flex justify-center p-6">
                                        <a href="#" class="flex py-2 px-4 bg-blue-600 text-white rounded-sm items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                                            </svg>
                                            &nbsp;Register for Bible Class
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
