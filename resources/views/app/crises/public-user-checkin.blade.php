@extends('app._layout.no-header')

@section('content')

    <div class="z-10 inset-0 overflow-y-auto">
        <div class="flex items-start justify-center min-h-screen pt-4 px-4 text-center sm:block">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-200 opacity-75"></div>
            </div>

            {{--            <!-- This element is to trick the browser into centering the modal contents. -->--}}
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div class="inline-block align-bottom bg-white rounded-lg pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:py-6" role="dialog" aria-modal="true" aria-labelledby="modal-headline">

                <div>
                    @if($crisis->account)
                        <div class="mx-auto flex items-center justify-center text-3xl font-medium">
                            {{ $crisis->account->name }}
                        </div>
                    @endif


                    @if(session('message.success') || session('message.failure') || isset($errors))
                        @if(session('message.success'))
                            <div class="bg-green-500" id="message-box">
                                <div class="max-w-(--breakpoint-xl) mx-auto py-3 px-3 sm:px-6 lg:px-8">
                                    <div class="flex items-center justify-between flex-wrap">
                                        <div class="w-0 flex-1 flex items-center">
                                            <span class="flex p-2 rounded-lg bg-green-700">
                                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                                                </svg>
                                            </span>
                                            <p class="ml-3 font-medium text-white truncate">
                                                {!! session('message.success') !!}
                                            </p>
                                        </div>
                                        <div class="order-2 shrink-0 sm:order-3 sm:ml-3">
                                            <button type="button" class="-mr-1 flex p-2 rounded-md hover:bg-green-500 focus:outline-hidden focus:bg-green-500 sm:-mr-2 transition ease-in-out duration-150" aria-label="Dismiss" onclick="document.getElementById('message-box').hidden = true;">
                                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if(session('message.failure'))
                            <div class="bg-red-500" id="message-box">
                                <div class="max-w-(--breakpoint-xl) mx-auto py-3 px-3 sm:px-6 lg:px-8">
                                    <div class="flex items-center justify-between flex-wrap">
                                        <div class="w-0 flex-1 flex items-center">
                                            <span class="flex p-2 rounded-lg bg-red-700">
                                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </span>
                                            <p class="ml-3 font-medium text-white truncate">
                                                {!! session('message.failure') !!}
                                            </p>
                                        </div>
                                        <div class="order-2 shrink-0 sm:order-3 sm:ml-3">
                                            <button type="button" class="-mr-1 flex p-2 rounded-md hover:bg-red-500 focus:outline-hidden focus:bg-red-500 sm:-mr-2 transition ease-in-out duration-150" aria-label="Dismiss" onclick="document.getElementById('message-box').hidden = true;">
                                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if(isset($errors) && $errors->count() > 0)
                            <div class="bg-red-500" id="message-box">
                                <div class="max-w-(--breakpoint-xl) mx-auto py-3 px-3 sm:px-6 lg:px-8">
                                    <div class="flex items-center justify-between flex-wrap">
                                        <div class="w-0 flex-1 flex items-center">
                                            <span class="flex p-2 rounded-lg bg-red-700">
                                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </span>
                                            <p class="ml-3 font-medium text-white truncate">
                                                @foreach ($errors->all() as $error)
                                                    {!! $error !!}<br>
                                                @endforeach
                                            </p>
                                        </div>
                                        <div class="order-2 shrink-0 sm:order-3 sm:ml-3">
                                            <button type="button" class="-mr-1 flex p-2 rounded-md hover:bg-red-500 focus:outline-hidden focus:bg-red-500 sm:-mr-2 transition ease-in-out duration-150" aria-label="Dismiss" onclick="document.getElementById('message-box').hidden = true;">
                                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif

                    <div class="mt-4">
                        <div class="bg-white overflow-hidden sm:rounded-md">
                            <form class="" action="{{ route('app.public.crises.user-checkin.save', $crisis) }}" method="POST">
                                @method('post')
                                @csrf()
                                <input type="hidden" name="token" value="{{ $token }}"/>
                                <div class="pt-4 px-4 space-y-4 sm:px-6 pb-2 lg:pb-4">
                                    <div>
                                        <h2 class="text-xl text-center font-bold text-gray-900">Family Check-in</h2>
                                    </div>
                                    <div>
                                        Let us know if you're OK, in need a little help, or a lot of help!
                                        <em>Your church family is eager to serve.</em>
                                    </div>
                                </div>

                                <div class="pt-4">
                                    <div class="px-4 space-y-2 sm:px-6">
                                        <div class="bg-white rounded-md -space-y-px">
                                            <fieldset x-data="radioGroup()">
                                                <div class="bg-white rounded-md -space-y-px" x-ref="radiogroup">
                                                    <script>
                                                        function radioGroup() {
                                                            return {
                                                                active: {{ (request('type') !== 'help') ? '0' : '1' }},
                                                                onArrowUp(index) {
                                                                    this.select(this.active - 1 < 0 ? this.$refs.radiogroup.children.length - 1 : this.active - 1);
                                                                },
                                                                onArrowDown(index) {
                                                                    this.select(this.active + 1 > this.$refs.radiogroup.children.length - 1 ? 0 : this.active + 1);
                                                                },
                                                                select(index) {
                                                                    this.active = index;
                                                                },
                                                            };
                                                        }
                                                    </script>
                                                    <div :class="{ 'border-gray-200': !(active === 0), 'bg-green-50 border-green-200 z-10': active === 0 }" class="relative border rounded-tl-md rounded-tr-md p-4 flex bg-green-50 border-green-200 z-10">
                                                        <div class="flex items-center h-5">
                                                            <input id="settings-option-0" name="checkin_type" value="ok" type="radio" @click="select(0)" @keydown.space="select(0)" @keydown.arrow-up="onArrowUp(0)" @keydown.arrow-down="onArrowDown(0)" class="focus:ring-green-500 h-4 w-4 text-green-600 cursor-pointer border-gray-300" @isChecked((request('type') !== 'help'))">
                                                        </div>
                                                        <label for="settings-option-0" class="ml-3 flex flex-col cursor-pointer">
                                                    <span class="block font-medium text-green-600">
                                                        We're OK!
                                                    </span>
                                                            <span :class="{ 'text-gray-700': active === 0, 'text-gray-500': !(active === 0) }" class="block text-sm text-gray-700">
                                                        Please check-in and then look for others who might be in need of help.
                                                    </span>
                                                        </label>
                                                    </div>

                                                    <div :class="{ 'border-gray-200': !(active === 1), 'bg-yellow-50 border-yellow-200 z-10': active === 1 }" class="relative border p-4 flex border-gray-200">
                                                        <div class="flex items-center h-5">
                                                            <input id="settings-option-1" name="checkin_type" value="help" type="radio" @click="select(1)" @keydown.space="select(1)" @keydown.arrow-up="onArrowUp(1)" @keydown.arrow-down="onArrowDown(1)" class="focus:ring-yellow-500 h-4 w-4 text-yellow-600 cursor-pointer border-gray-300" @isChecked((request('type') == 'help'))>
                                                        </div>
                                                        <label for="settings-option-1" class="ml-3 flex flex-col cursor-pointer">
                                                    <span class="block font-medium text-yellow-600">
                                                        We could use a <strong>little help</strong>!
                                                    </span>
                                                            <span :class="{ 'text-gray-700': active === 1, 'text-gray-500': !(active === 1) }" class="block text-sm text-gray-500">
                                                        If you could use some help as someone is available, are in need of supplies, or anything non-urgent.
                                                    </span>
                                                        </label>
                                                    </div>

                                                    <div :class="{ 'border-gray-200': !(active === 2), 'bg-red-50 border-red-200 z-10': active === 2 }" class="relative border rounded-bl-md rounded-br-md p-4 flex border-gray-200">
                                                        <div class="flex items-center h-5">
                                                            <input id="settings-option-2" name="checkin_type" value="urgent_help" type="radio" @click="select(2)" @keydown.space="select(2)" @keydown.arrow-up="onArrowUp(2)" @keydown.arrow-down="onArrowDown(2)" class="focus:ring-red-500 h-4 w-4 text-red-600 cursor-pointer border-gray-300">
                                                        </div>
                                                        <label for="settings-option-2" class="ml-3 flex flex-col cursor-pointer">
                                                    <span class="block font-medium text-red-500">
                                                        We need some <strong>urgent</strong> help!
                                                    </span>
                                                            <span :class="{ 'text-gray-700': active === 2, 'text-gray-500': !(active === 2) }" class="block text-sm text-gray-500">
                                                        If you need someone to come help, or make contact ASAP.  We'll notify those who have volunteered immediately!
                                                        <br>
                                                        If you are in need of <strong>urgent medical help</strong> or other <strong>emergency services</strong>, please <strong>call 911</strong>.
                                                    </span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </fieldset>
                                            <div class="pt-6">
                                                <label for="notes" class="block text-lg font-semibold text-gray-800">
                                                    Notes
                                                </label>
                                                <div class="mt-1">
                                                    <textarea id="notes" name="notes" rows="3" class="shadow-xs focus:ring-blue-500 focus:border-indigo-500 mt-1 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="...">{{ $checkin?->notes }}</textarea>
                                                </div>
                                                <p class="mt-2 text-sm text-gray-500">
                                                    Describe your need for help or current status.
                                                </p>
                                            </div>

                                        </div>
                                    </div>
                                    <div class="mt-4 py-4 px-4 flex justify-center sm:px-6">
                                        <div class="space-x-5 flex">
                                            <div class="rounded-md shadow-xs">
                                                <button type="submit" class="bg-blue-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center font-medium text-white hover:bg-blue-500 focus:outline-hidden focus:border-blue-800 focus:ring-blue active:bg-blue-800 transition duration-150 ease-in-out">
                                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                                    </svg>
                                                    &nbsp;Submit Check-in
                                                </button>
                                            </div>
                                            <div class="rounded-md shadow-xs">
                                                <a href="{{ route('app.home.index') }}" class="bg-white border border-gray-300 rounded-md py-2 px-4 inline-flex justify-center font-medium text-gray-700 hover:text-gray-500 focus:outline-hidden focus:border-light-blue-300 focus:ring-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out">
                                                    Cancel
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="mt-6 pt-6 sm:mt-6 text-center border-t border-gray-300">
                    Login to <a href="/">Lightpost</a> to see others' responses.
                </div>
                <div class="mx-auto flex items-center justify-center mt-6">
                    <img src="{{ asset('img/lightpost-logo.svg') }}" alt="Lightpost" class="mb-4 h-8">
                </div>

            </div>
        </div>
    </div>

@endsection
