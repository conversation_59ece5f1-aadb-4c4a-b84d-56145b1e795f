@extends('app._layout._app')

@section('title', 'Check-in')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-semibold pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Emergency Check-in
            </h1>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none mt-4">
        <div class="bg-white border border-gray-300 overflow-hidden sm:rounded-md">
            <form class="divide-y divide-gray-200 lg:col-span-9" action="{{ route('app.crises.user-checkin.save', $crisis) }}" method="POST">
                @method('post')
                @csrf()
                <div class="pt-4 px-4 space-y-4 sm:p-6 pb-2 lg:pb-4">
                    <div>
                        <h2 class="text-xl font-medium text-gray-900">Family Check-in</h2>
                    </div>
                    <div>
                        Let us know if you're OK, in need a little help, or a lot of help!
                        <br>
                        <em>Your church family is eager to serve.</em>
                    </div>
                </div>

                @php
                    if(request()->has('type')) {
                        $already_checked_in = false;
                        $selected_option = match(request('type')) {
                            'ok' => 1,
                            'help' => 1,
                            'urgent_help' => 2,
                        };
                    } else {
                        $already_checked_in = true;
                        $selected_option = match($checkin->type) {
                            'ok' => 1,
                            'help' => 1,
                            'urgent_help' => 2,
                        };
                    }
                @endphp
                <div class="pt-4 divide-y divide-gray-200">
                    <div class="px-4 space-y-2 sm:px-6">
                        <div class="bg-white rounded-md -space-y-px">
                            <fieldset x-data="radioGroup()">
                                <div class="bg-white rounded-md -space-y-px" x-ref="radiogroup">
                                    <script>
                                        function radioGroup() {
                                            return {
                                                active: {{ $selected_option }},
                                                onArrowUp(index) {
                                                    this.select(this.active - 1 < 0 ? this.$refs.radiogroup.children.length - 1 : this.active - 1);
                                                },
                                                onArrowDown(index) {
                                                    this.select(this.active + 1 > this.$refs.radiogroup.children.length - 1 ? 0 : this.active + 1);
                                                },
                                                select(index) {
                                                    this.active = index;
                                                },
                                            };
                                        }
                                    </script>
                                    @if($already_checked_in)
                                        <div class="p-3 bg-blue-50 border border-blue-500 mb-4 rounded-sm text-blue-700">
                                            It looks like your family has <strong>already checked in</strong>.
                                            <br>
                                            Update your check-in if something has changed!
                                        </div>
                                        <div class="p-2"></div>
                                    @endif
                                    <div :class="{ 'border-gray-200': !(active === 0), 'bg-green-50 border-green-200 z-10': active === 0 }" class=" border rounded-tl-md rounded-tr-md p-4 flex bg-green-50 border-green-200 z-10">
                                        <div class="flex items-center h-5">
                                            <input id="settings-option-0" name="checkin_type" value="ok" type="radio" @click="select(0)" @keydown.space="select(0)" @keydown.arrow-up="onArrowUp(0)" @keydown.arrow-down="onArrowDown(0)" class="focus:ring-green-500 h-4 w-4 text-green-600 cursor-pointer border-gray-300" @isChecked($selected_option == 0)">
                                        </div>
                                        <label for="settings-option-0" class="ml-3 flex flex-col cursor-pointer">
                                            <span class="block font-medium text-green-600">
                                                We're OK!
                                            </span>
                                            <span :class="{ 'text-gray-700': active === 0, 'text-gray-500': !(active === 0) }" class="block text-sm text-gray-700">
                                                Please check-in and then look for others who might be in need of help.
                                            </span>
                                        </label>
                                    </div>

                                    <div :class="{ 'border-gray-200': !(active === 1), 'bg-yellow-50 border-yellow-200 z-10': active === 1 }" class=" border p-4 flex border-gray-200">
                                        <div class="flex items-center h-5">
                                            <input id="settings-option-1" name="checkin_type" value="help" type="radio" @click="select(1)" @keydown.space="select(1)" @keydown.arrow-up="onArrowUp(1)" @keydown.arrow-down="onArrowDown(1)" class="focus:ring-yellow-500 h-4 w-4 text-yellow-600 cursor-pointer border-gray-300" @isChecked($selected_option == 1)>
                                        </div>
                                        <label for="settings-option-1" class="ml-3 flex flex-col cursor-pointer">
                                            <span class="block font-medium text-yellow-600">
                                                We could use a <strong>little help</strong>!
                                            </span>
                                            <span :class="{ 'text-gray-700': active === 1, 'text-gray-500': !(active === 1) }" class="block text-sm text-gray-500">
                                                If you could use some help as someone is available, are in need of supplies, or anything non-urgent.
                                            </span>
                                        </label>
                                    </div>

                                    <div :class="{ 'border-gray-200': !(active === 2), 'bg-red-50 border-red-200 z-10': active === 2 }" class=" border rounded-bl-md rounded-br-md p-4 flex border-gray-200">
                                        <div class="flex items-center h-5">
                                            <input id="settings-option-2" name="checkin_type" value="urgent_help" type="radio" @click="select(2)" @keydown.space="select(2)" @keydown.arrow-up="onArrowUp(2)" @keydown.arrow-down="onArrowDown(2)" class="focus:ring-red-500 h-4 w-4 text-red-600 cursor-pointer border-gray-300" @isChecked($selected_option == 2)>
                                        </div>
                                        <label for="settings-option-2" class="ml-3 flex flex-col cursor-pointer">
                                            <span class="block font-medium text-red-500">
                                                We need some <strong>urgent</strong> help!
                                            </span>
                                            <span :class="{ 'text-gray-700': active === 2, 'text-gray-500': !(active === 2) }" class="block text-sm text-gray-500">
                                                If you need someone to come help, or make contact ASAP.  We'll notify those who have volunteered immediately!
                                                <br>
                                                If you are in need of <strong>urgent medical help</strong> or other <strong>emergency services</strong>, please <strong>call 911</strong>.
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                            <div class="pt-6">
                                <label for="notes" class="block text-base font-semibold text-gray-800">
                                    Notes
                                </label>
                                <div class="mt-1">
                                    <textarea id="notes" name="notes" rows="3" class="shadow-xs focus:ring-blue-500 focus:border-indigo-500 mt-1 block w-full text-sm sm:text-base border-gray-300 rounded-md" placeholder="...">{{ $checkin?->notes }}</textarea>
                                </div>
                                <p class="mt-2 text-xs text-gray-500">
                                    Description of your need for help or current status.
                                </p>
                            </div>

                        </div>
                    </div>
                    <div class="mt-4 py-4 px-4 flex justify-between sm:px-6">
                        <div class="space-x-5 flex">
                            <div class="inline-flex rounded-md shadow-xs">
                                <button type="submit" class="bg-blue-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center  leading-5 font-medium text-white hover:bg-blue-500 focus:outline-hidden focus:border-blue-800 focus:ring-blue active:bg-blue-800 transition duration-150 ease-in-out">
                                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                    </svg>
                                    &nbsp;Submit Check-in
                                </button>
                            </div>
                            <div class="inline-flex rounded-md shadow-xs">
                                <a href="{{ route('app.home.index') }}" class="bg-white border border-gray-300 rounded-md py-2 px-4 inline-flex justify-center  leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-hidden focus:border-light-blue-300 focus:ring-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out">
                                    Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

@endsection
