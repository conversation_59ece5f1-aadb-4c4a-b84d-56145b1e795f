@extends('app._layout._app')

@section('title', 'Emergency Check-ins')

@section('content')

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="">
            <h1 class="text-4xl font-semibold pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Emergency Check-ins
            </h1>
            <div class="text-2xl">{{ $crisis->name }}</div>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none sm:py-3">
        <div class="overflow-x-auto">
            <div class="py-2 align-middle inline-block min-w-full">
                <div class="overflow-hidden border border-gray-300 rounded-sm">
                    <table class="min-w-full" cellpadding="3">
                        <thead>
                        <tr class="bg-gray-600 text-white">
                            <th class="py-2" width="10%">Response</th>
                            <th width="30%" class="text-left">Family</th>
                            <th class="text-left">Notes</th>
                            <th width="2%">&nbsp;</th>
                        </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-300">
                        @forelse($crisis->checkins as $checkin)
                            <tr class="bg-white hover:bg-gray-100 cursor-pointer"
                                onclick="window.location = '{{ route('app.crises.checkins.view', [$crisis, $checkin]) }}'"
                            >
                                <td class="text-right px-2">
                                    <a class="block text-black" href="{{ route('app.crises.checkins.view', [$crisis, $checkin]) }}">
                                        @if($checkin->type == 'ok')
                                            <div class="inline-flex bg-green-500 text-white mt-1 px-2 py-px rounded-sm">
                                                <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                                                </svg>
                                                <span>
                                                    OK
                                                </span>
                                            </div>
                                        @elseif($checkin->type == 'help')
                                            <div class="inline-flex whitespace-nowrap bg-yellow-500 text-white mt-1 px-2 py-px rounded-sm">
                                                <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"/>
                                                </svg>
                                                Help
                                            </div>
                                        @elseif($checkin->type == 'urgent_help')
                                            <div class="inline-flex whitespace-nowrap bg-red-500 text-white mt-1 px-2 py-px rounded-sm">
                                                <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                </svg>
                                                Urgent
                                            </div>
                                        @elseif($checkin->type == 'not_responded')
                                            <div class="text-gray-400 whitespace-nowrap py-1">
                                                <i class="fa fa-minus"></i> No Response
                                            </div>
                                        @endif
                                    </a>
                                </td>
                                <td>
                                    @if($checkin->replies()->count())
                                        <span class="block mt-px float-right border border-blue-400 text-blue-400 text-sm px-2 py-0 rounded-sm">
                                            {{ $checkin->replies()->count() }} {{ \Illuminate\Support\Str::of('Reply')->plural($checkin->replies()->count()) }}
                                        </span>
                                    @endif
                                    <a class="block text-black" href="{{ route('app.crises.checkins.view', [$crisis, $checkin]) }}">
                                        {{ $checkin->user->name }}
                                    </a>
                                </td>
                                <td>
                                    <a class="block text-black" href="{{ route('app.crises.checkins.view', [$crisis, $checkin]) }}">
                                        {{ $checkin->notes }}
                                        &nbsp;
                                    </a>
                                </td>
                                <td>
                                    <svg class="h-6 w-6 float-right mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </td>
                            </tr>
                        @empty
                            <div class="bg-white p-6 text-center">
                                <span class="badge badge-warning badge-large mt-5 font-semibold">No checkins yet.<br>Check back soon!</span>
                            </div>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

@endsection
