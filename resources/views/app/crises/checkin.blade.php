@extends('app._layout._app')

@section('title', 'Check-in Event Details')

@section('content')

    <div class="">
        <a href="{{ route('app.crises.view', $crisis) }}" class="inline-flex">
            <svg class="w-5 h-5 mt-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Back to Check-ins
        </a>
    </div>

    <div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-semibold pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                Check-in Details
            </h1>
        </div>
    </div>

    <div class="max-w-lg mx-auto sm:max-w-none pt-2 pb-4">
        <div class="p-4 bg-white rounded-sm shadow-sm overflow-hidden">
            <div class="w-full">
                <div class="flex">
                    <div class="">
                        <h2 class="text-3xl font-medium mb-1">
                            {{ $checkin?->family->name }} Family
                        </h2>
                    </div>
                </div>
                <div class="mb-4">
                    @if($checkin?->type == 'ok')
                        <div class="inline-flex bg-green-500 text-white text-lg mt-1 px-3 py-2 rounded-sm">
                            <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                            </svg>
                            OK
                        </div>
                    @elseif($checkin?->type == 'help')
                        <div class="inline-flex whitespace-nowrap bg-yellow-500 text-lg text-white mt-1 px-3 py-2 rounded-sm">
                            <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"/>
                            </svg>
                            Help
                        </div>
                    @elseif($checkin?->type == 'urgent_help')
                        <div class="inline-flex whitespace-nowrap bg-red-500 text-lg text-white mt-1 px-3 py-2 rounded-sm">
                            <svg class="h-6 w-6 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                            </svg>
                            Urgent
                        </div>
                    @elseif($checkin?->type == 'not_responded')
                        <div class="text-gray-400 whitespace-nowrap py-1">
                            <i class="fa fa-minus"></i> Unresponded
                        </div>
                    @endif
                </div>

                <table class="w-full border border-gray-200">
                    <tbody class="border border-gray-200">
                    <tr>
                        <td class="text-right text-nowrap p-4 border-r border-b border-gray-200" width="17%">Response Notes:</td>
                        <td class="p-4 border-b border-gray-200">
                            {{ $checkin?->notes }}
                        </td>
                    </tr>
                    <tr>
                        <td class="text-right text-nowrap p-4 border-r border-b border-gray-200">Responded At:</td>
                        <td class="p-4 border-b border-gray-200">
                            {{ $checkin?->responded_at ? $checkin?->responded_at->timezone(Auth::user()->account->timezone)->format('M d, Y @ g:ia') : null }}
                        </td>
                    </tr>
                    <tr>
                        <td class="text-right text-nowrap p-4 border-r border-gray-200 align-top">Replies:</td>
                        <td class="p-4">
                            @forelse($checkin?->replies as $reply)
                                <div class="p-2 border border-gray-300 bg-gray-50 rounded-sm mb-3">
                                    <div class="text-sm flex justify-between">
                                        <div>
                                            <strong>{{ $reply->user->name }}</strong> -
                                            {{ $reply->created_at->timezone(Auth::user()->account->timezone)->format('M d, Y @ g:ia') }}
                                        </div>
                                        <div class="border border-blue-300 bg-blue-50 px-2 py-1 block rounded-sm">
                                            {{ $reply->getTypeName() }}
                                        </div>
                                    </div>
                                    <div class="text-base">
                                        {{ $reply->notes }}
                                    </div>
                                </div>
                            @empty
                                <span class="px-2 py-1 bg-yellow-100 rounded-sm">No replies.</span>
                            @endforelse
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div class="mt-6">
                    <div>
                        Can you help? Respond here to let others know what you will do or have done.
                    </div>
                    <form class="mb-4" action="{{ route('app.crises.checkins.reply.store', [$crisis, $checkin]) }}" method="POST">
                        @method('post')
                        @csrf()
                        <input type="hidden" name="crisis_checkin_id" value="{{ $checkin?->id }}"/>
                        <select id="type" name="type" class="form-select transition duration-150 ease-in-out my-1">
                            @foreach(\App\Crises\CheckinReply::$types as $key => $value)
                                <option value="{{ $key }}">{{ $value }}</option>
                            @endforeach
                        </select>
                        <textarea name="notes" class="form-select my-1 placeholder-gray-400" placeholder="notes..."></textarea>
                        <button type="submit" class="px-4 py-2 bg-blue-500 rounded-sm text-white flex">
                            <svg class="h-4 w-4 mt-1 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"/>
                            </svg>
                            Reply
                        </button>
                    </form>
                    <div class="text-sm text-gray-400">
                        If you see others have responded, that doesn't mean the need has been met or more help is not needed.
                        <br>
                        Don't be afraid to reach out!
                    </div>

                </div>
            </div>
        </div>
    </div>

@endsection