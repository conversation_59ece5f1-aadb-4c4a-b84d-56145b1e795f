<div class="md:flex md:items-center md:justify-between lg:border-cool-gray-200">
    <div class="flex items-center">
        <h1 class="text-lg text-gray-500 text-cool-gray-900 items-center uppercase">
            Event Details
        </h1>
    </div>
    <div>
        <span onclick="closeModal()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </span>
    </div>
</div>

<div class="max-w-lg mx-auto sm:max-w-none py-4">
    <div class="bg-white rounded-sm overflow-hidden">
        <div class="w-full">
            <div class="flex">
                <div class="flex-initial block rounded-t-xl overflow-hidden bg-white text-center w-24">
                    <div class="text-white py-1 font-bold" style="background-color: #{{ $event->calendar->background_color }}; color: #{{ $event->calendar->font_color }}">
                        {{ $event->start_at->setTimezone(Auth::user()->account->timezone)->format('M') }}
                    </div>
                    <div class="py-1 border-l-2 border-r-2 border-b-2 rounded-b-xl">
                        <span class="text-5xl font-light">
                            {{ $event->start_at->setTimezone(Auth::user()->account->timezone)->format('j') }}
                        </span>
                    </div>
                </div>
                <div class="ml-4">
                    <h2 class="text-3xl font-medium mb-2">
                        {{ $event->title }}
                    </h2>
                    <h5 class="text-base text-gray-600">
                        {{ $event->start_at->setTimezone(Auth::user()->account->timezone)->format('l, F, j Y') }}
                        <br>
                        @if(!$event->is_all_day)
                            from {{ $event->start_at->setTimezone(Auth::user()->account->timezone)->format('g:ia') }} - {{ $event->end_at->setTimezone(Auth::user()->account->timezone)->format('g:ia') }}
                        @else
                            All Day
                        @endif
                    </h5>
                </div>
            </div>

            <div class="flex flex-col mt-6 border border-gray-300 rounded-lg">
                <div class="flex flex-row">
                    <div class="w-28 sm:w-32 p-3 text-right border-r border-gray-300">Calendar:</div>
                    <div class="flex">
                        <div class="px-2 py-0.5 rounded-sm my-auto ml-4" style="{{ $event->calendar->getBackgroundColorForCss() . $event->calendar->getFontColorForCss()  }}">
                            {{ $event->calendar->name }}
                        </div>
                    </div>
                </div>
                <div class="flex flex-row border-t border-gray-300">
                    <div class="w-28 sm:w-32 p-3 text-right border-r border-gray-300">Location:</div>
                    <div class="flex">
                        @if($event->location)
                            <div class="my-auto ml-4">
                                {{ $event->location }}
                            </div>
                        @else
                            <div class="px-2 py-1 bg-yellow-100 rounded-sm my-auto ml-4">No location provided.</div>
                        @endif
                    </div>
                </div>
                @if($event->overview)
                    <div class="flex flex-row border-t border-gray-300">
                        <div class="w-28 sm:w-32 p-3 text-right border-r border-gray-300">Overview:</div>
                        <div class="flex">
                            @if($event->overview)
                                <div class="my-auto ml-4">
                                    {{ $event->overview }}
                                </div>
                            @else
                                <div class="px-2 py-1 bg-yellow-100 rounded-sm my-auto ml-4">No overview available.</div>
                            @endif
                        </div>
                    </div>
                @endif
                <div class="flex flex-row border-t border-gray-300">
                    <div class="w-28 sm:w-32 p-3 text-right border-r border-gray-300">Details:</div>
                    <div class="flex">
                        @if($event->description)
                            <div class="my-auto ml-4">
                                {{ $event->description }}
                            </div>
                        @else
                            <div class="px-2 py-1 bg-yellow-100 rounded-sm my-auto ml-4">No details available.</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
