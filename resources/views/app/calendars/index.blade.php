@extends('app._layout._app')

@section('title', 'Calendar')

@section('content')

    <style>
        .fc-view-harness {
            background-color: #ffffff;
        }
        .fc .fc-daygrid-day-frame {
            min-height: 140px;
        }
    </style>

    <script src='/static/global/js/moment.js'></script>

    <div class="print:hidden md:flex md:items-center md:justify-between lg:border-cool-gray-200">
        <div class="flex items-center">
            <h1 class="text-4xl font-medium pb-1 text-cool-gray-900 sm:leading-9 sm:truncate group flex items-center">
                <svg class="text-center w-8 h-8 mr-3 text-gray-900" aria-hidden="true" focusable="false" data-prefix="far" data-icon="calendar-alt" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                    <path fill="currentColor" d="M148 288h-40c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12zm108-12v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 96v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm-96 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm192 0v-40c0-6.6-5.4-12-12-12h-40c-6.6 0-12 5.4-12 12v40c0 6.6 5.4 12 12 12h40c6.6 0 12-5.4 12-12zm96-260v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V112c0-26.5 21.5-48 48-48h48V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h128V12c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v52h48c26.5 0 48 21.5 48 48zm-48 346V160H48v298c0 3.3 2.7 6 6 6h340c3.3 0 6-2.7 6-6z"></path>
                </svg>
                Calendar
            </h1>
        </div>
        <div>
            <div class="relative inline-block text-left" x-data="{ openFeedsWindow: false, copied: false }" x-on:click.outside="openFeedsWindow = false">
                <div>
                    <button x-on:click="openFeedsWindow = !openFeedsWindow" type="button" class="inline-flex w-full justify-center rounded-sm border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-100" id="menu-button" aria-expanded="true" aria-haspopup="true">
                        Calendar Feeds
                        <x-heroicon-m-chevron-down class="-mr-1 ml-1 my-auto h-5 w-5"/>
                    </button>
                </div>

                <div x-show="openFeedsWindow"
                     class="block sm:absolute right-0 z-10 mt-2 w-full md:w-96 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden"
                     role="menu" aria-orientation="vertical"
                     aria-labelledby="menu-button" tabindex="-1">
                    <div class="px-4 py-3" role="none">
                        <p class="text-base font-medium" role="none">All Calendar Events <span class="text-gray-300 text-xs">(viewable to you)</span></p>
                        <div class="mt-1 flex rounded-md">
                            <div class="relative flex grow items-stretch focus-within:z-10">
                                <input type="text"
                                       class="block w-full rounded-none rounded-l-md text-gray-500 border-gray-300 pl-2 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                       value="{{ (new \App\Calendars\Services\CalendarFeed())->forUser(auth()->user())->getFeedUrl() }}"
                                       onclick="this.select()"
                                       readonly/>
                            </div>
                            <button type="button"
                                    onclick="navigator.clipboard.writeText('{{ (new \App\Calendars\Services\CalendarFeed())->forUser(auth()->user())->getFeedUrl() }}')" x-on:click="copied = true; setTimeout(() => { copied = false }, 3000);"
                                    class="relative -ml-px inline-flex items-center border border-gray-300 bg-gray-50 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:border-blue-500 focus:outline-hidden focus:ring-1 focus:ring-blue-500">
                                <x-heroicon-m-clipboard-document class="h-5 w-5 text-gray-400" x-show="!copied"/>
                                <x-heroicon-m-clipboard-document-check class="h-5 w-5 text-gray-400" x-show="copied"/>
                            </button>
                            <a href="{{ (new \App\Calendars\Services\CalendarFeed())->forUser(auth()->user())->getFeedUrl() }}"
                               class="relative -ml-px inline-flex items-center rounded-r-md border border-gray-300 bg-gray-50 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:border-blue-500 focus:outline-hidden focus:ring-1 focus:ring-blue-500">
                                <x-heroicon-m-arrow-top-right-on-square class="h-5 w-5 text-gray-400"/>
                            </a>
                        </div>
                        <p class="text-xs text-gray-400 mt-2">
                            <span class="text-red-600 font-semibold">Do not share this link.</span>
                        </p>
                        <p class="text-xs text-gray-400 mt-2">
                            This link is personalized to <strong>you</strong> and receives <strong>all</strong> calendar data accessible to you in Lightpost.
                        </p>
                    </div>
                    @foreach(\App\Calendars\Calendar::visibleTo(auth()->user())->get() as $calendar)
                        <div class="px-4 py-3" role="none">
                            <p class="text-base font-medium" role="none">{{ $calendar->name }}</p>
                            <div class="mt-1 flex rounded-md">
                                <div class="relative flex grow items-stretch focus-within:z-10">
                                    <input type="text"
                                           class="block w-full rounded-none rounded-l-md text-gray-500 border-gray-300 pl-2 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                                           value="{{ (new \App\Calendars\Services\CalendarFeed())->forUser(auth()->user())->forCalendar($calendar)->getFeedUrl() }}"
                                           onclick="this.select()"
                                           readonly/>
                                </div>
                                <button type="button"
                                        onclick="navigator.clipboard.writeText('{{ (new \App\Calendars\Services\CalendarFeed())->forUser(auth()->user())->forCalendar($calendar)->getFeedUrl() }}')" x-on:click="copied = true; setTimeout(() => { copied = false }, 3000);"
                                        class="relative -ml-px inline-flex items-center border border-gray-300 bg-gray-50 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:border-blue-500 focus:outline-hidden focus:ring-1 focus:ring-blue-500">
                                    <x-heroicon-m-clipboard-document class="h-5 w-5 text-gray-400" x-show="!copied"/>
                                    <x-heroicon-m-clipboard-document-check class="h-5 w-5 text-gray-400" x-show="copied"/>
                                </button>
                                <a href="{{ (new \App\Calendars\Services\CalendarFeed())->forUser(auth()->user())->forCalendar($calendar)->getFeedUrl() }}"
                                   class="relative -ml-px inline-flex items-center rounded-r-md border border-gray-300 bg-gray-50 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:border-blue-500 focus:outline-hidden focus:ring-1 focus:ring-blue-500">
                                    <x-heroicon-m-arrow-top-right-on-square class="h-5 w-5 text-gray-400"/>
                                </a>
                            </div>
                            <p class="text-xs text-gray-400 mt-2">
                                <span class="text-red-600 font-semibold">Do not share this link!</span>
                            </p>
                        </div>
                    @endforeach
                </div>
            </div>

        </div>
    </div>

    <div class="mt-6">
        <div id="calendar"></div>
    </div>

@endsection

@push('scripts')
    <script src='/static/global/js/fullcalendar.js'></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {

            var calendarEl = document.getElementById('calendar');

            var calendar = new FullCalendar.Calendar(calendarEl, {
                height: 'auto',
                schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,listMonth'
                },
                timeZone: 'local',
                initialView: 'dayGridMonth',
                refetchResourcesOnNavigate: true,
                eventClick: function (info) {
                    info.jsEvent.preventDefault(); // don't let the browser navigate
                    {{--console.log('{{ url('calendar/event') }}/' + info.event.id);--}}
                    openModal('{{ url('calendar/event/occurrence') }}/' + info.event.id, true);
                    {{--openModal('{{ url('calendar/event') }}/' + info.event.id);--}}
                },
                eventRender: function (event, element) {
                    // When we render an event on the calendar, check to see if we selected this calendar to be shown.
                    // Hide it if not, and put a 40ms delay because we need to wait for rendering to complete.
                },
                eventSources: [
                        @foreach($all_calendars as $calendar)
                    {
                        id: {{ $calendar->id }},
                        url: '/calendars/{{ $calendar->ulid }}',
                        className: 'cal_id_{{ $calendar->id }}{{ $calendar->user_group_id ? ' group_cal' : null }}',
                        method: 'GET',
                        failure: function () {
                            console.log('There was an error while fetching events!');
                        },
                        color: '#{{ $calendar->getBackgroundColor() }}',   // an option!
                        textColor: '#{{ $calendar->getFontColor() }}', // an option!
                        visible: false
                    },
                    @endforeach
                ],
                eventDidMount: function(info) {
                    if (info.event.extendedProps.isRecurring) {
                        info.el.insertAdjacentHTML('beforeend', '<span class="ml-1 mr-0.5"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 0 0-3.7-3.7 48.678 48.678 0 0 0-7.324 0 4.006 4.006 0 0 0-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 0 0 3.7 3.7 48.656 48.656 0 0 0 7.324 0 4.006 4.006 0 0 0 3.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3-3 3" /></svg></span>');
                    }
                }
            });

            calendar.render();
        });
    </script>
@endpush