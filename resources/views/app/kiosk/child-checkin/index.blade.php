@extends('app.kiosk._layout._app')

@section('title', 'Child Check-in')

@section('content')

    <!-- 3 column wrapper -->
    <div class="grow w-full max-w-(--breakpoint-2xl) mx-auto xl:px-8 lg:flex">

        <!-- Left sidebar & main wrapper -->
        <div class="flex-1 min-w-0 bg-white xl:flex">
            <!-- Account profile -->
            <div class="xl:shrink-0 xl:w-64 xl:border-r xl:border-gray-200 bg-white">
                <div class="pl-4 pr-6 py-6 sm:pl-6 lg:pl-8 xl:pl-0">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 space-y-8">
                            <div class="space-y-8 sm:space-y-0 sm:flex sm:justify-between sm:items-center xl:block xl:space-y-8">
                                <!-- Profile -->
                                <div class="flex items-center space-x-3">
                                    <div class="space-y-1" x-data="{
                                        time: new Date(),
                                        init() {
                                          setInterval(() => {
                                            this.time = new Date();
                                          }, 2000);
                                        },
                                        getTime() {
                                            return this.time.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
                                        },
                                        getDate() {
                                            return this.time.toLocaleString('en-US', { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' });
                                        }
                                    }">
                                        <div class="text-5xl font-medium text-gray-900" x-text="getTime()"></div>
                                        <div class="text-lg font-light text-gray-900" x-text="getDate()"></div>
                                    </div>
                                </div>
                                <!-- Action buttons -->
                                <div class="flex flex-col sm:flex-row xl:flex-col">
                                    <button type="button" onclick="Alpine.store('NewCheckinModal').toggle()" class="inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 xl:w-full">
                                        <svg class="mr-1 w-5 h-5" style="transform: rotate(180deg);" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                                        </svg>
                                        New Check-in
                                    </button>
                                    <button type="button" class="hidden mt-3 inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 xl:ml-0 xl:mt-3 xl:w-full">
                                        Checkout
                                        <svg class="ml-1 w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="py-20"></div>
                            <!-- Meta info -->
                            <div x-data="{ selectedPrinter: $store.selectedLabelPrinter, printers: $store.LabelPrinters }" class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-8 xl:flex-col xl:space-x-0 xl:space-y-2"
                            >
                                <div class="flex items-center space-x-2">
                                    <div class="mb-1">
                                        <select x-on:change="checkLabelPrinter()"
                                                id="printer_id"
                                                name="printer_id"
                                                class="block w-full py-1 text-gray-400 appearance-none text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-sm">
                                            <template x-for="printer in $store.LabelPrinters" :key="printer.name">
                                                <option :value="printer.name" x-text="printer?.name"></option>
                                            </template>
                                            <template x-if="$store.LabelPrinters?.length == 0">
                                                <option>No printer detected.</option>
                                            </template>
                                        </select>
                                    </div>
                                </div>
                                <div class="hidden flex items-center">
                                    <div x-show="!$store.LabelPrinterAvailable" class="flex">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span class="ml-1 text-sm text-gray-400 font-medium">Label Printer NOT Connected</span>
                                    </div>
                                    <div x-show="$store.LabelPrinterAvailable" class="flex">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span class="ml-1 text-sm text-gray-400 font-medium">Label Printer Connected</span>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div x-show="!$store.LabelServiceAvailable" class="flex">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span class="ml-1 text-sm text-gray-400 font-medium">Printer Service Unavailable</span>
                                    </div>
                                    <div x-show="$store.LabelServiceAvailable" class="flex">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span class="ml-1 text-sm text-gray-400 font-medium">Printer Service Available</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Checkins List -->
            @livewire('app.kiosk.child-checkin.checkins')

        </div>

        <!-- Activity feed -->
        @livewire('app.kiosk.child-checkin.activity')

        @livewire('app.kiosk.child-checkin.new-checkin')

    </div>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.store('LabelPrinters', []);
            Alpine.store('selectedLabelPrinter', {});
            Alpine.store('LabelPrinterAvailable', false);
            Alpine.store('LabelServiceAvailable', false);
            Alpine.store('ScannerCooldown', false);
            Alpine.store('scanner_input', '');
            {{-- Our label service is used to print labels to a Dymo label printer. --}}
            Alpine.store('LabelService', {
                printChildLabel(child_name, parent_info, allergies, special_needs, barcode_data) {
                    console.log('Printing child label...');

                    {{-- Don't try to print if things aren't available!  Labels will queue up and print when the printer is available again. --}}
                    // Checking the printer status is broken on Windows for now.
                    // if (!Alpine.store('LabelPrinterAvailable') || !Alpine.store('LabelServiceAvailable')) {
                    if (!Alpine.store('LabelServiceAvailable')) {
                        alert('Oops!  The label service is not available.');
                    } else {
                        var label = dymo.label.framework.openLabelXml(generateChildLabel(child_name, parent_info, allergies, special_needs, barcode_data));
                        label.print(Alpine.store('selectedLabelPrinter')?.name);
                    }
                },
                printParentLabel(child_name, parent_info, barcode_data) {
                    console.log('Printing parent label...');

                    {{-- Don't try to print if things aren't available!  Labels will queue up and print when the printer is available again. --}}
                    // Checking the printer status is broken on Windows for now.
                    // if (!Alpine.store('LabelPrinterAvailable') || !Alpine.store('LabelServiceAvailable')) {
                    if (!Alpine.store('LabelServiceAvailable')) {
                        alert('Oops!  The label service is not available.');
                    } else {
                        var label = dymo.label.framework.openLabelXml(generateParentLabel(child_name, parent_info, barcode_data));
                        label.print(Alpine.store('selectedLabelPrinter')?.name);
                    }
                }
            });

            {{--
                Component for providing a method to get "X minutes ago" given a timestamp
                <div x-data="TimeAgo(__TIMESTAMP__)">
                <span x-text="getTimeAgo()">
             --}}
            Alpine.data('TimeAgo', (createdAt) => ({
                thisDate: new Date(createdAt),
                time: timeAgo(new Date(createdAt)),
                init() {
                    setInterval(() => {
                        this.time = timeAgo(this.thisDate);
                    }, 4000);
                },
                getTimeAgo() {
                    return this.time;
                },
            }));


            {{-- We put this in alpine:init because we use Alpine components here that we don't want to load before Alpine is ready. --}}
            {{-- Start-up our Dymo framework so we can communicate with the printer and check that things will work. --}}
            dymo.label.framework.init(function () {
                try {
                    var result = dymo.label.framework.checkEnvironment();
                    console.log("isBrowserSupported: " + result.isBrowserSupported);
                    console.log("isFrameworkInstalled: " + result.isFrameworkInstalled);
                    if (dymo.label.framework.init) {
                        console.log("isWebServicePresent: " + result.isWebServicePresent);
                    }
                    console.log("errorDetails: " + result.errorDetails);
                } catch (e) {
                    alert(e.message || e);
                }

                if (result.isBrowserSupported && result.isFrameworkInstalled && result.isWebServicePresent) {
                    Alpine.store('LabelServiceAvailable', true);
                }

                {{-- Put the list of available printers in our global store. --}}
                Alpine.store('LabelPrinters', dymo.label.framework.getPrinters());

                {{-- Record if our printer and service are available. --}}
                if (Alpine.store('LabelPrinters')[0]) {
                    Alpine.store('LabelPrinterAvailable', Alpine.store('LabelPrinters')[0].isConnected);
                    Alpine.store('selectedLabelPrinter', Alpine.store('LabelPrinters')[0]);
                }

                // console.log('Printers: ', dymo.label.framework.getPrinters());
            }); //Initialize DYMO Label Framework

        });

        function checkLabelPrinter() {
            console.log('checked');
            // @TODO
        }
    </script>

    <script>
        {{-- This is a global event we can call with Livewire to refresh our Activity log when the checkins have changed.. --}}
        window.addEventListener('checkins-refreshed', event => {
            console.log('refreshed');
            setTimeout(() => {
                Livewire.dispatch('refreshCheckins');
                Livewire.dispatch('refreshActivity');
            }, 450);
        });
        window.addEventListener('close-new-checkin-modal', event => {
            Alpine.store('NewCheckinModal').close();
        });
    </script>

    {{-- This is our listener code for barcode scans. --}}
    <script>
        {{-- debounce function for clearing input after X seconds. --}}
        var debounceScannerInput = _.debounce(function () {
            Alpine.store('scanner_input', '');
        }, 1000);


        // Monitor for a scanner input somewhere.
        window.addEventListener("keydown", function (event) {
            if (event.defaultPrevented || Alpine.store('ScannerCooldown')) {
                return; // Do nothing if the event was already processed
            }

            {{-- Don't do anything if we're trying to do input in an <input> field (like creating a new visitor). --}}
                    {{-- EXCEPT, if we're on the "search" bar at the top. --}}
            if (document.activeElement.nodeName === 'INPUT' && document.activeElement.id !== 'qr_scan_input') {
                return; // Do nothing if we're trying to do input.
            }

            {{-- A list of input we EXPECT, let's not record anything else. --}}
            var valid_keys = [
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                ':', ';', '|', '@', '-'
            ];

            {{-- Signal the debounce timer to reset for this specific key press. --}}
            debounceScannerInput();

            {{-- If we have found this key in our list of valid_keys, add it to our scanner_input string --}}
            if (valid_keys.indexOf(event.key) !== -1) {
                Alpine.store('scanner_input', Alpine.store('scanner_input') + event.key);

                // Cancel the default action to avoid it being handled twice
                event.preventDefault();
            } else if (event.key == 'Enter' && Alpine.store('scanner_input') !== '') { {{-- If we've hit the Enter key && we have valid scanner_input, use Livewire to do a checkin attempt. --}}
                {{-- console.log('FOUND COMPLETED SCAN!', Alpine.store('scanner_input')); --}}

                // Checkin
                Livewire.dispatch('checkinViaQr', { qr_string: Alpine.store('scanner_input') });

                // Clear our input.
                Alpine.store('scanner_input', '');

                {{-- If we sent for a checkin/out, let's wait so we don't get accidental double input --}}
                Alpine.store('ScannerCooldown', true);

                {{-- If we sent for a checkin/out, let's wait so we don't get accidental double input --}}
                setTimeout(() => {
                    Alpine.store('ScannerCooldown', false);
                }, 2500);

                // Cancel the default action to avoid it being handled twice
                event.preventDefault();
            } else {
                {{-- console.log('char not valid!'); --}}
            }
        }, true);
    </script>

    <script>
        {{-- Generates the XML for the Child label. --}}
        function generateChildLabel(child_name, parent_info, allergies, special_needs, barcode_data) {
            return `<?= '<?xml version = "1.0" encoding = "utf-8"?>' ?>
            <DieCutLabel Version="8.0" Units="twips" MediaType="Default">
              <PaperOrientation>Portrait</PaperOrientation>
              <Id>Storage</Id>
              <PaperName>30258 Diskette</PaperName>
              <DrawCommands>
                <RoundRectangle X="0" Y="0" Width="3060" Height="3960" Rx="270" Ry="270"/>
              </DrawCommands>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Center</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>` + child_name + `</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="11" Bold="True" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="262.9123" Y="316.7999" Width="2701.24" Height="332.3032"/>
              </ObjectInfo>
              <ObjectInfo>
                <BarcodeObject>
                  <Name>BARCODE</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="255" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <Text>` + barcode_data + `</Text>
                  <Type>QRCode</Type>
                  <Size>Large</Size>
                  <TextPosition>None</TextPosition>
                  <TextFont Family="Helvetica" Size="10" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                  <CheckSumFont Family="Helvetica" Size="10" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                  <TextEmbedding>None</TextEmbedding>
                  <ECLevel>0</ECLevel>
                  <HorizontalAlignment>Right</HorizontalAlignment>
                  <QuietZonesPadding Left="0" Right="0" Top="0" Bottom="0"/>
                </BarcodeObject>
                <Bounds X="1982.166" Y="569.4725" Width="1018.484" Height="1126.796"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>PARENTS_INFO_TEXT</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Left</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>` + parent_info + `</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="9" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="168.1745" Y="677.4861" Width="1941.264" Height="1024.661"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT_2</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Left</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>Allergies:</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="7" Bold="True" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="168.1745" Y="1623.259" Width="854.5078" Height="332.3032"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>SPECIAL_NEEDS_TEXT</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Left</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>` + special_needs + `</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="9" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="163.9973" Y="2901.285" Width="2840.803" Height="972.3145"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT_2_1</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Left</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>Special Needs:</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="7" Bold="True" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="164.0519" Y="2728.218" Width="1187.892" Height="332.3032"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>ALERGIES_TEXT</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Left</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>` + allergies + `</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="9" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="172.297" Y="1789.905" Width="2832.503" Height="854.5612"/>
              </ObjectInfo>
            </DieCutLabel>`;
        }

        {{-- Generates the XML for the parent label. --}}
        function generateParentLabel(child_name, parent_info, barcode_data) {
            return `<?= '<?xml version = "1.0" encoding = "utf-8"?>' ?>
            <DieCutLabel Version="8.0" Units="twips" MediaType="Default">
              <PaperOrientation>Landscape</PaperOrientation>
              <Id>Storage</Id>
              <PaperName>30258 Diskette</PaperName>
              <DrawCommands>
                <RoundRectangle X="0" Y="0" Width="3060" Height="3960" Rx="270" Ry="270"/>
              </DrawCommands>
              <ObjectInfo>
                <BarcodeObject>
                  <Name>BARCODE</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="255" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <Text>` + barcode_data + `</Text>
                  <Type>QRCode</Type>
                  <Size>Large</Size>
                  <TextPosition>None</TextPosition>
                  <TextFont Family="Helvetica" Size="10" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                  <CheckSumFont Family="Helvetica" Size="10" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                  <TextEmbedding>None</TextEmbedding>
                  <ECLevel>0</ECLevel>
                  <HorizontalAlignment>Right</HorizontalAlignment>
                  <QuietZonesPadding Left="0" Right="0" Top="0" Bottom="0"/>
                </BarcodeObject>
                <Bounds X="2522.381" Y="1541.408" Width="1126.796" Height="1110.028"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT_1</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Left</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>` + parent_info + `</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="10" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="410.4456" Y="1244.281" Width="2491.103" Height="1465.056"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT_2</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Left</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>Parents:</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="7" Bold="True" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="316.7999" Y="1039.98" Width="683.2482" Height="332.3032"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT_4</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Center</HorizontalAlignment>
                  <VerticalAlignment>Bottom</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>{{ auth()->user()?->account->name }}</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="7" Bold="True" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="316.7999" Y="2524" Width="3295.356" Height="332.3032"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT_3</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Center</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>Child Checkin Card</String>
                      <Attributes>
                        <Font Family="Noto Sans" Size="13" Bold="False" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="706.787" Y="57.59979" Width="2540" Height="332.3032"/>
              </ObjectInfo>
              <ObjectInfo>
                <TextObject>
                  <Name>TEXT</Name>
                  <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                  <BackColor Alpha="0" Red="255" Green="255" Blue="255"/>
                  <LinkedObjectName></LinkedObjectName>
                  <Rotation>Rotation0</Rotation>
                  <IsMirrored>False</IsMirrored>
                  <IsVariable>False</IsVariable>
                  <HorizontalAlignment>Center</HorizontalAlignment>
                  <VerticalAlignment>Top</VerticalAlignment>
                  <TextFitMode>None</TextFitMode>
                  <UseFullFontHeight>True</UseFullFontHeight>
                  <Verticalized>False</Verticalized>
                  <StyledText>
                    <Element>
                      <String>` + child_name + `</String>
                      <Attributes>
                        <Font Family="Helvetica" Size="11" Bold="True" Italic="False" Underline="False" Strikeout="False"/>
                        <ForeColor Alpha="255" Red="0" Green="0" Blue="0"/>
                      </Attributes>
                    </Element>
                  </StyledText>
                </TextObject>
                <Bounds X="316.7999" Y="570.6309" Width="3340.404" Height="368.6421"/>
              </ObjectInfo>
            </DieCutLabel>`;
        }
    </script>

@endsection