<form method="post" action="">
    {{ csrf_field() }}
    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
            </svg>
        </div>
        <div class="grow mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

            <h3 class="text-xl leading-6 font-medium text-gray-900" id="modal-title">
                New Check-in
            </h3>
        </div>
        <div class="">
            <button type="submit" class="inline-flex justify-center w-full rounded-sm border border-transparent shadow-xs px-6 py-2 border-indigo-600 text-base font-medium text-indigo-600 hover:bg-blue-700 hover:text-white focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
                Check-in
                <svg class="ml-1 w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
            </button>
        </div>
    </div>
    <div>

    </div>
    <div>
        <div class="mt-4">
            <select name="user_email_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-sm">
            </select>
        </div>
    </div>

</form>