<form method="post" action="">
    {{ csrf_field() }}
    <div class="sm:flex sm:items-start align-middle">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"/>
            </svg>
        </div>
        <div class="grow mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left align-middle">
            <h3 class="text-2xl leading-6 font-medium text-gray-900" id="modal-title">
                {{ $child->name }}
            </h3>
            @if($child?->birthdate)
                <span class="text-gray-500">
                {{ $child?->birthdate?->diffForHumans(['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]) }} old
            </span>
            @endif
        </div>
        <div class="text-gray-500">

        </div>
    </div>
    @if(!$checkin->checkout_at)
        <div>
            <div class="flex space-x-4 mt-4">
                <div class="flex-1">
                    <select id="view_checkin_checkout_by_user_id" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 rounded-sm">
                        <option value="">Select a pick-up person (not required)</option>
                        @foreach($child->parents as $parent)
                            <option value="{{ $parent->id }}">{{ $parent->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex-none">
                    <button type="button" onclick="Livewire.dispatch('checkoutViaLivewire', { child_checkin: {{ $checkin->id }}, checkout_by_user_id: document.getElementById('view_checkin_checkout_by_user_id').value}); closeModal();" class="inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 xl:w-auto">
                        Checkout
                        <svg class="ml-1 w-5 h-5" style="transform: rotate(180deg);" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    @endif

    <hr class="my-4">

    <div class="mt-4 space-y-1">
        <h4 class="font-semibold font-lg">
            Parents:
        </h4>
        @foreach($child->parents as $parent)
            <div class="ml-4">
                <span class="text-xl">{{ $parent->name }}</span>
                <br>
                {{ \App\Users\Phone::format($parent->getBestPhone(false)?->number, '-') }}
            </div>
        @endforeach
    </div>

    <hr class="my-4">

    <div>
        <h4 class="font-semibold font-lg">
            Allergies:
        </h4>
        <div class="ml-4">
            {{ $child->allergies }}
        </div>
    </div>

    <hr class="my-4">

    <div>
        <h4 class="font-semibold font-lg">
            Special Needs:
        </h4>
        <div class="ml-4">
            {{ $child->special_needs }}
        </div>
    </div>

    <hr class="my-4">

    <div class="flow-root">
        <h4 class="font-semibold font-lg">
            Timeline:
        </h4>
        <ul role="list" class="-mb-8 mt-2 ml-4">
            @foreach($checkin->activity()->orderBy('created_at', 'DESC')->get() as $activity)
                <li>
                    <div class="relative pb-8">
                        @if(!$loop->last)
                            <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                        @endif
                        <div class="relative flex items-start space-x-3">
                            <div>
                                <div class="relative px-1">
                                    <div class="h-8 w-8 bg-gray-100 rounded-full ring-8 ring-white flex items-center justify-center">
                                        @if($activity->type == 'note')
                                            <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                            </svg>
                                        @elseif($activity->type == 'checkin')
                                            <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                                            </svg>
                                        @elseif($activity->type == 'checkout')
                                            <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                            </svg>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="min-w-0 flex-1 py-0">
                                <div class="leading-8 text-gray-500">
                                    <span class="mr-0.5">
                                        <span class="font-lg font-medium text-gray-900">
                                            {{ $activity->getTypeAsTitle() }}
                                        </span>
                                    </span>
                                    <span class="text-sm whitespace-nowrap" id="{{ uniqid() }}" x-data="TimeAgo('{{ $activity->created_at->setTimezone(auth()->user()->account->timezone)->format('c') }}')">
                                        <span x-text="getTimeAgo()"></span>
                                    </span>
                                </div>
                                @if($activity->type == 'checkin' && $checkin->checkin_by_user_id)
                                    <div class="mt-2 text-sm text-gray-700">
                                        <p>
                                            By {{ \App\Users\User::find($checkin->checkin_by_user_id)?->name }}
                                        </p>
                                    </div>
                                @endif
                                @if($activity->type == 'checkout' && $checkin->checkout_by_user_id)
                                    <div class="mt-2 text-sm text-gray-700">
                                        <p>
                                            By {{ \App\Users\User::find($checkin->checkout_by_user_id)?->name }}
                                        </p>
                                    </div>
                                @endif
                                @if($activity->notes)
                                    <div class="mt-2 text-sm text-gray-700">
                                        <p>
                                            {{ $activity->notes }}
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </li>
            @endforeach
        </ul>
    </div>

</form>