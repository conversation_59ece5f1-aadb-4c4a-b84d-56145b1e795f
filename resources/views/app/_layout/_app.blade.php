<!doctype html>
<html lang="{{ app()->getLocale() }}" class="h-full bg-zinc-50">
<head>
    <title>@yield('title', isset($title) ? $title : config('app.name', 'Lightpost'))</title>

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <link rel="apple-touch-icon" href="/favicon/favicon-180x180.png"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-196x196.png" sizes="196x196"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-96x96.png" sizes="96x96"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-32x32.png" sizes="32x32"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-16x16.png" sizes="16x16"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-128.png" sizes="128x128"/>
    <meta name="application-name" content="Lightpost"/>
    <meta name="msapplication-TileColor" content="#FFFFFF"/>
    <meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png"/>
    <meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png"/>
    <meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png"/>
    <meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png"/>
    <meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png"/>
    <link rel="manifest" href="/favicon/manifest.json">

    <meta name="theme-color" content="#ffffff">

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="Description" content=""/>

    {{--    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">--}}
    <link href="{{ mix('static/app/css/tailwind.app.css') }}" rel="stylesheet">
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>

    @livewireStyles

    <script>
        window.Components = {}, window.Components.menu = function (e = {open: !1}) {
            return {
                init() {
                    this.items = Array.from(this.$el.querySelectorAll('[role="menuitem"]')), this.$watch("open", (() => {
                        this.open && (this.activeIndex = -1)
                    }))
                }, activeDescendant: null, activeIndex: null, items: null, open: e.open, focusButton() {
                    this.$refs.button.focus()
                }, onButtonClick() {
                    this.open = !this.open, this.open && this.$nextTick((() => {
                        this.$refs["menu-items"].focus()
                    }))
                }, onButtonEnter() {
                    this.open = !this.open, this.open && (this.activeIndex = 0, this.activeDescendant = this.items[this.activeIndex].id, this.$nextTick((() => {
                        this.$refs["menu-items"].focus()
                    })))
                }, onArrowUp() {
                    if (!this.open) return this.open = !0, this.activeIndex = this.items.length - 1, void (this.activeDescendant = this.items[this.activeIndex].id);
                    0 !== this.activeIndex && (this.activeIndex = -1 === this.activeIndex ? this.items.length - 1 : this.activeIndex - 1, this.activeDescendant = this.items[this.activeIndex].id)
                }, onArrowDown() {
                    if (!this.open) return this.open = !0, this.activeIndex = 0, void (this.activeDescendant = this.items[this.activeIndex].id);
                    this.activeIndex !== this.items.length - 1 && (this.activeIndex = this.activeIndex + 1, this.activeDescendant = this.items[this.activeIndex].id)
                }, onClickAway(e) {
                    if (this.open) {
                        const t = ["[contentEditable=true]", "[tabindex]", "a[href]", "area[href]", "button:not([disabled])", "iframe", "input:not([disabled])", "select:not([disabled])", "textarea:not([disabled])"].map((e => `${e}:not([tabindex='-1'])`)).join(",");
                        this.open = !1, e.target.closest(t) || this.focusButton()
                    }
                }
            }
        }, window.Components.popover = function ({open: e = !1, focus: t = !1} = {}) {
            const i = ["[contentEditable=true]", "[tabindex]", "a[href]", "area[href]", "button:not([disabled])", "iframe", "input:not([disabled])", "select:not([disabled])", "textarea:not([disabled])"].map((e => `${e}:not([tabindex='-1'])`)).join(",");
            return {
                __type: "popover", open: e, init() {
                    t && this.$watch("open", (e => {
                        e && this.$nextTick((() => {
                            !function (e) {
                                const t = Array.from(e.querySelectorAll(i));
                                !function e(i) {
                                    void 0 !== i && (i.focus({preventScroll: !0}), document.activeElement !== i && e(t[t.indexOf(i) + 1]))
                                }(t[0])
                            }(this.$refs.panel)
                        }))
                    }));
                    let e = i => {
                        if (!document.body.contains(this.$el)) return void window.removeEventListener("focus", e, !0);
                        let n = t ? this.$refs.panel : this.$el;
                        if (this.open && i.target instanceof Element && !n.contains(i.target)) {
                            let e = this.$el;
                            for (; e.parentNode;) if (e = e.parentNode, e.__x instanceof this.constructor) {
                                if ("popoverGroup" === e.__x.$data.__type) return;
                                if ("popover" === e.__x.$data.__type) break
                            }
                            this.open = !1
                        }
                    };
                    window.addEventListener("focus", e, !0)
                }, onEscape() {
                    this.open = !1, this.restoreEl && this.restoreEl.focus()
                }, onClosePopoverGroup(e) {
                    e.detail.contains(this.$el) && (this.open = !1)
                }, toggle(e) {
                    this.open = !this.open, this.open ? this.restoreEl = e.currentTarget : this.restoreEl && this.restoreEl.focus()
                }
            }
        };
    </script>

    <script src="https://js.pusher.com/7.0/pusher.min.js"></script>

    @if(config('app.env') == 'production')
        <script src="https://cdn.usefathom.com/script.js" data-site="MYDHNYBQ" defer></script>
    @endif
</head>

<body class="h-full">

<div class="min-h-full">
    <div class="overflow-y-auto">

        <div class="min-h-full">
            <!-- When the mobile menu is open, add `overflow-hidden` to the `body` element to prevent double scrollbars -->
            <header class="print:hidden lg:static lg:overflow-y-visible"
                    x-state:on="Menu open" x-state:off="Menu closed"
                    :class="{ 'fixed inset-0 z-40 overflow-y-auto': open }"
                    x-data="Components.popover({ open: false, focus: false })" x-init="init()" @keydown.escape="onEscape" @close-popover-group.window="onClosePopoverGroup">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="relative flex bg-white py-3 mt-4 border border-gray-200 rounded-md px-3 lg:gap-8">
                        <div class="flex md:left-0 md:inset-y-0 lg:static">
                            <div class="flex items-center">
                                <a href="/" class="mr-2">
                                    <img class="h-8 w-auto hidden sm:block ml-2" src="/img/lightpost-logo.svg"/>
                                    <img class="block h-8 w-auto sm:hidden" src="/img/lightpost-logo-icon.svg"/>
                                </a>
                            </div>
                        </div>
                        <div class="flex-1 px-0">
                            <div class="flex items-center">
                                <form action="{{ route('app.directory.search') }}" method="get" class="ml-auto" id="header-directory-search-form">
                                    <label for="search" class="sr-only">Search</label>
                                    <div class="relative">
                                        <div class="pointer-events-none absolute inset-y-0 left-0 pl-3 flex items-center">
                                            <svg class="h-5 w-5 text-gray-400" x-description="Heroicon name: solid/search" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <input id="search" name="value" value="{{ request('value') }}"
                                               class="block w-full bg-white border border-gray-300 rounded-lg py-2 pl-10 pr-3 text-sm placeholder-gray-400 focus:outline-hidden focus:text-gray-900 focus:placeholder-gray-200 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                               placeholder="Search the directory..."
                                               type="search">
                                    </div>
                                </form>
                            </div>
                        </div>
                        {{-- Mobile Menu Button --}}
                        <div class="flex items-center lg:hidden ml-2">
                            <button type="button" class="rounded-md px-2 py-1.5 inline-flex text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-hidden focus:ring-2 focus:ring-inset focus:ring-blue-500"
                                    @click="toggle" @mousedown="if (open) $event.preventDefault()" aria-expanded="false" :aria-expanded="open.toString()">
                                <span class="sr-only">Open menu</span>
                                <svg x-description="Icon when menu is closed." x-state:on="Menu open" x-state:off="Menu closed" class="h-6 w-6 block" x-bind:class="{ 'hidden': open, 'block': !(open) }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                </svg>
                                <svg x-description="Icon when menu is open." x-state:on="Menu open" x-state:off="Menu closed" class="h-6 w-6 hidden" x-bind:class="{ 'block': open, 'hidden': !(open) }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        {{-- Desktop Menu --}}
                        <div class="hidden lg:flex shrink items-center">
                            <div x-data="Components.menu({ open: false })" x-init="init()" @keydown.escape.stop="open = false; focusButton()" @click.away="onClickAway($event)" class="shrink-0 relative">
                                <button type="button" class="rounded-md hover:bg-zinc-100 flex pl-2 pr-0 focus:outline-hidden focus:ring-3 focus:ring-offset-2 focus:ring-blue-500"
                                        id="user-menu-button" x-ref="button" @click="onButtonClick()" @keyup.space.prevent="onButtonEnter()" @keydown.enter.prevent="onButtonEnter()"
                                        aria-expanded="false" aria-haspopup="true" x-bind:aria-expanded="open.toString()" @keydown.arrow-up.prevent="onArrowUp()" @keydown.arrow-down.prevent="onArrowDown()">
                                    <span class="sr-only">Open Menu</span>
                                    <span class="min-w-0 max-w-32 text-right mr-3">
                                        <span aria-hidden="true" class="text-base">{{ auth()->user()->name }}</span>
                                        <span aria-hidden="true" class="block font-light text-xs truncate">{{ auth()->user()->getBestEmail()?->email }}</span>
                                    </span>
                                    @if(auth()->user()->avatar)
                                        <img class="w-10 h-10 rounded-md" src="{{ auth()->user()->avatar->getCdnUrl(256) }}"/>
                                    @else
                                        <div class="p-1 size-10 bg-white rounded-lg border border-zinc-300">
                                            <svg class="size-8 text-zinc-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </div>
                                    @endif
                                </button>
                                <div x-show="open" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="origin-top-right absolute z-10 right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 focus:outline-hidden" x-ref="menu-items" x-description="Dropdown menu, show/hide based on menu state." x-bind:aria-activedescendant="activeDescendant" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1" @keydown.arrow-up.prevent="onArrowUp()" @keydown.arrow-down.prevent="onArrowDown()" @keydown.tab="open = false" @keydown.enter.prevent="open = false; focusButton()" @keyup.space.prevent="open = false; focusButton()" style="display: none;">
                                    <a href="{{ route('app.account.edit') }}" class="block py-2 px-4 text-sm text-gray-700" x-state:on="Active" x-state:off="Not Active" :class="{ 'bg-gray-100': activeIndex === 0 }" role="menuitem" tabindex="-1" id="user-menu-item-0" @mouseenter="activeIndex = 0" @mouseleave="activeIndex = -1" @click="open = false; focusButton()">Your Account</a>
                                    <a href="{{ route('logout') }}" class="block py-2 px-4 text-sm text-gray-700" :class="{ 'bg-gray-100': activeIndex === 2 }" role="menuitem" tabindex="-1" id="user-menu-item-2" @mouseenter="activeIndex = 2" @mouseleave="activeIndex = -1" @click="open = false; focusButton()">Sign Out</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <nav class="lg:hidden bg-white" aria-label="Global" x-ref="panel" x-show="open" @click.away="open = false" style="display: none;">
                    <div class="max-w-3xl mx-auto px-2 pt-2 pb-3 space-y-1 sm:px-4">
                        <?php
                        $a_active   = 'bg-gray-100 text-gray-900 block rounded-md py-2 px-3 text-base font-medium';
                        $a_inactive = 'hover:bg-gray-50 block rounded-md py-2 px-3 text-base font-medium';
                        $i_active   = 'text-center w-6 h-6 mr-3 text-white group-focus:text-gray-600 transition ease-in-out duration-150';
                        $i_inactive = 'text-center w-6 h-6 mr-3 text-gray-400 group-hover:text-white group-focus:text-gray-600 transition ease-in-out duration-150';
                        ?>

                        @if(auth()->user()->hasAdminAccess())
                            <a href="{{ route('admin.dashboard.index') }}" class="{{ $a_inactive }} flex rounded-lg border border-gray-200 font-normal text-xs mb-4 mt-2">
                                <div class="flex flex-row mx-auto">
                                    <x-heroicon-o-arrows-right-left class="w-4 mr-1"/>
                                    Switch to Admin
                                </div>
                            </a>
                        @endif

                        <a href="/" class="{{ request()->is('/') ? $a_active : $a_inactive }}">
                            Home
                        </a>
                        @if(auth()->user()->account->hasFeature('feature.directory'))
                            <a href="{{ route('app.directory.index') }}" class="{{ (request()->is('directory*') && !request()->is('directory/photo*')) ? $a_active : $a_inactive }}">
                                Directory
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeature('feature.photo-directory'))
                            <a href="{{ route('app.directory.photo') }}" class="{{ request()->is('directory/photo*') ? $a_active : $a_inactive }}">
                                Photo Directory
                            </a>
                        @endif
                        @can('seeGroupPosts', \App\Users\Group::class)
                            <a href="{{ route('app.groups.index') }}" class="{{ request()->is('groups*') ? $a_active : $a_inactive }}">
                                Groups
                            </a>
                        @endcan
                        @if(auth()->user()->account->hasFeatureForMember('feature.calendars'))
                            <a href="{{ route('app.calendar.index') }}" class="{{ request()->is('calendar*') ? $a_active : $a_inactive }}">
                                Calendars
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeatureForMember('feature.prayers'))
                            <a href="{{ route('app.prayers.index') }}" class="{{ request()->is('prayers*') ? $a_active : $a_inactive }}">
                                Prayer Requests
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeatureForMember('feature.sermons'))
                            <a href="{{ route('app.sermons.index') }}" class="{{ request()->is('sermons*') ? $a_active : $a_inactive }}">
                                Sermons / Classes
                            </a>
                        @endif
                        @can('view', \App\Visitors\Visitor::class)
                            <a href="{{ route('app.visitors.index') }}" class="{{ request()->is('visitors*') ? $a_active : $a_inactive }}">
                                Visitor Tracking
                            </a>
                        @endcan
                        @if(false && auth()->user()->account->hasFeatureForMember('feature.sermons'))
                            <a href="#" class="{{ request()->is('sermons*') ? $a_active : $a_inactive }}">
                                Sermons
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeatureForMember('feature.files'))
                            <a href="{{ route('app.account-files.index') }}" class="{{ request()->is('files*') ? $a_active : $a_inactive }}">
                                Files
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeatureForMember('feature.involvement'))
                            <a href="{{ route('app.account.involvement') }}" class="{{ request()->is('account/involvement') ? $a_active : $a_inactive }}">
                                Involvement
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeatureForMember('feature.involvement'))
                            @can('selections', \App\Involvement\Category::class)
                                <a href="{{ route('app.involvement.selections') }}" class="{{ request()->is('account/involvement/selections*') ? $a_active : $a_inactive }}">
                                    Volunteers
                                </a>
                            @endcan
                        @endif
                        @if(auth()->user()->account->hasFeatureForMember('feature.online_giving') && auth()->user()->can('addPaymentMethods', \App\Finance\Transaction::class))
                            <a href="{{ route('app.giving.index') }}" class="{{ request()->is('giving*') ? $a_active : $a_inactive }}">
                                Online Giving
                            </a>
                        @endif

                    </div>
                    <div class="border-t border-gray-200 pt-4">
                        <div class="mt-3 max-w-3xl mx-auto px-2 space-y-1 sm:px-4">

                            <a href="{{ route('app.account.edit') }}" class="{{ request()->is('account') ? $a_active : $a_inactive }}">Your Account</a>

                            <a href="{{ route('logout') }}" class="block rounded-md py-2 px-3 text-base font-medium text-gray-500 hover:bg-gray-50 hover:text-gray-900">Sign out</a>

                        </div>
                    </div>
                </nav>
            </header>

            @php
                $section1 = [];
                $section2 = [];
                $section3 = [];
                $section4 = [];
                $section5 = [];

                // SECTION 1
                $section1[] = [
                    'href' => '/',
                    'title' => 'Home',
                    'request_is' => '/',
                    'icon' => 'house',
                    'icon_color' => 'text-red-600',
                ];

                if(auth()->user()->account->hasFeature('feature.directory')) {
                    $section1[] = [
                        'href' => route('app.directory.index'),
                        'title' => 'Directory',
                        'request_is' => 'directory*',
                        'request_is_not' => 'directory/photo*',
                        'icon' => 'address-book',
                        'icon_color' => 'text-green-600',
                    ];
                }
                if(auth()->user()->account->hasFeature('feature.photo-directory')) {
                    $section1[] = [
                        'href' => route('app.directory.photo'),
                        'title' => 'Photo Directory',
                        'request_is' => 'directory/photo*',
                        'icon' => 'address-book',
                        'icon_color' => 'text-green-600',
                    ];
                }
                if(auth()->user()->account->hasFeatureForMember('feature.calendars')) {
                    $section1[] = [
                        'href' => route('app.calendar.index'),
                        'title' => 'Calendar',
                        'request_is' => 'calendar*',
                        'icon' => 'calendar',
                        'icon_color' => 'text-blue-500',
                    ];
                }


                // SECTION 2
                if(auth()->user()->can('seeGroupPosts', \App\Users\Group::class)) {
                    $section2[] = [
                        'href' => route('app.groups.index'),
                        'title' => 'Groups',
                        'request_is' => 'groups*',
                        'icon' => 'users',
                        'icon_color' => 'text-purple-600',
                    ];
                }

                // SECTION 3
                if(auth()->user()->account->hasFeatureForMember('feature.prayers')) {
                    $section3[] = [
                        'href' => route('app.prayers.index'),
                        'title' => 'Prayer List',
                        'request_is' => 'prayers*',
                        'icon' => 'pray',
                        'icon_color' => 'text-purple-600',
                    ];
                }
                if(auth()->user()->account->hasFeatureForMember('feature.sermons')) {
                    $section3[] = [
                        'href' => route('app.sermons.index'),
                        'title' => 'Sermons / Lessons',
                        'request_is' => 'sermons*',
                        'icon' => '',
                        'icon_color' => 'text-gray-800',
                    ];
                }
                if(auth()->user()->account->hasFeatureForMember('feature.files')) {
                    $section3[] = [
                        'href' => route('app.account-files.index'),
                        'title' => 'Files',
                        'request_is' => 'files*',
                        'icon' => 'download',
                        'icon_color' => 'text-green-500',
                    ];
                }

                // SECTION 4
                if(auth()->user()->account->hasFeatureForMember('feature.online_giving') && auth()->user()->can('addPaymentMethods', \App\Finance\Transaction::class)) {
                    $section4[] = [
                        'href' => route('app.giving.index'),
                        'title' => 'Giving',
                        'request_is' => 'giving*',
                        'icon' => 'pray',
                        'icon_color' => 'text-red-500',
                    ];
                }
                if(auth()->user()->account->hasFeatureForMember('feature.involvement')) {
                    $section4[] = [
                        'href' => route('app.account.involvement'),
                        'title' => 'Involvement',
                        'request_is' => 'account/involvement',
                        'icon' => '',
                        'icon_color' => 'text-teal-600',
                    ];
                }
                $section4[] = [
                    'href' => route('app.account.edit'),
                    'title' => 'My Account',
                    'request_is' => ['account', 'account/emails*', 'account/phones*', 'account/addresses*'],
                    'icon' => 'download',
                    'icon_color' => 'text-gray-500',
                ];

                // SECTION 5
                if(auth()->user()->can('view', \App\Visitors\Visitor::class)) {
                    $section5[] = [
                        'href' => route('app.visitors.index'),
                        'title' => 'Visitor Tracking',
                        'request_is' => 'visitors*',
                        'icon' => 'pray',
                        'icon_color' => 'text-green-500',
                    ];
                }
                if(auth()->user()->account->hasFeatureForMember('feature.involvement') && auth()->user()->can('selections', \App\Involvement\Category::class)) {
                    $section5[] = [
                        'href' => route('app.involvement.selections'),
                        'title' => 'Volunteers',
                        'request_is' => 'account/involvement/selections*',
                        'icon' => '',
                        'icon_color' => 'text-orange-500',
                    ];
                }
                if(auth()->user()->account->hasFeatureForMember('feature.child_checkin') && auth()->user()->can('kiosk', \App\ChildCheckins\ChildCheckin::class)) {
                    $section5[] = [
                        'href' => route('app.kiosk.child-checkin.index'),
                        'title' => 'Child Check-in',
                        'request_is' => '',
                        'icon' => 'download',
                        'icon_color' => 'text-purple-500',
                    ];
                }

                $all_sections = [$section1, $section2, $section3, $section4, $section5];

                $a_active   = 'flex items-center bg-blue-500 text-white group px-3 py-2 font-normal';
                $a_inactive = 'flex items-center text-gray-600 hover:bg-gray-50 group px-3 py-2 font-normal';
                $i_active   = 'text-white shrink-0 mr-3 h-5 w-5';
                $i_inactive = ' group-hover:text-gray-500 shrink-0 mr-3 h-5 w-5';

            @endphp

            <div class="py-4">
                <div class="max-w-3xl mx-auto sm:px-6 lg:max-w-7xl lg:px-8 lg:grid lg:grid-cols-12 lg:gap-8">
                    <div class="print:hidden hidden lg:block lg:col-span-3 xl:col-span-3">
                        <nav aria-label="Sidebar" class="sticky top-4 divide-y divide-gray-300">
                            <div class="pb-8 space-y-4 text-base">

                                @if(auth()->user()->hasAdminAccess())
                                    <a href="{{ route('admin.dashboard.index') }}" class="{{ $a_inactive }} rounded-lg border border-gray-200 font-normal text-sm hover:bg-white">
                                        <div class="flex flex-row mx-auto">
                                            <x-heroicon-o-arrows-right-left class="w-4 mr-1"/>
                                            Switch to Admin
                                        </div>
                                    </a>
                                @endif

                                @foreach($all_sections as $current_section_group)
                                    @if(count($current_section_group) > 0)
                                        <div class="bg-white rounded-lg border border-gray-200">
                                            @foreach($current_section_group as $section)
                                                @php
                                                    $request_is = (request()->is($section['request_is']) && (\Illuminate\Support\Arr::get($section, 'request_is_not') ? !request()->is($section['request_is_not']) : true)) ? true : false;
                                                @endphp
                                                <a href="{{ $section['href'] }}" class="{{ $request_is ? $a_active : $a_inactive }} {{ ($loop->first ? ' rounded-t-lg' : null) . ($loop->last ? ' rounded-b-lg' : null) }}">
                                                    @switch($section['title'])
                                                        @case('Home')
                                                            <x-fal-house class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Directory')
                                                            <x-fal-address-book class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Photo Directory')
                                                            <x-fal-address-book class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Groups')
                                                            <x-fal-users class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Calendar')
                                                            <x-fal-calendar-days class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Prayer List')
                                                            <x-fal-square-heart class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Sermons / Lessons')
                                                            <x-fal-microphone-lines class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Files')
                                                            <x-fal-file-arrow-down class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Giving')
                                                            <x-fal-hand-holding-heart class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Involvement')
                                                            <x-fal-screwdriver-wrench class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('My Account')
                                                            <x-fal-address-card class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Visitor Tracking')
                                                            <x-fal-bullseye-pointer class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Volunteers')
                                                            <x-fal-handshake class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @case('Child Check-in')
                                                            <x-fal-user-check class="{{ $request_is ? $i_active : $i_inactive . ' ' . $section['icon_color'] }}"/>
                                                            @break
                                                        @default
                                                            @break
                                                    @endswitch
                                                    <div class="grow">
                                                        <div class="flex flex-row justify-between">
                                                            {{ $section['title'] }}
                                                            @if($section['title'] == 'Groups')
                                                                @if(auth()->user()->groupNotifications()->isUnread()->count() > 0)
                                                                    <div id="navbar_group_notifications_count" class="px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-200 text-black">
                                                                        {{ auth()->user()->groupNotifications()->isUnread()->count() }}
                                                                    </div>
                                                                @endif
                                                            @endif
                                                        </div>
                                                    </div>
                                                </a>
                                            @endforeach
                                        </div>
                                    @endif
                                @endforeach

                                <div class="flex">
                                    <a href="{{ route('logout') }}" class="mx-auto px-3 py-2 text-sm font-medium text-gray-400 border border-zinc-50 rounded-md hover:text-gray-900 hover:border-gray-200 hover:bg-white">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </nav>
                    </div>

                    <main class="lg:col-span-9 xl:col-span-9 px-2 sm:px-0">

                        @include('app._layout._messages')

                        @yield('content')

                    </main>

                </div>
            </div>
        </div>

    </div>
</div>


{{-- GLOBAL MODAL --}}
<div id="global-modal" x-cloak style="display:none" x-data x-show="$store.Modal.open"
     class="fixed top-0 z-50 sm:bottom-auto mt-16 inset-x-0 px-4 pb-6 sm:inset-0 sm:p-0 sm:flex sm:items-center sm:justify-center"
     x-transition:enter="ease-out duration-100" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-100"
     x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
    <!--
      Background overlay, show/hide based on modal state.
    -->
    <div @click="closeModal()" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-100" x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-gray-500/75 transition-opacity">
    </div>

    <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 sm:items-center sm:p-0">
            <!--
              Modal panel, show/hide based on modal state.
            -->
            <div id="global-modal-content"
                 @click.outside="closeModal()"
                 class="bg-white rounded-lg px-4 pt-5 pb-4 overflow-auto shadow-xl transform transition-all sm:max-w-2xl sm:w-full sm:p-6"
                 x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-100" x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0" role="dialog" aria-modal="true" aria-labelledby="modal-headline">

            </div>
        </div>
    </div>
</div>

{{--  This is some TailwindCSS that we need for our Tailwind Pagination that is not yet rendered, and PostCSS doesn't pick up  --}}
<div class=" hidden sm:hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"></div>

<!-- Scripts -->
<script>
    var pusher = new Pusher('{{ config('broadcasting.connections.pusher.key') }}', {
        cluster: '{{ config('broadcasting.connections.pusher.options.cluster') }}',
        authEndpoint: '/broadcasting/auth',
        forceTLS: true,
        disableStats: true,
    });

    pusher.connection.bind('error', function (err) {
        if (err.error) {
            console.error('Pusher connection error:', err.error);
        } else {
            console.error('Pusher connection error occurred.');
        }
    });

    {{--
    // pusher.connection.bind('state_change', function (states) {
    //     console.log(states);
    //     console.log(states.previous);
    //     console.log(states.current);
    // })
    --}}

    var channelUserNotifications = pusher.subscribe('private-{{ auth()->user()->account_id }}.user.notifications.{{ auth()->user()->id }}');
    channelUserNotifications.bind('pusher:error', function (err) {
        console.log(err);
    });
    channelUserNotifications.bind('pusher:subscription_error', function (err) {
        console.log(err);
    });
    channelUserNotifications.bind('notification.created', function (data) {
        console.log('A new notification was posted for user # ' + data.user_id);
    });
</script>

<script type="text/javascript">
    {{-- Setup for opening and closing our Modal and Sidebar  --}}
    document.addEventListener('alpine:init', () => {
        Alpine.store('Modal', {
            open: false,

            toggle() {
                this.open = !this.open
            }
        })
    })

    {{-- "Loading" indicator for our global modal/sidebar --}}
    const loadingDiv = `<div class="flex justify-around text-lg opacity-50">
            <div class="inline-flex items-center">
                <svg class="animate-spin h-6 w-6 text-black mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg> Loading...
            </div></div>`;

    function openModal(url = null) {
        {{--  Show loading indicator. --}}
        document.getElementById('global-modal-content').innerHTML = loadingDiv;

        {{--  Get our URL and put the content in the modal. --}}
        var request = new Request(url, {
            method: 'GET',
            mode: 'cors',
            headers: new Headers({
                'Content-Type': 'text/plain'
            })
        });

        fetch(request).then(response => response.blob())
            .then(blob => blob.text())
            .then(text => document.getElementById('global-modal-content').innerHTML = text);

        {{--  Unhide our modal --}}
        Alpine.store('Modal').toggle();

        setTimeout(() => {
            {{--  Rescan our Livewire components so they work in the modal.  --}}
            window.livewire.rescan();
        }, 300);
    }

    function closeModal() {
        Alpine.store('Modal').toggle();
        setTimeout(() => {
            document.getElementById('global-modal-content').innerHTML = ''
        }, 300);
    }
</script>

<script src="{{ mix('static/app/js/app.js') }}" defer></script>

@livewireScripts

@stack('scripts')

@fluxScripts


</body>
</html>
