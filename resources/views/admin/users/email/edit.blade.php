@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.users.email.save', [$user, $email]) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    {{ csrf_field() }}
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-edit" aria-hidden="true"></i> Edit Email
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Email Address
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <input type="text" name="email" placeholder="<EMAIL>" class="{{ $inputCSS }}" autocomplete="off" value="{{ $email->email }}"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Type
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <select name="type" class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                @foreach(\App\Users\Email::$types as $type => $name)
                                    <option value="{{ $type }}" {{ $type == $email->type ? 'selected' : null }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="py-3 sm:px-5">
                    <div class="space-y-3 py-3">
                        <fieldset>
                            <legend class="text-sm font-medium text-gray-900">
                                Email Options
                            </legend>
                            <div class="mt-2 space-y-3 sm:ml-4">
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_primary" name="is_primary" value="1" @isChecked($email->is_primary)>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_primary" class="font-medium text-gray-900">
                                                Primary email?
                                                <p class="text-gray-500 font-normal">
                                                    This is the users primary email address.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="receives_group_emails" name="receives_group_emails" value="1" @isChecked($email->receives_group_emails)>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="receives_group_emails" class="font-medium text-gray-900">
                                                Receives group emails?
                                                <p class="text-gray-500 font-normal">
                                                    Enable or disable group emails being sent to this address.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="relative flex items-start">
                                    <div class="absolute flex items-center h-5">
                                        <input type="checkbox" class="{{ $checkboxCSS }}" id="is_family" name="is_family" value="1" @isChecked($email->is_family)>
                                    </div>
                                    <div class="pl-7 text-sm">
                                        <label for="is_family" class="font-medium text-gray-900">
                                            Belongs to the whole family?
                                            <p class="text-gray-500 font-normal">
                                                This email will be attached to every member of the family.
                                                <br>
                                                <em>Not recommended.</em> This will prevent this address from receiving group emails or being able to login with this address.
                                            </p>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_hidden" name="is_hidden" value="1" @isChecked($email->is_hidden)>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_hidden" class="font-medium text-gray-900">
                                                Is hidden?
                                                <p class="text-gray-500 font-normal">
                                                    This email will be hidden from the church directory.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                    <div>
                        @can('delete', $email)
                            <button type="button" onclick="document.getElementById('delete_email_{{ $email->id }}').submit()" class="inline-flex justify-center py-2 px-4 border border-red-600 text-sm font-medium rounded-md text-red-600 bg-white hover:bg-red-600 hover:text-white focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500" title="Delete">Delete</button>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>

<form method="post" action="{{ route('admin.users.email.delete', [$user, $email]) }}" id="delete_email_{{ $email->id }}">
    @csrf
</form>
