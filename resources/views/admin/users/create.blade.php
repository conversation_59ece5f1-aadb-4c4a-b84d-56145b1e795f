@extends('admin._layouts._app')

@section('title', 'Create User')

@section('content')

    <div class="admin-standard-col-width">

        <div class="pb-2 md:flex md:items-center md:justify-between">
            <div class="flex-1">
                <div class="flex items-start sm:items-center">
                    <div class="ml-1">
                        <div class="flex items-center">
                            <div class="md:flex md:items-center align-middle md:justify-between">
                                <div>
                                    <a href="{{ route('admin.users.index') }}" class="admin-button-transparent px-2 mr-4 hover:bg-gray-200">
                                        <x-heroicon-s-arrow-left class="w-5 p-0"/>
                                    </a>
                                </div>
                                <div class="flex justify-between">
                                    <h1>
                                        Add User
                                    </h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <form class="mt-4 grid gap-6 grid-cols-1" action="{{ route('admin.users.store') }}" method="post">
            @csrf()
            <div class="admin-section px-4 py-5 sm:p-6">
                <div class="md:grid md:grid-cols-5 md:gap-6">
                    <div class="mt-5 md:mt-0 md:col-span-5 max-w-3xl">
                        <div class="grid grid-cols-12 gap-3">
                            <div class="col-span-5 sm:col-span-5">
                                <label for="first_name" class="block text-sm font-medium text-gray-700">First name</label>
                                <input type="text" name="first_name" id="first_name" autocomplete="off" class="mt-1">
                            </div>

                            <div class="col-span-7 sm:col-span-7">
                                <label for="last_name" class="block text-sm font-medium text-gray-700">Last name</label>
                                <input type="text" name="last_name" id="last_name" autocomplete="off" class="mt-1">
                            </div>

                            <div class="col-span-6 sm:col-span-5">
                                <label for="email" class="block text-sm font-medium text-gray-700">Email address (primary)</label>
                                <input type="text" name="email" id="email" autocomplete="off" placeholder="<EMAIL>" class="mt-1">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="mobile_number" class="block text-sm font-medium text-gray-700">Mobile Phone</label>
                                <input type="text" name="mobile_number" id="mobile_number" autocomplete="off" placeholder="************" class="mt-1">
                            </div>
                            <div class="col-span-6 sm:col-span-2 sm:col-start-1">
                                <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
                                <select id="gender" name="gender" autocomplete="off" class="mt-1">
                                    @foreach (\App\Users\User::$genders as $key => $value)
                                        <option value="{{ $key }}"{{ old('gender') === $key ? ' selected' : ''}}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="marital_status" class="block text-sm font-medium text-gray-700 whitespace-nowrap">Marital Status</label>
                                <select id="marital_status" name="marital_status" autocomplete="off" class="mt-1">
                                    @foreach (\App\Users\User::$marital_statuses as $key => $value)
                                        <option value="{{ $key }}"{{ old('marital_status') === $key ? ' selected' : ''}}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-span-6 sm:col-span-3 sm:col-start-1">
                                <label for="birthdate" class="block text-sm font-medium text-gray-700 mb-1">Birthdate</label>
                                <flux:date-picker name="birthdate" selectable-header>
                                    <x-slot name="trigger">
                                        <flux:date-picker.input/>
                                    </x-slot>
                                </flux:date-picker>
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="date_baptism" class="block text-sm font-medium text-gray-700 mb-1 whitespace-nowrap">Baptism Date</label>
                                <flux:date-picker name="birthdate" selectable-header onchange="document.getElementById('is_baptized').checked = true">
                                    <x-slot name="trigger">
                                        <flux:date-picker.input/>
                                    </x-slot>
                                </flux:date-picker>
                            </div>
                            <div class="col-span-6 sm:col-span-6">
                                <flux:checkbox value="{{ now()->format('Y-m-d') }}" name="is_baptized" id="is_baptized" label="Is Baptized?" description="Use this if you do not have a baptism date but want to indicate baptized status."/>
                            </div>
                            <div class="col-span-6 sm:col-span-5 sm:col-start-1">
                                <label for="family_id" class="block text-sm font-medium text-gray-700 mb-1">Belongs to Family</label>
                                <flux:select name="family_id" variant="listbox" searchable placeholder="Choose family...">
                                    @foreach(\App\Users\User::getAllFamilies()->visibleTo(auth()->user())->select('id', 'last_name', 'first_name')->get() as $family)
                                        <flux:select.option value="{{ $family->id }}">{{ $family->last_name.', '.$family->first_name }}</flux:select.option>
                                    @endforeach
                                </flux:select>
                            </div>
                            <div class="col-span-6 sm:col-span-4">
                                <label for="family_role" class="block text-sm font-medium text-gray-700">Family Role</label>
                                <select id="family_role" name="family_role" autocomplete="off" class="mt-1">
                                    <option></option>
                                    @foreach(\App\Users\User::$family_roles as $index => $value)
                                        <option value="{{ $index }}" {{ old('family_role') ? 'selected' : null }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-span-6 sm:col-start-1">
                                <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                                <input type="text" name="notes" id="notes" autocomplete="street-address" class="mt-1">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="admin-section px-4 py-5 sm:p-6">
                <div class="md:grid md:grid-cols-4 md:gap-6">
                    <div class="mt-5 md:mt-0 md:col-span-4">
                        <div class="space-y-6" action="#" method="POST">
                            <div class="col-span-6">
                                <label for="family_id" class="block text-lg font-semibold text-gray-700">Roles</label>
                                <hr class="my-1">
                                <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4">
                                    @foreach(\App\Users\Role::visibleTo(auth()->user())->orderBy('name')->get() as $index => $role)
                                        <label class="flex flex-row">
                                            <div>
                                                <input type="checkbox" name="roles[]" value="{{ $role->id }}"/>
                                            </div>
                                            <div class="ml-1 mt-0.5">
                                                {{ $role->name }}
                                            </div>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                            <div class="col-span-6">
                                <label for="family_id" class="block text-lg font-semibold text-gray-700">Groups</label>
                                <hr class="my-1">
                                <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4">
                                    @php
                                        $defaultGroup = null;
                                        $defaultGroup = auth()->user()->account->getDefaultMemberGroup()?->id;
                                    @endphp
                                    @foreach(\App\Users\Group::visibleTo(auth()->user())->orderBy('name', 'asc')->get() as $index => $group)
                                        <label class="flex flex-row">
                                            <div>
                                                <input type="checkbox" name="groups[]" value="{{ $group->id }}" {{ $defaultGroup == $group->id ? 'checked="checked"' : null }} />
                                            </div>
                                            <div class="ml-1 mt-0.5">{{ $group->name }}</div>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-start">
                <button type="submit" class="admin-button-blue">
                    <x-heroicon-s-check class="w-5 mr-2"/>
                    Save New User
                </button>
                <button type="button" class="admin-button-transparent hover:bg-gray-200 ml-4">Cancel</button>
            </div>
        </form>

    </div>

    @push('scripts')
        <script>
            var initChoiceJs = function () {
                jschoice1 = new Choices('.js-choice', {
                    removeItemButton: true,
                    allowHTML: false,
                });
            }

            document.addEventListener('DOMContentLoaded', event => {
                initChoiceJs();
            });
        </script>
    @endpush

@endsection
