@php
    $inputCSS = 'standard-input';
    $checkboxCSS = 'standard-checkbox';
@endphp

<form id="update-photo-form" method="post" enctype="multipart/form-data" action="{{ route('admin.users.photos.save', [$user, $photo]) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    <input type="hidden" class="{{ $checkboxCSS }}" id="needs_approval" name="needs_approval" value="{{ $photo->needs_approval ? '1' : '0' }}"/>
    <div class="overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-edit" aria-hidden="true"></i> Edit Photo <span class="text-blue-700">- #{{ $photo->id }}</span>
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    JPG files only!
                </p>
            </div>
        </div>
        <div class="flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <img src="{{ $photo->getCdnUrl(512) }}?{{ uniqid() }}" alt="" class="w-1/4 object-center object-cover rounded-lg"/>
                            <label for="label" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Want to change the photo? Try uploading a new photo.
                            </label>
                        </div>
                    </div>
                </div>
                <div class="py-3 sm:px-5">
                    <div class="space-y-3 py-3">
                        <fieldset>
                            <legend class="text-sm font-medium text-gray-900">
                                Photo Options
                            </legend>
                            <div class="mt-2 space-y-3 sm:ml-4">
                                <div class="relative flex items-start">
                                    <div class="absolute flex items-center h-5">
                                        <input type="checkbox" class="{{ $checkboxCSS }}" id="is_primary" name="is_primary" value="1" @isChecked($photo->is_primary) />
                                    </div>
                                    <div class="pl-7 text-sm">
                                        <label for="is_primary" class="font-medium text-gray-900">
                                            Primary Family Photo?
                                            <p class="text-gray-500 font-normal">
                                                Should this be the user's primary family photo?
                                            </p>
                                        </label>
                                    </div>
                                </div>
                                <div class="relative flex items-start">
                                    <div class="absolute flex items-center h-5">
                                        <input type="checkbox" class="{{ $checkboxCSS }}" id="is_avatar" name="is_avatar" value="1" @isChecked($photo->is_avatar) />
                                    </div>
                                    <div class="pl-7 text-sm">
                                        <label for="is_avatar" class="font-medium text-gray-900">
                                            Profile Photo?
                                            <p class="text-gray-500 font-normal">
                                                Is this the user's profile photo?
                                            </p>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_family" name="is_family" value="1" @isChecked($photo->isFamily()) />
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_family" class="font-medium text-gray-900">
                                                Family Photo?
                                                <p class="text-gray-500 font-normal">
                                                    Is this a photo of the whole family?
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_hidden" name="is_hidden" value="1" @isChecked($photo->is_hidden) />
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_hidden" class="font-medium text-gray-900">
                                                Hidden?
                                                <p class="text-gray-500 font-normal">
                                                    This photo will not be visible to anyone except admins.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" onclick="this.disabled = true; this.innerHTML = 'Uploading...'; document.getElementById('update-photo-form').submit();" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                    <div>
                        @can('delete', $photo)
                            <button type="button" onclick="document.getElementById('delete_photo_{{ $photo->id }}').submit()" class="inline-flex justify-center py-2 px-4 border border-red-600 text-sm font-medium rounded-md text-red-600 bg-white hover:bg-red-600 hover:text-white focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500" title="Delete">Delete</button>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
    @if($photo->needs_approval)
        <div class="shrink-0 px-6">
            <div class="flex justify-between py-4">
                <button type="button" onclick="this.disabled = true; this.innerHTML = 'Approving...'; document.getElementById('approve_photo_form').submit();" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white bg-green-500">
                    <x-heroicon-s-check class="w-5 h-5 mr-1"/>
                    Approve Photo
                </button>
            </div>
        </div>
    @endif
    <div class="flex shrink-0 px-6 space-x-3">
        <div class=" py-4">
            <button type="button" onclick="this.disabled = true; this.innerHTML = 'Rotating...'; document.getElementById('rotate_photo_left').submit();" class="inline-flex justify-center py-2 px-4 border border-gray-600 shadow-xs text-sm font-medium rounded-md text-black bg-white">
                <x-heroicon-s-arrow-uturn-left class="w-5 h-5 mr-1"/>
                Rotate Left
            </button>
        </div>
        <div class=" py-4">
            <button type="button" onclick="this.disabled = true; this.innerHTML = 'Rotating...'; document.getElementById('rotate_photo_right').submit();" class="inline-flex justify-center py-2 px-4 border border-gray-600 shadow-xs text-sm font-medium rounded-md text-black bg-white">
                Rotate Right
                <x-heroicon-s-arrow-uturn-right class="w-5 h-5 ml-1"/>
            </button>
        </div>
    </div>
    <div class="flex shrink-0 px-6 pt-2">
        WARNING: Rotating a photo can take a while to update in all places due to browser caching. Please be patient while the photo updates in all places.
        <br>
        Changes will not take place immediately, everywhere.
    </div>
</form>

<form method="post" action="{{ route('admin.users.photos.rotate', [$user, $photo]) }}" id="rotate_photo_left">
    <input type="hidden" name="direction" value="left"/>
    @method('post')
    @csrf
</form>
<form method="post" action="{{ route('admin.users.photos.rotate', [$user, $photo]) }}" id="rotate_photo_right">
    <input type="hidden" name="direction" value="right"/>
    @method('post')
    @csrf
</form>

@if($photo->needs_approval)
    <form method="post" action="{{ route('admin.users.photos.save', [$user, $photo]) }}" id="approve_photo_form">
        <input type="hidden" name="needs_approval" value="0"/>
        <input type="hidden" name="approved_by" value="{{ auth()->user()->id }}"/>
        <input type="hidden" class="{{ $checkboxCSS }}" id="is_primary" name="is_primary" value="{{ $photo->is_primary ? '1' : '0' }}"/>
        <input type="hidden" class="{{ $checkboxCSS }}" id="is_avatar" name="is_avatar" value="{{ $photo->is_avatar ? '1' : '0' }}"/>
        <input type="hidden" class="{{ $checkboxCSS }}" id="is_family" name="is_family" value="{{ $photo->isFamily() ? '1' : '0' }}"/>
        <input type="hidden" class="{{ $checkboxCSS }}" id="is_hidden" name="is_hidden" value="{{ $photo->is_hidden ? '1' : '0' }}"/>
        @method('post')
        @csrf
    </form>
@endif


@can('delete', $photo)
    <form method="post" action="{{ route('admin.users.photos.delete', [$user, $photo]) }}" id="delete_photo_{{ $photo->id }}">
        @method('delete')
        @csrf
    </form>
@endcan
