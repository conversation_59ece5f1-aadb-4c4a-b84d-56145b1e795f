@php
    $inputCSS = 'standard-input';
    $checkboxCSS = 'standard-checkbox';
@endphp

<form id="new-photo-form" method="post" enctype="multipart/form-data" action="{{ route('admin.users.photos.store', $user) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-edit" aria-hidden="true"></i> Add Photo
                </h2>
            </div>
            <div class="mt-4 hidden">
                <p class="text-sm text-blue-300">
                    JPG files only!
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="label" class="block text-base uppercase font-semibold text-gray-900 sm:mt-px sm:pt-2">
                                File
                            </label>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div class="mb-4">
                            <div class="custom-file border-gray-300">
                                <input class="custom-file-input hidden" id="fileInput" type="file" name="user_file" accept="image/*">
                                <input type="hidden" name="cropped_data" id="cropped_data">
                                <label for="fileInput" class="cursor-pointer bg-blue-600 text-white px-4 py-2 rounded-sm text-base">
                                    Choose Photo
                                </label>
                            </div>
                        </div>

                        <div class="flex flex-col font-medium text-sm">
                            <div><i class="fa fa-exclamation-square text-green-500 h-5" aria-hidden="true"></i> JPG files only.</div>
                            <div><i class="fa fa-image text-indigo-400" aria-hidden="true"></i> Landscape (4x3) is <strong>strongly recommended</strong> for family photos.</div>
                            <div><i class="fa fa-image text-indigo-400" aria-hidden="true"></i> Square (1x1) is <strong>strongly recommended</strong> for profile photos.</div>
                        </div>
                        
                        <!-- Image Preview & Cropper -->
                        <div id="imagePreview" class="hidden">
                            <div class="relative">
                                <img id="previewImage" class="max-w-full">
                            </div>
                        </div>

                        <!-- Hide the options initially -->
                        <div id="photoEditingOptions" class="hidden">
                            <div class="mt-4 flex justify-center space-x-4">
                                <!-- Reset Button -->
                                <div class="inline-flex rounded-md shadow-xs" role="group">
                                    <button type="button" 
                                            id="resetCrop"
                                            class="px-4 py-2 text-sm font-medium rounded-lg border border-gray-300 hover:bg-gray-50 hover:text-gray-600 focus:z-10 focus:ring-2 focus:ring-gray-500 text-gray-600">
                                        <i class="fa fa-refresh"></i>
                                    </button>
                                </div>

                                <!-- Photo Type Pills -->
                                <div class="inline-flex rounded-md shadow-xs" role="group">
                                    <button type="button" 
                                            id="type_standard" 
                                            data-type="standard"
                                            class="photo-type-btn px-4 py-2 text-sm font-medium rounded-l-lg border border-blue-600 hover:bg-blue-50 hover:text-blue-600 focus:z-10 focus:ring-2 focus:ring-blue-500 bg-blue-600 text-white">
                                        Standard/Family (4:3)
                                    </button>
                                    <button type="button" 
                                            id="type_profile" 
                                            data-type="profile"
                                            class="photo-type-btn px-4 py-2 text-sm font-medium border border-blue-600 hover:bg-blue-50 hover:text-blue-600 focus:z-10 focus:ring-2 focus:ring-blue-500 text-blue-600">
                                        Profile (1:1)
                                    </button>
                                    <button type="button" 
                                            id="type_none" 
                                            data-type="none"
                                            class="photo-type-btn px-4 py-2 text-sm font-medium rounded-r-lg border border-blue-600 hover:bg-blue-50 hover:text-blue-600 focus:z-10 focus:ring-2 focus:ring-blue-500 text-blue-600">
                                        No Cropping
                                    </button>
                                </div>

                                <!-- Rotate Pills -->
                                <div class="inline-flex rounded-md shadow-xs" role="group">
                                    <button type="button" 
                                            id="rotateLeft"
                                            class="px-4 py-2 text-sm font-medium rounded-l-lg border border-gray-600 hover:bg-gray-50 hover:text-gray-600 focus:z-10 focus:ring-2 focus:ring-gray-500 text-gray-600">
                                        <i class="fa fa-rotate-left"></i>
                                    </button>
                                    <button type="button" 
                                            id="rotateRight"
                                            class="px-4 py-2 text-sm font-medium rounded-r-lg border border-gray-600 hover:bg-gray-50 hover:text-gray-600 focus:z-10 focus:ring-2 focus:ring-gray-500 text-gray-600">
                                        <i class="fa fa-rotate-right"></i>
                                    </button>
                                </div>
                                <input type="hidden" name="photo_type" id="photo_type_input" value="standard">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="py-3 sm:px-5">
                    <div class="py-3">
                        <fieldset>
                            <legend class="text-base uppercase font-semibold text-gray-900">
                                Photo Options
                            </legend>
                            <div class="space-y-3 sm:ml-4">
                                <div class="mt-4 space-y-3">
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_primary" name="is_primary" value="1"/>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_primary" class="font-medium text-gray-900">
                                                Primary Family Photo?
                                                <p class="text-gray-500 font-normal">Should this be the family's primary photo?<br/>(This is what will show in the directory)</p>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_avatar" name="is_avatar" value="1"/>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_avatar" class="font-medium text-gray-900">
                                                Profile Photo?
                                                <p class="text-gray-500 font-normal">Is this the user's profile photo?</p>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_family" name="is_family" value="1"/>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_family" class="font-medium text-gray-900">
                                                Family Photo?
                                                <p class="text-gray-500 font-normal">Is this a photo of the whole family?</p>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_hidden" name="is_hidden" value="1"/>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_hidden" class="font-medium text-gray-900">
                                                Hidden?
                                                <p class="text-gray-500 font-normal">This photo will not be visible to anyone except admins.</p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
<div id="familyProfileWarning" class="hidden mt-2 p-2 bg-yellow-50 text-yellow-700 rounded-md text-sm">
    <i class="fa fa-exclamation-triangle text-yellow-400"></i>
                            It's not recommended to mark a photo as both a family photo and a profile photo. Profile photos are typically individual portraits.
                        </div>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" onclick="this.disabled = true; this.innerHTML = 'Uploading...'; document.getElementById('new-photo-form').submit();" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white btn-color-and-hover">
                            Save New Photo
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                </div>
                <div class="text-sm text-gray-500 pt-4">
                    Note: Both "primary family photo" and "family photo" are required if you want to replace the photo being used in the directory.
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>

<script>
    // Check if cropper already exists and destroy it if it does
    if (window.currentCropper) {
        window.currentCropper.destroy();
    }
    window.currentCropper = null;

    // Function to check and show/hide warning
    function checkFamilyProfileConflict() {
        const isFamily = document.getElementById('is_family').checked;
        const isAvatar = document.getElementById('is_avatar').checked;
        const warningElement = document.getElementById('familyProfileWarning');
        
        if (isFamily && isAvatar) {
            warningElement.classList.remove('hidden');
        } else {
            warningElement.classList.add('hidden');
        }
    }

    // Auto-check is_family when is_primary is checked
    document.getElementById('is_primary').addEventListener('change', function(e) {
        if (e.target.checked) {
            document.getElementById('is_family').checked = true;
            checkFamilyProfileConflict();
        }
    });

    // Auto-select profile type and uncheck family options when is_avatar is checked
    document.getElementById('is_avatar').addEventListener('change', function(e) {
        if (e.target.checked) {
            document.getElementById('type_profile').click();
            document.getElementById('is_primary').checked = false;
            document.getElementById('is_family').checked = false;
        }
        checkFamilyProfileConflict();
    });

    // Uncheck is_primary when is_hidden is checked
    document.getElementById('is_hidden').addEventListener('change', function(e) {
        if (e.target.checked) {
            document.getElementById('is_primary').checked = false;
        }
    });

    // Add listener for is_family checkbox
    document.getElementById('is_family').addEventListener('change', function() {
        checkFamilyProfileConflict();
    });

    // Function to update cropper aspect ratio
    function updateCropperAspectRatio() {
        if (!window.currentCropper) return;
        
        const photoType = document.getElementById('photo_type_input').value;
        if (photoType === 'none') {
            window.currentCropper.setAspectRatio(NaN); // NaN means free aspect ratio
            window.currentCropper.setDragMode('move'); // Allows free movement without cropping
        } else {
            const aspectRatio = photoType === 'profile' ? 1 : 4/3;
            window.currentCropper.setAspectRatio(aspectRatio);
            window.currentCropper.setDragMode('crop');
        }
    }

    // Update pill button selection
    const photoTypeButtons = document.querySelectorAll('.photo-type-btn');
    photoTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update button styles
            photoTypeButtons.forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('text-blue-600');
            });
            this.classList.add('bg-blue-600', 'text-white');
            this.classList.remove('text-blue-600');
            
            // Update hidden input
            document.getElementById('photo_type_input').value = this.dataset.type;
            
            // Update cropper aspect ratio
            updateCropperAspectRatio();
            
            // Update cropped data when type changes
            if (window.currentCropper) {
                updateCroppedData(window.currentCropper);
            }
        });
    });

    document.getElementById('fileInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewDiv = document.getElementById('imagePreview');
                const previewImage = document.getElementById('previewImage');
                const editingOptions = document.getElementById('photoEditingOptions');
                
                previewImage.src = e.target.result;
                previewDiv.classList.remove('hidden');
                editingOptions.classList.remove('hidden');
                
                if (window.currentCropper) {
                    window.currentCropper.destroy();
                }
                
                const photoType = document.getElementById('photo_type_input').value;
                const aspectRatio = photoType === 'none' ? NaN : (photoType === 'profile' ? 1 : 4/3);
                
                window.currentCropper = new Cropper(previewImage, {
                    aspectRatio: aspectRatio,
                    viewMode: 2,
                    autoCropArea: 1,
                    ready() {
                        updateCroppedData(this.cropper);
                    },
                    cropend() {
                        updateCroppedData(this.cropper);
                    }
                });
            }
            reader.readAsDataURL(file);
        }
    });

    // Separate function to handle getting cropped data
    function updateCroppedData(cropper) {
        const photoType = document.getElementById('photo_type_input').value;
        const options = {
            width: photoType === 'profile' ? 800 : null,  // Square size for profile
            height: photoType === 'profile' ? 800 : null, // Square size for profile
            aspectRatio: photoType === 'none' ? null : 
                        photoType === 'profile' ? 1 : 4/3,
            maxWidth: 2000,
            maxHeight: 2000,
            imageSmoothingQuality: 'high'
        };

        cropper.getCroppedCanvas(options).toBlob((blob) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onloadend = function() {
                document.getElementById('cropped_data').value = reader.result;
            }
        }, 'image/jpeg', 0.9);
    }

    // Rotation buttons
    document.getElementById('rotateLeft')?.addEventListener('click', function() {
        if (window.currentCropper) {
            window.currentCropper.rotate(-90);
        }
    });

    document.getElementById('rotateRight')?.addEventListener('click', function() {
        if (window.currentCropper) {
            window.currentCropper.rotate(90);
        }
    });

    // Add reset button handler
    document.getElementById('resetCrop')?.addEventListener('click', function() {
        if (window.currentCropper) {
            window.currentCropper.reset();
        }
    });
</script>
