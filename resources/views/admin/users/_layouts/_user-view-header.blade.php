@include('admin._layouts.components.breadcrumbs', ['levels' => [[
    'url' => route('admin.users.index') . (request()->has('index_filters') ? '?' . base64_decode(request()->get('index_filters')) : null),
    'text' => 'People',
], [
    'url' => null,
    'text' => 'View User',
]]])

<div class="pb-2 md:flex md:items-center md:justify-between">

    <div class="flex-1">
        <div class="flex items-start sm:items-center">
            @if($user->avatar)
                <img class="w-14 h-14 rounded-lg mr-2" src="{{ $user->avatar->getCdnUrl(512) }}"/>
            @else
                <svg class="w-14 h-14 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            @endif
            <div class="ml-1">
                <div class="flex items-center">
                    <h1 class="font-semibold text-gray-900 sm:leading-9 sm:truncate">
                        {{ $user->name }}
                    </h1>
                </div>
                <div class="flex flex-row space-x-2">
                    @if($user->isMember())
                        <div class="mt-1 flex items-center text-gray-600 text-xs font-medium uppercase border border-gray-500 rounded-sm px-2 py-0.5">
                            <i class="fas fa-user text-xs text-stone-600 my-auto mr-2"></i>
                            Member
                        </div>
                    @endif
                    @if($user->appearsInDirectory())
                        <div class="mt-1 flex items-center text-gray-600 text-xs font-medium uppercase border border-gray-500 rounded-sm px-2 py-0.5">
                            <i class="fas fa-book text-xs text-green-600 my-auto mr-2"></i>
                            In Directory
                        </div>
                    @endif
                    @if(!$user->family_id)
                        <div class="mt-1 flex items-center text-rose-600 text-xs font-medium uppercase border border-rose-500 rounded-sm px-2 py-0.5">
                            <i class="fas fa-group text-xs my-auto mr-2"></i>
                            Missing Family Unit
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4 flex md:mt-auto gap-2">
        @if(!request()->routeIs('admin.users.edit') && $user->account->hasFeature('feature.visitor_tracking') && ($user->isVisitor() || $user->isInVisitorTracking()))
            <flux:dropdown>
                <flux:button icon:trailing="chevron-down" align="end">Visitor</flux:button>
                <flux:menu>
                    @if($user->isInVisitorTracking())
                        <flux:menu.item icon="cursor-arrow-rays"
                                        href="{{ $user->getVisitorTrackingRecord() ? route('app.visitors.view', $user->getVisitorTrackingRecord()) : '#' }}">
                            Visitor Tracking
                        </flux:menu.item>
                    @else
                        <flux:menu.item icon="cursor-arrow-rays"
                                        onclick="openModal('{{ route('admin.users.visitor-tracking.start', $user) }}')">
                            Send to Visitor Tracking
                        </flux:menu.item>
                    @endif
                </flux:menu>
            </flux:dropdown>
        @endif
        @if(request()->routeIs('admin.users.edit'))
            <flux:button icon="trash" variant="danger" class="mr-2"
                         onclick="openModal('{{ route('admin.users.delete', $user) }}')">
                Delete
            </flux:button>
            <flux:button icon="x-mark"
                         onclick="history.back()">
                Cancel
            </flux:button>
            <flux:button icon="check" variant="primary"
                         onclick="document.getElementById('save_user_edit_form').submit()">
                Save Changes
            </flux:button>
        @endif
        @if(!request()->routeIs('admin.users.edit'))
            <div class="relative z-0 inline-flex ml-2 gap-2" x-data="{userOptionsMenuOpen: false}">
                <flux:button icon="pencil-square"
                             href="{{ route('admin.users.edit', $user) }}">
                    Edit
                </flux:button>


                <flux:dropdown>
                    <flux:button icon:trailing="chevron-down" align="end">Options</flux:button>
                    <flux:menu>
                        @if($user->isHeadOfFamily())
                            <flux:menu.item icon="arrow-path"
                                            onclick="openModal('{{ route('admin.users.change-head-of-household', $user) }}')">
                                Change Head of Household
                            </flux:menu.item>
                            <flux:menu.separator/>
                        @endif
                        <flux:menu.item variant="danger" icon="trash"
                                        onclick="openModal('{{ route('admin.users.clear-sessions', [$user]) }}')">
                            Clear Login Sessions
                        </flux:menu.item>
                    </flux:menu>
                </flux:dropdown>

            </div>
        @endif
    </div>
</div>


@php
    $unselected = 'text-gray-800 whitespace-nowrap px-2 py-1.5 md:px-4 rounded-md focus:outline-hidden focus:border-gray-300 hover:bg-gray-200';
    $selected = 'bg-blue-600 rounded-md whitespace-nowrap px-2 py-1.5 md:px-4 text-white focus:outline-hidden';
@endphp
<nav class="mt-2 p-1.5 flex flex-wrap bg-gray-50 rounded-lg gap-1 border border-gray-300 shadow-inner text-sm md:text-base font-normal">
    <a href="{{ route('admin.users.view', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs(['admin.users.view', 'admin.users.edit']) ? $selected : $unselected }}" aria-current="page">
        <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-info-circle"></i></span> Information
    </a>
    <a href="{{ route('admin.users.groups-roles', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.groups-roles') ? $selected : $unselected }}">
        <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-users-crown"></i></span> Groups / Roles
    </a>
    <a href="{{ route('admin.users.photos.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.photos.index') ? $selected : $unselected }}">
        <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-camera"></i></span> Photos
    </a>
    @if(auth()->user()->account->hasFeature('feature.involvement'))
        <a href="{{ route('admin.users.involvement.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.involvement.index') ? $selected : $unselected }}">
            <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-check-square"></i></span> Involvement
        </a>
    @endif
    @if(auth()->user()->account->hasFeature('feature.attendance'))
        <a href="{{ route('admin.users.attendance.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.attendance.index') ? $selected : $unselected }}">
            <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-clock"></i></span> Attendance
        </a>
    @endif
    @if(auth()->user()->account->hasFeature('feature.online_giving'))
        <a href="{{ route('admin.users.giving.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.giving.index') ? $selected : $unselected }}">
            <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-hand-holding-usd"></i></span> Giving
        </a>
    @endif
</nav>

{{--@php--}}
{{--    $unselected = 'text-gray-500 whitespace-nowrap px-2 py-2 md:px-4 rounded-md focus:outline-hidden focus:border-gray-300 hover:bg-gray-200';--}}
{{--    $selected = 'text-black bg-blue-100 rounded-md whitespace-nowrap px-2 py-2 md:px-4 focus:outline-hidden';--}}
{{--@endphp--}}
{{--<nav class="mt-4 py-3 flex flex-wrap gap-1 text-sm md:text-base font-normal border-b border-t border-gray-300">--}}
{{--    <span class="border-b-2 border-blue-600" style="margin-bottom: -13px;">--}}
{{--        <a href="{{ route('admin.users.view', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}"--}}
{{--           class="{{ request()->routeIs(['admin.users.view', 'admin.users.edit']) ? $selected : $unselected }}"--}}
{{--           aria-current="page">--}}
{{--            <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-info-circle"></i></span> Information--}}
{{--        </a>--}}
{{--    </span>--}}
{{--    <span class="border-b-2 border-transparent" style="margin-bottom: -1px;">--}}
{{--        <a href="{{ route('admin.users.groups-roles', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.groups-roles') ? $selected : $unselected }}">--}}
{{--            <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-users-crown"></i></span> Groups / Roles--}}
{{--        </a>--}}
{{--    </span>--}}
{{--    <span class="border-b-2 border-transparent" style="margin-bottom: -1px;">--}}
{{--        <a href="{{ route('admin.users.photos.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.photos.index') ? $selected : $unselected }}">--}}
{{--            <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-camera"></i></span> Photos--}}
{{--        </a>--}}
{{--    </span>--}}
{{--    @if(auth()->user()->account->hasFeature('feature.involvement'))--}}
{{--        <span class="border-b-2 border-transparent" style="margin-bottom: -1px;">--}}
{{--            <a href="{{ route('admin.users.involvement.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.involvement.index') ? $selected : $unselected }}">--}}
{{--                <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-check-square"></i></span> Involvement--}}
{{--            </a>--}}
{{--        </span>--}}
{{--    @endif--}}
{{--    @if(auth()->user()->account->hasFeature('feature.attendance'))--}}
{{--        <span class="border-b-2 border-transparent" style="margin-bottom: -1px;">--}}
{{--            <a href="{{ route('admin.users.attendance.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.attendance.index') ? $selected : $unselected }}">--}}
{{--                <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-clock"></i></span> Attendance--}}
{{--            </a>--}}
{{--        </span>--}}
{{--    @endif--}}
{{--    @if(auth()->user()->account->hasFeature('feature.online_giving'))--}}
{{--        <span class="border-b-2 border-transparent" style="margin-bottom: -1px;">--}}
{{--            <a href="{{ route('admin.users.giving.index', $user) . (request()->has('index_filters') ? '?index_filters=' . request()->get('index_filters') : null) }}" class="{{ request()->routeIs('admin.users.giving.index') ? $selected : $unselected }}">--}}
{{--                <span class="hidden sm:inline-block"><i class="mr-0.5 fal fa-hand-holding-usd"></i></span> Giving--}}
{{--            </a>--}}
{{--        </span>--}}
{{--    @endif--}}
{{--</nav>--}}