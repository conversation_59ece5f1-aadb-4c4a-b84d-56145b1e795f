<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        <th>First Name</th>
        <th>Last Name</th>
        <th>Head of Household</th>
        <th>Best Email</th>
        <th class="text-center">Home Phone</th>
        <th class="text-center">Mobile Phone</th>
        <th>Home Address</th>
        <th>Mailing Address</th>
        <th>Baptized</th>
        <th>Baptized Date</th>
        <th>Birthdate</th>
        <th>Family Role</th>
        <th>Marital Status</th>
        <th>Gender</th>
        <th>Membership Date</th>
        <td>Home address1</td>
        <td>Home address2</td>
        <td>Home address3</td>
        <td>Home city</td>
        <td>Home state</td>
        <td>Home zip</td>
        <td>Home country</td>
        <td>Mailing address1</td>
        <td>Mailing address2</td>
        <td>Mailing address3</td>
        <td>Mailing city</td>
        <td>Mailing state</td>
        <td>Mailing zip</td>
        <td>Mailing country</td>
    </tr>
    </thead>
    <tbody>
    @foreach($users as $user)
        @php
            $fam_address = $user->getFamilyAddress();
            $mail_address = $user->getMailingAddress();
        @endphp
        <tr>
            <td>
                {{ $user->display_first_name }}
            </td>
            <td>
                {{ $user->last_name }}
            </td>
            <td>
                @if($user->isHeadOfFamily())
                    Yes
                @endif
            </td>
            <td>
                @if($user->getBestEmail())
                    <a href="mailto:{{ optional($user->getBestEmail())->email }}">{{ optional($user->getBestEmail())->email }}</a>
                @endif
            </td>
            <td class="text-center">
                {{ optional($user->getFamilyPhone())->formattedNumber() }}
            </td>
            <td class="text-center">
                {{ optional($user->getMobilePhone())->formattedNumber() }}
            </td>
            <td class="text-center">
                {{ optional($fam_address)->getAddressString() }}
            </td>
            <td class="text-center">
                {{ optional($mail_address)->getAddressString() }}
            </td>
            <td>
                {{ $user->is_baptized ? 'Yes' : 'No' }}
            </td>
            <td>
                {{ $user->date_baptism ? $user->date_baptism?->format('Y-m-d') : '' }}
            </td>
            <td>
                {{ $user->birthdate ? $user->birthdate?->format('Y-m-d') : '' }}
            </td>
            <td>
                {{ $user->family_role }}
            </td>
            <td>
                {{ $user->marital_status }}
            </td>
            <td>
                {{ $user->gender }}
            </td>
            <td>
                {{ $user->date_membership ? $user->date_membership?->format('Y-m-d') : '' }}
            </td>
            <td>{{ $fam_address?->address1 }}</td>
            <td>{{ $fam_address?->address2 }}</td>
            <td>{{ $fam_address?->address3 }}</td>
            <td>{{ $fam_address?->city }}</td>
            <td>{{ $fam_address?->state }}</td>
            <td>{{ $fam_address?->zip }}</td>
            <td>{{ $fam_address?->county }}</td>
            <td>{{ $mail_address?->address1 }}</td>
            <td>{{ $mail_address?->address2 }}</td>
            <td>{{ $mail_address?->address3 }}</td>
            <td>{{ $mail_address?->city }}</td>
            <td>{{ $mail_address?->state }}</td>
            <td>{{ $mail_address?->zip }}</td>
            <td>{{ $mail_address?->county }}</td>
        </tr>
    @endforeach
    </tbody>
</table>