<form method="post" action="{{ route('admin.users.destroy', $user) }}">
    @method('delete')
    @csrf
    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <x-heroicon-s-trash class="w-5 text-red-500"/>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Delete User
            </h3>
            <div class="mt-4">
                <p><strong>Are you sure you want to delete {{ $user->name }}?</strong></p>
                <p class="p-2 rounded-md text-sm bg-purple-50 text-purple-600">
                    We recommend deactivating a user (remove groups &amp; roles) to keep historical information rather than deleting a user entirely.
                </p>
                <p class="mt-4 text-left font-bold">This will delete:
                <ul class="list-disc ml-10 text-left">
                    <li>User addresses</li>
                    <li>User phone numbers</li>
                    <li>User emails</li>
                    <li>User attendance</li>
                    <li>User involvement selections</li>
                    <li>User Bible class registration</li>
                    <li>User group & role associations</li>
                    <li>User shared family data (if head of family)</li>
                </ul>
                </p>
            </div>
        </div>
    </div>
    @if($user->groupPosts()->count() || $user->groupPostComments()->count())
        <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex text-red-500">
            Oops! This user has Group posts or comments and cannot be deleted, as this would leave those posts and comments without an author. Please delete the user's posts and comments first.
        </div>
    @else
        <div class=" sm:ml-14 mt-2 p-2 rounded-md text-sm bg-red-50 text-red-600">
            <strong>WARNING:</strong> You are about to permanently delete a large amount of information about a user from Lightpost.
        </div>
        <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex">
            <button type="submit" class="inline-flex justify-center w-full rounded-sm border border-transparent shadow-xs px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
                Confirm, Permanently Delete All User Data
            </button>
            <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                Cancel
            </button>
        </div>
    @endif
</form>