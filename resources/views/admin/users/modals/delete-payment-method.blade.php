<form method="post" action="{{ route('admin.users.giving.method.delete.submit', [$user, $payment_method]) }}">
    @method('delete')
    @csrf
    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <x-heroicon-s-trash class="w-5 text-red-500"/>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Delete Payment Method
            </h3>
            <div class="mt-4">
                <p><strong>Are you sure you want to delete this payment method?</strong></p>
                <p><mark>
                        This <strong>cannot</strong> be undone.
                    </mark></p>
                <div class="my-4 font-bold">
                    This will delete:
                </div>
                <a class="text-gray-900 font-semibold hover:text-gray-600 transition ease-in-out duration-150 text-lg">
                    {{ $payment_method->getStripeObject()->bank_name }}
                </a>
                <div class="text-gray-600 text-xs">
                    Bank account for <code class="bg-gray-100 px-1 rounded-sm py-1">{{ $payment_method->getStripeObject()->account_holder_name }}</code> ending in
                    <code class="font-medium bg-gray-100 px-1 rounded-sm py-1">{{ $payment_method->last4 }}</code>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex">
        <button type="submit" class="inline-flex justify-center w-full rounded-sm border border-transparent shadow-xs px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
            Confirm, Delete Payment Method
        </button>
        <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
        </button>
    </div>
</form>