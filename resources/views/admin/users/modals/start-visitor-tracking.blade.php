<form method="post" action="{{ route('admin.users.visitor-tracking.start.submit', $user) }}">
    {{ csrf_field() }}
    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <x-heroicon-s-cursor-arrow-rays class="w-6"/>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Add to Visitor Tracking
            </h3>
            <div class="mt-4">
                {{--                <select name="user_email_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-sm">--}}
                {{--                    @foreach($user->emails as $email)--}}
                {{--                        <option value="{{ $email->id }}">{{ $email->email }}</option>--}}
                {{--                    @endforeach--}}
                {{--                </select>--}}
                <select name="family_or_individual" name="type" class="">
                    <option value="family">Whole Family</option>
                    <option value="individual">Only this Individual</option>
                </select>
            </div>
        </div>
    </div>
    <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex align-middle">
        <button type="submit" class="admin-button-blue">
            <x-heroicon-s-check class="w-6 mr-1"/>
            Add to Visitor Tracking
        </button>
        <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
        </button>
    </div>
</form>