<form method="post" action="{{ route('admin.users.clear-sessions.submit', $user) }}">
    {{ csrf_field() }}
    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <x-heroicon-s-arrows-right-left class="h-6 w-6 text-blue-600"/>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Clear All Sessions
            </h3>
            <div class="mt-4">
                <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 rounded-sm">
                    <option>{{ $user->name }}</option>
                </select>
            </div>
            <div class="text-sm mt-2">
                This will:
                <ul class="list-disc text-sm pl-6">
                    <li>Log the user out of all web browsers.</li>
                    <li>Log the user out of all mobile devices.</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex">
        <button type="submit" class="inline-flex justify-center w-full rounded-sm border border-transparent shadow-xs px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
            Clear All Sessions
        </button>
        <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
        </button>
    </div>
</form>