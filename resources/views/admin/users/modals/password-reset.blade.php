<form method="post" action="{{ route('admin.users.password.reset.submit', $user) }}">
    {{ csrf_field() }}
    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Password Reset Email
            </h3>
            <div class="mt-4">
                <select name="user_email_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-sm">
                    @foreach($user->emails as $email)
                        <option value="{{ $email->id }}">{{ $email->email }}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>
    <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex">
        <button type="submit" class="inline-flex justify-center w-full rounded-sm border border-transparent shadow-xs px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
            Send Password Reset Email
        </button>
        <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
        </button>
    </div>
</form>