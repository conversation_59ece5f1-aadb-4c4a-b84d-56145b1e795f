@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.users.church-office.save', [$user, $church_office]) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus" aria-hidden="true"></i> Assign Church Office
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <label for="church_office_id" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Office
                            </label>
                        </div>
                        <div class="">
                            <select name="church_office_id" class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
                                <option selected="selected">{{ $church_office->name }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <label for="subtitle" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Subtitle
                            </label>
                        </div>
                        <div class="">
                            <input type="text" value="{{ $church_office->metadata?->subtitle }}" name="subtitle" placeholder="Technology / Finance" class="{{ $inputCSS }}" autocomplete="off"/>
                        </div>
                        <div class="text-sm text-gray-500">
                            Subtitles are a great way to describe a church role, such as the area of work a deacon is over.
                        </div>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                    @can('delete', $church_office)
                        <button type="button" onclick="document.getElementById('sidebar_delete_form').submit()" class="admin-button-red-transparent-small">Delete</button>
                    @endcan
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>


<form method="post" id="sidebar_delete_form" action="{{ route('admin.users.church-office.delete', [$user, $church_office]) }}">
    @csrf
    @method('DELETE')
    <button type="submit" class="admin-button-red-transparent-small">Delete</button>
</form>
