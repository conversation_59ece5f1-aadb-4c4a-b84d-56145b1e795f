@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.users.church-office.store', [$user]) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus" aria-hidden="true"></i> Assign Church Office
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <label for="church_office_id" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Office
                            </label>
                        </div>
                        <div class="">
                            <select name="church_office_id" class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
                                @foreach(auth()->user()->account->churchOffices as $church_office)
                                    <option value="{{ $church_office->id }}">{{ $church_office->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="text-sm text-gray-500">
                            Need to create a new office?
                            <br/>Create/edit them in <a href="{{ route('admin.accounts.settings.church-offices.index') }}">Account Settings</a>.
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <label for="subtitle" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Subtitle
                            </label>
                        </div>
                        <div class="">
                            <input type="text" name="subtitle" placeholder="Technology / Finance" class="{{ $inputCSS }}" autocomplete="off"/>
                        </div>
                        <div class="text-sm text-gray-500">
                            Subtitles are a great way to describe a church role, such as the area of work a deacon is over.
                        </div>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>
