@extends('admin._layouts._app')

@section('title', 'User View')

@section('content')

    <style>
        input[type='text'], input[type='password'], input[type='date'], select {
            padding: 0.3em 0.5em 0.3em 0.5em;
        }
    </style>

    <div class="admin-standard-col-width">
        {{--        <!-- Page header -->--}}
        {{--    @include('admin.layouts.breadcrumbs', [--}}
        {{--        'links' => [--}}
        {{--            'People' => route('admin.users.index'),--}}
        {{--            'View User' => null--}}
        {{--        ]--}}
        {{--    ])--}}

        @include('admin.users._layouts._user-view-header')

        <div>
            <form id="save_user_edit_form" method="post" action="{{ route('admin.users.save', $user) }}">
                @csrf

                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <div class="pb-2">
                            <h3 class="text-xl font-semibold text-gray-600">
                                Name
                            </h3>
                        </div>
                        <div class="bg-white admin-section-border sm:rounded-lg">
                            <div class="px-4 py-5 sm:p-0">
                                <dl class="grid grid-cols-1 divide-y divide-gray-300">
                                    <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Preferred First Name:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="preferred_first_name" type="text" value="{{ $user->preferred_first_name }}" placeholder="" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Given First Name:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="first_name" type="text" value="{{ $user->first_name }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Middle Name:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="middle_name" type="text" value="{{ $user->middle_name }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Last Name:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="last_name" type="text" value="{{ $user->last_name }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Name Suffix:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="suffix_name" type="text" value="{{ $user->suffix_name }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Maiden Name:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="maiden_name" type="text" value="{{ $user->maiden_name }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                        <div class="pb-2 mt-4">
                            <h3 class="text-xl font-semibold text-gray-600">
                                General Information
                            </h3>
                        </div>
                        <div class="bg-white admin-section-border sm:rounded-lg">
                            <div class="px-4 py-5 sm:p-0">
                                <dl class="grid grid-cols-1 divide-y divide-gray-300">
                                    <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Birthdate
                                        </dt>
                                        <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                            <input name="birthdate" type="date" value="{{ optional($user->birthdate)->format('Y-m-d') }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Gender
                                        </dt>
                                        <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                            <select name="gender" class="">
                                                @foreach(\App\Users\User::$genders as $index => $value)
                                                    <option value="{{ $index }}" {{ old('gender') ? 'selected' : ($user->gender == $index ? 'selected' : null) }}>{{ $value }}</option>
                                                @endforeach
                                            </select>
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Marital status
                                        </dt>
                                        <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                            <select name="marital_status" class="">
                                                @foreach(\App\Users\User::$marital_statuses as $index => $value)
                                                    <option value="{{ $index }}" {{ old('marital_status') ? 'selected' : ($user->marital_status == $index ? 'selected' : null) }}>{{ $value }}</option>
                                                @endforeach
                                            </select>
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Married on
                                        </dt>
                                        <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                            <input name="date_married" type="date" value="{{ optional($user->date_married)->format('Y-m-d') }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Username
                                        </dt>
                                        <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                            <input name="user_name" type="text" value="{{ $user->user_name }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2 align-content-center">
                                        <dt class="text-gray-500 my-auto">
                                            Password
                                        </dt>
                                        <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                            <input name="safe_fill_1" type="password" autocomplete="new-password" data-lpignore="true">
                                            <div class="text-gray-300 text-xs">Leave blank for no change.</div>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="py-2">
                                <h3 class="text-xl font-semibold text-gray-600">
                                    Extended Information
                                </h3>
                            </div>
                            <div class="bg-white admin-section-border rounded-sm">
                                <div class="px-4 py-5 sm:p-0">
                                    <dl class="grid grid-cols-1 divide-y divide-gray-300">
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Allergies
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="allergies" type="text" value="{{ $user->allergies }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Blood type
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <select name="blood_type" class="form-control form-control-sm">
                                                    <option value="">--</option>
                                                    @foreach(\App\Users\User::$blood_types as $index => $value)
                                                        <option value="{{ $index }}" {{ old('blood_type') ? 'selected' : ($user->blood_type ? 'selected' : null) }}>{{ $value }}</option>
                                                    @endforeach
                                                </select>
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                School attending
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="school_attending" type="text" value="{{ $user->school_attending }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                School grade
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <select name="user_grade_id" class="form-control form-control-sm">
                                                    <option value=""> --</option>
                                                    @foreach(\App\Accounts\Grade::visibleTo($user)->orderBy('sort_id', 'asc')->get() as $grade)
                                                        <option value="{{ $grade->id }}" {{ old('user_grade_id') ? 'selected' : ($user->user_grade_id == $grade->id ? 'selected' : null) }}>{{ $grade->name }}</option>
                                                    @endforeach
                                                </select>
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Employer
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="employer" type="text" value="{{ $user->employer }}" autocomplete="off" data-lpignore="true">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Job title
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="job_title" type="text" value="{{ $user->job_title }}" autocomplete="off" data-lpignore="true">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Job keywords
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="job_keywords" type="text" value="{{ $user->job_keywords }}" autocomplete="off" data-lpignore="true">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Notes
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <textarea rows="6" class="form-control form-control-sm" name="notes" autocomplete="off" data-lpignore="true">{{ $user->notes }}</textarea>
                                            </dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="">
                            <div class="pb-2">
                                <h3 class="text-xl font-semibold text-gray-600">
                                    Family Information
                                </h3>
                            </div>
                            <div class="bg-white admin-section-border rounded-sm" x-data="{ family_id: '{{ $user->family_id }}', selected_family_role: '{{ $user->family_role }}',  current_family_role: '{{ $user->family_role }}', family_members_count: {{ $user->familyMembers->count() }} }">
                                <div class="px-4 py-5 sm:p-0">
                                    <dl class="grid grid-cols-1 divide-y divide-gray-300">
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Belongs to Family
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <select name="family_id" data-placeholder="No family assigned." id="family_id_select" class="w-full">
                                                    <option></option>
                                                    @foreach(\App\Users\User::getAllFamilies()->visibleTo(Auth::user())->get() as $family)
                                                        <option value="{{ $family->id }}"{{ ($family->id == $user->family_id ? ' selected="selected"' : '') }}>{{ $family->last_name.', '.$family->first_name }}</option>
                                                    @endforeach
                                                </select>
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Family Role
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <select x-model="selected_family_role" name="family_role" class="w-full">
                                                    @foreach(\App\Users\User::$family_roles as $index => $value)
                                                        <option value="{{ $index }}" {{ old('family_role') ? 'selected' : ($user->family_role == $index ? 'selected' : null) }}>{{ $value }}</option>
                                                    @endforeach
                                                </select>
                                                {{-- This will show a warning if a current head of household is being changed to something else --}}
                                                <div x-cloak x-show="current_family_role == 'head' && selected_family_role != 'head' && current_family_role != selected_family_role && family_members_count > 1"
                                                     class="mt-2 px-2 py-1 rounded-sm border-red-300 border-l-4 bg-red-100 text-red-600 text-sm">
                                                    <strong>This will create a new family unit!</strong>
                                                    <br>Current family members will need to be reassigned a family or they will not show in the directory.
                                                    <br>It's recommended to re-assign other family members to a new family unit first.
                                                </div>
                                                {{-- This will show a warning if a we're creating a new head of household --}}
                                                <div x-cloak x-show="current_family_role != 'head' && selected_family_role == 'head' && current_family_role != selected_family_role && family_members_count > 1"
                                                     class="mt-2 px-2 py-1 rounded-sm border-amber-300 border-l-4 bg-amber-100 text-amber-600 text-sm">
                                                    <strong>This will create a new family unit!</strong>
                                                    <br>This user will be the head of the new family unit.
                                                </div>
                                            </dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="py-2">
                                <h3 class="text-xl font-semibold text-gray-600">
                                    Membership Information
                                </h3>
                            </div>
                            <div class="bg-white admin-section-border rounded-sm">
                                <div class="px-4 py-5 sm:p-0">
                                    <dl class="grid grid-cols-1 divide-y divide-gray-300">
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Church Office
                                            </dt>
                                            <dd class="mt-1 text-gray-600 sm:mt-0 sm:col-span-2 text-sm">
                                                Church offices have been moved to the
                                                <a href="{{ route('admin.users.view', $user) }}">main user profile</a>
                                                as an option to add from there.
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                <label class="custom-control-label" for="checkbox_exclude_from_reports">
                                                    Exclude from reports?
                                                </label>
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input type="hidden" name="exclude_from_reports" value="0"/>
                                                <input type="checkbox" class="custom-control-input" id="checkbox_exclude_from_reports" name="exclude_from_reports" value="1" @isChecked($user->exclude_from_reports)/>
                                            </dd>
                                        </div>
                                        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                <label class="custom-control-label" for="checkbox_can_teach">Approved to teach?</label>
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input type="hidden" name="can_teach" value="0"/>
                                                <input type="checkbox" id="checkbox_can_teach" name="can_teach" value="1" @isChecked($user->can_teach)/>
                                            </dd>
                                        </div>
                                        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                <label class="custom-control-label" for="checkbox_can_lead">Approved to Lead?</label>
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input type="hidden" name="can_lead" value="0"/>
                                                <input type="checkbox" id="checkbox_can_lead" name="can_lead" value="1" @isChecked($user->can_lead)/>
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Background Check Completed?
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="date_background_check" type="date" value="{{ optional($user->date_background_check)->format('Y-m-d') }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                <label class="custom-control-label" for="checkbox_is_baptized">
                                                    Is baptized?
                                                </label>
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input type="hidden" name="is_baptized" value="0"/>
                                                <input type="checkbox" class="custom-control-input" id="checkbox_is_baptized" name="is_baptized" value="1" @isChecked($user->is_baptized)/>
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Baptism date
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="date_baptism" type="date" value="{{ optional($user->date_baptism)->format('Y-m-d') }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Membership date
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="date_membership" type="date" value="{{ optional($user->date_membership)->format('Y-m-d') }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Became member by
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="member_by" type="text" value="{{ $user->member_by }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Date departed
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="date_departed" type="date" value="{{ optional($user->date_departed)->format('Y-m-d') }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Departed reason
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="departed_reason" type="text" value="{{ $user->departed_reason }}" autocomplete="off">
                                            </dd>
                                        </div>
                                        <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                            <dt class="text-gray-500 my-auto">
                                                Date deceased
                                            </dt>
                                            <dd class="mt-1 text-gray-900 sm:mt-0 sm:col-span-2">
                                                <input class="form-control form-control-sm" name="date_deceased" type="date" value="{{ optional($user->date_deceased)->format('Y-m-d') }}" autocomplete="off">
                                            </dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

        </div>

    </div>

@endsection