@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.users.address.store', $user) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    {{ csrf_field() }}
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i>Create Address
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="label" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Address Label
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <input type="text" name="label" placeholder="Label" class="{{ $inputCSS }}" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Type
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <select name="type" class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-hidden focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                @foreach(\App\Users\Address::$types as $type => $name)
                                    <option value="{{ $type }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="project-name" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Address
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <input type="text" name="address1" placeholder="Address" class="{{ $inputCSS }}" autocomplete="off"/>
                            <input type="text" name="address2" placeholder="Apt #, PO box, etc." class="{{ $inputCSS }} mt-1" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-0 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="project-name" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-3">
                                City
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <input type="text" name="city" placeholder="City" class="{{ $inputCSS }}" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-0 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="project-name" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-3">
                                State
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <div class="w-1/2"><input type="text" name="state" placeholder="State" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-0 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="project-name" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-3">
                                Zip Code
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <div class="w-1/2"><input type="text" name="zip" placeholder="Zip" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                        </div>
                    </div>
                </div>
                <div class="py-3 sm:px-5">
                    <div class="space-y-3 py-3">
                        <fieldset>
                            <legend class="text-sm font-medium text-gray-900">
                                Address Options
                            </legend>
                            <div class="mt-2 space-y-3 sm:ml-4">
                                <div class="relative flex items-start">
                                    <div class="absolute flex items-center h-5">
                                        <input type="checkbox" class="{{ $checkboxCSS }}" id="is_family" name="is_family" value="1"/>
                                    </div>
                                    <div class="pl-7 text-sm">
                                        <label for="is_family" class="font-medium text-gray-900">
                                            Belongs to the whole family?
                                            <p class="text-gray-500 font-normal">
                                                This address will be attached to every member of the family.
                                            </p>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_mailing" name="is_mailing" value="1"/>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_mailing" class="font-medium text-gray-900">
                                                Is mailing address?
                                                <p class="text-gray-500 font-normal">
                                                    Indicate this address is only for mailing purposes.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="is_hidden" name="is_hidden" value="1"/>
                                        </div>
                                        <div class="pl-7 text-sm">
                                            <label for="is_hidden" class="font-medium text-gray-900">
                                                Is hidden?
                                                <p class="text-gray-500 font-normal">
                                                    This address will be hidden from the church directory.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>
