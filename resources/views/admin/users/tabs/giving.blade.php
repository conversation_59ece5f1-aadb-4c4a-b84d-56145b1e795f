@extends('admin._layouts._app')

@section('title', 'User View')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin.users._layouts._user-view-header')

        <div class="mt-4">
            <div class="pb-2">
                <h3 class="text-xl font-semibold text-gray-600">
                    Giving Methods
                </h3>
            </div>
            <ul class="grid grid-cols-1 gap-5 sm:gap-6 md:grid-cols-3">
                @forelse($payment_methods as $method)
                    @if($method->type == 'bank_account')
                        <li class="col-span-2 max-w-xl flex rounded-lg">
                            <div class="flex-1 flex items-center justify-between border admin-border-color bg-white rounded-sm break-words">
                                <div class="flex-1 px-4 py-2 break-words">
                                    <a class="text-gray-900 font-semibold hover:text-gray-600 transition ease-in-out duration-150 text-lg">
                                        {{ $method->getStripeObject()->bank_name }}
                                    </a>
                                    <div class="text-gray-600 text-xs">
                                        Bank account for <code class="bg-gray-100 px-1 rounded-sm py-1">{{ $method->getStripeObject()->account_holder_name }}</code> ending in
                                        <code class="font-medium bg-gray-100 px-1 rounded-sm py-1">{{ $method->last4 }}</code>
                                    </div>
                                    <div class="my-1 flex flex-row justify-between">
                                        @if($method->getStripeObject()->status == 'new')
                                            <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-gray-200 text-gray-600">
                                                <i class="fas fa-exclamation-triangle"></i> &nbsp; Pending Verification
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                                <i class="fas fa-badge-check"></i> &nbsp; Verified
                                            </span>
                                        @endif
                                        <a class="card-link float-right text-sm text-red-500 cursor-pointer" onclick="openModal('{{ route('admin.users.giving.method.delete', [$method->user, $method]) }}')">Delete</a>
                                    </div>
                                </div>
                                <div class="shrink-0 pr-2 hidden">
                                    <button class="w-8 h-8 inline-flex items-center justify-center text-gray-500 rounded-full bg-transparent hover:text-gray-700 focus:outline-hidden focus:text-gray-700 focus:bg-gray-100 transition ease-in-out duration-150">
                                        <!-- Heroicon name: dots-vertical -->
                                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </li>
                    @elseif($method->type == 'card')
                        <li class="col-span-1 flex shadow-xs rounded-lg">
                            <div class="flex-1 flex items-center justify-between border-t border-l border-b border-gray-300 bg-white rounded-l-md break-words">
                                <div class="flex-1 px-4 py-2 break-words">
                                    <a class="text-gray-900 font-semibold hover:text-gray-600 transition ease-in-out duration-150 text-lg">
                                        {{ \Illuminate\Support\Str::ucfirst($method->getStripeObject()->brand) }}
                                    </a>
                                    <div class="text-gray-600 text-xs">
                                        Card ending in <code class="bg-gray-100 px-1 rounded-sm py-1">{{ $method->last4 }}</code>, expires on
                                        <code class="font-medium bg-gray-100 px-1 rounded-sm py-1">{{ $method->expire_month }}/{{ $method->expire_year }}</code>
                                    </div>
                                    <div class="my-1">
                                    <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-badge-check"></i> &nbsp; Verified
                                    </span>
                                    </div>
                                </div>
                                <div class="shrink-0 pr-2 hidden">
                                    <button class="w-8 h-8 inline-flex items-center justify-center text-gray-500 rounded-full bg-transparent hover:text-gray-700 focus:outline-hidden focus:text-gray-700 focus:bg-gray-100 transition ease-in-out duration-150">
                                        <!-- Heroicon name: dots-vertical -->
                                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                        </svg>
                                    </button>
                                </div>
                                {{--                                <form method="post" action="{{ route('app.giving.delete-payment-method.submit', $method) }}" class="hidden">--}}
                                {{--                                    @csrf--}}
                                {{--                                    @method('delete')--}}
                                {{--                                    <button type="submit" class="card-link float-right btn btn-outline-danger btn-sm">Delete</button>--}}
                                {{--                                </form>--}}
                            </div>
                            <a href="{{ route('app.giving.give', $method) }}" class="shrink-0 flex font-medium items-center justify-center w-24 bg-blue-600 text-white rounded-r-md hover:bg-blue-500 transition ease-in-out duration-150">
                                <i class="fas fa-hand-holding-heart"></i> &nbsp;Give
                            </a>
                        </li>
                    @endif
                @empty
                    <li class="col-span-1 flex rounded-lg">
                        <div class="flex-1 border border-gray-300 bg-white rounded-lg py-4">
                            <div class="bg-yellow-100 p-4 text-center" role="alert">
                                No credit/debit cards or bank accounts on file.
                            </div>
                        </div>
                    </li>
                @endforelse
            </ul>
        </div>

    </div>

@endsection