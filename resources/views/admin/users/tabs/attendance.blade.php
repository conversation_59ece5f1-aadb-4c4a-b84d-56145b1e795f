@extends('admin._layouts._app')

@section('title', 'User View')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin.users._layouts._user-view-header')

        <div class="flex flex-col space-y-6 mt-6">

            @foreach($attendance_types as $type)
                <div class="">
                    <h3 class="text-xl font-medium">{{ $type->type->name }}</h3>
                    <table class="mt-2 border-collapse w-full bg-white border admin-border-color rounded-md">
                        <thead>
                        <tr class="text-center">
                            @foreach($attendance_dates as $date)
                                @if($date->user_attendance_type_id == $type->user_attendance_type_id)
                                    <th class="p-2">
                                        {{ $date->date_attendance->format('m/d') }}
                                    </th>
                                @endif
                            @endforeach
                        </tr>
                        </thead>
                        <tr class="text-center border-t admin-border-color">
                            @foreach($attendance_dates as $date)
                                @if($date->user_attendance_type_id == $type->user_attendance_type_id)
                                    <td class="p-2">
                                        @if($attendance->where('user_attendance_type_id', $type->type->id)->contains('date_attendance', $date->date_attendance))
                                            <i class="fas fa-check"></i>
                                        @endif
                                    </td>
                                @endif
                            @endforeach
                        </tr>
                    </table>
                </div>
            @endforeach

        </div>

    </div>

@endsection