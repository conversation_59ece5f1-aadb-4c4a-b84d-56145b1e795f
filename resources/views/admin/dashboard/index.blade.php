@extends('admin._layouts._app')

@section('title', 'Dashboard')

@section('content')

    <div class="admin-standard-col-width">

        @livewire('admin.dashboard.dashboard', [
            'labels' => $labels,
        ])

    </div>

    @push('scripts')

        <script src="https://code.highcharts.com/highcharts.js"></script>

        <script>
            document.addEventListener('DOMContentLoaded', event => {
                // Fetch data from the API endpoint
                fetch('{{ route('admin.dashboard.api.activity-chart-data') }}')
                    .then(response => response.json())
                    .then(data => {
                        const chart = Highcharts.chart('activityChartContainer', {
                            chart: {
                                type: 'column',
                                animation: null,
                            },
                            xAxis: {
                                type: 'category',
                                categories: {!! json_encode($labels) !!},
                                crosshair: true,
                                accessibility: {
                                    description: 'Dates'
                                }
                            },
                            data: {
                                columnsURL: '{{ route('admin.dashboard.api.activity-chart-data') }}'
                            },
                            yAxis: {
                                floor: 0,
                                allowDecimals: false,
                                title: {
                                    text: null
                                },
                                accessibility: {
                                    description: 'Number of users'
                                },
                                showFirstLabel: false,
                                labels: {
                                    enabled: false
                                }
                            },
                            legend: {
                                enabled: false
                            },
                            tooltip: {
                                valueSuffix: ''
                            },
                            title: null,
                            plotOptions: {
                                column: {
                                    pointPadding: 0.1,
                                    borderWidth: 0
                                }
                            },
                            series: [{
                                name: 'Users',
                                data: data
                            }]
                        });
                    })
                    .catch(error => console.error('Error fetching data:', error));
            });
        </script>

    @endpush

@endsection
