@extends('admin._layouts._app')

@section('title', 'Podcast - ' . $podcast->title)

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.podcasts.index'),
            'text' => 'Podcasts',
        ], [
            'text' => 'Podcast',
        ]]])

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Podcast - {{ $podcast->title }}
                </h1>
            </div>
        </div>

        <main class="relative admin-section mt-4">
            <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                @include('admin.podcasts.podcasts-sidebar')

                <div class="divide-y divide-gray-200 lg:col-span-9">
                    <div class="py-6 px-4 sm:p-6 lg:pb-8">
                        <div class="col-md-9">
                            <h3>Podcast Share URL</h3>
                            <div class="my-2 border border-gray-300 rounded-sm p-3 ">
                                <a href="{{ $podcast->getPodcastUrl() }}" class="text-sm font-mono">{{ $podcast->getPodcastUrl() }}</a>
                            </div>

                            <h3>Podcast Feed URL</h3>
                            <div class="my-2 border border-gray-300 rounded-sm p-3 ">
                                <a href="{{ $podcast->getPodcastFeedUrl() }}" class="text-sm font-mono">{{ $podcast->getPodcastFeedUrl() }}</a>
                            </div>

                            <h3 class="mt-4">Stats</h3>
                            <table class="border border-gray-300" style="width: 400px">
                                <tbody>
                                <tr class="">
                                    <td class="py-2 px-2">
                                        Total Episodes
                                    </td>
                                    <td class="text-center">
                                        {{ $podcast->tracks()->count() }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 px-2">
                                        Total Downloads
                                    </td>
                                    <td class="text-center">
                                        {{ $podcast->downloads()->count() }}
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                            <h3 class="mt-4">Publishing Destinations</h3>
                            <table class="border border-gray-300" style="width: 400px" cellpadding="4">
                                <thead>
                                <tr class="bg-gray-700 text-white">
                                    <th>Destination</th>
                                    <th style="width: 120px">Status</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($podcast->destinations as $destination)
                                    <tr class="even:bg-gray-100 odd:bg-white">
                                        <td class="py-2 px-2">
                                            @if($destination->is_active)
                                                <span style="color: LimeGreen; font-size: 1.4em; float: right; padding-right: 6px; padding-top:2px;">
                                                            <i class="fas fa-badge-check float-left" data-toggle="tooltip" data-placement="left" title="Published"></i>
                                                        </span>
                                            @elseif($destination->is_pending)
                                                <span style="color: SandyBrown; font-size: 1.4em; float: right; padding-right: 6px; padding-top:2px;">
                                                            <i class="fas fa-clock float-left"></i>
                                                        </span>
                                            @elseif($destination->is_error)

                                            @endif
                                            @if($destination->direct_link)
                                                <a href="{{ $destination->direct_link }}">
                                                    {{ $destination->name }}
                                                </a>
                                            @else
                                                {{ $destination->name }}
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($destination->is_active)
                                                Active
                                            @elseif($destination->is_pending)
                                                Pending
                                            @elseif($destination->is_error)
                                                ERROR
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>

                            <h4 class="mt-4">Episodes</h4>
                            <table class="border border-gray-300" cellpadding="3">
                                <thead class="">
                                <tr class="bg-gray-700 text-white">
                                    <th>Title</th>
                                    <th style="width: 120px">Published At</th>
                                    <th style="width: 120px" class="text-center">Downloads</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($podcast->tracks()->orderBy('published_at', 'desc')->get() as $track)
                                    <tr class="even:bg-gray-100 odd:bg-white">
                                        <td class="py-2 px-2">
                                            @if($track->has_sync_error)
                                                <span style="color: OrangeRed; float: left; padding-right: 6px; padding-top: 4px">
                                                            <i class="fas fa-do-not-enter float-left"></i>
                                                        </span>
                                                <button class="btn btn-danger btn-sm text-white float-right" style="margin-left: 10px;">Sync Error</button>
                                                <button onclick="document.getElementById('resync-{{ $track->id }}').submit()" type="button" class="float-right btn btn-outline-primary btn-sm">Re-sync</button>
                                                <form action="{{ route('admin.podcasts.resync', [$podcast, $track]) }}" method="post" id="resync-{{ $track->id }}">
                                                    @csrf
                                                </form>
                                                @if($track?->sermon)
                                                    <a href="{{ route('admin.sermons.edit', $track->sermon) }}">
                                                        {{ $track->title }}
                                                    </a>
                                                @else
                                                    {{ $track->title }}
                                                @endif
                                            @else
                                                <span style="color: LimeGreen; float: left; padding-right: 6px; padding-top: 4px">
                                                            <i class="fas fa-check-circle float-left" data-toggle="tooltip" data-placement="left" title="Published"></i>
                                                        </span>
                                                {{ $track->title }}
                                            @endif
                                        </td>
                                        <td class="py-2 px-2">
                                            {{ $track->published_at?->format('M d, Y') }}
                                        </td>
                                        <td class="text-center">
                                            {{ $track->downloads()->count() }}
                                        </td>
                                    </tr>
                                    @if($track->has_sync_error)
                                        <tr>
                                            <td colspan="3" style="padding-left: 20px; font-size: 80%;">
                                                {{ $track->sync_error_message }}
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-Piv4xVNRyMGpqkS2by6br4gNJ7DXjqk09RmUpJ8jgGtD7zP9yug3goQfGII0yAns" crossorigin="anonymous"></script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>
@endpush

