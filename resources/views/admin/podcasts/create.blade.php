@extends('admin._layouts._app')

@section('title', 'Account Files')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.podcasts.index'),
            'text' => 'Podcasts',
        ], [
            'text' => 'Create',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                Create New Podcast
                            </h1>
                        </div>
                    </div>
                </div>
            </div>

            <main class="relative admin-section">
                <div class="mx-auto max-w-(--breakpoint-2xl) ">
                    <div class="overflow-hidden rounded-lg bg-white">
                        {{-- to change table size Match this div below with the other div below--}}
                        <div class="divide-y divide-gray-200 grid lg:grid lg:grid-cols-10 lg:divide-y-0 lg:divide-x">
                            @php
                                $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                                $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                            @endphp
                            {{-- to change table size Match this div below with the div above--}}
                            <div class="divide-y divide-gray-200 lg:col-span-10">
                                <div class="py-6 px-4 sm:p-6 lg:pb-8">

                                    <form action="{{ route('admin.podcasts.store') }}" method="post" enctype="multipart/form-data" id="pageForm">
                                        {{ csrf_field() }}

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-12">
                                                <div class="form-group my-2">
                                                    <label for="title">Title</label>
                                                    <input type="text" name="title" class="form-control{{ $errors->has('title') ? ' is-invalid' : '' }}" value="{{ old('title') }}" autocomplete="off">
                                                    @if ($errors->has('title'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('title') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-12">
                                                <div class="form-group my-2">
                                                    <label for="subtitle">Subtitle</label>
                                                    <span class="float-right text-muted" style="font-size: 80%;">A short, one sentence description of this podcast.</span>
                                                    <input type="text" name="subtitle" class="form-control{{ $errors->has('subtitle') ? ' is-invalid' : '' }}" value="{{ old('subtitle') }}" autocomplete="off">
                                                    @if ($errors->has('subtitle'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('subtitle') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-12">
                                                <div class="form-group my-2">
                                                    <label for="summary">Summary</label>
                                                    <span class="float-right text-muted" style="font-size: 80%;">A short explanation of this podcast.</span>
                                                    <textarea name="summary" rows="2" class="form-control{{ $errors->has('summary') ? ' is-invalid' : '' }}">{{ old('summary') }}</textarea>
                                                    @if ($errors->has('summary'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('summary') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-12">
                                                <div class="form-group my-2">
                                                    <label for="description">Description</label>
                                                    <span class="float-right text-muted" style="font-size: 80%;">A longer description with details of the podcast, authors and perhaps your congregation.</span>
                                                    <textarea name="description" rows="4" class="form-control{{ $errors->has('description') ? ' is-invalid' : '' }}">{{ old('description') }}</textarea>
                                                    @if ($errors->has('description'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('description') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-3">
                                                <div class="form-group my-2">
                                                    <label for="published_at">Published Date</label>
                                                    <input type="date" name="published_at" class="form-control w-full {{ $errors->has('published_at') ? ' is-invalid' : '' }}" value="{{ old('published_at') }}" autocomplete="off" placeholder="i.e. AM , PM">
                                                    @if ($errors->has('published_at'))
                                                        <span class=" invalid-feedback">
                                                <strong>{{ $errors->first('published_at') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-span-5">
                                                <div class="form-group my-2">
                                                    <label for="category">Category</label>
                                                    <select name="category" id="category" class="form-control col-md-10">
                                                        @foreach(\App\Podcasts\Podcast::$categories as $category)
                                                            <option value="{{ $category }}" {{ old('category') == $category ? 'selected="selected"' : '' }}>{{ $category }}</option>
                                                        @endforeach
                                                    </select>
                                                    @if ($errors->has('category'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('category') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>


                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-12">
                                                <div class="form-group my-2">
                                                    <label for="author">Author Name</label>
                                                    <input type="text" name="author" class="form-control{{ $errors->has('author') ? ' is-invalid' : '' }}" value="{{ old('author') ?: auth()->user()->account->name }}">
                                                    @if ($errors->has('author'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('author') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-span-12">
                                                <div class="form-group my-2">
                                                    <label for="location">Location</label>
                                                    <input type="text" name="location" placeholder="i.e. Katy, Texas" class="form-control{{ $errors->has('location') ? ' is-invalid' : '' }}" value="{{ old('location') }}">
                                                    @if ($errors->has('location'))
                                                        <span class="invalid-feedback">
                                        <strong>{{ $errors->first('location') }}</strong>
                                    </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>


                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-12">
                                                <div class="form-group my-2">
                                                    <label for="author_email">Author Email</label>
                                                    <input type="text" name="author_email" class="form-control{{ $errors->has('author_email') ? ' is-invalid' : '' }}" value="{{ old('author_email') }}">
                                                    @if ($errors->has('author_email'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('author_email') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-span-3">
                                                <div class="form-group my-2">
                                                    <label for="country_of_origin">Country of Origin</label>
                                                    <select name="country_of_origin" id="country_of_origin" class="form-control col-md-10">
                                                        <option value="US">United States</option>
                                                    </select>
                                                    @if ($errors->has('country_of_origin'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('country_of_origin') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="col-span-3">
                                                <div class="form-group my-2">
                                                    <label for="language">Language</label>
                                                    <select name="language" id="language" class="form-control">
                                                        @foreach(\App\Sermons\Sermon::$available_languages as $code => $title)
                                                            <option value="{{ $code }}" {{ old('language') == $code ? 'selected="selected"' : '' }}>{{ $title }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-span-3">
                                                <div class="form-group my-2">
                                                    <label for="timezone">Timezone</label>
                                                    <select name="timezone" id="timezone" class="form-control">
                                                        @foreach(\App\Podcasts\Podcast::$timezones as $timezone => $name)
                                                            <option value="{{ $timezone }}" {{ old('timezone') == $timezone ? 'selected="selected"' : '' }}>{{ $name }}</option>
                                                        @endforeach
                                                    </select>
                                                    @if ($errors->has('timezone'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('timezone') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-6">
                                                <div class="form-group my-2">
                                                    <label for="copyright">Copyright</label>
                                                    <input type="text" name="copyright" class="form-control{{ $errors->has('copyright') ? ' is-invalid' : '' }}" value="{{ old('copyright') ?: 'Copyright ' . auth()->user()->account->name }}">
                                                    @if ($errors->has('copyright'))
                                                        <span class="invalid-feedback">
                                        <strong>{{ $errors->first('copyright') }}</strong>
                                    </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-6">
                                                <div class="form-group my-2">
                                                    <label for="type">Type</label>
                                                    <select name="type" id="type" class="form-control w-full">
                                                        <option value="episodic" {{ old('type') == 'episodic' ? 'selected="selected"' : '' }}>Episodic - Newest first</option>
                                                        <option value="serial" {{ old('type') == 'serial' ? 'selected="selected"' : '' }}>Serial - Oldest first</option>
                                                    </select>
                                                    @if ($errors->has('type'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('type') }}</strong>
                                            </span>
                                                    @endif
                                                    <div x-data="{ open: false }">
                                                        <a @click="open = !open" style="font-size:80%; cursor: pointer; color: blue; float: right"><span x-text="open ? 'Close' : 'View'"></span> Details</a>
                                                        <div x-show="open" x-cloak style="display:none;">
                                                            <strong>Episodic</strong>
                                                            <br>
                                                            Specify episodic when episodes are intended to be consumed without any specific order. Apple Podcasts will present newest episodes first and display the publish date (required) of each episode. If organized into seasons, the newest season will be presented first - otherwise, episodes will be grouped by year published, newest first.
                                                            <br>
                                                            For new subscribers, Apple Podcasts adds the newest, most recent episode in their Library.
                                                            <br><br>
                                                            <strong>Serial</strong>
                                                            <br>
                                                            Specify serial when episodes are intended to be consumed in sequential order. Apple Podcasts will present the oldest episodes first and display the episode numbers (required) of each episode.
                                                            If organized into seasons, the newest season will be presented first and
                                                            episode numbers <em>must</em> be given for each episode.
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group my-2">
                                                    <label for="frequency">Frequency</label>
                                                    <select name="frequency" id="frequency" class="form-control">
                                                        @foreach (\App\Podcasts\Podcast::$frequency as $value => $name)
                                                            <option value="{{ $value }}">{{ $name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            {{--                    <div class="col-md-2">--}}
                                            {{--                        <div class="form-group my-2">--}}
                                            {{--                            <label for="episode_order">Episode Order</label>--}}
                                            {{--                            <select name="episode_order" id="episode_order" class="form-control">--}}
                                            {{--                                <option value="desc">Newest first</option>--}}
                                            {{--                                <option value="asc">Oldest first</option>--}}
                                            {{--                            </select>--}}
                                            {{--                        </div>--}}
                                            {{--                    </div>--}}
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-6">
                                                <div class="form-group my-2">
                                                    <label for="">Sermon Tag</label>
                                                    <select name="sermon_tag_id" id="tags" class="form-control w-full">
                                                        <option value=""></option>
                                                        @foreach ($tags as $tag)
                                                            <option value="{{ $tag->id }}" {{ request()->get('sermon_tag_id') == $tag->id ? 'selected' : null }}>{{ $tag->name }}</option>
                                                        @endforeach
                                                    </select>
                                                    <small class="form-text text-muted">This will be used to automatically add podcast audio/tracks from uploaded Sermons that are tagged the same.</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-6">
                                                <div class="form-group my-2">
                                                    <label for="image_title">Cover Image Title</label>
                                                    <input type="text" name="image_title" class="form-control{{ $errors->has('image_title') ? ' is-invalid' : '' }}" value="{{ old('image_title') }}" autocomplete="off">
                                                    @if ($errors->has('image_title'))
                                                        <span class="invalid-feedback">
                                                <strong>{{ $errors->first('image_title') }}</strong>
                                            </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-6">
                                                <div class="form-group my-2">
                                                    <label class="image-label" for="cover_image">Cover Image File</label>
                                                    <div class="image">
                                                        <input class="@error ('cover_image') required @enderror image-input border p-2 col-md-8" id="cover_image" type="file" name="cover_image">
                                                        @error('cover_image')
                                                        <div class="alert alert-danger">{{ $message }}</div>
                                                        @enderror

                                                        <small class="form-text text-muted col-span-2">
                                                            <strong>Required.</strong><br>
                                                            Must be a JPG or PNG file.<br>
                                                            Must be at least 3000x3000 pixels.<br>
                                                            Must be square (same width and height).
                                                        </small>
                                                        <script type="text/javascript">
                                                            $('input[type="file"]').change(function (e) {
                                                                var fileName = e.target.files[0].name;
                                                                $('.image-label').html(fileName);
                                                            });
                                                        </script>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-6">
                                                <div class="form-group my-2">
                                                    <div class="custom-control custom-switch">
                                                        <input type="hidden" name="is_active" value="0"/>
                                                        <input class="custom-control-input" type="checkbox" name="is_active" value="1" id="checkbox_hidden" checked>
                                                        <label class="custom-control-label" for="checkbox_hidden">
                                                            Make this podcast active.
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <br>


                                        <div class="grid grid-cols-12 gap-4">
                                            <div class=" col-span-6">
                                                <div class="form-group my-2">
                                                    <button type="submit" class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('pageForm').submit()">
                                                        <i class="fa fa-check mr-2" aria-hidden="true"></i> Create Podcast
                                                    </button>
                                                </div>
                                            </div>
                                        </div>


                                    </form>

                                </div>
                            </div>

                        </div>
            </main>
        </div>

@endsection