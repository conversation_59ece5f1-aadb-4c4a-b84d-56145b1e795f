@extends('admin._layouts._app')

@section('title', 'Podcasts')

@section('content')

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Podcasts
                </h1>
            </div>
            <div>
                <a href="{{ route('admin.podcasts.create') }}" class="admin-button-blue">
                    <x-heroicon-m-plus class="w-6 h-6 text-white mr-1"/>
                    New Podcast
                </a>
            </div>
        </div>

        <div class="flex flex-col mt-4">
            <div class="overflow-x-auto admin-section rounded-sm">
                <div class="inline-block min-w-full">
                    <div class="overflow-hidden overflow-x-auto">
                        <table class="table w-full text-center table-hover" cellpadding="6">

                            <tr class="bg-gray-700 text-white">
                                <th>Title</th>
                                <th>Published At</th>
                                <th>Destinations</th>
                                <th>Episodes</th>
                                <th>Downloads</th>
                            </tr>

                            <tbody>
                            @foreach($podcasts as $podcast)
                                <tr>
                                    <td class="py-2 px-1">
                                        <a href="{{ route('admin.podcasts.view', $podcast) }}">
                                            {{ $podcast->title }}
                                        </a>
                                    </td>
                                    <td class="py-2 px-1">
                                        {{ $podcast->published_at?->format('M d, Y') }}
                                    </td>
                                    <td class="py-2 px-1">
                                        {{ $podcast->destinations()->count() }}
                                    </td>
                                    <td class="py-2 px-1">
                                        {{ $podcast->tracks()->count() }}
                                    </td>
                                    <td class="py-2 px-1">
                                        {{ $podcast->downloads()->count() }}
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div>
                    {{ $podcasts->links() }}
                </div>
            </div>
        </div>
    </div>

@endsection







