@extends('admin._layouts._app')

@section('title', 'Create File')

@section('content')

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
        'url' => route('admin.account-files.index'),
        'text' => 'Files',
    ], [
        'url' => null,
        'text' => 'Create File',
    ]]])

    <div class="admin-heading-section mb-4">
        <h1>
            Create Account File
        </h1>
    </div>

    <form id="create_form"
          action="{{ route('admin.account-files.store') }}"
          method="post" enctype="multipart/form-data" autocomplete="off"
          class="pt-4">
        @csrf

        <div class="mt-4 mx-auto max-w-2xl">
            <div class="w-100">
                <div class="w-100 bg-white admin-section-border sm:rounded-lg">
                    <div class="px-4 py-5 sm:p-0">
                        <dl class="grid grid-cols-1 divide-y">
                            <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Title:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input name="title" type="text" value="{{ old('title') }}" placeholder="" autocomplete="off" data-lpignore="true">
                                </dd>
                                @if ($errors->has('title'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('title') }}</strong>
                                    </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Expire Date:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input name="expires_at" type="date" value="{{ old('expires_at') }}" autocomplete="off" data-lpignore="true">
                                </dd>
                                @if ($errors->has('expires_at'))
                                    <span class=" invalid-feedback">
                                        <strong>{{ $errors->first('expires_at') }}</strong>
                                    </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Type:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <select name="type">
                                        <option value="document" {{ old('type') == 'PDF' ? 'checked="checked"' : null }}>PDF</option>
                                        <option value="document" {{ old('type') == 'Document' ? 'checked="checked"' : null }}>Document</option>
                                        <option value="image" {{ old('type') == 'Image' ? 'checked="checked"' : null }}>Image</option>
                                        <option value="video" {{ old('type') == 'Video' ? 'checked="checked"' : null }}>Video</option>
                                        <option value="audio" {{ old('type') == 'Audio' ? 'checked="checked"' : null }}>Audio</option>
                                        <option value="other" {{ old('type') == 'Other' ? 'checked="checked"' : null }}>Other</option>
                                        <option value="" {{ old('type') == '' ? 'checked="checked"' : null }}></option>
                                    </select>
                                </dd>
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Folder:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <select name="account_file_parent_id">
                                        <option value=""> - No Folder -</option>
                                        @foreach ($folders as $folder)
                                            <option value="{{ $folder->id }}" {{ request()->get('account_file_parent_id') == $folder->id ? 'selected="selected"' : null }}>
                                                📂&nbsp;{{ $folder->title }}
                                            </option>
                                            @foreach($folder->folders as $child_folder)
                                                <option value="{{ $child_folder->id }}" {{ request()->get('account_file_parent_id') == $child_folder->id ? 'selected="selected"' : null }}>
                                                    &nbsp; &nbsp; ↳ {{ $child_folder->title }}
                                                </option>
                                            @endforeach
                                        @endforeach
                                    </select>
                                </dd>
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Description:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <textarea name="description" autocomplete="off" data-lpignore="true">{{ old('description') }}</textarea>
                                </dd>
                                @if ($errors->has('description'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('description') }}</strong>
                                    </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                <dt class="text-gray-800 align-content-start">
                                    File:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input class="" id="fileInput" type="file" name="user_file">
                                </dd>
                            </div>
                            <div class="flex flex-row justify-between mt-3 sm:mt-0 sm:px-4 sm:py-4">
                                <div class="flex flex-row space-x-4">
                                    <a class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('create_form').submit()">
                                        <x-heroicon-s-check class="w-5 mr-1"/>
                                        Save Changes
                                    </a>
                                    <a onclick="history.back()" class="cursor-pointer relative mr-4 admin-button-transparent inline-flex items-center  hover:bg-gray-200">
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </form>

@endsection
