@extends('admin._layouts._app')

@section('title', 'Edit File')

@section('content')

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
        'url' => route('admin.account-files.index'),
        'text' => 'Files',
    ], [
        'url' => null,
        'text' => 'Edit File',
    ]]])

    <div class="admin-heading-section mb-4">
        <h1>
            Edit File: {{ $file->title }}
        </h1>
    </div>

    <form id="edit_form"
          action="{{ route('admin.account-files.save', $file) }}"
          method="post" enctype="multipart/form-data" autocomplete="off"
          class="pt-4">
        @csrf
        @method('put')

        <div class="mt-4 mx-auto max-w-2xl">
            <div class="w-100">
                <div class="w-100 bg-white admin-section-border sm:rounded-lg">
                    <div class="px-4 py-5 sm:p-0">
                        <dl class="grid grid-cols-1 divide-y">
                            <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Title:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input name="title" type="text" value="{{ old('title') ?? $file->title }}" placeholder="" autocomplete="off">
                                </dd>
                                @if ($errors->has('title'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('title') }}</strong>
                                    </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Expire Date:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input name="expires_at" type="date" value="{{ old('expires_at') ?? $file->expires_at ? $file->expires_at->format('Y-m-d') : null }}" autocomplete="off">
                                </dd>
                                @if ($errors->has('expires_at'))
                                    <span class=" invalid-feedback">
                                                <strong>{{ $errors->first('expires_at') }}</strong>
                                            </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Type:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <select name="type">
                                        <option value="document" {{ old('type') == 'PDF' ? 'checked="checked"' : null }}>PDF</option>
                                        <option value="document" {{ old('type') == 'Document' ? 'checked="checked"' : null }}>Document</option>
                                        <option value="image" {{ old('type') == 'Image' ? 'checked="checked"' : null }}>Image</option>
                                        <option value="video" {{ old('type') == 'Video' ? 'checked="checked"' : null }}>Video</option>
                                        <option value="audio" {{ old('type') == 'Audio' ? 'checked="checked"' : null }}>Audio</option>
                                        <option value="other" {{ old('type') == 'Other' ? 'checked="checked"' : null }}>Other</option>
                                        <option value="other" {{ old('type') == '' ? 'checked="checked"' : null }}></option>
                                    </select>
                                </dd>
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Folder:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <select name="account_file_parent_id">
                                        <option value=""> - No Folder -</option>
                                        @foreach ($folders as $folder)
                                            <option value="{{ $folder->id }}" {{ $file->account_file_parent_id == $folder->id ? 'selected' : '' }}>
                                                📂&nbsp;{{ $folder->title }}
                                            </option>
                                            @foreach($folder->folders as $child_folder)
                                                <option value="{{ $child_folder->id }}" {{ $file->account_file_parent_id == $child_folder->id ? 'selected' : '' }}>
                                                    &nbsp; &nbsp; ↳ {{ $child_folder->title }}
                                                </option>
                                            @endforeach
                                        @endforeach
                                    </select>
                                </dd>
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Description:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <textarea name="description" autocomplete="off">{{ old('description') ?? $file->description }}</textarea>
                                </dd>
                                @if ($errors->has('description'))
                                    <span class="invalid-feedback">
                                                <strong>{{ $errors->first('description') }}</strong>
                                            </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                <dt class="text-gray-800 align-content-start">
                                    File:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input class="" id="fileInput" type="file" name="user_file">
                                    <div class="text-sm text-gray-400 mt-2">Selecting a new file will delete the existing file.</div>
                                    <div class="text-sm text-gray-400">Not selecting a new file will leave the existing file in place.</div>
                                    @if($file->file_original_name)
                                        <div class="text-sm text-gray-600 mt-2">
                                            Current File:<br>
                                            <a href="{{ $file->getTempUrl(2880) }}">
                                                <code class="bg-gray-100 px-2 py-1 rounded-sm">{{ $file->file_original_name }}</code>
                                            </a>
                                        </div>
                                    @endif
                                </dd>
                            </div>
                            <div class="flex flex-row justify-between mt-3 sm:mt-0 sm:px-4 sm:py-4">
                                <div class="flex flex-row space-x-4">
                                    <a class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('edit_form').submit()">
                                        <x-heroicon-s-check class="w-5 mr-1"/>
                                        Save Changes
                                    </a>
                                    <a onclick="history.back()" class="cursor-pointer relative mr-4 admin-button-transparent inline-flex items-center  hover:bg-gray-200">
                                        Cancel
                                    </a>
                                </div>
                                @if(!$file->is_folder)
                                    <button type="button" class="admin-button-red bg-transparent text-red-500 hover:text-white" onclick="this.disabled = true; this.innerHTML = 'Deleting...'; document.getElementById('delete-this-file').submit()">
                                        <x-heroicon-s-trash class="w-5 mr-1"/>
                                        Delete
                                    </button>
                                @endif
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </form>
    @if(!$file->is_folder)
        <form action="{{ route('admin.account-files.delete', $file) }}" method="post" id="delete-this-file">
            @csrf
            @method('delete')
        </form>
    @endif

@endsection
