@extends('admin._layouts._app')

@section('title', 'Create Folder')

@section('content')

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
        'url' => route('admin.account-files.index'),
        'text' => 'Files',
    ], [
        'url' => null,
        'text' => 'Create Folder',
    ]]])

    <div class="admin-heading-section mb-4">
        <h1>
            Create Folder
        </h1>
    </div>

    <form id="create_form"
          action="{{ route('admin.account-files.folders.store') }}"
          method="post" enctype="multipart/form-data" autocomplete="off"
          class="pt-4">
        @csrf

        <div class="mt-4 mx-auto max-w-3xl">
            <div class="w-100">
                <div class="w-100 bg-white admin-section-border sm:rounded-lg">
                    <div class="px-4 py-5 sm:p-0">
                        <dl class="grid grid-cols-1 divide-y">
                            <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Title:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input name="title" type="text" value="{{ old('title') }}" placeholder="" autocomplete="off" data-lpignore="true">
                                </dd>
                                @if ($errors->has('title'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('title') }}</strong>
                                    </span>
                                @endif
                            </div>
                            {{--                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">--}}
                            {{--                                <dt class="text-gray-800 my-auto">--}}
                            {{--                                    Folder Color:--}}
                            {{--                                </dt>--}}
                            {{--                                <dd class="sm:col-span-2">--}}
                            {{--                                    <input type="color" id="head" name="head" value="#3B82F6"/>--}}
                            {{--                                </dd>--}}
                            {{--                            </div>--}}
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Expire Date:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <input name="expires_at" type="date" value="{{ old('expires_at') }}" autocomplete="off" data-lpignore="true">
                                </dd>
                                @if ($errors->has('expires_at'))
                                    <span class=" invalid-feedback">
                                        <strong>{{ $errors->first('expires_at') }}</strong>
                                    </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 mt-2 align-text-top">
                                    Subfolder:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <select name="account_file_parent_id">
                                        <option value=""> - none -</option>
                                        @foreach ($folders as $folder)
                                            <option value="{{ $folder->id }}" {{ request()->get('account_file_parent_id') == $folder->id ? 'selected="selected"' : null }}>
                                                📂&nbsp;{{ $folder->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="text-gray-400 text-sm leading-tight">Only select this option if you want this new folder to be a subfolder of an existing folder.</div>
                                    <div class="text-gray-400 text-sm leading-tight">Folders can only be one level deep.</div>
                                </dd>
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 mt-2 align-text-top">
                                    Options:
                                </dt>
                                <dd class="sm:col-span-2 space-y-2">
                                    <div class="relative flex items-start">
                                        <div class="flex h-6 items-center">
                                            <input id="is_hidden" aria-describedby="is_hidden-description" name="is_hidden" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-600">
                                        </div>
                                        <div class="ml-2 leading-6">
                                            <label for="is_hidden">
                                                <span class="font-normal text-gray-900">Hidden Folder</span>
                                                <p id="is_hidden-description" class="text-gray-400 text-sm ">Only visible to Admins.</p>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="relative flex items-start">
                                        <div class="flex h-6 items-center">
                                            <input id="is_public" aria-describedby="is_public-description" name="is_public" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-600">
                                        </div>
                                        <div class="ml-2 leading-6">
                                            <label for="is_public">
                                                <span class="font-normal text-gray-900">Public Folder</span>
                                                <p id="is_public-description" class="text-gray-400 text-sm ">
                                                    Viewable on your Lightpost Website.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="relative flex items-start">
                                        <div class="flex h-6 items-center">
                                            <input id="is_men_only" aria-describedby="is_men_only-description" name="is_men_only" type="checkbox"
                                                   class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-600"/>
                                        </div>
                                        <div class="ml-2 leading-6">
                                            <label for="is_men_only">
                                                <span class="font-normal text-gray-900">Men Only</span>
                                                <p id="is_public-description" class="text-gray-400 text-sm ">
                                                    Viewable only to members logged into Lightpost who are <strong>men</strong>.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="relative flex items-start">
                                        <div class="flex h-6 items-center">
                                            <input id="is_women_only" aria-describedby="is_women_only-description" name="is_women_only" type="checkbox"
                                                   class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-600"/>
                                        </div>
                                        <div class="ml-2 leading-6">
                                            <label for="is_women_only">
                                                <span class="font-normal text-gray-900">Women Only</span>
                                                <p id="is_public-description" class="text-gray-400 text-sm ">
                                                    Viewable only to members logged into Lightpost who are <strong>women</strong>.
                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </dd>
                                @if ($errors->has('is_hidden'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('is_hidden') }}</strong>
                                    </span>
                                @endif
                            </div>
                            <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                <dt class="text-gray-800 my-auto">
                                    Description:
                                </dt>
                                <dd class="sm:col-span-2">
                                    <textarea name="description" autocomplete="off" data-lpignore="true">{{ old('description') }}</textarea>
                                </dd>
                                @if ($errors->has('description'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('description') }}</strong>
                                    </span>
                                @endif
                            </div>
                            <div class="flex flex-row justify-between mt-3 sm:mt-0 sm:px-4 sm:py-4">
                                <div class="flex flex-row space-x-4">
                                    <a class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('create_form').submit()">
                                        <x-heroicon-s-check class="w-5 mr-1"/>
                                        Save Changes
                                    </a>
                                    <a href="document.back()" onclick="history.back()" class="relative mr-4 admin-button-transparent inline-flex items-center  hover:bg-gray-200">
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </form>

@endsection
