<div class="md:flex md:items-center align-middle md:justify-between mb-2">
    <div class="flex justify-between">
        <h1>
            Edit Event {{ $more_than_one_occurrence ? 'Occurrence' : null }}
        </h1>
    </div>
</div>

<nav class="mt-2 mb-4 p-1.5 flex flex-wrap bg-gray-50 rounded-lg gap-1 border border-gray-300 shadow-inner text-sm md:text-base font-normal">
    <a href="{{ route('admin.calendars.event.occurrence.view', $occurrence) }}"
       class="{{ request()->routeIs(['admin.calendars.event.occurrence.view']) ? $selected : $unselected }}" aria-current="page">
        <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-calendar-day"></i></span> Event {{ $more_than_one_occurrence ? 'Occurrence' : null }}
    </a>
    <a href="{{ route('admin.calendars.event.occurrence.sign-ups', $occurrence) }}"
       class="{{ request()->routeIs('admin.calendars.event.occurrence.sign-ups') ? $selected : $unselected }}">
        <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-clipboard-list-check"></i></span> Sign-ups
    </a>
    {{--    <a href="" class="{{ request()->routeIs('admin.users.photos.index') ? $selected : $unselected }}">--}}
    {{--        <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-files"></i></span> Files--}}
    {{--    </a>--}}
    {{--    <a href="" class="{{ request()->routeIs('admin.users.photos.index') ? $selected : $unselected }}">--}}
    {{--        <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-paste"></i></span> Reports--}}
    {{--    </a>--}}
    {{--    <a href="" class="{{ request()->routeIs('admin.users.photos.index') ? $selected : $unselected }}">--}}
    {{--        <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-cog"></i></span> Settings--}}
    {{--    </a>--}}
    <a href="" class="hidden {{ request()->routeIs('admin.users.involvement.index') ? $selected : $unselected }}">
        <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-door-closed"></i></span> Room Reservation
    </a>
    @if($more_than_one_occurrence)
        <a href="{{ route('admin.calendars.event.occurrence.view.occurrences', $occurrence) }}"
           class="{{ request()->routeIs('admin.calendars.event.occurrence.view.occurrences') ? $selected : $unselected }}">
            <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-calendar-days"></i></span> Occurrences
        </a>
        <a href="{{ route('admin.calendars.event.occurrence.view.event', $occurrence) }}"
           class="{{ request()->routeIs('admin.calendars.event.occurrence.view.event') ? $selected : $unselected }}">
            <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-calendar-day"></i></span> Edit All
        </a>
    @endif
    @if(false && $more_than_one_occurrence)
        <a href="{{ route('admin.calendars.evens.occurrence.sign-ups', $occurrence) }}"
           class="{{ request()->routeIs('admin.calendars.evens.occurrence.sign-ups') ? $selected : $unselected }}">
            <span class="hidden sm:inline-block"><i class="mr-1 fal text-lg fa-calendar-star"></i></span> Main Event
        </a>
    @endif
</nav>