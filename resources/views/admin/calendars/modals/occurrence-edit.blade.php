<div class="" x-data="{ showTab: 'event' }">
    <div class="mb-4">
        <div class="border-b-2 border-gray-200">
            <nav class="-mb-px flex space-x-4" aria-label="Tabs">
                <!-- Current: "border-blue-500 text-blue-600", Default: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700" -->
                <a x-on:click="showTab = 'event'"
                   x-bind:class="showTab == 'event' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                   class="cursor-pointer group inline-flex items-center border-b-4 pb-4 px-1 text-base font-medium">
                    <i class="fas fa-calendar-alt my-auto mr-2"></i>
                    <span>Event Details</span>
                </a>
                @if($event->occurrences()->count() == 1)
                    <a x-on:click="showTab = 'occurrence'"
                       x-bind:class="showTab == 'occurrence' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                       class="cursor-pointer group inline-flex items-center border-b-4 pb-4 px-1 text-base font-medium">
                        <i class="fas fa-calendar-day my-auto mr-2"></i>
                        <span>Occurrence Details</span>
                    </a>
                @endif
                @if($occurrence->enable_responses)
                    <a x-on:click="showTab = 'occurrence_responses'"
                       x-bind:class="showTab == 'occurrence_responses' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                       class="cursor-pointer group inline-flex items-center border-b-4 pb-4 px-1 text-base font-medium">
                        <i class="fas fa-user-check my-auto mr-2"></i>
                        <span>Occurrence Responses</span>
                    </a>
                @endif
            </nav>
        </div>
    </div>

    <div x-show="showTab == 'occurrence'">
        <form method="post" action="{{ route('admin.calendars.event.occurrence.update', $occurrence) }}" id="save_event_occurrence_{{ $occurrence->id }}">
            {{ csrf_field() }}

            <div class="sm:flex sm:items-start">
                <div class="mt-3 w-full text-center sm:mt-0 sm:text-left">

                    <div class="flex flex-row justify-between">
                        <div class="border border-purple-600 text-purple-600 bg-purple-50 px-2 py-1 rounded-sm text-sm">
                            This is event occurrence information affecting <strong>only this occurrence: {{ $occurrence->start_at->format('M d, Y') }}</strong>.
                        </div>
                        <div class="text-sm my-auto">Timezone: <strong>{{ auth()->user()->account->timezone }}</strong></div>
                    </div>

                    <div class="my-6 space-y-6 sm:space-y-2">

                        <div class="sm:grid sm:grid-cols-5 sm:gap-2 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"></label>
                            <div class="mt-1 sm:pb-4 sm:mt-0 sm:col-span-4">
                                <input id="enable_responses_checkbox" name="enable_responses" value="1" type="checkbox" {{ $occurrence->enable_responses ? 'checked="checked"' : '' }}/>
                                <label class="my-auto" for="enable_responses_checkbox">
                                    Enable Responses
                                </label>
                            </div>
                            <label for="" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"></label>
                            <div class="mt-1 sm:pb-4 sm:mt-0 sm:col-span-4">
                                <input id="show_responses_checkbox" name="show_responses" value="1" type="checkbox" {{ $occurrence->show_responses ? 'checked="checked"' : '' }}/>
                                <label class="my-auto" for="show_responses_checkbox">
                                    Show Responses to Members
                                </label>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </form>

        <div class="flex flex-col sm:flex-row justify-between sm:flex">
            <div class="flex flex-col sm:flex-row">
                <button type="submit" class="admin-button-blue" onclick="document.getElementById('save_event_occurrence_{{ $occurrence->id }}').submit()">
                    <x-heroicon-o-check class="w-5 mr-1"/>
                    Save Occurrence Changes
                </button>
                <button type="button" @click="closeModal()" class="admin-button-transparent ml-0 sm:ml-2 mt-2 sm:mt-0">
                    Cancel
                </button>
                <span class="text-white">{{ $occurrence->id }}</span>
            </div>
            <div>
                {{--                @can('delete', $occurrence)--}}
                {{--                    <button type="button" class="float-right mt-2 sm:mt-0 admin-button-transparent hover:bg-red-600 hover:text-white border border-red-600 text-red-600 cursor-pointer" rel="tooltip" title="Delete" onclick="document.getElementById('delete_event_occurrence_{{ $occurrence->event->id }}').submit()">--}}
                {{--                        Delete Occurrence--}}
                {{--                    </button>--}}
                {{--                    <form method="post" action="{{ route('admin.calendars.event.occurrence.destroy', $occurrence) }}" id="delete_event_occurrence_{{ $occurrence->event->id }}" style="display: none;">@csrf @method('delete')</form>--}}
                {{--                @endcan--}}
            </div>
        </div>
    </div>

    <div x-show="showTab == 'event'">
        <form method="post" action="{{ route('admin.calendars.event.update', $event) }}" id="save_event_{{ $event->id }}">
            {{ csrf_field() }}

            <div class="sm:flex sm:items-start">
                <div class="w-full text-center sm:mt-0 sm:text-left">

                    @if($event->occurrences->count() > 1)
                        <div class="mb-3 flex flex-row justify-between">
                            <div class="border border-purple-600 text-purple-600 bg-purple-50 px-2 py-1 rounded-sm text-sm">
                                This information affects <strong>all occurrences</strong> for this repeating event.
                            </div>
                        </div>
                    @endif

                    <div class="space-y-6 sm:space-y-1" x-data="{ is_all_day: {{ $event->is_all_day ? 'true' : 'false' }} }">
                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="first-name" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"> Title: </label>
                            <div class="mt-1 sm:mt-0 sm:col-span-4">
                                <input type="text" name="title" autocomplete="off" placeholder="Youth Devo" value="{{ $event->title }}"/>
                            </div>
                        </div>

                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="location" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"> Location: </label>
                            <div class="mt-1 sm:mt-0 sm:col-span-2">
                                <input type="text" name="location" autocomplete="off" placeholder="Room 101" value="{{ $event->location }}"/>
                            </div>
                        </div>

                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="calendar_id" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"> Calendar: </label>
                            <div class="mt-1 sm:mt-0 sm:col-span-4">
                                <select name="calendar_id">
                                    @foreach($calendars as $cal)
                                        <option value="{{ $cal->id }}" {{ $cal->id == $event->calendar_id ? 'selected' : null }}>{{ $cal->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="city" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"> Event Details </label>
                            <div class="mt-1 sm:mt-0 sm:col-span-4">
                                <textarea rows="{{ $event->description ? 2 : 1 }}" name="description" class="admin-border-color border rounded-lg" type="text">{{ $event->description }}</textarea>
                            </div>
                        </div>


                        <div class="flex flex-col sm:flex-row justify-between sm:border-b sm:border-gray-200 sm:pt-2 sm:pb-3">
                            <div class="flex flex-col sm:flex-row">
                                <button type="submit" class="admin-button-blue mt-2 sm:mt-0" onclick="document.getElementById('save_event_{{ $event->id }}').submit()">
                                    <x-heroicon-o-check class="w-5 mr-1"/>
                                    Save Changes
                                </button>
                                <button type="button" @click="closeModal()" class="admin-button-transparent ml-0 sm:ml-2 mt-2 sm:mt-0">
                                    Cancel
                                </button>
                            </div>
                            <div>
                                @can('delete', $event->calendar)
                                    <button type="button" class="float-right mt-2 sm:mt-0 admin-button-transparent hover:bg-red-600 hover:text-white border border-red-600 text-red-600 cursor-pointer" rel="tooltip" title="Delete" onclick="document.getElementById('delete_event_{{ $event->id }}').submit()">
                                        Delete
                                        @if($event->occurrences()->count() > 1)
                                            All Occurrences ({{ $event->occurrences()->count() }})
                                        @endif
                                    </button>
                                @endcan
                            </div>
                        </div>

                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="calendar_id" class="block text-sm text-left font-medium text-gray-700"></label>
                            <div class="sm:col-span-4">
                                <div class="border border-purple-600 text-purple-600 bg-purple-50 px-2 py-1 rounded-sm text-sm">
                                    Only basic information can be edited. (More options coming soon!)
                                    <br>
                                    If you need to change the date or time, please delete this event and create a new one.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="opacity-50">
                        @if($event->isRecurring())
                            <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                                <label for="" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"></label>
                                <div class="block mt-1 sm:mt-0 sm:col-span-4 flex flex-row my-auto border border-emerald-700 bg-emerald-50 text-emerald-700 rounded-sm px-2 py-1">
                                    <x-heroicon-s-arrow-path class="h-4 w-4 my-auto"/>
                                    <label class="my-auto ml-1">
                                        Recurring Event - {{ ucfirst($event->getHumanReadableRRule()) }}
                                    </label>
                                </div>
                            </div>
                        @endif

                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"></label>
                            <div class="flex flex-row justify-between mt-1 sm:mt-0 sm:col-span-4">
                                <div>
                                    <input id="all_day_checkbox" type="checkbox" {{ $event->is_all_day ? 'checked="checked"' : '' }} disabled="disabled"/>
                                    <label for="all_day_checkbox">
                                        All Day Event
                                    </label>
                                </div>
                                <div class="text-sm my-auto">Timezone: <strong>{{ auth()->user()->account->timezone }}</strong></div>
                            </div>
                        </div>

                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="start_at_date" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"> Start: </label>
                            <div class="flex flex-row mt-1 sm:mt-0 sm:col-span-4">
                                <input type="date" name="start_at_date" disabled="disabled" autocomplete="off" value="{{ $event->start_at->setTimezone($event->account->timezone)->format('Y-m-d') }}"/>

                                @if(!$event->is_all_day)
                                    <div class="flex flex-row ml-6">
                                        <select class="w-26 mr-0.5" name="start_at_time_hour" disabled="disabled">
                                            @foreach(\App\Calendars\Calendar::$time_selections as $hour => $display_value)
                                                <option value="{{ $hour }}" {{ ($event->start_at->setTimezone(auth()->user()->account->timezone)->format('G') == $hour ? 'selected' : null) }}>
                                                    {{ $display_value }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <select class="w-20 mr-0.5" name="start_at_time_minute" disabled="disabled">
                                            @for($j=0;$j<=55;$j = $j+5)
                                                <option value="{{ $j }}" {{ $event->start_at->setTimezone($event->account->timezone)->format('i') == $j ? 'selected' : null }}>{{ $j == 0 ? '00' : ($j == 5 ? '05' : $j) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                            <label for="end_at_date" class="block text-sm text-left font-medium text-gray-700 sm:mt-px"> End: </label>
                            <div class="flex flex-row mt-1 sm:mt-0 sm:col-span-4">
                                <input type="date" name="end_at_date" disabled="disabled" autocomplete="off" value="{{ $event->end_at->setTimezone($event->account->timezone)->format('Y-m-d') }}"/>

                                @if(!$event->is_all_day)
                                    <div class="flex flex-row ml-6">
                                        <select class="w-26 mr-0.5" name="end_at_time_hour" disabled="disabled">
                                            @foreach(\App\Calendars\Calendar::$time_selections as $hour => $display_value)
                                                <option value="{{ $hour }}" {{ ($event->end_at->setTimezone(auth()->user()->account->timezone)->format('G') == $hour ? 'selected' : null) }}>
                                                    {{ $display_value }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <select class="w-20 mr-0.5" name="end_at_time_minute" disabled="disabled">
                                            @for($j=0;$j<=55;$j = $j+5)
                                                <option value="{{ $j }}" {{ $event->end_at->setTimezone($event->account->timezone)->format('i') == $j ? 'selected' : null }}>{{ $j == 0 ? '00' : ($j == 5 ? '05' : $j) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </form>

        <div class="flex flex-col sm:flex-row justify-between mt-5 sm:mt-4 sm:pl-4 sm:flex">
            <div>
                @can('delete', $event->calendar)
                    <form method="post" action="{{ route('admin.calendars.event.destroy', $event) }}" id="delete_event_{{ $event->id }}" style="display: none;">
                        @csrf @method('delete')
                    </form>
                @endcan
            </div>
        </div>
    </div>

    <div x-show="showTab == 'occurrence_responses'">
        @if($occurrence->enable_responses)
            <div class="grid grid-cols-1 sm:grid-cols-3 mt-1 border border-gray-300 rounded-lg">
                <div class="flex flex-col">
                    <div class="text-center font-medium py-2 bg-gray-100 rounded-tl-lg">
                        Going ({{ $occurrence->responses()->isGoing()->count() }})
                    </div>
                    @if($occurrence->show_responses)
                        <div class="flex flex-col text-center">
                            @foreach($occurrence->responses()->isGoing()->get() as $response)
                                <div class="py-1">
                                    <a href="{{ route('app.directory.view.family', $response->user->family_id) }}">
                                        @if($response->user)
                                            {{ $response->user->name }}
                                        @else
                                            {{ $response->name }}
                                        @endif
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
                <div class="flex flex-col">
                    <div class="text-center font-medium py-2 bg-gray-100">
                        Not Going ({{ $occurrence->responses()->isNotGoing()->count() }})
                    </div>
                    @if($occurrence->show_responses)
                        <div class="flex flex-col text-center">
                            @foreach($occurrence->responses()->isNotGoing()->get() as $response)
                                <div class="py-1">
                                    <a href="{{ route('app.directory.view.family', $response->user->family_id) }}">
                                        @if($response->user)
                                            {{ $response->user->name }}
                                        @else
                                            {{ $response->name }}
                                        @endif
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
                <div class="flex flex-col">
                    <div class="text-center font-medium py-2 bg-gray-100 rounded-tr-lg">
                        Interested ({{ $occurrence->responses()->isMaybe()->count() }})
                    </div>
                    @if($occurrence->show_responses)
                        <div class="flex flex-col text-center">
                            @foreach($occurrence->responses()->isMaybe()->get() as $response)
                                <div class="py-1">
                                    <a href="{{ route('app.directory.view.family', $response->user->family_id) }}">
                                        @if($response->user)
                                            {{ $response->user->name }}
                                        @else
                                            {{ $response->name }}
                                        @endif
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>

</div>