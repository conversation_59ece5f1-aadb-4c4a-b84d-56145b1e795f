<form method="post" action="{{ route('admin.calendars.save') }}">
    @csrf
    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <x-heroicon-o-pencil-square class="w-6 text-blue-600"/>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">

            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Create New Calendar
            </h3>
            <div class="mt-4">
                <div class="mb-3">
                    <div class="col-md-12">
                        Name:<br>
                        <input type="text" name="name" autocomplete="off"/>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="user_group_id">User Group:</label>
                    <div>
                        <select name="user_group_id" data-placeholder="No user group selected." id="user_group_id_select">
                            <option value="">- None -</option>
                            @foreach($groups as $group)
                                <option value="{{ $group->id }}">{{ $group->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="text-gray-400 text-sm"><em>Group calendars are only visible to group members.</em></div>
                </div>

                <div class="mb-4">
                    <div class="mb-2">Background Color:</div>
                    @foreach(\App\Calendars\Calendar::$colors as $color_name => $color)
                        <div class="inline-block mb-2">
                            <input type="radio" name="background_color" id="bgColor{{ $color_name }}" value="{{ $color['500'] }}">
                            <label class="ml-1 py-1 px-4 rounded-sm" style="background-color: #{{ $color['500'] }}" for="bgColor{{ $color_name }}"> </label>
                        </div>
                    @endforeach
                </div>

                <input type="hidden" name="text_color" value="ffffff" autocomplete="off"/>

                <div class="mb-3">
                    <div class="custom-control custom-switch">
                        <input type="checkbox" name="is_hidden" id="customSwitch1" value="1"/>
                        <label for="customSwitch1">Is Hidden (will not show to members)</label>
                    </div>
                    <div class="custom-control custom-switch">
                        <input type="checkbox" name="is_public" id="customSwitch2" value="1"/>
                        <label for="customSwitch2">Is Public (viewable to the open Internet)</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex">
        <button type="submit" class="admin-button-blue">
            Create Calendar
        </button>
        <button type="button" @click="closeModal()" class="ml-4 admin-button-transparent">
            Cancel
        </button>
    </div>
</form>
