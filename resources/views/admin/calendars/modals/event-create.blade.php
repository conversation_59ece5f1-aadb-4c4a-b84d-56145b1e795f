<form method="post" action="{{ route('admin.calendars.event.save') }}" id="save_event">
    {{ csrf_field() }}

    <div class="sm:flex sm:items-start">
        <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <x-heroicon-o-pencil-square class="w-6 text-blue-600"/>
        </div>
        <div class="mt-3 w-full text-center sm:mt-0 sm:ml-4 sm:text-left">

            <span class="float-right">Timezone: <strong>{{ auth()->user()->account->timezone }}</strong></span>
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Create Event
            </h3>

            <div class="mt-6 space-y-6 sm:space-y-1" x-data="{ is_all_day: false, selected_calendar: false, title: null }">
                @if(!$calendars)
                    <div class="flex rounded-lg bg-red-100 border border-red-600 text-red-600 p-6" role="alert">
                        Oops! You have no calendars yet. Please create a calendar first and then you can create events.
                    </div>
                @endif

                <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 sm:mt-px"> Title </label>
                    <div class="mt-1 sm:mt-0 sm:col-span-4">
                        <input type="text" name="title" autocomplete="off" placeholder="Youth Devo" value="" x-model="title"/>
                    </div>
                </div>

                <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                    <label for="calendar_id" class="block text-sm font-medium text-gray-700 sm:mt-px"> Calendar: </label>
                    <div class="flex flex-row mt-1 sm:mt-0 sm:col-span-4">
                        <select name="calendar_id" x-model="selected_calendar">
                            <option value=""> - select -</option>
                            @foreach($calendars as $cal)
                                <option value="{{ $cal->id }}">{{ $cal->name }}</option>
                            @endforeach
                        </select>
                        <span class="my-auto ml-4 text-red-600 font-medium" x-show="!selected_calendar && title">Please select a calendar!</span>
                    </div>
                </div>

                <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                    <label for="location" class="block text-sm font-medium text-gray-700 sm:mt-px"> Location: </label>
                    <div class="mt-1 sm:mt-0 sm:col-span-2">
                        <input type="text" name="location" autocomplete="off" placeholder="Room 101"/>
                    </div>
                </div>

                <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                    <label for="" class="block text-sm font-medium text-gray-700 sm:mt-px"></label>
                    <div class="mt-1 sm:mt-0 sm:col-span-4">
                        <input id="all_day_checkbox" type="checkbox" x-model="is_all_day"/>
                        <input type="hidden" name="is_all_day" x-bind:value="is_all_day ? '1' : '0'"/>
                        <label for="all_day_checkbox">
                            All Day Event
                        </label>
                    </div>
                </div>

                <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                    <label for="start_at_date" class="block text-sm font-medium text-gray-700 sm:mt-px"> Start: </label>
                    <div class="flex flex-row mt-1 sm:mt-0 sm:col-span-4">
                        <input type="date" name="start_at_date" autocomplete="off" value="{{ now()->setTimezone(auth()->user()->account->timezone)->format('Y-m-d') }}"/>

                        <div class="flex flex-row ml-6" x-show="!is_all_day">
                            <select class="w-20 mr-0.5" name="start_at_time_hour">
                                @for($i=1;$i<=12;$i++)
                                    <option value="{{ $i }}" {{ (now()->setTimezone(auth()->user()->account->timezone)->format('H') == $i || now()->setTimezone(auth()->user()->account->timezone)->format('H') - 12 == $i) ? 'selected' : null }}>{{ $i }}</option>
                                @endfor
                            </select>
                            <select class="w-20 mr-0.5" name="start_at_time_minute">
                                @for($j=0;$j<=55;$j = $j+5)
                                    <option value="{{ $j }}" {{ now()->setTimezone(auth()->user()->account->timezone)->format('i') == $j ? 'selected' : null }}>{{ $j == 0 ? '00' : ($j == 5 ? '05' : $j) }}</option>
                                @endfor
                            </select>
                            <select class="w-20" name="start_at_time_ampm">
                                <option value="am"{{ now()->setTimezone(auth()->user()->account->timezone)->format('H') < 12 ? 'selected' : null }}>AM</option>
                                <option value="pm"{{ now()->setTimezone(auth()->user()->account->timezone)->format('H') >= 12 ? 'selected' : null }}>PM</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                    <label for="end_at_date" class="block text-sm font-medium text-gray-700 sm:mt-px"> End: </label>
                    <div class="flex flex-row mt-1 sm:mt-0 sm:col-span-4">
                        <input type="date" name="end_at_date" autocomplete="off" value="{{ now()->setTimezone(auth()->user()->account->timezone)->format('Y-m-d') }}"/>

                        <div class="flex flex-row ml-6" x-show="!is_all_day">
                            <select class="w-20 mr-0.5" name="end_at_time_hour">
                                @for($i=1;$i<=12;$i++)
                                    <option value="{{ $i }}" {{ (now()->setTimezone(auth()->user()->account->timezone)->format('H') + 1 == $i || now()->setTimezone(auth()->user()->account->timezone)->format('H') - 11 == $i) ? 'selected' : null }}>{{ $i }}</option>
                                @endfor
                            </select>
                            <select class="w-20 mr-0.5" name="end_at_time_minute">
                                @for($j=0;$j<=55;$j = $j+5)
                                    <option value="{{ $j }}" {{ now()->setTimezone(auth()->user()->account->timezone)->format('i') == $j ? 'selected' : null }}>{{ $j == 0 ? '00' : ($j == 5 ? '05' : $j) }}</option>
                                @endfor
                            </select>
                            <select class="w-20" name="end_at_time_ampm">
                                <option value="am"{{ now()->setTimezone(auth()->user()->account->timezone)->format('H') - 1 < 12 ? 'selected' : null }}>AM</option>
                                <option value="pm"{{ now()->setTimezone(auth()->user()->account->timezone)->format('H') - 1 >= 12 ? 'selected' : null }}>PM</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="sm:grid sm:grid-cols-5 sm:gap-4 sm:items-center sm:border-b sm:border-gray-200 sm:py-2">
                    <label for="city" class="block text-sm font-medium text-gray-700 sm:mt-px"> Event Details </label>
                    <div class="mt-1 sm:mt-0 sm:col-span-4">
                        <textarea rows="2" name="description" class="admin-border-color border rounded-lg" type="text"></textarea>
                    </div>
                </div>
            </div>

        </div>
    </div>

</form>

<div class="flex flex-row justify-between mt-5 sm:mt-4 sm:ml-10 sm:pl-4 sm:flex">
    <div class="flex flex-row">
        @if($calendars)
            <button class="admin-button-blue" onclick="document.getElementById('save_event').submit()">
                <x-heroicon-o-check class="w-5 mr-1"/>
                Save Changes
            </button>
        @endif
        <button type="button" @click="closeModal()" class="admin-button-transparent ml-2">
            Cancel
        </button>
    </div>
</div>
