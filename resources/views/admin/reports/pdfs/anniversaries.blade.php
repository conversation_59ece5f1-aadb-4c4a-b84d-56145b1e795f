<?php

$col_count = 2;

if (in_array('home_address', $columns)) {
    $col_count++;
}
if (in_array('mailing_address', $columns)) {
    $col_count++;
}
if (in_array('email', $columns)) {
    $col_count++;
}
if (in_array('home_phone', $columns)) {
    $col_count++;
}
if (in_array('mobile_phone', $columns)) {
    $col_count++;
}

// First row, dates
// $date   = new DateTime($quarter->sunday_start_date); // Get this from our global settings
$i_html = null;

// First create our header
$i_html .= '<div style="font-weight: bold; font-size: 190%; width: 100%; text-align: center;">
Anniversaries
<br>
<span style="font-size: 80%;">' . Auth::user()->account->name . '</span>
</div>

    <table cellpadding="4" style="width: 100%; border-collapse: collapse; border-spacing: 2px;">

    <thead>
    <tr>
    <td style="width: 9%; text-align: center; font-weight: bold; border-top: 1px solid #333; border-left: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Date</td>
    <td style="width: 15%; text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Name</td>
    <td style="width: 10%; text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Years</td>';

if (in_array('home_address', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Home Address</td>';
}
if (in_array('mailing_address', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Mailing Address</td>';
}
if (in_array('email', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Email</td>';
}
if (in_array('home_phone', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Home Phone</td>';
}
if (in_array('mobile_phone', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Mobile Phone</td>';
}

$i_html .= '</tr></thead>';


$j = 0;
foreach ($users as $user):

    $i_html .= '<tr>
		<td style="text-align: center; border-left: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">'
        . ($user->date_married ? optional(\Carbon\Carbon::parse($user->date_married))->format('M d') . '<br>' . optional(\Carbon\Carbon::parse($user->date_married))->format('Y') : null)
        . '</td>
        <td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">';

    $i_html .= $user->display_first_name;
    if ($user->spouse) {
        $i_html .= ' and ' . $user->spouse->display_first_name;
    }
    $i_html .= '<br>' . $user->last_name;
    $i_html .= '</td>';

    $i_html .= '
    <td style="border-right: 1px solid #333; border-bottom: 1px solid #333; text-align: center;">
                ' . $user?->date_married?->diffForHumans(\Carbon\Carbon::create(now()->format('Y'), 12, 31, 23, 59, 59, auth()->user()->account->timezone), ['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]) . '
        <br>
        <span style="font-size: 80%">in ' . now()->format('Y') . '</span>
    </td>';

    if (in_array('home_address', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getFamilyAddress()?->getAddressString() . '</td>';
    }
    if (in_array('mailing_address', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getMailingAddress()?->getAddressString() . '</td>';
    }
    if (in_array('email', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">';
        if ($user_email = $user->getBestEmail()) {
            $i_html .= $user->display_first_name . ': <a href="mailto:' . $user_email?->email . '">' . $user_email?->email . '</a>';
        }
        if ($user_spouse_email = $user->spouse->getBestEmail()) {
            if ($user_email) {
                $i_html .= '<br>';
            }
            $i_html .= $user->spouse->display_first_name . ': <a href="mailto:' . $user_spouse_email?->email . '">' . $user_spouse_email?->email . '</a>';
        }
        $i_html .= '</td>';
    }
    if (in_array('home_phone', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getFamilyPhone()?->formattedNumber() . '</td>';
    }
    if (in_array('mobile_phone', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">';

        if ($user_phone = $user->getMobilePhone()) {
            $i_html .= $user->display_first_name . ': ' . $user_phone?->formattedNumber();
        }
        if ($user_spouse_phone = $user->spouse?->getMobilePhone()) {
            if ($user_phone) {
                $i_html .= '<br>';
            }
            $i_html .= $user->spouse->display_first_name . ': ' . $user_spouse_phone?->formattedNumber();
        }

        $i_html .= '</td>';
    }

    $i_html .= '</tr>';

endforeach;
?>
<html>
<style type=\"text/css\">
    body {
        font-family: 'Helvetica';
        font-size: 12px;
    }

    table {
        margin-top: 15px;
    }

    @page {
        margin: 0.4in 0.3in 0.4in 0.3in;
    }
</style>
<body>
<?= $i_html; ?>
</table>
</body>
</html>