<?php
// First row, dates
// $date   = new DateTime($quarter->sunday_start_date); // Get this from our global settings
$i_html = null;

// First create our header
$i_html .= '<div style="font-weight: bold; font-size: 190%; width: 100%; text-align: center;">
Missing Persons Report
<br>
<span style="font-size: 80%;">' . Auth::user()->account->name . '</span>
</div>

    <table cellpadding="4" style="width: 100%; border: 1px solid #333; border-collapse: collapse; border-spacing: 2px;">

    <thead style="margin-top: 4px;"><tr style="border-bottom: 1px solid #333;">
        <th>Last Attended</th>
        <th>Name</th>
        <th>Email</th>
        <th class="text-center">Home Phone</th>
        <th class="text-center">Mobile Phone</th>
        <th>Home Address</th>
    </tr></thead>';


$j = 0;
foreach ($users as $user):

    $i_html .= '<tr>';
    if (isset($is_export) && $is_export):
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->last_attended ? optional(\Carbon\Carbon::parse($user->last_attended))->format('Y-m-d') : null . '</td>';
    else:
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">';
        $i_html .= $user->last_attended ? optional(\Carbon\Carbon::parse($user->last_attended))->format('M d, y') : null;
        $i_html .= '<br><small style="color: #999999;">';
        $i_html .= '(' . $user->last_attended ? optional(\Carbon\Carbon::parse($user->last_attended))->diffForHumans(['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]) . ' ago' : 'unknown' . ')';
        $i_html .= '</small>';
    endif;

    $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->name . '</td>';
    $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">';
    if ($user->getBestEmail()) {
        $i_html .= '<a href="mailto:' . $user->getBestEmail()?->email . '">' . $user->getBestEmail()?->email . '</a>';
    }
    $i_html .= '</td>';
    $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getFamilyPhone()?->formattedNumber() . '</td>';
    $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getMobilePhone()?->formattedNumber() . '</td>';
    $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getFamilyAddress()?->getAddressString() . '</td>';

    $i_html .= '</tr>';

endforeach;
?>
<html>
<style type=\"text/css\">
    body {
        font-family: 'Helvetica';
        font-size: 12px;
    }

    table {
        margin-top: 15px;
    }

    @page {
        margin: 0.4in 0.3in 0.4in 0.3in;
    }
</style>
<body>
<?= $i_html; ?>
</table>
</body>
</html>