<?php

$col_count = 2;

if (in_array('home_address', $columns)) {
    $col_count++;
}
if (in_array('mailing_address', $columns)) {
    $col_count++;
}
if (in_array('email', $columns)) {
    $col_count++;
}
if (in_array('home_phone', $columns)) {
    $col_count++;
}
if (in_array('mobile_phone', $columns)) {
    $col_count++;
}

// First row, dates
// $date   = new DateTime($quarter->sunday_start_date); // Get this from our global settings
$i_html = null;

// First create our header
$i_html .= '<div style="font-weight: bold; font-size: 190%; width: 100%; text-align: center;">
Birthdays
<br>
<span style="font-size: 80%;">' . Auth::user()->account->name . '</span>
</div>

    <table cellpadding="4" style="width: 100%; border-collapse: collapse; border-spacing: 2px;">

    <thead>
    <tr>
    <td style="width: 8%; text-align: center; font-weight: bold; border-top: 1px solid #333; border-left: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Birthdate</td>
    <td style="width: 13%; text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Name</td>';

if (in_array('home_address', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Home Address</td>';
}
if (in_array('mailing_address', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Mailing Address</td>';
}
if (in_array('email', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Email</td>';
}
if (in_array('home_phone', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Home Phone</td>';
}
if (in_array('mobile_phone', $columns)) {
    $i_html .= '<td style="text-align: center; font-weight: bold; border-top: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">Mobile Phone</td>';
}

$i_html .= '</tr></thead>';


$j = 0;
foreach ($users as $user):

    $i_html .= '<tr>
		<td style="text-align: center; border-left: 1px solid #333; border-right: 1px solid #333; border-bottom: 1px solid #333;">' . ($user->birthdate ? optional(\Carbon\Carbon::parse($user->birthdate))->format('M d') : null) . '</td>
        <td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->name . '</td>';

    if (in_array('home_address', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getFamilyAddress()?->getAddressString() . '</td>';
    }
    if (in_array('mailing_address', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getMailingAddress()?->getAddressString() . '</td>';
    }
    if (in_array('email', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">';
        if ($user->getBestEmail()) {
            $i_html .= '<a href="mailto:' . $user->getBestEmail()?->email . '">' . $user->getBestEmail()?->email . '</a>';
        }
        $i_html .= '</td>';
    }
    if (in_array('home_phone', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getFamilyPhone()?->formattedNumber() . '</td>';
    }
    if (in_array('mobile_phone', $columns)) {
        $i_html .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $user->getMobilePhone()?->formattedNumber() . '</td>';
    }

    $i_html .= '</tr>';

endforeach;
?>
<html>
<style type=\"text/css\">
    body {
        font-family: 'Helvetica';
        font-size: 12px;
    }

    table {
        margin-top: 15px;
    }

    @page {
        margin: 0.4in 0.3in 0.4in 0.3in;
    }
</style>
<body>
<?= $i_html; ?>
</table>
</body>
</html>