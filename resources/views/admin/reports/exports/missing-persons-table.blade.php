<table class="">
    <thead class="">
    <tr>
        <th>Last Attended</th>
        <th>Name</th>
        <th>Email</th>
        <th class="text-center">Home Phone</th>
        <th class="text-center">Mobile Phone</th>
        <th>Home Address</th>
    </tr>
    </thead>
    <tbody>
    @foreach($users as $user)
        <tr>
            <td>
                @if(isset($is_export) && $is_export)
                    {{ $user->last_attended ? optional(\Carbon\Carbon::parse($user->last_attended))->format('Y-m-d') : null }}
                @else
                    {{ $user->last_attended ? optional(\Carbon\Carbon::parse($user->last_attended))->format('M d, Y') : null }}
                    <br>
                    <small class="">
                        ({{ $user->last_attended ? optional(\Carbon\Carbon::parse($user->last_attended))->diffForHumans(['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]) . ' ago' : 'unknown' }})
                    </small>
                @endif
            </td>
            <td class="font-semibold">
                @if(isset($is_export) && $is_export)
                    {{ $user->nameLastFirst }}
                @else
                    <a href="{{ route('admin.users.view', $user->id) }}" class="">{{ $user->nameLastFirst }}</a>
                @endif
            </td>
            <td>
                <a href="mailto:{{ optional($user->getPrimaryEmail())->email }}">
                    {{ optional($user->getPrimaryEmail())->email }}
                </a>
            </td>
            <td class="text-center">
                {{ optional($user->getFamilyPhone())->formattedNumber() }}
            </td>
            <td class="text-center">
                {{ optional($user->getMobilePhone())->formattedNumber() }}
            </td>
            <td>
                {{ $user->getFamilyAddress()?->getAddressString() }}
            </td>
        </tr>
    @endforeach
    </tbody>
</table>
