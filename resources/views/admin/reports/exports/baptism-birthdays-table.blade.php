<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        <th style="text-align: center">Baptism Birthday</th>
        <th>Name</th>
        @if(in_array('home_address', $columns))
            <th>
                Home Address
            </th>
        @endif
        @if(in_array('mailing_address', $columns))
            <th>
                Mailing Address
            </th>
        @endif
        @if(in_array('email', $columns))
            <th>
                Email
            </th>
        @endif
        @if(in_array('home_phone', $columns))
            <th>
                Home Phone
            </th>
        @endif
        @if(in_array('mobile_phone', $columns))
            <th>
                Mobile Phone
            </th>
        @endif
    </tr>
    </thead>
    <tbody>
    @foreach($users as $user)
        <tr>
            <td style="text-align: center">
                {{ $user->date_baptism ? optional(\Carbon\Carbon::parse($user->date_baptism))->format('M d') : null }}
            </td>
            <td>
                @if(isset($is_export) && $is_export)
                    {{ $user->nameLastFirst }}
                @else
                    <a href="{{ route('admin.users.view', $user->id) }}" class="d-print-none">{{ $user->nameLastFirst }}</a>
                    <span class="d-print-inline d-none">{{ $user->nameLastFirst }}</span>
                @endif
            </td>
            @if(in_array('home_address', $columns))
                <td class="text-center">
                    {{ $user->getFamilyAddress()?->getAddressString() }}
                </td>
            @endif
            @if(in_array('mailing_address', $columns))
                <td class="text-center">
                    {{ $user->getMailingAddress()?->getAddressString() }}
                </td>
            @endif
            @if(in_array('email', $columns))
                <td>
                    @if($user->getBestEmail())
                        <a href="mailto:{{ $user->getBestEmail()?->email }}">{{ $user->getBestEmail()?->email }}</a>
                    @endif
                </td>
            @endif
            @if(in_array('home_phone', $columns))
                <td class="text-center">
                    {{ $user->getFamilyPhone()?->formattedNumber() }}
                </td>
            @endif
            @if(in_array('mobile_phone', $columns))
                <td class="text-center">
                    {{ $user->getMobilePhone()?->formattedNumber() }}
                </td>
            @endif
        </tr>
    @endforeach
    </tbody>
</table>
