<table class="">
    <thead class="">
    <tr>
        <th>Family Name</th>
        <th>Last Name</th>
        <th>First Name</th>
        <th>Family Role</th>
        <th>Email</th>
        <th class="text-center">Mobile Phone</th>
        @foreach($dates as $date)
            <td>{{ $date->format('m/d/Y') }}</td>
        @endforeach
    </tr>
    </thead>
    <tbody>
    @foreach($users as $user)
        @php
            $attendance = \App\Attendance\Attendance::forUser($user)
                ->when($date_range, function($query) use ($date_range) {
                    $query->attendanceAfterDate($date_range->start());
                })->when(!$date_range, function($query) {
                    $query->attendanceAfterDate(now()->subWeeks(7));
                })
                ->with(['user:id,first_name,last_name'])
                ->get();
        @endphp
        <tr>
            <td>
                @if($user->id == $user->family_id)
                    {{ $user->getHeadOfFamily()->last_name }}
                @endif
            </td>
            <td>
                {{ $user->last_name }}
            </td>
            <td>
                {{ $user->first_name }}
            </td>
            <td>
                {{ $user->family_role }}
            </td>
            <td>
                <a href="mailto:{{ optional($user->getPrimaryEmail())->email }}">
                    {{ optional($user->getPrimaryEmail())->email }}
                </a>
            </td>
            <td class="text-center">
                {{ optional($user->getMobilePhone())->formattedNumber() }}
            </td>
            @foreach($dates as $date)
                <td>
                    {{-- We have to do this, because using contains() normally does not do the field casting needed first for the date_attendance field (it's a Carbon instance, not a string) --}}
                    @if($attendance->contains(function ($value, $key) use ($date) {
                        return $value->date_attendance->format('Y-m-d') == $date->format('Y-m-d');
                    }))
                        X
                    @endif
                </td>
            @endforeach
        </tr>
    @endforeach
    </tbody>
</table>
