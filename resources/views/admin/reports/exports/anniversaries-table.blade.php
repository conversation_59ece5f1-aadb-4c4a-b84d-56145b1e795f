<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        <th style="text-align: center">Date</th>
        <th style="text-align: center">Celebrating</th>
        <th>Name</th>
        @if(in_array('home_address', $columns))
            <th>
                Home Address
            </th>
        @endif
        @if(in_array('mailing_address', $columns))
            <th>
                Mailing Address
            </th>
        @endif
        @if(in_array('email', $columns))
            <th>
                Emails
            </th>
        @endif
        @if(in_array('home_phone', $columns))
            <th>
                Home Phone
            </th>
        @endif
        @if(in_array('mobile_phone', $columns))
            <th>
                Mobile Phones
            </th>
        @endif
    </tr>
    </thead>
    <tbody>
    @foreach($users as $user)
        <tr>
            <td style="text-align: center">
                {{ $user->date_married ? optional(\Carbon\Carbon::parse($user->date_married))->format('M d, Y') : null }}
            </td>
            <td style="text-align: center">
                {{ $user->date_married->diffForHumans(\Carbon\Carbon::create(now()->format('Y'), 12, 31, 23, 59, 59, auth()->user()->account->timezone), ['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]) }}
                <br>
                <small>in {{ now()->format('Y') }}</small>
            </td>
            <td>
                @if(isset($is_export) && $is_export)
                    {{ $user->display_first_name }}
                    @if($user->spouse)
                        {{ ' and ' . $user->spouse->display_first_name }}
                    @endif
                    {{ ' ' . $user->last_name }}
                @else
                    <a href="{{ route('admin.users.view', $user->id) }}" class="d-print-none">
                        {{ $user->display_first_name }}
                        @if($user->spouse)
                            {{ ' and ' . $user->spouse->display_first_name }}
                        @endif
                        {{ ' ' . $user->last_name }}
                    </a>
                    <span class="d-print-inline d-none">
                        {{ $user->display_first_name }}
                        @if($user->spouse)
                            {{ ' and ' . $user->spouse->display_first_name }}
                        @endif
                        {{ ' ' . $user->last_name }}
                    </span>
                @endif
            </td>
            @if(in_array('home_address', $columns))
                <td class="text-center">
                    {{ $user->getFamilyAddress()?->getAddressString() }}
                </td>
            @endif
            @if(in_array('mailing_address', $columns))
                <td class="text-center">
                    {{ $user->getMailingAddress()?->getAddressString() }}
                </td>
            @endif
            @if(in_array('email', $columns))
                <td>
                    @if($user_email = $user->getBestEmail())
                        {{ $user->display_first_name }}: <a href="mailto:{{ $user_email?->email }}">{{ $user_email?->email }}</a>
                    @endif
                    @if($user_spouse_email = $user->spouse?->getBestEmail())
                        @if($user_email)
                            <br>
                        @endif
                        {{ $user->spouse->display_first_name }}: <a href="mailto:{{ $user_spouse_email?->email }}">{{ $user_spouse_email?->email }}</a>
                    @endif
                </td>
            @endif
            @if(in_array('home_phone', $columns))
                <td class="text-center">
                    {{ $user->getFamilyPhone()?->formattedNumber() }}
                </td>
            @endif
            @if(in_array('mobile_phone', $columns))
                <td>
                    @if($user_phone = $user->getMobilePhone())
                        {{ $user->display_first_name }}: {{ $user_phone?->formattedNumber() }}
                    @endif
                    @if($user_spouse_phone = $user->spouse?->getMobilePhone())
                        @if($user_phone)
                            <br>
                        @endif
                        {{ $user->spouse->display_first_name }}: {{ $user_spouse_phone?->formattedNumber() }}
                    @endif
                </td>
            @endif
        </tr>
    @endforeach
    </tbody>
</table>
