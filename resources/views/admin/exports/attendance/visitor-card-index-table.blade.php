<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        <th>Name</th>
        <th>first_name</th>
        <th>last_name</th>
        <th>spouse_name</th>
        <th>family_member_names</th>
        <th>address1</th>
        <th>address2</th>
        <th>city</th>
        <th>state</th>
        <th>zip</th>
        <th>country</th>
        <th>phone</th>
        <th>email</th>
        <th>guest_of</th>
        <th>comments</th>
        <th>is_member</th>
        <th>is_visitor</th>
        <th>Read At</th>
        <th>Marked Spam?</th>
    </tr>
    </thead>
    <tbody>
    @foreach($rows as $row)
        <tr>
            <td>
                {{ $row->name }}
            </td>
            <td>
                {{ $row->first_name }}
            </td>
            <td>
                {{ $row->last_name }}
            </td>
            <td>
                {{ $row->spouse_name }}
            </td>
            <td>
                {{ $row->family_member_names }}
            </td>
            <td>
                {{ $row->address1 }}
            </td>
            <td>
                {{ $row->address2 }}
            </td>
            <td>
                {{ $row->city }}
            </td>
            <td>
                {{ $row->state }}
            </td>
            <td>
                {{ $row->zip }}
            </td>
            <td>
                {{ $row->country }}
            </td>
            <td>
                {{ $row->phone }}
            </td>
            <td>
                {{ $row->email }}
            </td>
            <td>
                {{ $row->guest_of }}
            </td>
            <td>
                {{ $row->comments }}
            </td>
            <td>
                {{ $row->is_member ? 'Yes' : '' }}
            </td>
            <td>
                {{ $row->is_visitor ? 'Yes' : '' }}
            </td>
            <td>
                {{ $row->read_at ? $row->read_at?->format('Y-m-d') : '' }}
            </td>
            <td>
                {{ $row->is_spam ? 'Yes' : 'No' }}
            </td>
        </tr>
    @endforeach
    </tbody>
</table>