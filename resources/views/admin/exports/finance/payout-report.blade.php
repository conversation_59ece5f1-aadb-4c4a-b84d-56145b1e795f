<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        <th>Give Date</th>
        <th>Name</th>
        <th>Bucket</th>
        <th>Amount</th>
        <th>Fee</th>
        <th>Deposited</th>
    </tr>
    </thead>
    <tbody>
    @foreach($payments as $payment)
        <tr>
            <td>
                {{ $payment->created_at->format('M j, y') }}
            </td>
            <td>
                {{ $payment->user->name }}
            </td>
            <td>
                {{ $payment->bucket->name }}
            </td>
            <td>
                {{ Brick\Money\Money::ofMinor($payment->amount, 'USD')->getAmount() }}
            </td>
            <td>
                {{ optional(Brick\Money\Money::ofMinor($payment->amount_fee ?: 0, 'USD'))->getAmount() }}
            </td>
            <td>
                {{ optional(Brick\Money\Money::ofMinor($payment->amount_deposited ?: 0, 'USD'))->getAmount() }}
            </td>
            <td>
                {{ ($payment->payout ? $payment->payout->deposited_at->format('M j, y') : '--') }}
            </td>
        </tr>
    @endforeach
    </tbody>
</table>