<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        <th>Give Date</th>
        <th>Name</th>
        <th>Bucket</th>
        <th>Amount</th>
        <th>Fee</th>
        <th>Deposited</th>
        <th>Payout</th>
    </tr>
    </thead>
    <tbody>
    @foreach($contributions as $contribution)
        <tr>
            <td>
                {{ $contribution->created_at->format('M j, y') }}
            </td>
            <td>
                {{ $contribution->user->name }}
            </td>
            <td>
                {{ $contribution->bucket->name }}
            </td>
            <td>
                {{ Brick\Money\Money::ofMinor($contribution->amount, 'USD')->getAmount() }}
            </td>
            <td>
                {{ optional(Brick\Money\Money::ofMinor($contribution->amount_fee ?: 0, 'USD'))->getAmount() }}
            </td>
            <td>
                {{ optional(Brick\Money\Money::ofMinor($contribution->amount_deposited ?: 0, 'USD'))->getAmount() }}
            </td>
            <td>
                {{ ($contribution->payout ? $contribution->payout->deposited_at->format('M j, y') : '--') }}
            </td>
        </tr>
    @endforeach
    </tbody>
</table>