<table class="table table-striped table-bordered table-hover table-sm d-print-table">
    <thead class="thead-dark">
    <tr>
        <td>Is Registered</td>
        <td>First Name</td>
        <td>Last Name</td>
        <td>Birthdate</td>
        <td>Family Role</td>
        <td>Gender</td>
        <td>Marital Status</td>
        <td>Allergies</td>
        <td>Special Needs</td>
        <td>Mobile Phone</td>
        <td>Home Phone</td>
        <td>Email</td>
        @foreach($groups as $group)
            <td>{{ $group->name }}</td>
        @endforeach
        @foreach($form_fields as $field)
            <th>{{ $field->title }}</th>
        @endforeach
    </tr>
    </thead>
    <tbody>
    @foreach($users as $user)
        {{ dd($user->registrationContacts) }}
        <tr>
            <td>{{ $user->isRegisteredUser() ? 'Yes' : 'No' }}</td>
            <td>{{ $user->first_name }}</td>
            <td>{{ $user->last_name }}</td>
            <td>{{ $user->birthdate ? $user->birthdate->format('Y-m-d') : '' }}</td>
            <td>{{ $user->family_role }}</td>
            <td>{{ $user->gender }}</td>
            <td>{{ $user->marital_status }}</td>
            <td>{{ $user->allergies }}</td>
            <td>{{ $user->special_needs }}</td>
            <td>{{ $user->mobile_phone }}</td>
            <td>{{ $user->home_phone }}</td>
            <td>{{ $user->email }}</td>
            @foreach($groups as $group)
                <td>
                    @if($user->groups->contains($group))
                        {{ $group->name }}
                    @endif
                </td>
            @endforeach
            @foreach($form_fields as $field)
                <td>
                    @php
                        $response = $user->formResponses->firstWhere('program_registration_form_field_id', $field->id);
                    @endphp
                    @if($response)
                        @if($field->is_text_answer)
                            {{ $response->response }}
                        @elseif($field->allow_multiple_answers)
                            {{ implode(', ', $response->multi_response ?? []) }}
                        @else
                            {{ $response->response }}
                        @endif
                    @endif
                </td>
            @endforeach
        </tr>
    @endforeach
    </tbody>
</table>