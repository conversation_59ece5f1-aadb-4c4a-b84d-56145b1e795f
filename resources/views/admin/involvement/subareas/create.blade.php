@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.involvement.subareas.store', [$category, $area]) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    {{ csrf_field() }}
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-edit mr-2" aria-hidden="true"></i>Create Subarea
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    This will create a new SUBAREA under a <strong>Area</strong>.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:pb-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                This will create an new <strong>subarea</strong> under the {{ $area->name }} area.
                            </label>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-4">
                        <div>
                            <label for="name" class="block text-base font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Name
                            </label>
                        </div>
                        <div class="sm:col-span-3">
                            <input type="text" name="name" placeholder="Subarea name..." class="{{ $inputCSS }}" autocomplete="off" value=""/>
                        </div>
                    </div>
                </div>
                <div class="py-3 sm:px-5">
                    <div class="space-y-3 py-3">
                        <fieldset>
                            <legend class="text-sm font-medium text-gray-900">
                                Subarea Options
                            </legend>
                            <div class="mt-2 space-y-3 sm:ml-4">
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="men_only" name="men_only" value="1" checked/>
                                        </div>
                                        <div class="pl-7 text-base">
                                            <label for="men_only" class="font-medium text-gray-900">
                                                Show for <strong>men</strong>
                                                <p class="text-gray-500 font-normal">

                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="relative flex items-start">
                                    <div class="absolute flex items-center h-5">
                                        <input type="checkbox" class="{{ $checkboxCSS }}" id="women_only" name="women_only" value="1" checked/>
                                    </div>
                                    <div class="pl-7 text-base">
                                        <label for="women_only" class="font-medium text-gray-900">
                                            Show for <strong>women</strong>
                                            <p class="text-gray-500 font-normal">

                                            </p>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="baptized_only" name="baptized_only" value="1"/>
                                        </div>
                                        <div class="pl-7 text-base">
                                            <label for="baptized_only" class="font-medium text-gray-900">
                                                Show for <strong>baptized users only</strong>
                                                <p class="text-gray-500 font-normal">

                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="approved_to_teach_only" name="approved_to_teach_only" value="1"/>
                                        </div>
                                        <div class="pl-7 text-base">
                                            <label for="approved_to_teach_only" class="font-medium text-gray-900">
                                                Show for <strong>"approved to teach" users only</strong>
                                                <p class="text-gray-500 font-normal">

                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="relative flex items-start">
                                        <div class="absolute flex items-center h-5">
                                            <input type="checkbox" class="{{ $checkboxCSS }}" id="completed_background_check_only" name="completed_background_check_only" value="1"/>
                                        </div>
                                        <div class="pl-7 text-base">
                                            <label for="completed_background_check_only" class="font-medium text-gray-900">
                                                Show for <strong>"completed a background check" users only</strong>
                                                <p class="text-gray-500 font-normal">

                                                </p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-sm font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                    {{--                    <div>--}}
                    {{--                        @can('delete', $category)--}}
                    {{--                            <button type="button" onclick="document.getElementById('delete_phone_').submit()" class="inline-flex justify-center py-2 px-4 border border-red-600 text-sm font-medium rounded-md text-red-600 bg-white hover:bg-red-600 hover:text-white focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500" title="Delete">Delete</button>--}}
                    {{--                        @endcan--}}
                    {{--                    </div>--}}
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>

{{--<form method="post" action="{{ route('admin.users.phone.delete', [$user, $phone]) }}" id="delete_phone_{{ $phone->id }}">--}}
{{--    @csrf--}}
{{--</form>--}}
