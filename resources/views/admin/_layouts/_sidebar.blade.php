@php
    $heading_class = 'uppercase pb-0 pt-2 text-neutral-600 rounded-md opacity-90 text-xs font-bold pl-2';
    $a_active = 'flex justify-between items-center px-2 py-2 text-sm font-normal rounded-sm text-neutral-50 bg-blue-600 focus:outline-hidden focus:bg-neutral-50';
    $a_inactive = 'flex justify-between items-center px-2 py-2 text-sm font-normal rounded-sm text-neutral-950 hover:text-black hover:bg-neutral-200 focus:outline-hidden focus:bg-neutral-50';
    $i_active = 'fas text-center w-5 mr-2';
    $i_inactive = 'fal text-center w-5 mr-2 text-neutral-600';
    $sub_a_active = 'flex justify-between items-center px-2 py-2 text-sm font-normal rounded-sm text-blue-600 bg-neutral-200 focus:outline-hidden focus:bg-neutral-50';
    $sub_a_inactive = 'flex justify-between items-center px-2 py-2 text-sm font-normal rounded-sm text-gray-400 hover:bg-neutral-200 focus:outline-hidden focus:bg-neutral-50';
@endphp

<div class="print:hidden relative" x-data="{ menuOpen: false }">

    <style>
        [data-tooltip-sidebar]:hover::after {
            display: block;
            position: absolute;
            content: attr(data-tooltip-sidebar);
            background: #000;
            padding: .25em .5em;
            color: #fff;
            white-space: nowrap;
            margin-top: 26px;
            margin-left: -50px;
            border-radius: 4px;
            font-size: 88%;
            z-index: 1000;
        }
    </style>

    {{-- Static sidebar for desktop --}}
    <div x-cloak :class="menuOpen ? 'block absolute z-50' : 'hidden relative lg:fixed'" class="relative w-auto lg:w-64 lg:inset-y-0 lg:z-50 lg:flex lg:flex-col">
        {{-- Sidebar component, swap this element with another sidebar if you like --}}
        <div class="flex grow flex-col overflow-y-auto bg-neutral-50">
            <div class="px-2 mb-2">
                <div class="flex mt-2 lg:mt-4 shrink-0 items-center">
                    <button x-on:click="menuOpen = !menuOpen" type="button" class="flex flex-row lg:hidden p-1 mr-2 border border-gray-400 rounded-sm">
                        <span class="sr-only">Close sidebar</span>
                        <svg class="h-6 w-6 text-gray-800" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                        <span class="ml-1 text-sm my-auto mr-0.5">Close</span>
                    </button>
                    <img class="h-8 ml-auto lg:ml-1" src="/img/lightpost-logo-200.svg" alt="Lightpost Logo"/>

                    @if(auth()->user()->is_super)
                        <a href="{{ route('super.settings.index-root') }}"
                           class="py-1.5 rounded-sm  hover:bg-white text-neutral-50 hover:text-gray-600 ml-auto border border-transparent hover:border-gray-400 px-2">
                            <div class="truncate text-xs">
                                _
                            </div>
                        </a>
                    @endif
                    <a href="{{ route('app.home.index') }}"
                       class="flex justify-between py-1.5 text-sm rounded-sm text-gray-500 hover:bg-white focus:outline-hidden focus:bg-neutral-50 ml-auto border border-gray-300 hover:border-gray-400 px-2"
                       data-tooltip-sidebar="Switch to Web App">
                        <div class="truncate flex flex-row mx-auto text-xs font-light">
                            <x-heroicon-o-arrows-right-left class="w-4"/>
                            <span class="ml-1 lg:hidden">Switch to Web App</span>
                        </div>
                    </a>
                </div>
                <form action="{{ route('admin.users.index') }}" method="get" class="mt-3 relative text-gray-400 focus-within:text-gray-500">
                    <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <input id="global_search_query" name="q" autocomplete="off"
                           class="text-sm border-gray-300 font-light w-full pl-8 pr-3 py-2 rounded-md focus:text-neutral-600"
                           placeholder="Search users..." type="search"/>
                    <div class="absolute inset-y-0 right-0 flex py-1.5 pr-1.5">
                        <kbd class="inline-flex items-center border border-gray-300 bg-neutral-50 rounded-sm px-1.5 text-xs font-sans font-medium text-gray-400">
                            {!! str_contains(request()->header('user-agent'), 'Macintosh') ? '⌘K' : 'Ctrl+K' !!}
                        </kbd>
                    </div>
                </form>
            </div>
            <nav class="flex-1 flex-col justify-between py-2 p-2 overflow-y-auto overflow-x-hidden">
                <div>
                    <div class="space-y-1">
                        <a href="/" class="{{ request()->is('/') ? $a_active : $a_inactive }}">
                            <div class="truncate">
                                <i class="fa-home fa-lg {{ request()->is('/') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                Dashboard
                            </div>
                        </a>

                        <div class="{{ $heading_class }}">
                            Members
                        </div>

                        @can('retrieve', \App\Users\User::class)
                            <a href="{{ route('admin.users.index') }}" class="{{ request()->is('users*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="{{ request()->is('users*') ? $i_active : $i_inactive }} fa-user-friends fa-lg" aria-hidden="true"></i>
                                    People
                                </div>
                            </a>
                        @endcan

                        @if(\Illuminate\Support\Facades\Gate::check('index', \App\Attendance\Attendance::class) || \Illuminate\Support\Facades\Gate::check('manage', \App\Attendance\Attendance::class))
                            <a href="{{ route('admin.attendance.index') }}" class="{{ request()->is('attendance*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-user-chart fa-lg {{ request()->is('attendance*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Attendance
                                </div>
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeature('feature.involvement'))
                            @can('index', App\Involvement\Category::class)
                                <a href="{{ route('admin.involvement.index') }}" class="{{ (request()->is('involvement*') && !request()->is('involvement/volunteers*')) ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-user-hard-hat fa-lg {{ (request()->is('involvement*') && !request()->is('involvement/volunteers*')) ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Involvement
                                    </div>
                                </a>
                            @endcan
                        @endif
                        @if(auth()->user()->account->hasFeature('feature.involvement'))
                            @can('manageVolunteers', App\Involvement\Category::class)
                                <a href="{{ route('admin.involvement.volunteers.index') }}" class="{{ request()->is('involvement/volunteers*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-hands-helping fa-lg {{ request()->is('involvement/volunteers*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Volunteers
                                    </div>
                                </a>
                            @endcan
                        @endif
                        @can('index', \App\BibleClasses\BibleClass::class)
                            <a href="{{ route('admin.bible-classes.index') }}" class="{{ request()->is('bible-class*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-books fa-lg {{ request()->is('bible-class*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Bible Classes
                                </div>
                            </a>
                        @endcan
                        @if(auth()->user()->account->hasFeature('feature.visitor_tracking'))
                            @can('view', \App\Visitors\Visitor::class)
                                <a href="{{ route('admin.visitors.settings') }}" class="{{ request()->is('visitors*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        {{--                                <span class="float-right bg-purple-500 text-white px-1 text-xs rounded-sm">New!</span>--}}
                                        <i class="fa-arrow-pointer fa-lg {{ request()->is('visitors*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Visitor Tracking
                                    </div>
                                </a>
                            @endcan
                        @endif

                        <div class="{{ $heading_class }}">
                            Communication
                        </div>

                        @can('index', \App\Calendars\Calendar::class)
                            <a href="{{ route('admin.calendars.index') }}" class="{{ request()->is('calendars*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-calendar-alt fa-lg {{ request()->is('calendars*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Calendars
                                </div>
                            </a>
                        @endcan
                        @can('index', \App\Prayers\Prayer::class)
                            <a href="{{ route('admin.prayers.index') }}" class="{{ request()->is('prayers*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-praying-hands fa-lg {{ request()->is('prayers*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Prayer List
                                </div>
                            </a>
                        @endcan
                        @if(auth()->user()->account->hasFeature('feature.files'))
                            @can('index', \App\AccountFiles\AccountFile::class)
                                <a href="{{ route('admin.account-files.index') }}" class="{{ request()->is('account-files*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-copy fa-lg {{ request()->is('account-files*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Files
                                    </div>
                                </a>
                            @endcan
                        @endif
                        @if(auth()->user()->account->hasFeature('feature.sermons'))
                            @can('index', \App\Sermons\Sermon::class)
                                <a href="{{ route('admin.sermons.index') }}" class="{{ request()->is('sermons*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-cloud-download fa-lg {{ request()->is('sermons*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Sermons
                                    </div>
                                </a>
                            @endcan
                        @endif
                        @if(auth()->user()->account->hasFeature('feature.worship_assignments'))
                            @can('index', \App\WorshipAssignments\Group::class)
                                <a href="{{ route('admin.worship-assignments.index') }}" class="{{ request()->is('worship-assignments*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-tasks fa-lg {{ request()->is('worship-assignments*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Assignments
                                    </div>
                                </a>
                            @endcan
                        @endif
                        @can('index', \App\Podcasts\Podcast::class)
                            <a href="{{ route('admin.podcasts.index') }}" class="{{ request()->is('podcasts*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-microphone fa-lg {{ request()->is('podcasts*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Podcasts
                                </div>
                            </a>
                        @endcan
                        @can('index', App\Involvement\Category::class)
                            <a href="{{ route('admin.crises.index') }}" class="{{ request()->is('crises*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-clipboard-check fa-lg {{ request()->is('crises*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Crisis Check-in
                                </div>
                            </a>
                        @endcan

                        <div class="{{ $heading_class }}">
                            Data
                        </div>

                        @can('index-reports')
                            <a href="{{ route('admin.reports.index') }}" class="{{ request()->is('reports*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-analytics fa-lg {{ request()->is('reports*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Reports
                                </div>
                            </a>
                        @endcan
                        @if(auth()->user()->account->hasFeature('feature.finance'))
                            <a href="{{ route('admin.finance.index') }}" class="{{ (request()->routeIs('admin.finance.*') && !request()->routeIs('admin.finance.giving.*')) ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-money-check-dollar fa-lg {{ (request()->routeIs('admin.finance.*') && !request()->routeIs('admin.finance.giving.*')) ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Finance
                                </div>
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeature('feature.online_giving'))
                            <a href="{{ route('admin.finance.giving.index') }}" class="{{ request()->is('finance/giving*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-hand-holding-usd fa-lg {{ request()->is('finance/giving*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Online Giving
                                </div>
                            </a>
                        @endif
                        @if(auth()->user()->account->hasFeature('feature.programs'))
                            <a href="{{ route('admin.programs.index') }}" class="{{ request()->is('programs*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-lightbulb-on fa-lg {{ request()->is('programs*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Programs
                                </div>
                            </a>
                        @endif

                        @canany(['manage', 'managePages', 'viewStats'], \App\Website\Website::class)
                            <div class="{{ $heading_class }}">
                                Website
                            </div>

                            @canany(['manage', 'managePages'], \App\Website\Website::class)
                                <a href="{{ route('admin.website.pages.index') }}" class="{{ request()->is('website/pages*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-files fa-lg {{ request()->is('website/pages*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Website Pages
                                    </div>
                                </a>
                            @endcan
                            @can(['manage'], \App\Website\Website::class)
                                <a href="{{ route('admin.website.settings.carousel') }}" class="{{ request()->is('website/settings*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-cog fa-lg {{ request()->is('website/settings*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Website Settings
                                    </div>
                                </a>
                            @endcan
                            @canany(['manage', 'viewStats'], \App\Website\Website::class)
                                <a href="{{ route('admin.groups.index') }}" class="{{ request()->is('website/stats*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-chart-line fa-lg {{ request()->is('website/stats*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        Stats
                                    </div>
                                </a>
                            @endcan
                        @endcan

                        <div class="{{ $heading_class }}">
                            Settings
                        </div>

                        @can('index', \App\Users\Group::class)
                            <a href="{{ route('admin.groups.index') }}" class="{{ request()->is('groups*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-users fa-lg {{ request()->is('groups*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Groups
                                </div>
                            </a>
                        @endcan
                        @can('index', \App\Users\Role::class)
                            <a href="{{ route('admin.roles.index') }}" class="{{ request()->is('roles*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-lock-alt fa-lg {{ request()->is('roles*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Roles
                                </div>
                            </a>
                        @endcan
                        @if(auth()->user()->account->hasFeature('feature.grades'))
                            @can('manage', \App\Accounts\Grade::class)
                                <a href="{{ route('admin.accounts.grades.index') }}" class="{{ request()->is('grades*') ? $a_active : $a_inactive }}">
                                    <div class="truncate">
                                        <i class="fa-user-graduate fa-lg {{ request()->is('grades*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                        School Grades
                                    </div>
                                </a>
                            @endcan
                        @endif
                        @can('manage', \App\Accounts\Account::class)
                            <a href="{{ route('admin.accounts.settings.index') }}" class="{{ request()->is('account/settings*') ? $a_active : $a_inactive }}">
                                <div class="truncate">
                                    <i class="fa-cog fa-lg {{ request()->is('account/settings*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                                    Account Settings
                                </div>
                            </a>
                        @endcan
                    </div>
                </div>
                <div>
                    <div class="{{ $heading_class }}">
                        Help
                    </div>

                    <a href="https://docs.{{ config('app.domains.frontend') }}?ref=admin" target="_blank" class="{{ $a_inactive }}">
                        <div class="truncate">
                            <i class="fa-question-circle fa-lg {{ $i_inactive }}" aria-hidden="true"></i>
                            Documentation
                        </div>
                    </a>

                    <a href="{{ route('admin.changelog.index') }}" class="{{ request()->is('changelog*') ? $a_active : $a_inactive }}">
                        <div class="truncate">
                            <i class="fa-check-square fa-lg {{ request()->is('changelog*') ? $i_active : $i_inactive }}" aria-hidden="true"></i>
                            Changelog
                        </div>
                    </a>
                </div>
            </nav>
            <div class="px-2 border-t border-neutral-200 mt-auto">
                <div class="relative flex items-center gap-x-3 rounded-lg px-2 hover:cursor-pointer py-2 my-2 text-sm font-medium text-neutral-600 hover:bg-neutral-200"
                     x-data="{ bottomMenuOpen: false }"
                     x-on:click="bottomMenuOpen = !bottomMenuOpen"
                     x-on:click.outside="bottomMenuOpen = false">
                    <div x-cloak x-show="bottomMenuOpen" class="w-full absolute left-0 bottom-0 z-10 mx-auto mb-16 mt-1 origin-top divide-y divide-gray-200 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-hidden"
                         x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95"
                         role="menu" aria-orientation="vertical" aria-labelledby="options-menu-button" tabindex="-1">
                        <div class="py-1 px-1" role="none">
                            <a href="{{ route('admin.users.view', auth()->user()->id) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-600 hover:text-white rounded-md" role="menuitem" tabindex="-1" id="options-menu-item-0">My Profile</a>
                            <a href="{{ route('logout') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-600 hover:text-white rounded-md" role="menuitem" tabindex="-1" id="options-menu-item-2">
                                Sign out
                            </a>
                        </div>
                    </div>

                    <div class="w-10 flex-none">
                        @if(auth()->user()->avatar)
                            <img class="size-10 rounded-lg border border-neutral-300" src="{{ auth()->user()->avatar->getCdnUrl(512) }}"/>
                        @else
                            <div class="p-1 size-10 rounded-lg border border-neutral-300">
                                <svg class="size-8 text-neutral-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        @endif
                    </div>
                    <span class="sr-only">Your profile</span>
                    <span class="min-w-0">
                        <span aria-hidden="true" class="text-sm">{{ auth()->user()->name }}</span>
                        <span aria-hidden="true" class="block font-light text-xs truncate">{{ auth()->user()->getBestEmail()?->email }}</span>
                    </span>
                    <div class="ml-auto flex-none w-3">
                        <x-heroicon-s-chevron-up class="w-3"/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="sticky top-0 z-40 flex items-center gap-x-4 bg-white px-4 py-2 sm:px-6 lg:hidden border-b border-neutral-300">
        <button x-on:click="menuOpen = !menuOpen" type="button" class="flex flex-row p-1 pr-1.5 text-neutral-500 border border-neutral-500 rounded-sm lg:hidden">
            <span class="sr-only">Open sidebar</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/>
            </svg>
            <span class="ml-1 text-sm my-auto">Menu</span>
        </button>
        <img class="flex-1 h-8 mx-auto" src="/img/lightpost-logo-200.svg" alt="Lightpost Logo"/>
        <div class="w-12"></div>
    </div>

</div>