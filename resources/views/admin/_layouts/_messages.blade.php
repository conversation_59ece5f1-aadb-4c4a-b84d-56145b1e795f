@php
    $message_container = 'mx-auto py-1 px-4 flex items-center justify-between flex-wrap';
@endphp

@if(auth()->user()->account->is_late_on_payment)
    <div class="px-8 py-4 mb-4 rounded-md bg-red-100 text-red-900">
        Your account has outstanding invoices due. Please <a href="mailto:{{ config('app.support_email') }}">contact support</a> to avoid account suspension.
    </div>
@endif

@if(session('message.success'))
    <div class="bg-green-200 border border-green-500 lg:absolute top-0 left-0 right-0 lg:-mt-11 rounded-md lg:rounded-t-none mb-4" id="success-modal">
        <div class="{{ $message_container }}">
            <div class="w-0 flex-1 flex items-center">
                <span class="flex rounded-lg text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="4" d="M5 13l4 4L19 7"/>
                    </svg>
                </span>
                <p class="ml-2 text-green-700">
                    {{ session('message.success') }}
                </p>
            </div>
            <div class="order-2 shrink-0 sm:order-3">
                <button onclick="document.getElementById('success-modal').remove();" type="button" class="-mr-1 flex p-1 rounded-md hover:bg-green-500 text-green-600 hover:text-white hover:cursor-pointer focus:outline-hidden focus:ring-2 focus:ring-white sm:-mr-2">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
@endif

@if(session('message.warning'))
    <div class="bg-amber-100 border border-amber-500 lg:absolute top-0 left-0 right-0 lg:-mt-11 rounded-md lg:rounded-t-none mb-4" id="warning-modal">
        <div class="{{ $message_container }}">
            <div class="w-0 flex-1 flex items-center">
                <span class="flex rounded-lg text-amber-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                </span>
                <p class="ml-2 text-amber-800">
                    {{ session('message.warning') }}
                </p>
            </div>
            <div class="order-2 shrink-0 sm:order-3">
                <button onclick="document.getElementById('warning-modal').remove();" type="button" class="-mr-1 flex p-1 rounded-md hover:bg-amber-500 text-amber-600 hover:text-white hover:cursor-pointer focus:outline-hidden focus:ring-2 focus:ring-white sm:-mr-2">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
@endif

@if(@session('message.failure'))
    <div class="bg-red-100 border border-red-500 lg:absolute top-0 left-0 right-0 lg:-mt-11 rounded-md lg:rounded-t-none mb-4" id="failure-modal">
        <div class="{{ $message_container }}">
            <div class="w-0 flex-1 flex items-center">
                <span class="flex rounded-lg text-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </span>
                <p class="ml-2 text-red-700">
                    {{ session('message.failure') }}
                </p>
            </div>
            <div class="order-2 shrink-0 sm:order-3">
                <button onclick="document.getElementById('failure-modal').remove();" type="button" class="-mr-1 flex p-1 rounded-md hover:bg-red-500 text-red-600 hover:text-white hover:cursor-pointer focus:outline-hidden focus:ring-2 focus:ring-white sm:-mr-2">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
@endif

@if(isset($errors) && $errors->count() > 0)
    <div class="bg-red-200 lg:absolute top-0 left-0 right-0 lg:-mt-11 rounded-md lg:rounded-t-none mb-4" id="error-listing-modal">
        <div class="{{ $message_container }}">
            <div class="w-0 flex-1 flex items-center">
                <p class="ml-2 font-medium text-sm text-red-700 truncate">
                    @foreach ($errors->all() as $error)
                        {{ $error }}<br>
                    @endforeach
                </p>
            </div>
            <div class="order-2 shrink-0 sm:order-3">
                <button onclick="document.getElementById('error-listing-modal').remove();" type="button" class="-mr-1 flex p-1 rounded-md hover:bg-red-500 text-red-600 hover:text-white hover:cursor-pointer focus:outline-hidden focus:ring-2 focus:ring-white sm:-mr-2">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
@endif
