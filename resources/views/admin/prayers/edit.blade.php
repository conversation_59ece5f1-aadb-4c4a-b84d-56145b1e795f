@extends('admin._layouts._app')

@section('title', 'Edit Prayer Request')

@section('content')

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
        'url' => route('admin.prayers.index'),
        'text' => 'Prayer List',
    ], [
        'url' => null,
        'text' => 'Edit Request',
    ]]])

    <div class="admin-standard-col-width">

        <div class="flex flex-row justify-between mb-2">
            <h1>
                Edit Prayer Request
            </h1>

            @if($prayer->is_active && $prayer->is_a_request && !$prayer->is_approved)
                <div class="flex flex-row space-x-4">
                    <form action="{{ route('admin.prayers.approve', $prayer) }}" method="POST">
                        @csrf()
                        <button type="submit" class="admin-button-transparent bg-green-500 text-white border-green-500 hover:bg-green-400">
                            <i class="fa fa-check mr-2" aria-hidden="true"></i> Approve Request
                        </button>
                    </form>
                    <form action="{{ route('admin.prayers.decline', $prayer) }}" method="POST">
                        @csrf()
                        <button type="submit" class="admin-button-red">
                            <i class="fa fa-times mr-2" aria-hidden="true"></i> Decline Request
                        </button>
                    </form>
                </div>
            @endif
        </div>

        <div class="admin-section px-4 py-5 sm:p-6">

            <div class="md:grid md:grid-cols-12 md:space-x-8">
                <div class="md:col-span-7">
                    <h2 class="mb-3">
                        {{ $prayer->title }}
                    </h2>
                    <hr class="my-2"/>
                    <h3 class="mt-3 mb-1">
                        Add an Update
                    </h3>
                    <form action="{{ route('admin.prayers.updates.store', $prayer) }}" method="post" id="pageFormUpdate">
                        @csrf()

                        <textarea name="update_details" rows="3" class="border border-gray-500 {{ $errors->has('update_details') ? ' is-invalid' : '' }}">{{ old('update_details') }}</textarea>
                        @if ($errors->has('update_details'))
                            <span class="text-red-600">
                                <strong>{{ $errors->first('update_details') }}</strong>
                            </span>
                        @endif

                        <div class="mt-4">
                            <button type="submit" class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('pageFormUpdate').submit()">
                                <i class="fa fa-check my-auto mr-2" aria-hidden="true"></i> Add Update
                            </button>
                        </div>

                    </form>
                    <h2 class="mb-1 mt-6">
                        Past Updates
                    </h2>
                    <div class="mt-3">
                        @forelse($prayer->updates()->orderBy('created_at', 'DESC')->get() as $update)

                            <form class="mb-6 border border-gray-300 rounded-sm p-4"
                                  action="{{ route('admin.prayers.updates.destroy', [$prayer, $update]) }}" method="post" id="delete-prayer-form">
                                @csrf()
                                @method('delete')
                                <button type="submit" class="admin-button-red-transparent-small float-right">
                                    <i class="fa fa-trash" aria-hidden="true"></i>&nbsp; Delete
                                </button>
                                <span class="font-semibold">
                                    {{ $update->created_at->timezone(Auth::user()->account->timezone)->format('l, F d, Y @ g:ia') }}
                                </span>
                                <div class="bg-gray-100 p-3 rounded-sm mt-3" role="alert">
                                    {{ $update->details }}
                                </div>
                                @if($update->createdBy)
                                    <div class="mt-2 text-sm">
                                        Created by:
                                        <a href="{{ route('admin.users.view', $update->createdBy) }}">{{ $update->createdBy->name }}</a>
                                    </div>
                                @endif
                            </form>

                        @empty
                            <div class="bg-amber-50 border border-amber-300 p-2 rounded-sm text-center" role="alert">
                                No updates yet!
                            </div>
                        @endforelse
                    </div>
                </div>
                <div class="md:col-span-5">


                    @if($prayer->is_active && $prayer->is_a_request && !$prayer->is_approved)
                        <div class="mb-4 p-4 border border-blue-300 bg-blue-50 rounded-sm text-blue-800 text-sm">
                            Approving a request will make it visible to members.
                            <br>
                            Declining a request will archive it.
                            <br>
                            The user will <strong>not</strong> be notified.
                        </div>
                    @endif

                    <h2 class="mb-3">Original Information</h2>
                    <hr class="my-2"/>

                    <div class="mb-2 text-sm bg-gray-100 p-2 rounded-sm">
                        It is <strong>recommended</strong> to <strong>not</strong> provide an update to a prayer request by changing the original details. No one will be notified of the change, and no historical outline will be recorded.
                    </div>

                    @if($prayer->createdBy)
                        <div class="mb-2 text-sm">
                            Created by:<br/>
                            <a href="{{ route('admin.users.view', $prayer->createdBy) }}" class="text-base">{{ $prayer->createdBy->name }}</a>
                        </div>
                    @endif

                    <form action="{{ route('admin.prayers.save', $prayer) }}" method="post" id="pageForm">
                        {{ csrf_field() }}
                        @method('PUT')

                        <div class="form-group">
                            <label for="title" class="text-sm">Title</label>
                            <input type="text" name="title" class="{{ $errors->has('title') ? ' is-invalid' : '' }}" value="{{ old('title') ?: $prayer->title }}" autocomplete="off">
                            @if ($errors->has('title'))
                                <span class="text-red-500">
                                    <strong>{{ $errors->first('title') }}</strong>
                                </span>
                            @endif
                        </div>

                        <div class="form-group mt-3">
                            <label for="details" class="text-sm">Details</label>
                            <textarea name="details" rows="2" class="{{ $errors->has('details') ? ' is-invalid' : '' }}">{{ old('details') ?: $prayer->details }}</textarea>
                            @if ($errors->has('details'))
                                <span class="text-red-500">
                                    <strong>{{ $errors->first('details') }}</strong>
                                </span>
                            @endif
                        </div>

                        <div class="form-group mt-3">
                            <label class="text-sm" for="prayer_folder_id">Folder <span class="text-gray-400"><small>(not required)</small></span></label>
                            <select name="prayer_folder_id" data-placeholder="" id="prayer_folder_id">
                                <option></option>
                                @foreach(\App\Prayers\PrayerFolder::visibleTo(auth()->user())->get() as $folder)
                                    <option value="{{ $folder->id }}" {{ old('prayer_folder_id') == $folder->id ? 'selected="selected"' : ($folder->id == $prayer->prayer_folder_id ? 'selected="selected"' : '') }}>{{ $folder->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form-group mt-3 max-w-lg">
                            <label class="text-sm" for="selected_user_ids[]">User Tags <span class="text-gray-400"><small>(not required)</small></span></label>
                            <div class="controls">
                                <livewire:components.select-multiuser
                                        :selected_user_ids="$prayer->users->pluck('id')->toArray()"
                                        :selected_classes="'mt-2 grid grid-cols-2 md:grid-cols-4 gap-2'"
                                />
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <label for="expires_at" class="text-sm">Expires At <span class="text-gray-400"><small>(not required)</small></span></label>
                            <input type="date" name="expires_at" class="{{ $errors->has('expires_at') ? ' is-invalid' : '' }}" value="{{ old('expires_at') ?: $prayer->expires_at?->format('Y-m-d') }}" autocomplete="off">
                            @if ($errors->has('expires_at'))
                                <span class="text-red-500">
                                    <strong>{{ $errors->first('expires_at') }}</strong>
                                </span>
                            @endif
                        </div>

                        <div class="my-8">
                            <span class="border border-gray-300 rounded-sm p-3">
                                <input type="hidden" name="is_hidden" value="0"/>
                                <input class="my-auto" type="checkbox" name="is_hidden" value="1" id="checkbox_hidden" @isChecked($prayer->is_hidden)>
                                <label class="" for="checkbox_hidden">
                                    Make this prayer request hidden.
                                </label>
                            </span>
                        </div>

                        <div class="flex flex-row justify-between mt-3">
                            <button class="admin-button-blue text-base" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('pageForm').submit()">
                                <i class="fa fa-check mr-2" aria-hidden="true"></i> Save Changes
                            </button>

                            <button class="admin-button-red-transparent" onclick="this.disabled = true; this.innerHTML = 'Deleting...'; document.getElementById('delete-prayer-form').submit()">
                                <i class="fa fa-trash" aria-hidden="true"></i>&nbsp; Delete
                            </button>
                        </div>

                    </form>
                    <form action="{{ route('admin.prayers.destroy', $prayer) }}" method="post" id="delete-prayer-form">
                        @csrf()
                        @method('delete')
                    </form>

                </div>
            </div>

        </div>

@endsection
