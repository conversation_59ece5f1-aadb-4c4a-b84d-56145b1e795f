@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.prayers.folders.save', $folder) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    {{ csrf_field() }}
    @method('PUT')
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-edit mr-2" aria-hidden="true"></i>Edit Folder
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">

                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2">
                        <div>
                            <label for="name" class="block text-base font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Name
                            </label>
                        </div>
                        <div class="sm:col-span-3">
                            <input type="text" name="name" value="{{ $folder->name }}"
                                   placeholder="Folder name..."
                                   class="{{ $inputCSS }}"
                                   autocomplete="off" value=""/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2">
                        <div>
                            <label for="description" class="block text-base font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Description
                            </label>
                        </div>
                        <div class="sm:col-span-3">
                            <textarea type="text" name="description"
                                      class="{{ $inputCSS }}"
                                      autocomplete="off">{{ $folder->description }}</textarea>
                        </div>
                    </div>
                </div>
                <div class="py-3 sm:px-5">
                    <div class="space-y-3 py-3">
                        <fieldset>
                            <legend class="hidden text-sm font-medium text-gray-900">
                                Options
                            </legend>
                            <div class="space-y-3 sm:ml-4">
                                <div class="relative flex items-start">
                                    <div class="absolute flex items-center h-5">
                                        <input type="checkbox"
                                               class="{{ $checkboxCSS }}"
                                               id="is_hidden" name="is_hidden"
                                               value="1" {{ $folder->is_hidden ? 'checked' : '' }}/>
                                    </div>
                                    <div class="pl-7 text-base">
                                        <label for="is_hidden" class="font-medium text-gray-900">
                                            Hidden folder
                                            <p class="text-gray-500 font-normal">
                                                Hidden folders will not show up for members.
                                            </p>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="flex flex-col space-y-6 justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>


                    <div>
                        @if($folder->prayers->count() === 0)
                            <button type="button" class="admin-button-red-transparent" onclick="this.disabled = true; this.innerHTML = 'Deleting...'; document.getElementById('delete-prayer-folder-form').submit()">
                                <i class="fa fa-trash" aria-hidden="true"></i>&nbsp; Delete
                            </button>
                        @else
                            <button type="button" class="admin-button-red-transparent opacity-50" disabled="disabled">
                                <i class="fa fa-trash" aria-hidden="true"></i>&nbsp; Delete
                            </button>
                            <br>
                            <span class="text-sm text-red-500">You cannot delete a folder with prayers in it.</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>

<form action="{{ route('admin.prayers.folders.destroy', $folder) }}" method="post" id="delete-prayer-folder-form">
    @csrf()
    @method('delete')
</form>