@extends('admin._layouts._app')

@section('title', 'Settings - Prayer List')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.prayers.index'),
            'text' => 'Prayer List',
        ], [
            'text' => 'Settings',
        ]]])

        <div class="md:flex md:items-start align-middle md:justify-between mb-4">
            <h1>
                Prayer List Settings
            </h1>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto">
                <div class="overflow-hidden rounded-lg bg-white p-4">

                    <div class="space-y-4">
                        <div class="border border-gray-300 rounded-sm p-4">
                            <livewire:components.account-setting-selector
                                    :setting_key="'account.setting.prayers.allow_requests'"
                                    :only_show_members_option="true"
                            />
                        </div>

                        <div class="border border-gray-300 rounded-sm p-4">
                            <livewire:components.account-setting-selector
                                    :setting_key="'account.setting.prayers.auto_approve_requests'"
                                    :only_show_members_option="true"
                            />
                        </div>
                    </div>

                    <div class="pt-4 text-gray-500">
                        *Changes are saved automatically upon selection.
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection