@extends('admin._layouts._app')

@section('title', 'Attendance')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.attendance.index'),
            'text' => 'Attendance',
        ], [
            'text' => 'Overview',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                Attendance
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.attendance.sidebar.attendance-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-9">

                            <div class="my-4 mx-4 flex justify-between">
                                <div class="text-3xl flex">
                                    <x-heroicon-o-qr-code class="w-8 h-8 my-auto mr-1 text-gray-500"/>
                                    Visitor Card QR Code
                                </div>
                            </div>

                            <div class="flex grid grid-cols-4 p-6">
                                <div class="col-span-1">
                                    <img src="{{ $visitor_qr_code_image }}" class="p-4 border border-gray-500 rounded-sm"/>
                                    <div class="mt-1 text-xs text-center text-gray-400">
                                        Right-click and "copy" to paste this image where needed.
                                    </div>
                                </div>
                                <div class="col-span-3 px-4">
                                    <div class="text-2xl">Visitor QR Code</div>
                                    <div class="my-6">
                                        <a class="font-medium px-4 py-3 border border-gray-300 rounded-sm"
                                           href="{{ route('public.attendance.cards.new', [auth()->user()->account->getHashId(), 'v']) }}">{{ route('public.attendance.cards.new', [auth()->user()->account->getHashId(), 'v']) }}</a>
                                    </div>
                                    <div class="my-3">
                                        This QR code presents a web form for <strong>visitors only</strong> to fill out and submit their information.
                                    </div>
                                    <div class="my-3">
                                        You can use this QR Code in your PowerPoint slides, church bulletins, visitor cards, and anywhere else you would like
                                        visitors to be able to check-in and leave a record of their presence.
                                    </div>
                                </div>
                            </div>
                            <div class="hidden flex grid grid-cols-4 p-6">
                                <div class="col-span-1">
                                    <img src="{{ $qr_code_image }}" class="p-4 border border-gray-500 rounded-sm"/>
                                    <div class="mt-1 text-xs text-center text-gray-400">
                                        Right-click and "copy" to paste this image where needed.
                                    </div>
                                </div>
                                <div class="col-span-3 px-4">
                                    <div class="text-2xl">General QR Code</div>
                                    <div class="my-6">
                                        <a class="font-medium px-4 py-3 border border-gray-300 rounded-sm"
                                           href="{{ route('public.attendance.cards.new', ['account_hash_id' => auth()->user()->account->getHashId()]) }}">{{ route('public.attendance.cards.new', ['account_hash_id' => auth()->user()->account->getHashId()]) }}</a>
                                    </div>
                                    <div class="my-3">
                                        This QR code presents a web form for <strong>members OR visitors</strong> to fill out and submit their information.
                                    </div>
                                    <div class="my-3">
                                        You can use this QR Code in your PowerPoint slides, church bulletins, visitor cards, and anywhere else you would like
                                        visitors to be able to check-in and leave a record of their presence.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
