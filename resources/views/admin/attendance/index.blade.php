@extends('admin._layouts._app')

@section('title', 'Attendance')

@section('content')

    <script src="https://code.highcharts.com/highcharts.js"></script>

    <div class="admin-heading-section">
        <h1>
            Attendance
        </h1>
        <div class="space-x-0 sm:space-x-2 space-y-2 sm:space-y-0">
            <a href="{{ route('admin.attendance.create') }}" class="admin-button-blue">
                <x-heroicon-s-plus class="w-4 h-4 mr-1"/>
                Add User Attendance
            </a>
            <a onclick="openModal('{{ route('admin.attendance.general.create') }}')" class="admin-button-blue">
                <x-heroicon-s-plus class="w-4 h-4 mr-1"/>
                Add General Attendance
            </a>
        </div>
    </div>

    <main class=" mt-4 admin-section">
        <div class="mx-auto max-w-(--breakpoint-2xl)">
            <div class="overflow-hidden rounded-lg bg-white">
                <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                    @include('admin.attendance.sidebar.attendance-sidebar')

                    @php
                        $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                        $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                    @endphp

                    <div class="divide-y divide-gray-200 lg:col-span-9">
                        <div class="py-6 px-4 sm:p-6 lg:pb-8">

                            <div class="mb-4">
                                <h3 class="text-2xl font-medium leading-6 text-gray-900">Stats</h3>
                                <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-4">
                                    <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <dt class="truncate text-sm font-medium text-gray-500">Attendance Cards</dt>
                                        <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                            {{ $attendance_cards_count }}
                                        </dd>
                                    </div>
                                    <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <dt class="truncate text-sm font-medium text-gray-500">Attendance Types</dt>
                                        <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                            {{ auth()->user()->account->attendanceTypes()->count() }}
                                        </dd>
                                    </div>
                                    <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <dt class="truncate text-sm font-medium text-gray-500">Check-in Types</dt>
                                        <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                            {{ auth()->user()->account->attendanceTypes()->IsMemberCheckin()->count() }}
                                        </dd>
                                    </div>
                                </dl>
                            </div>

                            <div class="admin-section mb-4">
                                <livewire:admin.attendance.reports.general-attendance-report/>
                            </div>
                            <div class="admin-section mb-4">
                                <livewire:admin.attendance.reports.user-attendance-report/>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
