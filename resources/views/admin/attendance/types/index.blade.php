@extends('admin._layouts._app')

@section('title', 'Attendance')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.attendance.index'),
            'text' => 'Attendance',
        ], [
            'url' => route('admin.attendance.types.index'),
            'text' => 'Types',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                Attendance
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.attendance.sidebar.attendance-sidebar')

                        <div class="lg:col-span-9">

                            <div class="my-4 mx-4 flex justify-between">
                                <div class="text-3xl">
                                    Attendance Types
                                </div>
                                <a onclick="openModal('{{ route('admin.attendance.types.create') }}')" class="admin-button-blue">
                                    <x-heroicon-s-plus class="w-4 h-4 mr-1"/>
                                    Create Type
                                </a>
                            </div>

                            <div class="flex flex-col">
                                <div class="overflow-x-auto">
                                    <div class="overflow-hidden overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-300 border-collapse">
                                            <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                                            <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                                <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
                                                <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Days</th>
                                                <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Options</th>
                                                <th scope="col" class="w-0"></th>
                                            </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                            @forelse($types as $type)
                                                <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                                    <td class="py-3 px-2 pl-4 whitespace-nowrap">
                                                        {{ $type->name }}
                                                    </td>
                                                    <td class="py-3 px-2 pl-4 whitespace-nowrap text-sm">
                                                        @php
                                                            $found_type = false;
                                                            $days = null;
                                                        @endphp
                                                        @foreach(\App\Attendance\AttendanceType::$days_of_week_short as $day_number => $day_title)
                                                            @if($type->days_of_week && in_array($day_number, $type->days_of_week))
                                                                @php
                                                                    $days .= $found_type ? ', '.$day_title : $day_title;
                                                                    $found_type = true;
                                                                @endphp
                                                            @endif
                                                        @endforeach
                                                        {{ $days }}
                                                    </td>
                                                    <td class="py-3 px-2 whitespace-nowrap">
                                                        @if($type->is_am)
                                                            <span class="bg-green-500 rounded-full text-xs text-white px-2 py-0.5 my-auto"><x-heroicon-m-check-circle class="h-5 w-4 mb-0.5 inline text-white"/> AM</span>
                                                        @else
                                                            <span class="bg-gray-300 rounded-full text-xs text-white px-2 py-0.5 my-auto"><x-heroicon-m-x-mark class="h-5 w-3 mb-0.5 inline text-white"/> AM</span>
                                                        @endif
                                                        @if($type->is_pm)
                                                            <span class="bg-green-500 rounded-full text-xs text-white px-2 py-0.5 my-auto"><x-heroicon-m-check-circle class="h-5 w-4 mb-0.5 inline text-white"/> PM</span>
                                                        @else
                                                            <span class="bg-gray-300 rounded-full text-xs text-white px-2 py-0.5 my-auto"><x-heroicon-m-x-mark class="h-5 w-3 mb-0.5 inline text-white"/> PM</span>
                                                        @endif
                                                        @if($type->enable_member_checkin)
                                                            <span class="bg-green-500 rounded-full text-xs text-white px-2 py-0.5 my-auto"><x-heroicon-m-check-circle class="h-5 w-4 mb-0.5 inline text-white"/> Check-in</span>
                                                        @else
                                                            <span class="bg-gray-300 rounded-full text-xs text-white px-2 py-0.5 my-auto"><x-heroicon-m-x-mark class="h-5 w-3 mb-0.5 inline text-white"/> Check-in</span>
                                                        @endif
                                                    </td>
                                                    <td class="pr-4">
                                                        <button onclick="openModal('{{ route('admin.attendance.types.edit', [$type]) }}')" type="button" class="admin-button-transparent-small my-0 py-0.5">Edit</button>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="100%" class="text-center p-5">
                                                        <span class="">No results found.</span>
                                                    </td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
