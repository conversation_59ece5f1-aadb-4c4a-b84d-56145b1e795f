@extends('admin._layouts._app')

@section('title', 'Attendance')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.attendance.index'),
            'text' => 'Attendance',
        ], [
            'url' => route('admin.attendance.user.index'),
            'text' => 'Users',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                Attendance
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.attendance.sidebar.attendance-sidebar')

                        <div class="lg:col-span-9">

                            <div class="my-4 mx-4 flex justify-between">
                                <div class="text-3xl">
                                    User Attendance
                                </div>
                                <a href="{{ route('admin.attendance.create') }}" class="admin-button-blue">
                                    <x-heroicon-s-plus class="w-4 h-4 mr-1"/>
                                    Create Records
                                </a>
                            </div>

                            <div class="flex flex-col">
                                <div class="overflow-x-auto">
                                    <div class="overflow-hidden overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-300 border-collapse">
                                            <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                                            <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                                <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Date</th>
                                                @foreach($types as $type)
                                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold text-center">{{ $type->getShortName() }}</th>
                                                @endforeach
                                                <th scope="col" class="w-0"></th>
                                            </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                            @forelse ($weeks as $week)
                                                <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                                    <td class="py-3 px-2 pl-4 whitespace-nowrap">
                                                        <a class="flex justify-between" href="{{ route('admin.attendance.show', $week->date_attendance->toDateString()) }}">
                                                            <span>{{ $week->date_attendance->toFormattedDateString() }}</span>
                                                            <span class="ml-2">{{ $week->date_attendance->format('D') }}</span>
                                                        </a>
                                                    </td>
                                                    @foreach($types as $type)
                                                        <td class="py-3 px-2 pl-4 whitespace-nowrap text-center">
                                                            {{ $type->getCountForDate($week->date_attendance) ?: '-' }}
                                                        </td>
                                                    @endforeach
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="100%" class="text-center p-5">
                                                        <span class="">No results found.</span>
                                                    </td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="border-t admin-border-color p-4">
                                        {{ $weeks->onEachSide(0)->links() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
