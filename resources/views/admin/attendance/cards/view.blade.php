@extends('admin._layouts._app')

@section('title', 'View - Visitor Cards')

@section('content')

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
        'url' => route('admin.attendance.index'),
        'text' => 'Attendance',
    ], [
        'url' => route('admin.attendance.cards.index'),
        'text' => 'Visitor Cards',
    ], [
        'text' => 'View Card',
    ]]])

    <div class="admin-standard-col-width">

        <div class="flex flex-row justify-between mb-2">
            <h1>
                Visitor Card
            </h1>
        </div>

        <div class="admin-section">

            <div class="md:grid md:grid-cols-12 divide-x divide-gray-300 ">
                <div class="md:col-span-8 divide-y divide-gray-300">
                    <div class=" space-y-2 p-4">
                        <div>
                            <div class="text-sm font-medium text-gray-500 uppercase">
                                Name
                            </div>
                            <h3 class="mb-3">
                                {{ $card->name }}
                            </h3>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500 uppercase">
                                Email
                            </div>
                            <div>
                                @if($card->email)
                                    <a href="mailto:{{ $card->email }}">{{ $card->email }}</a>
                                @else
                                    <span class="text-gray-400 text-sm">not provided</span>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500 uppercase">
                                Phone
                            </div>
                            <div>
                                @if($card->phone)
                                    {{ $card->phone }}
                                @else
                                    <span class="text-gray-400 text-sm">not provided</span>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500 uppercase">
                                Address
                            </div>
                            <div>
                                @if($card->address1 || $card->city)
                                    {{ $card->getAddressString() }}
                                @else
                                    <span class="text-gray-400 text-sm">not provided</span>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500 uppercase">
                                Guest of
                            </div>
                            <div>
                                @if($card->guest_of)
                                    {{ $card->guest_of }}
                                @else
                                    <span class="text-gray-400 text-sm">not provided</span>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div class="text-sm font-medium text-gray-500 uppercase">
                                Comments
                            </div>
                            <div>
                                @if($card->comments)
                                    {{ $card->comments }}
                                @else
                                    <span class="text-gray-400 text-sm">not provided</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    {{--                    <div class="space-y-2 p-4">--}}
                    {{--                        <div class="flex flex-row justify-between">--}}
                    {{--                            <h3>Comments</h3>--}}
                    {{--                            <div>--}}
                    {{--                                <button class="admin-button-blue-small">Add Comment</button>--}}
                    {{--                            </div>--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}
                    {{--                    <div class="space-y-2 p-4">--}}
                    {{--                        <div>--}}
                    {{--                            @forelse($card->userComments as $comment)--}}
                    {{--                                <div class="bg-gray-100 p-2 rounded-lg">--}}
                    {{--                                    <div class="text-sm font-medium text-gray-500 uppercase">--}}
                    {{--                                        {{ $comment->created_at?->format('F j, Y') }}--}}
                    {{--                                    </div>--}}
                    {{--                                    <div>--}}
                    {{--                                        {{ $comment->content }}--}}
                    {{--                                    </div>--}}
                    {{--                                </div>--}}
                    {{--                            @empty--}}
                    {{--                                No comments--}}
                    {{--                            @endforelse--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}
                </div>
                <div class="md:col-span-4 space-y-6 p-4">
                    <div>
                        <div class="text-sm font-medium text-gray-500 uppercase mb-1">
                            Created At
                        </div>
                        <div>
                            {{ $card->created_at->format('F j, Y') }}
                        </div>
                    </div>
                    @if($card->archived_at)
                        <div>
                            <div class="text-sm font-medium text-gray-500 uppercase mb-1">
                                Archived At
                            </div>
                            <div>
                                {{ $card->archived_at?->format('F j, Y') }}
                            </div>
                        </div>
                    @endif
                    @if($card->read_at)
                        <div>
                            <div class="flex flex-row justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-500 uppercase mb-1">
                                        Read At
                                    </div>
                                    <div>
                                        {{ $card->read_at?->format('F j, Y') }}
                                    </div>
                                </div>
                                <form method="post" action="{{ route('admin.attendance.cards.mark-as-unread', $card) }}" class="">
                                    @csrf
                                    <button class="admin-button-transparent-small">Mark as Unread</button>
                                </form>
                            </div>
                        </div>
                    @endif
                    <div>
                        <div class="text-sm font-medium text-gray-500 uppercase mb-1">
                            Is Spam?
                        </div>
                        <div>
                            @if(!$card->is_spam)
                                <form method="post" action="{{ route('admin.attendance.cards.mark-as-spam', $card) }}" class="">
                                    @csrf
                                    <button class="admin-button-red-transparent-small">Mark as Spam</button>
                                </form>
                            @else
                                <div class="flex flex-row justify-between">
                                    <div>
                                        <span class="bg-red-500 text-white rounded-sm px-2 py-1">
                                            Marked as Spam
                                        </span>
                                        <div class="mt-1 text-gray-400">
                                            {{ $card->is_spam?->format('F j, Y') }}
                                        </div>
                                    </div>
                                    <div class="">
                                        <form method="post" action="{{ route('admin.attendance.cards.unmark-as-spam', $card) }}" class="">
                                            @csrf
                                            <button class="admin-button-transparent-small">Unmark as Spam</button>
                                        </form>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div>
                        <div class="text-sm font-medium text-gray-500 uppercase mb-1">
                            Delete Completely?
                        </div>
                        <div>
                            <form method="post" action="{{ route('admin.attendance.cards.destroy', $card) }}" class="">
                                @csrf
                                @method('DELETE')
                                <button class="admin-button-red-transparent-small">Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>

@endsection
