@extends('admin._layouts._app')

@section('title', 'Attendance')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.attendance.index'),
            'text' => 'Attendance',
        ], [
            'url' => route('admin.attendance.general.index'),
            'text' => 'General',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                Attendance
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.attendance.sidebar.attendance-sidebar')

                        <div x-data="{ openDateRange: false }" class="lg:col-span-9">

                            <div class="my-4 mx-4 flex justify-between">
                                <div class="text-3xl">
                                    General Attendance
                                </div>
                                <div class="space-x-2 space-y-2 sm:space-y-0">
                                    <a onclick="openModal('{{ route('admin.attendance.general.create') }}')" class="admin-button-blue">
                                        <x-heroicon-s-plus class="w-4 h-4 mr-1"/>
                                        Create
                                    </a>
                                    <button @click="openDateRange = !openDateRange" type="button" class="admin-button-blue">
                                        <x-heroicon-s-document-arrow-down class="w-4 mr-1"/>
                                        CSV
                                    </button>
                                </div>
                            </div>

                            <div x-show="openDateRange"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                                 x-transition:enter-end="opacity-100 transform translate-y-0"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 transform translate-y-0"
                                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                                 class="bg-white p-4 rounded-lg shadow-sm mb-4">

                                <form action="{{ route('admin.attendance.general.download') }}" method="GET" class="space-y-4">
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                                            <input type="date" name="start_date" id="start_date"
                                                   value="{{ now()->subMonths(2)->format('Y-m-d') }}"
                                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                        </div>
                                        <div>
                                            <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                                            <input type="date" name="end_date" id="end_date"
                                                   value="{{ now()->format('Y-m-d') }}"
                                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div class="flex justify-end">
                                        <button type="submit" class="admin-button-blue">
                                            Download Report
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <div class="flex flex-col">
                                <div class="overflow-x-auto">
                                    <div class="overflow-hidden overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-300 border-collapse">
                                            <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                                            <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                                <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Date</th>
                                                @foreach($types as $type)
                                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold text-center">{{ $type->getShortName() }}</th>
                                                @endforeach
                                                <th scope="col" class="w-0"></th>
                                            </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                            @forelse ($weeks as $week)
                                                <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                                    <td class="py-3 px-2 pl-4 whitespace-nowrap">
                                                        {{ $week->attendance_at->format('M d, Y') }}
                                                    </td>
                                                    @foreach($types as $type)
                                                        <td class="py-3 px-2 pl-4 whitespace-nowrap text-center">
                                                            @if($number = $week->hydrateForTypeGivenDate($type))
                                                                <form action="{{ route('admin.attendance.general.destroy', $number) }}" method="post">
                                                                    @csrf()
                                                                    @method('delete')
                                                                    {{ optional($number)->count }}
                                                                    <button type="submit" class="btn btn-xs btn-light">
                                                                        <i class="fa fa-trash" aria-hidden="true"></i>
                                                                    </button>
                                                                </form>
                                                            @else
                                                                {{ '-' }}
                                                            @endif
                                                        </td>
                                                    @endforeach
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="100%" class="text-center p-5">
                                                        <span class="">No results found.</span>
                                                    </td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="border-t admin-border-color p-4">
                                        {{ $weeks->onEachSide(0)->links() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
