@extends('admin._layouts._app')

@section('title', 'Attendance - View')

@section('content')

    <style>
        input[type="checkbox"] {
            border: 2px solid #999;
        }

        td {
            padding: 6px;
            border-bottom: 1px solid #ccc;
        }

        th {
            padding: 3px 0;
            text-align: center;
        }
    </style>

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.attendance.index'),
            'text' => 'Attendance',
        ], [
            'url' => route('admin.attendance.user.index'),
            'text' => 'Users',
        ], [
            'url' => null,
            'text' => 'View / Edit Existing Records',
        ]]])

        <div class="flex-1">
            <div class="md:flex-1 md:items-start align-middle md:justify-between mb-4">
                <div class="flex flex-row justify-between">
                    <div class="flex flex-col">
                        <h1>
                            View Attendance
                        </h1>
                        <div class="text-lg">
                            {{ $date->toFormattedDateString() }}
                        </div>
                    </div>
                    <form class="flex flex-row space-x-4 mt-auto" action="{{ route('admin.attendance.show', $date->format('Y-m-d')) }}">
                        <select class="" name="filter_by_type" onchange="this.form.submit()">
                            <option value="">All</option>
                            @foreach(\App\Attendance\AttendanceType::visibleTo(Auth::user())->get() as $type1)
                                <option value="{{ $type1->id }}" {{ request('filter_by_type') == $type1->id ? 'selected' : null }}>{{ $type1->name }}</option>
                            @endforeach
                        </select>
                        <a href="{{ route('admin.attendance.create') }}?date={{ $date->format('Y-m-d') }}" class="admin-button-blue"><i class="fa fa-plus mr-2" aria-hidden="true"></i>Add Records</a>
                    </form>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">

                    <div x-data="{ showNotice: true }" x-show="showNotice" class="flex flex-row justify-between text-sm bg-purple-50 text-purple-700 px-4 py-2 border-b border-purple-300">
                        <div class="">
                            Click on the empty space or checkmark to change attendance records for this date. Changes are saved as you click.
                        </div>
                        <div class="my-auto">
                            <x-heroicon-s-x-mark class="w-5 h-5 hover:cursor-pointer" @click="showNotice = false"/>
                        </div>
                    </div>

                    <div class="flex flex-col m-4">

                        <div class="grid grid-cols-2 space-x-4">
                            <div class="col-span-1">
                                <table class="table-auto border border-gray-300" style="width: 100%;">
                                    <thead class="thead-dark border border-gray-300">
                                    <tr>
                                        <th>Name</th>
                                        @foreach($attendance_types as $attendance_type)
                                            <th class="text-center" rel="tooltip" data-placement="bottom" title="{{ $attendance_type->name }}">{{ $attendance_type->short_name }}</th>
                                        @endforeach
                                    </tr>
                                    </thead>
                                    <tbody class="border border-gray-300">
                                    @foreach ($users as $user)
                                        @php
                                            // Get all user attendance records for this date -- we use this smaller array to search through below.
                                            // Trying to search through our master array would take 5 seconds for a page load vs 1.2 seconds using this method.
                                            $current_user_attendances = \App\Attendance\Attendance::where('user_id', $user->id)->where('date_attendance', $date->format('Y-m-d'))->get();
                                        @endphp

                                        @if(ceil($loop->count / 2) == $loop->index)
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-span-1">
                                <table class="table-auto border border-gray-300" style="width: 100%;">
                                    <thead class="thead-dark">
                                    <tr>
                                        <th>Name</th>
                                        @foreach($attendance_types as $attendance_type)
                                            <th class="text-center" rel="tooltip" data-placement="bottom" title="{{ $attendance_type->name }}">{{ $attendance_type->short_name }}</th>
                                        @endforeach
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @endif
                                    <tr>
                                        <td class="border border-gray-300">
                                            {{ $user->last_name . ', ' . $user->display_first_name }}
                                        </td>
                                        @foreach($attendance_types as $attendance_type)
                                            <td class="text-center toggle-attendance border border-gray-300" data-user_id="{{ $user->id }}" data-attendance_type_id="{{ $attendance_type->id }}" data-is_selected="{{ $current_user_attendances->where('user_attendance_type_id', $attendance_type->id)->isNotEmpty() ? 'true' : 'false' }}">
                                                @if($current_user_attendances->where('user_attendance_type_id', $attendance_type->id)->isNotEmpty())
                                                    <i class="fa fa-check"></i>
                                                @endif
                                            </td>
                                        @endforeach
                                    </tr>
                                    @endforeach
                                    </tbody>
                                </table>

                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </main>
    </div>

@endsection

@push('scripts')
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <script>
        (function () {

            function toggleAttendanceRecord(user_id, attendance_type_id, date_attendance) {

                axios({
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    method: 'post',
                    url: '{{ route('admin.attendance.toggle.ajax') }}',
                    data: {
                        _method: 'PUT',
                        user_id: user_id,
                        user_attendance_type_id: attendance_type_id,
                        date_attendance: '{{ $date->format('Y-m-d') }}',
                    },
                })
                    .then(function (response) {
                        return true;
                    })
                    .catch(function (error) {
                        console.log('Failed.');
                        console.log(error);
                        alert('Unable to save changes!');
                    });

                return true;
            }

            var allElements = document.querySelectorAll(".toggle-attendance");

            allElements.forEach(el => el.addEventListener('click', (event) => {
                var element = event.target;
                // If we clicked the "check mark" icon, we want to get the parent element.
                if (element.tagName == 'I') {
                    element = element.parentElement;
                }

                if (element.dataset.is_selected == "true") {
                    if (toggleAttendanceRecord(element.dataset.user_id, element.dataset.attendance_type_id, '{{ $date->format('Y-m-d') }}')) {
                        element.innerHTML = '';
                        element.dataset.is_selected = "false";
                    }
                } else {
                    if (toggleAttendanceRecord(element.dataset.user_id, element.dataset.attendance_type_id, '{{ $date->format('Y-m-d') }}')) {
                        element.innerHTML = '<i class="fa fa-check"></i>';
                        element.dataset.is_selected = "true";
                    }
                }
            }));
        })();
    </script>

@endpush