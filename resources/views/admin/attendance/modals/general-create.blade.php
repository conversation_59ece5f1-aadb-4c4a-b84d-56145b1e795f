<form method="post" action="{{ route('admin.attendance.general.store') }}">
    @csrf
    <div class="">
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                General Attendance
            </h3>
            <hr>
            <div class="mt-4">
                <div class="mb-4">
                    <label for="date" class="block text-sm font-medium text-gray-700">Name</label>
                    <input type="date" name="date" id="date"
                           value="{{ request('date', $today) }}"
                           class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    @foreach($types as $type)
                        <div class="">
                            <label for="type_{{ $type->id }}" class="block text-sm font-medium text-gray-700">{{ $type->name }}</label>
                            <input type="text" name="counts[{{ $type->id }}]" id="type_{{ $type->id }}"
                                   class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>
                    @endforeach
                </div>

                <hr class="my-4"/>

                <div class="mt-4">
                    <div class="flex justify-start">
                        <button type="submit"
                                wire:click="createContribution"
                                class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <x-heroicon-s-check class="w-4 mr-1"/>
                            Save General Attendance
                        </button>
                        <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>