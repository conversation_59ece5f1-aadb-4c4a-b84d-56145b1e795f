<form method="post" action="{{ route('admin.attendance.types.edit', $type) }}">
    @csrf
    <div class="">
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Edit Attendance Type
            </h3>
            <hr>
            <div class="mt-4">
                <div class="">
                    <label for="year" class="block text-sm font-medium text-gray-700">Name</label>
                    <input type="text" name="name" id="name"
                           value="{{ $type->name }}"
                           class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                           placeholder="Sunday AM Class">
                </div>
                <div class="mt-2">
                    <label for="year" class="block text-sm font-medium text-gray-700">Short Name</label>
                    <input type="text" name="short_name" id="short_name"
                           value="{{ $type->short_name }}"
                           class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                           placeholder="Sun-AM-C">
                    <span class="text-xs text-gray-300">This is used in reports where space is limited.</span>
                </div>

                <div class="mt-2">
                    <fieldset class="grid grid-cols-4 gap-3">
                        @foreach(\App\Attendance\AttendanceType::$days_of_week as $number => $day)
                            <div class="col-span-1 flex items-center">
                                <input id="days_of_week{{ $number }}" name="days_of_week[]" value="{{ $number }}" type="checkbox" @isChecked($type->days_of_week && in_array($number, $type->days_of_week)) class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                <label for="days_of_week{{ $number }}" class="ml-2 text-base text-black">{{ $day }}</label>
                            </div>
                        @endforeach
                    </fieldset>
                </div>

                <hr class="my-4"/>

                <div class="">
                    <fieldset class="space-y-5">
                        <div class="relative flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="is_am" name="is_am" value="1" type="checkbox" @isChecked($type->is_am) class="h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="is_am" class="text-black text-base">Is AM</label>
                                <p id="is_am-description" class="text-gray-500">Attendance happens in the morning. Anytime <strong>before</strong> 2:00pm.</p>
                            </div>
                        </div>
                        <div class="relative flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="is_pm" name="is_pm" value="1" type="checkbox" @isChecked($type->is_pm) class="h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="is_pm" class="text-black text-base">Is PM</label>
                                <p id="is_pm-description" class="text-gray-500">Attendance happens in the evening. Anytime <strong>on or after</strong> 2:00pm.</p>
                            </div>
                        </div>
                        <div class="relative flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="enable_member_checkin" name="enable_member_checkin" value="1" type="checkbox" @isChecked($type->enable_member_checkin) class="h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="enable_member_checkin" class="text-black text-base">Allow Check-in</label>
                                <p id="enable_member_checkin-description" class="text-gray-500">Let members check their families in for this attendance type on the days selected above.</p>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between">
                        <div class="flex justify-start">
                            <button type="submit"
                                    wire:click="createContribution"
                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <x-heroicon-s-check class="w-4 mr-1"/>
                                Save Changes
                            </button>
                            <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto">
                                Cancel
                            </button>
                        </div>
                        <button onclick="document.getElementById('deleteType{{ $type->id }}').submit();" type="button" class="admin-button-red-transparent my-0 py-0.5">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<form method="post" action="{{ route('admin.attendance.types.destroy', [$type]) }}" id="deleteType{{ $type->id }}">
    @csrf
    @method('delete')
</form>