@extends('admin._layouts._app')

@section('title', 'Attendance - Create by Name')

@section('content')

    <style>
        input[type="checkbox"] {
            border: 2px solid #999;
        }

        td {
            padding: 6px;
            border-bottom: 1px solid #ccc;
        }

        th {
            padding: 3px 0;
            text-align: center;
        }
    </style>

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.attendance.index'),
            'text' => 'Attendance',
        ], [
            'url' => route('admin.attendance.user.index'),
            'text' => 'Users',
        ], [
            'url' => null,
            'text' => 'Create by Name',
        ]]])

        <div class="flex-1">
            <div class="md:flex-1 md:items-start align-middle md:justify-between mb-4">
                <div class="flex flex-col sm:flex-row justify-between">
                    <h1>
                        Record Attendance
                    </h1>
                    <a class="admin-button-blue px-4 mr-auto sm:ml-auto sm:mr-0" href="{{ route('admin.attendance.create-list') }}?date={{ request('date', $today) }}">
                        <i class="fa fa-refresh"></i> &nbsp;Switch to List View
                    </a>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">


                        <div class="col-span-9">
                            <div class="hidden" id="collected_attendance"></div>

                            <div x-data="{ showNotice: true }" x-show="showNotice" class="flex flex-row justify-between text-sm bg-purple-50 text-purple-700 px-4 py-2 border-b border-purple-300">
                                <div class="">
                                    Select the <em>attendance types</em> you'd like to record attendance for,
                                    then select a <em>date</em>,
                                    then search for users to record attendance for and hit "Enter" to add them to the list.

                                    Click "Submit Attendance" when you're ready to save your attendance records.
                                </div>
                                <div class="my-auto ml-2">
                                    <x-heroicon-s-x-mark class="w-5 h-5 hover:cursor-pointer" @click="showNotice = false"/>
                                </div>
                            </div>

                            <form action="{{ route('admin.attendance.store') }}"
                                  method="post"
                                  class="p-4"
                                  id="submit_attendance_form">

                                {{ csrf_field() }}
                                <div class="flex flex-col sm:flex-row space-x-0 sm:space-x-12 space-y-4 sm:space-y-0">
                                    <div class="">
                                        <label class="font-semibold">Attendance Types</label>
                                        <br>
                                        @foreach($types as $type)
                                            <div class="">
                                                <input type="checkbox" name="type[{{ $type->id }}]" value="{{ $type->id }}" class="custom-control-input type_checkbox" id="type_{{ $type->id }}" {{ is_array(request('type')) ? in_array($type->id, request('type')) ? 'checked="checked"' : '' : null }}/>
                                                <label class="" for="type_{{ $type->id }}"> {{ $type->name }}</label>
                                            </div>
                                        @endforeach
                                    </div>

                                    <div class="">
                                        <label for="date" class="font-semibold">Attendance Date</label>
                                        <input type="date" name="date" class="w-48" id="date" value="{{ request('date', $today) }}" placeholder="YYYY-MM-DD">
                                    </div>
                                </div>

                                <div class="mt-4 {{ $errors->has('name') ? ' has-error' : '' }}">
                                    <label for="name" class="form-label font-semibold">User Search</label>
                                    <select name="name_select" id="name_select" class="border border-gray-400 rounded-md" multiple="multiple">
                                        @foreach ($users as $user)
                                            <option value="{{ $user['id'] }}">{{ $user['value'] }}</option>
                                        @endforeach
                                    </select>
                                </div>


                                <div class="py-4">
                                    <button type="submit" class="admin-button-blue" id="uploadBtn">
                                        <i class="fa fa-check"></i> &nbsp;Submit Attendance
                                    </button>
                                </div>

                                <div class="mb-32 flex-1">
                                    <table class="table-auto" style="width: 100%;" id="input-table">
                                        <thead class="">
                                        <tr class="bg-gray-700 text-white">
                                            <th class="text-left pl-2">Name</th>
                                            @foreach($types as $attendance_type)
                                                <th class="text-center">{{ $attendance_type->short_name }}</th>
                                            @endforeach
                                        </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200"></tbody>
                                    </table>
                                </div>
                            </form>
                        </div>

                        <div class="col-span-3">
                            <h4 class="p-4">Event Log</h4>
                            <hr>
                            <div class="p-4" id="event_log"></div>
                        </div>

                    </div>
                </div>
            </div>
        </main>
    </div>

@endsection

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.15.2/js/selectize.min.js" integrity="sha512-IOebNkvA/HZjMM7MxL0NYeLYEalloZ8ckak+NDtOViP7oiYzG5vn6WVXyrJDiJPhl4yRdmNAG49iuLmhkUdVsQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.15.2/css/selectize.bootstrap5.min.css" integrity="sha512-Ars0BmSwpsUJnWMw+KoUKGKunT7+T8NGK0ORRKj+HT8naZzLSIQoOSIIM3oyaJljgLxFi0xImI5oZkAWEFARSA==" crossorigin="anonymous" referrerpolicy="no-referrer"/>

    <script type="text/javascript">
        $('#submit_attendance_form').on('keyup keypress', function (e) {
            var keyCode = e.keyCode || e.which;
            if (keyCode === 13) {
                e.preventDefault();
                return false;
            }
        });

        var $select = $('#name_select').selectize({
            delimiter: '~',
            persist: false,
            create: function (input) {
                return {
                    value: input,
                    text: input
                }
            }
        });

        var selectize = $select[0].selectize;

        var attendance_types = {!! json_encode($types) !!};
        var users = {!! json_encode($users, true) !!};
        var selectedUsers = [];

        selectize.on('item_add', function (UserId, item) {

            console.log('Attempt to add user ' + UserId);

            var currentUser;
            users.forEach(function (userElement) {
                if (userElement.id == UserId) {
                    currentUser = userElement;
                }
            });

            // If this user is already selected
            try {
                if (selectedUsers.indexOf(UserId) !== -1) {
                    console.log('User ' + UserId + ' already exists in list.');
                    // Clear our selection
                    selectize.clear(true);
                    // Focus the select box to add the next user
                    selectize.focus();
                    return;
                } else {
                    selectedUsers.push(UserId);
                }
            } catch (e) {
                console.log(e);
            }

            // If user selection is invalid
            try {
                if (!currentUser) {
                    console.log('ERROR: Invalid user typed.');

                    var log_item = '<div><span class="text-sm px-2 py-0.5 bg-red-600 text-white rounded-sm" style="white-space: normal; opacity: 0.7"> \
                 ' + UserId + ' not found. \
            </span></div>';

                    $('#event_log').prepend(log_item);

                    // Clear our selection
                    selectize.clear(true);
                    // Focus the select box to add the next user
                    selectize.focus();

                    return false;
                }
            } catch (e) {
                console.log(e);
            }

            // Insert our row
            try {
                cellIndex = 0;

                var newTableRow = document.getElementById('input-table').insertRow(1);
                newTableRow.className = 'hover:bg-gray-100';
                var c0 = newTableRow.insertCell(cellIndex);

                // Create our user name cell
                users.forEach(function (userElement) {
                    if (userElement.id == UserId) {
                        c0.innerHTML = userElement.value + '<input type="hidden" name="attendance[' + UserId + '][user_id]" value="' + UserId + '" />';
                        c0.id = 'user-row-' + UserId;
                    }
                });

                cellIndex++;

                // Create our attendance type cells
                attendance_types.forEach(function (attendTypeElement) {
                    var c1 = newTableRow.insertCell(cellIndex);
                    c1.className = 'text-center';

                    var isChecked = '';
                    if ($('#type_' + attendTypeElement.id).is(":checked")) {
                        isChecked = 'checked="checked"';
                    }

                    c1.innerHTML = '<div class="">\n' +
                        '  <input type="checkbox" name="attendance[' + UserId + '][types][]" value="' + attendTypeElement.id + '"' + isChecked + ' id="customSwitch' + UserId + attendTypeElement.id + '">\n' +
                        '  <label class=" w-auto" for="customSwitch' + UserId + attendTypeElement.id + '"></label>\n' +
                        '</div>';
                    // c1.innerHTML = '<input type="checkbox" name="attendance[' + UserId + '][types][]" value="' + attendTypeElement.id + '" />';
                    cellIndex++;
                });
            } catch (e) {
                console.log(e);
            }

            var log_type = isNaN(UserId) ? 'text-sm px-2 py-0.5 bg-red-600 text-white rounded-sm' : 'text-sm px-2 py-0.5 bg-blue-600 text-white rounded-sm';
            var display_log_type = log_type;
            // display_log_type = (document.querySelector('#includeFamily').checked && log_type == 'primary') ? 'info' : display_log_type;
            var display_value = isNaN(UserId) ? UserId : $('#user_' + UserId).data('name');

            // Add our item to the Event Log
            // WITH Family
            // var log_item = '<div><span class="badge badge-' + display_log_type + '" style="white-space: normal; opacity: 0.7"> \
            //      ' + display_value + ' ' + (document.querySelector('#includeFamily').checked ? 'FAMILY' : '') + ' added. \
            // </span></div>';

            var log_item = '<div><span class="' + display_log_type + '" style="white-space: normal; opacity: 0.7"> \
                 ' + currentUser.value + ' added. \
            </span></div>';

            // Add our item to the Event Log
            // WITH Family
            // var log_item2 = '<span class="badge badge-large badge-' + display_log_type + ' mb-2 user_attendance_' + value + '"> \
            //      ' + display_value + ' ' + (document.querySelector('#includeFamily').checked ? 'FAMILY' : '') + ' \
            //      &nbsp;<a style="cursor: pointer" onclick="removeUser(' + value + ')"><i class="fa fa-times-octagon" aria-hidden="true"></i></a> \
            // </span> ';

            var log_item2 = '<span class="' + display_log_type + ' mb-2 user_attendance_' + UserId + '"> \
                 ' + display_value + '&nbsp;<a style="cursor: pointer" onclick="removeUser(' + UserId + ')"><i class="fa fa-times-octagon" aria-hidden="true"></i></a> \
            </span> ';

            $('#event_log').prepend(log_item);

            $('.type_checkbox').each(function (index, element) {
                if ($(element).is(":checked")) {
                    $('#registration_list_' + $(this).val()).prepend(log_item2);
                }
            });

            // Remove our option from the dropdown
            this.removeOption(UserId);

            // Clear our selection
            selectize.clear(true);
            // Focus the select box to add the next user
            selectize.focus();


            console.log('Added user ' + currentUser.value);
        });
    </script>
@endpush
