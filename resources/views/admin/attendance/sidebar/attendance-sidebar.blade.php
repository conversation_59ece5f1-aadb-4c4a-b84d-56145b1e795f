@php
    $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
    $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="space-y-09 lg:space-y-1">
        <!-- Current: "bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700", Default: "border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900" -->
        <a href="{{ route('admin.attendance.index') }}" class="{{ request()->routeIs('admin.attendance.index') ? $setting_link_selected : $setting_link }}" aria-current="page">
            <x-heroicon-o-user-group class="{{ request()->routeIs('admin.groups.view') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Overview</span>
        </a>

        <a href="{{ route('admin.attendance.user.index') }}" class="{{ request()->routeIs('admin.attendance.user.index') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-user class="{{ request()->routeIs('admin.attendance.user.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">User Attendance</span>
        </a>

        <a href="{{ route('admin.attendance.general.index') }}" class="{{ request()->routeIs('admin.attendance.general.index') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-calculator class="{{ request()->routeIs('admin.attendance.general.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">General Attendance</span>
        </a>

        <a href="{{ route('admin.attendance.cards.index') }}" class="{{ request()->routeIs('admin.attendance.cards.index') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-clipboard-document-list class="{{ request()->routeIs('admin.attendance.cards.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Visitor Cards</span>
        </a>

        <a href="{{ route('admin.attendance.cards.visitors.qrcode') }}" class="{{ request()->routeIs('admin.attendance.cards.visitors.qrcode') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-qr-code class="{{ request()->routeIs('admin.attendance.cards.visitors.qrcode') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Visitor Card - QR Code</span>
        </a>

        <a href="{{ route('admin.attendance.types.index') }}" class="{{ request()->routeIs('admin.attendance.types.index') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-cog class="{{ request()->routeIs('admin.attendance.types.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Attendance Types</span>
        </a>

        {{--        <a href="" class="flex-row justify-between {{ request()->routeIs('admin.attendance.reports.index') ? $setting_link_selected : $setting_link }}">--}}
        {{--            <div class="flex">--}}
        {{--                <x-heroicon-o-presentation-chart-bar class="{{ request()->routeIs('admin.attendance.reports.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>--}}
        {{--                <span class="truncate my-auto">Reports</span>--}}
        {{--            </div>--}}
        {{--            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">--}}
        {{--                0--}}
        {{--            </span>--}}
        {{--        </a>--}}

        {{--        <a href="" class="flex-row justify-between {{ request()->routeIs('admin.groups.admins.index') ? $setting_link_selected : $setting_link }}">--}}
        {{--            <div class="flex">--}}
        {{--                <x-heroicon-o-key class="{{ request()->routeIs('admin.groups.admins.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>--}}
        {{--                <span class="truncate my-auto">Access Permissions</span>--}}
        {{--            </div>--}}
        {{--            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">--}}
        {{--                0--}}
        {{--            </span>--}}
        {{--        </a>--}}

    </nav>
</aside>