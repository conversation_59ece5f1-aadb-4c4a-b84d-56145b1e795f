@extends('admin._layouts._app')

@section('title', 'Changelog')

@section('content')

    <div x-data="{ openForm: false }">

        <div class="admin-heading-section">
            <h1>
                Changelog
            </h1>
            @if(auth()->user()->is_super)
                <div>
                    <span x-on:click="openForm = !openForm" class="admin-button-blue">Create</span>
                </div>
            @endif
        </div>

        <main class="mt-4 admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @php
                            $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                            $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                            $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-3 flex items-center text-sm font-medium';
                            $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-3 flex items-center text-sm font-medium';
                        @endphp

                        <aside class="lg:col-span-3 lg:pb-16">
                            <!-- Mobile Filters -->
                            <div class="lg:hidden space-y-4 p-4">
                                <div x-data="{ open: false }" class="relative">
                                    <button @click="open = !open" type="button" class="w-full bg-white px-4 py-2 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <div class="flex justify-between items-center">
                                            <span>Filter by Type</span>
                                            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                    </button>
                                    <div x-show="open"
                                         @click.away="open = false"
                                         x-transition:enter="transition ease-out duration-100"
                                         x-transition:enter-start="transform opacity-0 scale-95"
                                         x-transition:enter-end="transform opacity-100 scale-100"
                                         x-transition:leave="transition ease-in duration-75"
                                         x-transition:leave-start="transform opacity-100 scale-100"
                                         x-transition:leave-end="transform opacity-0 scale-95"
                                         class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1">
                                        <a href="{{ route('admin.changelog.index') }}"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ !request()->has('category') && !request()->has('platform') ? 'bg-blue-50 text-blue-700' : '' }}">
                                            All
                                        </a>
                                        @foreach(\App\Base\Models\Changelog::$categories as $key => $category)
                                            <a href="{{ route('admin.changelog.index', ['category' => $key]) }}"
                                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ request()->get('category') === $key ? 'bg-blue-50 text-blue-700' : '' }}">
                                                {{ $category }}
                                            </a>
                                        @endforeach
                                    </div>
                                </div>

                                <div x-data="{ open: false }" class="relative">
                                    <button @click="open = !open" type="button" class="w-full bg-white px-4 py-2 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <div class="flex justify-between items-center">
                                            <span>Filter by Platform</span>
                                            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                    </button>
                                    <div x-show="open"
                                         @click.away="open = false"
                                         x-transition:enter="transition ease-out duration-100"
                                         x-transition:enter-start="transform opacity-0 scale-95"
                                         x-transition:enter-end="transform opacity-100 scale-100"
                                         x-transition:leave="transition ease-in duration-75"
                                         x-transition:leave-start="transform opacity-100 scale-100"
                                         x-transition:leave-end="transform opacity-0 scale-95"
                                         class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1">
                                        @foreach(\App\Base\Models\Changelog::$platforms as $key => $platform)
                                            <a href="{{ route('admin.changelog.index', ['platform' => $key]) }}"
                                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ request()->get('platform') === $key ? 'bg-blue-50 text-blue-700' : '' }}">
                                                {{ $platform }}
                                            </a>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Desktop Sidebar -->
                            <nav class="hidden lg:block lg:space-y-0">
                                <a href="{{ route('admin.changelog.index') }}"
                                   class="{{ !request()->has('category') && !request()->has('platform') ? $setting_link_selected : $setting_link }}"
                                   aria-current="page">
                                    <x-heroicon-o-user-group class="{{ !request()->has('category') && !request()->has('platform') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                                    <span class="truncate my-auto">All</span>
                                </a>

                                <div class="uppercase px-3 pt-6 pb-2 flex items-center text-sm font-medium text-gray-400">
                                    Filter Types
                                </div>

                                @foreach(\App\Base\Models\Changelog::$categories as $key => $category)
                                    <a href="{{ route('admin.changelog.index', ['category' => $key]) }}"
                                       class="{{ request()->get('category') === $key ? $setting_link_selected : $setting_link }}">
                                        <x-heroicon-o-information-circle class="{{ request()->get('category') === $key ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                                        <span class="truncate my-auto">{{ $category }}</span>
                                    </a>
                                @endforeach

                                <div class="uppercase px-3 pt-6 pb-2 flex items-center text-sm font-medium text-gray-400">
                                    Filter Platform
                                </div>

                                @foreach(\App\Base\Models\Changelog::$platforms as $key => $platform)
                                    <a href="{{ route('admin.changelog.index', ['platform' => $key]) }}"
                                       class="{{ request()->get('platform') === $key ? $setting_link_selected : $setting_link }}">
                                        <x-heroicon-o-information-circle class="{{ request()->get('platform') === $key ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                                        <span class="truncate my-auto">{{ $platform }}</span>
                                    </a>
                                @endforeach
                            </nav>
                        </aside>

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8">

                                @if(auth()->user()->is_super)
                                    <div x-cloak x-show="openForm" class="mt-8 bg-white rounded-lg border border-gray-300 p-6">
                                        <h3 class="text-lg font-medium text-gray-900 mb-6">Create New Changelog Entry</h3>

                                        <form action="{{ route('admin.changelog.submit') }}" method="POST" class="space-y-6">
                                            @csrf
                                            @method('PUT')

                                            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                                <div>
                                                    <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                                                    <input type="text" name="title" id="title" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>

                                                <div>
                                                    <label for="subtitle" class="block text-sm font-medium text-gray-700">Subtitle (Optional)</label>
                                                    <input type="text" name="subtitle" id="subtitle" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>

                                                <div>
                                                    <label for="released_at" class="block text-sm font-medium text-gray-700">Release Date</label>
                                                    <input type="date" name="released_at" id="released_at" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>
                                                <div>
                                                    <label for="posted_at" class="block text-sm font-medium text-gray-700">Posted Date</label>
                                                    <input type="date" name="posted_at" id="posted_at" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                </div>

                                                <div class="sm:col-span-2">
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Categories</label>
                                                    <div class="flex flex-wrap gap-4">
                                                        @foreach(\App\Base\Models\Changelog::$categories as $key => $category)
                                                            <label class="inline-flex items-center">
                                                                <input type="checkbox" name="categories[]" value="{{ $key }}" class="rounded-sm border-gray-300 text-blue-600 shadow-xs focus:border-blue-500 focus:ring-blue-500">
                                                                <span class="ml-2 text-sm text-gray-600">{{ $category }}</span>
                                                            </label>
                                                        @endforeach
                                                    </div>
                                                </div>

                                                <div class="sm:col-span-2">
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Platforms</label>
                                                    <div class="flex flex-wrap gap-4">
                                                        @foreach(\App\Base\Models\Changelog::$platforms as $key => $platform)
                                                            <label class="inline-flex items-center">
                                                                <input type="checkbox" name="platforms[]" value="{{ $key }}" class="rounded-sm border-gray-300 text-blue-600 shadow-xs focus:border-blue-500 focus:ring-blue-500">
                                                                <span class="ml-2 text-sm text-gray-600">{{ $platform }}</span>
                                                            </label>
                                                        @endforeach
                                                    </div>
                                                </div>

                                                <div class="sm:col-span-2">
                                                    <label for="details" class="block text-sm font-medium text-gray-700">Details (Optional)</label>
                                                    <textarea name="details" id="details" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                                                </div>

                                                <div class="sm:col-span-2">
                                                    <div class="flex items-center space-x-4">
                                                        <label class="inline-flex items-center">
                                                            <input type="checkbox" name="is_public" value="1" checked class="rounded-sm border-gray-300 text-blue-600 shadow-xs focus:border-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2 text-sm text-gray-600">Public</span>
                                                        </label>
                                                        <label class="inline-flex items-center">
                                                            <input type="checkbox" name="is_tiny" value="1" class="rounded-sm border-gray-300 text-blue-600 shadow-xs focus:border-blue-500 focus:ring-blue-500">
                                                            <span class="ml-2 text-sm text-gray-600">Tiny Update</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="flex justify-end">
                                                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                    Create Changelog Entry
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                @endif

                                @forelse($changelogs as $month => $items)
                                    <div class="relative mb-8">
                                        <div class="sticky top-0 bg-white z-40 pb-4">
                                            <h2 class="text-2xl font-bold text-gray-900">{{ $month }}</h2>
                                        </div>

                                        <div class="">
                                            @foreach($items as $item)
                                                <div class="relative pl-6 pr-4 py-2 hover:bg-gray-100">
                                                    <div class="absolute left-2 top-4 w-2 h-2 rounded-full bg-blue-600"></div>

                                                    <div class="flex items-start">
                                                        <div class="grow">
                                                            <div x-data="{ showDetails: false }">
                                                                <div class="flex justify-between">
                                                                    <div class="flex items-center gap-2">
                                                                        <h3 class="text-base text-gray-900">
                                                                            {{ $item->title }}
                                                                        </h3>
                                                                        @if($item->details)
                                                                            <button
                                                                                    @click="showDetails = !showDetails"
                                                                                    class="text-sm text-blue-600 hover:text-blue-800 ml-4"
                                                                            >
                                                                                <span x-text="showDetails ? 'Hide details' : 'View details'"></span>
                                                                                <span class="sr-only">Toggle details</span>
                                                                            </button>
                                                                        @endif
                                                                    </div>


                                                                    <div class="ml-6 shrink-0 text-sm text-gray-500">
                                                                        {{ $item->released_at->format('M j') }}
                                                                    </div>
                                                                </div>

                                                                @if($item->subtitle)
                                                                    <p class="text-sm text-gray-500">
                                                                        {{ $item->subtitle }}
                                                                    </p>
                                                                @endif

                                                                @if($item->details)
                                                                    <div
                                                                            x-show="showDetails"
                                                                            x-transition:enter="transition ease-out duration-200"
                                                                            x-transition:enter-start="opacity-0 transform -translate-y-2"
                                                                            x-transition:enter-end="opacity-100 transform translate-y-0"
                                                                            x-transition:leave="transition ease-in duration-150"
                                                                            x-transition:leave-start="opacity-100 transform translate-y-0"
                                                                            x-transition:leave-end="opacity-0 transform -translate-y-2"
                                                                            class="mt-2 text-sm text-gray-600 bg-gray-50 rounded-lg p-4"
                                                                    >
                                                                        {{ $item->details }}
                                                                    </div>
                                                                @endif
                                                            </div>

                                                            <div class="mt-2 flex flex-wrap justify-between gap-2">
                                                                <div class="inline-flex rounded-md shadow-xs">
                                                                    @foreach($item->categories as $index => $category)
                                                                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium
                                                                            {{ $index === 0 ? 'rounded-l-md' : '' }}
                                                                            {{ $index === count($item->categories) - 1 ? 'rounded-r-md' : '' }}
                                                                            {{ $index !== 0 ? '-ml-px border-l border-white/25' : '' }}
                                                                            {{ match($category) {
                                                                                'fix' => 'bg-orange-100 text-orange-800',
                                                                                'improvement' => 'bg-yellow-100 text-yellow-800',
                                                                                'feature' => 'bg-blue-100 text-blue-800',
                                                                                'ui' => 'bg-indigo-100 text-indigo-800',
                                                                                'ux' => 'bg-pink-100 text-pink-800',
                                                                                'update' => 'bg-green-100 text-green-800',
                                                                                'breaking' => 'bg-red-100 text-red-800',
                                                                                'major' => 'bg-purple-100 text-purple-800',
                                                                                default => 'bg-gray-100 text-gray-800'
                                                                            }
                                                                        }}">
                                                                            {{ \App\Base\Models\Changelog::$categories[$category] }}
                                                                        </span>
                                                                    @endforeach
                                                                </div>

                                                                @if(count($item->platforms) > 0)
                                                                    <div class="inline-flex rounded-md shadow-xs">
                                                                        @foreach($item->platforms as $index => $platform)
                                                                            <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-800
                                                                                {{ $index === 0 ? 'rounded-l-md' : '' }}
                                                                                {{ $index === count($item->platforms) - 1 ? 'rounded-r-md' : '' }}
                                                                                {{ $index !== 0 ? '-ml-px border-l border-gray-200' : '' }}">
                                                                                {{ \App\Base\Models\Changelog::$platforms[$platform] }}
                                                                            </span>
                                                                        @endforeach
                                                                    </div>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @empty
                                    <div class="py-6 px-4 sm:p-6 lg:pb-8">
                                        <p class="text-gray-500">No changelog entries found. Check back soon!</p>
                                    </div>
                                @endforelse

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection