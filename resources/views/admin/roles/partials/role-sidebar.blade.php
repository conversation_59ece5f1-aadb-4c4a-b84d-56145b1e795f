@php
    $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
    $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="space-y-09 lg:space-y-1">
        <a href="{{ route('admin.roles.view', $role) }}" class="{{ request()->routeIs('admin.roles.view') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-cog class="{{ request()->routeIs('admin.groups.settings') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">General Settings</span>
        </a>

        <a href="{{ route('admin.roles.permissions', $role) }}" class="{{ request()->routeIs('admin.roles.permissions') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-lock-closed class="{{ request()->routeIs('admin.groups.posts') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Permissions</span>
        </a>

        <a href="{{ route('admin.roles.users', $role) }}" class="flex-row justify-between {{ request()->routeIs('admin.roles.users') ? $setting_link_selected : $setting_link }}">
            <div class="flex">
                <x-heroicon-o-users class="{{ request()->routeIs('admin.roles.users') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Members</span>
            </div>
            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">
                {{ $role->users()->count() }}
            </span>
        </a>
    </nav>
</aside>