@extends('admin._layouts._app')

@section('title', 'Role Permissions')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.roles.index'),
            'text' => 'Roles',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.roles.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $role->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.roles.partials.role-sidebar')

                        <form class="lg:col-span-9" action="{{ route('admin.roles.permissions.save', $role) }}" method="POST">
                            @csrf
                            <div class="divide-y divide-gray-200">
                                <div class="flex flex-row justify-between py-6 px-4 sm:p-6">
                                    <h3 class="flex flex-row text-2xl font-medium leading-6 text-gray-900 my-auto">
                                        <x-heroicon-o-cog class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                                        <div class="my-auto">Permissions</div>
                                    </h3>
                                    <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 font-medium text-white shadow-xs hover:bg-blue-800 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Save Changes</button>
                                </div>

                                <div>
                                    <div class="py-3 px-4">
                                        <h2 class="text-2xl">Member App Only</h2>
                                        <p class="text-sm text-gray-500">Member app permissions relate to features specific to the member Mobile and Web applications only.</p>
                                    </div>
                                </div>

                                <div>
                                    <div class="px-4 py-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                                        @foreach($app_permissions->unique('group_name') as $u_permission)
                                            <div class="row-start-1 col-span-1 border border-gray-300 rounded-lg">
                                                <div class="p-2 bg-gray-100 rounded-t-lg">
                                                    <h4 class="font-semibold text-lg">{{ $u_permission->group_name }}</h4>
                                                </div>
                                                <div class="px-2 py-1">
                                                    @foreach($app_permissions->where('group_name', '=', $u_permission->group_name) as $permission)
                                                        <div class="flex flex-row">
                                                            <input type="checkbox" class="mr-1 mt-1" id="permission-{{ $permission->id }}" name="permissions[]"
                                                                   value="{{ $permission->id }}" {{ $role->hasPermission($permission->id) ? 'checked="checked"' : (is_array(old('permissions')) ? in_array($permission->id, old('permissions')) ? 'checked="checked"' : '' : null) }}>
                                                            <div class="">
                                                                <label class="" for="permission-{{ $permission->id }}">{{ $permission->name }}</label>
                                                                @if($permission->isApp())
                                                                    <br/><span class="bg-blue-100 text-blue-800 text-sm rounded-sm px-1 py-0.5">App/Mobile</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>

                                <div>
                                    <div class="py-3 px-4">
                                        <h2 class="text-2xl">Admin Only</h2>
                                        <p class="text-sm text-gray-500">Admin app permissions relate to features specific to the Admin site only.</p>
                                        <p class="text-sm text-red-500">Selecting any of these permissions below gives users access to the Admin site.</p>
                                        <p class="text-sm text-red-500">These are admin specific permissions.</p>
                                    </div>
                                </div>

                                <div>
                                    <div class="px-4 py-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                                        @foreach($admin_permissions->unique('group_name') as $u_permission)
                                            <div class="col-span-1 border border-gray-300 rounded-lg">
                                                <div class="p-2 bg-gray-100 rounded-t-lg">
                                                    <h4 class="font-semibold text-lg">{{ $u_permission->group_name }}</h4>
                                                </div>
                                                <div class="px-2 py-1">
                                                    @foreach($admin_permissions->where('group_name', '=', $u_permission->group_name) as $permission)
                                                        <div class="flex flex-row">
                                                            <input type="checkbox" class="mr-1 mt-1" id="permission-{{ $permission->id }}" name="permissions[]"
                                                                   value="{{ $permission->id }}" {{ $role->hasPermission($permission->id) ? 'checked="checked"' : (is_array(old('permissions')) ? in_array($permission->id, old('permissions')) ? 'checked="checked"' : '' : null) }}>
                                                            <div class="">
                                                                <label class="" for="permission-{{ $permission->id }}">{{ $permission->name }}</label>
                                                                @if($permission->isApp())
                                                                    <br/><span class="bg-blue-100 text-blue-800 text-sm rounded-sm px-1 py-0.5">App/Mobile</span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="flex justify-between py-4 px-4 sm:px-6">
                                    <div>
                                        <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 font-medium text-white shadow-xs hover:bg-blue-800 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Save Changes</button>
                                        <button type="button" class="ml-5 inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Cancel</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
