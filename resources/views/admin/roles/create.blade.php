<form method="post" action="{{ route('admin.roles.store') }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i>Create New Role
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 py-4 sm:px-6">
                <div class="flex flex-col lg:flex-row">
                    <div class="grow space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Role Name</label>
                            <div class="mt-1 flex rounded-md shadow-xs">
                                <input type="text" name="name" id="name" autocomplete="name" placeholder="Soundbooth..." class="block w-full min-w-0 grow rounded-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            </div>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <div class="mt-1">
                                <textarea id="description" name="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="divide-y divide-gray-200">
                    <div class="px-4 sm:px-6">
                        <fieldset class="space-y-5">
                            <legend class="sr-only"></legend>
                            <div class="relative flex items-start">
                                <div class="flex h-5 items-center">
                                    <input id="indicates_membership" aria-describedby="indicates_membership-description" name="indicates_membership" value="1" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                </div>
                                <div class="ml-3">
                                    <label for="indicates_membership" class="font-medium text-gray-700">Indicates congregation membership?</label>
                                    <p id="comments-description" class="text-gray-500 text-sm">
                                        Members must belong to at least one role that indicates membership to be able to login to Lightpost for your congregation.
                                    </p>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>

                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs font-medium rounded-md text-white btn-color-and-hover">
                            Create Role
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>
