@extends('admin._layouts._app')

@section('title', 'Edit Role')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.roles.index'),
            'text' => 'Roles',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.roles.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $role->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.roles.partials.role-sidebar')

                        <div class="lg:col-span-9">

                            <form class="lg:col-span-9" action="{{ route('admin.roles.save', $role) }}" method="POST">
                                @csrf
                                <div class="divide-y divide-gray-200">
                                    <div class="py-6 px-4 sm:p-6">
                                        <h3 class="flex flex-row text-2xl font-medium leading-6 text-gray-900 my-auto">
                                            <x-heroicon-o-cog class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                                            <div class="my-auto">Edit Role</div>
                                        </h3>
                                    </div>
                                    <div class="py-6 px-4 sm:p-6">
                                        <div class="flex flex-col lg:flex-row">
                                            <div class="grow space-y-6">
                                                <div>
                                                    <label for="name" class="block text-sm font-medium text-gray-700">Group Name</label>
                                                    <div class="mt-1 flex rounded-md shadow-xs">
                                                        <input type="text" name="name" id="name" autocomplete="name" value="{{ $role->name }}" class="block w-full min-w-0 grow rounded-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    </div>
                                                </div>

                                                <div>
                                                    <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                                    <div class="mt-1">
                                                        <textarea id="description" name="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm">{{ $role->description }}</textarea>
                                                    </div>
                                                    <p class="mt-2 text-sm text-gray-500">Brief description for this role.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="divide-y divide-gray-200">
                                        <div class="px-4 py-6 sm:px-6">
                                            <fieldset class="space-y-5">
                                                <legend class="sr-only"></legend>
                                                <div class="relative flex items-start">
                                                    <div class="flex h-5 items-center">
                                                        <input id="indicates_membership" aria-describedby="indicates_membership-description" name="indicates_membership" value="1" @isChecked($role->indicates_membership) type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                    </div>
                                                    <div class="ml-3">
                                                        <label for="indicates_membership" class="font-medium text-gray-700">Indicates congregation membership?</label>
                                                        <p id="comments-description" class="text-gray-500 text-sm">
                                                            Members must belong to at least one role that indicates membership to be able to login to Lightpost for your congregation.
                                                        </p>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                        <div class="mt-6 flex justify-between py-4 px-4 sm:px-6">
                                            <div>
                                                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 font-medium text-white shadow-xs hover:bg-blue-800 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Save Changes</button>
                                                <button type="button" class="ml-5 inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Cancel</button>
                                            </div>
                                            @if($role->can_delete)
                                                <button type="button" class="admin-button-red-transparent rounded-md" onclick="document.getElementById('delete-role').submit()">Delete</button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </form>

                            @if($role->can_delete)
                                <form id="delete-role" method="post" action="{{ route('admin.roles.destroy', $role) }}">
                                    @method('delete')
                                    @csrf
                                </form>
                            @endif

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
