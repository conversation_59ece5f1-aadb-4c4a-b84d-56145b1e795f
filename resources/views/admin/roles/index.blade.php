@extends('admin._layouts._app')

@section('title', 'Roles')

@section('content')

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Roles
                </h1>
            </div>
            <div>
                <button onclick="openSidebar('{{ route('admin.roles.create') }}')" class="admin-button-blue">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Create Role
                </button>
            </div>
        </div>

        <div class="admin-section-border admin-section mt-4">

            <div class="flex flex-col">
                <div class="overflow-x-auto">
                    <div class="overflow-hidden overflow-x-auto">
                        <table class="table-auto min-w-full divide-y divide-gray-300 border-collapse">
                            <thead class="uppercase">
                            <tr class="border-b border-gray-300 text-gray-200">
                                <th scope="col" class="bg-gray-700 rounded-tl-lg py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
                                <th scope="col" class="bg-gray-700 w-12 py-2 px-2 text-xs text-left font-semibold">Members</th>
                                <th scope="col" class="bg-gray-700 w-20 py-2 px-2 text-xs text-left font-semibold">Membership</th>
                                <th scope="col" class="bg-gray-700 rounded-tr-lg w-0">&nbsp;</th>
                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($roles as $role)
                                <tr class="hover:bg-blue-50 {{ $loop->odd ? 'bg-gray-50' : null }} hover:cursor-pointer" onclick="window.location='{{ route('admin.roles.view', $role)  }}'">
                                    <td class="py-3 px-2 pl-4 whitespace-nowrap text-black font-medium">
                                        {{ $role->name }}
                                    </td>
                                    <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-center">
                                        <span class="border border-gray-500 bg-gray-50 px-2 py-0.5 text-sm rounded-md">{{ $role->users()->count() }}</span>
                                    </td>
                                    <td class="py-3 px-2 whitespace-nowrap text-sm">
                                        @if($role->indicates_membership)
                                            <span class="bg-green-200 text-gray-800 rounded-md px-1 py-0.5">Indicates Membership</span>
                                        @else
                                            <span class="bg-gray-100 text-gray-400 rounded-md px-1 py-0.5">Indicates Membership</span>
                                        @endif
                                    </td>
                                    <td class="px-2 w-0">
                                        <x-heroicon-o-chevron-right class="w-4 h-4 text-gray-600"/>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="100%" class="text-center p-5">
                                        <span class="">No results found.</span>
                                    </td>
                                </tr>
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="border-t admin-border-color p-4">

                    </div>
                </div>
            </div>

        </div>

    </div>

@endsection

