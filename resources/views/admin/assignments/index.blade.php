@extends('admin._layouts._app')

@section('title', 'Assignment Groups')

@section('content')

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Assignment Groups
                </h1>
            </div>
            <div>
                <a onclick="openModal('{{ route('admin.worship-assignments.settings') }}')" class="admin-button-transparent mr-2">
                    <x-heroicon-o-cog-8-tooth class="w-6 h-6 text-gray-500 mr-1"/>
                    Settings
                </a>
                <a onclick="openSidebar('{{ route('admin.worship-assignments.groups.create') }}')" class="admin-button-blue">
                    <x-heroicon-m-plus class="w-6 h-6 text-white mr-1"/>
                    Assignment Group
                </a>
            </div>
        </div>

        <div class="admin-section mt-4">

            <ul role="list" class="divide-y divide-gray-200">
                @forelse($groups as $group)
                    <li>
                        <a href="{{ route('admin.worship-assignments.groups.view', $group) }}" class="block hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg text-gray-700">
                            <div class="flex items-center px-4 py-4 sm:px-6">
                                <div class="flex min-w-0 flex-1 items-center">
                                    <div class="min-w-0 flex-1 md:grid md:grid-cols-3 md:gap-4">
                                        <div>
                                            <p class="truncate text-xl font-medium text-blue-600">{{ $group->name }}</p>
                                            <p class="mt-2 flex items-center text-base text-gray-700">
                                                <span class="bg-gray-200 px-2 py-0.5 rounded-md">
                                                    {{ $group->getActivePositionCount() }} Positions
                                                </span>
                                            </p>
                                        </div>
                                        <div class="hidden md:block my-auto">
                                            @if($group->getCurrentPeriod())
                                                <div>
                                                    <span class="px-2 py-0.5 bg-green-500 text-sm text-white rounded-sm">Active</span>
                                                </div>
                                                <div class="font-semibold">{{ $group->getCurrentPeriod()->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $group->getCurrentPeriod()->filledPicks()->count() }} / {{ $group->getCurrentPeriod()->picks()->count() }} Spots Confirmed</div>
                                            @else
                                                <div class="text-gray-400 text-sm">No active period.</div>
                                            @endif
                                        </div>
                                        <div class="hidden md:block my-auto">
                                            @if($group->getNextPeriod())
                                                <div>
                                                    <span class="px-2 py-0.5 bg-gray-500 text-sm text-white rounded-sm">Upcoming</span>
                                                </div>
                                                <div class="font-semibold">{{ $group->getNextPeriod()->name }}</div>
                                                <div class="text-gray-500 text-sm">{{ $group->getNextPeriod()->filledPicks()->count() }} / {{ $group->getNextPeriod()->picks()->count() }} Spots Confirmed</div>
                                            @else
                                                <div class="text-gray-400 text-sm">No upcoming period.</div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <x-heroicon-s-chevron-right class="h-5 w-5 text-gray-400"/>
                                </div>
                            </div>
                        </a>
                    </li>
                @empty

                @endforelse
            </ul>

        </div>

        <div class="mt-4">
            <div class="col-md-12">
                {{ $groups->links() }}
            </div>
        </div>
    </div>

@endsection