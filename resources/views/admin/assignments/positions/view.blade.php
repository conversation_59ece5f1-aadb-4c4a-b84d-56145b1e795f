@extends('admin._layouts._app')

@section('title', 'Positions')

@section('content')

    <div class="admin-standard-col-width">

        <div class="admin-standard-col-width">

            @include('admin._layouts.components.breadcrumbs', ['levels' => [[
                'url' => route('admin.worship-assignments.index'),
                'text' => 'Assignments',
            ], [
                'url' => route('admin.worship-assignments.groups.view', $group),
                'text' => 'Group (' . $group->name . ')',
            ], [
                'url' => null,
                'text' => 'Positions',
            ]]])

            <div class="pb-2 md:flex md:items-center md:justify-between">
                <div class="flex-1">
                    <div class="flex items-start sm:items-center">
                        <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.worship-assignments.groups.view', $group) }}">
                            <x-heroicon-s-arrow-left class="w-5 p-0"/>
                        </a>
                        <div class="flex items-center">
                            <h1>
                                Positions
                            </h1>
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex md:mt-0">
                    <a href="{{ route('admin.worship-assignments.groups.positions.create', $group) }}" class="admin-button-blue">
                        <x-heroicon-m-plus class="w-6 h-6 text-white mr-1"/>
                        Position
                    </a>
                </div>
            </div>

            <div class="flex flex-col mt-4">
                <div class="overflow-x-auto admin-section rounded-sm">
                    <div class="inline-block min-w-full">
                        <div class="overflow-hidden overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-300 border-collapse">
                                <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                                <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                    <th scope="col" class="w-4">&nbsp;</th>
                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter">Name</th>
                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter text-center">Users</th>
                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter text-center">Blocking</th>
                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter text-center">Whole Period</th>
                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter">Day of Week</th>
                                    <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter">Auto-fill</th>
                                    <th scope="col" class=""></th>
                                </tr>
                                </thead>
                                <tbody id="positions-table" class="bg-white divide-y divide-gray-200">
                                @forelse($group->positions()->isNotTemporary()->orderBy('sort_id', 'ASC')->get() as $position)
                                    <tr data-positionid="{{ $position->id }}" data-sortid="{{ $position->sort_id }}" class="cursor-pointer hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}" onclick="window.location='{{ route('admin.worship-assignments.groups.positions.edit', [$group, $position]) }}'">
                                        <td class="py-2 px-2 cursor-move">
                                            <x-heroicon-s-bars-3 class="w-5 handle"/>
                                        </td>
                                        <td class="py-3 px-2 whitespace-nowrap font-medium text-gray-900 flex flex-row justify-between">
                                            <span>{{ $position->name }}</span>
                                            @if($position->remindersEnabled())
                                                <span class="bg-green-500 rounded-full text-xs text-white px-2 py-0.5 my-auto"><i class="fa fa-check" aria-hidden="true"></i> Reminders</span>
                                            @endif
                                            @if($position->can_lead_only)
                                                <span class="bg-purple-500 rounded-full text-xs text-white px-2 py-0.5 my-auto">Leaders Only</span>
                                            @endif
                                        </td>
                                        <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-center">
                                            {{ $position->number_of_users }}
                                        </td>
                                        <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-center">
                                            {!! $position->is_blocking ? '<span class="text-danger">Yes</span>' : 'No' !!}
                                        </td>
                                        <td class="py-3 px-2 whitespace-nowrap text-gray-500 text-center">
                                            {{ $position->span_whole_period ? 'Yes' : 'No' }}
                                        </td>
                                        <td class="py-3 px-2 whitespace-nowrap text-gray-500">
                                            {{ \App\WorshipAssignments\Position::$days_of_week[$position->day_of_week] }}
                                        </td>
                                        <td class="py-3 px-2 whitespace-nowrap text-gray-500">
                                            @if(!$position->hasAutofillEnabled())
                                                <span class="bg-gray-300 rounded-full text-xs text-white px-2 py-0.5"><i class="fa fa-times" aria-hidden="true"></i> auto-fill</span>
                                            @else
                                                <span class="bg-green-500 rounded-full text-xs text-white px-2 py-0.5"><i class="fa fa-check" aria-hidden="true"></i> auto-fill</span>
                                            @endif
                                        </td>
                                        <td class="py-3 px-2 whitespace-nowrap text-gray-500">
                                            <x-heroicon-s-chevron-right class="w-5"/>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="100%" class="text-center p-5">
                                            <span class="">No positions created yet.</span>
                                        </td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        @endsection

        @push('scripts')
            <script src=""></script>
            <script>
                new Sortable(document.getElementById('positions-table'), {
                    handle: '.handle', // handle's class
                    animation: 150,
                    onEnd: function (event) {
                        // console.log('new id: ' + event.newIndex);
                        // console.log('old id: ' + event.oldIndex);

                        fetch('/worship-assignments/{{ $group->id }}/positions/' + event.item.dataset.positionid + '/update-sort-id', {
                            method: 'POST',
                            mode: 'cors', // no-cors, *cors, same-origin
                            cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
                            credentials: 'same-origin', // include, *same-origin, omit
                            headers: {
                                'Content-Type': 'application/json',
                                // 'Content-Type': 'application/x-www-form-urlencoded',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            body: JSON.stringify({
                                new_sort_id: event.newIndex,
                                old_sort_id: event.oldIndex
                            })
                        })
                            .then(response => {
                            })
                            .then(data => {
                            })
                            .catch(error => {
                                alert(error);
                            });

                        // var itemEl = event.item;  // dragged HTMLElement
                        // event.to;    // target list
                        // event.from;  // previous list
                        // event.oldIndex;  // element's old index within old parent
                        // event.newIndex;  // element's new index within new parent
                        // event.oldDraggableIndex; // element's old index within old parent, only counting draggable elements
                        // event.newDraggableIndex; // element's new index within new parent, only counting draggable elements
                        // event.clone // the clone element
                        // event.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
                    },
                });
            </script>
    @endpush
