@extends('admin._layouts._app')

@section('title', 'Assignment Group - ' . $group->name)

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.worship-assignments.index'),
            'text' => 'Assignments',
        ], [
            'url' => route('admin.worship-assignments.groups.view', $group),
            'text' => 'Group',
        ]]])

        <div class="pb-2 md:flex md:items-center md:justify-between">
            <div class="flex-shrink min-w-0">
                <div class="flex items-start sm:items-center">
                    <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200 flex-shrink-0" href="{{ route('admin.worship-assignments.index') }}">
                        <x-heroicon-s-arrow-left class="w-5 p-0"/>
                    </a>
                    <h1 class="truncate text-2xl font-semibold text-gray-900" title="{{ $group->name }}">
                        {{ $group->name }}
                    </h1>
                </div>
            </div>
            <div class="mt-6 flex-shrink-0 md:mt-0">
                <div class="flex flex-row gap-2">
                    <a onclick="openSidebar('{{ route('admin.worship-assignments.groups.edit', $group) }}')" class="admin-button-transparent mr-2">
                        <x-heroicon-o-pencil-square class="w-6 h-6 text-gray-500 mr-1"/>
                        Edit Group
                    </a>
                    <a href="{{ route('admin.worship-assignments.groups.positions.view', $group) }}" class="admin-button-transparent mr-2">
                        <x-heroicon-o-cog-8-tooth class="w-6 h-6 text-gray-500 mr-1"/>
                        Edit Positions
                        <span class="px-1.5 bg-gray-400 text-white font-medium text-sm rounded-md ml-2">{{ $group->positions()->count() }}</span>
                    </a>
                    <a onclick="openModal('{{ route('admin.worship-assignments.periods.create', $group) }}')" class="admin-button-blue">
                        <x-heroicon-m-plus class="w-6 h-6 text-white mr-1"/>
                        Time Period
                    </a>
                </div>
            </div>
        </div>

        <div class="admin-section mt-4">

            <div class="px-4 py-3 border-gray-300 border-b rounded-t-lg bg-purple-50 text-purple-800 text-sm">
                <strong>New!</strong> Time Periods now have a "Published" status. Users will not see unpublished assignments.
                <br>Once you are ready to publish the time period, you can do so from the "Edit Time Period" page, using the "Publish" button.
            </div>

            <ul role="list" class="divide-y divide-gray-200">
                <li>
                    <div class="px-4 py-3 sm:px-6">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">Current Period</h3>
                    </div>
                </li>
                @if($current_period)
                    <li>
                        <a href="{{ route('admin.worship-assignments.periods.view', [$group, $current_period]) }}" class="block hover:bg-gray-50 last:rounded-b-lg text-gray-700">
                            <div class="flex items-center px-4 py-4 sm:px-6">
                                <div class="flex min-w-0 flex-1 items-center">
                                    <div class="min-w-0 flex-1 md:grid md:grid-cols-3 md:gap-4">
                                        <div>
                                            <p class="truncate text-lg font-medium text-blue-600">{{ $current_period->name }}</p>
                                            <div>
                                                <span class="px-2 py-0.5 bg-green-500 text-sm text-white rounded-sm">Active</span>
                                            </div>
                                        </div>
                                        <div class="hidden md:block">
                                            @if($group->getCurrentPeriod())
                                                <div class="mt-2 items-center text-base text-gray-700 ">
                                                    {{ $current_period->start_at->format('M d, Y') }} - {{ $current_period->end_at->format('M d, Y') }}
                                                </div>
                                                <div class="text-sm text-gray-500">{{ $current_period->filledPicks()->count() }} / {{ $current_period->picks()->count() }} Spots Confirmed</div>
                                            @else
                                                <span class="text-gray-400 text-sm py-auto">No active period.</span>
                                            @endif
                                            @if($current_period->isPublished())
                                                <span class="px-2 py-0.5 bg-green-500 text-sm text-white rounded-sm">Published</span>
                                            @else
                                                <span class="px-2 py-0.5 bg-gray-500 text-sm text-white rounded-sm">Not Published</span>
                                            @endif
                                        </div>
                                        <div class="hidden md:block">
                                            <div class="text-green-600 font-medium">{{ $current_period->filledPicks()->count() }} Confirmed</div>
                                            <div class="text-red-600 font-medium">{{ $current_period->declinedPicks()->count() }} Declined</div>
                                            <div class="text-gray-600 font-medium">{{ $current_period->noResponsePicks()->count() }} No Response</div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <x-heroicon-s-chevron-right class="h-5 w-5 text-gray-400"/>
                                </div>
                            </div>
                        </a>
                    </li>
                @else
                    <li class="py-4 text-center text-gray-500">
                        No current period. <a class="cursor-pointer" onclick="openModal('{{ route('admin.worship-assignments.periods.create', $group) }}')">Create one.</a>
                    </li>
                @endif
            </ul>

        </div>

        <div class="admin-section mt-8">

            <ul role="list" class="divide-y divide-gray-200">
                <li>
                    <div class="px-4 py-3 sm:px-6">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">All Periods</h3>
                    </div>
                </li>
                @forelse($all_periods->when($current_period, function($collection, $value) use ($current_period) { $collection->where('id', '!=', $current_period->id); })->all() as $period)
                    <li>
                        <a href="{{ route('admin.worship-assignments.periods.view', [$group, $period]) }}" class="block hover:bg-gray-50 last:rounded-b-lg text-gray-700">
                            <div class="flex items-center px-4 py-4 sm:px-6">
                                <div class="flex min-w-0 flex-1 items-center">
                                    <div class="min-w-0 flex-1 md:grid md:grid-cols-3 md:gap-4">
                                        <div>
                                            <p class="truncate text-lg font-medium text-blue-600">{{ $period->name }}</p>
                                            @if($period->end_at > now())
                                                <div>
                                                    <span class="px-2 py-0.5 bg-gray-600 text-sm text-white rounded-sm">Upcoming</span>
                                                </div>
                                            @else
                                                <div>
                                                    <span class="px-2 py-0.5 bg-gray-300 text-sm text-white rounded-sm">Ended</span>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="hidden md:block">
                                            @if($group->getCurrentPeriod())
                                                <div class="mt-2 items-center text-base text-gray-700">
                                                    {{ $period->start_at->format('M d, Y') }} - {{ $period->end_at->format('M d, Y') }}
                                                </div>
                                                <div class="text-sm text-gray-500">{{ $period->filledPicks()->count() }} / {{ $period->picks()->count() }} Spots Confirmed</div>
                                            @else
                                                <span class="text-gray-400 text-sm my-auto">No active period.</span>
                                            @endif
                                            @if($period->isPublished())
                                                <span class="px-2 py-0.5 bg-green-500 text-sm text-white rounded-sm">Published</span>
                                            @else
                                                <span class="px-2 py-0.5 bg-gray-500 text-sm text-white rounded-sm">Not Published</span>
                                            @endif
                                        </div>
                                        <div class="hidden md:block text-sm">
                                            <div class="text-green-600">{{ $period->filledPicks()->count() }} Confirmed</div>
                                            <div class="text-red-600">{{ $period->declinedPicks()->count() }} Declined</div>
                                            <div class="text-gray-600">{{ $period->noResponsePicks()->count() }} No Response</div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <x-heroicon-s-chevron-right class="h-5 w-5 text-gray-400"/>
                                </div>
                            </div>
                        </a>
                    </li>
                @empty
                    <li class="py-4 text-center text-gray-500">
                        No past periods.
                    </li>
                @endforelse
            </ul>

        </div>

        <div class="mt-4">
            <div class="col-md-12">
                {{ $all_periods->links() }}
            </div>
        </div>

    </div>

@endsection