<style>
    @page {
        margin: 20px;
    }

    table {
        page-break-inside: avoid;
    }
</style>
<div style="font-family: Helvetica, Arial, Verdana, sans-serif; font-size: 88%;">
    <div style="text-align: center; font-size: 2.5em;">
        {{ $period->name }}
    </div>
    <div style="text-align: center; font-size: 1.2em; margin-bottom:18px;">
        {{ $period->start_at->format('F d, Y') }} - {{ $period->end_at->format('F d, Y') }}
    </div>

    @if($period->picks()->where('span_whole_period', true)->exists())
        <?php $cur_date = null; ?>
        <table width="100%">
            @if($period->picks()->where('span_whole_period', true)->exists())
                @php
                    $cell_count = 0
                @endphp
                <table width="100%" style="margin-bottom: 2px;">
                    @for($i = 0; $i < 7; $i++)

                        @if($period->picks()->where('span_whole_period', true)->where('day_of_week', $period->convertDayOfWeekToISO8601($i))->exists())
                            @if($cell_count % 2 == 0)
                                <tr>
                                    <td style="vertical-align: top;">
                                    @else
                                        <td style="vertical-align: top;">
                                            @endif
                                            <table cellpadding="4" cellspacing="0" style="border: 2px solid #777; width: 100%;">
                                                <thead style="background-color: #dddddd;">
                                                <tr nobr="true" style="text-align: left; font-weight: bold;">
                                                    <th colspan="2" style="padding: 8px;">EVERY: {{ \App\WorshipAssignments\Period::$days_of_week_non_standard[$i] }}</th>
                                                </tr>
                                                </thead>
                                                @foreach($period->picks()->where('span_whole_period', true)->where('day_of_week', $period->convertDayOfWeekToISO8601($i))->get() as $pick)
                                                    <tbody>
                                                    <tr nobr="true" style="{{ $loop->even ? 'background-color: #eeeeee;"' : '' }}">
                                                        <td>
                                                            {{ $pick->position->name }}
                                                        </td>
                                                        <td style="text-align: right;">
                                                            {!! $pick->user ? $pick->user->name : '' !!}
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                @endforeach
                                            </table>
                                            @php
                                                $cell_count++
                                            @endphp
                                            @if($cell_count % 2 == 0)
                                        </td></tr>
                                @else
                                </td>
                            @endif
                        @endif

                    @endfor
                </table>
            @endif
        </table>
        <div style="padding: 10px;"></div>
    @endif


    <?php $cur_date = null; ?>
    @forelse($period->picks()->distinct('start_at')->select('start_at')->orderBy('start_at', 'ASC')->where('span_whole_period', false)->get() as $pick_date)
        <table cellpadding="4" cellspacing="0" style="border: 2px solid #777; width: 100%; margin-bottom: 10px;">
            @foreach($period->picks()->where('start_at', $pick_date->start_at)->where('span_whole_period', false)->get() as $pick)
                @if($cur_date != $pick->start_at)
                    <thead style="background-color: #666666; color: #ffffff">
                    <tr>
                        <th colspan="2" style="text-align: left; padding: 8px;">{{ $pick->start_at->format('l, F d, Y') }}</th>
                    </tr>
                    </thead>
                    <?php $cur_date = $pick->start_at; ?>
                @endif
                <tbody>
                <tr style="{{ $loop->even ? 'background-color: #eeeeee;"' : '' }}">
                    <td>{{ $pick->position->name }}</td>
                    <td style="text-align: right;">{!! $pick->user ? $pick->user->name : '' !!}</td>
                </tr>
                </tbody>
            @endforeach
        </table>
    @empty
        <div style="text-align: center;">
            No assignments found.
        </div>
    @endforelse
</div>
