<form method="post" action="{{ route('admin.worship-assignments.periods.sendNotifications', [$group, $period]) }}">
    @csrf
    <div class="">
        <div class="flex flex-row mb-6">
            <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                <x-heroicon-m-paper-airplane class="h-6 w-6 text-blue-600"/>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 my-auto ml-3" id="modal-title">
                Send Assignment Notifications
            </h3>
        </div>
        <fieldset class="space-y-3">
            <legend class="sr-only">Notifications</legend>
            <div class="relative flex items-start">
                <div class="flex h-5 items-center">
                    <input id="send_via_email" aria-describedby="send_via_email-description" name="send_via_email" value="1" type="checkbox" checked class="h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500">
                </div>
                <div class="ml-3 text-base">
                    <label for="send_via_email" class="font-medium text-base text-gray-700">Send via Email</label>
                    <p id="comments-description" class="text-gray-500">This will send an email to everyone with an email in Lightpost.</p>
                </div>
            </div>
            <div class="relative flex items-start">
                <div class="flex h-5 items-center">
                    <input id="send_via_mobile_notification" aria-describedby="send_via_mobile_notification-description" name="send_via_mobile_notification" value="1" type="checkbox" checked class="h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500">
                </div>
                <div class="ml-3 text-base">
                    <label for="send_via_mobile_notification" class="font-medium text-base text-gray-700">Send via Mobile Notification</label>
                    <p id="candidates-description" class="text-gray-500">This will send mobile notifications to everyone who has logged in via the mobile app.</p>
                </div>
            </div>
        </fieldset>

        @if($period->isPublished())
            <div class="mt-4 border border-purple-600 text-purple-800 bg-purple-50 p-4 rounded-md">
                <div class="font-bold">Are you sure?</div>
                <div class="mt-3">This will send <strong>email &amp; mobile</strong> notifications based on your selections above.</div>
                <div class="mt-3 text-sm">For Worship Assignments assigned to children, a notification will be sent to the head of household, if no email is on file for that child.</div>
            </div>
        @endif
    </div>
    @if($period->isPublished())
        <div class="mt-6 sm:mt-8 sm:flex">
            <button type="submit" class="inline-flex justify-center w-full rounded-sm border border-transparent shadow-xs px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto">
                Confirm, Send Notifications
            </button>
            <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto">
                Cancel
            </button>
        </div>
    @else
        <div class="mt-4 bg-red-100 text-gray-600 border border-red-500 rounded-sm px-4 py-2 mb-4 text-base">
            <strong>Oops!</strong> You must publish this period before you can send notifications.
        </div>
        <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto">
            Close
        </button>
    @endif
</form>

