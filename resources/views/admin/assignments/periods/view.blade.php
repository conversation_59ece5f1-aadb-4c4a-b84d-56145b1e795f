@extends('admin._layouts._app')

@section('title', 'Assignments - ' . $period->name)

@section('content')

    @livewireStyles

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.worship-assignments.index'),
            'text' => 'Assignments',
        ], [
            'url' => route('admin.worship-assignments.groups.view', $period->group),
            'text' => 'Group',
        ], [
            'text' => 'Time Period',
        ]]])

        <div class="pb-2 md:flex md:items-center md:justify-between">
            <div class="flex-shrink min-w-0">
                <div class="flex items-start sm:items-center">
                    <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200 flex-shrink-0" href="{{ route('admin.worship-assignments.groups.view', $period->group) }}">
                        <x-heroicon-s-arrow-left class="w-5 p-0"/>
                    </a>
                    <h1 class="truncate" title="{{ $period->name }}">
                        {{ $period->name }}
                    </h1>
                </div>
                <div class="mt-4">
                    @if($period->isPublished())
                        <span class="px-3 py-1 bg-green-500 text-base text-white rounded-sm">Published</span>
                    @else
                        <span class="px-3 py-1 bg-gray-500 text-base text-white rounded-sm">Not Published</span>
                    @endif
                    @if($period->isCurrent())
                        <span class="px-3 py-1 bg-green-500 text-base text-white rounded-sm">Current</span>
                    @endif
                    @if($period->isUpcoming())
                        <span class="px-3 py-1 bg-gray-500 text-base text-white rounded-sm">Upcoming</span>
                    @endif
                    @if($period->isEnded())
                        <span class="px-3 py-1 bg-gray-300 text-base text-white rounded-sm">Ended</span>
                    @endif
                </div>
            </div>
            <div class="mb-auto flex-shrink-0 mt-4 md:mt-0">
                <div class="flex flex-col md:flex-row gap-2">
                    <a onclick="openSidebar('{{ route('admin.worship-assignments.periods.edit', [$group, $period]) }}')" class="admin-button-transparent hover:bg-white">
                        <x-heroicon-s-pencil-square class="w-5 h-5 mr-1"/>
                        Edit
                    </a>
                    <a href="{{ route('admin.worship-assignments.periods.print', [$group, $period]) }}" class="admin-button-transparent hover:bg-white">
                        <x-heroicon-o-printer class="w-5 h-5 text-gray-500 mr-2"/>
                        Print
                    </a>
                    @if($period->isPublished())
                        <form method="post" class="inline" action="{{ route('admin.worship-assignments.periods.unpublish', [$group, $period]) }}">
                            @csrf
                            <button type="submit" class="admin-button-transparent border-red-500 hover:bg-red-500 hover:text-white">
                                <x-heroicon-o-x-mark class="w-5 h-5 mr-2"/>
                                Unpublish
                            </button>
                        </form>
                    @else
                        <form method="post" class="inline" action="{{ route('admin.worship-assignments.periods.publish', [$group, $period]) }}">
                            @csrf
                            <button type="submit" class="admin-button-transparent border-green-500 hover:bg-green-500 hover:text-white">
                                <x-heroicon-o-globe-americas class="w-5 h-5 mr-2"/>
                                Publish
                            </button>
                        </form>
                    @endif
                    <a onclick="openModal('{{ route('admin.worship-assignments.periods.confirmSendNotifications', [$group, $period]) }}')" class="admin-button-blue">
                        <x-heroicon-s-megaphone class="w-5 h-5 text-white mr-1"/>
                        Notifications
                        <span class="ml-1.5 px-1 bg-white rounded-md text-sm text-blue-600">{{ $period->notifiablePicks()->count() }}</span>
                    </a>
                </div>
            </div>
        </div>

        <main class="relative admin-section mt-2">

            <div class="overflow-hidden">
                <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">
                    <aside class="lg:col-span-3">
                        <nav class="">
                            @php
                                $cat_active = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-2 py-2 flex items-center text-base font-medium';
                                $cat_normal = 'cursor-pointer border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-2 py-2 flex items-center text-base font-medium last:rounded-bl-md';
                            @endphp
                            <a href="{{ route('admin.worship-assignments.periods.view', [$group, $period]) }}" class="rounded-tl-md flex justify-between {{ !request()->hasAny(['day', 'date']) ? $cat_active : $cat_normal }}">
                                <span class="truncate">All Assignments</span>
                                <span class="truncate px-1 py-0.5 bg-gray-400 text-white rounded-md text-sm">{{ $period->picks()->count() }}</span>
                            </a>
                            @if($period->picks()->where('span_whole_period', true)->exists())
                                <div class="uppercase text-xs bg-gray-50 text-gray-500 border-b border-t border-gray-300 py-2 px-2 font-semibold">
                                    Days
                                </div>
                                @for($i = 0; $i < 7; $i++)
                                    @if($period->picks()->where('span_whole_period', true)->where('day_of_week', $period->convertDayOfWeekToISO8601($i))->exists())
                                        <a href="{{ route('admin.worship-assignments.periods.view', [$group, $period]) }}?day={{ $i }}"
                                           class=" flex justify-between {{ (request()->has('day') && request()->get('day') == $i) ? $cat_active : $cat_normal }}">
                                            <span class="truncate">Every {{ \App\WorshipAssignments\Period::$days_of_week_non_standard[$i] }}</span>
                                            <span class="truncate px-1 py-0.5 bg-gray-400 text-white rounded-md text-sm">
                                                {{ $period->picks()->where('span_whole_period', true)->forDayOfWeek($period->convertDayOfWeekToISO8601($i))->count() }}
                                            </span>
                                        </a>
                                    @endif
                                @endfor
                            @endif
                            <div class="uppercase text-xs bg-gray-50 text-gray-500 border-b border-t border-gray-300 py-2 px-2 font-semibold">
                                Dates
                            </div>
                            @foreach($period->picks()->distinct('start_at')->select('start_at')->orderBy('start_at', 'ASC')->where('span_whole_period', false)->get() as $pick_date)
                                <a href="{{ route('admin.worship-assignments.periods.view', [$group, $period]) }}?date={{ $pick_date->start_at->format('Y-m-d') }}"
                                   class="flex-row justify-between {{ (request()->has('date') && request()->get('date') == $pick_date->start_at->format('Y-m-d')) ? $cat_active : $cat_normal }}">
                                    <span class="truncate">{{ $pick_date->start_at->format('M d') }}</span>
                                    <span class="truncate text-gray-400 font-normal text-sm">
                                    <span class="truncate px-1 py-0.5 mr-2 bg-gray-400 text-white rounded-md text-sm">{{ $period->picks()->where('span_whole_period', false)->where('start_at', $pick_date->start_at)->count() }}</span>
                                        {{ $pick_date->start_at->format('D') }}
                                    </span>
                                </a>
                            @endforeach
                        </nav>
                    </aside>

                    <div class="divide-y divide-gray-200 lg:col-span-9">
                        @if($picks)
                            <!-- Profile section -->
                            <div class="py-6 lg:pb-8">
                                <div class="px-6">
                                    <div class="flex justify-between items-start">
                                        <h2 class="text-3xl mb-6 font-light text-gray-900">
                                            @if(request()->has('day'))
                                                <strong>Every</strong> {{ \App\WorshipAssignments\Period::$days_of_week_non_standard[request()->get('day')] }}
                                            @elseif(request()->has('date'))
                                                {{ (new DateTime(request()->get('date')))->format('l, F d, Y') }}
                                            @else
                                                All Assignments
                                            @endif
                                        </h2>
                                    </div>
                                </div>

                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50 border-t border-gray-300">
                                    <tr>
                                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                                            Position
                                        </th>
                                        <th scope="col" class="hidden px-3 py-1.5 text-center text-sm font-semibold text-gray-900 sm:table-cell">
                                            Notes
                                        </th>
                                        <th scope="col" class="hidden px-4 py-3.5 text-center text-sm font-semibold text-gray-900 sm:table-cell">Assigned User</th>
                                    </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-white">
                                    @forelse($picks as $pick)
                                        <livewire:admin.assignments.view-time-period-assignment :pick="$pick" :key="$pick->id"/>
                                    @empty
                                        No assignments for this date or day!
                                    @endforelse
                                    </tbody>
                                </table>

                            </div>
                        @else
                            <div class="py-20 text-center text-gray-400">
                                No day / date selected!
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
