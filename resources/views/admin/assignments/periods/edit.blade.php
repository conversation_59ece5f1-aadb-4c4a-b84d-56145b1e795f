<form method="post" action="{{ route('admin.worship-assignments.periods.save', [$period->group, $period]) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    @method('put')
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i>Edit Time Period
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 py-4 sm:px-6">
                <div>
                    <label for="name" class="block font-medium text-gray-900">Time Period Name</label>
                    <div class="mt-1">
                        <input type="text" name="name" id="name" class="block w-full rounded-md border-gray-300 shadow-xs focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                               value="{{ $period->name }}"
                               autocomplete="off" placeholder="i.e. Regular Monthly Worship Assignments">
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                </div>
                <div class="flex py-4 mt-8">
                    <div>
                        <button type="button" onclick="document.getElementById('delete-group-form').submit()" class="admin-button-red-transparent">
                            Delete
                        </button>
                        <div class="text-red-600 text-sm mt-1">
                            <strong>WARNING:</strong> This will delete <strong>{{ $period->picks()->count() }} picks!
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>

<form method="post" id="delete-group-form" action="{{ route('admin.worship-assignments.periods.destroy', [$period->group, $period]) }}">
    @csrf()
    @method('delete')
</form>