<form method="post" action="{{ route('admin.worship-assignments.settings.save') }}">
    @csrf
    <div class="">
        <div class="flex flex-row mb-6">
            <div class="mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                <x-heroicon-m-cog class="h-6 w-6 text-blue-600"/>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 my-auto ml-3" id="modal-title">
                Assignment Settings
            </h3>
        </div>
        <div class="flex-row mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <div class="relative flex items-start mb-4">
                <div class="flex h-5 items-center">
                    <input id="enable_reminders" aria-describedby="enable_reminders-description" name="enable_reminders" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500"
                           @isChecked(auth()->user()->account->getSettingValue('wa.enable_reminders')))>
                </div>
                <div class="ml-3">
                    <label for="enable_reminders" class="font-semibold text-gray-700">Enable Assignment Reminder Notifications?</label>
                    <p id="comments-description" class="text-gray-500 text-sm">
                        If we should send reminders before an assignment date happens.
                        <br>
                        <strong>NOTE:</strong> Mobile notifications must be enabled also for notifications to go out.
                    </p>
                </div>
            </div>
            <div class="relative flex items-start mb-4">
                <div class="flex h-5 items-center">
                    <input id="send_mobile_notifications" aria-describedby="send_mobile_notifications-description" name="send_mobile_notifications" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500"
                           @isChecked(auth()->user()->account->getSettingValue('wa.send_mobile_notifications')))>
                </div>
                <div class="ml-3">
                    <label for="send_mobile_notifications" class="font-semibold text-gray-700">Send Reminders via Mobile Notification?</label>
                    <p id="comments-description" class="text-gray-500 text-sm">Get notified when someones posts a comment on a posting.</p>
                </div>
            </div>
            <div class="relative flex flex-col mb-4">
                <label for="send_days_before" class="block font-semibold text-gray-700">Days Before Assignment to send Reminders:</label>
                <select id="send_days_before" name="send_days_before" class="mt-1 w-48 rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-hidden focus:ring-blue-500">
                    @foreach(range(0, 14) as $key)
                        <option value="{{ $key }}" {{ auth()->user()->account->getSettingValue('wa.send_days_before') == $key ? 'selected="selected"' : null }}>{{ $key }}</option>
                    @endforeach
                </select>
                <p class="text-gray-500 text-sm">
                    How many days before an assignment takes place should a reminder go out?
                </p>
            </div>
            <div class="relative flex flex-col mb-4">
                <label for="hour_to_send" class="block font-semibold text-gray-700">Time of Day:</label>
                <select id="hour_to_send" name="hour_to_send" class="mt-1 w-48 rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-hidden focus:ring-blue-500">
                    @foreach(range(1, 24) as $key)
                        <option value="{{ $key }}" {{ auth()->user()->account->getSettingValue('wa.hour_to_send') == $key ? 'selected="selected"' : null }}>{{ $key < 12 ? $key . ':00 am' : ($key == 12 ? '12' : $key - 12) . ':00 ' . ($key == 24 ? 'am' : 'pm') }}</option>
                    @endforeach
                </select>
                <p class="text-gray-500 text-sm">
                    What hour of the day should reminders go out?
                </p>
            </div>
        </div>
    </div>
    <div class="mt-6 sm:mt-4 sm:pl-4 sm:flex">
        <button type="submit" class="inline-flex justify-center w-full rounded-sm border border-transparent shadow-xs px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto">
            Save Changes
        </button>
        <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto">
            Cancel
        </button>
    </div>
</form>
