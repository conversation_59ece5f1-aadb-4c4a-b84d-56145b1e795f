@extends('admin._layouts._app')

@section('title', 'Finance')

@section('content')

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Finance
                </h1>
            </div>
            <div class="space-x-2">
                <a href="{{ route('admin.finance.expenses.create') }}" class="admin-button-blue">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Add Expenses
                </a>
                <a href="{{ route('admin.finance.contributions.create') }}" class="admin-button-blue">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Add Contributions
                </a>
            </div>
        </div>

        <div class="mt-4">
            <div>
                <h3 class="text-lg font-medium leading-6 text-gray-900">
                    <form action="{{ route('admin.finance.index') }}" method="get" id="change_reporting_date" class=" flex flex-row space-x-2">
                        <select name="month" onchange="document.getElementById('change_reporting_date').submit()">
                            @foreach(range(1, 12) as $month)
                                <option value="{{ $month }}" {{ $report_date->format('n') == $month ? 'selected="selected"' : null }}>
                                    {{ now()->setMonth($month)->format('F') }}
                                </option>
                            @endforeach
                        </select>
                        <select name="year" onchange="document.getElementById('change_reporting_date').submit()">
                            @foreach(range(2015, now()->year + 1) as $year)
                                <option value="{{ $year }}" {{ $report_date->format('Y') == $year ? 'selected="selected"' : null }}>
                                    {{ $year }}
                                </option>
                            @endforeach
                        </select>
                    </form>
                </h3>
                <dl class="relative mt-2 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white border border-gray-300 md:grid-cols-3 md:divide-y-0 md:divide-x">
                    <div class="px-4 py-5 sm:p-6 hover:bg-gray-50 hover:cursor-pointer" onclick="window.location = '{{ route('admin.finance.contributions.index') }}'">
                        <dt class="text-base font-normal text-gray-900">Income</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                {{ \Brick\Money\Money::ofMinor($total_income, 'USD')->getAmount() }}
                                {{--                                <span class="ml-2 text-sm font-medium text-gray-500">from {{ \Brick\Money\Money::ofMinor($last_month_total_income, 'USD')->getAmount() }}</span>--}}
                            </div>

                            @if(\App\Finance\Transaction::percentDifference($total_income, $last_month_total_income) > 0 || \App\Finance\Transaction::percentDifference($total_income, $last_month_total_income) === null)
                                <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800 md:mt-2 lg:mt-0">
                                    <x-heroicon-s-arrow-up class="-ml-1 mr-0.5 h-5 w-5 shrink-0 self-center text-green-500"/>
                                    <span class="sr-only"> Increased by </span>
                                    {{ \App\Finance\Transaction::percentDifference($total_income, $last_month_total_income) }}%
                                </div>
                            @elseif(\App\Finance\Transaction::percentDifference($total_income, $last_month_total_income) == 0)
                                <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-gray-100 text-gray-800 md:mt-2 lg:mt-0">
                                    <x-heroicon-s-arrows-right-left class="-ml-1 mr-0.5 h-5 w-5 shrink-0 self-center text-gray-500"/>
                                    <span class="sr-only"> No Change </span>
                                    0%
                                </div>
                            @else
                                <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-orange-100 text-orange-800 md:mt-2 lg:mt-0">
                                    <x-heroicon-s-arrow-down class="-ml-1 mr-0.5 h-5 w-5 shrink-0 self-center text-orange-500"/>
                                    <span class="sr-only"> Decreased by </span>
                                    {{ \App\Finance\Transaction::percentDifference($total_income, $last_month_total_income) }}%
                                </div>
                            @endif
                        </dd>
                        <div class="mt-1 text-xs font-medium text-gray-400 uppercase">
                            {{ $total_income_count }} transactions
                        </div>
                        <div class="flex flex-row justify-start mt-4 text-xs font-medium text-gray-400 uppercase">
                            <span class="flex flex-row bg-gray-50 rounded-md border border-gray-300 px-2 py-1.5 text-gray-800">
                                View All
                                <x-heroicon-s-arrow-small-right class="ml-1 my-auto w-3 h-3"/>
                            </span>
                        </div>
                    </div>

                    <div class="px-4 py-5 sm:p-6 hover:bg-gray-50 hover:cursor-pointer" onclick="window.location = '{{ route('admin.finance.expenses.index') }}'">
                        <dt class="text-base font-normal text-gray-900">Expenses</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                {{ \Brick\Money\Money::ofMinor($total_expense, 'USD')->getAmount() }}
                                <span class="ml-2 text-sm font-medium text-gray-500">from {{ \Brick\Money\Money::ofMinor($last_month_total_expense, 'USD')->getAmount() }}</span>
                            </div>

                            @if(\App\Finance\Transaction::percentDifference($total_expense, $last_month_total_expense) > 0 || \App\Finance\Transaction::percentDifference($total_expense, $last_month_total_expense) === null)
                                <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800 md:mt-2 lg:mt-0">
                                    <x-heroicon-s-arrow-up class="-ml-1 mr-0.5 h-5 w-5 shrink-0 self-center text-green-500"/>
                                    <span class="sr-only"> Increased by </span>
                                    {{ \App\Finance\Transaction::percentDifference($total_expense, $last_month_total_expense) }}%
                                </div>
                            @elseif(\App\Finance\Transaction::percentDifference($total_expense, $last_month_total_expense) == 0)
                                <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-gray-100 text-gray-800 md:mt-2 lg:mt-0">
                                    <x-heroicon-s-arrows-right-left class="-ml-1 mr-0.5 h-5 w-5 shrink-0 self-center text-gray-500"/>
                                    <span class="sr-only"> No Change </span>
                                    0%
                                </div>
                            @else
                                <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-orange-100 text-orange-800 md:mt-2 lg:mt-0">
                                    <x-heroicon-s-arrow-down class="-ml-1 mr-0.5 h-5 w-5 shrink-0 self-center text-orange-500"/>
                                    <span class="sr-only"> Decreased by </span>
                                    {{ \App\Finance\Transaction::percentDifference($total_expense, $last_month_total_expense) }}%
                                </div>
                            @endif
                        </dd>
                        <div class="mt-1 text-xs font-medium text-gray-400 uppercase">
                            {{ $total_expense_count }} transactions
                        </div>
                        <div class="flex flex-row justify-start mt-4 text-xs font-medium text-gray-400 uppercase">
                            <span class="flex flex-row bg-gray-50 rounded-md border border-gray-300 px-2 py-1.5 text-gray-800">
                                View All
                                <x-heroicon-s-arrow-small-right class="ml-1 my-auto w-3 h-3"/>
                            </span>
                        </div>
                    </div>

                    <div class="px-4 py-5 sm:p-6 hover:bg-gray-50 hover:cursor-pointer" onclick="window.location = '{{ route('admin.finance.buckets.index') }}'">
                        <dt class="text-base font-normal text-gray-900">Finance Categories</dt>
                        <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                            <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                                {{ $buckets->count() }}
                            </div>
                        </dd>
                        <div class="mt-1 text-xs font-medium text-gray-400 uppercase">
                            {{ $expense_budgets->count() + $income_budgets->count() }} Active Budgets
                        </div>
                        <div class="flex flex-row justify-start mt-4 text-xs font-medium text-gray-400 uppercase">
                            <span class="flex flex-row bg-gray-50 rounded-md border border-gray-300 px-2 py-1.5 text-gray-800">
                                View All
                                <x-heroicon-s-arrow-small-right class="ml-1 my-auto w-3 h-3"/>
                            </span>
                        </div>
                    </div>
                </dl>
            </div>

        </div>

        <h3 class="mt-8 text-3xl font-light leading-6 text-gray-900">
            Budgets
            <span class="text-base font-medium text-gray-600">{{ now()->format('Y') }}</span>
        </h3>

        {{--        <div class="p-6 rounded-xl md:grid md:grid-cols-2 md:gap-4 sm:space-y-0">--}}

        {{--            <div class="flex items-center justify-center" x-data="{ circumference: 22 / 7 * 120, percent:75 }">--}}
        {{--                <svg class="transform -rotate-90 w-72 h-72">--}}
        {{--                    <circle cx="145" cy="145" r="60" stroke="currentColor" stroke-width="12" fill="transparent"--}}
        {{--                            class="text-gray-300"/>--}}

        {{--                    <circle cx="145" cy="145" r="60" stroke="currentColor" stroke-width="12" fill="transparent" stroke-linecap="round"--}}
        {{--                            :stroke-dasharray="circumference"--}}
        {{--                            :stroke-dashoffset="circumference - percent / 100 * circumference"--}}
        {{--                            class="text-blue-700 "/>--}}
        {{--                </svg>--}}
        {{--                <span class="absolute text-2xl" x-text="`${percent}%`"></span>--}}
        {{--            </div>--}}
        {{--        </div>--}}

        <div class="py-6">
            <div class="grid grid-cols-1 gap-10 lg:grid-cols-2 xl:grid-cols-3 lg:gap-y-10 gap-x-8">
                @foreach($income_budgets as $bucket)
                    <div class="flex items-center px-2 bg-white hover:bg-gray-50 hover:cursor-pointer shadow-lg rounded-xl h-16 border border-gray-300 relative"
                         onclick="window.location = '{{ route('admin.finance.contributions.index') }}?buckets[0]={{ $bucket->id }}'"
                         x-data="{ circumference: 10.29 / 7 * 120, percent: {{ $bucket->currentBudget?->getPercentUsed() <= 100 ? $bucket->currentBudget?->getPercentUsed() : 100 }} }"
                    >
                        <div class="absolute right-1 -top-3 text-xs bg-green-50 border border-green-300 text-green-700 px-2 py-0.5 rounded-sm uppercase">Income</div>
                        <div class="flex -rotate-90 items-center justify-center bg-white border border-gray-100 rounded-full">
                            <svg class="w-20 h-20 transform translate-x-1 translate-y-1" x-cloak aria-hidden="true">
                                <circle
                                        class="text-gray-300"
                                        stroke-width="10"
                                        stroke="currentColor"
                                        fill="transparent"
                                        r="28"
                                        cx="36"
                                        cy="36"
                                />
                                <circle
                                        class="text-blue-600"
                                        stroke-width="10"
                                        :stroke-dasharray="circumference"
                                        :stroke-dashoffset="circumference - percent / 100 * circumference"
                                        stroke-linecap="round"
                                        stroke="currentColor"
                                        fill="transparent"
                                        r="28"
                                        cx="36"
                                        cy="36"
                                />
                            </svg>
                            <span class="absolute rotate-90 text-base text-blue-700" x-text="`{{ $bucket->currentBudget?->getPercentUsed() }}%`"></span>
                        </div>
                        <div class="flex flex-col ml-2 font-medium text-gray-600 sm:text-lg truncate whitespace-nowrap">
                            <span>{{ $bucket->name }}</span>
                            <span class="text-sm text-gray-500">${{ number_format($bucket->currentBudget?->getAmountUsed()) }} out of ${{ number_format($bucket->currentBudget?->formatAmount()) }}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="py-6">
            <div class="grid grid-cols-1 gap-10 lg:grid-cols-2 xl:grid-cols-3 lg:gap-y-10 gap-x-8">
                @foreach($expense_budgets as $bucket)
                    <div class="flex items-center px-2 bg-white hover:bg-gray-50 hover:cursor-pointer shadow-lg rounded-xl h-16 border border-gray-300 relative"
                         onclick="window.location = '{{ route('admin.finance.expenses.index') }}?buckets[0]={{ $bucket->id }}'"
                         x-data="{ circumference: 10.29 / 7 * 120, percent: {{ $bucket->currentBudget?->getPercentUsed() <= 100 ? $bucket->currentBudget?->getPercentUsed() : 100 }} }"
                    >
                        <div class="absolute right-1 -top-3 text-xs bg-purple-50 border border-purple-300 text-purple-700 px-2 py-0.5 rounded-sm uppercase">Expense</div>
                        <div class="flex -rotate-90 items-center justify-center bg-white border border-gray-100 rounded-full">
                            <svg class="w-20 h-20 transform translate-x-1 translate-y-1" x-cloak aria-hidden="true">
                                <circle
                                        class="text-gray-300"
                                        stroke-width="10"
                                        stroke="currentColor"
                                        fill="transparent"
                                        r="28"
                                        cx="36"
                                        cy="36"
                                />
                                <circle
                                        class="text-blue-600"
                                        stroke-width="10"
                                        :stroke-dasharray="circumference"
                                        :stroke-dashoffset="circumference - percent / 100 * circumference"
                                        stroke-linecap="round"
                                        stroke="currentColor"
                                        fill="transparent"
                                        r="28"
                                        cx="36"
                                        cy="36"
                                />
                            </svg>
                            <span class="absolute rotate-90 text-base text-blue-700" x-text="`{{ $bucket->currentBudget?->getPercentUsed() }}%`"></span>
                        </div>
                        <div class="flex flex-col ml-2 font-medium text-gray-600 sm:text-lg truncate whitespace-nowrap">
                            <span>{{ $bucket->name }}</span>
                            <span class="text-sm text-gray-500">${{ number_format($bucket->currentBudget?->getAmountUsed()) }} out of ${{ number_format($bucket->currentBudget?->formatAmount()) }}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

    </div>

@endsection
