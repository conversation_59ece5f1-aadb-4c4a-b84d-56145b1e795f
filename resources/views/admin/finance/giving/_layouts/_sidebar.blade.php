@php
    $setting_link = 'flex items-center px-3 py-3 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50';
    $setting_link_selected = 'flex items-center px-3 py-3 text-sm font-medium text-blue-700 bg-blue-50 rounded-md hover:text-blue-700 hover:bg-blue-50';
    $setting_icon_classes = 'shrink-0 -ml-1 mr-3 h-6 w-6 text-gray-400';
    $setting_icon_classes_selected = 'shrink-0 -ml-1 mr-3 h-6 w-6 text-blue-500';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="space-y-6">
        <div class="space-y-1">
            <a href="{{ route('admin.finance.giving.index') }}" class="{{ request()->routeIs('admin.finance.giving.index') ? $setting_link_selected : $setting_link }}" aria-current="page">
                <x-heroicon-o-globe-americas class="{{ request()->routeIs('admin.finance.giving.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Online Giving Setup</span>
            </a>
            <a href="{{ route('admin.finance.giving.reports.contribution') }}" class="{{ request()->routeIs('admin.finance.giving.reports.contribution') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-gift class="{{ request()->routeIs('admin.finance.giving.reports.contribution') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Contribution Report</span>
            </a>
            <a href="{{ route('admin.finance.giving.reports.payout') }}" class="{{ request()->routeIs('admin.finance.giving.reports.payout') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-credit-card class="{{ request()->routeIs('admin.finance.giving.reports.payout') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Payout Report</span>
            </a>
            <a href="{{ route('admin.finance.giving.settings') }}" class="{{ request()->routeIs('admin.finance.giving.settings') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-cog class="{{ request()->routeIs('admin.finance.giving.settings') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Settings</span>
            </a>
            <a href="{{ route('admin.finance.contributions.reports.yearly.index') }}" class="{{ request()->routeIs('admin.finance.contributions.reports.yearly.index') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-arrow-up-on-square class="{{ request()->routeIs('admin.finance.contributions.reports.yearly.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">End of Year Contribution Letters</span>
            </a>
            <a href="{{ route('admin.finance.buckets.index') }}" class="{{ request()->routeIs('admin.finance.buckets.index') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-tag class="{{ request()->routeIs('admin.finance.buckets.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Finance Categories</span>
            </a>
        </div>
    </nav>
</aside>