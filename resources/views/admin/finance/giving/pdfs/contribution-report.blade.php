<?php

$i_html = null;

// First create our header
$i_html = '<div style="font-weight: bold; font-size: 160%; width: 100%; text-align: center;">
				Contribution Report<br><span style="font-size: 80%;">' . $from_date . ' - ' . $to_date . '</span></div>
				<div style="width: 100%;">
				<table cellpadding="5" style="width: 100%; border-collapse: collapse; border-top: 1px solid #333; border-left: 1px solid #333; border-spacing: 0px; font-size: 140%">
				<tr>
				<td style="width: 12%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Give Date</td>
                <td style="width: 22%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Name</td>
				<td style="width: 22%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Bucket</td>
				<td style="width: 10%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Amount</td>
				<td style="width: 8%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Fee</td>
				<td style="width: 10%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Deposited</td>
				<td style="width: 12%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Payout</td>
                </tr>';


$j = 0;
foreach ($contributions as $contribution):

    $i_html .= '<tr>
				<td style="text-align: center; border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $contribution->created_at->format('M j, y') . '</td>
                <td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $contribution->user->first_name . ' ' . $contribution->user->last_name . '</td>
				<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $contribution->bucket->name . '</td>
				<td style="text-align: right; border-right: 1px solid #333; border-bottom: 1px solid #333;">' . Brick\Money\Money::ofMinor($contribution->amount, 'USD')->getAmount() . '</td>
				<td style="text-align: right; border-right: 1px solid #333; border-bottom: 1px solid #333;">' . optional(Brick\Money\Money::ofMinor($contribution->amount_fee ?: 0, 'USD'))->getAmount() . '</td>
				<td style="text-align: right; border-right: 1px solid #333; border-bottom: 1px solid #333;">' . optional(Brick\Money\Money::ofMinor($contribution->amount_deposited ?: 0, 'USD'))->getAmount() . '</td>
				<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;">' . ($contribution->payout ? $contribution->payout->deposited_at->format('M j, y') : '--') . '</td>
				</tr>';

    // If we reached the max number of rows per page, do a page break, redo the header and beginning of the table
    if ($j == 29) {
        $i_html .= '</table></div><div style="page-break-after:always;"></div>';
        // Add our header again
        $i_html .= '<div style="font-weight: bold; font-size: 160%; width: 100%; text-align: center;">
				Contribution Report<br><span style="font-size: 80%;">' . $from_date . ' - ' . $to_date . '</span></div>
				<div style="width: 100%;">
				<table cellpadding="5" style="width: 100%; border-collapse: collapse; border-top: 1px solid #333; border-left: 1px solid #333; border-spacing: 0px; font-size: 140%">
				<tr>
				<td style="width: 12%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Give Date</td>
                <td style="width: 22%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Name</td>
				<td style="width: 22%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Bucket</td>
				<td style="width: 10%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Amount</td>
				<td style="width: 8%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Fee</td>
				<td style="width: 10%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Deposited</td>
				<td style="width: 12%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;">Payout</td>
                </tr>';
        $j      = 0;
    }

    $j++;

endforeach;
?>

<html>
<style type=\"text/css\">
    body {
        font-family: 'Helvetica';
        font-size: 9px;
    }

    td {
        font-size: 68%;
    }

    @page {
        margin: 0.2in 0.5in 0.2in 0.5in;
    }
</style>
<body>
<?= $i_html; ?>
</table>
</div></body>
</html>