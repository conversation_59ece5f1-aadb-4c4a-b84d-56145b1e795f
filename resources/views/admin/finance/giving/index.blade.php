@extends('admin._layouts._app')

@section('title', 'Online Giving')

@section('content')

    <div class="admin-heading-section">
        <h1>
            Online Giving
        </h1>
        <a href="https://stripe.com">
            <svg xmlns="http://www.w3.org/2000/svg" width="149" height="41">
                <path fill-rule="evenodd" fill="#32364E" d="M6 0h137c3.314 0 6 2.686 6 6v29c0 3.314-2.686 6-6 6H6c-3.314 0-6-2.686-6-6V6c0-3.314 2.686-6 6-6z"/>
                <path fill-rule="evenodd" fill="#FFF" d="M71.403 26.625h-1.462l1.132-2.796-2.253-5.685h1.545l1.416 3.869 1.427-3.869h1.545l-3.35 8.481zm-5.615-2.442c-.507 0-1.026-.188-1.498-.554v.413h-1.509v-8.481h1.509v2.985c.472-.354.991-.543 1.498-.543 1.581 0 2.666 1.274 2.666 3.09 0 1.816-1.085 3.09-2.666 3.09zM65.47 19.3c-.413 0-.826.177-1.18.531v2.524c.354.354.767.531 1.18.531.849 0 1.439-.731 1.439-1.793 0-1.061-.59-1.793-1.439-1.793zm-8.8 4.329c-.46.366-.979.554-1.498.554-1.569 0-2.665-1.274-2.665-3.09 0-1.816 1.096-3.09 2.665-3.09.519 0 1.038.189 1.498.543v-2.985h1.522v8.481H56.67v-.413zm0-3.798c-.342-.354-.755-.531-1.168-.531-.861 0-1.45.732-1.45 1.793 0 1.062.589 1.793 1.45 1.793.413 0 .826-.177 1.168-.531v-2.524zm-8.988 1.675c.094.896.802 1.51 1.793 1.51.542 0 1.144-.201 1.757-.555v1.262c-.672.307-1.344.46-2.005.46-1.781 0-3.031-1.297-3.031-3.137 0-1.781 1.227-3.043 2.913-3.043 1.545 0 2.595 1.215 2.595 2.949 0 .165 0 .353-.024.554h-3.998zm1.368-2.335c-.731 0-1.297.542-1.368 1.356h2.571c-.047-.802-.531-1.356-1.203-1.356zm-5.343.931v3.94h-1.51v-5.898h1.51v.59c.424-.472.943-.731 1.45-.731.166 0 .331.012.496.059v1.345c-.165-.048-.354-.071-.531-.071-.495 0-1.026.271-1.415.766zm-6.736 1.404c.095.896.802 1.51 1.793 1.51.543 0 1.144-.201 1.758-.555v1.262c-.673.307-1.345.46-2.006.46-1.781 0-3.031-1.297-3.031-3.137 0-1.781 1.227-3.043 2.913-3.043 1.546 0 2.595 1.215 2.595 2.949 0 .165 0 .353-.023.554h-3.999zm1.368-2.335c-.731 0-1.297.542-1.368 1.356h2.572c-.048-.802-.531-1.356-1.204-1.356zm-6.641 4.871l-1.203-4.01-1.191 4.01h-1.357l-2.028-5.898h1.509l1.192 4.011 1.191-4.011h1.368l1.191 4.011 1.192-4.011h1.509l-2.017 5.898h-1.356zm-9.224.141c-1.781 0-3.043-1.285-3.043-3.09 0-1.816 1.262-3.09 3.043-3.09 1.781 0 3.031 1.274 3.031 3.09 0 1.805-1.25 3.09-3.031 3.09zm0-4.918c-.885 0-1.498.743-1.498 1.828s.613 1.828 1.498 1.828c.873 0 1.486-.743 1.486-1.828s-.613-1.828-1.486-1.828zm-6.629 1.864h-1.357v2.913h-1.509v-8.115h2.866c1.651 0 2.83 1.073 2.83 2.607 0 1.533-1.179 2.595-2.83 2.595zm-.213-3.975h-1.144v2.748h1.144c.873 0 1.486-.554 1.486-1.368 0-.826-.613-1.38-1.486-1.38zM136.827 21.868h-7.25c.165 1.736 1.437 2.247 2.88 2.247 1.471 0 2.629-.309 3.639-.819v2.984c-1.007.557-2.335.96-4.106.96-3.607 0-6.135-2.259-6.135-6.726 0-3.772 2.144-6.768 5.668-6.768 3.518 0 5.354 2.995 5.354 6.788 0 .358-.033 1.134-.05 1.334zm-5.328-5.102c-.926 0-1.955.699-1.955 2.368h3.829c0-1.667-.964-2.368-1.874-2.368zM119.981 27.24c-1.296 0-2.088-.547-2.62-.937l-.008 4.191-3.703.788-.002-17.289h3.262l.192.915c.513-.479 1.45-1.162 2.902-1.162 2.601 0 5.051 2.343 5.051 6.655 0 4.707-2.424 6.839-5.074 6.839zm-.862-10.213c-.851 0-1.383.311-1.769.734l.022 5.504c.359.389.878.703 1.747.703 1.369 0 2.287-1.491 2.287-3.485 0-1.938-.932-3.456-2.287-3.456zm-10.702-3.034h3.718v12.982h-3.718V13.993zm0-4.145l3.718-.791v3.017l-3.718.79V9.848zm-3.841 8.326v8.801h-3.702V13.993h3.202l.233 1.095c.866-1.594 2.598-1.271 3.091-1.094v3.404c-.471-.152-1.949-.374-2.824.776zm-7.817 4.246c0 2.183 2.337 1.504 2.812 1.314v3.015c-.494.271-1.389.491-2.6.491-2.198 0-3.847-1.619-3.847-3.812l.016-11.883 3.616-.768.003 3.216h2.813v3.158h-2.813v5.269zm-4.494.632c0 2.666-2.122 4.188-5.202 4.188-1.277 0-2.673-.248-4.05-.841v-3.536c1.243.676 2.827 1.183 4.054 1.183.826 0 1.421-.222 1.421-.906 0-1.768-5.631-1.102-5.631-5.203 0-2.622 2.003-4.191 5.007-4.191 1.227 0 2.454.189 3.681.678v3.488c-1.127-.608-2.557-.953-3.684-.953-.776 0-1.258.224-1.258.803 0 1.666 5.662.874 5.662 5.29z"/>
            </svg>
        </a>
    </div>

    <main class="mt-4 admin-section">
        <div class="mx-auto max-w-(--breakpoint-2xl)">
            <div class="overflow-hidden rounded-lg bg-white">
                <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                    @include('admin.finance.giving._layouts._sidebar')

                    <div class="divide-y divide-gray-200 lg:col-span-9">
                        <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">

                            @if(isset($error))
                                <div class="text-red-600" role="alert">
                                    <strong>Oops!</strong> Stripe returned an error: {{ $error }}
                                    <br><br>Don't lose heart! Please try again or reach out to support for assistance.
                                </div>
                            @endif

                            <div class="col-md-6 pb-4">
                                @if(!auth()->user()->account->stripe_account_id)
                                    <p>Lightpost enables and organizes online giving through our service provider Stripe.com.</p>
                                    <p>Click here to get started and connect your Stripe account with Lightpost.</p>
                                    <a href="https://connect.stripe.com/oauth/authorize?response_type=code&amp;client_id={{ config('services.stripe.connect.client_id') }}&amp;scope=read_write&amp;state=lightpost_token_{{ auth()->user()->account_id }}" class="mt-2 connect-button">
                                        <span>Connect with Stripe</span>
                                    </a>
                                @elseif(isset($stripe_account))
                                    <div class="bg-green-100 border-0 border-green-400 text-green-700 px-4 py-3 rounded-sm relative" role="alert">
                                        <h4 class="text-xl font-semibold text-gray-800 font-weight-bold">Connected</h4>
                                        Your Stripe account <strong>{{ optional($stripe_account->business_profile)->name }}</strong> is connected!
                                    </div>
                                    <div class="mt-4 text-sm text-gray-600">
                                        No further action is required.<br>You can now start accepting online giving through Lightpost.
                                    </div>
                                @endif
                            </div>

                            @if(!auth()->user()->account->stripe_account_id)
                                <div class="col-md-6 pb-4">
                                    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-sm relative" role="alert">
                                        <h4 class="alert-heading">What is Stripe?</h4>
                                        <p>Stripe is a world class payment processor, which can accept credit, debit and ACH payments online, on your behalf, in a secure and responsible way.</p>
                                        <hr>
                                        <p class="mb-0">Lightpost
                                            <strong>never</strong> records, stores or even handles payment information of any kind.
                                            <br>All payment information goes to Stripe, is tokenized, and Lightpost uses tokens to tell Stripe what to do with a payment.
                                        </p>
                                    </div>
                                </div>
                            @endif

                            {{-- We no longer use this because we no longer need the publishable key --}}
                            @if(false && auth()->user()->account->stripe_account_id && !auth()->user()->account->stripe_account_publishable_key)
                                <div class="col-md-6 pb-4">
                                    <div class="bg-amber-50 border border-amber-400 px-4 py-3 rounded-sm relative" role="alert">
                                        <h4 class="text-amber-700 alert-heading">Stripe Key Needed</h4>
                                        <p>To allow Lightpost to use your Stripe account for your members, we need the "Stripe publishable key" found in <a href="https://dashboard.stripe.com/developers">here in your Stripe account</a>.</p>
                                        <p>Copy and paste that below to complete the setup. The key will begin with: <code class="text-sm font-bold">pk_live_</code></p>
                                        <p><a href="https://help.lightpost.app/help/online-giving-setup">Read more information.</a></p>
                                        <p class="mt-2">
                                        <form action="{{ route('admin.finance.giving.giving.publishable-key') }}"
                                              method="post">
                                            @csrf
                                            <input type="text" name="stripe_account_publishable_key" value="{{ auth()->user()->account->stripe_account_publishable_key }}" class="max-w-xl"/>
                                            <button type="submit" class="admin-button-blue mt-1">Save Key</button>
                                        </form>
                                        </p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <style>
        .connect-button {
            display: inline-block;
            margin-bottom: 1px;
            background-image: -webkit-gradient(linear, left top, left bottom, from(#28a0e5), to(#015e94));
            background-image: linear-gradient(#28a0e5, #015e94);
            -webkit-font-smoothing: antialiased;
            border: 0;
            padding: 1px;
            height: 32px;
            text-decoration: none;
            border-radius: 4px;
            -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, .2);
            box-shadow: 0 1px 0 rgba(0, 0, 0, .2);
            cursor: pointer;
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            user-select: none;
            text-decoration: none !important
        }

        .connect-button span {
            display: block;
            position: relative;
            padding: 0 12px;
            height: 30px;
            background: #1275ff;
            background-image: -webkit-gradient(linear, left top, left bottom, from(#7dc5ee), color-stop(85%, #008cdd), to(#30a2e4));
            background-image: linear-gradient(#7dc5ee, #008cdd 85%, #30a2e4);
            font-size: 15px;
            line-height: 30px;
            color: #fff;
            font-weight: 700;
            font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
            text-shadow: 0 -1px 0 rgba(0, 0, 0, .2);
            -webkit-box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, .25);
            box-shadow: inset 0 1px 0 hsla(0, 0%, 100%, .25);
            border-radius: 3px;
            padding-left: 44px
        }

        .connect-button span:before {
            content: "";
            display: block;
            position: absolute;
            left: 11px;
            top: 50%;
            width: 23px;
            height: 24px;
            margin-top: -12px;
            background-repeat: no-repeat;
            background-size: 23px 24px
        }

        .connect-button:active {
            background: #005d93
        }

        .connect-button:active span {
            color: #eee;
            background: #008cdd;
            background-image: -webkit-gradient(linear, left top, left bottom, from(#008cdd), color-stop(85%, #008cdd), to(#239adf));
            background-image: linear-gradient(#008cdd, #008cdd 85%, #239adf);
            -webkit-box-shadow: inset 0 1px 0 rgba(0, 0, 0, .1);
            box-shadow: inset 0 1px 0 rgba(0, 0, 0, .1)
        }

        .connect-button.blue span:before, .connect-button span:before {
            background-image: url("data:image/png;base64,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")
        }

        @media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min--moz-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5) {
            .connect-button.blue span:before, .connect-button span:before {
                background-image: url("data:image/png;base64,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")
            }
        }
    </style>

@endsection


