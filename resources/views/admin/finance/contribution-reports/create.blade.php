@extends('admin._layouts._app')

@section('title', 'Contribution Reports - Finance')

@section('content')

    <div class="admin-heading-section">
        <h1>
            Yearly Contribution Reports
        </h1>
    </div>

    <main class="mt-4 admin-section">
        <div class="mx-auto max-w-(--breakpoint-2xl)">
            <div class="overflow-hidden rounded-lg bg-white">
                <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                    @include('admin.finance.contribution-reports._layouts._sidebar')

                    <div class="divide-y divide-gray-200 lg:col-span-9">
                        <div class="py-6 px-4 sm:p-6 lg:pb-8">
                            <div class="mb-6">
                                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                                    Finalize New Report
                                </h2>
                                <p class="mt-1 text-sm text-gray-500">
                                    Finalize a new contribution report for a specific year.
                                </p>
                            </div>

                            @if(auth()->user()->hasPermission('finance.contributions.view-amounts'))
                                <form action="{{ route('admin.finance.contributions.reports.yearly.create.submit') }}" method="post" class="space-y-6">
                                    @csrf
                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <div class="sm:col-span-2">
                                            <label for="year" class="block text-sm font-medium text-gray-700">
                                                Year
                                            </label>
                                            <div class="mt-1">
                                                <select id="year"
                                                        name="year"
                                                        class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-hidden focus:ring-blue-500 sm:text-sm">
                                                    <option value="{{ $year }}">
                                                        {{ $year }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Add a text box for a "statement_message" -->
                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <div class="sm:col-span-5">
                                            <label for="statement_message" class="block text-sm font-medium text-gray-700">
                                                Statement Message
                                                <span class="float-right text-sm text-blue-600">
                                                    <i class="fa fa-eye"></i> Visible to Users
                                                </span>
                                            </label>
                                            <textarea
                                                    rows="3"
                                                    id="statement_message"
                                                    name="statement_message"
                                                    class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-hidden focus:ring-blue-500 sm:text-sm"
                                                    placeholder=""></textarea>
                                            <p class="mt-1 text-xs text-gray-500">
                                                This message will be displayed at the top of each user's contribution statement.
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Add a textbox for "notes" -->
                                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                        <div class="sm:col-span-5">
                                            <label for="notes" class="block text-sm font-medium text-gray-700">
                                                Notes
                                                <span class="float-right text-sm text-gray-600">
                                                    <i class="fa fa-eye-slash"></i> Internal Only
                                                </span>
                                            </label>
                                            <textarea
                                                    rows="3"
                                                    id="notes"
                                                    name="notes"
                                                    class="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-hidden focus:ring-blue-500 sm:text-sm"
                                                    placeholder=""></textarea>
                                            <p class="mt-1 text-xs text-gray-500">
                                                These notes are for internal reference only and will not be visible to users.
                                            </p>
                                        </div>
                                    </div>

                                    <p class="inline-block mt-2 text-sm text-gray-800 bg-purple-100 py-2 px-4 rounded-md border border-purple-500">
                                        <i class="fa fa-info-circle"></i> NOTES
                                        <br>
                                                                          — All statements will automatically include wording that "no good or services were provided"
                                        <br>
                                                                          — Reports will <strong>not</strong> be generated or updated for users that have already received a report for this year.
                                    </p>

                                    <div class="flex justify-start">
                                        <button type="submit"
                                                class="admin-button-blue">
                                            <svg class="mr-2 -ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                            </svg>
                                            Generate Contribution Reports for {{ $year }}
                                        </button>
                                    </div>
                                </form>
                            @else
                                <div class="rounded-md bg-yellow-50 p-4">
                                    <div class="flex">
                                        <div class="shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">
                                                Permission Required
                                            </h3>
                                            <div class="mt-2 text-sm text-yellow-700">
                                                <p>
                                                    You do not have permissions to generate financial reports.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection


