@php
    $setting_link = 'flex items-center px-3 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50';
    $setting_link_selected = 'flex items-center px-3 py-3 text-sm font-medium text-blue-700 bg-blue-50 hover:text-blue-700 hover:bg-blue-50';
    $setting_icon_classes = 'shrink-0 -ml-1 mr-3 h-6 w-6 text-gray-400';
    $setting_icon_classes_selected = 'shrink-0 -ml-1 mr-3 h-6 w-6 text-blue-500';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="space-y-6">
        <div class="">
            <a href="{{ route('admin.finance.contributions.reports.yearly.index') }}" class="{{ request()->routeIs('admin.finance.contributions.reports.yearly.index') ? $setting_link_selected : $setting_link }}" aria-current="page">
                <x-heroicon-o-document-plus class="{{ request()->routeIs('admin.finance.contributions.reports.yearly.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Create New Report</span>
            </a>
            <a href="{{ route('admin.finance.contributions.reports.yearly.history') }}" class="{{ request()->routeIs('admin.finance.contributions.reports.yearly.history*') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-document-arrow-down class="{{ request()->routeIs('admin.finance.contributions.reports.yearly.history*') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Past Reports</span>
            </a>
        </div>
    </nav>
</aside>