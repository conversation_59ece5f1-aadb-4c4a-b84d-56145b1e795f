<?php

$i_html = null;
$page   = 2;

// First create our header
$i_html = '<div style="font-weight: bold; font-size: 140%; width: 100%; text-align: center;">Contributions<br></div>
				<table cellpadding="3" style="width: 100%; border: 1px solid #777777;font-size: 140%">
				<tr>
				<td style="width: 15%; text-align: left; font-weight: bold;">Date</td>
				<td style="width: 65%; text-align: left; font-weight: bold;">Fund</td>
				<td style="width: 20%; text-align: right; font-weight: bold;">Amount</td>
                </tr>';


$j = 0;
foreach ($contribution_report->transactions as $report_transaction):

    $i_html .= '<tr>
				<td style="text-align: left;">' . $report_transaction->transaction?->posted_at->format('m/d/Y') . '</td>
				<td style="text-align: left;">' . $report_transaction->transaction?->bucket->name . '</td>
				<td style="text-align: right;">' . Brick\Money\Money::ofMinor($report_transaction->amount, 'USD')->getAmount() . '</td>
				</tr>';

    // If we reached the max number of rows per page, do a page break, redo the header and beginning of the table
    if (($j > 1 && $j < 25 && $j % 22 == 0) || ($j > 32 && $j % 32 == 0)) {
        $i_html .= '</table><div style="page-break-after:always;"></div>';
        $i_html .= '<table style="width: 100%; padding: 0; margin: 0; font-size: 120%;">
    <tr>
        <td class="normalRow" style="width: 50%;"><span style="font-weight: bolder;">' . $account->name . '</span><br><span>' . $account->getAddressString(false) . '</span></td>
        <td class="normalRow" style="text-align: right;">Page ' . $page . '<br/>' . $contribution_report->posted_at->format('F d, Y') . '</td>
    </tr>
</table>';
        // Add our header again
        $i_html .= '<div style="font-weight: bold; font-size: 140%; width: 100%; text-align: center;">Contributions</div>
				<table cellpadding="3" style="width: 100%; border: 1px solid #777777;font-size: 140%">
				<tr>
				<td style="width: 15%; text-align: left; font-weight: bold;">Date</td>
				<td style="width: 65%; text-align: left; font-weight: bold;">Fund</td>
				<td style="width: 20%; text-align: right; font-weight: bold;">Amount</td>
                </tr>';
        $page++;
    }

    $j++;

endforeach;
?>

<html>
<style type=\"text/css\">
    body {
        font-family: 'Helvetica';
        font-size: 9px;
    }

    td {
        font-size: 75%;
    }

    td.normalRow {
        font-size: 100%;
    }

    @page {
        margin: 0.2in 0.5in 0.2in 0.5in;
    }
</style>
<body>

<table style="width: 100%; padding: 0; margin: 0; font-size: 120%;">
    <tr>
        <td class="normalRow" style="width: 50%;"><span style="font-weight: bolder;">{{ $account->name }}</span><br><span>{!! $account->getAddressString(false) !!}</span>
            {!! $account->phone_work ? '<br>' . \App\Users\Phone::format($account->phone_work, '-') : null !!}</td>
        <td class="normalRow" style="text-align: right;">Page 1<br/>{{ $contribution_report->posted_at->format('F d, Y') }}</td>
    </tr>
    <tr>
        <td class="normalRow" style="text-align: center;" colspan="2">
            <br>
            <span style="font-weight: bold; font-size: 120%;">Year: {{ $year }}</span>
        </td>
    </tr>
</table>

<br><br>

<div style="font-size: 1.2em;">
    {{ $contribution_report->user->name }}
    <br>
    {{ $contribution_report->user->getFamilyAddress(true)?->getAddressString() }}
</div>

<br>

<table style="width: 100%; padding: 0; margin: 0; font-size: 110%; border: 1px solid #777777;">
    <thead>
    <tr>
        <th style="text-align: center; font-weight: bolder;">FUND SUMMARY</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td class="normalRow" style="width:50%; text-align: left;">
            Total Contributions from
            <br>
            {{ now()->setYear((int)$year)->startOfYear()->format('F d, Y') }} to {{ now()->setYear((int)$year)->endOfYear()->format('F d, Y') }}
            <br>
        </td>
        <td class="normalRow" style="text-align: right; font-weight: bolder;">
            $<?= Brick\Money\Money::ofMinor($contribution_report->amount_total, 'USD')->getAmount() ?>&nbsp;&nbsp;&nbsp;
        </td>
    </tr>
    </tbody>
</table>

<div style="text-align: center;">Unless otherwise noted, no goods or services were provided in connection with any contribution, or their value was insignificant, or consisted entirely of intangible religious benefits.</div>

@if($contribution_report->statement_message)
    <div style="text-align: center;">{{ $contribution_report->statement_message }}</div>
@endif

<?= $i_html; ?>
</body>
</html>