@extends('admin._layouts._app')

@section('title', 'Create Contribution')

@section('content')

    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.finance.index'),
            'text' => 'Finance',
        ], [
            'url' => route('admin.finance.contributions.index'),
            'text' => 'Contributions',
        ], [
            'url' => null,
            'text' => 'Create',
        ]]])

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Add Contribution
                </h1>
            </div>
            <div>

            </div>
        </div>

        @livewire('admin.finance.contributions.create')

    </div>

    <script>
        // Init our Alpine storage variable
        document.addEventListener('alpine:init', () => {
            Alpine.store('scanner_input', '');
        })

        {{-- debounce function for clearing input after X seconds. --}}
        var debounceScannerInput = _.debounce(function () {
            Alpine.store('scanner_input', '');
        }, 1000);


        // Monitor for a scanner input somewhere.
        window.addEventListener("keydown", function (event) {
            if (event.defaultPrevented || Alpine.store('ScannerCooldown') || document.activeElement.nodeName === 'TEXTAREA') {
                return; // Do nothing if the event was already processed
            }

            if (event.key === 'Enter' && document.activeElement.nodeName === 'INPUT' && document.activeElement.id === 'name') {
                {{-- If we've hit the Enter key && we have valid scanner_input, use Livewire to do a checkin attempt. --}}
                console.log('FOUND COMPLETED SCAN IN INPUT!', document.activeElement.value);

                // Parse check scanner input
                Livewire.dispatch('checkScanned', {check_scanner_input: document.activeElement.value});

                // Select our amount field after scanning.
                setTimeout(() => {
                    document.getElementById('amount').select();
                }, 200);

                return;
            }
            // If submitting from the amount input.
            if (event.key === 'Enter' && document.activeElement.nodeName === 'INPUT' && document.activeElement.id === 'amount') {
                {{-- If we've hit the Enter key && we have valid scanner_input, use Livewire to do a checkin attempt. --}}
                console.log('SUBMIT VIA AMOUNT!', document.activeElement.value);

                // Parse check scanner input
                Livewire.dispatch('createContribution');

                // Select our amount field after scanning.
                setTimeout(() => {
                    document.getElementById('amount_input').select();
                }, 300);

                return;
            }

            {{-- Don't do anything if we're trying to do input in an <input> field (like creating a new visitor). --}}
                    {{-- EXCEPT, if we're on the "search" bar at the top. --}}
            if (document.activeElement.nodeName === 'INPUT' && document.activeElement.id !== 'name1') {
                return; // Do nothing if we're trying to do input.
            }

            {{-- A list of input we EXPECT, let's not record anything else. --}}
            var valid_keys = [
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                ':', ';', '|', '?', '-', ' '
            ];

            {{-- Signal the debounce timer to reset for this specific key press. --}}
            debounceScannerInput();

            {{-- If we have found this key in our list of valid_keys, add it to our scanner_input string --}}
            if (valid_keys.indexOf(event.key) !== -1) {
                Alpine.store('scanner_input', Alpine.store('scanner_input') + event.key);
                // Cancel the default action to avoid it being handled twice
                event.preventDefault();

            } else if (event.key === 'Enter' && Alpine.store('scanner_input') !== '') {
                {{-- If we've hit the Enter key && we have valid scanner_input, use Livewire to do a checkin attempt. --}}
                console.log('FOUND COMPLETED SCAN!', Alpine.store('scanner_input'));

                // Parse check scanner input
                Livewire.dispatch('checkScanned', {check_scanner_input: Alpine.store('scanner_input')});

                // Select our amount field after scanning.
                setTimeout(() => {
                    document.getElementById('amount').select();
                }, 200);

                // Clear our input.
                Alpine.store('scanner_input', '');

                {{-- If we sent for a checkin/out, let's wait so we don't get accidental double input --}}
                Alpine.store('ScannerCooldown', true);

                {{-- If we sent for a checkin/out, let's wait so we don't get accidental double input --}}
                setTimeout(() => {
                    Alpine.store('ScannerCooldown', false);
                }, 2500);

                // Cancel the default action to avoid it being handled twice
                event.preventDefault();

            } else {
                {{-- console.log('char not valid!'); --}}
            }
        }, true);
    </script>

@endsection

