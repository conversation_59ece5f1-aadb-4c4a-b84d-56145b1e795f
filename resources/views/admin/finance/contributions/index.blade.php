@extends('admin._layouts._app')

@section('title', 'Contributions')

@section('content')

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.finance.index'),
            'text' => 'Finance',
        ], [
            'url' => route('admin.finance.contributions.index'),
            'text' => 'Contributions'
        ]]])

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Contributions
                </h1>
            </div>
            <div>
                <a href="{{ route('admin.finance.contributions.create') }}" class="admin-button-blue">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Add Contributions
                </a>
            </div>
        </div>

        @livewire('admin.finance.contributions.contribution-index')

    </div>

@endsection