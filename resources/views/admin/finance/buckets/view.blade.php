@extends('admin._layouts._app')

@section('title', 'Bucket View')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.finance.index'),
            'text' => 'Finance',
        ], [
            'url' => route('admin.finance.buckets.index'),
            'text' => 'Buckets',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.finance.buckets.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $bucket->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.finance.buckets.view-bucket-sidebar')

                        @php
                            $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                            $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                        @endphp

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8">
                                <div class="mb-4">
                                    <h3 class="text-2xl font-medium leading-6 text-gray-900">Stats</h3>
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-4">
                                        <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                            <dt class="truncate text-sm font-medium text-gray-500">{{ date('Y') }} Budget</dt>
                                            @if($bucket->currentBudget)
                                                <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                                    ${{ number_format($bucket->currentBudget->formatAmount()) }}
                                                </dd>
                                            @else
                                                <dd class="mt-1 text-xl font-light tracking-tight text-gray-400">
                                                    None
                                                </dd>
                                            @endif
                                        </div>
                                        <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                            <dt class="truncate text-sm font-medium text-gray-500">Transactions</dt>
                                            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                                {{ $bucket->transactions()->count() }}
                                            </dd>
                                        </div>
                                    </dl>
                                </div>
                                <div class="mb-4">
                                    <h3 class="text-2xl font-medium leading-6 text-gray-900">Overview</h3>
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-5">
                                        <span class="{{ $bucket->is_income ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-inbox-arrow-down class="w-5 h-5 mr-1 my-auto"/>
                                                Income Bucket
                                            </dt>
                                        </span>
                                        <span class="{{ $bucket->is_expense ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-currency-dollar class="w-5 h-5 mr-0.5 my-auto"/>
                                                Expense Bucket
                                            </dt>
                                        </span>
                                    </dl>
                                </div>
                                <div class="mb-4">
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
                                        <span class="{{ $bucket->is_contribution ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-banknotes class="mr-1 w-5 h-5 my-auto"/>
                                                Contribution Bucket
                                            </dt>
                                        </span>
                                        <span class="{{ $bucket->is_reimbursement ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-receipt-refund class="mr-1 w-5 h-5 my-auto"/>
                                                Reimbursement Bucket
                                            </dt>
                                        </span>
                                    </dl>
                                </div>

                                @if($bucket->description)
                                    <div class="mt-4">
                                        <h3 class="text-2xl font-medium leading-6 text-gray-900">Description</h3>
                                        <div class="mt-3 border p-3 border-gray-300 rounded-sm">
                                            {{ $bucket->description }}
                                        </div>
                                    </div>
                                @endif

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
