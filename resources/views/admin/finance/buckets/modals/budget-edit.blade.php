<form method="post" action="{{ route('admin.finance.buckets.budgets.edit', [$bucket, $budget]) }}">
    @csrf
    <div class="">
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Edit Budget
            </h3>
            <hr>
            <div class="mt-4">
                <div class="mt-4">
                    <label for="yearly_budget" class="block text-sm font-medium text-gray-700">Yearly Budget</label>
                    <div class="mt-1 flex rounded-md shadow-xs">
                        <span class="inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-50 pl-2 pr-3 text-base font-medium text-gray-500">$</span>
                        <input type="text" name="yearly_budget" id="yearly_budget"
                               class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm font-semibold"
                               placeholder="12000.00"
                               value="{{ $budget->formatAmount() }}">
                    </div>
                </div>
                <div class="mt-4">
                    <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                    <div class="mt-1 flex rounded-md shadow-xs">
                        <input type="text" name="description" id="description"
                               class="block flex-1 -ml-1.5 rounded-l-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                               placeholder="optional"
                               value="{{ $budget->description }}"/>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between">
                        <div class="flex justify-start">
                            <button type="submit"
                                    wire:click="createContribution"
                                    class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <x-heroicon-s-check class="w-4 mr-1"/>
                                Save Changes
                            </button>
                            <button type="button" @click="closeModal()" class="mt-3 w-full inline-flex justify-center rounded-sm border border-gray-300 px-4 py-2 bg-white text-base font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto">
                                Cancel
                            </button>
                        </div>
                        <button onclick="document.getElementById('deleteBudget{{ $budget->id }}').submit();" type="button" class="mt-2 admin-button-red-transparent-small my-0 py-0.5 text-xs">Delete</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<form method="post" action="{{ route('admin.finance.buckets.budgets.delete', [$bucket, $budget]) }}" id="deleteBudget{{ $budget->id }}">
    @csrf
    @method('delete')
</form>