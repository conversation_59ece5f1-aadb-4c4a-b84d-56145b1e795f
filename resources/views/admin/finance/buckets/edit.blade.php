@extends('admin._layouts._app')

@section('title', 'Bucket Edit')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.finance.index'),
            'text' => 'Finance',
        ], [
            'url' => route('admin.finance.buckets.index'),
            'text' => 'Buckets',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.finance.buckets.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $bucket->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.finance.buckets.view-bucket-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-9">

                            <div class="my-4 mx-4 flex justify-between">
                                <div class="text-3xl">
                                    Edit Bucket
                                </div>
                            </div>

                            <form method="post" action="{{ route('admin.finance.buckets.edit.save', $bucket) }}" class="bg-white px-4 py-5 sm:p-6">
                                @csrf
                                <div class="md:grid md:grid-cols-5 md:gap-6">
                                    <div class="mt-5 md:col-span-4 md:mt-0">
                                        <div class="grid grid-cols-4 gap-6">
                                            <div class="col-span-4 sm:col-span-4 col-start-1" x-data>
                                                <label for="name" class="block text-sm font-medium text-gray-700">Bucket Name</label>
                                                <input type="text"
                                                       name="name"
                                                       id="name"
                                                       autocomplete="off"
                                                       placeholder="Special Evangelism Fund"
                                                       value="{{ $bucket->name }}"
                                                       class="mt-1 block w-full rounded-md border-gray-300 placeholder:text-gray-800 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                                            </div>

                                            <div class="col-span-4 sm:col-span-4 col-start-1" x-data>
                                                <label for="account_code" class="block text-sm font-medium text-gray-700">Accounting Code</label>
                                                <input type="text"
                                                       name="account_code"
                                                       id="account_code"
                                                       autocomplete="off"
                                                       placeholder="5000-101"
                                                       value="{{ $bucket->account_code }}"
                                                       class="mt-1 block w-50 rounded-md border-gray-300 placeholder:text-gray-800 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-base">
                                            </div>

                                            <div class="col-span-4" x-data="{}">
                                                @if($bucket->is_income)
                                                    <div class="relative flex">
                                                        <div class="flex h-5 items-center">
                                                            <input id="is_contribution" aria-describedby="is_contribution-description"
                                                                   name="is_contribution" type="checkbox" value="1" @isChecked($bucket->is_contribution)
                                                                   class="h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                        </div>
                                                        <div class="ml-3 text-sm">
                                                            <label for="is_contribution" class="text-base font-medium text-gray-700">Contribution Bucket?</label>
                                                            <p id="is_contribution-description" class="text-gray-500">Selecting this will show this bucket to members as a bucket they can submit a contribution towards with Online Giving.</p>
                                                        </div>
                                                    </div>
                                                @endif
                                                @if($bucket->is_expense)
                                                    <div class="relative flex">
                                                        <div class="flex h-5 items-center">
                                                            <input id="is_reimbursement" aria-describedby="is_reimbursement-description"
                                                                   name="is_reimbursement" type="checkbox" value="1" @isChecked($bucket->is_reimbursement)
                                                                   class="h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                        </div>
                                                        <div class="ml-3 text-sm">
                                                            <label for="is_reimbursement" class="text-base font-medium text-gray-700">Reimbursement Bucket?</label>
                                                            <p id="is_reimbursement-description" class="text-gray-500">Selecting this will show this bucket to members as a bucket they can submit reimbursement requests to.</p>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>

                                            <div class="col-span-4 sm:col-span-3 sm:col-start-1">
                                                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                                <div class="mt-1 flex rounded-md shadow-xs">
                                                    <textarea name="description" id="description"
                                                              class="block w-full flex-1 pl-2 pr-0 border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-base"
                                                              placeholder="" rows="2">{{ $bucket->description }}</textarea>
                                                </div>
                                            </div>

                                            <div class="col-span-4 sm:col-span-4">
                                                <div class="flex justify-start">
                                                    <button type="submit"
                                                            class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-base font-medium text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                        <x-heroicon-s-check class="w-4 mr-1"/>
                                                        Save Changes
                                                    </button>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
