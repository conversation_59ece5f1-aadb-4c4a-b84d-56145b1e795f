@php
    $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
    $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="space-y-09 lg:space-y-1">
        <!-- Current: "bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700", Default: "border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900" -->
        <a href="{{ route('admin.finance.buckets.view', $bucket) }}" class="{{ request()->routeIs('admin.finance.buckets.view') ? $setting_link_selected : $setting_link }}" aria-current="page">
            <x-heroicon-o-user-group class="{{ request()->routeIs('admin.groups.view') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Overview</span>
        </a>

        <a href="{{ route('admin.finance.buckets.edit', $bucket) }}" class="{{ request()->routeIs('admin.finance.buckets.edit') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-cog class="{{ request()->routeIs('admin.finance.buckets.edit') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">General Settings</span>
        </a>

        <a href="{{ route('admin.finance.buckets.budgets', $bucket) }}" class="flex-row justify-between {{ request()->routeIs('admin.finance.buckets.budgets') ? $setting_link_selected : $setting_link }}">
            <div class="flex">
                <x-heroicon-o-currency-dollar class="{{ request()->routeIs('admin.finance.buckets.budgets') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Budgets</span>
            </div>
            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">
                {{ $bucket->budgets()->count() }}
            </span>
        </a>

        {{--        <a href="" class="flex-row justify-between {{ request()->routeIs('admin.groups.admins.index') ? $setting_link_selected : $setting_link }}">--}}
        {{--            <div class="flex">--}}
        {{--                <x-heroicon-o-key class="{{ request()->routeIs('admin.groups.admins.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>--}}
        {{--                <span class="truncate my-auto">Access Permissions</span>--}}
        {{--            </div>--}}
        {{--            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">--}}
        {{--                0--}}
        {{--            </span>--}}
        {{--        </a>--}}

    </nav>
</aside>