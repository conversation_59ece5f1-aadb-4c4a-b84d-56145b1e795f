@extends('admin._layouts._app')

@section('title', 'Create Expenses')

@section('content')

    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js"></script>

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.finance.index'),
            'text' => 'Finance',
        ], [
            'url' => route('admin.finance.expenses.index'),
            'text' => 'Expenses',
        ], [
            'url' => null,
            'text' => 'Create',
        ]
    ]])

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Add Expense
                </h1>
            </div>
            <div>

            </div>
        </div>

        @livewire('admin.finance.expenses.create')

    </div>

@endsection

