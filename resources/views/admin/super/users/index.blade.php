@extends('admin._layouts._app')

@section('title', 'SUPER - Account Settings')

@section('content')

    <div class="flex-1">
        <div class="md:flex md:items-start align-middle md:justify-between mb-4">
            <h1>
                _ Users
            </h1>
        </div>
    </div>

    <main class="relative admin-section">
        <div class="mx-auto max-w-(--breakpoint-2xl)">
            <div class="overflow-hidden rounded-lg bg-white">
                <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                    @php
                        $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                        $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                        $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                        $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                    @endphp

                    @include('admin.super._layouts.super-sidebar')

                    <div class="divide-y divide-gray-200 lg:col-span-10">
                        <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">
                            <form action="{{ route('super.users.index') }}" method="get" class="flex items-center gap-4">
                                <input type="text" name="search_term"/>
                                <button type="submit" class="admin-button-blue">Search</button>
                            </form>

                            <table class="mt-6" style="width: 100%" cellpadding="2">
                                <tr class="bg-gray-600 text-white">
                                    <th>ID</th>
                                    <th>Account ID</th>
                                    <th>Account Name</th>
                                    <th>Name</th>
                                    <th>Member</th>
                                    <th>Admin</th>
                                    <th>Login</th>
                                </tr>
                                @forelse($users as $user)
                                    <tr class="even:bg-gray-100 odd:bg-white">
                                        <td class="text-sm font-mono">
                                            <a href="{{ route('super.users.view', $user) }}">
                                                {{ $user->id }}
                                            </a>
                                        </td>
                                        <td class="text-sm font-mono text-center">
                                            {{ $user->account->id }}
                                        </td>
                                        <td class="text-sm font-mono">
                                            {{ $user->account->name }}
                                        </td>
                                        <td>
                                            {{ $user->name ?? $user->first_name }}
                                        </td>
                                        <td class="text-center">
                                            {{ $user->isMember() ? 'yes' : 'no' }}
                                        </td>
                                        <td class="text-center">
                                            {{ $user->hasAdminAccess() ? 'yes' : 'no' }}
                                        </td>
                                        <td class="text-center">
                                            <a href="/loginAs/{{ $user->id }}">Login As</a>
                                        </td>
                                    </tr>
                                @empty
                                    <div class="col-md-12 mt-4 mb-5 text-center">
                                        <span class="badge badge-warning badge-large">No users found.</span>
                                    </div>
                                @endforelse
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
