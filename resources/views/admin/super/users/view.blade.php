@extends('admin._layouts._app')

@section('title', 'SUPER - Account Settings')

@section('content')

    <div class="admin-standard-col-width">

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <h1>
                    _ Users
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @php
                            $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                            $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                            $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                            $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                        @endphp

                        @include('admin.super._layouts.super-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-10">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8">
                                <h2>{{ $user->name }} <small># {{ $user->id }}</small></h2>
                                <h3>{{ $user->account->name }} <small># {{ $user->account->id }}</small></h3>

                                Is Member? {{ $user->isMember() ? 'Yes' : 'No' }}<br>
                                Is Admin? {{ $user->hasAdminAccess() ? 'Yes' : 'No' }}<br>

                                <h3 class="mt-6">Groups</h3>
                                <table class="ml-6">
                                    <thead>
                                    <tr>
                                        <th class="px-2">ID</th>
                                        <th class="px-2">Name</th>
                                        <th class="px-2">Posts</th>
                                        <th class="px-2">Email</th>
                                        <th class="px-2">SMS</th>
                                        <th class="px-2">Voice</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($user->groups as $group)
                                        <tr>
                                            <td class="px-2 m-0">{{ $group->id }}</td>
                                            <td class="px-2 m-0">{{ $group->name }}</td>
                                            <td class="px-2 m-0">{{ $group->enable_posts ? 'posts' : '-' }}</td>
                                            <td class="px-2 m-0">{{ $group->hasMessageType(1) ? 'email' : '-' }}</td>
                                            <td class="px-2 m-0">{{ $group->hasMessageType(2) ? 'sms' : '-' }}</td>
                                            <td class="px-2 m-0">{{ $group->hasMessageType(4) ? 'voice' : '-' }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>

                                <h3 class="mt-6">Roles</h3>
                                <table class="ml-6">
                                    <thead>
                                    <tr>
                                        <th class="px-2">ID</th>
                                        <th class="px-2">Name</th>
                                        <th class="px-2">Membership?</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($user->roles as $role)
                                        <tr>
                                            <td class="px-2 m-0">{{ $role->id }}</td>
                                            <td class="px-2 m-0">{{ $role->name }}</td>
                                            <td class="px-2 m-0">{{ $role->indicates_membership ? 'membership' : '-' }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>

                                <h3 class="mt-6">Emails</h3>
                                <div class="ml-6">
                                    @foreach($user->emails as $email)
                                        <div class="p-0 m-0">{{ $email->email }} <small># {{ $email->id }}</small></div>
                                    @endforeach
                                </div>

                                <h3 class="mt-6">Phones</h3>
                                <div class="ml-6">
                                    @foreach($user->phones as $phone)
                                        <div class="p-0 m-0">{{ $phone->number }} <small># {{ $phone->id }}</small></div>
                                    @endforeach
                                </div>

                                <h3 class="mt-6">Send Mobile Notification</h3>
                                <form action="{{ route('super.users.send.mobile-notification', $user->id) }}" method="post">
                                    @csrf
                                    Device:
                                    <select name="user_device_id">
                                        <option></option>
                                        @foreach($user->devices as $device)
                                            <option value="{{ $device->id }}">{{ $device->name }} - {{ $device->device_version }} - {{ $device->device_token }}</option>
                                        @endforeach
                                    </select>
                                    Title:
                                    <input type="text" name="title" value="Test Message"/>
                                    Message:
                                    <textarea name="message"></textarea>
                                    <button type="submit" class="admin-button-blue">Send</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
