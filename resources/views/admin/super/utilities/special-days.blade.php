@extends('admin._layouts._app')

@section('title', 'SUPER - Account Settings')

@section('content')

    <div class="admin-standard-col-width">

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <h1>
                    _ Account Settings
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @php
                            $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                            $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                            $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                            $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                        @endphp

                        @include('admin.super._layouts.super-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-10">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">
                                <h1>Import Dates for an Account</h1>
                                <form action="{{ route('super.utilities.special-days.submit') }}" method="post">
                                    @csrf
                                    <span>Account:</span>
                                    <select name="account_id">
                                        @foreach(\App\Accounts\Account::all() as $current_account)
                                            <option value="{{ $current_account->id }}">{{ $current_account->id }} -- {{ $current_account->name }}</option>
                                        @endforeach
                                    </select>
                                    <span>Date Type:</span>
                                    <select name="date_type">
                                        <option value="birthdate">Birthdays</option>
                                        <option value="date_married">Anniversaries</option>
                                        <option value="date_baptism">Baptism</option>
                                        <option value="date_membership">Membership Date</option>
                                    </select>
                                    <input type="checkbox" name="overwrite_existing" value="1"/>
                                    <span>Overwrite Existing </span>
                                    <br/>
                                    <span>Data:</span>
                                    <textarea name="data" rows="10"></textarea>
                                    <span>Format: One entry per line: <code>First_name,Last_name,Date</code></span>
                                    <br>
                                    <button type="submit" class="admin-button-blue mt-4" href="#"><i class="far fa-check"></i> &nbsp; Upload Data</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
