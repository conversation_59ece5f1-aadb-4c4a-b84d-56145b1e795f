@extends('admin._layouts._app')

@section('title', 'Sermon Tag - Create')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.sermons.index'),
            'text' => 'Sermons',
        ], [
            'url' => route('admin.sermons.tags.index'),
            'text' => 'Tags',
        ], [
            'url' => route('admin.sermons.tags.create'),
            'text' => 'Create',
        ]]])

        <div class="mb-4 md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Sermon Tag - Create
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="py-6 px-4 sm:p-6 lg:pb-8">

                        {{--This is section being displayed beside the sidebar --}}
                        <form action="{{ route('admin.sermons.tags.store') }}" method="post" enctype="multipart/form-data" id="pageForm">
                            {{ csrf_field() }}

                            <div class="form-group py-2">
                                <label for="title">Name</label>
                                <input type="text" name="name" class="form-control{{ $errors->has('name') ? ' is-invalid' : '' }}" value="{{ old('name') }}" autocomplete="off">
                                @if ($errors->has('name'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('name') }}</strong>
                                    </span>
                                @endif
                            </div>

                            <div class="form-group py-2">
                                <label for="description">Description</label>
                                <textarea name="description" rows="2" class="form-control{{ $errors->has('description') ? ' is-invalid' : '' }}">{{ old('description') }}</textarea>
                                @if ($errors->has('description'))
                                    <span class="invalid-feedback">
                                        <strong>{{ $errors->first('description') }}</strong>
                                    </span>
                                @endif
                            </div>

                            <div class="custom-control custom-switch">
                                <input type="hidden" name="is_hidden" value="0"/>
                                <input class="custom-control-input" type="checkbox" name="is_hidden" value="1" id="checkbox_hidden">
                                <label class="custom-control-label" for="checkbox_hidden">
                                    Make this tag hidden from Lightpost users.
                                </label>
                            </div>
                            <div class="custom-control custom-switch">
                                <input type="hidden" name="is_public" value="1"/>
                                <input class="custom-control-input" type="checkbox" name="is_public" value="1" id="checkbox_public" checked>
                                <label class="custom-control-label" for="checkbox_public">
                                    Make this tag public to people outside Lightpost.
                                </label>
                            </div>

                            <br>

                            <div class="form-group py-2">
                                <button type="submit" class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; getElementById('pageForm').submit()">
                                    <i class="fa fa-check pr-2" aria-hidden="true"></i> Create Tag
                                </button>
                            </div>

                            <br>
                        </form>

                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection






