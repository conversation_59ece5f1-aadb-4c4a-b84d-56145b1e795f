@extends('admin._layouts._app')

@section('title', 'Sermon - Create')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.sermons.index'),
            'text' => 'Sermons',
        ], [
            'url' => route('admin.sermons.create'),
            'text' => 'Create',
        ]]])

        <div class="mb-4 md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Create Sermon
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl) ">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="py-6 px-4 sm:p-6 lg:pb-8">

                        {{--This is section being displayed beside the sidebar --}}
                        <form action="{{ route('admin.sermons.store') }}" method="post" enctype="multipart/form-data" id="pageForm">
                            {{ csrf_field() }}

                            <div class="form-group pb-2">
                                <label for="title">Title</label>
                                <input type="text" name="title" class="form-control{{ $errors->has('title') ? ' is-invalid' : '' }}" value="{{ old('title') }}" autocomplete="off">
                                @if ($errors->has('title'))
                                    <span class="invalid-feedback">
                                                <strong>{{ $errors->first('title') }}</strong>
                                            </span>
                                @endif
                            </div>

                            <div class="grid grid-cols-12 gap-4">
                                <div class=" col-span-6">
                                    <div class="form-group py-2 ">
                                        <label for="date_sermon">Sermon Date</label>
                                        <input type="date" class="w-full" name="date_sermon" class="form-control{{ $errors->has('date_sermon') ? ' is-invalid' : '' }}" value="{{ old('date_sermon') }}" autocomplete="off" placeholder="i.e. AM , PM">
                                        @if ($errors->has('date_sermon'))
                                            <span class=" invalid-feedback">
                                                <strong>{{ $errors->first('date_sermon') }}</strong>
                                                </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-span-6 ">
                                    <div class="form-group py-2">
                                        <label for="type">Type</label>
                                        <input type="text" name="type" class="form-control{{ $errors->has('type') ? ' is-invalid' : '' }}" value="{{ old('type') }}" autocomplete="off" placeholder="i.e. AM or PM">
                                        @if ($errors->has('type'))
                                            <span class="invalid-feedback">
                                                        <strong>{{ $errors->first('type') }}</strong>
                                                    </span>
                                        @endif
                                    </div>
                                </div>
                            </div>


                            <div class="grid grid-cols-12 gap-4">
                                <div class="col-span-6">
                                    <div class="form-group py-2">
                                        <label for="speaker">Speaker Name</label>
                                        <input type="text" name="speaker" class="form-control{{ $errors->has('speaker') ? ' is-invalid' : '' }}" value="{{ old('speaker') }}">
                                        @if ($errors->has('speaker'))
                                            <span class="invalid-feedback">
                                                <strong>{{ $errors->first('speaker') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-span-6">
                                    <div class="form-group py-2">
                                        <label for="language">Language</label>
                                        <select name="language" id="language" class="form-control col-span-6">
                                            @foreach(\App\Sermons\Sermon::$available_languages as $code => $title)
                                                <option value="{{ $code }}" {{ old('language') == $code ? 'selected="selected"' : '' }}>{{ $title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class=" col-span-12">
                                    <div class="form-group py-2">
                                        <label for="summary">Summary</label>
                                        <span class="float-right text-muted mt-2" style="font-size: 80%;">A short, 1 to 3 sentence explanation of the sermon.</span>
                                        <textarea name="summary" rows="2" class="form-control{{ $errors->has('summary') ? ' is-invalid' : '' }}">{{ old('summary') }}</textarea>
                                        @if ($errors->has('summary'))
                                            <span class="invalid-feedback">
                                                    <strong>{{ $errors->first('summary') }}</strong>
                                                </span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-span-12">
                                    <div class="form-group py-2">
                                        <label for="description">Description</label>
                                        <span class="float-right text-muted mt-2" style="font-size: 80%;">A longer description with details of the sermon, speaker(s) and perhaps your congregation.</span>
                                        <textarea name="description" rows="4" class="form-control{{ $errors->has('description') ? ' is-invalid' : '' }}">{{ old('description') }}</textarea>
                                        @if ($errors->has('description'))
                                            <span class="invalid-feedback">
                                                    <strong>{{ $errors->first('description') }}</strong>
                                                </span>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-12 gap-4">
                                <div class="col-span-6">
                                    <div class="form-group py-2">
                                        <label for="file_title">MP3 File Title</label>
                                        <input type="text" name="file_title" class="form-control{{ $errors->has('file_title') ? ' is-invalid' : '' }}" value="{{ old('file_title') }}" autocomplete="off">
                                        <small class="form-text text-muted">This will be used when a user downloads this file.</small>
                                        @if ($errors->has('file_title'))
                                            <span class="invalid-feedback">
                                                        <strong>{{ $errors->first('file_title') }}</strong>
                                                    </span>
                                        @endif
                                    </div>
                                    <div class="form-group py-2">
                                        <label for="">Tags</label>
                                        <select name="tags[]" id="tags" class="js-choice1" multiple="multiple">
                                            @foreach ($tags as $tag)
                                                <option value="{{ $tag->id }}" {{ Illuminate\Support\Arr::has(request()->get('tags[]'), $tag->id) ? 'selected' : null }}>{{ $tag->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-span-6">
                                    <div class="form-group py-2">
                                        <label for="user_file">MP3 File</label>
                                        <input class="custom-file-input w-full border-gray-300 border p-4 rounded-sm" id="fileInput" type="file" name="user_file">

                                        <small class="form-text text-muted">
                                            - It is recommended that the file you upload be in a format:<br>
                                            DATE_TYPE_SPEAKER_TITLE.mp3<br>
                                            - <em>2019-01-01_PM_John-Baker_Good-Attitudes.mp3</em>
                                            <br>
                                            - Special characters will automatically be removed from the file name.
                                        </small>
                                        <script type="text/javascript">
                                            $('input[type="file"]').change(function (e) {
                                                var fileName = e.target.files[0].name;
                                                $('.custom-file-label').html(fileName);
                                            });
                                        </script>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-12 gap-4 mt-4">
                                <div class="col-span-6">
                                    <div class="custom-control custom-switch">
                                        <input type="hidden" name="is_hidden" value="0"/>
                                        <input class="custom-control-input" type="checkbox" name="is_hidden" value="1" id="checkbox_hidden">
                                        <label class="custom-control-label" for="checkbox_hidden">
                                            Make this sermon hidden from Lightpost users.
                                        </label>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="hidden" name="is_public" value="0"/>
                                        <input class="custom-control-input" type="checkbox" name="is_public" value="1" id="checkbox_public" checked>
                                        <label class="custom-control-label" for="checkbox_public">
                                            Make this sermon public to people outside Lightpost.
                                        </label>
                                    </div>

                                    <div class="form-group py-2 mt-4">
                                        <button type="submit" class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('pageForm').submit()">
                                            <i class="fa fa-check pr-2" aria-hidden="true"></i> Create Sermon
                                        </button>
                                    </div>
                                </div>
                                <div class="col-span-6">
                                    <div class="form-group py-2">
                                        <label for="youtube_url">YouTube URL</label>
                                        <input type="text" name="youtube_link" class="form-control{{ $errors->has('youtube_link') ? ' is-invalid' : '' }}" value="{{ old('youtube_link') }}" autocomplete="off">
                                        @if ($errors->has('youtube_link'))
                                            <span class=" invalid-feedback">
                                                <strong>{{ $errors->first('youtube_link') }}</strong>
                                                </span>
                                        @endif
                                    </div>
                                    <div class="form-group py-2">
                                        <label for="youtube_id">YouTube Video ID</label>
                                        <input type="text" name="youtube_id" class="form-control{{ $errors->has('youtube_id') ? ' is-invalid' : '' }}" value="{{ old('youtube_id') }}" autocomplete="off">
                                        @if ($errors->has('youtube_id'))
                                            <span class=" invalid-feedback">
                                                <strong>{{ $errors->first('youtube_id') }}</strong>
                                                </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        var initChoiceJs = function () {
            jschoice1 = new Choices('.js-choice1', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($tags) !!},
            });


            jschoice1.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addGroupFilter', {group_id: event.detail.value});
                    // do something creative here...
                    // console.log('item added');
                    // console.log(event.detail.id);
                    // console.log(event.detail.value);
                    // console.log(event.detail.label);
                    // console.log(event.detail.customProperties);
                    // console.log(event.detail.groupValue);
                },
                false,
            );
            jschoice1.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeGroupFilter', {remove_group_id: event.detail.value});
                },
                false,
            );
        }
        document.addEventListener('contentChanged', (event) => {
            console.log('changed');
            initChoiceJs();
        });
        document.addEventListener('DOMContentLoaded', event => {
            initChoiceJs();
        });
    </script>

@endsection

