<div class="modal-header px-4 text-white bg-blue-700 sm:px-6">
    <h3 class="modal-title text-lg font-medium text-white">Edit Crisis Check-in</h3>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        {{--<span aria-hidden="true">&times;</span>--}}
    </button>
</div>

<div class="modal-body pl-4">
    <form method="post" action="{{ route('admin.crises.save', $crisis) }}" id="EditCrisis">
        @method('put')
        @csrf()

        <div class="row">
            <div class="col pt-2">
                <strong>Name:</strong> <input type="text" name="name" class="form-control border-slate-300" value="{{ $crisis->name }}" autocomplete="off" placeholder="i.e. February Winter Storm"/>
            </div>
        </div>
        <br>

        <hr>

        <div class="row">
            <div class="col my-3">
                <div class="custom-control custom-switch custom-switch-lg">
                    <input type="checkbox" name="is_active" value="1" class="custom-control-input" id="customSwitch1" @isChecked($crisis->is_active)>
                    <label class="custom-control-label font-weight-bold" for="customSwitch1">Active</label>
                </div>
            </div>
        </div>
        <hr>
    </form>
</div>
<div class="modal-footer pl-4 pt-2">
    <button class="admin-button-blue" onclick="document.getElementById('EditCrisis').submit();"><i class="fa fa-check"></i> Save Changes</button>
    {{--<a class="btn btn-light" data-dismiss="modal">Close</a>--}}
    <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        Cancel
    </button>
</div>
