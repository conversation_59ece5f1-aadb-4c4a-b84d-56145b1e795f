@extends('admin._layouts._app')

@section('title', 'crisis')

@section('content')

    <div class="admin-standard-col-width">


        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.crises.index'),
            'text' => 'Crisis',
        ], [
            'text' => 'Settings',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex justify-start">
                            <a href="{{ url()->previous() }}" class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200">
                                <x-heroicon-s-arrow-left class="w-5 p-0"/>
                            </a>
                            <h1>
                                Emergency Response
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-[theme(screens.xm)]">
                <div class="overflow-hidden rounded-lg bg-white ">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-7 lg:divide-y-0 lg:divide-x">


                        @php
                            $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                            $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                        @endphp

                        <div class="divide-y divide-gray-200 lg:col-span-7 mt">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-0">
                                <div class="row">
                                    <div class="col-md-12">

                                        <h1><strong> Emergency Check-in Settings </strong></h1>
                                    </div>
                                </div>

                                <hr>

                                <div class="row">
                                    <div class="col-md-7 ">

                                        <form action="{{ route('admin.crises.settings.save') }}" method="post" id="pageForm">
                                            @csrf
                                            @method('put')

                                            <div class="form-group mb-2 py-6">
                                                <label class="font-weight-bold">Groups to Notify</label>
                                                <br>
                                                <small>Groups listed here are users who are notified of a new crisis/emergency check-in.</small>
                                                <br>
                                                <div class="control-group mt-2">
                                                    <div class="controls">

                                                        <select name="notify_groups[]" id="groups" class="js-choice1" multiple="multiple" autocomplete=off>
                                                            @foreach(\App\Users\Group::visibleTo(Auth::user())->select(['id', 'name'])->get() as $group)
                                                                <option value="{{ $group->id }}" {{ in_array($group->id, (is_array($settings->notify_groups) ? $settings->notify_groups : [])) ? 'selected="selected"' : '' }}>{{ $group->name }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <hr>

                                            <div class="form-group mb-2 py-6">
                                                <label>Response Groups to Notify - Help Requests</label>
                                                <br>
                                                <small>Groups listed here will be the users who are notified of help requests.</small>
                                                <br>
                                                <div class="control-group mt-2">
                                                    <div class="controls">


                                                        <select name="help_notification_groups[]" id="groups" class="js-choice2" multiple="multiple" autocomplete=off>
                                                            @foreach(\App\Users\Group::visibleTo(Auth::user())->select(['id', 'name'])->get() as $group)
                                                                <option value="{{ $group->id }}" {{ in_array($group->id, (is_array($settings->notify_groups) ? $settings->help_notification_groups : [])) ? 'selected="selected"' : '' }}>{{ $group->name }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <hr>

                                            <div class="form-group mb-2 py-6">
                                                <label class="font-weight-bold">Response Groups to Notify - URGENT Help Requests</label>
                                                <br>
                                                <small>Groups listed here will be the users who are notified of <strong>urgent</strong> help requests.</small>
                                                <br>
                                                <div class="control-group mt-2">
                                                    <div class="controls">
                                                        <select name="urgent_help_notification_groups[]" id="groups" class="js-choice3" multiple="multiple" autocomplete=off>
                                                            @foreach(\App\Users\Group::visibleTo(Auth::user())->select(['id', 'name'])->get() as $group)
                                                                <option value="{{ $group->id }}" {{ in_array($group->id, (is_array($settings->notify_groups) ? $settings->urgent_help_notification_groups : [])) ? 'selected="selected"' : '' }}>{{ $group->name }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>

                                            <hr>

                                            <div class="form-group mb-2 ">
                                                <label class="font-weight-bold"></label>
                                                <br>
                                                <div class="custom-control custom-switch">
                                                    <input type="checkbox" name="send_via_email" class="custom-control-input type_checkbox" value="1" id="send_via_email" @isChecked($settings->send_via_email)/>
                                                    <label class="custom-control-label" for="send_via_email">Send help requests to groups via Email</label>
                                                </div>
                                                <div class="custom-control custom-switch">
                                                    <input type="checkbox" name="send_via_mobile_notification" class="custom-control-input type_checkbox" value="1" id="send_via_mobile_notification" @isChecked($settings->send_via_mobile_notification)/>
                                                    <label class="custom-control-label" for="send_via_mobile_notification">Send help requests to groups via Mobile Notification</label>
                                                </div>
                                            </div>

                                            <hr>

                                            <div class="form-group mt-4">
                                                <button type="button" class="admin-button-blue " onclick="this.disabled = true; this.innerHTML = 'Loading...'; getElementById('pageForm').submit()">
                                                    <i class="fa fa-check" aria-hidden="true"></i>&nbsp; Save Changes
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                @push('scripts')
                                    <script>
                                        $('#notify_groups_select').selectize({
                                            sortField: 'text',
                                            plugins: ['remove_button']
                                        });
                                        $('#help_notification_groups_select').selectize({
                                            sortField: 'text',
                                            plugins: ['remove_button']
                                        });
                                        $('#urgent_help_notification_groups_select').selectize({
                                            sortField: 'text',
                                            plugins: ['remove_button']
                                        });
                                    </script>
                                @endpush


                            </div>
                        </div>
                    </div>
                </div>
        </main>

    </div>

    <script>
        var initChoiceJs = function () {
            jschoice1 = new Choices('.js-choice1', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($settings->notify_groups) !!},
            });
            jschoice2 = new Choices('.js-choice2', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($settings->notify_groups) !!},
            });
            jschoice3 = new Choices('.js-choice3', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($settings->notify_groups) !!},
            });

            jschoice1.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addGroupFilter', {group_id: event.detail.value});
                    // do something creative here...
                    // console.log('item added');
                    // console.log(event.detail.id);
                    // console.log(event.detail.value);
                    // console.log(event.detail.label);
                    // console.log(event.detail.customProperties);
                    // console.log(event.detail.groupValue);
                },
                false,
            );
            jschoice1.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeGroupFilter', {remove_group_id: event.detail.value});
                },
                false,
            );

            jschoice2.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addGroupExcludeFilter', {group_id: event.detail.value});
                },
                false,
            );
            jschoice2.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeGroupExcludeFilter', {remove_group_id: event.detail.value});
                },
                false,
            );

            jschoice3.passedElement.element.addEventListener(
                'addItem',
                function (event) {
                    Livewire.dispatch('addGrade', {grade_id: event.detail.value});
                },
                false,
            );
            jschoice3.passedElement.element.addEventListener(
                'removeItem',
                function (event) {
                    Livewire.dispatch('removeGrade', {grade_id: event.detail.value});
                },
                false,
            );
        }
        document.addEventListener('contentChanged', (event) => {
            console.log('changed');
            initChoiceJs();
        });
        document.addEventListener('DOMContentLoaded', event => {
            initChoiceJs();
        });
    </script>

@endsection