@extends('admin._layouts._app')

@section('title', 'Emergency Check-in')

@section('content')

    <div class="admin-standard-col-width">

        {{--
        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.crisis.index'),
            'text' => 'crisis',
        ], [
            'text' => 'Overview',
        ]]])
            --}}

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Emergency Check-in
                </h1>
            </div>
            <div>
                <a class="admin-button-transparent" href="{{ route('admin.crises.settings') }}">
                    <i class="fa fa-cog mr-2" aria-hidden="true"></i> Settings
                    @if(Auth::user()->account->crisisSettings->isMissingSettings())
                        <span class="text-danger font-weight-bold">(Missing settings!)</span>
                    @endif
                </a>
                <a class="admin-button-blue" onclick="openSidebar('{{ route('admin.crises.create')  }}')">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i> Emergency Check-in
                </a>
            </div>
        </div>

        <main class="relative admin-section mt-4">
            <div class="mx-auto max-w-[theme(screens.xm)]">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-7 lg:divide-y-0 lg:divide-x">
                        @php
                            $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                            $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                        @endphp

                        <div class="divide-y divide-gray-200 lg:col-span-7">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">
                                <table class="w-full border border-collapse table-auto">
                                    <thead>
                                    <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                        <th>Name</th>
                                        <th>Total</th>
                                        <th class="text-left">Responses</th>
                                        <th>Active</th>
                                    </tr>
                                    </thead>
                                    <tbody class="w-max">
                                    @forelse($crises as $crisis)
                                        <tr class="w-max overflow-hidden rounded-lg bg-white px-5 py-4 my-4 border border-gray-300 hover:bg-gray-100">
                                            <td class="text-center">
                                                <h5 class="font-weight-bold mb-0 pb-0">
                                                    {{ $crisis->name }}
                                                </h5>
                                                <a href="{{ route('app.crises.view', $crisis) }}" class="admin-button-blue mt-2">
                                                    View Responses <i class="fa fa-arrow-right ml-2" aria-hidden="true"></i>
                                                </a>

                                            </td>
                                            <td class="text-center">
                                                <div class="mb-1 text-success">
                                                    @php
                                                        $emergency_count = $crisis->getOkResponses()->count();
                                                        $emergency_help = $crisis->getHelpResponses()->count();
                                                        $emergency_urgent = $crisis->getUrgentHelpResponses()->count();
                                                        $emergency_not = $crisis->getNotResponded()->count();
                                                    @endphp
                                                    <span class="inline-block px-2 py-1 w-10 text-center text-xs font-bold text-white bg-green-500 rounded-md">{{ $emergency_count  }}</span>
                                                </div>
                                                <div class="mb-1 text-warning">
                                                    <span class="inline-block px-2 py-1 w-10 text-center text-xs font-bold text-white bg-yellow-500 rounded-md">{{ $emergency_help }}</span>
                                                </div>
                                                <div class="mb-1 text-danger">
                                                    <span class="inline-block px-2 py-1 w-10 text-center text-xs font-bold text-white bg-red-500 rounded-md">{{ $emergency_urgent }}</span>
                                                </div>
                                                <div class="mb-1 text-secondary">
                                                    <span class="inline-block px-2 py-1 w-10 text-center text-xs font-bold text-white bg-gray-500 rounded-md">{{ $emergency_not }}</span>
                                                </div>
                                            </td>

                                            <td class="text-left">
                                                <div class="mb-1 text-success">
                                                    OK
                                                </div>
                                                <div class="mb-1 text-warning">
                                                    Need Help
                                                </div>
                                                <div class="mb-1 text-danger">
                                                    URGENT Help Needed
                                                </div>
                                                <div class="mb-1 text-secondary">
                                                    Unresponded
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                @if($crisis->is_active)
                                                    <div class="inline-block px-4 py-1 font-bold text-white bg-green-500 rounded-md">Active</div>
                                                @else
                                                    <div class="inline-block px-4 py-1 font-bold text-white bg-yellow-500 rounded-md">Inactive</div>
                                                @endif
                                                <div class="mt-2">
                                                    <a class="admin-button-transparent-small text-xs" onclick="openSidebar('{{ route('admin.crises.edit', $crisis)  }}')">
                                                        <i class="fal fa-edit mr-1"></i> Edit Crisis
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td class="text-center" colspan="100%">
                                                <span class="badge badge-warning badge-large m-3">No crises yet.</span>
                                            </td>
                                        </tr>
                                    @endforelse
                                    </tbody>
                                </table>

                                <div class="col-md-6">
                                    {{ $crises->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>




    {{--
        <div class="row">
            <div class="col-md-12">
                <h1>Account Settings</h1>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="nav nav-tabs justify-content-lg-center justify-content-sm-end" id="nav-tab" role="tablist">
                    <a class="nav-item nav-link active" href="{{ route('admin.accounts.settings.index') }}" role="tab" aria-controls="nav-general" aria-selected="true"><i class="fal fa-info-circle"></i>&nbsp; General</a>
                    <a class="nav-item nav-link" href="{{ route('admin.accounts.billing.index') }}" role="tab" aria-controls="nav-billing" aria-selected="false"><i class="fal fa-file-invoice"></i>&nbsp; Billing</a>
                    <a class="nav-item nav-link" href="{{ route('admin.accounts.features.index') }}" role="tab" aria-controls="nav-features" aria-selected="false"><i class="fal fa-cog"></i>&nbsp; Features</a>
                </div>
            </div>
        </div>
        --}}

@endsection





