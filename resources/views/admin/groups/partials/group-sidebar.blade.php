@php
    $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-3 flex items-center text-sm font-medium';
    $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-3 flex items-center text-sm font-medium';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="lg:space-y-0">
        <div class="pl-3 py-1.5 text-xs uppercase font-bold text-black bg-gray-100 border-b border-gray-300">
            General
        </div>

        <!-- Current: "bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700", Default: "border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900" -->
        <a href="{{ route('admin.groups.view', $group) }}" class="{{ request()->routeIs('admin.groups.view') ? $setting_link_selected : $setting_link }}" aria-current="page">
            <x-heroicon-o-user-group class="{{ request()->routeIs('admin.groups.view') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Overview</span>
        </a>

        <a href="{{ route('admin.groups.settings', $group) }}" class="{{ request()->routeIs('admin.groups.settings') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-cog class="{{ request()->routeIs('admin.groups.settings') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Settings</span>
        </a>

        <a href="{{ route('admin.groups.members', $group) }}" class="flex-row justify-between {{ request()->routeIs('admin.groups.members') ? $setting_link_selected : $setting_link }}">
            <div class="flex">
                <x-heroicon-o-users class="{{ request()->routeIs('admin.groups.members') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Members</span>
            </div>
            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">
                {{ $group->users()->count() }}
            </span>
        </a>

        <div class="flex flex-row justify-between px-3 py-1.5 text-xs uppercase font-bold text-black bg-gray-100 border-y border-gray-300">
            <div>Messaging</div>
            <div class="pl-2 font-normal">Email / SMS / Voice</div>
        </div>

        <a href="{{ route('admin.groups.messaging', $group) }}" class="{{ request()->routeIs('admin.groups.messaging') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-envelope class="{{ request()->routeIs('admin.groups.messaging') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Messaging</span>
        </a>

        <a href="{{ route('admin.groups.senders.index', $group) }}" class="flex-row justify-between {{ request()->routeIs('admin.groups.senders.index') ? $setting_link_selected : $setting_link }}">
            <div class="flex">
                <x-heroicon-o-paper-airplane class="{{ request()->routeIs('admin.groups.senders.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Authorized Senders</span>
            </div>
            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">
                {{ $group->senders()->count() }}
            </span>
        </a>

        @if($group->hasMessageType(2))
            <a href="{{ route('admin.groups.messaging.sms', $group) }}" class="pl-10 {{ request()->routeIs('admin.groups.messaging.sms') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-megaphone class="{{ request()->routeIs('admin.groups.messaging.sms') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Send SMS</span>
            </a>
        @endif
        @if($group->hasMessageType(4))
            <a href="{{ route('admin.groups.messaging.voice', $group) }}" class="pl-10 {{ request()->routeIs('admin.groups.messaging.voice') ? $setting_link_selected : $setting_link }}">
                <x-heroicon-o-megaphone class="{{ request()->routeIs('admin.groups.messaging.voice') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Send Voice</span>
            </a>
        @endif

        <a href="{{ route('admin.groups.message-history.index', $group) }}" class="flex-row justify-between {{ request()->routeIs('admin.groups.message-history.*') ? $setting_link_selected : $setting_link }}">
            <div class="flex">
                <x-heroicon-o-archive-box class="{{ request()->routeIs('admin.groups.message-history.*') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Message History</span>
            </div>
            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">
                {{ number_format($group->messages()->count()) }}
            </span>
        </a>

        <div class="pl-3 py-1.5 text-xs uppercase font-bold text-black bg-gray-100 border-y border-gray-300">
            Group Posts
        </div>

        <a href="{{ route('admin.groups.posts', $group) }}" class="{{ request()->routeIs('admin.groups.posts') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-chat-bubble-left-right class="{{ request()->routeIs('admin.groups.posts') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Group Posts</span>
        </a>

        <a href="{{ route('admin.groups.admins.index', $group) }}" class="flex-row justify-between {{ request()->routeIs('admin.groups.admins.index') ? $setting_link_selected : $setting_link }}">
            <div class="flex">
                <x-heroicon-o-key class="{{ request()->routeIs('admin.groups.admins.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                <span class="truncate my-auto">Group Post Admins</span>
            </div>
            <span class="mr-2 bg-gray-100 rounded-full py-0.5 px-2 border border-gray-300 text-xs">
                {{ $group->admins()->count() }}
            </span>
        </a>
    </nav>
</aside>