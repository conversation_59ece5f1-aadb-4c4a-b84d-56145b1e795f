@extends('admin._layouts._app')

@section('title', 'Group Post Settings')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.groups.index'),
            'text' => 'Groups',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.groups.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $group->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.groups.partials.group-sidebar')

                        <form class="lg:col-span-9" action="{{ route('admin.groups.posts.save', $group) }}" method="POST">
                            @csrf
                            <div class="divide-y divide-gray-200">
                                <div class="py-6 px-4 sm:p-6">
                                    <h3 class="text-2xl font-medium leading-6 text-gray-900">Group Post Settings</h3>
                                </div>

                                <div class="px-4 py-6 sm:px-6">
                                    <fieldset class="space-y-5">
                                        <legend class="sr-only"></legend>
                                        <div class="relative flex items-start">
                                            <div class="flex h-5 items-center">
                                                <input name="enable_posts" value="0" type="hidden"/>
                                                <input id="enable_posts" aria-describedby="enable_posts-description" name="enable_posts" type="checkbox" value="1" @isChecked($group->enable_posts) class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </div>
                                            <div class="ml-3">
                                                <label for="enable_posts" class="font-medium text-gray-700">Turn on Group Posts</label>
                                                <p id="comments-description" class="text-gray-500 text-sm">
                                                    Determines if Group Posts are turned on or not.
                                                    <br/>
                                                    Disabling this will make this group <em>not</em> show up in Group Posts.
                                                </p>
                                            </div>
                                        </div>
                                        <div class="relative flex items-start ml-8">
                                            <div class="flex h-5 items-center">
                                                <input name="allow_members_to_post" value="0" type="hidden"/>
                                                <input id="allow_members_to_post" aria-describedby="allow_members_to_post-description" name="allow_members_to_post" type="checkbox" value="1" @isChecked($group->allow_members_to_post) class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </div>
                                            <div class="ml-3">
                                                <label for="allow_members_to_post" class="font-medium text-gray-700">Enable Posts</label>
                                                <p id="candidates-description" class="text-gray-500 text-sm">
                                                    Disable this to allow only Admins to create posts.
                                                </p>
                                            </div>
                                        </div>
                                        <div class="relative flex items-start ml-8">
                                            <div class="flex h-5 items-center">
                                                <input name="allow_members_to_comment" value="0" type="hidden"/>
                                                <input id="allow_members_to_comment" aria-describedby="allow_members_to_comment-description" name="allow_members_to_comment" type="checkbox" value="1" @isChecked($group->allow_members_to_comment) class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </div>
                                            <div class="ml-3">
                                                <label for="allow_members_to_comment" class="font-medium text-gray-700">Enable Comments</label>
                                                <p id="candidates-description" class="text-gray-500 text-sm">
                                                    Disabling this does not allow <em>anyone</em> to comment or view existing comments. Only members of a group can see the membership list.
                                                </p>
                                            </div>
                                        </div>
                                        <div class="relative flex items-start ml-8">
                                            <div class="flex h-5 items-center">
                                                <input name="allow_members_to_view_membership" value="0" type="hidden"/>
                                                <input id="allow_members_to_view_membership" aria-describedby="allow_members_to_view_membership-description" name="allow_members_to_view_membership" type="checkbox" value="1" @isChecked($group->allow_members_to_view_membership) class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                            </div>
                                            <div class="ml-3">
                                                <label for="allow_members_to_view_membership" class="font-medium text-gray-700">Viewable Membership List</label>
                                                <p id="candidates-description" class="text-gray-500 text-sm">
                                                    Disabling this will hide the list of group members, except for Admins.
                                                </p>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>

                                <div class="mt-6 flex justify-start py-4 px-4 sm:px-6">
                                    <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 font-medium text-white shadow-xs hover:bg-blue-800 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Save Changes</button>
                                    <button type="button" class="ml-5 inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
