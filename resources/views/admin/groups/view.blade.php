@extends('admin._layouts._app')

@section('title', 'Group View')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.groups.index'),
            'text' => 'Groups',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.groups.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $group->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.groups.partials.group-sidebar')

                        @php
                            $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                            $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                        @endphp

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8">
                                <div class="mb-4">
                                    <h3 class="text-2xl font-medium leading-6 text-gray-900">Stats</h3>
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-4">
                                        <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                            <dt class="truncate text-sm font-medium text-gray-500">Members</dt>
                                            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ $group->users()->count() }}</dd>
                                        </div>

                                        <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                            <dt class="truncate text-sm font-medium text-gray-500">Admins</dt>
                                            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ $group->admins()->count() }}</dd>
                                        </div>

                                        <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                            <dt class="truncate text-sm font-medium text-gray-500">Authorized Senders</dt>
                                            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ $group->senders()->count() }}</dd>
                                        </div>

                                        <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                            <dt class="truncate text-sm font-medium text-gray-500">Group Posts</dt>
                                            <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">{{ $group->posts()->count() }}</dd>
                                        </div>
                                    </dl>
                                </div>
                                <div class="mb-4">
                                    <h3 class="text-2xl font-medium leading-6 text-gray-900">Overview</h3>
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-5">
                                        <a href="{{ route('admin.groups.settings', $group) }}" class="{{ $group->allow_individual_to_toggle ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-lock-open class="w-5 h-5 mr-1 my-auto"/>
                                                Open Group
                                            </dt>
                                        </a>
                                        <a href="{{ route('admin.groups.settings', $group) }}" class="{{ $group->is_hidden ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-lock-closed class="w-5 h-5 mr-0.5 my-auto"/>
                                                Private Group
                                            </dt>
                                        </a>
                                    </dl>
                                </div>
                                <div class="mb-4">
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-4 xl:grid-cols-5">
                                        <a href="{{ route('admin.groups.posts', $group) }}" class="{{ $group->enable_posts ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-user-group class="mr-1 w-5 h-5 my-auto"/>
                                                Group Posts
                                            </dt>
                                        </a>
                                        <a href="{{ route('admin.groups.posts', $group) }}" class="{{ $group->allow_members_to_post ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-document-check class="mr-1 w-5 h-5 my-auto"/>
                                                Posting
                                            </dt>
                                        </a>
                                        <a href="{{ route('admin.groups.posts', $group) }}" class="{{ $group->allow_members_to_comment ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-chat-bubble-bottom-center-text class="mr-1 w-5 h-5 my-auto"/>
                                                Commenting
                                            </dt>
                                        </a>
                                        <a href="{{ route('admin.groups.posts', $group) }}" class="{{ $group->allow_members_to_view_membership ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-users class="mr-1 w-5 h-5 my-auto"/>
                                                Membership List
                                            </dt>
                                        </a>
                                    </dl>
                                </div>
                                <div class="mb-4">
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
                                        <a href="{{ route('admin.groups.settings', $group) }}" class="{{ $group->indicates_membership ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-m-user-circle class="mr-1 w-5 h-5 my-auto"/>
                                                Indicates Membership
                                            </dt>
                                        </a>
                                        <a href="{{ route('admin.groups.settings', $group) }}" class="{{ $group->indicates_visitor ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-m-user class="mr-1 w-5 h-5 my-auto"/>
                                                Indicates Visitor
                                            </dt>
                                        </a>
                                    </dl>
                                </div>
                                <div class="mb-4">
                                    <h3 class="text-2xl font-medium leading-6 text-gray-900">Messaging</h3>
                                    <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-5">
                                        <a href="{{ route('admin.groups.messaging', $group) }}" class="{{ $group->hasMessageType(1) ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-envelope class="mr-1 w-5 h-5 my-auto"/>
                                                Email
                                            </dt>
                                        </a>
                                        <a href="{{ route('admin.groups.messaging', $group) }}" class="{{ $group->hasMessageType(2) ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-chat-bubble-left-ellipsis class="mr-1 w-5 h-5 my-auto"/>
                                                SMS
                                            </dt>
                                        </a>
                                        <a href="{{ route('admin.groups.messaging', $group) }}" class="{{ $group->hasMessageType(4) ? $enabled_option : $disabled_option }}">
                                            <dt class="flex truncate">
                                                <x-heroicon-o-phone class="mr-1 w-5 h-5 my-auto"/>
                                                Voice
                                            </dt>
                                        </a>
                                    </dl>
                                </div>


                                @if($group->hasMessageType(1))
                                    <div class="my-8 sm:ml-6">
                                        <h2 class="text-lg font-medium leading-6 text-gray-900">Email Messaging</h2>
                                        <p class="mt-1 text-sm text-gray-500">Use this email address to send an email to the group.<br>Senders must be an Authorized Sender.</p>
                                        <p class="mt-4 text-sm text-gray-800">
                                            <code class="rounded-sm px-3 py-2 bg-purple-100 border border-purple-500">
                                                {{ $group->messageHandlers()->where('message_type_id', 1)->first()?->address }}
                                            </code>
                                        </p>
                                    </div>
                                @endif

                                @if($group->hasMessageType(2))
                                    <div class="my-8 sm:ml-6">
                                        <h2 class="text-lg font-medium leading-6 text-gray-900">SMS Messaging</h2>
                                        <p class="mt-1 text-sm text-gray-500">Text messages will come from this phone number.</p>
                                        <p class="mt-4 text-sm text-gray-800">
                                            <code class="rounded-sm px-3 py-2 bg-gray-100 border border-gray-300">
                                                {{ \App\Users\Phone::format($group->messageHandlers()->where('message_type_id', 2)->first()?->address, '-') }}
                                            </code>
                                        </p>
                                    </div>
                                @endif

                                @if($group->hasMessageType(4))
                                    <div class="my-8 sm:ml-6">
                                        <h2 class="text-lg font-medium leading-6 text-gray-900">Voice Messaging</h2>
                                        <p class="mt-1 text-sm text-gray-500">Voice calls will come from this phone number.</p>
                                        <p class="mt-4 text-sm text-gray-800">
                                            <code class="rounded-sm px-3 py-2 bg-gray-100 border border-gray-300">
                                                {{ \App\Users\Phone::format($group->messageHandlers()->where('message_type_id', 4)->first()?->address, '-') }}
                                            </code>
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
