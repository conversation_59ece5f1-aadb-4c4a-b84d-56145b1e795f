<form method="post" action="{{ route('admin.groups.store') }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    @method('put')
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i>Create New Group
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 py-4 sm:px-6">
                <div class="flex flex-col lg:flex-row">
                    <div class="grow space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Group Name</label>
                            <div class="mt-1 flex rounded-md shadow-xs">
                                <input type="text" name="name" id="name" autocomplete="name" placeholder="Evangelism Group..." class="block w-full min-w-0 grow rounded-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            </div>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <div class="mt-1">
                                <textarea id="description" name="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="px-4 sm:px-6 pb-6">
                    <fieldset class="space-y-5">
                        <legend class="sr-only"></legend>
                        <div class="relative flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="allow_individual_to_toggle" aria-describedby="allow_individual_to_toggle-description" name="public_or_private" type="radio" value="public" class="h-4 w-4 rounded-full border-gray-300 text-blue-600 focus:ring-blue-500">
                            </div>
                            <div class="ml-3">
                                <label for="allow_individual_to_toggle" class="font-medium text-gray-700">Join / Leave Group</label>
                                <p id="comments-description" class="text-gray-500 text-sm">
                                    Allow users to join and leave this group on their own.
                                    <br/>
                                    Enabling this will show this group to all members as "available to join".
                                </p>
                            </div>
                        </div>
                        <div class="relative flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="is_hidden" aria-describedby="is_hidden-description" name="public_or_private" type="radio" value="private" checked="checked" class="h-4 w-4 rounded-full border-gray-300 text-blue-600 focus:ring-blue-500">
                            </div>
                            <div class="ml-3">
                                <label for="is_hidden" class="font-medium text-gray-700">Private Group</label>
                                <p id="candidates-description" class="text-gray-500 text-sm">
                                    Private groups only show for users that are already in the group.<br/>
                                    Enabling this will override the "join/leave" option, and this group will <strong>not</strong> show up as "available to join".
                                </p>
                            </div>
                        </div>
                    </fieldset>
                </div>

                <div class="divide-y divide-gray-200">
                    <div class="px-4 sm:px-6">
                        <fieldset class="space-y-5">
                            <legend class="sr-only"></legend>
                            <div class="relative flex items-start">
                                <div class="flex h-5 items-center">
                                    <input id="indicates_visitor" aria-describedby="indicates_visitor-description" name="indicates_visitor" value="1" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                </div>
                                <div class="ml-3">
                                    <label for="indicates_visitor" class="font-medium text-gray-700">Indicates visitors?</label>
                                    <p id="visitor-description" class="text-gray-500 text-sm">
                                        Visitors must belong to at least one group that "indicates a visitor" to be able to be added to the Visitor Tracking feature.
                                    </p>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>

                <div class="flex justify-between py-4 mt-2">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs font-medium rounded-md text-white btn-color-and-hover">
                            Create Group
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>
