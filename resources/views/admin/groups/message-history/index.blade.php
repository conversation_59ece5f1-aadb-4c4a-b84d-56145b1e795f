@extends('admin._layouts._app')

@section('title', 'Message History')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.groups.index'),
            'text' => 'Groups',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.groups.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $group->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.groups.partials.group-sidebar')

                        <div class="lg:col-span-9">

                            <div class="divide-y divide-gray-200">
                                <div class="py-6 px-4 sm:p-6">
                                    <h3 class="flex flex-row text-2xl font-medium leading-6 text-gray-900 my-auto">
                                        <x-heroicon-o-archive-box class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                                        <div class="my-auto">Message History</div>
                                    </h3>
                                </div>

                                <table class="w-full divide-y divide-gray-300 border-collapse">
                                    <thead class="bg-white bg-opacity-90 uppercase">
                                    <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                        <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Date</th>
                                        <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Sent By</th>
                                        <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Subject</th>
                                        <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Sent</th>
                                        <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Errors</th>
                                        <th scope="col" class="w-0">&nbsp;</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                    @forelse($messages as $message)
                                        <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }} hover:cursor-pointer" onclick="window.location='{{ route('admin.groups.message-history.view', [$group, $message]) }}'">
                                            <td class="py-3 px-2 pl-4 whitespace-nowrap font-normal text-gray-900">
                                                {{ $message->created_at->setTimezone(auth()->user()->account->timezone)->format('M d, Y') }}
                                                <br/>
                                                <span class="text-sm">{{ $message->created_at->setTimezone(auth()->user()->account->timezone)->format('H:ia') }}</span>
                                            </td>
                                            <td class="py-3 px-2 font-normal text-gray-900">
                                                {{ $message->from_address }}
                                            </td>
                                            <td class="py-3 px-2 font-normal text-gray-900">
                                                {{ \Illuminate\Support\Str::words($message->subject, 3, '...') }}
                                            </td>
                                            <td class="py-3 px-2 whitespace-nowrap font-normal text-gray-900">
                                                {{ $message->history()->count() }}
                                            </td>
                                            <td class="py-3 px-2 whitespace-nowrap font-normal text-gray-900">
                                                {{ $message->history()->join('message_statuses', 'message_history.message_status_id', '=', 'message_statuses.id')->where('message_statuses.indicates_error', true)->count() }}
                                            </td>
                                            <td class="pr-2">
                                                <x-heroicon-o-chevron-right class="w-4 h-4 text-gray-600"/>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="100%" class="text-muted text-center p-5">
                                                <span class="badge badge-warning badge-large">No messages sent yet.</span>
                                            </td>
                                        </tr>
                                    @endforelse
                                    </tbody>
                                </table>

                                <div class="border-t admin-border-color p-4">
                                    {{ $messages->links() }}
                                </div>

                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
