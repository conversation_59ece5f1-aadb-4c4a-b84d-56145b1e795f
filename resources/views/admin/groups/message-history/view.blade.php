@extends('admin._layouts._app')

@section('title', 'Archived Message')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.groups.index'),
            'text' => 'Groups',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.groups.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $group->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.groups.partials.group-sidebar')

                        <div class="lg:col-span-9">

                            <div class="divide-y divide-gray-200">
                                <div class="py-6 px-4 sm:p-6">
                                    <h3 class="flex flex-row text-2xl font-medium leading-6 text-gray-900 my-auto">
                                        <x-heroicon-o-archive-box class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                                        <div class="my-auto">Message History</div>
                                    </h3>
                                </div>

                                <?php
                                $selected_status = null; ?>

                                <div>
                                    <div class="mx-4">
                                        <h3 class="mt-3">Messages sent for: "{{ $message->subject }}"</h3>
                                        <h5>Sent on: {{ $message->created_at->format('M d, Y @ H:ia') }}</h5>
                                    </div>

                                    <div class="grid grid-cols-2 md:grid-cols-6 space-x-4 m-4">
                                        <div class="col col-span-1">
                                            <a href="?" class="">
                                                <div class="rounded-md border-gray-300 border px-3 py-2 bg-white hover:bg-gray-100">
                                                    <p>Total</p>
                                                    <h3 class="text-2xl font-bold">{{ $message->history()->count() }}</h3>
                                                </div>
                                            </a>
                                        </div>
                                        @foreach($message->history()->distinct()->select('message_status_id')->get() as $message_type)
                                            @php
                                                $message_status = \App\Messages\MessageStatus::find($message_type->message_status_id);
                                            @endphp
                                            <div class="col col-span-1">
                                                <a href="?message_status_id={{ $message_status->id }}" class="">
                                                    <div class="rounded-md border-gray-300 border px-3 py-2 bg-white hover:bg-gray-100">
                                                        <p>{{ $message_status->status }}</p>
                                                        <h3 class="text-2xl font-bold">{{ $message->history()->where('message_status_id', $message_type->message_status_id)->count() }}</h3>
                                                    </div>
                                                </a>
                                            </div>
                                            @if(request('message_status_id') == $message_status->id)
                                                    <?php
                                                    $selected_status = $message_status->status; ?>
                                            @endif
                                        @endforeach
                                    </div>

                                    <table class="w-full divide-y divide-gray-300 border-collapse">
                                        <thead class="bg-white bg-opacity-90 uppercase">
                                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                            <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">User</th>
                                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold">Sent To</th>
                                            <th scope="col" class="py-1 px-2 text-xs text-left font-semibold">Last Update</th>
                                            <th scope="col" class="px-2 text-xs text-left font-semibold">Status</th>
                                        </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                        @forelse($messageHistory->sortBy('user.last_name') as $message_h)
                                            <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                                <td class="py-3 px-2 pl-4 whitespace-nowrap text-gray-900">
                                                    @if($message_h->user)
                                                        <a href="{{ route('admin.users.view', $message_h?->user) }}">
                                                            {{ $message_h?->user?->name }}
                                                        </a>
                                                    @endif
                                                </td>
                                                <td class="py-3 px-2 text-gray-900">
                                                    {{ $message_h->sentTo() }}
                                                </td>
                                                <td class="py-1 px-2 text-gray-900">
                                                    {{ $message_h->created_at->setTimezone(auth()->user()->account->timezone)->format('M d, Y') }}
                                                    <br/>
                                                    <span class="text-sm">{{ $message_h->created_at->setTimezone(auth()->user()->account->timezone)->format('H:ia') }}</span>
                                                </td>
                                                <td class="pr-1 whitespace-nowrap text-gray-900">
                                                    <span class="border border-gray-400 rounded-sm bg-gray-50 px-2 py-1 {{ $message_h->getBadgeColor() }}" x-data x-tooltip="{{ $message_h->failed_reason }}">
                                                        {{ $message_h->messageStatus->status }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="100%" class="text-muted text-center p-5">
                                                    <span class="badge badge-warning badge-large">No messages sent yet.</span>
                                                </td>
                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>

                                    <div class="border-t admin-border-color p-4">
                                        {{--                                        {{ $messageHistory->links() }}--}}
                                    </div>

                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>
    <script>
        document.addEventListener('alpine:init', () => {
            // Magic: $tooltip
            Alpine.magic('tooltip', el => message => {
                let instance = tippy(el, {content: message, trigger: 'manual'})

                instance.show()

                setTimeout(() => {
                    instance.hide()

                    setTimeout(() => instance.destroy(), 150)
                }, 2000)
            })

            // Directive: x-tooltip
            Alpine.directive('tooltip', (el, {expression}) => {
                tippy(el, {content: expression, placement: 'left'})
            })
        })
    </script>

@endsection


