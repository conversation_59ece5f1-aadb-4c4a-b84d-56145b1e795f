@extends('admin._layouts._app')

@section('title', 'Groups')

@section('content')

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Groups
                </h1>
            </div>
            <div>
                <button onclick="openSidebar('{{ route('admin.groups.create') }}')" class="admin-button-blue">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Create Group
                </button>
            </div>
        </div>

        <div class="admin-section-border admin-section mt-4">
            @livewire('admin.groups.group-index')
        </div>

    </div>

@endsection
