@extends('admin._layouts._app')

@section('title', 'Messaging Settings')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.groups.index'),
            'text' => 'Groups',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.groups.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $group->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.groups.partials.group-sidebar')

                        <form class="lg:col-span-9" action="{{ route('admin.groups.messaging.save', $group) }}" method="POST">
                            @csrf
                            <div class="divide-y divide-gray-200">
                                <div class="py-6 px-4 sm:p-6">
                                    <h3 class="text-2xl font-medium leading-6 text-gray-900">Messaging Settings</h3>
                                </div>

                                @foreach(\App\Messages\MessageType::active()->get() as $type)
                                    <div class="px-4 sm:px-6 py-6">
                                        <fieldset class="">
                                            <legend class="sr-only"></legend>
                                            <div class="relative flex items-start">
                                                <div class="flex h-5 items-center">
                                                    <input id="message_type_{{ $type->id }}" name="message_types[]" type="checkbox" value="{{ $type->id }}" @isChecked($group->hasMessageType($type->id)) class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                                <div class="ml-3">
                                                    <label for="message_type_{{ $type->id }}" class="font-medium text-gray-700">Enable messaging by {{ $type->name }}</label>
                                                    <div id="message_type_{{ $type->id }}-description" class="text-gray-500 text-sm">
                                                        @if($type->incurs_sending_fee || $type->incurs_monthly_fee)
                                                            <div class="flex-row my-1 space-y-2">
                                                                @if($type->incurs_sending_fee)
                                                                    <div>
                                                                        <span class="px-2 py-0.5 border border-purple-500 bg-purple-50 text-purple-700 rounded-sm">Per Message Cost - ${{ rtrim(Auth::user()->account->plan->{$type->account_plan_sending_fee_field}, '0') * .01 }} / message</span>
                                                                    </div>
                                                                @endif
                                                                @if($type->incurs_monthly_fee)
                                                                    <div>
                                                                        <span class="px-2 py-0.5 border border-purple-500 bg-purple-50 text-purple-700 rounded-sm">Incurs Monthly Cost - ${{ number_format(rtrim(Auth::user()->account->plan->{$type->account_plan_monthly_fee_field}, '0') * .01, 2) }} / month</span>
                                                                    </div>
                                                                @else
                                                                    {{-- <div>--}}
                                                                    {{-- <span class="px-2 py-0.5 border border-gray-500 bg-gray-50 text-gray-700 rounded-sm">No monthly fee</span>--}}
                                                                    {{-- </div>--}}
                                                                @endif
                                                            </div>
                                                        @endif
                                                        @if($type->sending_fee_description)
                                                            <div class="ml-3">{!! $type->sending_fee_description !!}</div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                        @if($type->id == 4)
                                            <fieldset class="">
                                                <legend class="sr-only"></legend>
                                                <div class="relative flex items-start">
                                                    <div class="ml-7">
                                                        <label for="message_type_{{ $type->id }}" class="font-medium text-gray-700">Voice type</label>
                                                        <div id="message_type_{{ $type->id }}-voice-call-voice" class="text-gray-500 text-sm">
                                                            @php
                                                                $voice_call_handler = $group->messageHandlers()->where('message_type_id', 4)->first();
                                                                $current_voice = $voice_call_handler?->voice_call_voice;
                                                            @endphp
                                                            <select name="voice_call_voice">
                                                                @foreach(\App\Messages\MessageHandler::$voice_call_voices as $voice_key => $voice_display)
                                                                    <option value="{{ $voice_key }}" {{ $current_voice == $voice_key ? 'selected' : null }}>{{ $voice_display }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        @endif
                                    </div>
                                @endforeach

                                @if($group->messageHandlers()->where('message_type_id', 1)->exists())
                                    @php($handler = $group->messageHandlers()->where('message_type_id', 1)->first())
                                    <div class="py-6 px-4 sm:p-6">
                                        <div class="flex flex-col lg:flex-row">
                                            <div class="grow space-y-6">
                                                <div>
                                                    <label for="name" class="block text-sm font-medium text-gray-700">Reply E-mail for Group E-mail Messages</label>
                                                    <div class="mt-1 flex rounded-md shadow-xs">
                                                        <input type="text" name="first_email_handler_reply_to_address" id="first_email_handler_reply_to_address" autocomplete="off" value="{{ $handler->reply_to }}" class="block w-full min-w-0 grow rounded-none rounded-r-md border-gray-300 focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                                    </div>
                                                    <p class="mt-2 text-sm text-gray-500">
                                                        ‐ This email is where emails will go when someone replies to a group message.
                                                        <br/>
                                                        ‐ It is recommended not to use free email services as a reply address (Gmail, Yahoo, Hotmail, etc). This can hurt email delivery and cause emails to go to spam.
                                                        <br/>
                                                        ‐ You can leave this blank and not worry about replies.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <div class=" flex justify-start py-4 px-4 sm:px-6">
                                    <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 font-medium text-white shadow-xs hover:bg-blue-800 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Save Changes</button>
                                    <button type="button" class="ml-5 inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Cancel</button>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
