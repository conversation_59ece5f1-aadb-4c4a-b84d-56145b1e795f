@extends('admin._layouts._app')

@section('title', 'Group View')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.groups.index'),
            'text' => 'Groups',
        ], [
            'text' => 'View',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.groups.index') }}">
                    <x-heroicon-s-arrow-left class="w-5 p-0"/>
                </a>
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                {{ $group->name }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.groups.partials.group-sidebar')

                        <form class="lg:col-span-9" action="{{ route('admin.groups.messaging.sms.send', $group) }}" method="post">
                            @csrf
                            <div class="divide-y divide-gray-200">
                                <div class="py-6 px-4 sm:p-6">
                                    <h3 class="flex flex-row text-2xl font-medium leading-6 text-gray-900 my-auto">
                                        <x-heroicon-o-megaphone class="shrink shrink-0 mr-2 h-7 w-7 my-auto"/>
                                        <div class="my-auto">Send SMS</div>
                                    </h3>
                                </div>
                                <div class="py-6 px-4 sm:p-6">
                                    <div class="flex flex-col lg:flex-row">
                                        <div class="grow space-y-6">
                                            <div>
                                                <label for="text-message" class="block text-sm font-medium text-gray-700">Message</label>
                                                <div class="mt-1">
                                                    <textarea id="text-message" name="message" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                                                </div>
                                                <div class="mt-2 text-sm text-gray-500">
                                                    <span id="smsLength">159</span> characters left.
                                                    <div class="mt-1">
                                                        This will be sent as <span id="numberOfTexts">1 text message</span> per user.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="divide-y divide-gray-200">
                                    <div class="flex justify-between py-4 px-4 sm:px-6">
                                        <div>
                                            <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 font-medium text-white shadow-xs hover:bg-blue-800 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Send SMS</button>
                                            <button type="button" class="ml-5 inline-flex justify-center rounded-md border border-gray-300 bg-white py-2 px-4 font-medium text-gray-700 shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Cancel</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

    </div>

    <script src="https://unpkg.com/jquery"></script>
    <script type="text/javascript">
        (function ($) {
            $.fn.smsArea = function (options = {}) {

                var lengthCounterId = options.lengthCounterId ? options.lengthCounterId : 'smsLength';

                var
                    e = this,
                    cutStrLength = 0,

                    s = $.extend({

                        cut: true,
                        maxSmsNum: 1,
                        interval: 50,

                        counters: {
                            message: $('#smsCount'),
                            character: $('#' + lengthCounterId)
                        },

                        lengths: {
                            ascii: [159, 306, 459],
                            unicode: [68, 134, 201]
                        }
                    }, options);


                e.keyup(function () {

                    clearTimeout(this.timeout);
                    this.timeout = setTimeout(function () {

                        var
                            smsType,
                            smsLength = 0,
                            smsCount = -1,
                            charsLeft = 0,
                            text = e.val(),
                            isUnicode = false;

                        for (var charPos = 0; charPos < text.length; charPos++) {
                            switch (text[charPos]) {
                                case "\n":
                                case "[":
                                case "]":
                                case "\\":
                                case "^":
                                case "{":
                                case "}":
                                case "|":
                                case "€":
                                    smsLength += 2;
                                    break;

                                default:
                                    smsLength += 1;
                            }


                            if (text.charCodeAt(charPos) > 127 && text[charPos] != "€") isUnicode = true;
                        }

                        if (isUnicode) {
                            smsType = s.lengths.unicode;

                        } else {
                            smsType = s.lengths.ascii;
                        }

                        for (var sCount = 0; sCount < s.maxSmsNum; sCount++) {

                            cutStrLength = smsType[sCount];
                            if (smsLength <= smsType[sCount]) {

                                smsCount = sCount + 1;
                                charsLeft = smsType[sCount] - smsLength;
                                break
                            }
                        }

                        if (s.cut) e.val(text.substring(0, cutStrLength));
                        smsCount == -1 && (smsCount = s.maxSmsNum, charsLeft = 0);

                        s.counters.message.html(smsCount);
                        s.counters.character.html(charsLeft);

                        if ($('#text-message').val().length > 0 && $('#text-message-2').val().length > 0) {
                            $('#numberOfTexts').html('<strong>2 text messages</strong>');
                        } else {
                            $('#numberOfTexts').html('1 text message');
                        }
                    }, s.interval);

                }).keyup()
            }
        }(jQuery));

        $("#text-message").smsArea();
        $("#text-message-2").smsArea({
            lengthCounterId: 'smsLength2'
        });


    </script>

@endsection
