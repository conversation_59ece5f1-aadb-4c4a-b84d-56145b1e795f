@extends('admin._layouts._app')

@section('title', 'School Grades')

@section('content')

    <div class="admin-standard-col-width">


        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.accounts.grades.create'),
            'text' => 'School Grades',
        ], [
            'text' => 'create grade',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex justify-start">
                            <a href="{{ url()->previous() }}" class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200">
                                <x-heroicon-s-arrow-left class="w-5 p-0"/>
                            </a>
                            <h1>
                                School Grades
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-[theme(screens.xm)]">
                <div class="overflow-hidden rounded-lg bg-white ">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-7 lg:divide-y-0 lg:divide-x">


                        @php
                            $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                            $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                        @endphp

                        <div class="divide-y divide-gray-200 lg:col-span-7 mt">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-0">
                                <div class="row">
                                    <div class="col-md-12 ">
                                        <h1>Create New Grade</h1>
                                    </div>
                                </div>

                                <hr>

                                <div class="row pt-2">
                                    <div class="col-md-7 ">

                                        <div class="container-fluid">
                                            <div class="row">

                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">

                                                    <form action="{{ route('admin.accounts.grades.store') }}" method="post">
                                                        {{ csrf_field() }}

                                                        <div class="form-group">
                                                            <label for="name">Grade Name</label>
                                                            <input type="text" name="name" class="form-control{{ $errors->has('name') ? ' is-invalid' : '' }}" value="{{ old('name') }}">
                                                            @if ($errors->has('name'))
                                                                <span class="invalid-feedback">
                                                            <strong>{{ $errors->first('name') }}</strong>
                                                        </span>
                                                            @endif
                                                        </div>

                                                        <div class="form-group">
                                                            <label for="sort_id">Grade Sort Order</label>
                                                            <input type="text" name="sort_id" class="form-control{{ $errors->has('sort_id') ? ' is-invalid' : '' }}" value="{{ old('sort_id') }}">
                                                            @if ($errors->has('sort_id'))
                                                                <span class="invalid-feedback">
                                                            <strong>{{ $errors->first('sort_id') }}</strong>
                                                        </span>
                                                            @endif
                                                            <small class="text-muted">A number indicating what order this grade should be listed with other grades. e.g. 5</small>
                                                        </div>

                                                        <div class="form-group">
                                                            <label for="description">Description</label>
                                                            <textarea name="description" rows="3" class="form-control{{ $errors->has('description') ? ' is-invalid' : '' }}">{{ old('description') }}</textarea>
                                                            @if ($errors->has('description'))
                                                                <span class="invalid-feedback">
                                                            <strong>{{ $errors->first('description') }}</strong>
                                                        </span>
                                                            @endif
                                                        </div>

                                                        <div class="form-group">
                                                            <button type="submit" class="admin-button-blue mt-4">
                                                                <i class="fa fa-check pr-2" aria-hidden="true"></i> Create Grade
                                                            </button>
                                                        </div>

                                                    </form>

                                                </div>
                                            </div>
                                        </div>

@endsection
