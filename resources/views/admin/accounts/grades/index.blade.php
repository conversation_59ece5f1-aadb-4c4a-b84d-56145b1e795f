@extends('admin._layouts._app')

@section('title', 'School Grades')

@section('content')

    <div class="admin-standard-col-width">
        <div class="md:flex md:items-start md:justify-between mb-4">
            <h1>
                School Grades
            </h1>
            <a href="{{ route('admin.accounts.grades.create') }}" class="admin-button-blue">
                <i class="fa fa-plus mr-2" aria-hidden="true"></i> Add School Grade
            </a>
        </div>

        <main class="admin-section bg-white">

            <div class="grid grid-cols-2 space-x-8">
                <div class="p-4">
                    <h2 class="text-2xl font-medium">List of Grades</h2>
                    <table class="table-auto border border-gray-300 w-full" cellpadding="6">
                        <thead class="border-b border-gray-300 bg-gray-800 text-white">
                        <tr>
                            <th>Name</th>
                            <th class="text-center">People</th>
                            <th class="text-center">Sort</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody class="divide-y">
                        @foreach ($grades as $grade)
                            <tr>
                                <td class="text-center">
                                    <a href="{{ route('admin.accounts.grades.edit', $grade) }}">{{ $grade->name }}</a>
                                </td>
                                <td class="text-center">{{ optional($grade)->users_count }}</td>
                                <td class="text-center">{{ $grade->sort_id }}</td>

                                <td class="text-right">
                                    <a href="{{ route('admin.accounts.grades.edit', $grade) }}" class="admin-button-transparent-small"><i class="fa fa-edit" aria-hidden="true"></i> Edit</a>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="p-4">
                    <h2 class="text-2xl font-medium">Promote / Demote Grades</h2>
                    <div class="my-4 p-4 bg-blue-50 border border-blue-300 text-blue-800 rounded-sm text-sm">
                        When <strong>promoting</strong> up, the <strong>last</strong> grade will have
                        <strong>{{ optional($last_grade)->users_count }}</strong> {{ \Illuminate\Support\Str::plural('user', optional($last_grade)->users_count) }} grade <strong style="color: tomato"> irrevocably</strong> removed.
                        <br>
                        When <strong>demoting</strong> down, the <strong>first</strong> grade will have
                        <strong>{{ optional($first_grade)->users_count }}</strong> {{ \Illuminate\Support\Str::plural('user', optional($first_grade)->users_count) }} grade <strong style="color: tomato"> irrevocably</strong> removed.
                    </div>
                    <p class="text-left">Select which grades to promote/demote students.</p>
                    <form action="{{ route('admin.accounts.grades.promote') }}" method="post">
                        @csrf
                        @foreach ($grades as $grade)
                            <div class="custom-control custom-switch mt-1 pl-4 ">

                                <input type="checkbox" name="grades[]" value="{{ $grade->id }}" class="custom-control-input" id="custom_switch_{{ $grade->id }}" checked>
                                <label class="custom-control-label" for="custom_switch_{{ $grade->id }}">{{ $grade->name }}</label>
                            </div>
                        @endforeach
                        <div class="my-4 flex flex-row space-x-4">
                            <select class="text-sm" name="promotion_action">
                                <option class="text-sm" value="promote">Promote</option>
                                <option class="text-sm" value="demote">Demote</option>
                            </select>
                            <button type="submit" class="admin-button-blue"><i class="fa fa-check pr-1"></i> Submit</button>
                        </div>

                        <p class="float-right mb-2">

                        </p>


                    </form>
                </div>
            </div>
        </main>

    </div>

@endsection