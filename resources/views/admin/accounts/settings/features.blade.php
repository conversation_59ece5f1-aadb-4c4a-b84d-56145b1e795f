@extends('admin._layouts._app')

@section('title', 'Account - Features')

@section('content')

    <div class="admin-standard-col-width">

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1">
                    <div class="flex">
                        <div class="flex-1 justify-between">
                            <h1>
                                Account Settings
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin/accounts/settings/sidebar/settings-sidebar')

                        @php
                            $enabled_option = 'text-sm text-gray-800 bg-green-50 border border-green-600 overflow-hidden rounded-lg font-normal px-2 py-1';
                            $disabled_option = 'text-sm text-gray-400 bg-gray-50 border border-gray-400 overflow-hidden rounded-lg font-normal px-2 py-1';
                        @endphp

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8">

                                <div class="col">

                                    @method('post')
                                    @csrf
                                    <h2 class="text-3xl mb-2 font-medium">Features</h2>
                                    <div class="mb-4 bg-purple-50 border border-purple-600 overflow-hidden rounded-lg font-normal px-2 py-1">
                                        Disabled Temporarily!
                                        Reach out to support for help changing these settings.
                                    </div>
                                    <table class="table">
                                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                            <th>Feature</th>
                                            <th>Enable for Member</th>
                                            <th>Enable for Admin</th>
                                        </tr>
                                        @forelse($settings as $setting)
                                            @php
                                                $account_setting_value = \App\Accounts\AccountSettingValue::where('account_id', auth()->user()->account->id)->where('account_setting_id', $setting->id)->first();
                                            @endphp
                                            <tr>
                                                <td>
                                                    <div class="custom-control custom-switch overflow-hidden rounded-lg bg-white px-5 py-4 ">
                                                        <input type="checkbox" name="enable_for_account[]"
                                                               value="{{ $setting->id ?? null }}"
                                                               class="custom-control-input"
                                                               id="account_custom_switch_{{ $account_setting_value->id ?? null }}"
                                                                {{ $account_setting_value ? ($account_setting_value->value ? 'checked' : null) : null }}
                                                                {!! $setting->allow_account_to_enable ? '' : 'disabled="disabled"' !!}>
                                                        <label class="custom-control-label" for="account_custom_switch_{{ $account_setting_value->id ?? null }}">{{ $setting->name }}</label>
                                                        @if($setting->description)
                                                            <br>
                                                            <small class="text-muted">{{ $setting->description }}</small>
                                                        @endif
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="custom-control custom-switch overflow-hidden rounded-lg bg-white px-5 py-4 " style="text-align: center">
                                                        <input type="checkbox" name="enable_for_member[]"
                                                               value="{{ $setting->id ?? null }}"
                                                               class="custom-control-input"
                                                               id="member_custom_switch_{{ $setting->id }}"
                                                                {{ $account_setting_value ? ($account_setting_value->enable_for_member ? 'checked' : null) : null }}
                                                                {!! $setting->allow_account_to_select_permissions ? '' : 'disabled="disabled"' !!}>
                                                        <label class="custom-control-label" for="member_custom_switch_{{ $setting->id }}">&nbsp;</label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="custom-control custom-switch overflow-hidden rounded-lg bg-white px-5 py-4 " style="text-align: center">
                                                        <input type="checkbox" name="enable_for_admin[]"
                                                               value="{{ $setting->id ?? null }}"
                                                               class="custom-control-input"
                                                               id="admin_custom_switch_{{ $setting->id }}"
                                                                {{ $account_setting_value ? ($account_setting_value->enable_for_admin ? 'checked' : null) : null }}
                                                                {!! $setting->allow_account_to_select_permissions ? '' : 'disabled="disabled"' !!}>
                                                        <label class="custom-control-label" for="admin_custom_switch_{{ $setting->id }}">&nbsp;</label>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <div class="col-md-12 mt-4 mb-5 text-center">
                                                <span class="badge badge-warning badge-large">No Settings Exist</span>
                                            </div>
                                        @endforelse
                                    </table>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        </main>

    </div>




    {{--
        <div class="row">
            <div class="col-md-12">
                <h1>Account Settings</h1>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="nav nav-tabs justify-content-lg-center justify-content-sm-end" id="nav-tab" role="tablist">
                    <a class="nav-item nav-link active" href="{{ route('admin.accounts.settings.index') }}" role="tab" aria-controls="nav-general" aria-selected="true"><i class="fal fa-info-circle"></i>&nbsp; General</a>
                    <a class="nav-item nav-link" href="{{ route('admin.accounts.billing.index') }}" role="tab" aria-controls="nav-billing" aria-selected="false"><i class="fal fa-file-invoice"></i>&nbsp; Billing</a>
                    <a class="nav-item nav-link" href="{{ route('admin.accounts.features.index') }}" role="tab" aria-controls="nav-features" aria-selected="false"><i class="fal fa-cog"></i>&nbsp; Features</a>
                </div>
            </div>
        </div>
        --}}

@endsection










