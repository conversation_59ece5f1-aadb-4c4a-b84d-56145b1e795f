@extends('admin._layouts._app')

@section('title', 'Congregation Information')

@section('content')

    @php
        $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
        $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
    @endphp

    <div class="admin-standard-col-width">

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <h1>
                    Account Settings
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin/accounts/settings/sidebar/settings-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-1">

                                <h2 class="mb-6">Congregation Information</h2>

                                <form action="{{ route('admin.accounts.settings.congregation-information.save') }}"
                                      method="post">
                                    @csrf()

                                    <button type="submit"
                                            name="Save Changes"
                                            class="admin-button-blue mb-6"
                                    >
                                        Save Changes
                                    </button>

                                    <div class="mb-6">
                                        <h3>Church Website</h3>

                                        <div class="max-w-md ml-6 space-y-2">
                                            <div class="">
                                                <input value="{{ $account->church_website }}" type="text" name="church_website" placeholder="https://" class="{{ $inputCSS }}" autocomplete="off"/>
                                                <div class="text-sm text-gray-400">Include "https://"</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-6">
                                        <h3>Physical Address</h3>

                                        <div class="max-w-md ml-6 space-y-2">
                                            <div class="">
                                                <div>
                                                    <label for="address1" class="block text-sm font-medium text-gray-900">
                                                        Address
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <input value="{{ $account->address1 }}" type="text" name="address1" placeholder="Address" class="{{ $inputCSS }}" autocomplete="off"/>
                                                    <input value="{{ $account->address2 }}" type="text" name="address2" placeholder="Apt #, PO box, etc." class="{{ $inputCSS }} mt-1" autocomplete="off"/>
                                                    <input value="{{ $account->address3 }}" type="text" name="address3" placeholder="C/O: " class="{{ $inputCSS }} mt-1" autocomplete="off"/>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="city" class="block text-sm font-medium text-gray-900">
                                                        City
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <input value="{{ $account->city }}" type="text" name="city" placeholder="City" class="{{ $inputCSS }}" autocomplete="off"/>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="state" class="block text-sm font-medium text-gray-900">
                                                        State
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <div class="w-1/2"><input value="{{ $account->state }}" type="text" name="state" placeholder="State" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="postal_code" class="block text-sm font-medium text-gray-900">
                                                        Zip Code
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <div class="w-1/2"><input value="{{ $account->postal_code }}" type="text" name="postal_code" placeholder="Zip" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-6">
                                        <h3>Mailing Address</h3>

                                        <div class="max-w-md ml-6 space-y-2">
                                            <div class="">
                                                <div>
                                                    <label for="mailing_address1" class="block text-sm font-medium text-gray-900">
                                                        Address
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <input value="{{ $account->mailing_address1 }}" type="text" name="mailing_address1" placeholder="Address" class="{{ $inputCSS }}" autocomplete="off"/>
                                                    <input value="{{ $account->mailing_address2 }}" type="text" name="mailing_address2" placeholder="Apt #, PO box, etc." class="{{ $inputCSS }} mt-1" autocomplete="off"/>
                                                    <input value="{{ $account->mailing_address3 }}" type="text" name="mailing_address3" placeholder="C/O: " class="{{ $inputCSS }} mt-1" autocomplete="off"/>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="mailing_city" class="block text-sm font-medium text-gray-900">
                                                        City
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <input value="{{ $account->mailing_city }}" type="text" name="mailing_city" placeholder="City" class="{{ $inputCSS }}" autocomplete="off"/>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="mailing_state" class="block text-sm font-medium text-gray-900">
                                                        State
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <div class="w-1/2"><input value="{{ $account->mailing_state }}" type="text" name="mailing_state" placeholder="State" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="mailing_postal_code" class="block text-sm font-medium text-gray-900">
                                                        Zip Code
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <div class="w-1/2"><input value="{{ $account->mailing_postal_code }}" type="text" name="mailing_postal_code" placeholder="Zip" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-6">
                                        <h3>Phones</h3>

                                        <div class="max-w-md ml-6 space-y-2">
                                            <div class="">
                                                <div>
                                                    <label for="phone_work" class="block text-sm font-medium text-gray-900">
                                                        Office Phone
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <input value="{{ $account->phone_work }}" type="text" name="phone_work" placeholder="************" class="{{ $inputCSS }}" autocomplete="off"/>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="phone_fax" class="block text-sm font-medium text-gray-900">
                                                        Office Fax
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <div class=""><input value="{{ $account->phone_fax }}" type="text" name="phone_fax" placeholder="************" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-6">
                                        <h3>Emails</h3>

                                        <div class="max-w-md ml-6 space-y-2">
                                            <div class="">
                                                <div>
                                                    <label for="congregation_contact_email" class="block text-sm font-medium text-gray-900">
                                                        Contact Email for Congregation
                                                        <br>
                                                        <span class="font-light">(Viewable only to church members)</span>
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <input value="{{ $account->congregation_contact_email }}" type="text" name="congregation_contact_email" placeholder="<EMAIL>" class="{{ $inputCSS }}" autocomplete="off"/>
                                                </div>
                                            </div>
                                            <div class="">
                                                <div>
                                                    <label for="public_contact_email" class="block text-sm font-medium text-gray-900">
                                                        Contact Email for General Public
                                                        <br>
                                                        <span class="font-light">(Used for things like website contact form)</span>
                                                    </label>
                                                </div>
                                                <div class="">
                                                    <div class="">
                                                        <input value="{{ $account->public_contact_email }}" type="text" name="public_contact_email" placeholder="<EMAIL>" class="{{ $inputCSS }} mt-1" autocomplete="off"/></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <button type="submit"
                                            name="Save Changes"
                                            class="admin-button-blue mb-6"
                                    >
                                        Save Changes
                                    </button>

                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection