@extends('admin._layouts._app')

@section('title', 'Account - Welcome Emails')

@section('content')

    <div class="admin-standard-col-width">

        <h1>
            Account Settings
        </h1>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin/accounts/settings/sidebar/settings-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-9 p-4">

                            <form action="{{ route('admin.accounts.settings.welcome-emails.submit') }}" method="post" id="welcomeEmailPageForm">
                                @csrf
                                @method('post')

                                <div class="flex justify-between pb-3">
                                    <h2 class="text-3xl text-black font-medium">Welcome Emails</h2>

                                    <button class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Sending... please wait.'; document.getElementById('welcomeEmailPageForm').submit();">
                                        <i class="fas fa-paper-plane"></i> &nbsp;Send Emails
                                    </button>
                                </div>

                                <div id="checkbox_container">

                                    <div class="text-base">
                                        <p>List of members available to send a "Welcome" email to, which will include a link to create a password.
                                            <br>
                                           (Users that already have a password set are unchecked by default)
                                            <br>
                                    </div>

                                    <div class="pb-4 pt-4">
                                        <input type="checkbox" class="custom-control-input" id="customSwitch_check_uncheck" name="" id="checkuncheck" value="" onclick="for(c in document.getElementsByName('user_ids[]')) document.getElementsByName('user_ids[]').item(c).checked = this.checked"/>
                                        <label class="custom-control-label" for="customSwitch_check_uncheck">Check / Uncheck All</label>
                                    </div>

                                    <table class="min-w-full divide-y divide-gray-300 border border-gray-200 border-collapse">
                                        <thead>
                                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                            <th class="w-12">Send</th>
                                            <th class="text-left pl-2">Name</th>
                                        </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                        @forelse($users as $user)
                                            <tr>
                                                <td>
                                                    <div class="text-center overflow-hidden rounded-lg bg-white border border-white">
                                                        <input type="checkbox" class="custom-control-input" id="customSwitch_{{ $user->id }}" name="user_ids[]" value="{{ $user->id }}" @isChecked(!$user->password)/>
                                                        <label class="custom-control-label" for="customSwitch_{{ $user->id }}"></label>
                                                    </div>
                                                </td>
                                                <td>
                                                    <a class="pl-2 overflow-hidden rounded-lg bg-white" href="{{ route('admin.users.view', $user->id) }}">{{ $user->last_name }}, {{ $user->display_first_name }}</a>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="100%" class="text-center p-5">
                                                    <span class="badge badge-warning badge-large">No results found.</span>
                                                </td>
                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>

                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection














