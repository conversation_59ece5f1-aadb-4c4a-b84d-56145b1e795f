@extends('admin._layouts._app')

@section('title', 'Church Offices - Account Settings')

@section('content')

    <div class="admin-standard-col-width">

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <h1>
                    Account Settings
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin/accounts/settings/sidebar/settings-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">

                                <div>
                                    <a href="{{ route('admin.accounts.settings.church-offices.index') }}" class="admin-button-transparent">Back</a>
                                </div>
                                <div class="flex flex-row justify-between">
                                    <h2 class="text-3xl text-black font-medium">{{ $church_office->plural_name }}</h2>
                                    <hr>
                                </div>


                                <script src=""></script>

                                @livewire('admin.accounts.settings.church-office-users', ['church_office' => $church_office])

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection