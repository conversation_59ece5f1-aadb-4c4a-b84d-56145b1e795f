@extends('admin._layouts._app')

@section('title', 'Account Settings')

@section('content')

    <div class="admin-standard-col-width">

        <div class="flex-1 mb-4">
            <h1>
                Account Settings
            </h1>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin/accounts/settings/sidebar/settings-sidebar')

                        <div class="lg:col-span-9 p-4">

                            <script src="https://js.stripe.com/v3/"></script>

                            <style>
                                .StripeElement {
                                    box-sizing: border-box;

                                    height: 40px;

                                    padding: 10px 12px;

                                    border: 1px solid transparent;
                                    border-radius: 4px;
                                    background-color: white;

                                    box-shadow: 0 1px 3px 0 #e6ebf1;
                                    -webkit-transition: box-shadow 150ms ease;
                                    transition: box-shadow 150ms ease;
                                }

                                .StripeElement--focus {
                                    box-shadow: 0 1px 3px 0 #cfd7df;
                                }

                                .StripeElement--invalid {
                                    border-color: #fa755a;
                                }

                                .StripeElement--webkit-autofill {
                                    background-color: #fefde5 !important;
                                }
                            </style>

                            @if(!auth()->user()->account->use_v2_billing)
                                <strong>Current Plan: </strong> {{ auth()->user()->account?->plan->name }} @ {{ @\Brick\Money\Money::ofMinor(auth()->user()->account?->plan->price_per_month, 'USD')->formatTo('en_US') }} / month
                            @endif

                            <?php
                            $intent = \Stripe\SetupIntent::create(); ?>
                                    <!-- placeholder for Elements -->
                            <form class="max-w-md" id="payment-form" method="post" data-secret="{!! $intent->client_secret !!}" action="{{ route('admin.accounts.settings.credit-card.submit') }}">
                                @csrf
                                @method('post')
                                @if($payment_method)
                                    <div class="bg-green-100 border border-green-300 p-4 rounded-sm" role="alert">
                                        <strong>Current Method:</strong>
                                        <mark>{{ \Illuminate\Support\Str::ucfirst($payment_method->card->brand) }}</mark>
                                        ending in <mark>{{ $payment_method->card->last4 }}</mark>;
                                        expires on <mark>{{ $payment_method->card->exp_month }}/{{ $payment_method->card->exp_year }}</mark>.
                                    </div>
                                @else
                                    <div class="flex bg-amber-100 border border-amber-300 p-4 rounded-sm" role="alert">
                                        No credit card on file.
                                    </div>
                                @endif

                                <div id="card-element" style="border: 1px solid #777" class="border border-gray-700 my-4"></div>
                                <div class="mt-1" id="card-errors"></div>

                                <button id="card-button" class="admin-button-blue">
                                    @if($payment_method)
                                        <i class="fas fa-credit-card"></i> &nbsp;Update Card
                                    @else
                                        <i class="fas fa-credit-card"></i> &nbsp;Save Card
                                    @endif
                                </button>
                            </form>

                            <form id="bank-form" method="post" data-secret="" action="" class="max-w-md my-6">
                                @csrf
                                @method('post')
                                @if($bank_method)
                                    <div class="bg-green-100 border border-green-300 p-4 rounded-sm" role="alert">
                                        <strong>Current Bank Account:</strong>
                                        <mark>{{ \Illuminate\Support\Str::ucfirst($bank_method->us_bank_account?->bank_name) }}</mark> ending in <mark>{{ $bank_method->us_bank_account?->last4 }}</mark>
                                    </div>
                                @else
                                    <div class="flex bg-amber-100 border border-amber-300 p-4 rounded-sm" role="alert">
                                        No bank account on file.
                                    </div>
                                @endif
                                To add a bank account, please contact Lightpost support.
                            </form>

                            <h2 class="text-3xl font-medium mt-4 mb-2">Invoices</h2>

                            <table class="table-auto border border-gray-300" cellpadding="6">
                                <thead>
                                <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                    <th>Date</th>
                                    <th>Total</th>
                                    <th></th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($invoices as $invoice)
                                    <tr class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <td class="px-4">{{ $invoice->created_at->format('M d, Y') }}</td>
                                        <td class="px-4">
                                            ${{ $invoice->formatAmount($invoice->amount_total) }}
                                        </td>
                                        <td class="px-4">
                                            <a href="{{ route('admin.accounts.settings.invoice.pdf', $invoice) }}" class="admin-button-transparent-small">PDF</a>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>

                            <script type="application/javascript">
                                // Create a Stripe client.
                                var stripe = Stripe('{{ config('services.stripe.key') }}');

                                // Create an instance of Elements.
                                var elements = stripe.elements();

                                // Custom styling can be passed to options when creating an Element.
                                var style = {
                                    base: {
                                        color: '#32325d',
                                        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                                        fontSmoothing: 'antialiased',
                                        fontSize: '16px',
                                        '::placeholder': {
                                            color: '#aab7c4'
                                        }
                                    },
                                    invalid: {
                                        color: '#fa755a',
                                        iconColor: '#fa755a'
                                    }
                                };

                                // Create an instance of the card Element.
                                var card = elements.create('card', {style: style});

                                // Add an instance of the card Element into the `card-element` <div>.
                                card.mount('#card-element');

                                // Handle real-time validation errors from the card Element.
                                card.addEventListener('change', function (event) {
                                    var displayError = document.getElementById('card-errors');
                                    if (event.error) {
                                        displayError.innerHTML = event.error.message;
                                    } else {
                                        displayError.innerHTML = '';
                                    }
                                });

                                // Handle form submission.
                                var form = document.getElementById('payment-form');
                                form.addEventListener('submit', async function (event) {
                                    event.preventDefault();

                                    // Disable button to prevent double submission
                                    document.getElementById('card-button').setAttribute("disabled", "disabled");

                                    const {error, paymentMethod} = await stripe.createPaymentMethod({
                                        type: 'card',
                                        card: card,
                                    });

                                    if (error) {
                                        // Re-enable button if there's an error
                                        document.getElementById('card-button').removeAttribute("disabled");

                                        // Inform the user if there was an error.
                                        var errorElement = document.getElementById('card-errors');
                                        errorElement.innerHTML = error.message;
                                    } else {
                                        // Send paymentMethod.id (pm_xxx) to your server.
                                        stripePaymentMethodHandler(paymentMethod.id);
                                    }
                                });

                                // Submit the form with the payment method ID.
                                function stripePaymentMethodHandler(paymentMethodId) {
                                    var form = document.getElementById('payment-form');
                                    var hiddenInput = document.createElement('input');
                                    hiddenInput.setAttribute('type', 'hidden');
                                    hiddenInput.setAttribute('name', 'stripePaymentMethod');
                                    hiddenInput.setAttribute('value', paymentMethodId);
                                    form.appendChild(hiddenInput);

                                    // Submit the form
                                    form.submit();
                                }
                            </script>

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection


