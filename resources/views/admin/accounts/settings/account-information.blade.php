@extends('admin._layouts._app')

@section('title', 'Congregation Information')

@section('content')

    @php
        $inputCSS = 'block shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
        $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
    @endphp

    <div class="admin-standard-col-width">

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <h1>
                    Account Settings
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin/accounts/settings/sidebar/settings-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-1">

                                <h2 class="mb-6">Account Information</h2>

                                <form action="{{ route('admin.accounts.settings.account-information.save') }}"
                                      method="post">
                                    @csrf()

                                    <div class="mb-6">
                                        <h4>Timezone</h4>

                                        <select name="timezone" class="{{ $inputCSS }}">
                                            @foreach(DateTimeZone::listIdentifiers(DateTimeZone::PER_COUNTRY, 'US') as $timezone)
                                                @php
                                                    $tz = new DateTimeZone($timezone);
                                                    $offset = $tz->getOffset(new DateTime('now', $tz));
                                                    $hours = $offset / 3600;
                                                    $minutes = abs(($offset / 60) % 60);
                                                    $offsetString = sprintf("%d:%02d", $hours, $minutes);
                                                @endphp
                                                <option value="{{ $timezone }}" {{ $account->timezone == $timezone ? 'selected="selected"' : null }}>{{ $timezone . ' (UTC' . ($hours >= 0 ? '+' : '') . $offsetString . ')' }}</option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <button type="submit"
                                            name="Save Changes"
                                            class="admin-button-blue mb-6"
                                    >
                                        Save Changes
                                    </button>

                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection