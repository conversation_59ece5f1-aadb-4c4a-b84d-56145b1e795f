@extends('admin._layouts._app')

@section('title', 'Account Settings')

@section('content')

    <div class="admin-standard-col-width">

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <h1>
                    Account Settings
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin/accounts/settings/sidebar/settings-sidebar')

                        <div class="divide-y divide-gray-200 lg:col-span-9">
                            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">
                                <div class="grid grid-col-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <dt class="truncate text-sm font-medium text-gray-500">Members Accounts</dt>
                                        <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                            {{ \App\Users\User::visibleTo(Auth::user())->membersOnly()->count() }}
                                            {{ auth()->user()->account->use_v2_billing ? '' : ' / ' . Auth::user()->account->plan->max_users }}
                                        </dd>
                                    </div>
                                    <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <dt class="truncate text-sm font-medium text-gray-500">Storage</dt>
                                        <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                            {{ round($total_storage_used / **********, 2) }} GB
                                        </dd>
                                    </div>

                                    <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <dt class="truncate text-sm font-medium text-gray-500">Emails this month</dt>
                                        <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                            {{ number_format($this_month_emails, 0) }}
                                        </dd>
                                    </div>
                                    <div class="overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                                        <dt class="truncate text-sm font-medium text-gray-500">SMS this month</dt>
                                        <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                                            {{ number_format($this_month_sms, 0) }}
                                        </dd>
                                    </div>
                                </div>

                                <div class="pt-4">
                                    <h2 class="text-3xl text-black font-medium">Support</h2>
                                </div>

                                <div class="pb-4 text-base">
                                    <strong>Questions? Feedback? Help?</strong>
                                    <br><br>
                                    E-mail Lightpost support:
                                    <br>
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection