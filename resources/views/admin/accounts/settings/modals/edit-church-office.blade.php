@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.accounts.settings.church-offices.edit.submit', $church_office) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i>Edit Church Office
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <label for="label" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Name
                            </label>
                        </div>
                        <div class="">
                            <input type="text" value="{{ $church_office->name }}" name="name" placeholder="Deacon" class="{{ $inputCSS }}" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <label for="label" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Plural Name
                            </label>
                        </div>
                        <div class="">
                            <input type="text" value="{{ $church_office->plural_name }}" name="plural_name" placeholder="Deacons" class="{{ $inputCSS }}" autocomplete="off"/>
                        </div>
                        <div class="text-sm text-gray-500">
                            Provide the plural form of this office name. This is used for list titles. i.e. "Deacons"
                        </div>
                    </div>
                </div>
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1">
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Description
                            </label>
                        </div>
                        <div class="">
                            <input type="text" value="{{ $church_office->description }}" name="description" placeholder="" class="{{ $inputCSS }}" autocomplete="off"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-0">
                        <div class="my-2">
                            <input type="checkbox" name="is_public" id="is_public" value="1" @isChecked($church_office->is_public)/>
                            <label for="is_public"><span>Is Public?</span></label>
                            <div class="mt-2 text-sm text-gray-600">
                                Public offices will show up on Lightpost Websites and other places where the general public can see them.
                                <br>
                                Offices are always visible to members.
                            </div>
                        </div>
                        <div class="my-2">
                            <input type="checkbox" name="show_in_leadership" id="show_in_leadership" value="1" @isChecked($church_office->show_in_leadership)/>
                            <label for="show_in_leadership"><span>Show in Leadership section?</span></label>
                            <div class="mt-2 text-sm text-gray-600">
                                The "Leadership" section shows in the member app under a section titled "Leadership".
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-base font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>
                    </div>
                    <div>
                        <button type="button" onclick="document.getElementById('delete_form').submit()"
                                class="admin-button-red-transparent">
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>

<form action="{{ route('admin.accounts.settings.church-offices.delete.submit', $church_office) }}" method="post" id="delete_form">
    @csrf
    @method('delete')
</form>