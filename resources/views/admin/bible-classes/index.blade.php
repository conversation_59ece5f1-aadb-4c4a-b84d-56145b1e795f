@extends('admin._layouts._app')

@section('title', 'Bible Classes')

@section('content')

    <div class="admin-standard-col-width">

        <div class="mb-4 md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Bible Class Groups
                </h1>
            </div>
            <div>
                <button
                        onclick="openSidebar('{{ route('admin.bible-classes.groups.create') }}')"
                        type="button"
                        class="admin-button-blue"
                        data-toggle="modal"
                        data-target="#large-modal"
                        data-source="{{ route('admin.bible-classes.groups.create') }}"
                        rel="tooltip"
                        data-placement="top"
                        title="Create Bible Class Grouping">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i> New Class Group
                </button>
            </div>
        </div>


        <div class="py-6 space-y-4">
            @forelse ($bibleClassGroups as $group)
                <div class="row">
                    <div class="col-span-12">
                        <div class="card overflow-hidden rounded-lg bg-white px-5 py-4 border border-gray-300">
                            <div class="float-right text-right">
                                @if($group->is_signup_active)
                                    <div class="inline-block px-4 py-2 text-sm font-bold text-white bg-emerald-500 rounded-lg"><i class="fa fa-check" aria-hidden="true"></i> Sign-up Active</div>
                                @else
                                    <div class="inline-block px-4 py-2 text-sm font-bold text-gray-700 bg-gray-200 rounded-lg">Sign-up Disabled</div>
                                @endif
                                <div class="mt-2">
                                    {{ $group->start_date->format('M d, Y') }}
                                    @if($group->end_date)
                                        through {{ $group->end_date->format('M d, Y') }}
                                        <br>
                                        <small class="text-gray-400">({{ $group->end_date->diff($group->start_date)->format("%a") }} days)</small>
                                    @endif
                                </div>
                            </div>
                            <a class="text-2xl font-medium" href="{{ route('admin.bible-classes.groups.view', $group) }}">
                                {{ $group->name }}
                            </a>

                            <h5>{{ $group->classes_count }}
                                <span>Class{{ $group->classes_count == 1 ? null : 'es' }}</span>
                            </h5>
                            <h5>{{ $group->registrationCount() }}
                                <span>Registration{{ $group->registrationCount() == 1 ? null : 's' }}</span>
                            </h5>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col text-center">
                    <br>
                    <span class="inline-block px-4 py-2 text-lg font-bold text-white bg-yellow-500 rounded-lg">No Class Groups created yet.</span>
                    <br><br>
                    <a data-toggle="modal"
                       data-target="#large-modal"
                       data-source="<?= route('admin.bible-classes.groups.create'); ?>"
                       href="#"
                    >Create one now.</a>
                    <br>
                    <br>
                    <br>
                </div>
            @endforelse

            {{ $bibleClassGroups->links() }}
        </div>

    </div>

@endsection
