<?php
foreach ($classes as $class):

// First create our header, which we might use again
    $header = '<div style="font-weight: bold; font-size: 140%; width: 100%; text-align: center;">
    ' . $class->title . '<br><span style="font-size: 88%;">Registration List<br>' . strtoupper($class->dayOfWeek()) . ' - ' . $group->name . '</span></div>
<div style="width: 100%;">
    <table style="border: 1px solid #333; border-spacing: 3px; width: 100%; font-size: 88%;">';

    $users        = $class->registrations()->orderByRaw('last_name asc, family_id, first_name asc')->get();
    $max_per_page = count($users) > 152 ? 152 : count($users);

    // First add our header to the output
    $i_html .= $header;


    $j      = 0;
    $t      = 0;
    $i_html .= '<tr><td style="width:33%;" valign="top">';

    foreach ($users as $user):

        $i_html .= '&nbsp; ' . $user->last_name . ', ' . $user->display_first_name . '<br>';

        if ($t == floor($max_per_page)) {
            $i_html .= '</td></tr>';
            // Add a page break at the end of this class
            $i_html .= '</table></div><div style="page-break-after:always;"></div>';
            $i_html .= '<div style="width: 100%;"><table style="border: 1px solid #333; border-spacing: 3px; width: 100%; font-size: 88%;">';
            $i_html .= '<tr><td style="width:33%;" valign="top">';

            $t = 0;
            $j = 0;
        }

        // If we need to start a new page
        if ($j == (floor($max_per_page / 3) + 1)) {
            $i_html .= '<br></td><td style="width: 33%;" valign="top">';
            $j      = 0;
        }
        $j++;
        $t++;

    endforeach;  // End foreach user

    $i_html .= '</td></tr>';

    // Add a page break at the end of this class
    $i_html .= '</table></div><div style="page-break-after:always;"></div>';

endforeach; // End foreach class
?>

<html>
<style type=\"text/css\">
    body {
        font-family: 'Helvetica';
    }
</style>
<body>
<?= $i_html; ?>
</div></body>
</html>
