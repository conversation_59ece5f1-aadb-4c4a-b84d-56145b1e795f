<?php

// First row, dates - date we start this quarter on
//        if ($class->day_of_week == 7) { // Sunday
//            $date = $group->start_date;
//        } elseif ($class->day_of_week == 3) {
//            $date = $group->start_date;
//        } // Get this from our global settings

$date = $group->start_date;
// Start our class dates on the DAY OF THE WEEK since the start date of the group.  Not all classes start on the Group start date.
$date->modify(\App\BibleClasses\BibleClass::$days_of_week[$class->day_of_week]);
// Find how many weeks of classes we have.

$weeks = ceil((int)$date->diffInDays($group->end_date) / 7);

// Add an extra week if the end date DOES make a perfect quarter.  Example was Quarter from 12/1/19 - 2/26/20.
if ($date->diffInDays($group->end_date) % 7 == 0) {
    $weeks++;
}

$CHECKBOX_COLUMN_WIDTH = 40;
$NAME_COLUMN_WIDTH     = 160;

if ($weeks > 13) {
    $CHECKBOX_COLUMN_WIDTH = 36;
    $NAME_COLUMN_WIDTH     = 150;
} elseif ($weeks > 16) {
    $CHECKBOX_COLUMN_WIDTH = 33;
    $NAME_COLUMN_WIDTH     = 140;
} elseif ($weeks > 19) {
    $CHECKBOX_COLUMN_WIDTH = 30;
    $NAME_COLUMN_WIDTH     = 130;
}


$other_rows = null;

// First create our header
$first_row = '<div style="padding: 8px 0 15px 0; font-weight: bold; font-size: 120%; width: 100%; text-align: center;">
				' . $class->title . ' <span style="font-size: 88%;"> (' . $class->location_name . ')</span></div>
				<div>
				<table style="border-top: 1px solid #333; border-left: 1px solid #333; border-spacing: 0px;">';

// Create our first row with the dates
$first_row .= '<tr><td style="width: ' . $NAME_COLUMN_WIDTH . 'px; border-right: 1px solid #333; border-bottom: 1px solid #333;">&nbsp;</td>';
for ($i = 0; $i < $weeks; $i++):
    $first_row .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333; text-align: center; width: ' . $CHECKBOX_COLUMN_WIDTH . 'px;">' . $date->format('m/d') . '</td>';
    $date->add(new DateInterval('P7D'));
endfor;
$first_row .= '</tr>';

$registered_users = $class->registrations()->orderByRaw('last_name asc, family_id, first_name asc')->get();

$j = 0;
foreach ($registered_users as $user):

    $other_rows .= '<tr><td style="width: ' . $NAME_COLUMN_WIDTH . 'px; border-right: 1px solid #333; border-bottom: 1px solid #333; ' . ($j % 2 == 0 ? 'background-color: #f1f1f1;' : '') . '">&nbsp;' . $user->last_name . ', ' . $user->display_first_name . ' &nbsp; &nbsp; </td>';

    $date = $group->start_date;
    $date->modify(\App\BibleClasses\BibleClass::$days_of_week[$class->day_of_week]);

    for ($i = 0; $i < $weeks; $i++):
        if ($user->attendance()->where('date_attendance', $date)->where('user_attendance_type_id', $class->user_attendance_type_id)->exists()) {
            $other_rows .= '<td style="text-align: center; border-right: 1px solid #333; border-bottom: 1px solid #333; ' . ($j % 2 == 0 ? 'background-color: #f1f1f1;' : '') . '">X</td>';
        } else {
            $other_rows .= '<td style="border-right: 1px solid #333; border-bottom: 1px solid #333; ' . ($j % 2 == 0 ? 'background-color: #f1f1f1;' : '') . '">&nbsp;</td>';
        }
        $date->add(new \DateInterval('P7D'));
    endfor;
    $other_rows .= '</tr>';

    // If we reached the max number of users, do a page break, redo the header and beginning of the table
    if ($j == 34) {
        $other_rows .= '</table></div><div style="page-break-after:always;"></div>';
        // Add our header again
        $other_rows .= $first_row;

        $j = 0;
    }

    $j++;

endforeach;
?>

<html>
<body>
<?= $first_row . $other_rows ?>
</table>
</div></body>
</html>
