<?php

// First row, dates
// $date   = new DateTime($quarter->sunday_start_date); // Get this from our global settings
$i_html = null;

// First create our header
$i_html = '<div style="font-weight: bold; font-size: 160%; width: 100%; text-align: center;">
				Bible Class Registration<br><span style="font-size: 88%;">' . $group->name . ' - ' . $type->name . '</span></div>
				<div style="width: 100%;">
				<table cellpadding="5" style="width: 100%; border-collapse: collapse; border-top: 1px solid #333; border-left: 1px solid #333; border-spacing: 0px; font-size: 140%">
				<tr><td style="width: 25%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp; Name &nbsp; </td>
				<td style="width: 12%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp; Room # &nbsp; </td>
				<td style="width: 63%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp; Class </td></tr>';


$j = 0;
foreach ($users as $user):

    $t_class = $user->bibleClassRegistrations()->where('user_attendance_type_id', $type->id)->where('bible_class_group_id', $group->id)->first();

    if (!$t_class) {
        continue;
    }

    $i_html .= '<tr><td style="border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp;' . $user->last_name . ', ' . $user->display_first_name . ' &nbsp; &nbsp; </td>
						<td style="text-align: center; border-right: 1px solid #333; border-bottom: 1px solid #333;">' . $t_class->location_name . '</td>
						<td style="border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp; ' . $t_class->title . '</td>
						</tr>';

    // If we reached the max number of users, do a page break, redo the header and beginning of the table
    if ($j == 29) {
        $i_html .= '</table></div><div style="page-break-after:always;"></div>';
        // Add our header again
        $i_html .= '<div style="font-weight: bold; font-size: 160%; width: 100%; text-align: center;">
							Bible Class Registration<br><span style="font-size: 88%;">' . $group->name . ' - ' . $type->name . '</span></div>
							<div style="width: 100%; ">
							<table cellpadding="5" style="width: 100%;  border-collapse: collapse; border-top: 1px solid #333; border-left: 1px solid #333; border-spacing: 0px; font-size: 140%">
							<tr><td style="width: 25%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp; Name &nbsp; </td>
							<td style="width: 12%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp; Room # &nbsp; </td>
							<td style="width: 63%; text-align: center; font-weight: bold; border-right: 1px solid #333; border-bottom: 1px solid #333;"> &nbsp; Class </td></tr>';
        $j      = 0;
    }

    $j++;

endforeach;
?>

<html>
<style type=\"text/css\">
    body {
        font-family: 'Helvetica';
        font-size: 9px;
    }

    td {
        font-size: 75%;
    }

    @page {
        margin: 0.2in 0.5in 0.2in 0.5in;
    }
</style>
<body>
<?= $i_html; ?>
</table>
</div></body>
</html>