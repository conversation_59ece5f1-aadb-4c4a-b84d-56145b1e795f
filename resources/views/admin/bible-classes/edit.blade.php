@extends('admin._layouts._app')

@section('title', 'Bible Class - Edit')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.bible-classes.index'),
            'text' => 'Bible Classes',
        ], [
            'url' => route('admin.bible-classes.groups.view', $group),
            'text' => 'Group',
        ], [
            'text' => 'Edit Class',
        ]]])

        <div class="mb-4 md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Edit Class
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="py-6 px-4 sm:p-6 lg:pb-8">
                <form action="{{ route('admin.bible-classes.save', [$group, $class]) }}" method="post">
                    @csrf
                    @method('put')

                    <div class="space-y-4">

                        <div>
                            <label class="font-semibold" for="title">Class Title</label>
                            <input type="text" name="title" class="form-control col-md-8{{ $errors->has('title') ? ' is-invalid' : '' }}" value="{{ old('title') ?: $class->title }}" autocomplete="off">
                            @if ($errors->has('title'))
                                <span class="invalid-feedback">
                                        <strong>{{ $errors->first('title') }}</strong>
                                    </span>
                            @endif
                        </div>

                        <div>
                            <label class="font-semibold">Description</label>
                            <div class="controls">
                                <textarea name="description" class="col-md-6 form-control" style="height: 80px">{{ old('description') ?: $class->description }}</textarea>
                            </div>
                        </div>

                        <div>
                            <label class="font-semibold">Room Number / Location Name</label><br>
                            <div class="max-w-md">
                                <input type="text" name="location_name" value="{{ $class->location_name }}" class="col-md-2 form-control"/>
                            </div>
                        </div>
                        <div>
                            <label class="font-semibold">Teachers</label>
                            <div class="controls">
                                <select name="teacher_ids[]" class="js-choice1" id="teacher_ids_select" class="form-control col-md-6" multiple>
                                    <option></option>
                                    @foreach(\App\Users\User::visibleTo(Auth::user())->get() as $potential_teacher)
                                        <option value="{{ $potential_teacher->id }}" {{ old('teacher_ids') == $potential_teacher->id || $class->teachers->contains($potential_teacher->id) ? 'selected="selected"' : '' }}>{{ $potential_teacher->last_name.', '.$potential_teacher->first_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="font-semibold">Day of the Week</label>
                            <div class="controls">
                                @foreach(\App\BibleClasses\BibleClass::$days_of_week as $key => $day_name)
                                    <label><input type="radio" name="day_of_week" value="{{ $key }}" {{ $key == $class->day_of_week ? 'checked' : null }}> {{ $day_name }}</label>
                                    &nbsp; &nbsp;
                                @endforeach
                            </div>
                        </div>
                        <div>
                            <label class="font-semibold">Attendance Type / Group</label>
                            <div class="controls">
                                <select name="user_attendance_type_id" class="form-control col-md-3">
                                    @foreach(\App\Attendance\AttendanceType::visibleTo(Auth::user())->get() as $type)
                                        <option value="{{ $type->id }}" {{ $class->user_attendance_type_id == $type->id ? 'selected="selected"' : null }}>{{ $type->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div>
                            <fieldset>
                                <input type="hidden" name="enable_signup" value="0"/>
                                <input class="custom-control-input" type="checkbox" name="enable_signup" value="1" id="checkbox_enable_signup" @isChecked($class->enable_signup)>
                                <label class="custom-control-label" for="checkbox_enable_signup">
                                    <strong>Allow members to sign-up?</strong> (From the mobile app)
                                </label>
                            </fieldset>
                            <fieldset>
                                <input type="hidden" name="is_hidden" value="0"/>
                                <input class="custom-control-input" type="checkbox" name="is_hidden" value="1" id="checkbox_is_hidden" @isChecked($class->is_hidden)>
                                <label class="custom-control-label" for="checkbox_is_hidden">
                                    <strong>Hide this class?</strong> (Only viewable in the Admin for attendance tracking)
                                </label>
                            </fieldset>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="admin-button-blue">
                            <i class="fa fa-check" aria-hidden="true"></i> &nbsp; Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script>
        var initChoiceJs = function () {
            jschoice1 = new Choices('.js-choice1', {
                removeItemButton: true,
                allowHTML: false,
                items: {!! json_encode($potential_teacher) !!},
            });
        }
        document.addEventListener('DOMContentLoaded', event => {
            initChoiceJs();
        });
    </script>

@endsection


