@extends('admin._layouts._app')

@section('title', 'Bible Class - Registration')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.bible-classes.index'),
            'text' => 'Bible Classes',
        ], [
            'url' => route('admin.bible-classes.groups.view', $class->group),
            'text' => 'Group',
        ], [
            'text' => 'Class Registration',
        ]]])

        <div class="mb-4 md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Class Registration
                </h1>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="p-4 border-b border-gray-300">
                <h3 class="text-3xl ">{{ $class->title }}</h3>
            </div>
            <div class="grid grid-cols-12 gap-4">
                <div class="col-span-6">
                    <form action="{{ route('admin.bible-classes.registration.save', [$group, $class]) }}" method="post">
                        @method('put')
                        @csrf
                        <div class="hidden" id="collected_registration">
                            @foreach($class->registrations()->orderBy('last_name')->get() as $user)
                                <div class="hidden user_attendance_{{ $user->id }}">
                                    <input type="hidden" name="registration[{{ $user->id }}][user_id]" value="{{ $user->id }}"/>
                                    <input type="hidden" name="registration[{{ $user->id }}][family_selected]" value="false"/>
                                </div>
                            @endforeach
                        </div>

                        <h2 class="m-4 text-2xl">Registration List</h2>
                        <hr>
                    </form>
                    <div id="registration_list" class="m-2">
                        <div class="grid grid-col-12">
                            <div class="cols-span-4">
                                @foreach($class->registrations()->orderBy('last_name')->get() as $user)
                                    <form method="post" class="inline" action="{{  route('admin.bible-classes.registration.remove-user', [$class->group, $class, $user])  }}">
                                        @csrf
                                        @method('delete')

                                        <span class="inline-block px-4 py-2 text-sm font-semibold leading-none text-white bg-green-500 rounded-full mb-2 user_attendance_{{ $user->id }}">
                                            {{ $user->name }}
                                            &nbsp;<button style="cursor: pointer" type="submit"><i class="fa fa-times-octagon" aria-hidden="true"></i></button>
                                        </span>
                                    </form>
                                @endforeach
                            </div>
                        </div>
                        <br>
                    </div>
                </div>
                <div class="col-span-4 mt-4">
                    <div class="form-group{{ $errors->has('name') ? ' has-error' : '' }}">
                        <h2 class="my-2">User Search</h2>
                        <form method="post" id="upload-form" action={{ route('admin.bible-classes.registration.add-users', [$class->group, $class]) }}>
                            @csrf
                            <livewire:components.select-multiuser
                                    :selected_classes="'mt-2 grid grid-cols-2 gap-2'"
                            />
                        </form>
                    </div>
                    @foreach ($users as $user)
                        <input type="hidden" id="user_{{ $user['id'] }}" data-name="{{ $user['value'] }}" data-family_id="{{ $user['family_id'] }}"/>
                    @endforeach

                    <p class="">
                        Begin typing a user's name. Once you select the user to add, press enter. The user will be added to the list and you can begin searching for the next user.
                    </p>
                    <button type="submit" class="admin-button-blue mt-4" form="upload-form">
                        <i class="fa fa-check"></i> &nbsp; Save Registration
                    </button>
                </div>
            </div>
        </main>
    </div>

@endsection
