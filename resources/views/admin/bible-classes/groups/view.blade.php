@extends('admin._layouts._app')

@section('title', 'Account Files')

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.bible-classes.index'),
            'text' => 'Bible Classes',
        ], [
            'text' => 'View Group',
        ]]])

        <div class="mb-4 md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    {{ $group->name }}
                </h1>
            </div>
            <div class="space-x-2 flex flex-row">
                @if($group->is_signup_active)
                    <div class="inline-block px-3 py-1 text-white bg-emerald-500 rounded-full">Sign-up Active</div>
                @else
                    <div class="inline-block px-3 py-1 text-gray-700 bg-gray-300 rounded-full">Sign-up Disabled</div>
                @endif
                <button
                        onclick="openSidebar('{{ route('admin.bible-classes.groups.edit', $group) }}')"
                        type="button"
                        class="admin-button-transparent"
                        title="Edit Bible Class Group">
                    <i class="far fa-edit mr-2" aria-hidden="true"></i> Edit Class Group
                </button>
                <div class="relative z-10 flex flex-row" x-data="{userOptionsMenuOpen: false}">
                    <a href="{{ route('admin.bible-classes.create', $group) }}" class=" admin-button-blue rounded-r-none">
                        <i class="fa fa-plus mr-2" aria-hidden="true"></i>
                        New Class
                    </a>
                    <div class="relative block">
                        <button type="button" @click="userOptionsMenuOpen = true" class="relative admin-button-transparent inline-flex items-center px-1 rounded-l-none hover:bg-gray-200" aria-expanded="true" aria-haspopup="true">
                            <span class="sr-only">Open options</span>
                            <span>&nbsp;</span>
                            <x-heroicon-s-chevron-down class="h-5"/>
                            <span>&nbsp;</span>
                        </button>

                        <div @click.away="userOptionsMenuOpen = false" x-cloak x-show="userOptionsMenuOpen"
                             class="origin-top-right absolute right-0 mt-1 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-hidden border border-gray-300"
                             role="menu" aria-orientation="vertical" aria-labelledby="option-menu-button" tabindex="-1">
                            <div class="py-1" role="none">
                                <a href="{{ route('admin.bible-classes.copy', $group) }}" class="cursor-pointer flex justify-between text-gray-700 block px-4 py-2 text-base hover:bg-blue-500 hover:text-white" role="menuitem" tabindex="-1" id="option-menu-item-0">
                                    <x-heroicon-s-arrows-right-left class="w-5"/>
                                    <span>&nbsp;Copy from previous class</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-10">
                @forelse($classesByAttendanceType as $class_for_type)
                    <div class="">
                        <div class="float-right">
                            <div>
                                <a class="admin-button-transparent-small bg-white" href="{{ route('admin.bible-classes.pdfs.registration-by-class', [$group, $class_for_type->user_attendance_type_id]) }}">
                                    <i class="far fa-file-pdf mr-2"></i>Registration by Class
                                </a>
                                <a class="admin-button-transparent-small bg-white" href="{{ route('admin.bible-classes.pdfs.registration-by-user', [$group, $class_for_type->user_attendance_type_id]) }}">
                                    <i class="far fa-file-pdf mr-2"></i>Registration by User
                                </a>
                            </div>
                        </div>

                        <h3 class="text-2xl font-semibold">{{ $class_for_type->attendanceType ? $class_for_type->attendanceType->name : null}}</h3>

                        <table class="w-full mt-3">

                            <thead class="rounded-t">
                            <tr>
                                <td width="" class="rounded-tl p-2 bg-gray-600 text-white text-sm">Name</td>
                                <td class="p-2 bg-gray-600 text-white text-sm">Teachers</td>
                                <td class="rounded-tr p-2 bg-gray-600 text-white text-sm">&nbsp;</td>
                            </tr>
                            </thead>

                            <tbody class="border-l border-b border-r border-gray-300">
                            @forelse ($group->classesByType($class_for_type->attendanceType) as $class)
                                <tr class="even:bg-gray-100 odd:bg-white">
                                    <td class="p-2 font-medium">
                                        {{ $class->title }}
                                        @if($class->location_name)
                                            <div class="flex flex-row justify-between mt-2">
                                                <div class="font-normal text-gray-500 text-base">
                                                    ({{ $class->location_name }})
                                                </div>
                                                <span class="px-1 py-0 text-sm text-blue-800 rounded-sm bg-blue-100 border border-blue-300">
                                                    {{ $class->registrations->count() }} Registered
                                                </span>
                                            </div>
                                        @endif
                                    </td>

                                    <td class="space-y-1">
                                        @forelse($class->teachers as $user)
                                            <span class="inline-block px-2 py-1 text-sm text-white bg-blue-500 rounded-sm" rel="tooltip" data-placement="top" title="Teacher">
                                                    {{ $user->name  }}
                                                </span>
                                        @empty
                                            <span class="inline-block px-2 py-1 text-sm text-black bg-gray-300 rounded-sm">
                                                    No teachers assigned
                                                </span>
                                        @endforelse
                                    </td>

                                    <td class="w-5">
                                        <div class="flex flex-row space-x-2 no-wrap">
                                            <a class="admin-button-transparent-small bg-white"
                                               href="{{ route('admin.bible-classes.edit', [$group, $class]) }}">
                                                Edit
                                            </a>
                                            <a class="admin-button-transparent-small bg-white"
                                               href="{{ route('admin.bible-classes.registration.edit', [$group, $class]) }}">
                                                Registration
                                            </a>
                                            <a class="admin-button-transparent-small bg-white" href="{{ route('admin.bible-classes.pdfs.registration', [$group, $class]) }}">
                                                <i class="far fa-file-pdf mr-2"></i>Attendance
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                Empty
                            @endforelse
                            </tbody>
                        </table>
                    </div>
                @empty
                    <div class="row">
                        <div class="col text-center">
                            <br>
                            <span class="badge badge-large badge-warning">No classes created yet.</span>
                            <br><br>
                            <a href="<?= route('admin.bible-classes.create', $group); ?>">Create one now.</a>
                            <br>
                            <br>
                            <br>
                        </div>
                    </div>
                @endforelse
            </div>
        </main>
    </div>

@endsection
