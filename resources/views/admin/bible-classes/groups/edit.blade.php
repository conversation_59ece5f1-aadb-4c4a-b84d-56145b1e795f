@php
    $inputCSS = 'block w-full shadow-xs sm:text-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md';
    $checkboxCSS = 'focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded-sm';
@endphp

<form method="post" action="{{ route('admin.bible-classes.groups.save', $group) }}" class="h-full divide-y divide-gray-200 flex flex-col bg-white">
    @csrf
    <div class="flex-1 h-0 overflow-y-auto">
        <div class="py-3 px-4 bg-blue-700 sm:px-6">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-white" id="slide-over-title">
                    <i class="fa fa-plus mr-2" aria-hidden="true"></i>Create Bible Class Group
                </h2>
            </div>
            <div class="mt-1 hidden">
                <p class="text-sm text-blue-300">
                    Description goes here.
                </p>
            </div>
        </div>
        <div class="flex-1 flex flex-col">
            <div class="px-4 divide-y divide-gray-200 sm:px-6">
                <div class="py-3">
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Name
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <input type="text" name="name" placeholder="{{ date('Y') }} - Quarter 2" class="{{ $inputCSS }}" autocomplete="off" value="{{ $group->name }}"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                Start Date
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <input type="date" name="start_date" class="form-control" value="{{ $group->start_date?->format('Y-m-d') }}"/>
                        </div>
                    </div>
                    <div class="space-y-1 py-2 sm:px-6 sm:py-1 sm:grid sm:grid-cols-3">
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-900 sm:mt-px sm:pt-2">
                                End Date
                            </label>
                        </div>
                        <div class="sm:col-span-2">
                            <input type="date" name="end_date" class="form-control" value="{{ $group->end_date?->format('Y-m-d') }}"/>
                        </div>
                    </div>
                </div>
                <div class="py-3 sm:px-5">
                    <div class="space-y-3 py-3">
                        <div class="relative flex items-start">
                            <div class="absolute flex items-center h-5">
                                <input type="hidden" name="is_signup_active" value="0"/>
                                <input type="checkbox" class="{{ $checkboxCSS }}" id="is_signup_active" name="is_signup_active" value="1" @isChecked($group->is_signup_active)/>
                            </div>
                            <div class="pl-7 text-base">
                                <label for="is_signup_active" class="font-medium text-gray-900">
                                    Make the sign-ups active for this group?
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between py-4">
                    <div>
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-xs text-base font-medium rounded-md text-white btn-color-and-hover">
                            Save Changes
                        </button>
                        <button type="button" onclick="closeSidebar()" class="ml-4 bg-white py-2 px-4 border border-gray-300 rounded-md shadow-xs text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </button>

                        <div id="delete-group-confirm-div" style="display: none">
                            <br>
                            <strong>Are you sure?</strong>
                            <br>
                            Deleting a group will delete all classes and <strong>registrations</strong>.
                            <br>
                            This will remove the attendance rosters for these classes (but not attendance records for the day).
                            <br><br>
                            <button type="button" onclick="document.getElementById('delete-group-for-sure').submit()" class="inline-block px-3 py-1 text-sm font-semibold leading-none text-red-700 border border-red-700 rounded-sm focus:outline-hidden focus:border-red-900 hover:bg-red-100">Yes, delete this group.</button>
                        </div>
                    </div>

                    @can('delete', $group)
                        <div>
                            <button type="button" onclick="document.getElementById('delete-group-confirm-div').style.display = 'block';" class="inline-flex justify-center py-2 px-4 border border-red-600 rounded-md text-red-600 bg-white hover:bg-red-600 hover:text-white focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-red-500" title="Delete">Delete</button>
                        </div>
                    @endcan
                </div>
            </div>
        </div>
    </div>
    <div class="hidden shrink-0 px-4 py-4">
        {{-- footer text here --}}
    </div>
</form>

<form method="post" id="delete-group-for-sure" action="{{ route('admin.bible-classes.groups.delete', $group) }}" style="display: none;">
    @csrf
    @method('delete')
</form>
