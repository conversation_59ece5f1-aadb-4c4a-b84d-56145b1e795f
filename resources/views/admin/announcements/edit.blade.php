@extends('admin._layouts._app')

@section('title', 'Edit Announcement')

@section('content')

    <div class="admin-standard-col-width">

        <div class="pb-2 md:flex md:items-center md:justify-between">
            <div class="flex-1">
                <div class="flex items-start sm:items-center">
                    <a class="admin-button-transparent cursor-point px-2 mr-4 hover:bg-gray-200" href="{{ route('admin.account-files.index') }}">
                        <x-heroicon-s-arrow-left class="w-5 p-0"/>
                    </a>
                    <div class="ml-1">
                        <div class="flex items-center">
                            <h1>
                                {{ $file->title }}
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-6 flex md:mt-0">
                <form action="{{ route('admin.account-files.delete', $file) }}" method="post">
                    @csrf
                    @method('delete')
                    <button type="submit" class="admin-button-red mr-4 bg-transparent text-red-500 hover:text-white">
                        <x-heroicon-s-trash class="w-5 mr-1"/>
                        Delete
                    </button>
                </form>
                <a href="#" onclick="history.back()" class="relative mr-4 admin-button-transparent inline-flex items-center  hover:bg-gray-200">
                    Cancel
                </a>
                <a class="admin-button-blue" onclick="this.disabled = true; this.innerHTML = 'Loading...'; document.getElementById('account_files_edit_form').submit()">
                    <x-heroicon-s-check class="w-5 mr-1"/>
                    Save Changes
                </a>
            </div>
        </div>

        <div>
            <form id="account_files_edit_form" action="{{ route('admin.account-files.save', $file) }}" method="post" enctype="multipart/form-data">
                @csrf
                @method('put')

                <div class="mt-4 mx-auto max-w-2xl">
                    <div class="w-100">
                        <div class="pb-2">
                            <h3 class="text-xl font-semibold text-gray-600">
                                File Information
                            </h3>
                        </div>
                        <div class="w-100 bg-white admin-section-border sm:rounded-lg">
                            <div class="px-4 py-5 sm:p-0">
                                <dl class="grid grid-cols-1 divide-y">
                                    <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Title:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="title" type="text" value="{{ old('title') ?? $file->title }}" placeholder="" autocomplete="off" data-lpignore="true">
                                        </dd>
                                        @if ($errors->has('title'))
                                            <span class="invalid-feedback">
                                                <strong>{{ $errors->first('title') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Expire Date:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="expires_at" type="date" value="{{ old('expires_at') ?? $file->expires_at ? $file->expires_at->format('Y-m-d') : null }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                        @if ($errors->has('expires_at'))
                                            <span class=" invalid-feedback">
                                                <strong>{{ $errors->first('expires_at') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Type:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input name="type" type="text" value="{{ old('type') ?? $file->type }}" autocomplete="off" data-lpignore="true">
                                        </dd>
                                    </div>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4  sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 my-auto">
                                            Description:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <textarea name="description" autocomplete="off" data-lpignore="true">{{ old('description') ?? $file->description }}</textarea>
                                        </dd>
                                        @if ($errors->has('description'))
                                            <span class="invalid-feedback">
                                                <strong>{{ $errors->first('description') }}</strong>
                                            </span>
                                        @endif
                                    </div>
                                    <hr>
                                    <div class="mt-3 sm:mt-0 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-4 sm:py-2">
                                        <dt class="text-gray-500 align-content-start">
                                            File:
                                        </dt>
                                        <dd class="sm:col-span-2">
                                            <input class="" id="fileInput" type="file" name="user_file">
                                            <br><br>
                                            <div class="text-sm text-gray-400">Selecting a new file will delete the existing file.</div>
                                            <div class="text-sm text-gray-400">Selecting no new file will leave the existing file.</div>
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

    </div>

@endsection
