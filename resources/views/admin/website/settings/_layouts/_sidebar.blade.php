@php
    $setting_link = 'flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50';
    $setting_link_selected = 'flex items-center px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 rounded-md hover:text-blue-700 hover:bg-blue-50';
    $setting_icon_classes = 'shrink-0 -ml-1 mr-3 h-6 w-6 text-gray-400';
    $setting_icon_classes_selected = 'shrink-0 -ml-1 mr-3 h-6 w-6 text-blue-500';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="space-y-6">
        <!-- Content Settings -->
        <div>
            <h3 class="px-3 my-2 text-sm font-semibold text-gray-500 uppercase tracking-wider">Content</h3>
            <div class="mt-2 space-y-1">
                <a href="{{ route('admin.website.settings.carousel') }}" class="{{ request()->routeIs('admin.website.settings.carousel') ? $setting_link_selected : $setting_link }}" aria-current="page">
                    <x-heroicon-o-photo class="{{ request()->routeIs('admin.website.settings.carousel') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Home Page Carousel</span>
                </a>
                <a href="{{ route('admin.website.settings.content') }}" class="{{ request()->routeIs('admin.website.settings.content') ? $setting_link_selected : $setting_link }}">
                    <x-heroicon-o-cog class="{{ request()->routeIs('admin.website.settings.content') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Content Settings</span>
                </a>
                <a href="{{ route('admin.website.settings.quick-links') }}" class="{{ request()->routeIs('admin.website.settings.quick-links') ? $setting_link_selected : $setting_link }}">
                    <x-heroicon-o-link class="{{ request()->routeIs('admin.website.settings.quick-links') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Quick Links</span>
                </a>
                <a href="{{ route('admin.website.settings.special-links') }}" class="{{ request()->routeIs('admin.website.settings.special-links') ? $setting_link_selected : $setting_link }}">
                    <x-heroicon-o-link class="{{ request()->routeIs('admin.website.settings.special-links') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Special Links</span>
                </a>
            </div>
        </div>

        <!-- Branding Settings -->
        <div>
            <h3 class="px-3 my-2 text-sm font-semibold text-gray-500 uppercase tracking-wider">Branding</h3>
            <div class="">
                <a href="{{ route('admin.website.settings.logos') }}" class="{{ request()->routeIs('admin.website.settings.logos') ? $setting_link_selected : $setting_link }}" aria-current="page">
                    <x-heroicon-o-photo class="{{ request()->routeIs('admin.website.settings.logos') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Logos</span>
                </a>
                <a href="{{ route('admin.website.settings.social-links') }}" class="{{ request()->routeIs('admin.website.settings.social-links') ? $setting_link_selected : $setting_link }}">
                    <x-heroicon-o-share class="{{ request()->routeIs('admin.website.settings.social-links') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Social Links</span>
                </a>
            </div>
        </div>

        <!-- Technical Settings -->
        <div>
            <h3 class="px-3 my-2 text-sm font-semibold text-gray-500 uppercase tracking-wider">Technical</h3>
            <div class="">
                <a href="{{ route('admin.website.settings.domain') }}" class="{{ request()->routeIs('admin.website.settings.domain') ? $setting_link_selected : $setting_link }}" aria-current="page">
                    <x-heroicon-o-globe-americas class="{{ request()->routeIs('admin.website.settings.domain') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Domain</span>
                </a>
                <a href="{{ route('admin.website.settings.redirects') }}" class="{{ request()->routeIs('admin.website.settings.redirects') ? $setting_link_selected : $setting_link }}" aria-current="page">
                    <x-heroicon-o-link class="{{ request()->routeIs('admin.website.settings.redirects') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
                    <span class="truncate my-auto">Redirects</span>
                </a>
            </div>
        </div>
    </nav>
</aside>