@extends('admin._layouts._app')

@section('title', 'Website Settings')

@section('content')

    <div class="admin-heading-section">
        <h1>
            Website Settings
        </h1>
    </div>

    <main class="mt-4 admin-section">
        <div class="mx-auto max-w-(--breakpoint-2xl)">
            <div class="overflow-hidden rounded-lg bg-white">
                <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                    @php
                        $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                        $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
                        $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                        $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
                    @endphp

                    @include('admin.website.settings._layouts._sidebar')

                    <div class="divide-y divide-gray-200 lg:col-span-9">
                        <div class="px-6 py-4">
                            <h2>Main Logo</h2>
                            <div>
                                These logos are used on the website.
                            </div>
                        </div>
                        <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">
                            <div class="flex flex-row space-x-4">
                                <div>
                                    <div class="flex flex-col space-y-3">
                                        <form action="{{ route('admin.website.settings.logos.update', (new \App\Website\Services\GetWebsiteSetting)->get('logo.main.light.website_file_id')) }}"
                                              method="post" enctype="multipart/form-data" autocomplete="off">
                                            @method('post')
                                            @csrf
                                            <input type="hidden" name="setting_key" value="logo.main.light.website_file_id"/>
                                            <div class="flex flex-col space-y-3">
                                                <input type="file" name="file" class="w-fit border border-gray-300 px-2 py-1.5 rounded-sm"/>
                                                <button type="submit" class="w-fit admin-button-blue shrink"><i class="far fa-check"></i> &nbsp; Save Changes</button>
                                            </div>
                                        </form>

                                        @if($main_logo)
                                            <form action="{{ route('admin.website.settings.logos.delete', 'logo.main.light.website_file_id') }}"
                                                  method="POST"
                                                  onsubmit="return confirm('Are you sure you want to remove this logo?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="w-fit admin-button-red shrink">
                                                    <i class="far fa-trash-alt"></i> &nbsp; Remove Logo
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                    <div class="mt-2 text-sm">
                                        <strong>Required:</strong>
                                        <ul class="list-disc list-inside pl-4">
                                            <li>JPG, PNG or SVG file.</li>
                                        </ul>
                                        <strong>Recommended:</strong>
                                        <ul class="list-disc list-inside pl-4">
                                            <li>PNG file with transparent background.</li>
                                            <li>Image at least 800px wide.</li>
                                            <li>File size less than 200KB.</li>
                                            <li>Image should be wider than it is tall.</li>
                                            <li>Aspect ratio recommended is 4:1 or 3:1.</li>
                                        </ul>
                                    </div>
                                </div>
                                <div>
                                    @if($main_logo)
                                        <img src="{{ $main_logo?->getUrl() }}" class="max-w-xl max-h-64 min-w-64 border border-gray-300"/>
                                    @else
                                        <div class="flex">
                                            <div class="mr-auto text-amber-600 px-2 py-1 border border-amber-600 rounded-sm">No main logo uploaded.</div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="py-6 px-4 sm:p-6 lg:pb-8 space-y-4">
                            <h2>Footer Logo</h2>
                            <div class="flex flex-row space-x-4">
                                <div>
                                    <div class="flex flex-col space-y-3">
                                        <form action="{{ route('admin.website.settings.logos.update', (new \App\Website\Services\GetWebsiteSetting)->get('logo.footer.light.website_file_id')) }}"
                                              method="post" enctype="multipart/form-data" autocomplete="off">
                                            @method('post')
                                            @csrf
                                            <input type="hidden" name="setting_key" value="logo.footer.light.website_file_id"/>
                                            <div class="flex flex-col space-y-3">
                                                <input type="file" name="file" class="w-fit border border-gray-300 px-2 py-1.5 rounded-sm"/>
                                                <button type="submit" class="w-fit admin-button-blue shrink"><i class="far fa-check"></i> &nbsp; Save Changes</button>
                                            </div>
                                        </form>

                                        @if($footer_logo)
                                            <form action="{{ route('admin.website.settings.logos.delete', 'logo.footer.light.website_file_id') }}"
                                                  method="POST"
                                                  onsubmit="return confirm('Are you sure you want to remove this logo?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="w-fit admin-button-red shrink">
                                                    <i class="far fa-trash-alt"></i> &nbsp; Remove Logo
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                    <div class="mt-2 text-sm">
                                        <strong>Required:</strong>
                                        <ul class="list-disc list-inside pl-4">
                                            <li>JPG, PNG or SVG file.</li>
                                        </ul>
                                        <strong>Recommended:</strong>
                                        <ul class="list-disc list-inside pl-4">
                                            <li>PNG file with transparent background.</li>
                                            <li>Image at least 800px wide.</li>
                                            <li>File size less than 200KB.</li>
                                            <li>Image should be wider than it is tall.</li>
                                            <li>Aspect ratio recommended is 4:1 or 3:1.</li>
                                        </ul>
                                    </div>
                                </div>
                                <div>
                                    @if($footer_logo)
                                        <img src="{{ $footer_logo?->getUrl() }}" class="max-w-xl max-h-64 min-w-64 border border-gray-300"/>
                                    @else
                                        <div class="flex">
                                            <div class="mr-auto text-amber-600 px-2 py-1 border border-amber-600 rounded-sm">No footer logo uploaded.</div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection
