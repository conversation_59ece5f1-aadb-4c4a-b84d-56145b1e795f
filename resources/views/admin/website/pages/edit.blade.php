@extends('admin._layouts._app')

@section('title', 'Edit - Website Page')

@section('content')

    @include('admin._layouts.components.breadcrumbs', ['levels' => [[
        'url' => route('admin.website.pages.index'),
        'text' => 'Website Pages',
    ], [
        'url' => null,
        'text' => 'Edit Page',
    ]]])

    <style>
        trix-editor > * {
            outline: none;
        }

        /* Add these styles for lists */
        trix-editor ul {
            list-style-type: disc;
            padding-left: 1.5em;
            margin: 0.5em 0;
        }

        trix-editor ol {
            list-style-type: decimal;
            padding-left: 1.5em;
            margin: 0.5em 0;
        }

        trix-editor li {
            margin: 0.2em 0;
        }

        trix-editor li p {
            margin: 0;
        }

        trix-editor blockquote {
            border-left: #9eb0b7 4px solid;
            padding: 4px 0 4px 10px;
        }
    </style>

    <div class="admin-heading-section">
        <h1>Edit Website Page</h1>
    </div>

    <link rel="stylesheet" type="text/css" href="https://unpkg.com/trix@2.0.8/dist/trix.css">
    <script type="text/javascript" src="https://unpkg.com/trix@2.0.8/dist/trix.umd.min.js"></script>

    <livewire:admin.website.pages.edit-page :page="$page"/>

@endsection
