@extends('admin._layouts._app')

@section('title', 'Website Pages')

@section('content')

    <div class="md:flex md:items-center align-middle md:justify-between">
        <div class="flex justify-between">
            <h1>
                Website Pages
            </h1>
        </div>
        <div>
            <a href="{{ route('admin.website.pages.create') }}" class="admin-button-blue">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                </svg>
                Create Page
            </a>
        </div>
    </div>

    <div class="flex flex-col mt-4">
        <div class="overflow-x-auto admin-section rounded-sm">
            <div class="inline-block min-w-full">
                <div class="overflow-hidden overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-300 border-collapse">
                        <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                        <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                            <th scope="col" class="w-4">&nbsp;</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter">Name</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter">Link</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter">Status</th>
                            <th scope="col" class="py-2 px-2 text-xs text-left font-semibold backdrop-blur-sm backdrop-filter">Expires</th>
                        </tr>
                        </thead>
                        <tbody id="files-table" class="bg-white divide-y divide-gray-200">
                        @forelse($pages as $page)
                            <tr data-fileid="{{ $page->id }}" data-sortid="{{ $page->sort_id }}" class="cursor-pointer hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                <td class="py-2 px-2 cursor-move">
                                    <x-heroicon-s-bars-3 class="w-5 handle"/>
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap font-medium text-gray-900">
                                    <a href="{{ route('admin.website.pages.edit', $page) }}">
                                        {{ $page->title }}
                                    </a>
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-gray-500 my-auto">
                                    <a href="{{ $page->getUrl() }}"
                                       target="_blank"
                                       class="text-sm">
                                        Link
                                        <x-heroicon-s-arrow-top-right-on-square class="w-4 mb-1 inline"/>
                                    </a>
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-gray-500 pl-6">
                                    {{ $page->getStatus() }}
                                </td>
                                <td class="py-3 px-2 whitespace-nowrap text-gray-500 pl-6">
                                    {{ $page->expires_at ? $page->expires_at->format('M d, Y') : 'never' }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="100%" class="text-center p-5">
                                    <span class="">No results found.</span>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="border-t admin-border-color p-4">
                {{ $pages->links() }}
            </div>
        </div>
    </div>

@endsection

@push('scripts')
    <script src=""></script>
    <script>
        new Sortable(document.getElementById('files-table'), {
            handle: '.handle', // handle's class
            animation: 150,
            onEnd: function (event) {
                // console.log('new id: ' + event.newIndex);
                // console.log('old id: ' + event.oldIndex);

                fetch('/account-files/' + event.item.dataset.fileid + '/update-sort-id', {
                    method: 'POST',
                    mode: 'cors', // no-cors, *cors, same-origin
                    cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
                    credentials: 'same-origin', // include, *same-origin, omit
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        new_sort_id: event.newIndex,
                        old_sort_id: event.oldIndex
                    })
                })
                    .then(response => {
                    })
                    .then(data => {
                    })
                    .catch(error => {
                        alert(error);
                    });

                // var itemEl = event.item;  // dragged HTMLElement
                // event.to;    // target list
                // event.from;  // previous list
                // event.oldIndex;  // element's old index within old parent
                // event.newIndex;  // element's new index within new parent
                // event.oldDraggableIndex; // element's old index within old parent, only counting draggable elements
                // event.newDraggableIndex; // element's new index within new parent, only counting draggable elements
                // event.clone // the clone element
                // event.pullMode;  // when item is in another sortable: `"clone"` if cloning, `true` if moving
            },
        });
    </script>
@endpush