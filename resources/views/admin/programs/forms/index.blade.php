@extends('admin._layouts._app')

@section('title', 'Program Users - ' . $program->name)

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.programs.index'),
            'text' => 'Programs',
        ], [
            'text' => 'Sign-up Forms',
        ]]])

        <div class="admin-heading-section mb-4">
            <h1>
                Sign-up Forms
            </h1>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.programs.sidebar.program-sidebar', [
                            'program' => $program,
                        ])

                        <div class="lg:col-span-9">

                            <div class="py-6 px-4">
                                <div class="flex flex-row justify-between">
                                    <h3 class="flex flex-row text-3xl font-medium leading-6 text-gray-900 my-auto">
                                        Sign-up Forms
                                    </h3>

                                    <div>
                                        <a href="{{ route('admin.programs.forms.create', [$program]) }}" class="admin-button-blue">
                                            Create Form
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="flex flex-col">
                                <div class="overflow-x-auto">
                                    <div class="overflow-hidden overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-300 border-collapse">
                                            <thead class="_sticky _z-10 bg-white bg-opacity-90 uppercase">
                                            <tr class="bg-gray-700 border-b border-gray-300 text-gray-200">
                                                <th scope="col" class="py-2 px-2 pl-4 text-xs text-left font-semibold">Name</th>
                                                <th scope="col" class="w-1/3 text-right"></th>
                                            </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                            @forelse($program->forms as $form)
                                                <tr class="hover:bg-gray-100 {{ $loop->odd ? 'bg-gray-50' : null }}">
                                                    <td class="py-3 px-2 pl-4 whitespace-nowrap text-gray-900">
                                                        <a href="{{ route('admin.programs.forms.edit', [$program, $form]) }}">
                                                            {{ $form->name }}
                                                        </a>
                                                    </td>
                                                    <td class="pr-4 text-right">
                                                        <div class="flex flex-row justify-end gap-2 overflow-x-auto">
                                                            <span class="inline-block bg-purple-500 whitespace-nowrap overflow-hidden text-white rounded my-auto py-0.5 px-2 text-sm">
                                                                {{ $form->fields->count() }} Fields
                                                            </span>
                                                            <span class="inline-block bg-blue-500 whitespace-nowrap overflow-hidden text-white rounded my-auto py-0.5 px-2 text-sm">
                                                                {{ $form->registrations()->IsNotWaitlist()->count() }} Registered
                                                            </span>
                                                            <span class="inline-block bg-blue-500 whitespace-nowrap overflow-hidden text-white rounded my-auto py-0.5 px-2 text-sm">
                                                                {{ $form->registrations()->isWaitlist()->count() }} Waitlist
                                                            </span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="100%" class="text-center p-5">
                                                        <span class="">No results found.</span>
                                                    </td>
                                                </tr>
                                            @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
