@extends('admin._layouts._app')

@section('title', 'Program Users - ' . $program->name)

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.programs.index'),
            'text' => 'Programs',
        ], [
            'text' => 'Registrations',
        ]]])

        <div class="admin-heading-section mb-4">
            <h1>
                {{ $program->name }}
            </h1>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.programs.sidebar.program-sidebar', [
                            'program' => $program,
                        ])

                        <div class="lg:col-span-9">

                            @livewire('admin.programs.view-users', ['program' => $program, 'registrants_only' => true])

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
