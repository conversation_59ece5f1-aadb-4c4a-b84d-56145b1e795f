@extends('admin._layouts._app')

@section('title', 'Program Groups - ' . $program->name)

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.programs.index'),
            'text' => 'Programs',
        ], [
            'url' => route('admin.programs.users.index', [$program]),
            'text' => 'Users',
        ], [
            'text' => 'Edit',
        ]]])

        <div class="flex-1">
            <div class="md:flex md:items-start align-middle md:justify-between mb-4">
                <div class="flex-1 justify-between">
                    <h1>
                        {{ $program->name }}
                    </h1>
                </div>
            </div>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.programs.sidebar.program-sidebar', [
                            'program' => $program,
                        ])

                        <div class="lg:col-span-9">

                            <div class="flex flex-row justify-between p-6">
                                <h4 class="flex flex-row font-medium leading-6 text-gray-900 my-auto">
                                    {{ $programUser->name }}
                                </h4>
                            </div>

                            <flux:tab.group class="">
                                <flux:tabs class="px-6">
                                    <flux:tab name="profile" icon="user">Profile</flux:tab>
                                    <flux:tab name="contacts" icon="users">Contacts</flux:tab>
                                    <flux:tab name="registrations" icon="document-check">Registrations</flux:tab>
                                    <flux:tab name="forms" icon="clipboard-document-check">Form Answers</flux:tab>
                                </flux:tabs>


                                <flux:tab.panel name="profile" class="mx-6">
                                    <form class="grid gap-4 grid-cols-1" action="{{ route('admin.programs.users.edit.save', [$program, $programUser]) }}" method="post">
                                        @csrf()
                                        <div class="md:grid md:grid-cols-1 md:gap-4">
                                            <div class="mt-5 md:mt-0 md:col-span-1">
                                                <div class="grid grid-cols-6 gap-4">
                                                    <div class="col-span-6 sm:col-span-2">
                                                        <label for="first_name" class="block text-sm font-medium text-gray-700">First name</label>
                                                        <input type="text" name="first_name" value="{{ $programUser->first_name }}" id="first_name" autocomplete="off" class="mt-1">
                                                    </div>

                                                    <div class="col-span-6 sm:col-span-4">
                                                        <label for="last_name" class="block text-sm font-medium text-gray-700">Last name</label>
                                                        <input type="text" name="last_name" value="{{ $programUser->last_name }}" id="last_name" autocomplete="off" class="mt-1">
                                                    </div>


                                                    <div class="col-span-6 sm:col-span-1 sm:col-start-1 lg:col-span-1">
                                                        <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
                                                        <select id="gender" name="gender" autocomplete="off" class="mt-1">
                                                            @foreach (\App\Users\User::$genders as $key => $value)
                                                                <option value="{{ $key }}"{{ $programUser->gender === $key ? ' selected' : ''}}>{{ $value }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-span-6 sm:col-span-1 sm:col-start-3 md:col-start-3 lg:col-start-2">
                                                        <label for="marital_status" class="block text-sm font-medium text-gray-700 whitespace-nowrap">Marital Status</label>
                                                        <select id="marital_status" name="marital_status" autocomplete="off" class="mt-1">
                                                            @foreach (\App\Users\User::$marital_statuses as $key => $value)
                                                                <option value="{{ $key }}"{{ $programUser->marital_status === $key ? ' selected' : ''}}>{{ $value }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-span-6 sm:col-span-1 sm:col-start-1 lg:col-start-3">
                                                        <label for="birthdate" class="block text-sm font-medium text-gray-700">Birthdate</label>
                                                        <input type="date" name="birthdate" value={{ $programUser->birthdate?->format('YYYY-MM-DD') }} id="birthdate" autocomplete="off" class="mt-1">
                                                    </div>
                                                    <div class="col-span-6 sm:col-span-2 sm:col-start-5">
                                                        <label for="family_role" class="block text-sm font-medium text-gray-700">Family Role</label>
                                                        <select id="family_role" name="family_role" autocomplete="off" class="mt-1">
                                                            <option></option>
                                                            @foreach(\App\Programs\ProgramUser::$family_roles as $index => $value)
                                                                <option value="{{ $index }}" {{ $programUser->family_role == $index ? 'selected' : null }}>{{ $value }}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>

                                                    <div class="col-span-6 lg:col-span-3">
                                                        <label for="allergies" class="block text-sm font-medium text-gray-700">Allergies</label>
                                                        <textarea type="text" name="allergies" id="allergies" autocomplete="off" class="mt-1 border border-gray-300 rounded-sm" rows="{{ $programUser->allergies ? 6 : 1 }}">{{ $programUser->allergies }}</textarea>
                                                    </div>

                                                    <div class="col-span-6 lg:col-span-3 lg:col-start-4">
                                                        <label for="special_needs" class="block text-sm font-medium text-gray-700">Special Needs</label>
                                                        <textarea type="text" name="special_needs" id="special_needs" autocomplete="off" class="mt-1 border border-gray-300 rounded-sm" rows="{{ $programUser->special_needs ? 6 : 1 }}">{{ $programUser->special_needs }}</textarea>
                                                    </div>

                                                    <div class="col-span-6 lg:col-span-3">
                                                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                                                        <textarea type="text" name="notes" id="notes" autocomplete="off" class="mt-1 border border-gray-300 rounded-sm" rows="{{ $programUser->notes ? 6 : 1 }}">{{ $programUser->notes }}</textarea>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>


                                        <div class="md:grid md:grid-cols-4 md:gap-6">
                                            <div class="md:col-span-1">
                                                <h3 class="text-lg font-medium leading-6 text-gray-900">Groups</h3>
                                                <p class="mt-1 text-sm text-gray-500">Choose what registration group(s) this new user should belong to.</p>
                                            </div>
                                            <div class="mt-5 md:mt-0 md:col-span-3">
                                                <div class="space-y-6" action="#" method="POST">
                                                    <div class="col-span-6">
                                                        <label for="family_id" class="block text-lg font-semibold text-gray-700">
                                                            Groups
                                                        </label>
                                                        <hr class="my-1">
                                                        <div class="grid grid-cols-3">
                                                            @forelse($program->groups()->IsRegistrationGroup()->orderBy('name', 'asc')->get() as $index => $group)
                                                                <label class="mb-1">
                                                                    <input type="checkbox" name="groups[]" value="{{ $group->id }}" {{ $programUser->groups->where('id', $group->id)->count() > 0 ? ' checked="checked"' : null }}/>
                                                                    <span>{{ $group->name }}</span>
                                                                </label>
                                                            @empty
                                                                <p class="text-gray-500">No groups found.</p>
                                                            @endforelse
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="flex justify-start pb-6 my-6">
                                            <button type="submit" class="admin-button-blue">
                                                <x-heroicon-s-check class="w-5 mr-2"/>
                                                Save Changes
                                            </button>
                                            <button type="button" class="admin-button-transparent hover:bg-gray-200 ml-4">Cancel</button>
                                        </div>
                                    </form>
                                </flux:tab.panel>


                                <flux:tab.panel name="contacts">

                                    @if($programUser->registration?->contacts()->exists())
                                        <div class="grid-cols-1 px-4 mb-6 sm:px-6">
                                            <h3 class="text-lg font-medium leading-6 text-gray-900">Contacts</h3>
                                            <p class="mt-1 text-sm text-gray-500">This user has the following contacts through registrations:</p>
                                            <div class="mt-2 border border-gray-300 rounded-md p-4">
                                                <ul>
                                                    @foreach($programUser->registration?->contacts as $contact)
                                                        <li>
                                                            <a href="{{ route('admin.programs.users.edit', ['program' => $contact->program, 'programUser' => $contact]) }}">
                                                                {{ $contact->name }}
                                                            </a>
                                                            <div class="mt-2 text-xs">
                                                                @if($contact->settings->is_primary_contact)
                                                                    <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10 mr-1">Primary</span>
                                                                @else
                                                                    <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 font-medium text-gray-500 ring-1 ring-inset ring-gray-700/10 mr-1">Not Primary</span>
                                                                @endif
                                                                @if($contact->settings->is_emergency_contact)
                                                                    <span class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 font-medium text-red-700 ring-1 ring-inset ring-red-700/10 mr-1">Emergency</span>
                                                                @else
                                                                    <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 font-medium text-gray-500 ring-1 ring-inset ring-gray-700/10 mr-1">Not Emergency</span>
                                                                @endif
                                                                @if($contact->settings->can_pickup)
                                                                    <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 font-medium text-green-700 ring-1 ring-inset ring-green-700/10">Can Pickup</span>
                                                                @else
                                                                    <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 font-medium text-gray-500 ring-1 ring-inset ring-gray-700/10 mr-1">Can Pickup</span>
                                                                @endif
                                                            </div>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    @endif

                                </flux:tab.panel>


                                <flux:tab.panel name="registrations">

                                    @if($programUser->associatedRegistrations->isNotEmpty())
                                        <div class="grid-cols-1 px-4 sm:px-6">
                                            <h3 class="text-lg font-medium leading-6 text-gray-900">Associated Registrations</h3>
                                            <p class="mt-1 text-sm text-gray-500">This user belongs to these registrations:</p>
                                            {{--                                            @foreach($programUser->associatedRegistrations as $registration)--}}
                                            {{--                                                <div class="mt-2">--}}
                                            {{--                                                    {{ $registration->registrationForm?->name }}--}}
                                            {{--                                                </div>--}}
                                            {{--                                                <div class="mt-4">--}}
                                            {{--                                                    @foreach($registration->registrationForm->registrationFormResponses as $response)--}}
                                            {{--                                                </div>--}}
                                            {{--                                            @endforeach--}}
                                        </div>
                                    @endif

                                    @if($programUser->registration?->registrationForm()->exists())
                                        <div class="grid-cols-1 px-4 my-6 sm:px-6">
                                            <h3 class="text-lg font-medium leading-6 text-gray-900">Sign-up Forms</h3>
                                            <p class="mt-1 text-sm text-gray-500">This user is registered with the following form:</p>
                                            <div class="mt-2">
                                                <ul>
                                                    <li>
                                                        <a href="{{ route('admin.programs.view', $programUser->registration->program) }}">{{ $programUser->registration->registrationForm?->name }}</a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="mt-4">
                                                @foreach($programUser->registration->registrationFormResponses as $response)
                                                    <div class="mt-5">
                                                        <h4 class="text-lg font-medium leading-6 text-gray-900">{{ $response->formField->title }}</h4>
                                                        @if(!$response->response && !$response->multi_response)
                                                            <p class="mt-1 text-sm text-gray-500">No answer</p>
                                                        @elseif($response->multi_response)
                                                            <p class="mt-1 text-sm text-gray-500">{{ implode(', ', $response->multi_response) }}</p>
                                                        @elseif($response->response)
                                                            <p class="mt-1 text-sm text-gray-500">{{ $response->response }}</p>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif

                                </flux:tab.panel>


                                <flux:tab.panel name="forms">

                                    @if($programUser->forms()->exists())
                                        <div class="grid-cols-1 pb-6 px-4 sm:px-6">
                                            <h3 class="text-lg font-medium leading-6 text-gray-900">Form Information</h3>
                                            <p class="mt-1 text-sm text-gray-500">This user is associated with information from the following forms:</p>
                                            @foreach($programUser->forms as $form)
                                                <div class="mt-6">
                                                    <h4 class="text-lg font-medium leading-6 text-gray-900">{{ $form->name }}</h4>
                                                </div>
                                                <div class="mt-4 border border-gray-300 rounded-md p-4">
                                                    @foreach($programUser->formResponsesForForm($form)->get() as $response)
                                                        <div class="mb-2">
                                                            <h5 class="font-medium leading-6 text-gray-900">{{ $response->formField->title }}</h5>
                                                            @if(!$response->response && !$response->multi_response)
                                                                <p class="mt-1 text-sm text-gray-500">No answer</p>
                                                            @elseif($response->multi_response)
                                                                <p class="mt-1 text-sm text-gray-500">{{ implode(', ', $response->multi_response) }}</p>
                                                            @elseif($response->response)
                                                                <p class="mt-1 text-sm text-gray-500">{{ $response->response }}</p>
                                                            @endif
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif

                                </flux:tab.panel>


                            </flux:tab.group>

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection
