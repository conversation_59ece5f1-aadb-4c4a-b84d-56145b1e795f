@extends('admin._layouts._app')

@section('title', 'Check-ins - ' . $program->name)

@section('content')

    <div class="admin-standard-col-width">

        @include('admin._layouts.components.breadcrumbs', ['levels' => [[
            'url' => route('admin.programs.index'),
            'text' => 'Programs',
        ], [
            'url' => route('admin.programs.view', $program),
            'text' => $program->name,
        ], [
            'text' => 'Check-ins',
        ]]])

        <div class="admin-heading-section mb-4">
            <h1>
                {{ $program->name }} - Check-ins
            </h1>
        </div>

        <main class="relative admin-section">
            <div class="mx-auto max-w-(--breakpoint-2xl)">
                <div class="overflow-hidden rounded-lg bg-white">
                    <div class="divide-y divide-gray-200 lg:grid lg:grid-cols-12 lg:divide-y-0 lg:divide-x">

                        @include('admin.programs.sidebar.program-sidebar', [
                            'program' => $program,
                        ])

                        <div class="lg:col-span-9">

                            @livewire('admin.programs.checkins', ['program' => $program])

                        </div>
                    </div>
                </div>
            </div>
        </main>

    </div>

@endsection