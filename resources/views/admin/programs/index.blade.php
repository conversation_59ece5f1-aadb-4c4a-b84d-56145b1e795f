@extends('admin._layouts._app')

@section('title', 'Programs')

@section('content')

    <div class="admin-standard-col-width">

        <div class="md:flex md:items-center align-middle md:justify-between">
            <div class="flex justify-between">
                <h1>
                    Programs
                </h1>
            </div>
            <div>
                <a onclick="openModal('{{ route('admin.programs.create') }}')" class="admin-button-blue">
                    <x-heroicon-m-plus class="w-6 h-6 text-white mr-1"/>
                    New Program
                </a>
            </div>
        </div>

        <div class="admin-section mt-4">

            <ul role="list" class="divide-y divide-gray-200">
                @forelse($programs as $program)
                    <li>
                        <a href="{{ route('admin.programs.view', $program) }}" class="block hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg text-gray-700">
                            <div class="flex items-center px-4 py-4 sm:px-6">
                                <div class="flex min-w-0 flex-1 items-center">
                                    <div class="min-w-0 flex-1 md:grid md:grid-cols-3 md:gap-4">
                                        <div>
                                            <p class="truncate text-xl font-medium text-blue-600">{{ $program->name }}</p>
                                            <div class="mt-2 flex flex-col gap-2 text-base text-gray-700">
                                                <div class="flex">
                                                    <div class="bg-gray-200 shrink px-2 py-0.5 rounded-md">
                                                        {{ $program->registeredUsers()->count() }} Registrations
                                                    </div>
                                                </div>
                                                <div class="flex">
                                                    <div class="bg-green-500 text-white px-2 py-0.5 rounded-md">
                                                        {{ $program->currentCheckins()->count() }} Checked in
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="hidden md:block my-auto">

                                        </div>
                                        <div class="hidden md:block my-auto">

                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <x-heroicon-s-chevron-right class="h-5 w-5 text-gray-400"/>
                                </div>
                            </div>
                        </a>
                    </li>
                @empty
                    <div class="mx-auto my-12 max-w-lg">
                        <div class="text-center">
                            <x-heroicon-o-archive-box class="size-12 text-gray-300 mx-auto"/>
                            <h3 class="mt-2 text-sm font-semibold text-gray-900">No programs</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating a new program.</p>
                            <div class="mt-6">
                                <flux:button onclick="openModal('{{ route('admin.programs.create') }}')"
                                             icon="plus" variant="primary"
                                             class="cursor-pointer">
                                    New Program
                                </flux:button>
                            </div>
                        </div>
                    </div>
                @endforelse
            </ul>

        </div>

        <div class="mt-4">
            <div class="col-md-12">
                {{ $programs->links() }}
            </div>
        </div>
    </div>

@endsection