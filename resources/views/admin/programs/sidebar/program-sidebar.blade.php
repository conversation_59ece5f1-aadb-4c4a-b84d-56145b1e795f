@php
    $setting_icon_classes_selected = 'text-blue-500 group-hover:text-blue-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_icon_classes = 'text-gray-400 group-hover:text-gray-500 shrink-0 -ml-1 mr-3 h-6 w-6';
    $setting_link_selected = 'bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700 group border-l-4 px-3 py-2 flex items-center text-sm font-medium';
    $setting_link = 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900 group border-l-4 px-3 py-2 flex flex-row items-center text-sm font-medium';
@endphp

<aside class="lg:col-span-3 lg:pb-16">
    <nav class="space-y-0 lg:space-y-1">
        <!-- Current: "bg-blue-50 border-blue-500 text-blue-700 hover:bg-blue-50 hover:text-blue-700", Default: "border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900" -->
        <a href="{{ route('admin.programs.view', [$program]) }}" class="{{ request()->routeIs('admin.programs.view') ? $setting_link_selected : $setting_link }}" aria-current="page">
            <x-heroicon-o-viewfinder-circle class="{{ request()->routeIs('admin.programs.view') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Overview</span>
        </a>

        <a href="{{ route('admin.programs.users.registered', [$program]) }}" class="{{ request()->routeIs('admin.programs.users.registered') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-user-group class="{{ request()->routeIs('admin.programs.users.registered') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Registrations</span>
            <span class="ml-auto px-2 my-auto bg-blue-500 text-white rounded-full font-medium text-sm">{{ $program->registeredUsers()->count() }}</span>
        </a>

        <a href="{{ route('admin.programs.checkins', [$program]) }}" class="{{ request()->routeIs('admin.programs.users.checkins') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-clipboard-document-check class="{{ request()->routeIs('admin.programs.users.checkins') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">User Check-ins</span>
        </a>

        <a href="{{ route('admin.programs.users.index', [$program]) }}" class="{{ request()->routeIs('admin.programs.users.index') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-user-group class="{{ request()->routeIs('admin.programs.users.index') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">All Users</span>
            <span class="ml-auto px-2 my-auto bg-gray-400 text-white rounded-full font-medium text-sm">{{ $program->users()->count() }}</span>
        </a>

        <a href="{{ route('admin.programs.groups', [$program]) }}" class="{{ request()->routeIs('admin.programs.groups') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-rectangle-group class="{{ request()->routeIs('admin.programs.groups') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Groups</span>
            <span class="ml-auto px-2 my-auto bg-gray-400 text-white rounded-full font-medium text-sm">{{ $program->groups()->count() }}</span>
        </a>

        <a href="{{ route('admin.programs.forms.index', [$program]) }}" class="{{ request()->routeIs('admin.programs.forms.*') ? $setting_link_selected : $setting_link }}">
            <x-heroicon-o-user-group class="{{ request()->routeIs('admin.programs.forms.*') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>
            <span class="truncate my-auto">Sign-up Forms</span>
            <span class="ml-auto px-2 my-auto bg-gray-400 text-white rounded-full font-medium text-sm">{{ $program->forms()->count() }}</span>
        </a>

        {{--        <a href="{{ route('admin.programs.checkins', [$program]) }}" class="{{ request()->routeIs('admin.programs.checkins') ? $setting_link_selected : $setting_link }}">--}}
        {{--            <x-heroicon-o-clipboard-document-check class="{{ request()->routeIs('admin.programs.checkins') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>--}}
        {{--            <span class="truncate my-auto">Group Check-ins</span>--}}
        {{--        </a>--}}

        {{--        <a href="{{ route('admin.programs.checkins.live', [$program]) }}" class="{{ request()->routeIs('admin.programs.checkins.live') ? $setting_link_selected : $setting_link }}">--}}
        {{--            <x-heroicon-o-arrow-path class="{{ request()->routeIs('admin.programs.checkins.live') ? $setting_icon_classes_selected : $setting_icon_classes }}"/>--}}
        {{--            <span class="truncate my-auto">Check-ins - Live View</span>--}}
        {{--        </a>--}}
    </nav>
</aside>