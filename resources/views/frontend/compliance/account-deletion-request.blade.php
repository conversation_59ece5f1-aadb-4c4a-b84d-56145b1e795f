@extends('frontend.layouts.app', [
    'no_index' => true,
])

@section('title', 'Remove Data Request - Lightpost')

@section('content')

    @include('frontend.layouts.header')

    <div class="bg-gray-50">
        <div class="py-12 sm:py-16 lg:pt-20 lg:pb-12">
            <div class="max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-4xl leading-9 font-extrabold text-gray-900 sm:leading-10 lg:text-5xl lg:leading-none">
                        Contact Us
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-(--breakpoint-md) mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form method="post" action="{{ route('frontend.compliance.account-deletion-request.submit') }}" novalidate>
            @csrf
            <div>
                <div>
                    <div class="rounded-md bg-blue-50 p-4">
                        <div class="flex">
                            <div class="shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm leading-5 font-medium text-blue-700">
                                    This form is to request account deletion from Lightpost support.
                                    <br>
                                    <strong>This will send your deletion request to the congregation that is responsible for your information.</strong>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                        <div class="sm:col-span-2">
                            <label for="first_name" class="block text-sm font-medium leading-5 text-gray-700">
                                First name
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <input type="text" id="first_name" name="first_name" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"/>
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="last_name" class="block text-sm font-medium leading-5 text-gray-700">
                                Last name
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <input type="text" id="last_name" name="last_name" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"/>
                            </div>
                        </div>

                        <div class="sm:col-span-3">
                            <label for="church" class="block text-sm font-medium leading-5 text-gray-700">
                                Congregation Name *
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <input id="church" name="church" type="text" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"/>
                            </div>
                        </div>

                        <div class="sm:col-span-4">
                            <label for="email" class="block text-sm font-medium leading-5 text-gray-700">
                                Email address *
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <input id="email" name="email" type="email" placeholder="<EMAIL>" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"/>
                            </div>
                        </div>
                        <div class="sm:col-span-2 hidden sm:inline"></div>

                        <div class="sm:col-span-2">
                            <label for="phone" class="block text-sm font-medium leading-5 text-gray-700">
                                Phone Number
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <input type="text" id="phone" name="phone" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"/>
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="city" class="block text-sm font-medium leading-5 text-gray-700">
                                City
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <input type="text" id="city" name="city" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"/>
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="state" class="block text-sm font-medium leading-5 text-gray-700">
                                State
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <input type="text" id="state" name="state" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"/>
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <label for="comment" class="block text-sm font-medium leading-5 text-gray-700">
                                Comments
                            </label>
                            <div class="mt-1 rounded-md shadow-xs">
                                <textarea id="comment" name="comment" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"></textarea>
                            </div>
                        </div>

                        <input type="text" name="address" id="address" class="hidden">
                    </div>
                </div>
            </div>
            <div class="mt-4 pt-5">
                <div class="flex justify-start">
                    <span class="inline-flex w-full rounded-md shadow-xs">
                        <button type="submit" class="w-full inline-flex justify-center py-4 px-10 border border-transparent text-base leading-5 font-medium text-lg rounded-md text-white bg-blue-600 hover:bg-blue-500 focus:outline-hidden focus:border-blue-700 focus:ring-blue active:bg-blue-700 transition duration-150 ease-in-out">
                            Submit Request
                        </button>
                    </span>
                </div>
            </div>
        </form>
    </div>


    <div class="mt-12 border-t border-gray-200 mx-8"></div>

    @include('frontend.layouts.footer')

@endsection

@push('scripts')



@endpush