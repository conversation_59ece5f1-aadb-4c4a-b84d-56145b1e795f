@extends('frontend.layouts._app')

@section('title', 'Pricing - Lightpost - A plan for every congregation size.')

@section('content')

    @include('frontend.layouts._header')

    <div class="max-w-6xl mx-auto pt-16 sm:pt-16 lg:pt-28">
        <div class="mt-6 pb-8 sm:mt-8 sm:pb-20 lg:pb-16"
             x-data="{ members: 100, cost: 29, showAdditionalMembersCost: false, weeklyCost: false, updateCost() {
                this.cost = $refs.stepRange.value;
                if($refs.worship_assignments.checked) { this.cost = parseInt(this.cost) + parseInt($refs.worship_assignments.value) };
                if($refs.attendance_tracking.checked) { this.cost = parseInt(this.cost) + parseInt($refs.attendance_tracking.value) };
                if($refs.visitor_tracking.checked) { this.cost = parseInt(this.cost) + parseInt($refs.visitor_tracking.value) };
                if($refs.podcasts.checked) { this.cost = parseInt(this.cost) + parseInt($refs.podcasts.value) };
                if($refs.child_checkin.checked) { this.cost = parseInt(this.cost) + parseInt($refs.child_checkin.value) };
                if($refs.online_giving.checked) { this.cost = parseInt(this.cost) + parseInt($refs.online_giving.value) };
                if($refs.financial_management.checked) { this.cost = parseInt(this.cost) + parseInt($refs.financial_management.value) };
                if($refs.website_feature.checked) { this.cost = parseInt(this.cost) + parseInt($refs.website_feature.value) };
                if($refs.domain_management.checked) { this.cost = parseInt(this.cost) + parseInt($refs.domain_management.value) };
                if($refs.stepRange.value > 65) { this.showAdditionalMembersCost = true; } else { this.showAdditionalMembersCost = false };
                this.weeklyCost = Math.round(parseFloat((parseInt(this.cost) * 12 / 52)));
                } }"
             x-init="updateCost">
            <div class="relative">
                <div class="absolute inset-0 mx-2 sm:mx-6 lg:mx-4 xl:mx-0 h-full rounded-2xl -mt-20 lg:-mt-32 bg-linear-to-r from-blue-500 from-10% via-blue-400 via-50% to-blue-500 to-90%">
                    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 mt-5 lg:mt-10">
                        <h2 class="text-2xl text-center font-medium tracking-tight text-white sm:text-4xl lg:text-5xl">A plan for every church size</h2>
                    </div>
                </div>
                <div class="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div class="mx-auto max-w-lg overflow-hidden rounded-lg shadow-lg lg:flex lg:max-w-none">
                        <div class="flex-1 bg-white px-6 py-8 lg:p-10">
                            <div class="">
                                <div class="flex items-center">
                                    <h4 class="shrink-0 bg-white pr-4 text-base font-semibold text-blue-600">What's Included</h4>
                                    <div class="flex-1 border-t-2 border-gray-200"></div>
                                </div>
                                <ul role="list" class="mt-4 space-y-1 lg:grid lg:grid-cols-2 lg:gap-x-8 md:gap-y-2 lg:space-y-0">
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Church Directory</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto"><strong>Unlimited</strong> Visitors</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Photo Directory</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">20GB Storage</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">
                                            Group Messaging
                                            <span class="px-2 inline-flex text-xs font-normal rounded-sm bg-amber-50 py-0.5 text-amber-800 border border-amber-400">Email, SMS, Voice</span>
                                        </p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Native Mobile Apps &nbsp; <i class="fab fa-apple"></i> &nbsp;<i class="fab fa-sm fa-google"></i></p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">
                                            Group Posts
                                            <span class="px-2 inline-flex text-xs font-normal rounded-sm bg-purple-50 py-0.5 text-purple-800 border border-purple-400">Instant Communication</span>
                                        </p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Sermon / Lesson Uploading</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Member Involvement</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Prayer List</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Bible Classes</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">File Sharing</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Calendars</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Emergency Check-in</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Website Integration</p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-400 my-auto">Song Tracking <span class="font-light text-sm">- Coming soon!</span></p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Background Checks <span class="font-light text-sm">- Coming soon!</span></p>
                                    </li>
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <x-heroicon-m-check-circle class="shrink h-5 w-5 text-green-400 my-auto"/>
                                        <p class="ml-3 text-base text-gray-700 my-auto">Email Support</p>
                                    </li>
                                </ul>
                            </div>
                            <div class="mt-8">
                                <div class="flex items-center">
                                    <h4 class="shrink-0 bg-white pr-4 text-base font-semibold text-blue-600">Premium Add-on Features</h4>
                                    <div class="flex-1 border-t-2 border-gray-200"></div>
                                </div>
                                <ul role="list" class="mt-4 space-y-1 lg:grid lg:grid-cols-2 lg:gap-x-8 md:gap-y-3 lg:space-y-0 select-none">
                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="worship_assignments" aria-describedby="worship_assignments-description" x-on:change="updateCost" x-ref="worship_assignments" value="15" name="worship_assignments" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="worship_assignments" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>
                                                 Assignments
                                                <span class="px-1.5 inline-flex text-xs font-normal rounded-sm bg-green-50 py-0.5 text-green-800 border border-green-400">v2.0</span>
                                            </span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$15<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="attendance_tracking" aria-describedby="attendance_tracking-description" x-on:change="updateCost" x-ref="attendance_tracking" value="9" name="attendance_tracking" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="attendance_tracking" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>Attendance Cards &amp; Tracking</span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$9<span class="text-xs"> / m</span></span></label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="visitor_tracking" aria-describedby="visitor_tracking-description" x-on:change="updateCost" x-ref="visitor_tracking" value="15" name="visitor_tracking" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="visitor_tracking" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>Visitor Tracking</span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$15<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="podcasts" aria-describedby="podcasts-description" x-on:change="updateCost" x-ref="podcasts" value="9" name="podcasts" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="podcasts" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>Podcasts</span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$9<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="child_checkin" aria-describedby="child_checkin-description" x-on:change="updateCost" x-ref="child_checkin" value="15" name="child_checkin" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="child_checkin" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>Child Check-in</span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$15<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="online_giving" aria-describedby="online_giving-description" x-on:change="updateCost" x-ref="online_giving" value="24" name="online_giving" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="online_giving" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>Online Giving</span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$24<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="financial_management" aria-describedby="financial_management-description" x-on:change="updateCost" x-ref="financial_management" value="20" name="financial_management" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="financial_management" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>
                                                Financial Management
                                            </span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$20<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input disabled id="vbs_management" aria-describedby="vbs_management-description" x-on:change="updateCost" x-ref="vbs_management" value="0" name="vbs_management" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="vbs_management" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>Programs <span class="text-sm">(VBS/Camp/etc)</span></span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$39<span class="text-xs"> / program</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="website_feature" aria-describedby="website_feature-description" x-on:change="updateCost" x-ref="website_feature" value="25" name="website_feature" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="website_feature" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>
                                                Website
                                                <span class="px-1.5 inline-flex text-xs font-normal rounded-sm bg-blue-50 py-0.5 text-blue-800 border border-blue-400">New!</span>
                                            </span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$25<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                    <li class="flex items-start lg:col-span-1 list-none ml-0">
                                        <div class="shrink-0">
                                            <div class="relative flex items-start">
                                                <div class="flex h-6 items-center">
                                                    <input id="domain_management" aria-describedby="domain_management-description" x-on:change="updateCost" x-ref="domain_management" value="10" name="domain_management" type="checkbox" class="h-4 w-4 rounded-sm border-gray-300 text-blue-600 focus:ring-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                        <label for="domain_management" class="w-full flex flex-row justify-between ml-3 text-base text-gray-800 font-normal">
                                            <span>Domain Management</span>
                                            <span class="-mt-0.5 text-gray-700 font-light bg-gray-100 px-2 py-0.5 rounded-lg">$10+<span class="text-xs"> / m</span></span>
                                        </label>
                                    </li>

                                </ul>
                            </div>
                        </div>
                        <div class="bg-gray-100 py-8 px-6 text-center lg:flex lg:shrink-0 lg:flex-col lg:justify-center lg:p-12 lg:w-80">
                            <div class="mt-4 flex items-center justify-center text-6xl font-bold tracking-tight text-gray-900">
                                <span>$<span class="font-bold" x-text="weeklyCost"></span></span>
                                <span class="ml-3 text-2xl font-medium tracking-normal text-gray-600 text-left leading-6">per<br/><span class="">week</span></span>
                            </div>
                            <div x-show="weeklyCost" class="text-center text-gray-500 mt-1">
                                Billed at <span class="">$</span><span x-text="cost">29</span>/month
                            </div>
                            <div class="pt-4">
                                <label for="membersRange" class="flex">
                                    <div class="mx-auto text-sm font-semibold">Members</div>
                                </label>
                                <input
                                        x-ref="stepRange"
                                        type="range"
                                        class="w-full h-4 p-0 bg-transparent focus:outline-hidden focus:ring-1"
                                        value="29"
                                        min="29"
                                        max="69"
                                        step="10"
                                        id="membersRange"
                                        x-on:mousemove="updateCost"
                                        x-on:change="updateCost"
                                />
                                <ul class="flex justify-between w-full text-sm px-[10px] mb-6">
                                    <li class="flex justify-center relative list-none ml-0"><span class="absolute">100</span></li>
                                    <li class="flex justify-center relative list-none ml-0"><span class="absolute">200</span></li>
                                    <li class="flex justify-center relative list-none ml-0"><span class="absolute">300</span></li>
                                    <li class="flex justify-center relative list-none ml-0"><span class="absolute">400</span></li>
                                    <li class="flex justify-center relative list-none ml-0"><span class="absolute">500+</span></li>
                                </ul>
                            </div>
                            <p x-cloak x-show="showAdditionalMembersCost" class="mt-4 text-sm"
                               x-transition:enter="transition ease-out duration-1300"
                               x-transition:enter-start="opacity-0 scale-90"
                               x-transition:enter-end="opacity-100 scale-100"
                               x-transition:leave="transition ease-in duration-1300"
                               x-transition:leave-start="opacity-100 scale-100"
                               x-transition:leave-end="opacity-0 scale-90">
                                <span class="font-normal text-gray-500">Add additional members<br>for only $10 per 100 members.</span>
                            </p>
                            <div class="mt-10">
                                <div class="rounded-md shadow-sm">
                                    <a href="{{ route('frontend.signup') }}" class="blaze-button flex items-center justify-center">
                                        Get Started
                                        <x-heroicon-m-arrow-right-circle class="ml-2 w-5 h-5"/>
                                    </a>
                                </div>
                            </div>
                            <div class="flex-col mt-10 tracking-normal leading-4">
                                <svg class="w-10 text-gray-500 mx-auto" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                                    <path d="M56 16H40L0 152c0 41.5 31.6 75.6 72 79.6V472v24h48V472 231.6c40.4-4 72-38.1 72-79.6L152 16H136V152H120L104 16H88L72 152H56V16zm584 0s-128 16-128 144V320h80V472v24h48V472 320 216 192 16zM336 32c-49.8 0-95.9 16.3-133.1 43.8L222.7 143l0 0c29-29 69-47 113.3-47c63.3 0 118.1 36.8 144 90.2V160c0-25.1 4.2-46.8 11.2-65.5C450.9 55.8 396.2 32 336 32zm0 384c-88.4 0-160-71.6-160-160c0-4.2 .2-8.3 .5-12.4c-7.5 5.3-15.7 9.7-24.5 13.1V383.8C192.5 441.9 259.8 480 336 480c89.4 0 166.5-52.3 202.4-128H512 480V325.8C454.1 379.2 399.3 416 336 416zM464 256a128 128 0 1 0 -256 0 128 128 0 1 0 256 0z"/>
                                </svg>

                                <div class="mt-1 text-sm text-gray-500">Less than the cost to take the <span x-show="cost > 89">large</span> family to <span x-show="cost > 39">a nice</span> dinner!</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div id="candle" class="relative max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8 mb-8 mt-8 md:mt-0">
        <div class="max-w-lg mx-auto lg:max-w-5xl">
            <div class="rounded-lg bg-gray-100 px-6 py-6 sm:py-6 sm:px-8 lg:flex lg:items-center">
                <div class="flex-1">
                    <div>
                      <span class="inline-flex px-4 py-1 rounded-full text-sm leading-5 font-semibold tracking-wide uppercase bg-white text-gray-800">
                        Candle Plan
                      </span>
                    </div>
                    <div class="mt-2 text-base leading-7 text-gray-600">
                        <span class="font-semibold">Directory &amp; Messaging Only</span>
                        <br>
                        Get the Directory, Mobile Apps &amp; Messaging features for only <strong class="font-semibold text-gray-900">$19</strong>/month.
                    </div>
                </div>
                <div class="mt-6 rounded-md shadow-sm lg:mt-0 lg:ml-10 lg:shrink-0">
                    <a href="{{ route('frontend.signup') }}" class="flex items-center justify-center px-5 py-3 border border-transparent text-base leading-6 font-medium rounded-md text-gray-900 bg-white hover:text-gray-700 focus:outline-hidden focus:ring-3 transition duration-150 ease-in-out">
                        Purchase Candle Plan
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="relative max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="max-w-lg mx-auto lg:max-w-5xl">
            <div class="rounded-lg bg-gray-100 px-6 py-6 sm:py-6 sm:px-8 lg:flex lg:items-center">
                <div class="flex-1">
                    <div>
                      <span class="inline-flex px-4 py-1 rounded-full text-sm leading-5 font-semibold tracking-wide uppercase bg-white text-gray-800">
                        Flashlight Plan
                      </span>
                    </div>
                    <div class="mt-2 text-base leading-7 text-gray-600">
                        <span class="font-semibold">Administrative only?</span>
                        <br>
                        Manage your directory and enable messaging for only <strong class="font-semibold text-gray-900">$10</strong>/month.
                        <div class="text-sm">No member apps.</div>
                    </div>
                </div>
                <div class="mt-6 rounded-md shadow-sm lg:mt-0 lg:ml-10 lg:shrink-0">
                    <a href="{{ route('frontend.signup') }}" class="flex items-center justify-center px-5 py-3 border border-transparent text-base leading-6 font-medium rounded-md text-gray-900 bg-white hover:text-gray-700 focus:outline-hidden focus:ring-3 transition duration-150 ease-in-out">
                        Purchase Flashlight Plan
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="mx-4 pt-6">
        <div class="flex flex-col mx-auto max-w-(--breakpoint-sm) lg:max-w-(--breakpoint-lg) divide-y divide-gray-200 border border-gray-300 rounded-lg shadow-lg">
            <div class="grid grid-cols-1 lg:grid-cols-2 divide-y lg:divide-x lg:divide-y-0 divide-gray-200">
                <div id="data-import" class="p-4 sm:p-8">
                    <div class="text-gray-700 text-center text-xl font-semibold mb-4">
                        <div>
                            <x-heroicon-s-inbox-arrow-down class="w-6 mr-1 text-blue-500 inline"/>
                            Data Import?
                        </div>
                    </div>
                    <div class="text-gray-700 text-center">
                        Directory data <strong>importing</strong> from other systems is available for a $199 one-time fee.
                    </div>
                </div>
                <div class="p-4 sm:p-8">
                    <div class="text-gray-700 text-center text-xl font-semibold mb-4">
                        <div>
                            <x-heroicon-s-user-group class="w-6 mr-1 text-blue-500 inline"/>
                            Need More Members?
                        </div>
                    </div>
                    <div class="text-gray-700 text-center">
                        Add <span class="font-semibold">additional members</span> for only $10 per 100 members.
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 divide-y lg:divide-x lg:divide-y-0 divide-gray-200">
                <div class="p-4 sm:p-8">
                    <div class="text-gray-700 text-center text-xl font-semibold mb-4">
                        <div>
                            <x-heroicon-s-bolt class="w-6 mr-1 text-blue-500 inline"/>
                            Fully Managed?
                        </div>
                    </div>
                    <div class="text-gray-700 text-center">
                        As easy as sending an <strong>email to update</strong> data.
                        <br/>
                        Starting at $99/m.
                    </div>
                </div>
                <div class="p-4 sm:p-8">
                    <div class="text-gray-700 text-center text-xl font-semibold mb-4">
                        <div>
                            <x-heroicon-s-cursor-arrow-rays class="w-6 mr-1 text-blue-500 inline"/>
                            More features on the way!
                        </div>
                    </div>
                    <div class="text-gray-700 text-center">
                        <a href="{{ route('frontend.roadmap') }}">Check our roadmap</a> for more details on what {{ config('app.name') }} is growing into.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8 sm:mt-12 text-sm sm:text-base max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-md mx-auto lg:max-w-5xl text-gray-400 text-center">
            <strong>Special budget</strong> constraints? We want to help.
            <br>
            <a href="{{ route('frontend.signup') }}" class="text-blue-400">Reach out</a> for more information.
        </div>
    </div>

    <div class="bg-gray-200 my-8 sm:my-20">
        <div class="max-w-(--breakpoint-xl) mx-auto py-6 sm:py-12 px-4 sm:px-6 lg:py-12 lg:px-8 lg:flex lg:items-center lg:justify-between">
            <h2 class="text-3xl leading-9 font-extrabold tracking-tight text-gray-900 sm:text-4xl sm:leading-10">
                Ready to dive in?
                <br/>
                <span class="text-blue-600">Start today.</span>
            </h2>
            <div class="mt-8 flex lg:shrink-0 lg:mt-0">
                <div class="inline-flex rounded-md shadow-sm">
                    <a href="{{ route('frontend.signup') }}" class="blaze-button inline-flex items-center justify-center ">
                        Get started
                    </a>
                </div>
                <div class="ml-3 inline-flex rounded-md shadow-sm">
                    <a href="#" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base leading-6 font-light rounded-md text-blue-600 bg-white hover:text-blue-500 focus:outline-hidden focus:ring-3 transition duration-150 ease-in-out">
                        Learn more
                    </a>
                </div>
            </div>
        </div>
    </div>


    <div id="consumables" class="relative max-w-(--breakpoint-lg) mx-auto px-4 sm:px-6 lg:px-8 mt-20">
        <div class="flex justify-center">
            <div class="inline-flex px-4 py-1 rounded-full bg-blue-50 border border-gray-300 text-base shadow-lg text-gray-700 font-normal">
                <div class="flex items-center gap-x-1">
                    <x-heroicon-m-sparkles class="w-5"/>
                    <span>Consumables</span>
                </div>
            </div>
        </div>
        <div class="mt-6 text-3xl text-center font-semibold tracking-tight text-gray-900 sm:text-4xl">
            Extra communication features
            <br>
            <span class="text-blue-600">No hidden costs</span>
        </div>
        <p class="mt-3 mb-6 mx-auto text-center text-base text-gray-500">
            Certain features we offer require <strong>consumption based</strong> pricing.
            <br>
            We offer these as close to a <mark class="bg-yellow-100 px-0.5 py-0.5">pass through cost</mark> as we can.
        </p>
    </div>

    <div class="relative mx-auto max-w-(--breakpoint-lg) p-4 bg-gray-100 rounded-2xl shadow-inner">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-48">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-envelope" aria-hidden="true"></i>
                    </span>
                    Email Messaging
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-left">
                                Per Group Email Address
                            </div>
                            <div class="flex-1 leading-5 text-gray-900 text-left sm:text-right">
                                FREE
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row items-stretch border-t px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-left">
                                Per Email Recipient
                                <br>
                                <span class="text-sm text-gray-400 font-light">to an individual</span>
                            </div>
                            <div class="flex-1 leading-5 text-left sm:text-right">
                                <span class="text-blue-500">$0.0012 / email</span>
                                <br>
                                <span class="text-xs text-gray-400 font-light">($0.12 per 100 emails)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-48">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-comments" aria-hidden="true"></i>
                    </span>
                    SMS Messaging
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-left">
                                Enablement Fee
                                <br>
                                <span class="text-sm text-gray-400 font-light">Enables text messages to any group.</span>
                            </div>
                            <div class="w-40 leading-5 text-gray-900 text-left sm:text-right">
                                $9
                                <br>
                                <span class="text-sm text-gray-400 font-light">per month</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row items-stretch border-t px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-left">
                                Per SMS Recipient
                                <br>
                                <span class="text-sm text-gray-400 font-light">to an individual</span>
                            </div>
                            <div class="flex-1 leading-5 text-left sm:text-right">
                                <span class="text-blue-500">$0.027 / SMS</span>
                                <br>
                                <span class="text-xs text-gray-400 font-light">($2.70 per 100 emails)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-48">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-waveform" aria-hidden="true"></i>
                    </span>
                    Voice Messaging
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row items-stretch px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-left">
                                Per Call Recipient
                                <br>
                                <span class="text-sm text-gray-400 font-light">to an individual</span>
                            </div>
                            <div class="flex-1 leading-5 text-left sm:text-right">
                                <span class="text-blue-500">$0.039 / minute</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-48">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-database" aria-hidden="true"></i>
                    </span>
                    Extra Storage
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row items-stretch px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-left">
                                Per Extra GB Storage
                            </div>
                            <div class="flex-1 leading-5 text-left sm:text-right">
                                $0.09 / GB
                                <br>
                                <span class="text-sm text-gray-400 font-light">per month</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-auto px-4 py-2 text-sm text-gray-400 text-left border-t">
                        Each plan comes with 20GB storage included!
                    </div>
                </div>
            </div>

        </div>
    </div>

    <div class="relative mx-auto max-w-(--breakpoint-lg) p-4 text-center text-sm text-gray-300">
        *Telecom rules and verifications are changing all the time. We are constantly working to improve and sustain SMS.
    </div>



    <div id="online-giving" class="relative max-w-(--breakpoint-lg) mx-auto px-4 sm:px-6 lg:px-8 mt-20">
        <div class="flex justify-center">
            <div class="inline-flex px-4 py-1 rounded-full bg-blue-50 border border-gray-300 text-base shadow-lg text-gray-700 font-normal">
                <div class="flex items-center gap-x-1">
                    <x-heroicon-m-sparkles class="w-5"/>
                    <span>Members Only</span>
                </div>
            </div>
        </div>
        <div class="mt-6 text-3xl text-center font-semibold tracking-tight text-gray-900 sm:text-4xl">
            Online Giving
        </div>
        <p class="mt-3 mb-6 mx-auto text-center text-base text-gray-500">
            A flat cost to enable all online giving features.
        </p>
    </div>

    <div class="relative mx-auto max-w-(--breakpoint-lg) p-4 bg-gray-100 rounded-2xl shadow-inner">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-48">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-hands-heart" aria-hidden="true"></i>
                    </span>
                    Online & Mobile Giving
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-left">
                                Enablement Fee
                                <br>
                                <span class="text-sm text-gray-400 font-light">Integrated into the Lightpost web and mobile app.</span>
                            </div>
                            <div class="w-40 leading-5 text-gray-900 text-left sm:text-right">
                                $5
                                <br>
                                <span class="text-sm text-gray-400 font-light">per month</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row items-stretch border-t px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-left">
                                Choice of payment methods
                                <br>
                                <span class="text-sm text-gray-400 font-light">Select which types of payments you want to accept.</span>
                            </div>
                            <div class="flex leading-5 text-right sm:text-right">
                                <x-heroicon-m-check-circle class="h-5 w-5 text-green-400"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-48">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-dollar-circle" aria-hidden="true"></i>
                    </span>
                    Transaction Fees
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-left">
                                Credit / Debit Cards
                            </div>
                            <div class="flex-1 leading-5 text-left sm:text-right">
                                <span>2.9% + $0.40</span>
                                <br>
                                <span class="text-sm text-gray-400 font-light">per transaction</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row items-stretch border-t px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-left">
                                ACH Debits
                                <br>
                                <span class="text-sm text-gray-400 font-light">ACH direct debit</span>
                            </div>
                            <div class="flex-1 leading-5 text-left sm:text-right">
                                <span>0.9% + $0.10</span>
                                <br>
                                <span class="text-sm text-gray-400 font-light">per transaction</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row items-stretch border-t px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-left">
                                Payout Fee
                                <br>
                                <span class="text-sm text-gray-400 font-light">Transfer from processor to bank</span>
                            </div>
                            <div class="w-40 leading-5 text-gray-900 text-left sm:text-right">
                                0.25%
                                <br>
                                <span class="text-sm text-gray-400 font-light">of total processed</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <div class="relative mx-auto max-w-(--breakpoint-lg) p-4 text-center text-sm text-gray-400">
        *Lower transaction fees can be negotiated with our payment processor at volume.
    </div>


    <div id="podcasts" class="relative max-w-(--breakpoint-lg) mx-auto px-4 sm:px-6 lg:px-8 mt-20">
        <div class="flex justify-center">
            <div class="inline-flex px-4 py-1 rounded-full bg-blue-50 border border-gray-300 text-base shadow-lg text-gray-700 font-normal">
                <div class="flex items-center gap-x-1">
                    <x-heroicon-m-sparkles class="w-5"/>
                    <span>Podcasts</span>
                </div>
            </div>
        </div>
        <div class="mt-6 text-3xl text-center font-semibold tracking-tight text-gray-900 sm:text-4xl">
            Simple, Automatic Podcasting
        </div>
        <p class="mt-3 mb-6 mx-auto text-center text-base text-gray-500">
            Turn the lessons you <strong>upload</strong> into Podcasts ‐ distributed to <span class="text-blue-500">all the major platforms</span>.
            <br>
            All automatically. You just <strong>bring the content</strong>.
        </p>
    </div>

    <div class="relative mx-auto max-w-(--breakpoint-sm) p-4 bg-gray-100 rounded-2xl shadow-inner">
        <div class="grid grid-cols-1 gap-4">

            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-48">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-microphone-alt" aria-hidden="true"></i>
                    </span>
                    Super Easy Postcasting
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-left">
                                Each Published Podcast
                                <br>
                                <span class="text-sm text-gray-400 font-light">Includes <strong>analytics</strong> and distribution to major platforms.</span>
                            </div>
                            <div class="w-40 leading-5 text-gray-900 text-left sm:text-right">
                                $39
                                <br>
                                <span class="text-sm text-gray-400 font-light">one-time fee</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row px-4 py-4 border-t">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-left">
                                First 10,000 downloads
                                <br>
                                <span class="text-sm text-gray-400 font-light">Downloads can be used across all published podcasts. </span>
                            </div>
                            <div class="w-40 leading-5 text-gray-900 text-left sm:text-right">
                                $9
                                <br>
                                <span class="text-sm text-gray-400 font-light">per month</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col rounded-xl shadow-md bg-white min-h-32">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-chart-line-up" aria-hidden="true"></i>
                    </span>
                    Additional Bandwidth
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-left">
                                10,000 additional downloads
                                <br>
                                <span class="text-sm text-gray-400 font-light">Billed based on actual usage.</span>
                            </div>
                            <div class="w-40 leading-5 text-gray-900 text-left sm:text-right">
                                $7.50
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col rounded-xl shadow-md bg-white">
                <div class="p-4 border-b border-gray-200 text-lg font-medium text-gray-800">
                    <span class="mr-2 inline-flex rounded-lg bg-sky-50 border border-blue-200 p-2 text-gray-800 ring-4 ring-white">
                        <i class="far fa-expand-arrows" aria-hidden="true"></i>
                    </span>
                    Platform Distribution
                </div>
                <div class="flex flex-col flex-1 text-gray-700">
                    <div class="flex-1">
                        <div class="flex flex-col sm:flex-row px-4 py-4">
                            <div class="flex-1 leading-5 font-medium text-gray-900 text-center">
                                Includes automatic submission to Apple Podcasts, Spotify Podcasts, Amazon Music and Listen Notes.
                                <br><br>
                                <span class="text-sm text-gray-400 font-light">Submission processing time varies by platform.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>



    <div class="bg-blue-700 mt-24">
        <div class="max-w-(--breakpoint-xl) mx-auto py-8 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center">
            <div class="lg:w-0 lg:flex-1">
                <h2 class="text-3xl leading-9 font-extrabold tracking-tight text-white sm:text-4xl sm:leading-10">
                    Sign-up for our newsletter
                </h2>
                <p class="mt-3 max-w-3xl text-lg leading-6 text-gray-300">
                    Get the latest news, updates, advice articles, helpful tips & more.
                </p>
            </div>
            <div class="mt-8 lg:mt-0 lg:ml-8">
                <div>
                    <form
                            action="https://buttondown.email/api/emails/embed-subscribe/lightpost"
                            method="post"
                            target="popupwindow"
                            onsubmit="window.open('https://buttondown.email/lightpost', 'popupwindow')"
                            class="sm:flex embeddable-buttondown-form"
                    >
                        <input type="hidden" name="tag" value="Lightpost End Users"/>
                        <div class="lg:w-full">
                            <input type="email" value="" name="email" id="bd-email" aria-label="Email address" required class="appearance-none w-full px-5 py-3 border border-transparent text-base leading-6 rounded-md text-gray-900 bg-white placeholder-gray-500 focus:outline-hidden focus:placeholder-gray-400 transition duration-150 ease-in-out sm:max-w-xs" placeholder="Enter your email"/>
                        </div>
                        <div class="mt-3 rounded-md shadow-sm sm:mt-0 sm:ml-3 sm:shrink-0">
                            <input class="w-full flex items-center justify-center px-5 py-3 border border-transparent text-base leading-6 font-medium rounded-md text-white hover:text-blue-900 bg-blue-900 hover:bg-blue-300 focus:outline-hidden focus:bg-blue-400 transition duration-150 ease-in-out" type="submit" value="Subscribe"/>
                        </div>
                    </form>
                </div>
                <p class="mt-3 text-sm leading-5 text-gray-300">
                    We care about the protection of your data. Read our
                    <a href="{{ route('frontend.privacy-policy') }}" class="text-white font-medium underline">
                        Privacy Policy.
                    </a>
                </p>
            </div>
        </div>
    </div>


    {{--        <h2 class="display-3 font-weight-bolder">Online Giving</h2>--}}
    {{--            <strong class="text-success">Coming soon!</strong>--}}
    {{--                We're hard at work with the <mark>online giving</mark> feature. Stay tuned!--}}

    @include('frontend.layouts._footer')

@endsection

@push('scripts')



@endpush