@extends('frontend.layouts._app')

@section('title', 'Value - Lightpost - Paying for itself month after month.')

@section('content')

    @include('frontend.layouts._header')

    <div class="lg:pt-8 mb-8">
        <div class="max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8 pb-8 mb-8">
            <div class="text-center">
                <h2 class="text-4xl leading-9 font-extrabold text-gray-900 sm:leading-10 lg:text-5xl lg:leading-none">
                    Proven Value
                </h2>
                <p class="mt-4 sm:text-xl leading-7 text-gray-500">
                    Based on <mark class="bg-yellow-100 px-0.5 py-0.5">real history</mark> and <mark class="bg-yellow-100 px-0.5 py-0.5">actual usage</mark>, we can easily
                    <br>
                    do a little <strong>napkin math</strong> to see just <em>how much</em> value Lightpost brings.
                </p>
            </div>
        </div>
    </div>

    <div class="max-w-(--breakpoint-xl) mx-auto lg:grid lg:grid-cols-2">
        <div class="px-0">
            <div class="flex flex-col rounded-lg bg-white border border-gray-300 mx-8">
                <p class="px-6 py-5 border-b border-gray-300 bg-gray-100 rounded-t-lg text-center text-xl leading-4 font-bold text-gray-800 tracking-wider">
                    Before Lightpost
                </p>
                <div class="text-gray-800 text-center">
                    <div class="flex">
                        <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                            Member Management
                            <br>
                            <small class="font-light">Time spent updating Word documents, Excel files, or swapping emails to keep up with member addresses &amp; phone numbers.</small>
                        </div>
                        <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                            <span class="text-xl">4 hours</span>
                            <br>
                            <small>per <strong>week</strong></small>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                            Calls for Information
                            <br>
                            <small class="font-light">The amount of time spent fielding calls for requests for information.</small>
                        </div>
                        <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                            <span class="text-xl">3 hours</span>
                            <br>
                            <small>per <strong>month</strong></small>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                            Attendance
                            <br>
                            <small class="font-light">The amount of time spent taking attendance using Excel sheets or other disparate methods.</small>
                        </div>
                        <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                            <span class="text-xl">3 hours</span>
                            <br>
                            <small>per <strong>week</strong></small>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                            Worship Assignments
                            <br>
                            <small class="font-light">The amount of time spent creating and filling positions for worship with Excel documents, emails and phone calls.</small>
                        </div>
                        <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                            <span class="text-xl">6 hours</span>
                            <br>
                            <small>per <strong>month</strong></small>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="flex-3 px-6 pt-6 pb-8 text-2xl leading-5 font-bold text-gray-900 mx-auto">
                            <mark class="bg-yellow-100 px-4 py-3 rounded-lg">37 hours / month</mark>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 lg:mt-0">
            <div class="flex-1">
                <div class="flex flex-col rounded-lg bg-white border border-gray-300 mx-8">
                    <p class="px-6 py-5 border-b border-gray-200 bg-gray-100 rounded-t-lg text-center text-xl leading-4 font-bold text-gray-800 tracking-wider">
                        After Lightpost
                    </p>
                    <div class="text-gray-800 text-center">
                        <div class="flex">
                            <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                                Member Management
                                <br>
                                <small class="font-light">Time spent updating Lightpost to keep up with member addresses &amp; phone numbers.</small>
                                <br>&nbsp;
                            </div>
                            <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                                <span class="text-xl">1 hour</span>
                                <br>
                                <small>per <strong>week</strong></small>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                                Calls for Information
                                <br>
                                <small class="font-light"><em>Reduced</em> because members have immediate access to the latest information using Lightpost.</small>
                            </div>
                            <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                                <span class="text-xl">&lt; 1 hour</span>
                                <br>
                                <small>per <strong>month</strong></small>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                                Attendance
                                <br>
                                <small class="font-light">The amount of time spent taking attendance using only Lightpost.</small>
                            </div>
                            <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                                <span class="text-xl">< 1 hour</span>
                                <br>
                                <small>per <strong>week</strong></small>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="flex-1 px-6 py-4 text-lg leading-5 font-medium text-gray-900 text-left">
                                Worship Assignments
                                <br>
                                <small class="font-light"><em>Reduced</em> because assignments can be auto-generated with a new schedules sent via mobile notifications.</small>
                            </div>
                            <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                                <span class="text-xl">< 1 hour</span>
                                <br>
                                <small>per <strong>month</strong></small>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="flex-3 px-6 pt-6 pb-8 text-2xl leading-5 font-bold text-gray-900 mx-auto">
                                <mark class="bg-yellow-100 px-4 py-3 rounded-lg">10 hours / month</mark>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="pt-8 px-0 max-w-(--breakpoint-md) mx-auto sm:px-6 lg:px-8">
        <div class="flex-1">
            <div class="flex flex-col rounded-lg bg-white border border-gray-300 mx-8">
                <p class="px-6 py-5 border-b border-gray-200 bg-gray-100 rounded-t-lg text-center text-xl leading-4 font-bold text-gray-800 tracking-wider">
                    Time Savings!
                </p>
                <div class="text-gray-800 text-center">
                    <div class="flex">
                        <div class="flex-1 px-6 py-4 text-lg leading-5 font-bold text-gray-900 text-left">
                            Total Saved
                            <br>
                            <small class="font-light">Secretary working @ $20/hour.</small>
                        </div>
                        <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                            <span class="text-2xl font-bold bg-yellow-100 px-4 py-1">$680.00</span>
                            <br>
                            <small><strong>savings</strong> per month</small>
                        </div>
                    </div>
                    <hr>
                    <div class="flex">
                        <div class="flex-1 px-6 py-4 text-lg leading-5 font-bold text-gray-900 text-left">
                            Lightpost Price
                            <br>
                            <small class="font-light">Depending on features &amp; congregation size.</small>
                        </div>
                        <div class="flex-2 px-6 py-4 text-lg leading-5 text-gray-900 text-right">
                            <span class="text-xl"><small>starting @ </small><strong class="text-2xl">$19</strong></span>
                            <br>
                            <small>per month</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div>
        <div class="pt-12 sm:pt-16 lg:pt-20 pb-10">
            <div class="max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8">
                <hr class="my-8 max-w-(--breakpoint-xl) mx-auto border-gray-200">
            </div>
        </div>
    </div>

    @include('frontend.layouts._footer')

@endsection

@push('scripts')



@endpush