@php
    if(!isset($absolute)) {
        $absolute = false;
    }
@endphp
<header x-data="{openMenu: false}" class="{{ $absolute ? 'absolute inset-x-0 top-0' : '' }} max-w-7xl mx-auto z-50">
    <nav class="flex items-center justify-between p-6 lg:px-8" aria-label="Global">
        <div class="flex space-x-10">
            <a href="/">
                <span class="sr-only">Lightpost</span>
                <img src="{{ asset('img/lightpost-logo.svg') }}" alt="Lightpost Logo" class="h-10 lg:h-12">
            </a>
            <div class="hidden lg:flex lg:gap-x-2 my-auto">
                <a href="/#features" class="px-2 py-2 hover:bg-gray-100 rounded-lg text-base font-medium text-gray-600 hover:text-blue-600" key="Features">
                    Features
                </a>
                <a href="{{ route('frontend.mobile') }}" class="px-2 py-2 hover:bg-gray-100 rounded-lg text-base font-medium text-gray-600 hover:text-blue-600" key="Mobile App">
                    Mobile&nbsp;App
                </a>
                <a href="{{ route('frontend.value') }}" class="px-2 py-2 hover:bg-gray-100 rounded-lg text-base font-medium text-gray-600 hover:text-blue-600" key="Value">
                    Value
                </a>
                <a href="{{ route('frontend.security') }}" class="px-2 py-2 hover:bg-gray-100 rounded-lg text-base font-medium text-gray-600 hover:text-blue-600" key="Security">
                    Security
                </a>
                <a href="{{ route('frontend.pricing') }}" class="px-2 py-2 hover:bg-gray-100 rounded-lg text-base font-medium text-gray-600 hover:text-blue-600" key="Pricing">
                    Pricing
                </a>
            </div>
        </div>
        <div class="flex lg:hidden">
            <button type="button" x-on:click="openMenu = !openMenu" class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700">
                <span class="sr-only">Open main menu</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/>
                </svg>
            </button>
        </div>
        <div class="hidden lg:flex lg:flex-1 lg:justify-end">
            <a href="https://{{ config('app.domains.app') }}/login" class="inline-flex items-center px-2 py-1 sm:px-4 sm:py-2 border border-transparent text-base leading-6 font-medium rounded-md text-gray-800 hover:text-blue-600 focus:outline-hidden focus:ring-blue active:bg-gray-50 active:text-blue-700 transition duration-150 ease-in-out">
                Login&nbsp;<span aria-hidden="true">&rarr;</span>
            </a>
            <a href="{{ route('frontend.signup') }}" class="inline-flex items-center px-2 py-1 sm:px-4 sm:py-2 border border-transparent text-base leading-6 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-800 focus:outline-hidden focus:ring-blue transition duration-150 ease-in-out">
                Contact
            </a>
        </div>
    </nav>
    <!-- Mobile menu, show/hide based on menu open state. -->
    <div x-show="openMenu" class="lg:hidden" role="dialog" aria-modal="true">
        <!-- Background backdrop, show/hide based on slide-over state. -->
        <div class="fixed inset-0 z-50"></div>
        <div class="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
            <div class="flex items-center justify-between">
                <a href="#" class="-m-1.5 p-1.5">
                    <span class="sr-only">Lightpost</span>
                    <img src="{{ asset('img/lightpost-logo.svg') }}" alt="Lightpost Logo" class="h-10 w-auto">
                </a>
                <button type="button" x-on:click="openMenu = !openMenu" class="-m-2.5 rounded-md p-2.5 text-gray-700">
                    <span class="sr-only">Close menu</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="mt-6 flow-root">
                <div class="-my-6 divide-y divide-gray-500/10">
                    <div class="space-y-2 py-6">
                        <a href="/#features" class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50" key="Features">
                            Features
                        </a>
                        <a href="{{ route('frontend.about') }}" class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50" key="Features">
                            About
                        </a>
                        <a href="{{ route('frontend.mobile') }}" class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50" key="Mobile App">
                            Mobile&nbsp;App
                        </a>
                        <a href="{{ route('frontend.value') }}" class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50" key="Value">
                            Value
                        </a>
                        <a href="{{ route('frontend.security') }}" class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50" key="Security">
                            Security
                        </a>
                        <a href="{{ route('frontend.pricing') }}" class="-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50" key="Pricing">
                            Pricing
                        </a>
                    </div>
                    <div class="py-6">
                        <a href="https://{{ config('app.domains.app') }}/login" class="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50">Log in</a>
                        <a href="{{ route('frontend.signup') }}" class="-mx-3 block rounded-lg px-3 py-2.5 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50">Contact</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>