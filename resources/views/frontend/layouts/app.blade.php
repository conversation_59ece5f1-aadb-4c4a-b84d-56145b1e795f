<!doctype html>
<html lang="{{ app()->getLocale() }}">
<head>
    <title>@yield('title', isset($title) ? $title : config('app.name', 'Lightpost'))</title>

    <link rel="canonical" href="{{ url()->current() }}"/>

    <link rel="apple-touch-icon" href="/favicon/favicon-180x180.png"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-196x196.png" sizes="196x196"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-96x96.png" sizes="96x96"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-32x32.png" sizes="32x32"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-16x16.png" sizes="16x16"/>
    <link rel="icon" type="image/png" href="/favicon/favicon-128.png" sizes="128x128"/>
    <meta name="application-name" content="Lightpost"/>
    @if(isset($no_index) && $no_index)
        <meta name="robots" content="noindex,nofollow">
    @endif
    <meta name="msapplication-TileColor" content="#FFFFFF"/>
    <meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png"/>
    <meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png"/>
    <meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png"/>
    <meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png"/>
    <meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png"/>
    <link rel="manifest" href="/favicon/manifest.json">

    <meta property="og:url" content="{{ \Illuminate\Support\Facades\URL::to('') }}"/>
    <meta property="og:type" content="website"/>
    <meta property="og:image" content="{{ \Illuminate\Support\Facades\URL::to('') }}/static/frontend/img/social/lightpost-og-image-1200-630.png"/>
    <meta property="og:title" content="Lightpost - Enabling your congregation to focus on spirital matters."/>
    <meta property="og:description" content="Lightpost is the simple church app that helps your congregation communicate and work together effortlessly. Built just for the churches of Christ."/>
    <meta property="og:image:width" content="1200"/>
    <meta property="og:image:height" content="630"/>

    <meta name="theme-color" content="#ffffff">

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="Description" content="Lightpost is a complete church communication & management platform, with a mobile application for members. Enable your congregation to focus on spiritual matters - not church management issues."/>

    {{-- See: https://rsms.me/inter/ --}}
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">

    <link href="{{ mix('/static/frontend/css/tailwind.frontend.css') }}" rel="stylesheet">
    <link href="/webfonts/css/all.min.css" rel="stylesheet">

    <script src="" defer></script>

    @if(config('app.env') == 'production')
        <script src="https://cdn.usefathom.com/script.js" data-site="YBPBLHMI" defer></script>
    @endif
</head>

<body>

@yield('content')

</body>
</html>
