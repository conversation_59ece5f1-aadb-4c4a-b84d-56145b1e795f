@extends('frontend.layouts.app')

@section('title', 'Lightpost - Enabling your congregation to focus on spiritual matters.')

@section('content')

    @include('frontend.layouts.header', ['absolute' => true])

    <div x-data="{openMenu: false, activeSlide: 1, slides: [1, 2, 3, 4, 5, 6, 7]}" x-init="setInterval(function() { activeSlide > 6 ? activeSlide = 1 : activeSlide++  }, 7000)">
        <div class="relative isolate">
            <div class="relative isolate overflow-hidden bg-linear-to-b from-orange-100/60 pt-6">
                <div class="absolute inset-y-0 right-1/2 -z-10 -mr-96 w-[200%] origin-top-right skew-x-[-30deg] bg-white shadow-xl shadow-gray-600/10 ring-1 ring-gray-50 sm:-mr-80 lg:-mr-96" aria-hidden="true"></div>
                <div class="mx-auto max-w-7xl px-6 lg:flex lg:items-center lg:gap-x-10 lg:px-8 py-24 lg:py-32">
                    <div class="mx-auto max-w-2xl mb-auto lg:mx-0 lg:flex-auto mt-2 lg:mt-28">
                        <div class="w-full flex justify-center lg:justify-start">
                            <div class="relative group cursor-pointer inline-block">
                                <div class="absolute -inset-1 bg-linear-to-r from-blue-500 to-orange-500 rounded-full blur-sm opacity-15 group-hover:opacity-80 transition duration-1000 group-hover:duration-400"></div>
                                <div class="relative px-4 py-1 bg-white ring-1 ring-gray-900/5 rounded-full leading-none flex items-center justify-center">
                                    <span class="text-sm hover:cursor-pointer text-orange-500 text-center">
                                        Built exclusively for the churches of Christ
                                    </span>
                                </div>
                            </div>
                        </div>
                        <h1 class="mt-8 text-center lg:text-left lg:mx-0 max-w-2xl text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
                            Connect. Communicate. <span class="whitespace-nowrap">Grow Together.</span>
                        </h1>
                        <p class="mt-6 text-center lg:text-left text-lg sm:text-xl leading-6 text-gray-600">
                            <span class="font-semibold">Lightpost</span> is the <span class="font-semibold">simple church app</span> that
                                                                         helps your congregation
                            <span class="font-semibold">communicate</span> and
                            <span class="font-semibold">work together</span> <em>effortlessly</em>.
                        </p>
                        <div class="mt-10 flex justify-center lg:justify-start items-center gap-x-6">
                            <a href="{{ route('frontend.signup') }}" class="rounded-md bg-blue-600 px-3.5 py-2.5 text-base font-medium text-white hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">Get started</a>
                            <a href="#features" class="text-base font-medium leading-6 text-gray-900 hover:text-blue-500">Learn more <span aria-hidden="true">→</span></a>
                        </div>
                    </div>
                    <div class="mt-12 sm:mt-18 lg:mt-0 lg:shrink-0 lg:grow"
                         x-data="{
                            activeSlide: 1,
                            slides: [1, 2, 3, 4, 5, 6, 7],
                            timer: null,
                            touchStartX: 0,
                            touchEndX: 0,
                            startTimer() {
                                this.timer = setInterval(() => {
                                    this.activeSlide = this.activeSlide >= 7 ? 1 : this.activeSlide + 1;
                                }, 5000);
                            },
                            stopTimer() {
                                if (this.timer) clearInterval(this.timer);
                            },
                            setSlide(slide) {
                                this.activeSlide = slide;
                                this.stopTimer();
                                this.startTimer();
                            },
                            handleTouchStart(event) {
                                this.touchStartX = event.touches[0].clientX;
                                this.stopTimer();
                            },
                            handleTouchMove(event) {
                                this.touchEndX = event.touches[0].clientX;
                            },
                            handleTouchEnd() {
                                const diffX = this.touchStartX - this.touchEndX;
                                
                                // Require at least 50px swipe
                                if (Math.abs(diffX) > 50) {
                                    if (diffX > 0) {
                                        // Swiped left - next slide
                                        this.activeSlide = this.activeSlide >= 7 ? 1 : this.activeSlide + 1;
                                    } else {
                                        // Swiped right - previous slide
                                        this.activeSlide = this.activeSlide <= 1 ? 7 : this.activeSlide - 1;
                                    }
                                }
                                
                                this.startTimer();
                            },
                            init() {
                                this.startTimer();
                            }
                         }">
                        <div class="relative max-w-[366px] mx-auto">
                            <!-- Phone Frame -->
                            <div class="relative rounded-[40px] bg-gray-300 p-2">
                                <!-- Screen Container -->
                                <div class="relative overflow-hidden rounded-[32px] bg-white aspect-[9/19.5]"
                                     @touchstart="handleTouchStart($event)"
                                     @touchmove="handleTouchMove($event)"
                                     @touchend="handleTouchEnd()">
                                    <!-- Slides Container -->
                                    <div class="absolute inset-0 flex transition-transform duration-500 ease-in-out"
                                         :style="{ transform: `translateX(-${(activeSlide - 1) * 100}%)` }">
                                        <!-- Individual Slides -->
                                        @for($i = 1; $i <= 7; $i++)
                                            <div class="shrink-0 w-full h-full">
                                                <img src="/static/frontend/img/screenshots/{{ $i }}-phone-2023.png"
                                                     alt="App screenshot {{ $i }}"
                                                     class="w-full h-full object-cover"
                                                     loading="{{ $i === 1 ? 'eager' : 'lazy' }}"
                                                     draggable="false"/>
                                            </div>
                                        @endfor
                                    </div>
                                </div>
                            </div>

                            <!-- Navigation Dots -->
                            <div class="flex items-center justify-center mt-4 space-x-2">
                                <template x-for="slide in slides" :key="slide">
                                    <button
                                            class="w-2 h-2 rounded-full transition-colors duration-200 ease-out hover:bg-blue-400"
                                            :class="{
                                            'bg-blue-600': activeSlide === slide,
                                            'bg-gray-300': activeSlide !== slide
                                        }"
                                            @click="setSlide(slide)"
                                            :aria-label="`Go to slide ${slide}`">
                                    </button>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gray-700">
        <div class="max-w-(--breakpoint-xl) mx-auto py-8 px-4 sm:py-12 sm:px-6 lg:px-8 lg:py-12">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl leading-9 font-bold text-white sm:text-4xl sm:leading-10">
                    Trusted by the Lord's church near &amp; yonder
                </h2>
                <p class="mt-3 text-lg leading-7 text-gray-200 sm:mt-4">
                    Lightpost enables <em class="text-yellow-100">communication</em>, reporting, management &amp; <em class="text-yellow-100">so much more</em>.
                </p>
            </div>
            <div class="mt-8 text-center sm:max-w-4xl mx-auto grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-8">
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        24+
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Features
                    </p>
                </div>
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        {{ round((395000 + $mail_count) / 1000000, 2) }}m+
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Messages Sent
                    </p>
                </div>
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        24/7
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Availability
                    </p>
                </div>
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        10k+
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Notifications Per Day
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="overflow-hidden py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 lg:items-start">
                <div class="lg:pr-4 lg:pt-4 my-auto">
                    <div class="lg:max-w-lg">
                        <h2 class="text-base font-semibold leading-7 text-blue-600">Manage faster</h2>
                        <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">The best workflow</p>
                        <p class="mt-6 text-lg leading-8 text-gray-600">
                            Lightpost works hard to make your work easy. One simple place to update data &amp; communicate with your congregation.
                        </p>
                        <p class="mt-6 text-lg leading-8 text-gray-600">
                            Lightpost is built specifically to help the churches of Christ work as a congregation of the Lord's body.
                        </p>
                        <div class="mt-8">
                            <a href="{{ route('frontend.signup') }}" class="inline-flex rounded-md bg-blue-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-xs hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">Get started &rarr;</a>
                        </div>
                        <figure class="hidden mt-16 bg-blue-500 text-white py-2 pr-2 pl-6 rounded-2xl text-gray-600">
                            <blockquote class="text-base leading-3">
                                <p>“The administration app makes organizing and maintaining member information almost effortless! I highly recommend this system!”</p>
                            </blockquote>
                            <figcaption class="mt-6 flex gap-x-4 text-sm leading-6">
                                <div><span class="font-semibold text-gray-900">Marilyn R.</span> – Church Secretary</div>
                            </figcaption>
                        </figure>
                    </div>
                </div>
                <img src="/static/frontend/img/screenshots/1-screen-2025.png" alt="Product screenshot" class="w-[48rem] max-w-none border border-gray-300 rounded-xl shadow-2xl ring-gray-400/10 sm:w-[57rem] md:-ml-4 lg:ml-0" width="2432" height="1442">
            </div>
        </div>
    </div>


    <div class="py-12" id="features">
        <div class="max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <p class="text-base leading-6 text-blue-600 font-semibold text-xl tracking-wide uppercase">Features</p>
                <h2 class="mt-3 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-5xl sm:leading-10">
                    <span class="bg-yellow-100 px-2">&nbsp;Lots of them.&nbsp;</span>
                </h2>
                <p class="my-8 max-w-2xl text-xl leading-7 text-gray-600 lg:mx-auto">
                    Enough features to get you started, and lots more to grow into later.
                    <br>
                    <strong>Don't worry</strong> — you don't have to use them <span class="text-blue-500 font-bold">all at once</span>.
                </p>
            </div>

            <div class="mt-10">
                <ul class="lg:grid lg:grid-cols-3 lg:gap-x-10 lg:gap-y-12 sm:grid sm:grid-cols-2 sm:gap-x-10 sm:gap-y-12 grid grid-cols-1 gap-y-5">
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-users fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-gray-200 text-gray-500">
                                        Core Feature
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Directory</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    Member management is at the core of Lightpost, enabling an online directory. Members can also self-manage their own account and information.
                                </p>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-user-hard-hat fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                        Popular
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Member Involvement</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    Allow users to indicate what areas of work they are willing and able to be active in, and increase participation. Enable your deacons to involve all members in service.
                                </p>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-mail-bulk fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                        Popular
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Messaging</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    With email and SMS support, you can send messages to groups, via Lightpost or direct by sending an email or SMS.
                                </p>
                            </div>
                        </div>
                    </li>

                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-abacus fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                        Newly Released
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Worship Assignments</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    Get organized and automated with assigning people who take part in leading worship.
                                </p>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-cloud-download fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-gray-200 text-gray-500">
                                        Core Feature
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Sermons</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    Upload, store, link and serve audio, video and text files to have a full history of sermons and classes.
                                </p>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-user-chart fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                        Add-on
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Attendance</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    Keep track of attendance, with advanced data input features that make quick work of recording attendance.
                                </p>
                            </div>
                        </div>
                    </li>

                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-file-certificate fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">File Sharing</h5>
                                <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-gray-200 text-gray-500">
                                    Core Feature
                                </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-mobile-alt fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Mobile App</h5>
                                <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                    Popular
                                </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-user-tag fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Visitor Tracking</h5>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-sm bg-blue-50 border border-blue-300 text-blue-700">
                                    Premium
                                </span>
                            </div>
                        </div>
                    </li>

                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-comments fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Group Posts</h5>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-sm bg-purple-500 text-white">
                                    Newly Released!
                                </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-calendar-alt fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Calendars</h5>
                                <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-gray-200 text-gray-500">
                                    Core Feature
                                </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-images fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Photos</h5>
                                <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-gray-200 text-gray-500">
                                    Core Feature
                                </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-microphone-alt fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Podcasts</h5>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-sm bg-blue-50 border border-blue-300 text-blue-700">
                                     Premium
                                </span>
                            </div>
                        </div>
                    </li>

                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-hand-holding-usd fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Online Giving</h5>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-sm bg-blue-50 border border-blue-300 text-blue-700">
                                    Premium
                               </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-child fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Child Check-in</h5>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-sm bg-blue-50 border border-blue-300 text-blue-700">
                                    Premium
                               </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-comments fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Emergency Check-in</h5>
                                <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                    Core Feature
                                </span>
                            </div>
                        </div>
                    </li>

                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-hands-usd fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Finance Management</h5>
                                <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                    Newly Released
                                </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-globe fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Website Hosting</h5>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-sm bg-blue-50 border border-blue-300 text-blue-700">
                                    New! Premium
                               </span>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex items-center justify-center w-16 text-black">
                                    <i class="fal fa-music fa-2x" aria-hidden="true"></i>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Song Tracking</h5>
                                <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-orange-200 text-orange-800">
                                    Coming Soon!
                                </span>
                            </div>
                        </div>
                    </li>

                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-file-import fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                    Popular
                                </span>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Import Support</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    We can help you get up and going quickly, with optional data import support. Just provide us with your current directory and we'll get it uploaded.
                                </p>
                            </div>
                        </div>
                    </li>
                    <li class="list-none ml-0">
                        <div class="flex">
                            <div class="shrink-0 text-center">
                                <div class="flex flex-col items-center justify-center w-16 text-black">
                                    <i class="fal fa-people-carry fa-2x" aria-hidden="true"></i>
                                    <span class="inline-flex items-center px-2 py-0.5 mt-2 rounded-sm text-xs font-medium leading-4 bg-green-200 text-green-800">
                                    Popular
                                </span>
                                </div>
                            </div>
                            <div class="ml-4 my-auto">
                                <h5 class="text-2xl leading-6 font-medium text-gray-900">Even More!</h5>
                                <p class="mt-2 text-md leading-5 text-gray-500">
                                    We have an <a href="{{ route('frontend.roadmap') }}" class="text-blue-500 font-weight-bold">extensive roadmap</a> and are working hard to achieve the vision of a
                                    <mark class="bg-yellow-100">church working together effortlessly</mark>.
                                </p>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>


    <div class="max-w-(--breakpoint-xl) mx-auto rounded-0 lg:rounded-xl my-8 lg:my-20 bg-blue-700 py-8 px-4 sm:px-6 lg:py-8 lg:px-8 lg:flex lg:items-center">
        <div class="lg:w-0 lg:flex-1">
            <h2 class="text-2xl lg:text-3xl leading-9 font-bold tracking-tight text-white sm:leading-10">
                <span class="text-3xl lg:text-4xl"><span class="font-light">$</span>0.00</span> &nbsp; <br> Lightpost <em class="font-medium">pays for itself</em>.
            </h2>
            <p class="mt-2 max-w-3xl text-base leading-6 text-gray-100">
                <strong>Proven</strong> to save time, for so many common tasks.

                <a href="{{ route('frontend.signup') }}" class="text-white underline">Signing up</a> can be a <em><strong>no-brainer</strong></em>.
            </p>
        </div>
        <div class="mt-8 lg:mt-0 lg:ml-8">
            <a href="{{ route('frontend.value') }}" class="rounded-md shadow w-1/2 items-center justify-center px-12 py-3 border border-transparent text-base leading-6
                                font-medium rounded-md text-blue-700 hover:text-white bg-white hover:bg-green-500 focus:outline-hidden focus:ring-blue transition duration-150 ease-in-out
                                md:py-4 md:text-lg md:px-10">
                See Value Comparison &rightarrow;
            </a>
        </div>
    </div>



    <div class="bg-white">
        <div class="text-center pt-16 pb-4 mb-8 md:mb-8">
            <p class="text-base leading-6 text-blue-600 font-semibold text-xl tracking-wide uppercase">Mobile Apps</p>
            <h2 class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-black sm:text-5xl sm:leading-10">
                Yup &dash; we have those.
            </h2>
        </div>
        <div class="max-w-(--breakpoint-xl) mx-auto" id="download">
            <div class="max-w-(--breakpoint-xl) mx-auto md:grid lg:grid-cols-2 md:px-6 lg:px-12">
                <div class="flex-row flex mx-auto mb-8 lg:mb-0">
                    <div class="mx-8 sm:mx-16 md:max-w-lg">
                        <img src="{{ asset('static/frontend/img/screenshots/1-mobile-showcase.png') }}" class=" md:mb-0">
                    </div>
                </div>
                <div class="text-center text-2xl my-auto">
                    <p class="mb-6 mx-4">
                        An <strong>incredible</strong> and <strong>growing</strong> number of useful features
                        &dash; <mark class="bg-yellow-100 p-1">right from your phone</mark>.
                    </p>

                    <div class="pb-12 pt-4 flex flex-row justify-center">
                        <a href="https://apps.apple.com/us/app/lightpost-mobile/id1458435817">
                            <img alt="" src="{{ asset('static/frontend/img/badges/apple-app-store.svg') }}" class="h-12">
                        </a>
                        <a href="https://play.google.com/store/apps/details?id=com.tinybitfarm.lightpost&amp;utm_source=lightpost">
                            <img alt="Get it on Google Play" class="h-12 ml-4" src="{{ asset('static/frontend/img/badges/google-play-store.png') }}">
                        </a>
                    </div>

                    <div class="mt-2 lg:mt-0 mx-16">
                        <dl class="text-sm sm:text-base grid grid-cols-1 sm:grid-cols-2 divide-y border border-gray-300 rounded-lg">
                            <div class="text-left p-2 flex flex-row">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Directory</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Birthdays</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Anniversaries</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Baptism Birthdays</div>
                            </div>


                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Calendars</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Member Involvement</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Group Posts</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Online Giving</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Worship Assignments</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Sermons</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Verse of the Day</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Bible Class Registration</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Assignment Management</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Volunteers</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">File Sharing</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">User Account Settings</div>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="mt-14 sm:mt-20 border-t border-gray-200"></div>
        </div>
    </div>

    <div class="mx-auto max-w-(--breakpoint-xl)">
        <div class="text-center pt-8 pb-6 sm:pb-0">
            <p class="text-base leading-6 text-blue-600 font-semibold text-xl tracking-wide uppercase">Testimonials</p>
            <h2 class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-black sm:text-5xl">
                Loved by Members
            </h2>
        </div>
        <div class="mx-auto sm:mt-12 grid max-w-2xl grid-cols-1 grid-rows-1 gap-8 text-sm/6 text-gray-900 sm:grid-cols-2 xl:mx-0 xl:max-w-none xl:grid-flow-col xl:grid-cols-4">
            <div class="space-y-8 mx-4 sm:mx-0 xl:contents xl:space-y-0">
                <div class="space-y-8 xl:row-span-2">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“The administration app makes organizing and maintaining member information almost effortless!
                               I highly recommend this system!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Marilyn R.</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use it and love all the features!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Jacob M.</div>
                                <div class="text-gray-600">Jerseyville, Illinois</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“Thanks for ALL you do, brother! Our congregation LOVES Lightpost!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">John W.</div>
                                <div class="text-gray-600">San Marcos, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“If you’re looking for a church communication &amp; management app that’s VERY user friendly, check out Lighpost!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Kolton B.</div>
                                <div class="text-gray-600">San Marcos, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“If you are a secretary for your congregation, you definitely want this app. Great way to keep the entire congregation informed about important events, update on sick members, the daily list of birthdays and upcoming birthdays… all this and having a membership directory in hand at all times.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Karen S.</div>
                                <div class="text-gray-600">San Marcos, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
                <div class="space-y-8 xl:row-start-1">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“I love Lightpost's various efficient tools that can be used in so many church service areas, while letting those who prefer the “old way” to continue with their preferred way.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Church Member</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We have used [other tools] and investigated many others, and most contained features we didn't need and weren't willing to pay for. This is designed specifically with the Lord's church in mind. We love it!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Cindy P.</div>
                                <div class="text-gray-600">Knoxville, Tennessee</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“This app has been way easier on the older generation. The admin can set the password for the member if that becomes necessary. We also uploaded handouts and bulletins to the file section so people would want to use it.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">James E.</div>
                                <div class="text-gray-600">Gonzales, LA</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“Really liking this app!!!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Phyllis</div>
                                <div class="text-gray-600">Austin, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
            </div>
            <div class="space-y-8 mx-4 sm:mx-0 xl:contents xl:space-y-0">
                <div class="space-y-8 xl:row-start-1">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“If you haven’t downloaded and used the Lightpost app, you are missing out!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Church Member</div>
                                <div class="text-gray-600">Rosenburg, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use this at [our congregation]. It's an awesome app.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Michael T.</div>
                                <div class="text-gray-600">Cookeville, Tennessee</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“For office staff: this is an excellent church management tool. It will generate reports, has a calendar, generates birthdays and anniversaries, has involvement areas for members in that they can choose areas they will serve, has a prayer request section, and more.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Cindy P.</div>
                                <div class="text-gray-600">Knoxville, Tennessee</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“s a deacon in our congregation, I find it invaluable in finding volunteers to help with tasks and to communicate with other deacons about upcoming events, not to mention contacting members by quickly finding their emails and phone numbers.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Larry J.</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
                <div class="space-y-8 xl:row-span-2">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use Lightpost at [our congregation] and we love it. Drew Johnston is great to work with!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Caleb R.</div>
                                <div class="text-gray-600">Roanoke, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We have found the interface to be intuitive for our everyday needs!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Mickie W.</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use it and we love it!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">James E.</div>
                                <div class="text-gray-600">Gonzales, LA</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“Glad we have it at Westisde, Round Rock! So efficient and handy for member use.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Lori H.</div>
                                <div class="text-gray-600">Round Rock, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“It is an awesome app for the church!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Caleb M.</div>
                                <div class="text-gray-600">Austin, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-8 bg-blue-700">
        <div class="max-w-(--breakpoint-xl) mx-auto py-8 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center">
            <div class="lg:w-0 lg:flex-1">
                <h2 class="text-3xl leading-9 font-extrabold tracking-tight text-white sm:text-4xl sm:leading-10">
                    Sign-up for our newsletter
                </h2>
                <p class="mt-3 max-w-3xl text-lg leading-6 text-gray-300">
                    Get the latest news, updates, advice articles, helpful tips & more.
                </p>
            </div>
            <div class="mt-8 lg:mt-0 lg:ml-8">
                <div>
                    <form
                            action="https://buttondown.email/api/emails/embed-subscribe/lightpost"
                            method="post"
                            target="popupwindow"
                            onsubmit="window.open('https://buttondown.email/lightpost', 'popupwindow')"
                            class="sm:flex embeddable-buttondown-form"
                    >
                        <input type="hidden" name="tag" value="Lightpost End Users"/>
                        <div class="lg:w-full">
                            <input type="email" value="" name="email" id="bd-email" aria-label="Email address" required class="appearance-none w-full px-5 py-3 border border-transparent text-base leading-6 rounded-md text-gray-900 bg-white placeholder-gray-500 focus:outline-hidden focus:placeholder-gray-400 transition duration-150 ease-in-out sm:max-w-xs" placeholder="Enter your email"/>
                        </div>
                        <div class="mt-3 rounded-md shadow-sm sm:mt-0 sm:ml-3 sm:shrink-0">
                            <input class="w-full flex items-center justify-center px-5 py-3 border border-transparent text-base leading-6 font-medium rounded-md text-white hover:text-blue-900 bg-blue-900 hover:bg-blue-300 focus:outline-hidden focus:bg-blue-400 transition duration-150 ease-in-out" type="submit" value="Subscribe"/>
                        </div>
                    </form>
                </div>
                <p class="mt-3 text-sm leading-5 text-gray-300">
                    We care about the protection of your data. Read our
                    <a href="{{ route('frontend.privacy-policy') }}" class="text-white font-medium underline">
                        Privacy Policy.
                    </a>
                </p>
            </div>
        </div>
    </div>

    @include('frontend.layouts.footer')

@endsection

@push('scripts')



@endpush