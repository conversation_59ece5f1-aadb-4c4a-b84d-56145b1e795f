@extends('frontend.layouts._app')

@section('title', 'The Church App that helps focus on spiritual matters - ' . config('app.name'))

@section('content')

    @include('frontend.layouts._header')

    <div>
        <div class="mx-auto max-w-7xl px-6 lg:items-center lg:px-8">
            <div class="lg:mx-0 mt-2 lg:mt-20 text-center">
                <h1 class="mx-auto max-w-2xl text-5xl font-semibold tracking-tight text-gray-900 sm:text-6xl">
                    <span class="text-blue-900">Connect, Communicate</span>
                    <span class="text-blue-600"><span class="hidden sm:inline-block">and</span> <span class="whitespace-nowrap">Grow Together</span></span>
                </h1>
                <p class="mx-auto max-w-xl mt-6 text-center text-lg sm:text-xl font-light text-gray-600">
                    The <span class="font-semibold">simple church app</span> that
                    helps your whole congregation
                    <span class="font-semibold">communicate</span> and
                    <span class="font-semibold">work together</span> <em>effortlessly</em>.
                </p>
                <div class="mt-6 w-full flex justify-center lg:justify-center">
                    <div class="relative group cursor-pointer inline-block">
                        <div class="absolute -inset-1 bg-linear-to-r from-blue-500 to-orange-500 rounded-full blur-sm opacity-15 group-hover:opacity-80 transition duration-1000 group-hover:duration-400"></div>
                        <div class="relative px-4 py-1 bg-white ring-1 ring-gray-900/5 rounded-full leading-none flex items-center justify-center">
                                    <span class="text-sm hover:cursor-pointer text-orange-500 text-center">
                                        Built exclusively for the churches of Christ
                                    </span>
                        </div>
                    </div>
                </div>
                <div class="mt-8 flex justify-center items-center gap-x-4">
                    <a href="{{ route('frontend.signup') }}"
                       class="blaze-button">
                        Get Started
                    </a>
                    <a href="{{ route('frontend.pricing') }}"
                       class="text-base px-3.5 py-2.5 bg-white border border-gray-300 hover:border-blue-500 rounded-lg font-normal text-gray-600 hover:text-blue-500">
                        View Pricing
                    </a>
                </div>
            </div>

            <div class="mt-12 sm:mt-18 pb-12 max-w-4xl md:mx-auto">
                <img alt="" class="" src="{{ asset('static/frontend/img/screenshots/2025-mobile-showcase-1.png') }}"/>
            </div>
        </div>
    </div>

    <div class="bg-gray-700">
        <div class="max-w-(--breakpoint-xl) mx-auto py-8 px-4 sm:py-12 sm:px-6 lg:px-8 lg:py-12">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl leading-9 font-bold text-white sm:text-4xl sm:leading-10">
                    Trusted by churches church near &amp; yonder
                </h2>
                <p class="mt-3 text-lg leading-7 text-gray-200 sm:mt-4">
                    {{ config('app.name') }} enables <em class="text-yellow-100">communication</em>, reporting, management &amp; <em class="text-yellow-100"> more</em>.
                </p>
            </div>
            <div class="mt-8 text-center sm:max-w-4xl mx-auto grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-8">
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        24+
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Features
                    </p>
                </div>
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        {{ round((395000 + $mail_count) / 1000000, 2) }}m+
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Messages Sent
                    </p>
                </div>
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        24/7
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Availability
                    </p>
                </div>
                <div>
                    <p class="text-2xl sm:text-4xl leading-none font-extrabold text-white">
                        15k+
                    </p>
                    <p class="mt-2 text-lg leading-6 font-normal text-gray-200">
                        Notifications Daily
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="overflow-hidden bg-white py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 lg:items-start">
                <div class="lg:pr-4 lg:pt-4 my-auto">
                    <div class="lg:max-w-lg">
                        <p class="mt-2 text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">
                            Manage faster with the <span class="text-blue-600">best workflow</span>
                        </p>
                        <p class="mt-6 text-lg text-gray-600">
                            {{ config('app.name') }} works hard to make your work easy. One simple place to update data &amp; communicate with your congregation.
                        </p>
                        <p class="mt-6 text-lg text-gray-600">
                            {{ config('app.name') }} is built specifically to help the churches of Christ work as a congregation of the Lord's body.
                        </p>
                        <div class="mt-8">
                            <a href="{{ route('frontend.signup') }}" class="blaze-button">Get started &rarr;</a>
                        </div>
                        <figure class="hidden mt-16 bg-blue-500 text-white py-2 pr-2 pl-6 rounded-2xl text-gray-600">
                            <blockquote class="text-base leading-3">
                                <p>"The administration app makes organizing and maintaining member information almost effortless! I highly recommend this system!"</p>
                            </blockquote>
                            <figcaption class="mt-6 flex gap-x-4 text-sm leading-6">
                                {{--                                <img src="https://images.unsplash.com/photo-1509783236416-c9ad59bae472?ixlib=rb-=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=8&w=1024&h=1024&q=80" alt="" class="h-6 w-6 flex-none rounded-full">--}}
                                <div><span class="font-semibold text-gray-900">Marilyn R.</span> – Church Secretary</div>
                            </figcaption>
                        </figure>
                    </div>
                </div>
                <img src="/static/frontend/img/screenshots/1-screen-2025.png" alt="Product screenshot" class="w-[48rem] max-w-none rounded-xl shadow-xl ring-1 ring-gray-400/10 sm:w-[57rem] md:-ml-4 lg:ml-0" width="2432" height="1442">
            </div>
        </div>
    </div>




    <div class="py-12 bg-white" id="features">
        <div class="max-w-(--breakpoint-xl) mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center mx-auto">
                <div class="flex justify-center">
                    <div class="inline-flex px-4 py-1 rounded-full bg-blue-50 border border-gray-300 text-base shadow-lg text-gray-700 font-normal">
                        <div class="flex items-center gap-x-1">
                            <x-heroicon-m-sparkles class="w-5"/>
                            <span>Our Features</span>
                        </div>
                    </div>
                </div>
                <p class="mt-6 text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">
                    Checkout our features
                    <br>
                    <span class="text-blue-600">Lots of them</span>
                </p>
                <p class="my-8 max-w-2xl text-xl leading-7 text-gray-600 lg:mx-auto">
                    Enough features to get you started, and lots more to grow into later.
                    <br>
                    <strong>Don't worry</strong> — you don't have to use them <span class="text-blue-500 font-bold">all at once</span>.
                </p>
            </div>
        </div>
    </div>

    <div class="max-w-6xl mx-4 lg:mx-auto divide-y divide-gray-200 border border-gray-300 overflow-hidden rounded-lg bg-gray-200 shadow-sm sm:grid sm:grid-cols-3 sm:gap-px sm:divide-y-0">
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-users" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Directory
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Member management is at the core of {{ config('app.name') }}, enabling an online directory. Members can also self-manage their own account and information.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-mail-bulk" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Messaging
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    With <strong>4 ways</strong> to communicate, you can use <strong>e-mail</strong>, <strong>SMS</strong>, <strong>voice calls</strong> or <strong>in-app messaging</strong> to communicate with groups.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-hard-hat" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Member Involvement Areas
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Allow members to indicate what areas of work they are willing to be active in, and increase participation.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-abacus" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Worship Assignments
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Get organized and automated with assigning people who take part in all kinds of assignments.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-cloud-arrow-down" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Sermons / Lessons
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Upload, store, link and serve audio, video and text files to have a full history of sermons, classes, lessons, etc.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-chart-user" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Attendance Tracking
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Keep track of user attendance, with advanced data input features, visitor QR codes, general attendance and more.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-print" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Printable Directory
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Print a directory of your congregation, with or without photos, contact information and more.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-mobile-alt" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Mobile Apps
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Instant access to your directory.
                    Our free mobile apps let users find members and communicate instantly with mobile notifications and group chats.
                    Available on Apple App and Google Play stores.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-user-tag" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Visitor Tracking
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Be intentional about follow-ups and out reach for those that have shown interest.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-cloud-download" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    File Sharing
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Upload, store, and share files with your whole congregation. Organize them into folders and restrict access on certain conditions.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-user-chart" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Online Member Website
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    The same functionality as the mobile app, but accessible via any web browser.
                    The online members' website works just like the mobile app but is viewable in any modern browser and device at our secure membership site.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-comments" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Group Posts
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    In-app messaging with mobile notifications! Group Posts is a great way to quickly and easily communicate with each other directly in the app.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-calendar-alt" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Calendars
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    A whole calendar system - built-in. Create multiple calendars and group specific calendars.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-images" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Photos
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Upload photos freely in Group Posts! Also, directory photos can be uploaded by individual church members.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-praying-hands" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Prayer List
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Keep up with the latest updates to on-going prayer requests in your church.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-globe" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Website Hosting
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Simple, self-managed effective online presence so you can be discovered in your local area.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-child" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Child Check-in
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Help keep up with child drop-offs and pick-ups with minimal fuss.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-microphone-alt" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Podcasts
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Publish and manage your podcasts right in Lightpost. Simple, managed publishing to major podcast platforms.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-exclamation-triangle" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Emergency Check-in
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Major power outage? Severe weather? Notify, request help and respond to requests, directly in the app.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-chart-area" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Reports
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    Birthday, anniversary and directory reports.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-hand-holding-heart" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Online Giving
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Built-in for members to give contributions.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-hands-holding-dollar" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Financial Management
                </h3>
                <p class="my-2 text-sm text-gray-500">
                    Track more than just online giving. Keep up with budgets throughout the year.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <div class="flex flex-row justify-between">
                <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                    <i class="far fa-file-import" aria-hidden="true"></i>
                </span>
                <span class="my-auto bg-purple-50 px-2 py-1 rounded-full border border-purple-300 text-purple-500 text-xs">One-time Premium</span>
            </div>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Import Support
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    We can help you get up and going quickly, with optional data import support. Just provide us with your current directory and we'll get it uploaded.
                </p>
            </div>
        </div>
        <div class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500">
            <span class="inline-flex rounded-lg bg-teal-50 p-3 text-teal-700 ring-4 ring-white">
                <i class="far fa-people-carry" aria-hidden="true"></i>
            </span>
            <div class="mt-4">
                <h3 class="text-xl font-semibold text-gray-700">
                    Even More!
                </h3>
                <p class="mt-2 text-sm text-gray-500">
                    We have an <a href="{{ route('frontend.roadmap') }}" class="text-blue-500 font-weight-bold">extensive roadmap</a> and are working hard to achieve the vision of a
                    <mark class="bg-yellow-100">church working together effortlessly</mark>.
                </p>
            </div>
        </div>
    </div>



    <div class="max-w-(--breakpoint-xl) mx-auto rounded-0 lg:rounded-xl my-8 lg:my-20 bg-blue-700 py-8 px-4 sm:px-6 lg:py-8 lg:px-8 lg:flex lg:items-center">
        <div class="lg:w-0 lg:flex-1">
            <h2 class="text-2xl lg:text-3xl leading-9 font-bold tracking-tight text-white sm:leading-10">
                <span class="text-3xl lg:text-4xl"><span class="font-light">$</span>0.00</span> &nbsp; <br> {{ config('app.name') }} <em class="font-medium">pays for itself</em>.
            </h2>
            <p class="mt-2 max-w-3xl text-base leading-6 text-gray-100">
                <strong>Proven</strong> to save time, for so many common tasks.

                <a href="{{ route('frontend.signup') }}" class="text-white underline">Signing up</a> can be a <em><strong>no-brainer</strong></em>.
            </p>
        </div>
        <div class="mt-8 lg:mt-0 lg:ml-8">
            <a href="{{ route('frontend.value') }}" class="shadow w-1/2 items-center justify-center px-12 py-3 border border-transparent text-base leading-6
                                font-medium rounded-md text-blue-700 hover:text-white bg-white hover:bg-green-500 focus:outline-hidden focus:ring-blue transition duration-150 ease-in-out
                                md:py-4 md:text-lg md:px-10">
                See Value Comparison &rightarrow;
            </a>
        </div>
    </div>



    <div class="bg-white">
        <div class="text-center pt-16">
            <div class="flex justify-center">
                <div class="inline-flex px-4 py-1 rounded-full bg-blue-50 border border-gray-300 text-base shadow-lg text-gray-700 font-normal">
                    <div class="flex items-center gap-x-1">
                        <x-heroicon-m-sparkles class="w-5"/>
                        <span>Mobile App</span>
                    </div>
                </div>
            </div>
            <p class="mt-6 text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">
                Our mobile app
                <br>
                <span class="text-blue-600">in both app stores</span>
            </p>
            <p class="my-4 max-w-4xl text-lg leading-7 text-gray-600 lg:mx-auto">
                An <strong>incredible</strong> and <strong>growing</strong> number of useful features
                &dash; <mark class="bg-yellow-100 p-1">right from your phone</mark>.
            </p>
        </div>
        <div class="pb-12 pt-4 flex flex-row justify-center">
            <a href="https://apps.apple.com/us/app/lightpost-mobile/id1458435817">
                <img alt="" src="{{ asset('static/frontend/img/badges/apple-app-store.svg') }}" class="h-8 sm:h-12">
            </a>
            <a href="https://play.google.com/store/apps/details?id=com.tinybitfarm.lightpost&amp;utm_source=lightpost">
                <img alt="Get it on Google Play" class="h-8 sm:h-12 ml-4" src="{{ asset('static/frontend/img/badges/google-play-store.png') }}">
            </a>
        </div>
        <div class="mt-12 sm:mt-18 lg:mt-0 lg:shrink-0 lg:grow"
             x-data="{
                            activeSlide: 1,
                            slides: [1, 2, 3, 4, 5, 6, 7],
                            timer: null,
                            touchStartX: 0,
                            touchEndX: 0,
                            startTimer() {
                                this.timer = setInterval(() => {
                                    this.activeSlide = this.activeSlide >= 7 ? 1 : this.activeSlide + 1;
                                }, 5000);
                            },
                            stopTimer() {
                                if (this.timer) clearInterval(this.timer);
                            },
                            setSlide(slide) {
                                this.activeSlide = slide;
                                this.stopTimer();
                                this.startTimer();
                            },
                            handleTouchStart(event) {
                                this.touchStartX = event.touches[0].clientX;
                                this.stopTimer();
                            },
                            handleTouchMove(event) {
                                this.touchEndX = event.touches[0].clientX;
                            },
                            handleTouchEnd() {
                                const diffX = this.touchStartX - this.touchEndX;

                                // Require at least 50px swipe
                                if (Math.abs(diffX) > 50) {
                                    if (diffX > 0) {
                                        // Swiped left - next slide
                                        this.activeSlide = this.activeSlide >= 7 ? 1 : this.activeSlide + 1;
                                    } else {
                                        // Swiped right - previous slide
                                        this.activeSlide = this.activeSlide <= 1 ? 7 : this.activeSlide - 1;
                                    }
                                }

                                this.startTimer();
                            },
                            init() {
                                this.startTimer();
                            }
                         }">
            <div class="relative max-w-[320px] mx-auto">
                <!-- Phone Frame -->
                <div class="relative rounded-[40px] bg-gray-300 p-2">
                    <!-- Screen Container -->
                    <div class="relative overflow-hidden rounded-[32px] bg-white aspect-[9/19.5]"
                         @touchstart="handleTouchStart($event)"
                         @touchmove="handleTouchMove($event)"
                         @touchend="handleTouchEnd()">
                        <!-- Slides Container -->
                        <div class="absolute inset-0 flex transition-transform duration-500 ease-in-out"
                             :style="{ transform: `translateX(-${(activeSlide - 1) * 100}%)` }">
                            <!-- Individual Slides -->
                            @for($i = 1; $i <= 7; $i++)
                                <div class="shrink-0 w-full h-full">
                                    <img src="/static/frontend/img/screenshots/{{ $i }}-phone-2023.png"
                                         alt="App screenshot {{ $i }}"
                                         class="w-full h-full object-cover"
                                         loading="{{ $i === 1 ? 'eager' : 'lazy' }}"
                                         draggable="false"/>
                                </div>
                            @endfor
                        </div>
                    </div>
                </div>

                <!-- Navigation Dots -->
                <div class="flex items-center justify-center mt-4 space-x-2">
                    <template x-for="slide in slides" :key="slide">
                        <button
                                class="w-2 h-2 rounded-full transition-colors duration-200 ease-out hover:bg-blue-400"
                                :class="{
                                            'bg-blue-600': activeSlide === slide,
                                            'bg-gray-300': activeSlide !== slide
                                        }"
                                @click="setSlide(slide)"
                                :aria-label="`Go to slide ${slide}`">
                        </button>
                    </template>
                </div>
            </div>
        </div>
        <div class="max-w-(--breakpoint-xl) mx-auto" id="download">
            <div class="max-w-(--breakpoint-xl) mx-auto md:grid md:grid-cols-1 md:px-6 lg:px-12">
                <div class="text-center text-2xl my-auto">


                    <div class="hidden mt-2 lg:mt-0 mx-24">
                        <dl class="text-sm sm:text-base grid grid-cols-1 sm:grid-cols-3 divide-y border border-gray-300 rounded-lg">
                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Directory</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Birthdays</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Anniversaries</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Baptism Birthdays</div>
                            </div>


                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Calendars</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Group Posts</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Prayer List</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Verse of the Day</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">File Sharing</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">User Account Settings</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Emergency Check-in</div>
                            </div>

                            <div class="text-left p-2 flex flex-row align-text-top">
                                <x-heroicon-m-check-circle class="mr-1 h-6 w-6 text-green-500"/>
                                <div class="my-auto sm:my-0">Photo Directory</div>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="mt-14 sm:mt-20 border-t border-gray-200"></div>
        </div>
    </div>

    <div class="bg-white  max-w-(--breakpoint-xl) mx-auto">
        <div class="text-center pt-8 pb-4">
            <div class="flex justify-center">
                <div class="inline-flex px-4 py-1 rounded-full bg-blue-50 border border-gray-300 text-base shadow-lg text-gray-700 font-normal">
                    <div class="flex items-center gap-x-1">
                        <x-heroicon-m-sparkles class="w-5"/>
                        <span>Testimonials</span>
                    </div>
                </div>
            </div>
            <p class="mt-6 text-3xl font-semibold tracking-tight text-gray-900 sm:text-4xl">
                Hear why members love
                <br>
                <span class="text-blue-600">Lightpost</span>
            </p>
        </div>
        <div class="mx-auto sm:mt-12 grid max-w-2xl grid-cols-1 grid-rows-1 gap-8 text-sm/6 text-gray-900 sm:grid-cols-2 xl:mx-0 xl:max-w-none xl:grid-flow-col xl:grid-cols-4">
            <div class="space-y-8 mx-4 sm:mx-0 xl:contents xl:space-y-0">
                <div class="space-y-8 xl:row-span-2">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“The administration app makes organizing and maintaining member information almost effortless!
                               I highly recommend this system!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Marilyn R.</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use it and love all the features!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Jacob M.</div>
                                <div class="text-gray-600">Jerseyville, Illinois</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“Thanks for ALL you do, brother! Our congregation LOVES Lightpost!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">John W.</div>
                                <div class="text-gray-600">San Marcos, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“If you’re looking for a church communication &amp; management app that’s VERY user friendly, check out Lighpost!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Kolton B.</div>
                                <div class="text-gray-600">San Marcos, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“If you are a secretary for your congregation, you definitely want this app. Great way to keep the entire congregation informed about important events, update on sick members, the daily list of birthdays and upcoming birthdays… all this and having a membership directory in hand at all times.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Karen S.</div>
                                <div class="text-gray-600">San Marcos, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
                <div class="space-y-8 xl:row-start-1">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“I love Lightpost's various efficient tools that can be used in so many church service areas, while letting those who prefer the “old way” to continue with their preferred way.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Church Member</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We have used [other tools] and investigated many others, and most contained features we didn't need and weren't willing to pay for. This is designed specifically with the Lord's church in mind. We love it!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Cindy P.</div>
                                <div class="text-gray-600">Knoxville, Tennessee</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“This app has been way easier on the older generation. The admin can set the password for the member if that becomes necessary. We also uploaded handouts and bulletins to the file section so people would want to use it.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">James E.</div>
                                <div class="text-gray-600">Gonzales, LA</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“Really liking this app!!!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Phyllis</div>
                                <div class="text-gray-600">Austin, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
            </div>
            <div class="space-y-8 mx-4 sm:mx-0 xl:contents xl:space-y-0">
                <div class="space-y-8 xl:row-start-1">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“If you haven’t downloaded and used the Lightpost app, you are missing out!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Church Member</div>
                                <div class="text-gray-600">Rosenburg, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use this at [our congregation]. It's an awesome app.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Michael T.</div>
                                <div class="text-gray-600">Cookeville, Tennessee</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“For office staff: this is an excellent church management tool. It will generate reports, has a calendar, generates birthdays and anniversaries, has involvement areas for members in that they can choose areas they will serve, has a prayer request section, and more.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Cindy P.</div>
                                <div class="text-gray-600">Knoxville, Tennessee</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“s a deacon in our congregation, I find it invaluable in finding volunteers to help with tasks and to communicate with other deacons about upcoming events, not to mention contacting members by quickly finding their emails and phone numbers.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Larry J.</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
                <div class="space-y-8 xl:row-span-2">
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use Lightpost at [our congregation] and we love it. Drew Johnston is great to work with!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Caleb R.</div>
                                <div class="text-gray-600">Roanoke, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We have found the interface to be intuitive for our everyday needs!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Mickie W.</div>
                                <div class="text-gray-600">Katy, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“We use it and we love it!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">James E.</div>
                                <div class="text-gray-600">Gonzales, LA</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“Glad we have it at Westisde, Round Rock! So efficient and handy for member use.”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Lori H.</div>
                                <div class="text-gray-600">Round Rock, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                    <figure class="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5 border border-gray-300">
                        <blockquote class="text-gray-900 italic">
                            <p>“It is an awesome app for the church!”</p>
                        </blockquote>
                        <figcaption class="mt-2 flex items-center gap-x-4">
                            <div>
                                <div class="font-semibold">Caleb M.</div>
                                <div class="text-gray-600">Austin, Texas</div>
                            </div>
                        </figcaption>
                    </figure>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-blue-700 mt-12">
        <div class="max-w-(--breakpoint-xl) mx-auto py-8 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center">
            <div class="lg:w-0 lg:flex-1">
                <h2 class="text-3xl leading-9 font-extrabold tracking-tight text-white sm:text-4xl sm:leading-10">
                    Sign-up for our newsletter
                </h2>
                <p class="mt-3 max-w-3xl text-lg leading-6 text-gray-300">
                    Get the latest news, updates, advice articles, helpful tips & more.
                </p>
            </div>
            <div class="mt-8 lg:mt-0 lg:ml-8">
                <div>
                    <form
                            action="https://buttondown.email/api/emails/embed-subscribe/churchdirectory"
                            method="post"
                            target="popupwindow"
                            onsubmit="window.open('https://buttondown.email/churchdirectory', 'popupwindow')"
                            class="sm:flex embeddable-buttondown-form"
                    >
                        <input type="hidden" name="tag" value="{{ config('app.name') }} End Users"/>
                        <div class="lg:w-full">
                            <input type="email" value="" name="email" id="bd-email" aria-label="Email address" required class="appearance-none w-full px-5 py-3 border border-transparent text-base leading-6 rounded-md text-gray-900 bg-white placeholder-gray-500 focus:outline-hidden focus:placeholder-gray-400 transition duration-150 ease-in-out sm:max-w-xs" placeholder="Enter your email"/>
                        </div>
                        <div class="mt-3 rounded-md shadow-sm sm:mt-0 sm:ml-3 sm:shrink-0">
                            <input class="w-full flex items-center justify-center px-5 py-3 border border-transparent text-base leading-6 font-medium rounded-md text-white hover:text-blue-900 bg-blue-900 hover:bg-blue-300 focus:outline-hidden focus:bg-blue-400 transition duration-150 ease-in-out" type="submit" value="Subscribe"/>
                        </div>
                    </form>
                </div>
                <p class="mt-3 text-sm leading-5 text-gray-300">
                    We care about the protection of your data. Read our
                    <a href="https://church.work/privacy-policy" target="_blank" class="text-white font-medium underline">
                        Privacy Policy.
                    </a>
                </p>
            </div>
        </div>
    </div>

    @include('frontend.layouts._footer')

@endsection