@extends('frontend.layouts._app')

@section('title', 'About - Lightpost')

@section('content')

    @include('frontend.layouts._header')

    <div class="relative pt-16 pb-6 overflow-hidden">
        <div class="relative px-4 sm:px-6 lg:px-8">
            <div class="text-lg max-w-4xl mx-auto text-center">
                <h1>
                    <span class="block text-base text-center text-blue-600 font-semibold tracking-wide uppercase">About</span>
                    <span class="mt-2 block text-3xl text-center leading-8 font-extrabold tracking-tight text-gray-800 sm:text-4xl">A tiny bit of history.</span>
                </h1>
                <p class="mt-8 text-lg text-gray-500 leading-8">
                    Lightpost began in 2009 as an unnamed custom web application built for the congregation in Katy, Texas.
                    The church had a deep and growing need to <span class="bg-yellow-100 px-1">centralize and organize data</span> for the Lord's church,
                    to stop confusion from outdated spreadsheets and emails being passed around.
                </p>
                <p class="mt-8 text-lg text-gray-500 leading-8">
                    Beginning in 2016, this unnamed application was completely rewritten into <strong>Lightpost</strong>,
                    with support for other congregations, a <span class="bg-yellow-100 px-1">mobile app</span> and new
                    <span class="bg-yellow-100 px-1">communication</span> features.
                    After much testing, a quiet launch, and success at Katy &dash; Lightpost was <em>officially launched</em> in 2018.
                </p>
            </div>
            <div class="pt-12 px-6 max-w-5xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg border border-gray-200 lg:flex lg:justify-between text-center py-2 lg:py-6 px-16 mx-auto mx:mx-0">
                    <div class="py-4 lg:py-0">
                        <div class="font-bold tracking-tight text-4xl text-orange-400">2016</div>
                        <div class="mt-2 text-base text-grey-700 uppercase">Founded</div>
                    </div>
                    <div class="border-b-2 lg:border-r-0 lg:border-r-2 border-grey-300"></div>
                    <div class="py-4 lg:py-0">
                        <div class="font-bold tracking-tight text-4xl text-orange-400">1</div>
                        <div class="mt-2 text-base text-grey-700 uppercase">Employee</div>
                    </div>
                    <div class="border-b-2 lg:border-r-0 lg:border-r-2 border-grey-300"></div>
                    <div class="py-4 lg:py-0">
                        <div class="font-bold tracking-tight text-4xl text-orange-400">15k+</div>
                        <div class="mt-2 text-base text-grey-700 uppercase">Notifications Per Day</div>
                    </div>
                    <div class="border-b-2 lg:border-r-0 lg:border-r-2 border-grey-300"></div>
                    <div class="py-4 lg:py-0">
                        <div class="font-bold tracking-tight text-4xl text-orange-400">~50k</div>
                        <div class="mt-2 text-base text-grey-700 uppercase">emails / month</div>
                    </div>
                    <div class="border-b-2 lg:border-r-0 lg:border-r-2 border-grey-300"></div>
                    <div class="py-4 lg:py-0">
                        <div class="font-bold tracking-tight text-4xl text-orange-400">24+</div>
                        <div class="mt-2 text-base text-grey-700 uppercase">features</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="py-12">
        <div class="sm:flex md:mx-auto md:max-w-4xl md:px-8 gap-12">

            <div class="">
                <img class="rounded-2xl shadow-lg overflow-hidden inset-0 mx-auto w-1/2 sm:w-full object-cover" src="/img/drew-photo-2.jpg" alt="">
            </div>

            <div class="mx-auto max-w-md px-4 sm:max-w-4xl sm:px-6 md:px-0 mt-6 sm:mt-0">
                <!-- Content area -->
                <div class="">
                    <h2 class="text-3xl text-gray-900 font-extrabold tracking-tight sm:text-4xl">
                        Tiny Business
                    </h2>
                    <div class="font-sm font-medium"><a href="https://tinybit.farm">Tiny Bit Farm LLC</a></div>
                    <div class="mt-6 text-gray-500 space-y-6">
                        <p>
                            Tiny Bit Farm is owned and operated by me &dash; Drew Johnston &dash; a member and deacon at the Lord's church in Katy, Texas.
                            My wife and I attend the church of Christ in Katy, Texas, along with our three kiddos.
                        </p>
                        <p class="text-base leading-7">
                            I am thrilled to put my 20+ years of experience building things with technology to use; helping the church communicate and function more efficiently with Lightpost.
                        </p>
                        <p class="text-base leading-7">
                            My hope is that Lightpost proves so incredibly useful to the Lord's church, that it will become entirely self-sustaining;
                            allowing me to not only support my family, but to pursue other technology projects to further assist the Lord's church, and the spreading of the Gospel.
                        </p>
                        <p class="text-base leading-7">
                            I have no <a href="{{ route('frontend.roadmap') }}">shortage of ideas</a> that I only need, and greatly desire, the time to create.
                        </p>
                        <p class="text-base leading-7">

                        </p>
                    </div>
                </div>


            </div>
        </div>
    </div>

    @include('frontend.layouts._footer')

@endsection

@push('scripts')



@endpush