<script type="text/javascript">
    function loadScript(url, callback) {
        {{-- // Adding the script tag to the head as suggested before --}}
        var head = document.head;
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        {{-- // Then bind the event to the callback function. --}}
                {{-- // There are several events for cross browser compatibility. --}}
            script.onreadystatechange = callback;
        script.onload = callback;

        {{-- // Fire the loading --}}
        head.appendChild(script);
    }

    var LPCode = function () {
        var calendarEl = document.getElementById('lightpost-public-calendars');

        var isLikelyDesktop = window.innerWidth >= 765;

        var calendar = new FullCalendar.Calendar(calendarEl, {
            schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
            {{--timeZone: '{{ Auth::user()->account->timezone }}',--}}
            headerToolbar: {
                left: isLikelyDesktop ? 'prev,next today' : 'prev,next',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,listMonth'
            },
            titleFormat: {
                year: isLikelyDesktop ? 'numeric' : '2-digit', month: isLikelyDesktop ? 'long' : 'short'  // Example: "Feb 2025" instead of "February 2025"
            },
            timeZone: 'local',
            initialView: 'listMonth', /* isLikelyDesktop ? 'dayGridMonth' : 'listMonth', */
            eventSources: [
                    @foreach($all_calendars as $calendar)
                {
                    id: '{{ $calendar->ulid }}',
                    url: '{{ route('public.calendars.full_calendar_feed', ['calendar_ulid' => $calendar->ulid]) }}',
                    className: 'cal_id_{{ $calendar->ulid }}',
                    method: 'GET',
                    failure: function () {
                        console.log('There was an error while fetching events!');
                    },
                    color: '#{{ $calendar->getBackgroundColor() }}',   // an option!
                    textColor: '#{{ $calendar->getFontColor() }}', // an option!
                    visible: false
                },
                @endforeach
            ],
            eventClick: function (info) {
                info.jsEvent.preventDefault(); // don't let the browser navigate
                console.log('clicked');
            }
        });

        calendar.render();
    }

    {{-- Load our static assets and then run our code. --}}
    loadScript('https://static.lightpost.app/public/calendars/js/fullcalendar.global.min.js', LPCode);
</script>