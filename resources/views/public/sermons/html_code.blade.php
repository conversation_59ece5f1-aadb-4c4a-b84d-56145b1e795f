<div class="max-w-5xl mx-auto mb-8">
    <form action="" method="get">
        <input name="search_term" type="text"
               class="border border-gray-300 rounded-sm p-2 placeholder-gray-400 mb-2 w-64"
               id="lp-search-term"
               placeholder="Search by title or speaker..."/>
        <button class="bg-blue-600 text-white px-4 py-2 rounded-sm">Search</button>
    </form>
    <div class="border border-gray-300 rounded-sm">
        <table class="w-100" style="width:100%">
            <thead>
            <tr class="bg-gray-100 rounded-t">
                <th class="p-2 pl-4 text-left uppercase rounded-tl">Title</th>
                <th class="p-2 uppercase rounded-tr">Link</th>
            </tr>
            </thead>
            <tbody>
            @foreach($sermons as $sermon)
                <tr class="hover:bg-gray-50 border-y border-gray-300">
                    <td class="pl-4 py-2" style="width:95%">
                        <span class="text-lg">
                            {{ $sermon->title }}
                        </span>
                        @if($sermon->speaker)
                            <div class="text-sm">
                                {{ $sermon->speaker }}
                            </div>
                        @endif
                        @if($sermon->date_sermon)
                            <div class="text-sm font-semibold">
                                {{ $sermon->date_sermon->format('F d, Y') }}
                            </div>
                        @endif
                    </td>
                    <td class="text-center pr-4">
                        @if($sermon->files->count() > 0)
                            <a href="{{ $sermon->files()->first()?->getCdnUrl() }}" target="_blank" class="text-nowrap px-2 py-1 rounded-sm text-white bg-blue-600 hover:bg-blue-400">▶&nbsp;Listen</a>
                        @endif
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
        <div class="p-4">
            {{ $sermons->withQueryString()->onEachSide(1)->links() }}
        </div>
    </div>
</div>