<script type="text/javascript">
    function loadScript(url, callback) {
        {{-- // Adding the script tag to the head as suggested before --}}
        var head = document.head;
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        {{-- // Then bind the event to the callback function. --}}
                {{-- // There are several events for cross browser compatibility. --}}
            script.onreadystatechange = callback;
        script.onload = callback;

        {{-- // Fire the loading --}}
        head.appendChild(script);
    }

    loadScript('https://cdn.tailwindcss.com/3.4.5');

    async function inHtmlFromUrl(url, targetElementId) {
        try {
            {{-- Fetch HTML content from the URL --}}
            const response = await fetch(url);

            {{-- Check if the response is OK (status code 200-299) --}}
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            {{-- Get the text (HTML content) from the response --}}
            const htmlContent = await response.text();

            {{-- Find the target element by ID --}}
            const targetElement = document.getElementById(targetElementId);

            {{-- Inject the HTML content into the target element --}}
            if (targetElement) {
                targetElement.innerHTML = htmlContent;
            } else {
                console.error(`Element with ID ${targetElementId} not found.`);
            }
        } catch (error) {
            {{-- Handle any errors that occurred during the fetch --}}
            console.error('Error fetching HTML content:', error);
        }
    }

    function getQueryParam(key) {
        {{-- Get the query string from the current URL --}}
        const queryString = window.location.search;

        {{-- Parse the query string --}}
        const urlParams = new URLSearchParams(queryString);

        {{-- Get the value of the specified key --}}
            return urlParams.get(key);
    }

    function setInputValueFromQuery(key, inputId) {
        {{-- Get the value of the specified key from the query string --}}
        const value = getQueryParam(key);

        {{-- Find the input element by ID --}}
        const inputElement = document.getElementById(inputId);

        {{-- Set the value of the input element --}}
        if (inputElement) {
            inputElement.value = value || '';
        } else {
            console.error(`Element with ID ${inputId} not found.`);
        }
    }

    // Use MutationObserver to detect when HTML is injected
    const observer = new MutationObserver((mutationsList, observer) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                // Check if the input element is now in the DOM
                if (document.getElementById('lp-search-term')) {
                    // Set the input value from the query string
                    setInputValueFromQuery('search_term', 'lp-search-term');
                    // Stop observing once the input is found and set
                    observer.disconnect();
                }
            }
        }
    });

    // Start observing the document body for added nodes
    observer.observe(document.body, {childList: true, subtree: true});

    {{-- RUN IT --}}
    const url = '{{ route('public.sermons.html_code', $account->ulid) }}' + window.location.search;
    inHtmlFromUrl(url, 'lightpost-public-sermons');
</script>