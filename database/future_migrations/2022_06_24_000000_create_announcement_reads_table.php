<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAnnouncementReadsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('announcement_reads', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->smallInteger('account_id')->unsigned()->index();
            $table->integer('user_id')->unsigned();
            $table->integer('announcement_id')->unsigned()->index();

            $table->index(['account_id', 'user_id']);
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('announcement_reads');
    }

}
