<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_notification_selections', function (Blueprint $table) {
            $table->id();
            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('last_sent_at')->nullable();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('user_notification_type_id')->unsigned()->index();

            $table->boolean('enable_mobile_notifications')->nullable()->default(false);
            $table->boolean('enable_emails')->nullable()->default(false);
            $table->boolean('enable_sms')->nullable()->default(false);
            $table->boolean('enable_voice_calls')->nullable()->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_notification_selections');
    }
}
