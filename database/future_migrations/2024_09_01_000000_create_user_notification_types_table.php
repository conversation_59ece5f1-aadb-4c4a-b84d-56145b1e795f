<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_notification_types', function (Blueprint $table) {
            $table->id();

            $table->integer('account_id')->unsigned()->index();
            $table->mediumInteger('user_notification_type_blueprint_id')->unsigned()->index();

            $table->string('name', 120);
            $table->string('short_name', 120);
            $table->string('key', 40)->comment('A unique key for this notification type.');
            $table->text('user_description')->nullable();
            $table->text('description')->nullable();

            $table->string('type_key', 64)->nullable();
            $table->integer('id_of_object')->nullable()->comment('The id of the object that this notification is about.');

            $table->string('folder', 80)->nullable();
            $table->string('folder_short_name', 40)->nullable();

            $table->boolean('allow_user_mobile_notifications')->nullable()->default(false);
            $table->boolean('allow_user_emails')->nullable()->default(false);
            $table->boolean('allow_user_sms')->nullable()->default(false);
            $table->boolean('allow_user_voice_calls')->nullable()->default(false);

            $table->boolean('allow_account_mobile_notifications')->nullable()->default(false);
            $table->boolean('allow_account_emails')->nullable()->default(false);
            $table->boolean('allow_account_sms')->nullable()->default(false);
            $table->boolean('allow_account_voice_calls')->nullable()->default(false);

            $table->boolean('allow_user_to_view')->nullable()->default(false)->index()->comment('Should the individual users be able to see this notification type?');
            $table->boolean('allow_user_to_modify')->nullable()->default(false)->index()->comment('Should the user be allowed to enable/disable this notification type?');

            $table->boolean('allow_account_to_view')->nullable()->default(false)->index()->comment('Should the account be able to see this setting?');
            $table->boolean('allow_account_to_modify')->nullable()->default(false)->index()->comment('Can an account owner change settings for this type?');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_notification_types');
    }
}
