<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserGroupPostHistoryTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_group_post_history', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_group_id')->unsigned()->index();
            $table->integer('original_user_group_post_id')->unsigned()->index();
            $table->integer('user_group_post_id')->unsigned()->nullable()->index()->comment('Parent post, if a shared post.');
            $table->integer('creator_id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('last_edited_at')->nullable();
            $table->string('title', 500)->nullable();
            $table->string('url_title', 500)->nullable();
            $table->text('content')->nullable();
            $table->text('link')->nullable()->comment('If sharing a website link.');

            $table->smallInteger('count_likes')->unsigned()->nullable();
            $table->smallInteger('count_comments')->unsigned()->nullable();
            $table->smallInteger('count_shares')->unsigned()->nullable();
            $table->integer('count_views')->unsigned()->nullable();

            $table->boolean('is_shared')->nullable()->default(false);
            $table->boolean('allow_comments')->nullable()->default(false);
            $table->boolean('is_pinned')->nullable()->default(false);
            $table->boolean('is_hidden')->nullable()->default(false)->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_group_post_history');
    }

}
