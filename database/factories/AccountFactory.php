<?php

namespace Database\Factories;

use App\Accounts\Account;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Accounts\Account>
 */
class AccountFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Account::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name'            => fake()->company(),
            'account_plan_id' => 1,
            'timezone'        => fake()->timezone(),
            'status'          => 'active',
            'is_active'       => 1,
            'api_token'       => Str::random(60), // Standard API token length
        ];
    }
}
