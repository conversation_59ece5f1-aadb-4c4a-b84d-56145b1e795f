<?php

namespace Database\Factories;

use App\Users\Address;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Users\Address>
 */
class UserAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Address::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type'     => 'residence',
            'label'    => fake()->streetName(),
            'address1' => fake()->streetAddress(),
            'address2' => '',
            'city'     => fake()->city(),
            'state'    => 'TX', // Keep state/zip/country specific for now, or use fake()->stateAbbr() etc.
            'zip'      => '77493',
            'country'  => 'US',
        ];
    }
}
