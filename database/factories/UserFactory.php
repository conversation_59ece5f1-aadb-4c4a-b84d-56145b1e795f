<?php

namespace Database\Factories;

use App\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Users\User>
 */
class UserFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_name'      => fake()->userName(),
            'first_name'     => fake()->firstName(),
            'last_name'      => fake()->lastName(),
            'gender'         => 'm',
            'timezone'       => fake()->timezone(), // Use fake() for timezone too
            'password'       => bcrypt('secret'), // No need for static password here, usually handled in tests/seeders if needed
            'remember_token' => Str::random(10),
            'is_active'      => 1,
            'status'         => 'active',
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return static
     */
    public function unverified(): static
    {
        // Example state transformation - you might not need this one
        // return $this->state(fn (array $attributes) => [
        //     'email_verified_at' => null,
        // ]);
        return $this->state([]); // Placeholder if no specific unverified state needed
    }
}
