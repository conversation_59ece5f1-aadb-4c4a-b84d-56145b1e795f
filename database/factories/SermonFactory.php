<?php

namespace Database\Factories;

use App\Sermons\Sermon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Sermons\Sermon>
 */
class SermonFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Sermon::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            // Define default attributes here, e.g.:
            // 'title' => fake()->sentence,
            // 'description' => fake()->paragraph,
            // 'series_id' => null, // Or use Series::factory()
            // 'speaker_id' => null, // Or use Speaker::factory()
            // 'recorded_at' => fake()->dateTimeThisYear(),
        ];
    }
}
