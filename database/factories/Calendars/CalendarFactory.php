<?php

namespace Database\Factories\Calendars;

use App\Accounts\Account;
use App\Calendars\Calendar;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CalendarFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Calendar::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'account_id' => Account::factory(), // Assumes AccountFactory exists
            'name'       => fake()->word() . ' Calendar',
            'url_name'   => Str::slug(fake()->words(3, true)),
            'is_public'  => fake()->boolean(),
            'color'      => fake()->hexColor(),
            // Add other necessary default fields for Calendar
        ];
    }
}
