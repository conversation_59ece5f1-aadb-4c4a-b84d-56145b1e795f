<?php

namespace Database\Factories\Calendars;

use App\Accounts\Account;
use App\Calendars\Calendar;
use App\Calendars\Event;
use App\Calendars\EventOccurrence;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class EventOccurrenceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = EventOccurrence::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $start    = Carbon::instance(fake()->dateTimeBetween('+1 day', '+5 days'));
        $end      = (clone $start)->addHours(fake()->numberBetween(1, 3));
        $timezone = fake()->timezone();

        return [
            'account_id'          => Account::factory(), // Assumes AccountFactory exists
            'calendar_event_id'   => Event::factory(), // Assumes EventFactory exists
            'calendar_id'         => function (array $attributes) {
                // Use the calendar_id from the associated event if available
                return Event::find($attributes['calendar_event_id'])->calendar_id ?? Calendar::factory();
            },
            'title'               => fake()->words(3, true),
            'description'         => fake()->words(10, true),
            'location'            => fake()->words(3, true),
            'start_at'            => $start->copy()->setTimezone('UTC'), // Store in UTC
            'end_at'              => $end->copy()->setTimezone('UTC'), // Store in UTC
            'is_all_day'          => false,
            'timezone'            => $timezone,
            'start_at_date'       => $start->copy()->setTimezone('UTC')->toDateString(),
            'start_at_time'       => $start->copy()->setTimezone('UTC')->toTimeString(),
            'end_at_date'         => $end->copy()->setTimezone('UTC')->toDateString(),
            'end_at_time'         => $end->copy()->setTimezone('UTC')->toTimeString(),
            'local_start_at_time' => $start->copy()->setTimezone($timezone)->toTimeString(), // Local time based on occurrence timezone
            'local_end_at_time'   => $end->copy()->setTimezone($timezone)->toTimeString(), // Local time based on occurrence timezone
            // Add other necessary default fields for EventOccurrence
        ];
    }
}
