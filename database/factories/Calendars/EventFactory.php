<?php

namespace Database\Factories\Calendars;

use App\Accounts\Account;
use App\Calendars\Calendar;
use App\Calendars\Event;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class EventFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Event::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $start = Carbon::instance(fake()->dateTimeBetween('+1 day', '+5 days'));
        $end   = (clone $start)->addHours(fake()->numberBetween(1, 3));

        return [
            'account_id'  => Account::factory(), // Assumes AccountFactory exists
            'calendar_id' => Calendar::factory(), // Assumes CalendarFactory exists
            'title'       => fake()->words(3, true),
            'url_title'   => 'slug-here',
            'description' => fake()->words(10, true),
            'location'    => fake()->words(3, true),
            'start_at'    => $start,
            'end_at'      => $end,
            'is_all_day'  => false,
            'timezone'    => 'America/Chicago',

            // Add other necessary default fields for Event
        ];
    }
}
