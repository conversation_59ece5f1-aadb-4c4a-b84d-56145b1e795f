<?php

namespace Database\Factories\Accounts;

use App\Accounts\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

class AccountFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Account::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name'            => fake()->company(),
            'account_plan_id' => 1,
            'timezone'        => 'America/Chicago',
            'status'          => 'active',
            'is_active'       => 1,
            // Add other necessary default fields for Account
        ];
    }
}
