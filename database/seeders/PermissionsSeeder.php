<?php

namespace Database\Seeders;

use App\Users\Permission;
use Illuminate\Database\Seeder;

class PermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        # Accounts
        // Permission::create(['key' => 'account.create', 'name' => 'Create Account', 'group_name' => 'Sermons', 'sort_id' => 6, 'description' => 'Allow user to create account.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'account.index', 'name' => 'View Account', 'group_name' => 'Account', 'sort_id' => 1, 'description' => 'Allow user to view account information.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'account.view', 'name' => 'View Account', 'group_name' => 'Account', 'sort_id' => 2, 'description' => 'Allow user to view account information.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'account.update', 'name' => 'Update Account', 'group_name' => 'Account', 'sort_id' => 9, 'description' => 'Allow user to update account information.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'account.delete', 'name' => 'Delete Account', 'group_name' => 'Account', 'sort_id' => 10, 'description' => 'Allow user to delete account information.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'account.manage', 'name' => 'Manage Account', 'group_name' => 'Account', 'sort_id' => 3, 'description' => 'Allow user to manage account information.', 'permission_type' => 'admin']);

        # Account Location
//        Permission::create(['key' => 'account_locations.create', 'name' => 'Create Account Locations', 'group_name' => 'Account Locations', 'sort_id' => 6, 'description' => 'Allow user to create account locations.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'account_locations.index', 'name' => 'Retrieve Account Locations', 'group_name' => 'Account Locations', 'sort_id' => 7, 'description' => 'Allow user to view account locations.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'account_locations.view', 'name' => 'View Account Locations', 'group_name' => 'Account Locations', 'sort_id' => 8, 'description' => 'Allow user to view specific account locations.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'account_locations.update', 'name' => 'Update Account Locations', 'group_name' => 'Account Locations', 'sort_id' => 9, 'description' => 'Allow user to update account locations.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'account_locations.delete', 'name' => 'Delete Account Locations', 'group_name' => 'Account Locations', 'sort_id' => 10, 'description' => 'Allow user to delete account locations.', 'permission_type' => 'admin']);

        # Announcements
        Permission::create(['key' => 'announcements.manage', 'name' => 'Manage Announcements', 'group_name' => 'Announcements', 'sort_id' => 1, 'description' => 'Allow user to manage announcements.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'announcements.create', 'name' => 'Create Announcements', 'group_name' => 'Announcements', 'sort_id' => 2, 'description' => 'Allow user to create announcements.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'announcements.index', 'name' => 'View Announcements', 'group_name' => 'Announcements', 'sort_id' => 3, 'description' => 'Allow user to view announcements.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'announcements.view', 'name' => 'View Announcements', 'group_name' => 'Announcements', 'sort_id' => 4, 'description' => 'Allow user to view specific announcements.', 'permission_type' => 'app']);
//        Permission::create(['key' => 'announcements.update', 'name' => 'Update Announcements', 'group_name' => 'Announcements', 'sort_id' => 5, 'description' => 'Allow user to update announcements.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'announcements.delete', 'name' => 'Delete Announcements', 'group_name' => 'Announcements', 'sort_id' => 6, 'description' => 'Allow user to delete announcements.', 'permission_type' => 'admin']);

        # Calendars
        Permission::create(['key' => 'calendars.manage', 'name' => 'Manage Calendars', 'group_name' => 'Calendars', 'sort_id' => 1, 'description' => 'Allow user to manage calendars.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'calendars.create', 'name' => 'Create Calendars', 'group_name' => 'Calendars', 'sort_id' => 6, 'description' => 'Allow user to create calendars.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'calendars.index', 'name' => 'View Calendars', 'group_name' => 'Calendars', 'sort_id' => 7, 'description' => 'Allow user to view calendars.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'calendars.view', 'name' => 'View Calendars', 'group_name' => 'Calendars', 'sort_id' => 8, 'description' => 'Allow user to view specific calendars.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'calendars.update', 'name' => 'Update Calendars', 'group_name' => 'Calendars', 'sort_id' => 9, 'description' => 'Allow user to update calendars.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'calendars.delete', 'name' => 'Delete Calendars', 'group_name' => 'Calendars', 'sort_id' => 10, 'description' => 'Allow user to delete calendars.', 'permission_type' => 'admin']);

        # Users
        Permission::create(['key' => 'users.manage', 'name' => 'Manage Users', 'group_name' => 'Users', 'sort_id' => 1, 'description' => 'Allow user to create users and edit basic information.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'users.manage.roles', 'name' => 'Manage Roles for a User', 'group_name' => 'Users', 'sort_id' => 2, 'description' => 'Allow user to edit a users\' roles.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'users.manage.groups', 'name' => 'Manage Groups for a User', 'group_name' => 'Users', 'sort_id' => 3, 'description' => 'Allow user to edit a user\s groups.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'users.create', 'name' => 'Create Users', 'group_name' => 'Users', 'sort_id' => 1, 'description' => 'Allow user to create new users.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'users.index', 'name' => 'View User Basic Information', 'group_name' => 'Users', 'sort_id' => 2, 'description' => 'Allow user to view basic user information.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'users.view', 'name' => 'View User Basic Information', 'group_name' => 'Users', 'sort_id' => 3, 'description' => 'Allow user to view user basic information.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'users.view.extended', 'name' => 'View User Extended Information', 'group_name' => 'Users', 'sort_id' => 4, 'description' => 'Allow user to view user extended information.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'users.update', 'name' => 'Update User Basic Information', 'group_name' => 'Users', 'sort_id' => 5, 'description' => 'Allow user to update user basic information.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'users.manage.extended', 'name' => 'Update User Extended Information', 'group_name' => 'Users', 'sort_id' => 6, 'description' => 'Allow user to update user extended information.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'users.delete', 'name' => 'Delete Users', 'group_name' => 'Users', 'sort_id' => 7, 'description' => 'Allow user to delete users.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'users.photos', 'name' => 'Manage User Photos', 'group_name' => 'Users', 'sort_id' => 7, 'description' => 'Allow user to manage user photos.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'users.manage.photos.family', 'name' => 'Manage Family Photos', 'group_name' => 'Users', 'sort_id' => 7, 'description' => 'Allow user to manage family photos.', 'permission_type' => 'admin']);

        # Interactions
        Permission::create(['key' => 'interactions.create', 'name' => 'Create Interactions', 'group_name' => 'Interactions', 'sort_id' => 1, 'description' => 'Allow user to create new interactions.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'interactions.manage', 'name' => 'Manage Interactions', 'group_name' => 'Interactions', 'sort_id' => 2, 'description' => 'Allow user to manage interactions.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'interactions.index', 'name' => 'View Interactions', 'group_name' => 'Interactions', 'sort_id' => 3, 'description' => 'Allow user to view interactions.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'interactions.view', 'name' => 'View User Basic Information', 'group_name' => 'Interactions', 'sort_id' => 3, 'description' => 'Allow user to view specific interactions.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'interactions.update', 'name' => 'Update User Basic Information', 'group_name' => 'Interactions', 'sort_id' => 5, 'description' => 'Allow user to update interactions.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'interactions.delete', 'name' => 'Delete Interactions', 'group_name' => 'Interactions', 'sort_id' => 7, 'description' => 'Allow user to delete interactions.', 'permission_type' => 'admin']);

        # Files
        Permission::create(['key' => 'files.manage', 'name' => 'Manage Files', 'group_name' => 'Account Files', 'sort_id' => 1, 'description' => 'Allow user to manage files.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'files.create', 'name' => 'Create Files', 'group_name' => 'Account Files', 'sort_id' => 1, 'description' => 'Allow user to create new files.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'files.index', 'name' => 'View Files', 'group_name' => 'Account Files', 'sort_id' => 3, 'description' => 'Allow user to view file lists.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'files.view', 'name' => 'View Files', 'group_name' => 'Account Files', 'sort_id' => 3, 'description' => 'Allow user to view files.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'files.update', 'name' => 'Update Files', 'group_name' => 'Account Files', 'sort_id' => 5, 'description' => 'Allow user to update files.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'files.delete', 'name' => 'Delete Files', 'group_name' => 'Account Files', 'sort_id' => 7, 'description' => 'Allow user to delete files.', 'permission_type' => 'admin']);

        # Reports
        Permission::create(['key' => 'reports.index', 'name' => 'View Reports', 'group_name' => 'Reports', 'sort_id' => 1, 'description' => 'Allow user to view reports lists.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'reports.view', 'name' => 'View Reports', 'group_name' => 'Reports', 'sort_id' => 2, 'description' => 'Allow user to view reports.', 'permission_type' => 'admin']);

        # Roles
        Permission::create(['key' => 'roles.manage', 'name' => 'Manage Roles', 'group_name' => 'Roles', 'sort_id' => 1, 'description' => 'Allow user to manage roles.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'roles.create', 'name' => 'Create Roles', 'group_name' => 'Roles', 'sort_id' => 6, 'description' => 'Allow user to create new roles.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'roles.index', 'name' => 'View Roles', 'group_name' => 'Roles', 'sort_id' => 7, 'description' => 'Allow user to view roles.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'roles.view', 'name' => 'View Roles', 'group_name' => 'Roles', 'sort_id' => 8, 'description' => 'Allow user to view specific role.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'roles.update', 'name' => 'Update Roles', 'group_name' => 'Roles', 'sort_id' => 9, 'description' => 'Allow user to update roles.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'roles.delete', 'name' => 'Delete Roles', 'group_name' => 'Roles', 'sort_id' => 10, 'description' => 'Allow user to delete roles.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'roles.assign', 'name' => 'Assign Roles', 'group_name' => 'Roles', 'sort_id' => 11, 'description' => 'Allow user to assign roles to users.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'roles.unassign', 'name' => 'Unassign Roles', 'group_name' => 'Roles', 'sort_id' => 12, 'description' => 'Allow user to unassign roles from users.', 'permission_type' => 'admin']);

        # Groups
        Permission::create(['key' => 'groups.manage', 'name' => 'Manage Groups', 'group_name' => 'Groups', 'sort_id' => 1, 'description' => 'Allow user to manage groups.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'groups.create', 'name' => 'Create Groups', 'group_name' => 'Groups', 'sort_id' => 6, 'description' => 'Allow user to create new groups.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'groups.index', 'name' => 'View Groups', 'group_name' => 'Groups', 'sort_id' => 7, 'description' => 'Allow user to view groups.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'groups.view', 'name' => 'View Groups', 'group_name' => 'Groups', 'sort_id' => 8, 'description' => 'Allow user to view specific groups.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'groups.update', 'name' => 'Update Groups', 'group_name' => 'Groups', 'sort_id' => 9, 'description' => 'Allow user to update groups.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'groups.delete', 'name' => 'Delete Groups', 'group_name' => 'Groups', 'sort_id' => 10, 'description' => 'Allow user to delete groups.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'groups.assign', 'name' => 'Assign Groups', 'group_name' => 'Groups', 'sort_id' => 11, 'description' => 'Allow user to assign groups to users.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'groups.unassign', 'name' => 'Unassign Groups', 'group_name' => 'Groups', 'sort_id' => 12, 'description' => 'Allow user to unassign groups from users.', 'permission_type' => 'admin']);

        # Bible Classes
        Permission::create(['key' => 'bible-classes.manage', 'name' => 'Manage Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 1, 'description' => 'Allow user to create new bible classes.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'bible-classes.create', 'name' => 'Create Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 6, 'description' => 'Allow user to create new bible classes.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'bible-classes.index', 'name' => 'View Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 7, 'description' => 'Allow user to view bible classes.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'bible-classes.view', 'name' => 'View Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 4, 'description' => 'Allow user to view specific bible classes.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'app.bible-classes.register', 'name' => 'Register for Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 5, 'description' => 'Allow a user to register for bible classes.', 'permission_type' => 'app']);
//        Permission::create(['key' => 'bible-classes.update', 'name' => 'Update Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 9, 'description' => 'Allow user to update bible classes.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'bible-classes.delete', 'name' => 'Delete Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 10, 'description' => 'Allow user to delete bible classes.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'bible-classes.assign', 'name' => 'Assign Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 11, 'description' => 'Allow user to assign users to bible classes.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'bible-classes.unassign', 'name' => 'Unassign Bible Classes', 'group_name' => 'Bible Classes', 'sort_id' => 12, 'description' => 'Allow user to unassign users from bible classes.', 'permission_type' => 'admin']);

        # Member Involvement
        Permission::create(['key' => 'involvement.manage', 'name' => 'Manage Member Involvement', 'group_name' => 'Member Involvement', 'sort_id' => 1, 'description' => 'Allow user to manage member involvement areas.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'involvement.index', 'name' => 'View Member Involvement Areas', 'group_name' => 'Member Involvement', 'sort_id' => 2, 'description' => 'Allow user to view member involvement areas.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'involvement.selections', 'name' => 'View Member Involvement Selections', 'group_name' => 'Member Involvement', 'sort_id' => 3, 'description' => 'Allow user to view who has selected member involvement areas.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'app.involvement.selections', 'name' => 'View Member Involvement Selections', 'group_name' => 'Member Involvement', 'sort_id' => 4, 'description' => 'Allow a user to view who has selected member involvement areas on the frontend.', 'permission_type' => 'app']);

        # Finance
        Permission::create(['key' => 'finance.manage', 'name' => 'Manage Financial Records', 'group_name' => 'Finance', 'sort_id' => 1, 'description' => 'Allow user to manage financial records.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'finance.reports', 'name' => 'View Financial Reports', 'group_name' => 'Finance', 'sort_id' => 2, 'description' => 'Allow user to view financial reports.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'finance.create', 'name' => 'Create Financial Records', 'group_name' => 'Finance', 'sort_id' => 6, 'description' => 'Allow user to create financial records.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'finance.index', 'name' => 'View Financial Records', 'group_name' => 'Finance', 'sort_id' => 7, 'description' => 'Allow user to view financial records.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'finance.view', 'name' => 'View Financial Records', 'group_name' => 'Finance', 'sort_id' => 8, 'description' => 'Allow user to view specific financial records.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'finance.update', 'name' => 'Update Financial Records', 'group_name' => 'Finance', 'sort_id' => 9, 'description' => 'Allow user to update financial records.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'finance.delete', 'name' => 'Delete Financial Records', 'group_name' => 'Finance', 'sort_id' => 10, 'description' => 'Allow user to delete financial records.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'finance.contributions.view-amounts', 'name' => 'View Contribution Amounts', 'group_name' => 'Finance', 'sort_id' => 11, 'description' => 'Allow user to see contribution amounts.', 'permission_type' => 'admin']);

        # Sermons
        Permission::create(['key' => 'sermons.manage', 'name' => 'Manage Sermons', 'group_name' => 'Sermons', 'sort_id' => 1, 'description' => 'Allow user to manage sermons.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'sermons.index', 'name' => 'View Sermons', 'group_name' => 'Sermons', 'sort_id' => 2, 'description' => 'Allow user to view sermons.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'sermons.delete', 'name' => 'Delete Sermons', 'group_name' => 'Sermons', 'sort_id' => 3, 'description' => 'Allow user to delete sermons.', 'permission_type' => 'admin']);

        # Attendance
        Permission::create(['key' => 'attendance.manage', 'name' => 'Manage Attendance', 'group_name' => 'Attendance', 'sort_id' => 1, 'description' => 'Allow user to manage attendance.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'attendance.index', 'name' => 'View Attendance Records', 'group_name' => 'Attendance', 'sort_id' => 2, 'description' => 'Allow user to view attendance.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'attendance.record.general', 'name' => 'Record General Attendance', 'group_name' => 'Attendance', 'sort_id' => 3, 'description' => 'Allow a user to take general attendance.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'attendance.record.user', 'name' => 'Record User Attendance', 'group_name' => 'Attendance', 'sort_id' => 4, 'description' => 'Allow a user to take user attendance.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'attendance.record.class', 'name' => 'Record Class Attendance', 'group_name' => 'Attendance', 'sort_id' => 5, 'description' => 'Allow a user to take Bible class attendance.', 'permission_type' => 'admin']);

        # Messages
//        Permission::create(['key' => 'messages.create', 'name' => 'Create Messages', 'group_name' => 'Messages', 'sort_id' => 1, 'description' => 'Allow user to create messages.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'messages.index', 'name' => 'View Messages', 'group_name' => 'Messages', 'sort_id' => 2, 'description' => 'Allow user to view messages.', 'permission_type' => 'admin']);

        # Schedules
//        Permission::create(['key' => 'schedules.manage', 'name' => 'Manage Schedules', 'group_name' => 'Schedules', 'sort_id' => 1, 'description' => 'Allow user to manage schedules.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'schedules.index', 'name' => 'View Schedules', 'group_name' => 'Schedules', 'sort_id' => 3, 'description' => 'Allow user to view schedules.', 'permission_type' => 'admin']);

        # Worship Assignments
        Permission::create(['key' => 'worship-assignments.manage', 'name' => 'Manage Worship Assignments', 'group_name' => 'Worship Assignments', 'sort_id' => 1, 'description' => 'Allow user to manage worship assignments.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'worship-assignments.index', 'name' => 'View Worship Assignments', 'group_name' => 'Worship Assignments', 'sort_id' => 2, 'description' => 'Allow user to view worship assignments.', 'permission_type' => 'admin']);

        # Family Circles
        Permission::create(['key' => 'family-circles.manage', 'name' => 'Manage Family Circles', 'group_name' => 'Family Circles', 'sort_id' => 1, 'description' => 'Allow user to manage family circles.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'family-circles.index', 'name' => 'View Family Circles', 'group_name' => 'Family Circles', 'sort_id' => 2, 'description' => 'Allow user to view family circles.', 'permission_type' => 'admin']);

        # Prayers
        Permission::create(['key' => 'prayers.manage', 'name' => 'Manage Prayer List', 'group_name' => 'Account Prayer List', 'sort_id' => 1, 'description' => 'Allow user to manage prayers.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'prayers.create', 'name' => 'Create Prayer List', 'group_name' => 'Account Prayer List', 'sort_id' => 1, 'description' => 'Allow user to create new prayers.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'prayers.index', 'name' => 'View Prayer List', 'group_name' => 'Account Prayer List', 'sort_id' => 3, 'description' => 'Allow user to view file lists.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'prayers.update', 'name' => 'Update Prayer List', 'group_name' => 'Account Prayer List', 'sort_id' => 5, 'description' => 'Allow user to update prayers.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'prayers.delete', 'name' => 'Delete Prayer List', 'group_name' => 'Account Prayer List', 'sort_id' => 7, 'description' => 'Allow user to delete prayers.', 'permission_type' => 'admin']);

        # Podcasts
        Permission::create(['key' => 'podcasts.manage', 'name' => 'Manage Podcasts', 'group_name' => 'Account Podcasts', 'sort_id' => 2, 'description' => 'Allow user to manage podcasts.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'podcasts.index', 'name' => 'View Podcasts', 'group_name' => 'Account Podcasts', 'sort_id' => 1, 'description' => 'Allow user to view file lists.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'podcasts.delete', 'name' => 'Delete Podcasts', 'group_name' => 'Account Podcasts', 'sort_id' => 3, 'description' => 'Allow user to delete podcasts.', 'permission_type' => 'admin']);

        # Crisis Checkin
        Permission::create(['key' => 'crisis.manage', 'name' => 'Manage Crises', 'group_name' => 'Crisis Check-in', 'sort_id' => 1, 'description' => 'Allow user to create/manage crises.', 'permission_type' => 'admin']);

        # Chat / Forums
//        Permission::create(['key' => 'chat.manage', 'name' => 'Manage Chat / Forums', 'group_name' => 'Chat / Forums', 'sort_id' => 1, 'description' => 'Allow user to manage chat / forums.', 'permission_type' => 'admin']);
//        Permission::create(['key' => 'chat.index', 'name' => 'View Chat / Forums', 'group_name' => 'Chat / Forums', 'sort_id' => 2, 'description' => 'Allow user to view chat /forums.', 'permission_type' => 'admin']);

        # Child Check-in
        Permission::create(['key' => 'child-checkin.manage', 'name' => 'Manage Child Check-in Settings', 'group_name' => 'Child Check-in', 'sort_id' => 1, 'description' => 'Allow user to manage child check-in settings.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'child-checkin.enable_kiosk', 'name' => 'Enter Kiosk Mode', 'group_name' => 'Child Check-in', 'sort_id' => 2, 'description' => 'Allows the user to see a link to enter Child Check-in mode.', 'permission_type' => 'admin']);

        # Visitors
        Permission::create(['key' => 'visitors.view', 'name' => 'View Visitors', 'group_name' => 'Visitor Tracking', 'sort_id' => 1, 'description' => 'Allow user to view visitor information.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'visitors.manage', 'name' => 'Edit / Manage Visitors', 'group_name' => 'Visitor Tracking', 'sort_id' => 2, 'description' => 'Allows the user to edit visitor information.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'visitors.manage_settings', 'name' => 'Visitor Tracking Settings', 'group_name' => 'Visitor Tracking', 'sort_id' => 3, 'description' => 'Allows the user to edit visitor tracking settings (admin only).', 'permission_type' => 'admin']);

        # Programs
        Permission::create(['key' => 'programs.view', 'name' => 'View Programs', 'group_name' => 'Programs', 'sort_id' => 1, 'description' => 'Allow user to view programs.', 'permission_type' => 'admin']);
        Permission::create(['key' => 'programs.manage', 'name' => 'Manage Programs', 'group_name' => 'Programs', 'sort_id' => 2, 'description' => 'Allows the user to edit programs.', 'permission_type' => 'admin']);

        # Website
        Permission::create([
            'key'             => 'website.manage',
            'name'            => 'Manage Website Settings',
            'group_name'      => 'Website',
            'sort_id'         => 1,
            'description'     => 'Allow user to manage configuration of the website.',
            'permission_type' => 'admin',
        ]);
        Permission::create([
            'key'             => 'website.manage.pages',
            'name'            => 'Manage Website Pages',
            'group_name'      => 'Website',
            'sort_id'         => 2,
            'description'     => 'Allow user to manage website pages only.',
            'permission_type' => 'admin',
        ]);
        Permission::create([
            'key'             => 'website.stats',
            'name'            => 'View Website Statistics',
            'group_name'      => 'Website',
            'sort_id'         => 3,
            'description'     => 'Allow user to view website statistics.',
            'permission_type' => 'admin',
        ]);
    }
}
