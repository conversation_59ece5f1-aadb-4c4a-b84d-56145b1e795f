<?php

namespace Database\Seeders;

use App\Users\Permission;
use App\Users\Role;
use Illuminate\Database\Seeder;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        ## ACCOUNT
        $role = Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Account Admin',
            'key'                 => 'admin',
            'sort_id'             => 1,
        ]);
        # Give all permissions
        Role::find($role->id)->permissions()->sync(Permission::get(['id'])->pluck('id')->toArray());

        ## ACCOUNT LOCATION
        $role = Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Account Location Admin',
            'key'                 => 'local-admin',
            'sort_id'             => 2,
        ]);
        # Give permissions
        Role::find($role->id)->permissions()->sync(Permission::where([
            ['group_name', '<>', 'Account'],
        ])->get(['id'])->pluck('id')->toArray());

        ## ELDER
        $role = Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Elder',
            'key'                 => 'elder',
            'sort_id'             => 3,
        ]);
        # Give permissions
        Role::find($role->id)->permissions()->sync(Permission::where([
            ['group_name', '<>', 'Account'],
            ['group_name', '<>', 'Account Locations'],
        ])->get(['id'])->pluck('id')->toArray());

        ## DEACON
        $role = Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Deacon',
            'key'                 => 'deacon',
            'sort_id'             => 4,
        ]);
        # Give permissions
        Role::find($role->id)->permissions()->sync(Permission::where([
            ['group_name', '<>', 'Account'],
            ['group_name', '<>', 'Account Locations'],
            ['group_name', '<>', 'Elders'],
            ['group_name', '<>', 'Finance'],
        ])->get(['id'])->pluck('id')->toArray());

        ## FINANCE
        $role = Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Finance Officer',
            'key'                 => 'finance',
            'sort_id'             => 5,
        ]);
        # Give permissions
        Role::find($role->id)->permissions()->sync(Permission::where([
            ['group_name', '<>', 'Account'],
            ['group_name', '<>', 'Account Locations'],
            ['group_name', '<>', 'Elders'],
        ])->get(['id'])->pluck('id')->toArray());


        ## STAFF
        $role = Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Staff',
            'key'                 => 'staff',
            'sort_id'             => 6,
        ]);
        # Give permissions
        Role::find($role->id)->permissions()->sync(Permission::where([
            ['group_name', '<>', 'Account'],
            ['group_name', '<>', 'Account Locations'],
            ['group_name', '<>', 'Elders'],
            ['group_name', '<>', 'Deacons'],
            ['group_name', '<>', 'Roles'],
            ['group_name', '<>', 'Finance'],
            ['group_name', '<>', 'Interactions'],
        ])->get(['id'])->pluck('id')->toArray());

        ## MEMBER
        $role = Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Member',
            'key'                 => 'member',
            'sort_id'             => 7,
        ]);
        # Give permissions
        Role::find($role->id)->permissions()->sync(Permission::where([
            ['group_name', '<>', 'Account'],
            ['group_name', '<>', 'Account Locations'],
            ['group_name', '<>', 'Elders'],
            ['group_name', '<>', 'Finance'],
            ['group_name', '<>', 'Sermons'],
            ['group_name', '<>', 'Attendance'],
            ['group_name', '<>', 'Messages'],
            ['group_name', '<>', 'Schedules'],
            ['group_name', '<>', 'Roles'],
            ['group_name', '<>', 'Files'],
            ['group_name', '<>', 'Calendars'],
            ['group_name', '<>', 'Interactions'],
        ])->get(['id'])->pluck('id')->toArray());

        ## FORMER MEMBER
        Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Former Member',
            'key'                 => 'former-member',
            'sort_id'             => 7,
        ]);

        ## VISITOR
        Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Visitor',
            'key'                 => 'visitor',
            'sort_id'             => 8,
        ]);

        ## OTHER
        Role::create([
            'account_id'          => 1,
            'account_location_id' => 1,
            'name'                => 'Other',
            'key'                 => 'other',
            'sort_id'             => 8,
        ]);
    }
}
