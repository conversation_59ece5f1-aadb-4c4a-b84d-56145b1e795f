<?php

namespace Database\Seeders;

use App\Accounts\Account;
use App\Users\Address;
use App\Users\Email;
use App\Users\Phone;
use App\Users\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->createSuper();

        // Make 10 base users
        for ($i = 0; $i < 10; $i++) {
            $user = User::factory()->create([
                'account_id'  => Account::inRandomOrder()->first()->id,
                'first_name'  => fake()->firstName('male'),
                'gender'      => 'm',
                'family_role' => 'head',
            ]);

            $this->createEmails($user);
            $this->createFamilies($user);
            $this->createAddresses($user);
            $this->createPhoneNumber($user);
        }
    }

    private function createSuper()
    {
        $user = User::factory()->create([
            'account_id' => Account::first()->id,
            'user_name'  => 'super',
            'first_name' => 'Joe',
            'last_name'  => 'Bob',
            'gender'     => 'm',
            'password'   => 'password',
            'timezone'   => 'America/Chicago',
        ]);

        $user->is_active = 1;
        $user->is_super  = 1;
        $user->save();

        $user->family_id = $user->id;
        $user->save();

        $user->emails()->create([
            'email'      => '<EMAIL>',
            'is_primary' => 1,
            'type'       => 'home',
        ]);

        return $user;
    }

    private function createEmails(User $user)
    {
        return Email::factory()->create([
            'user_id'    => $user->id,
            'is_primary' => 1,
            // 'type' and 'email' are handled by the factory definition
        ]);
    }

    private function createFamilies(User $user)
    {
        $user->family_id = $user->id;
        $user->save();

        $family_member = User::factory()->create([
            'account_id'  => $user->account->id,
            'family_role' => 'spouse',
            'first_name'  => fake()->firstName('female'),
            'last_name'   => $user->last_name,
            'gender'      => 'f', // Corrected gender to match first name request
            'family_id'   => $user->id,
        ]);

        foreach (range(0, rand(1, 6)) as $index) {
            $gender        = rand(0, 1) == 0 ? 'm' : 'f';
            $family_member = User::factory()->create([
                'account_id'  => $user->account->id,
                'family_role' => 'child',
                'first_name'  => fake()->firstName($gender == 'm' ? 'male' : 'female'),
                'last_name'   => $user->last_name,
                'gender'      => $gender,
                'family_id'   => $user->id,
            ]);
        }

        return $user; // Keep return in case it was used elsewhere
    }

    private function createAddresses(User $user)
    {
        $address = Address::factory()->create([
            'user_id'   => $user->id,
            'family_id' => $user->family_id, // Use family_id set in createFamilies
        ]);

        $user->family->primary_user_address_id = $address->id;
        $user->family->save(); // Save the family model after updating

        return $address;
    }

    private function createPhoneNumber(User $user)
    {
        $phone = Phone::factory()->create([
            'user_id'    => $user->id,
            'family_id'  => $user->family_id, // Use family_id set in createFamilies
            'is_primary' => true,
        ]);

        return $phone;
    }
}
