<?php

namespace Database\Seeders;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use Illuminate\Database\Seeder;

class AccountLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $location = AccountLocation::create([
            'account_id'   => Account::first()->id,
            'name'         => 'Test Church Location',
            'address1'     => '123 Place Land',
            'address2'     => '',
            'address3'     => '',
            'city'         => 'Katy',
            'state'        => 'TX',
            'postal_code'  => '77493',
            'country_code' => 'US',
            'phone_work'   => '**********',
            'phone_fax'    => '',
            'phone_other'  => '',
            'timezone'     => 'America/Chicago',
            'status'       => 'active',
            'is_active'    => true,
        ]);
    }
}
