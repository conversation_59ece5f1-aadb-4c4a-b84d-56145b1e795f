<?php

namespace Database\Seeders;

use App\Accounts\Account;
use App\Messages\Message;
use App\Messages\MessageType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class MessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            MessageTypeSeeder::class,
        ]);

        Message::create([
            'account_id'      => Account::first()->id,
            'message_type_id' => MessageType::first()->id,
            'sent_at'         => Carbon::now(),
            'subject'         => 'Test Subject',
            'content'         => 'Content Test',
            'html_content'    => '<h1>Content Test</h1>',
            'status'          => 'sent',
        ]);
    }
}
