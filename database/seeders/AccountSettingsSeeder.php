<?php

namespace Database\Seeders;

use App\Accounts\AccountSetting;
use Illuminate\Database\Seeder;

class AccountSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        AccountSetting::create(['id' => 1, 'key' => 'feature.directory', 'name' => 'Feature - Directory']);
        AccountSetting::create(['id' => 2, 'key' => 'feature.attendance', 'name' => 'Feature - Attendance']);
        AccountSetting::create(['id' => 3, 'key' => 'feature.involvement', 'name' => 'Feature - Member Involvement']);
        AccountSetting::create(['id' => 4, 'key' => 'feature.bible_classes', 'name' => 'Feature - Bible Class']);
        AccountSetting::create(['id' => 5, 'key' => 'feature.sermons', 'name' => 'Feature - Sermons']);
        AccountSetting::create(['id' => 6, 'key' => 'feature.files', 'name' => 'Feature - Files']);
        AccountSetting::create(['id' => 7, 'key' => 'feature.calendars', 'name' => 'Feature - Calendars']);
        AccountSetting::create(['id' => 8, 'key' => 'feature.worship_assignments', 'name' => 'Feature - Worship Assignments']);
        AccountSetting::create(['id' => 9, 'key' => 'feature.groups', 'name' => 'Feature - Groups']);
        AccountSetting::create(['id' => 10, 'key' => 'feature.roles', 'name' => 'Feature - Roles']);
        AccountSetting::create(['id' => 11, 'key' => 'feature.grades', 'name' => 'Feature - Grades']);
        AccountSetting::create(['id' => 12, 'key' => 'feature.reports', 'name' => 'Feature - Reports']);
        AccountSetting::create(['id' => 13, 'key' => 'feature.settings', 'name' => 'Feature - Account Settings']);
        AccountSetting::create(['id' => 14, 'key' => 'feature.website_integrations', 'name' => 'Feature - Website Integrations']);
        AccountSetting::create(['id' => 15, 'key' => 'feature.mobile_app', 'name' => 'Feature - Mobile App']);
        AccountSetting::create(['id' => 16, 'key' => 'feature.messages', 'name' => 'Feature - Messaging']);
        AccountSetting::create(['id' => 17, 'key' => 'feature.chat', 'name' => 'Feature - Chat']);
        AccountSetting::create(['id' => 18, 'key' => 'feature.visitor_tracking', 'name' => 'Feature - Visitor Tracking']);
        AccountSetting::create(['id' => 19, 'key' => 'feature.online_giving', 'name' => 'Feature - Online Giving']);
        AccountSetting::create(['id' => 20, 'key' => 'feature.podcasts', 'name' => 'Feature - Podcasts']);
        AccountSetting::create(['id' => 21, 'key' => 'feature.finance', 'name' => 'Feature - Financial Management']);
        AccountSetting::create(['id' => 22, 'key' => 'feature.library', 'name' => 'Feature - Library Management']);
        AccountSetting::create(['id' => 23, 'key' => 'feature.child_checkin', 'name' => 'Feature - Child Check-in']);
        AccountSetting::create(['id' => 24, 'key' => 'feature.family_circles', 'name' => 'Feature - Family Circles']);
        AccountSetting::create(['id' => 25, 'key' => 'feature.background_checks', 'name' => 'Feature - Background Checks']);
        AccountSetting::create(['id' => 26, 'key' => 'account.setting.enable_my_account', 'name' => 'Account Setting - Enable "My Account"']);
        AccountSetting::create(['id' => 28, 'key' => 'feature.prayers', 'name' => 'Feature - Prayer List']);
        AccountSetting::create(['id' => 29, 'key' => 'feature.photo-directory', 'name' => 'Feature - Photo Directory']);
        AccountSetting::create(['id' => 30, 'key' => 'account.setting.show_directory_by_user', 'name' => 'Account Setting - Directory Listing by User']);
        AccountSetting::create(['id' => 31, 'key' => 'feature.group_posts', 'name' => 'Feature - Group Posts']);
        AccountSetting::create(['id' => 32, 'key' => 'account.setting.verses.frequency', 'name' => 'Verse of the Day - Frequency']);
//        AccountSetting::create(['id' => 33, 'key' => 'account.setting.notify_on_user_info_change', 'name' => 'Account Setting - Notify when user information is changed.']);
        AccountSetting::create([
            'id'                      => 33,
            'key'                     => 'wa.enable_reminders',
            'name'                    => 'Worship Assignments - Enable Reminders',
            'type'                    => 'bool',
            'allow_account_to_view'   => 0,
            'allow_account_to_enable' => 0,
            'default'                 => '',
        ]);
        AccountSetting::create([
            'id'                      => 34,
            'key'                     => 'wa.send_days_before',
            'name'                    => 'Worship Assignments - Number of days before an assignment to send a reminder.',
            'type'                    => 'integer',
            'allow_account_to_view'   => 0,
            'allow_account_to_enable' => 0,
            'default'                 => '',
        ]);
        AccountSetting::create([
            'id'                      => 35,
            'key'                     => 'wa.hour_to_send',
            'name'                    => 'Worship Assignments - Hour (in 24 hours) to send the reminder.',
            'type'                    => 'string',
            'allow_account_to_view'   => 0,
            'allow_account_to_enable' => 0,
            'default'                 => '',
        ]);
        AccountSetting::create([
            'id'                      => 36,
            'key'                     => 'wa.send_mobile_notifications',
            'name'                    => 'Worship Assignments - Send Mobile Notifications',
            'type'                    => 'bool',
            'allow_account_to_view'   => 0,
            'allow_account_to_enable' => 0,
            'default'                 => 1,
        ]);
        AccountSetting::create([
            'id'                      => 37,
            'key'                     => 'wa.send_email_notifications',
            'name'                    => 'Worship Assignments - Send Email Notifications',
            'type'                    => 'bool',
            'allow_account_to_view'   => 0,
            'allow_account_to_enable' => 0,
            'default'                 => 1,
        ]);
        AccountSetting::create([
            'id'                      => 38,
            'key'                     => 'feature.verse_of_the_day',
            'name'                    => 'Verse of the Day',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 39,
            'key'                     => 'account.setting.hide_member_since',
            'name'                    => 'Account Settings - Hide "Member Since" indicator',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 40,
            'key'                     => 'account.setting.show_spouse_in_family_name',
            'name'                    => 'Account Settings - Show spouse as part of family unit name',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 41,
            'key'                     => 'feature.announcements',
            'name'                    => 'Feature - Announcements',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 42,
            'key'                     => 'account.setting.show_children_in_attendance_tracking',
            'name'                    => 'Account Settings - Show children in Attendance Tracking',
            'description'             => 'Determines if we should show children when recording attendance records in the admin or mobile app.',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 43,
            'key'                     => 'account.setting.mobile.directory.show_family_thumbnails',
            'name'                    => 'Mobile App - Directory family thumbnails',
            'description'             => 'Show family thumbnails directory listing.',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 44,
            'key'                     => 'account.setting.attendance.enable_member_checkin',
            'name'                    => 'Account Settings - Attendance Check-in',
            'description'             => 'Enable attendance check-in for members based on Attendance Types.',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 45,
            'key'                     => 'account.setting.frontend.show_anniversaries',
            'name'                    => 'Account Settings - Show Anniversaries',
            'description'             => '',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 1,
        ]);
        AccountSetting::create([
            'id'                      => 46,
            'key'                     => 'account.setting.frontend.show_birthdays',
            'name'                    => 'Account Settings - Show Birthdays',
            'description'             => '',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 1,
        ]);
        AccountSetting::create([
            'id'                      => 47,
            'key'                     => 'account.setting.frontend.show_baptism_birthdays',
            'name'                    => 'Account Settings - Show Baptism Birthdays',
            'description'             => '',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 1,
        ]);
        AccountSetting::create([
            'id'                      => 48,
            'key'                     => 'account.setting.directory.show_wedding_anniversary',
            'name'                    => 'Show wedding anniversaries on family page.',
            'description'             => '',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 49,
            'key'                     => 'feature.programs',
            'name'                    => 'Feature - Programs',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 0,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 50,
            'key'                     => 'account.setting.prayers.allow_requests',
            'name'                    => 'Allow members to submit prayer requests.',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 51,
            'key'                     => 'account.setting.prayers.auto_approve_requests',
            'name'                    => 'Automatically approve member submitted prayer requests.',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 52,
            'key'                     => 'account.setting.giving.enable_payment_schedules',
            'name'                    => 'Giving - Enable recurring giving',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 53,
            'key'                     => 'account.setting.giving.allow_ach.create',
            'name'                    => 'Giving - Allow ACH / Bank Account as a payment method.',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 1,
        ]);
        AccountSetting::create([
            'id'                      => 54,
            'key'                     => 'account.setting.giving.allow_card.create',
            'name'                    => 'Giving - Allow Debit/Credit Card as a payment method.',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 1,
            'default'                 => 0,
        ]);
        AccountSetting::create([
            'id'                      => 55,
            'key'                     => 'feature.website',
            'name'                    => 'Feature - Website',
            'type'                    => 'bool',
            'allow_account_to_view'   => 1,
            'allow_account_to_enable' => 0,
            'default'                 => 0,
        ]);
    }
}
