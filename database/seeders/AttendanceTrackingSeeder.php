<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;

class AttendanceTrackingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $last_sunday = new Carbon('last sunday');

//        foreach (User::get() as $user) {
//            foreach (range(0, 52) as $index) {
//                $attendance_tracking = Attendance::create([
//                    'date_attendance' => $last_sunday->subWeek($index),
//                    'user_id'         => $user->id,
//                    'type'            => 'service',
//                ]);
//
//                $last_sunday = new Carbon('last sunday');
//            }
//        }
    }
}
