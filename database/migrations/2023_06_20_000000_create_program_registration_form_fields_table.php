<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_registration_form_fields', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->uuid()->nullable()->index();
            $table->integer('account_id')->unsigned()->index('prff_account_id_index');
            $table->integer('program_id')->unsigned()->index('prrf_prid_index');
            $table->integer('program_registration_form_id')->unsigned()->index('prff_prfid_index');

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->string('title', 500)->nullable();
            $table->string('subtitle', 500)->nullable();
            $table->string('description', 500)->nullable();

            $table->boolean('is_required')->nullable()->default(false);
            $table->boolean('is_question_answer')->nullable()->default(false)->comment('If true, use the supplied answer options.');
            $table->boolean('allow_multiple_answers')->nullable()->default(false)->comment('If true, allow selecting multiple answers.');
            $table->boolean('is_text_answer')->nullable()->default(false)->comment('If true, provide a textbox for the user to enter their answer.');
            $table->boolean('is_true_false')->nullable()->default(false);
            $table->boolean('is_yes_no')->nullable()->default(false);
            $table->boolean('requires_file')->nullable()->default(false)->comment('If true, the user must upload a file for this field.');
            $table->boolean('allow_multiple_files')->nullable()->default(false);

            $table->boolean('is_user')->nullable()->default(false);
            $table->boolean('for_contact_only')->nullable()->default(false)->comment('If this field should show only for contacts, not the registrant.');

            $table->jsonb('question_answer_options')->nullable()->comment('These are the options the user can choose from. This should be an array of strings.');
            $table->jsonb('question_correct_answers')->nullable()->comment('These are the correct answer(s) to the question. This should be an array of the indexes of the question_answer_options array.');

            $table->boolean('is_select_program_group')->nullable()->default(false);
            $table->jsonb('select_from_program_group_ids')->nullable()->comment('If set, a JSON array of program group IDs that the user can select to be added to when they submit their registration.');

            $table->tinyInteger('sort_id')->unsigned()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_registration_form_fields');
    }
};
