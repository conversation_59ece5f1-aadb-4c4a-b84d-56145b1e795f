<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFinanceTransactionSplitsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_transaction_splits', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->integer('account_finance_bucket_id')->unsigned()->nullable()->index();

            $table->string('title', 200)->nullable()->comment('Title / Payee');
            $table->text('notes')->nullable();

            $table->integer('amount')->nullable()->comment('cents -- Final amount after it has been cleared of any payment processors, etc. Does not include fees.');

            $table->string('currency', 6)->nullable()->default('USD');

            $table->boolean('is_income')->nullable()->default(false)->index();
            $table->boolean('is_expense')->nullable()->default(false)->index();
            $table->boolean('is_contribution')->nullable()->default(false)->index();
            $table->boolean('is_payment')->nullable()->default(false)->index()->comment('A payment could be a transaction from a user towards Camp Bandina, or towards a Youth Devo, that might not be a "contribution".');
            $table->boolean('is_reimbursement')->nullable()->default(false)->index()->comment('Indicates an amount subtracted from a budget that was a outgoing payment to a user.');

            $table->boolean('is_hidden')->nullable()->default(false)->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finance_transaction_splits');
    }

}
