<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_notifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->dateTime('created_at')->nullable()->index();

            $table->integer('user_id')->unsigned()->index();
            $table->integer('user_device_id')->unsigned();

            $table->string('p_t', 40)->nullable()->index()->comment('parent_table');
            $table->bigInteger('p_t_id')->nullable()->index()->comment('parent_table_id');
            $table->string('t', 40)->nullable()->index()->comment('table');
            $table->bigInteger('t_id')->nullable()->index()->comment('table_id');
            $table->string('msg', 200)->nullable()->comment('message');

            $table->string('type', 40)->nullable()->index();
            $table->jsonb('data')->nullable();

            $table->boolean('is_read')->default(false)->index();

            // New - Sept 2024
            $table->integer('user_notification_type_id')->unsigned()->index();
            $table->bigInteger('msg_id')->nullable()->index()->comment('user_notification_message_id');
            $table->integer('provider_id');
            $table->string('provider_transaction_id', 40)->nullable()->index();

            $table->index([
                'user_id',
                'p_t',
                'p_t_id',
                'is_read',
            ], 'user_notifications_user_id_p_t_p_t_id_is_read_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_notifications');
    }
}
