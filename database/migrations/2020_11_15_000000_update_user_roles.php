<?php

use Illuminate\Database\Migrations\Migration;

class UpdateUserRoles extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $all_users = \App\Users\User::all();

        echo 'Found ' . $all_users->count() . ' users to evaluate family roles.';

        foreach ($all_users as $user) {
            // If they are a husband/wife, and the head of house, mark them as head
            if (($user->family_role == 'husband' || $user->family_role == 'wife') && $user->family_id === $user->id) {
                $user->family_role = 'head';
            } // If they are a husband/wife, but not the head of house, mark them as spouse
            elseif (($user->family_role == 'husband' || $user->family_role == 'wife') && $user->family_id != $user->id) {
                $user->family_role = 'spouse';
            }
            // If they are a child, no change
            // If they are other, no change

            $user->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // No reversing!
    }
}
