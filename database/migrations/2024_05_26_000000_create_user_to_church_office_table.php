<?php

use App\Accounts\ChurchOffice;
use App\Users\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_to_church_office', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('church_office_id')->unsigned()->index();
            $table->string('subtitle', 255)->nullable();
            $table->integer('sort_id')->nullable()->unsigned()->index();
        });

        $this->migrate_users();
    }

    public function migrate_users()
    {
        $users = User::doesntHave('churchOffices')
            ->whereNotNull('church_office')
            ->get();

        foreach ($users as $user) {
            $sort_id = 0;
            foreach (User::$church_offices as $office_value => $office_name) {
                if ($user->church_office == $office_value) {
                    $office = ChurchOffice::firstOrCreate([
                        'account_id'  => $user->account_id,
                        'name'        => $office_name,
                        'plural_name' => \Illuminate\Support\Str::plural($office_name),
                        'short_name'  => \Illuminate\Support\Str::kebab($office_name),
                        'url_name'    => \Illuminate\Support\Str::kebab($office_name),
                    ], [
                        'is_public'          => true,
                        'show_in_leadership' => $office_value == 'secretary' ? false : true,
                        'sort_id'            => $sort_id,
                    ]);

                    if (!$office->ulid) {
                        $office->generateUlid();
                    }

                    $user->churchOffices()->syncWithoutDetaching([
                        $office->id => [
                            'subtitle'   => $user->church_office_description,
                            'account_id' => $user->account_id,
                        ],
                    ]);
                }

                $sort_id++;
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_to_role');
    }

};
