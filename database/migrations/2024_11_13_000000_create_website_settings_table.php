<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateWebsiteSettingsTable extends Migration
{
    public function up()
    {
        Schema::create('website_settings', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->string('key', 60)->index();
            $table->string('type', 100)->nullable()->comment('int,string,text,json,html,markdown,datetime,WebsiteFile');
            $table->string('name', 200)->nullable();
            $table->text('default_value')->nullable();

            $table->datetime('is_active')->nullable()->index();
            $table->boolean('account_can_edit')->nullable()->default(true);
        });
    }
}
