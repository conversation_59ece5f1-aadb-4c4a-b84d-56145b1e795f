<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateCalendarFilesTable extends Migration
{

    public function up()
    {
        Schema::create('calendar_files', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->uuid('uuid')->nullable()->index();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('calendar_id')->unsigned()->index();
            $table->integer('calendar_event_id')->unsigned()->nullable()->index();
            $table->integer('calendar_event_occurrence_id')->unsigned()->nullable()->index();
            $table->string('title', 200)->nullable();
            $table->string('description', 500)->nullable();
            $table->string('type', 32)->default('')->nullable()->index()->comment('\'audio\',\'video\',\'document\'');
            $table->string('storage_service', 128)->nullable()->default('do-spaces');
            $table->string('file_original_name', 500)->nullable();
            $table->integer('file_size')->unsigned()->default(0)->comment('in bytes');
            $table->string('data_separator', 32)->nullable()->default('--');
            $table->string('file_folder', 500)->nullable()->comment('The folder in which the file is stored on the storage service');
            $table->string('file_id', 500)->nullable();
            $table->string('file_name', 255)->comment('without extension')->nullable();
            $table->string('file_extension', 16)->nullable();
            $table->string('file_type', 64)->nullable();
            $table->string('file_sha1', 64)->nullable();

            $table->smallInteger('sort_id')->unsigned()->nullable();

            $table->dateTime('is_public')->nullable()->index();
            $table->dateTime('is_image')->nullable()->index();
            $table->dateTime('is_event_background')->nullable()->index()->comment('The event background image, like the header of a facebook page.');
            $table->dateTime('is_event_cover')->nullable()->index()->comment('The event cover image, usually a square, like a podcast cover image.');
            $table->dateTime('is_hidden')->nullable()->index();
        });
    }
}
