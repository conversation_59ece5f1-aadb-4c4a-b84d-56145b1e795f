<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCrisisCheckinsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crisis_checkins', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('crisis_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('family_id')->unsigned()->index();
            $table->string('type', 32)->nullable()->default('not_responded')->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('sent_at')->nullable()->index();
            $table->dateTime('read_at')->nullable()->index();
            $table->dateTime('responded_at')->nullable();
            $table->dateTime('resolved_at')->nullable();
            $table->integer('resolved_by')->nullable()->unsigned();
            $table->boolean('make_anonymous')->default(false);
            $table->string('notes', 500)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('crisis_checkins');
    }

}
