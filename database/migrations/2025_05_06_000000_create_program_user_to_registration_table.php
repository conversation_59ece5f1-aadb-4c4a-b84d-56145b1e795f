<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_user_to_registration', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('program_id')->unsigned()->index();
            $table->integer('program_registration_id')->unsigned()->nullable()->index();
            $table->integer('program_user_id')->unsigned()->nullable()->index()->comment('The user that we are linking to the registration. NOT the registrant.');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('is_contact')->nullable();
            $table->dateTime('is_primary_contact')->nullable();
            $table->dateTime('is_emergency_contact')->nullable();
            $table->dateTime('can_pickup')->nullable();
            $table->dateTime('can_not_pickup')->nullable();
            $table->dateTime('no_contact_allowed')->nullable()->comment('If set, no contact is allowed with the registrant.');

            $table->text('contact_notes')->nullable();
            $table->text('admin_notes')->nullable();

            $table->jsonb('metadata')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_user_to_registration');
    }
};
