<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserPaymentSchedulesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_payment_schedules', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->ulid()->nullable()->index();
            $table->integer('account_id')->unsigned()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('last_run_at')->nullable();

            $table->integer('user_id')->unsigned()->index();
            $table->integer('user_payment_method_id')->unsigned()->index();
            $table->integer('account_finance_bucket_id')->unsigned()->index();

            $table->dateTime('recur_start_at')->nullable();
            $table->dateTime('recur_end_at')->nullable();

            $table->integer('amount')->nullable()->comment('cents');

            $table->string('recur_frequency', 16)->comment('WEEKLY,MONTHLY');
            $table->smallInteger('recur_day_of_week')->unsigned()->nullable()->comment('1-7');
            $table->smallInteger('recur_day_of_month')->unsigned()->nullable()->comment('1-31');

            $table->boolean('is_paused')->default(false)->index();

            $table->text('details')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_payment_schedules');
    }

}
