<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserInteractionsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_interactions', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('user_id')->unsigned()->nullable()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('remind_at')->nullable()->index();
            $table->integer('remind_user_id')->unsigned()->nullable()->index();
            $table->integer('created_by_user_id')->unsigned()->nullable()->index();
            $table->integer('assigned_to_user_id')->unsigned()->nullable()->index();
            $table->dateTime('due_at')->nullable();
            $table->dateTime('completed_at')->nullable();
            $table->integer('completed_by_user_id')->unsigned()->nullable()->index();
            $table->string('action', 500)->nullable()->index();
            $table->text('action_notes')->nullable();
            $table->text('notes')->nullable();
            $table->text('private_notes')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_interactions');
    }

}
