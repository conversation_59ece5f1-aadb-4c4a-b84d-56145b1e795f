<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserPhonesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_phones', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('family_id')->unsigned()->nullable()->index();
            $table->string('type', 255)->default('');
            $table->string('mobile_provider', 255)->nullable()->comment('\'att\',\'verizon\',\'sprint\',\'tmobile\',\'boost\',\'cricket\',\'virgin\'');
            $table->string('number')->nullable()->index();
            $table->string('extension', 32)->nullable();
            $table->boolean('is_family')->default(false)->index();
            $table->boolean('is_hidden')->default(false);
            $table->boolean('is_primary')->default(false);
            $table->boolean('messages_opt_out')->default(false);
            $table->boolean('voice_opt_out')->default(false);
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_phones');
    }

}
