<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitorHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visitor_history', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable()->index();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('archived_at')->nullable()->index();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('visitor_id')->unsigned()->index();
            $table->integer('family_id')->nullable()->unsigned()->index();
            $table->integer('created_by_user_id')->nullable()->unsigned();

            $table->string('type', 32)->nullable()->index();

            $table->text('notes')->nullable();

            $table->boolean('is_contact')->default(false);
            $table->boolean('is_contact_attempt')->default(false);
            $table->boolean('is_action')->default(false);
            $table->boolean('is_info')->default(false);
            $table->boolean('is_request_for_action')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('visitor_history');
    }
}
