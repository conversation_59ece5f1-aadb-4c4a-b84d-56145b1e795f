<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountPlansTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_plans', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->string('type', 128)->nullable()->default('')->index();
            $table->string('name', 500);
            $table->string('url_name', 500)->nullable()->index();
            $table->string('domain', 500)->nullable()->index();

            $table->integer('max_emails_per_send')->unsigned()->nullable()->default(0);
            $table->integer('max_emails_per_month')->unsigned()->nullable()->default(0);
            $table->integer('max_sms_per_send')->unsigned()->nullable();
            $table->integer('max_sms_per_month')->unsigned()->nullable();
            $table->integer('max_voice_per_send')->unsigned()->nullable();
            $table->integer('max_voice_per_month')->unsigned()->nullable();
            $table->integer('max_notifications_per_send')->unsigned()->nullable();
            $table->integer('max_notifications_per_month')->unsigned()->nullable();
            $table->integer('max_users')->unsigned()->nullable()->default(0);
            $table->smallInteger('max_admins')->unsigned()->nullable();
            $table->smallInteger('max_senders')->unsigned()->nullable();

            $table->integer('max_storage')->unsigned()->nullable()->default(10)->comment('in Bytes');

            $table->integer('price_per_month')->nullable()->comment('CENTS');
            $table->integer('price_per_year')->nullable()->comment('CENTS');

            $table->boolean('is_public')->nullable()->default(false)->index();
            $table->boolean('is_active')->nullable()->default(false)->index();
            $table->boolean('is_recommended')->nullable()->default(false);

            $table->boolean('allow_signups')->nullable()->default(false)->index();

            $table->integer('free_emails_per_month')->unsigned()->nullable()->default(0);
            $table->integer('free_sms_per_month')->unsigned()->nullable();
            $table->integer('free_voice_per_month')->unsigned()->nullable();
            $table->integer('free_notifications_per_month')->unsigned()->nullable();
            $table->integer('free_storage_per_month')->unsigned()->nullable()->comment('in Bytes'); // SEPT 2021
            $table->integer('free_podcast_downloads_per_month')->unsigned()->nullable();            // SEPT 2021

            $table->decimal('price_per_email', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('price_per_sms', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('price_per_voice', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('price_per_notification', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('price_per_10000_podcast_downloads', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS'); // SEPT 2021
            $table->decimal('price_per_50000_podcast_downloads', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS'); // SEPT 2021

            $table->decimal('price_per_100_users', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS'); // MARCH 2022

            $table->decimal('monthly_price_per_email_group', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('monthly_price_per_sms_group', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('monthly_price_per_voice_group', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('monthly_price_per_notification_group', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');
            $table->decimal('monthly_price_per_gb_storage', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');

            $table->decimal('monthly_price_online_giving', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS'); // SEPT 2021
            $table->decimal('monthly_price_podcasts', 10, 6)->unsigned()->nullable()->default(0.000000)->comment('CENTS');      // SEPT 2021

            $table->integer('included_users')->unsigned()->nullable()->default(50)->comment('The number of users included by default.'); // OCT 2022
            $table->integer('monthly_price_base')->nullable()->default(2900)->comment('CENTS');                                          // OCT 2022
            $table->integer('monthly_price_attendance')->nullable()->default(0)->comment('CENTS');                                       // OCT 2022
            $table->integer('monthly_price_assignments')->nullable()->default(0)->comment('CENTS');                                      // OCT 2022
            $table->integer('monthly_price_visitor_tracking')->nullable()->default(0)->comment('CENTS');                                 // OCT 2022
            $table->integer('monthly_price_child_checkin')->nullable()->default(0)->comment('CENTS');                                    // OCT 2022
            $table->integer('monthly_price_financial_management')->nullable()->default(0)->comment('CENTS');                             // OCT 2022
            $table->integer('monthly_price_sms_enabled')->nullable()->default(0)->comment('CENTS');                                      // NOV 2023
            $table->integer('monthly_price_website')->nullable()->default(0)->comment('CENTS');                                          // DEC 2024
            $table->integer('monthly_price_domain_management')->nullable()->default(0)->comment('CENTS');                                // DEC 2024
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_plans');
    }

}
