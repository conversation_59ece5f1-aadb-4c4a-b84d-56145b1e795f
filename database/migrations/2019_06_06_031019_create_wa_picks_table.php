<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWaPicksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wa_picks', function (Blueprint $table) {
            $table->bigIncrements('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->integer('wa_group_id')->unsigned()->index();
            $table->integer('wa_period_id')->unsigned()->index();
            $table->integer('wa_position_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->nullable()->index();
            $table->date('start_at')->nullable()->index();
            $table->date('end_at')->nullable();
            $table->boolean('span_whole_period')->default(false);
            $table->tinyInteger('day_of_week')->nullable()->index()->comment('ISO-8601 numeric representation of the day of the week');
            $table->dateTime('sent_email')->nullable()->default(null);
            $table->dateTime('sent_sms')->nullable()->default(null);
            $table->dateTime('is_confirmed')->nullable()->default(null);
            $table->dateTime('is_declined')->nullable()->default(null);
            $table->dateTime('has_replied')->nullable()->default(null);
            $table->string('notes', 400)->nullable();
            $table->string('token', 13)->nullable();
            $table->dateTime('reminder_last_sent_at')->nullable()->default(null);

            $table->dateTime('reminder_1_sent_at')->nullable()->default(null);
            $table->dateTime('reminder_2_sent_at')->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wa_picks');
    }
}
