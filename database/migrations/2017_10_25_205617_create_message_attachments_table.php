<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessageAttachmentsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_attachments', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('message_id')->unsigned()->index();
            $table->string('storage_service', 128)->nullable()->default('do-spaces');
            $table->string('file_original_name', 500)->nullable();
            $table->bigInteger('file_size')->unsigned()->default(0)->comment('in bytes');
            $table->string('data_separator', 32)->nullable()->default('--');
            $table->string('file_folder', 500)->nullable();
            $table->string('file_id', 500)->nullable();
            $table->string('file_name', 500)->comment('without extension');
            $table->string('file_extension', 16);
            $table->string('file_type', 256)->nullable();
            $table->string('file_sha1', 64)->nullable();
            $table->string('inline_content_id', 500)->nullable()->default(null);
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('message_attachments');
    }

}
