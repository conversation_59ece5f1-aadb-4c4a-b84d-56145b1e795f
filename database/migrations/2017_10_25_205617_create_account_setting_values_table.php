<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountSettingValuesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_setting_values', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->unsigned()->nullable()->index();
            $table->integer('account_setting_id')->unsigned()->index();
            $table->text('value')->nullable();
            $table->boolean('enable_for_member')->nullable()->default(false)->index();
            $table->boolean('enable_for_admin')->nullable()->default(true)->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_settings');
    }

}
