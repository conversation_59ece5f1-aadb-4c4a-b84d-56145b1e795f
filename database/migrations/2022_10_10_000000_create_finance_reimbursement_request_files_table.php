<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFinanceReimbursementRequestFilesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_reimbursement_request_files', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index('frrf_account_id');

            $table->dateTime('created_at')->nullable()->index('frrf_created_at');
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index('frrf_deleted_at');

            $table->integer('user_id')->unsigned()->default(null)->index('frrf_user_id');
            $table->integer('created_by_user_id')->unsigned()->nullable();
            $table->integer('finance_reimbursement_request_id')->unsigned()->default(null)->index('frrf_finance_reimbursement_request_id');

            $table->string('title', 200)->nullable();
            $table->text('description')->nullable();

            $table->string('type', 32)->default('')->index('frrf_type')->comment('audio,video,image,file');
            $table->string('storage_service', 32)->nullable()->default('linode');

            $table->string('file_original_name', 500)->nullable();
            $table->integer('file_size')->unsigned()->default(0)->comment('in bytes');
            $table->string('data_separator', 32)->nullable()->default('--');

            $table->string('file_folder', 500)->nullable();
            $table->string('file_id', 500)->nullable();
            $table->string('file_name', 500)->comment('without extension')->nullable();
            $table->string('file_extension', 16)->nullable();
            $table->string('file_type', 64)->nullable();
            $table->string('file_sha1', 64)->nullable();

            $table->mediumInteger('sort_id')->unsigned()->nullable()->index('frrf_sort_id');

            $table->integer('width')->nullable()->default(0);
            $table->integer('height')->nullable()->default(0);

            $table->boolean('has_original')->default(true);
            $table->boolean('has_1024')->default(true);
            $table->boolean('has_512')->default(true);
            $table->boolean('has_256')->default(false);
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('finance_reimbursement_request_files');
    }

}
