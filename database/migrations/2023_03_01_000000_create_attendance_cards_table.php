<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attendance_cards', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('archived_at')->nullable()->index();
            $table->dateTime('read_at')->nullable();

            $table->integer('account_id')->unsigned()->index();

            $table->ipAddress()->nullable();

            $table->integer('user_id')->nullable()->unsigned();
            $table->integer('family_id')->nullable()->unsigned();

            $table->string('name', 400)->nullable();
            $table->string('first_name', 250)->nullable();
            $table->string('last_name', 250)->nullable();
            $table->string('spouse_name', 250)->nullable();
            $table->text('family_member_names')->nullable();

            $table->string('address1', 200)->nullable();
            $table->string('address2', 200)->nullable();
            $table->string('city', 80)->nullable();
            $table->string('state', 80)->nullable();
            $table->string('zip', 40)->nullable();
            $table->string('country', 80)->nullable();

            $table->string('phone', 40)->nullable();
            $table->string('email', 250)->nullable();

            $table->string('guest_of', 200)->nullable();
            $table->text('comments')->nullable();

            $table->boolean('is_member')->nullable()->default(false);
            $table->boolean('is_visitor')->nullable()->default(false);

            $table->dateTime('is_spam')->nullable();

            $table->integer('marked_read_user_id')->nullable()->unsigned();
            $table->integer('marked_archive_user_id')->nullable()->unsigned();
            $table->integer('marked_spam_user_id')->nullable()->unsigned();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attendance_cards');
    }
};
