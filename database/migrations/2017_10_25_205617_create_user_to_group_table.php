<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserToGroupTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_to_group', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('user_group_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();

            // ATTRIBUTES
            $table->boolean('receive_group_emails')->unsigned()->nullable()->default(true);
            $table->boolean('receive_group_sms')->unsigned()->nullable()->default(true);
            $table->boolean('receive_group_voice')->unsigned()->nullable()->default(true);
            $table->boolean('receive_group_post_email_summaries')->unsigned()->nullable()->default(false);
            $table->boolean('receive_group_post_mobile_notifications')->unsigned()->nullable()->default(true);
            $table->boolean('receive_all_group_post_comment_mobile_notifications')->unsigned()->nullable()->default(false);
            $table->boolean('receive_group_own_post_comment_mobile_notifications')->unsigned()->nullable()->default(true);

            $table->boolean('is_favorite')->unsigned()->nullable()->default(false);
            $table->boolean('is_admin')->unsigned()->nullable()->default(false);

            // Added 2025-03
            $table->boolean('can_post')->unsigned()->nullable()->default(false)->comment('Can post group posts');
            $table->boolean('can_invite')->unsigned()->nullable()->default(false)->comment('Can invite people to group');
            $table->boolean('can_invite_to_events')->unsigned()->nullable()->default(false)->comment('Can invite people to events outside of the app account');
            $table->boolean('can_create_events')->unsigned()->nullable()->default(false)->comment('Can create ANY event');
            $table->boolean('can_edit_events')->unsigned()->nullable()->default(false)->comment('Can edit ANY event');

            $table->boolean('can_sms')->unsigned()->nullable()->default(false)->comment('Can send SMS to the group');
            $table->boolean('can_email')->unsigned()->nullable()->default(false)->comment('Can send email to the group');

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('last_updated_by')->unsigned()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_to_group');
    }

}
