<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserAddressesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_addresses', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('family_id')->unsigned()->nullable()->default(null)->index();
            $table->string('type', 255)->nullable()->default(null);
            $table->string('label', 500)->nullable()->default(null);
            $table->string('address1', 500)->default(null)->index();
            $table->string('address2', 500)->nullable();
            $table->string('address3', 500)->nullable();
            $table->string('city', 500)->default('');
            $table->string('state', 500)->nullable();
            $table->string('zip', 64)->nullable();
            $table->string('county', 128)->nullable();
            $table->string('country', 128)->nullable();
            $table->boolean('is_family')->index()->default(false);
            $table->boolean('is_mailing')->index()->nullable()->default(false);
            $table->boolean('is_hidden')->index()->nullable()->default(false);
            $table->boolean('is_primary_for_user')->index()->nullable()->default(false);
            $table->string('map_thumbnail_url_512', 500)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_addresses');
    }

}
