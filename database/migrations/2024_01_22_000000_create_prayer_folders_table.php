<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePrayerFoldersTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('prayer_folders', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_group_id')->unsigned()->nullable()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('last_updated_at')->nullable()->index();

            $table->integer('created_by')->unsigned()->nullable();

            $table->string('name', 300)->nullable();
            $table->text('description')->nullable();

            $table->boolean('is_hidden')->nullable()->default(false)->index();

            $table->integer('sort_id')->unsigned()->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('prayer_folders');
    }

}
