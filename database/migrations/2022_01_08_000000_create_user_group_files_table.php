<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserGroupFilesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_group_files', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->mediumInteger('account_id')->unsigned()->default(null)->index();
            $table->integer('user_group_id')->unsigned()->default(null)->index();
            $table->integer('user_group_post_id')->unsigned()->nullable()->default(null)->index();
            $table->integer('user_group_post_comment_id')->unsigned()->nullable()->default(null)->index();
            $table->integer('user_id')->unsigned()->default(null)->index();
            $table->string('storage_service', 16)->nullable()->default('do-spaces');
            $table->integer('file_size')->unsigned()->default(0)->comment('in bytes');
            $table->string('data_separator', 12)->nullable()->default('--');
            $table->string('file_folder', 500)->nullable();
            $table->string('file_id', 500)->nullable();
            $table->string('file_name_original', 500)->nullable();
            $table->string('file_name', 255)->comment('without extension');
            $table->string('file_extension', 8);
            $table->string('file_type', 80)->nullable();
            $table->string('file_sha1', 64)->nullable();
            $table->text('remote_gif_url')->nullable();
            $table->mediumInteger('width')->unsigned()->nullable();
            $table->mediumInteger('height')->unsigned()->nullable();
            $table->string('has_1024', 255)->nullable()->comment('without extension');
            $table->string('has_512', 255)->nullable()->comment('without extension');
            $table->string('has_256', 255)->nullable()->comment('without extension');
            $table->boolean('is_remote_gif')->default(false);
            $table->boolean('is_image')->default(false);
            $table->boolean('is_video')->default(false);
            $table->boolean('is_document')->default(false);
            $table->boolean('force_download')->default(false);
            $table->boolean('is_flagged')->default(false);
            $table->integer('views')->unsigned()->nullable();
            $table->integer('downloads')->unsigned()->nullable();
            $table->mediumInteger('sort_id')->nullable()->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_group_files');
    }

}
