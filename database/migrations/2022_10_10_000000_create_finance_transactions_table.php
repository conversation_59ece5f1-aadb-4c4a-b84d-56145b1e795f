<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFinanceTransactionsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_transactions', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->integer('created_by')->unsigned()->nullable();
            $table->integer('user_id')->unsigned()->nullable()->index();
            $table->integer('finance_vendor_id')->unsigned()->nullable()->index();
            $table->integer('user_payment_id')->unsigned()->nullable();
            $table->integer('account_finance_bucket_id')->unsigned()->nullable()->index();

            $table->string('title', 200)->nullable()->comment('Title / Payee');
            $table->text('notes')->nullable();
            $table->json('tags')->nullable();

            $table->string('check_number', 24)->nullable();
            $table->string('check_account_number', 64)->nullable()->comment('Hash of account number.');
            $table->string('check_account_number_last4', 16)->nullable()->comment('Last 4 digits of account number.');
            $table->string('check_routing_number', 64)->nullable();

            $table->dateTime('posted_at')->nullable()->comment('Date the transaction was charged.');

            $table->dateTime('captured_at')->nullable()->comment('Date the transaction was charged. Only applicable with user payments from a payment processor.');
            $table->dateTime('cleared_at')->nullable()->comment('Date the transaction was processed. Only applicable with user payments from a payment processor.');
            $table->dateTime('disputed_at')->nullable()->comment('Date the transaction was disputed. Only applicable with user payments from a payment processor.');
            $table->dateTime('deposited_at')->nullable()->comment('Date the transaction was deposited to a checking account or similar.');

            $table->string('source', 24)->nullable()->index()->comment('card|bank_account|check|cash|account|other');

            $table->integer('amount_original')->nullable()->default(0)->comment('cents -- Original amount if a payment failed.');
            $table->integer('amount')->nullable()->default(0)->comment('cents -- Final amount after it has been cleared of any payment processors, etc. Does not include fees.');
            $table->integer('amount_fee')->nullable()->default(0)->comment('cents -- If a user payment, this is the payment processor fees.');
            $table->integer('amount_deposited')->nullable()->default(0)->comment('cents -- If a user payment, how much was deposited to a checking account.');
//            $table->integer('amount_platform_fee')->nullable()->default(0)->comment('cents -- Lightpost fee. Never applicable.');
            $table->integer('amount_refunded')->nullable()->default(0)->comment('cents -- If there is a refund to the user, note that amount here.');

            $table->string('currency', 6)->nullable()->default('USD');

            $table->boolean('is_income')->nullable()->default(false);
            $table->boolean('is_expense')->nullable()->default(false);
            $table->boolean('is_contribution')->nullable()->default(false);
            $table->boolean('is_payment')->nullable()->default(false)->comment('A payment could be a transaction from a user towards Camp Bandina, or towards a Youth Devo, that might not be a "contribution".');
            $table->boolean('is_reimbursement')->nullable()->default(false)->comment('Indicates an amount subtracted from a budget that was a outgoing payment to a user.');

            $table->boolean('is_hidden')->nullable()->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finance_transactions');
    }

}
