<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChildCheckinActivitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('child_checkin_activities', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('archived_at')->nullable()->index();

            $table->string('type', 64)->nullable();
            $table->text('notes')->nullable();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('child_checkin_id')->unsigned()->index();
            $table->integer('child_user_id')->unsigned()->nullable();

            $table->integer('created_by_user_id')->unsigned()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('child_checkin_activities');
    }
}
