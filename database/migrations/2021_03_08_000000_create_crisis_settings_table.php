<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCrisisSettingsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('crisis_settings', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable()->index();
            $table->jsonb('notify_groups')->nullable();
            $table->jsonb('help_notification_groups')->nullable();
            $table->jsonb('urgent_help_notification_groups')->nullable();
            $table->boolean('send_via_email')->default(true);
            $table->boolean('send_via_mobile_notification')->default(true);
        });
    }


    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('crisis_settings');
    }

}
