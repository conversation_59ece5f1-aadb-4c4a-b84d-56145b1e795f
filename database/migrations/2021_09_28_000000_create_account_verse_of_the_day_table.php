<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAccountVerseOfTheDayTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_verse_of_the_day', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('start_at')->nullable()->index();

            $table->integer('account_id')->nullable()->unsigned()->index();

            $table->string('book_chapter_verse', 64)->nullable();
            $table->string('version', 32)->nullable();

            $table->text('text')->nullable();
            $table->text('link')->nullable();

            $table->mediumInteger('sort_id')->nullable()->unsigned()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('account_verse_of_the_day');
    }
}
