<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_activity', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('program_id')->unsigned()->index();
            $table->integer('program_user_id')->unsigned()->index()->nullable();
            $table->integer('program_group_id')->unsigned()->index()->nullable();
            $table->integer('created_by_user_id')->unsigned()->nullable();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->string('title', 180)->nullable();
            $table->string('url_title', 180)->nullable();
            $table->text('content')->nullable();

            $table->boolean('allow_comments')->nullable()->default(false);

            $table->boolean('is_sensitive')->default(false);
            $table->boolean('is_shared')->default(false);
            $table->boolean('is_hidden')->default(false)->index();
            $table->boolean('is_pinned')->default(false)->index();
            $table->boolean('is_public')->default(false)->index();
            $table->boolean('is_flagged')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_activity');
    }
};
