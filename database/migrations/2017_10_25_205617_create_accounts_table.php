<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->ulid()->nullable()->index();
            $table->string('hash_id', 24)->nullable()->index();
            $table->integer('account_plan_id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('last_payment_at')->nullable();
            $table->dateTime('last_invoice_at')->nullable();
            $table->dateTime('next_invoice_at')->nullable();
            $table->string('billing_frequency', 32)->nullable()->default('monthly');
            $table->string('name', 500)->default('');
            $table->string('short_name', 500)->nullable();
            $table->string('url_name', 500)->nullable();
            $table->string('church_website', 500)->nullable();
            $table->string('group_email_prefix', 32)->nullable();
            $table->string('static_short_name', 255)->nullable()->index();
            $table->string('podcasts_prefix', 32)->nullable()->index();
            $table->string('domain', 500)->nullable()->index();
            $table->string('subdomain', 500)->nullable()->index();
            $table->string('cname_domain', 255)->nullable();
            $table->string('cname_subdomain', 255)->nullable();
            $table->string('info_separator', 32)->nullable();
            $table->boolean('use_ssl')->nullable()->default(true);
            $table->string('file_storage_service', 128)->nullable();
            $table->string('s3_base_url', 500)->nullable();
            $table->string('s3_region', 500)->nullable();
            $table->string('s3_bucket', 500)->nullable();
            $table->string('s3_folder', 500)->nullable();
            $table->boolean('require_verified_emails')->nullable()->default(false);
            $table->boolean('require_verified_sms')->nullable()->default(false);
            $table->string('address1', 500)->nullable()->default('');
            $table->string('address2', 500)->nullable()->default('');
            $table->string('address3', 500)->nullable()->default('');
            $table->string('city', 500)->nullable()->default('');
            $table->string('state', 500)->nullable()->default('');
            $table->string('postal_code', 100)->nullable()->default('');
            $table->string('country_code', 8)->nullable()->default('');
            $table->string('phone_work', 32)->nullable()->default('');
            $table->string('phone_fax', 32)->nullable()->default('');
            $table->string('phone_other', 32)->nullable()->default('');
            $table->string('ein', 32)->nullable()->default(null);
            $table->string('ein_name', 300)->nullable()->default(null);
            $table->string('timezone', 32)->nullable()->default('');
            $table->string('email', 500)->nullable();

            $table->string('status', 128)->default('active')->index();
            $table->boolean('is_active')->default(false)->index();
            $table->boolean('is_late_on_payment')->default(false)->index();
            $table->boolean('billing_active')->nullable()->default(true)->index();
            $table->boolean('is_suspended')->default(false)->index();
            $table->boolean('is_tax_exempt')->nullable()->default(false);

            $table->string('api_token', 128)->nullable()->index();

            $table->string('stripe_customer_id', 128)->nullable()->comment('Customer ID for charging for Lightpost.');
            $table->string('stripe_payment_method_id', 128)->nullable()->comment('Payment Method ID for charging the customer for Lightpost.');
            $table->string('stripe_account_id', 128)->nullable()->comment('Account ID for Onling Giving of the congregations Stripe Account.');
            $table->string('stripe_granted_scope', 32)->nullable()->comment('Scope for access to their Stripe Account.');
            $table->text('stripe_account_publishable_key')->nullable()->comment('Public key for their Stripe Account.');

            $table->boolean('use_v2_billing')->nullable()->default(true);

            $table->string('payment_receipt_email', 500)->nullable();

            $table->index(['cname_subdomain', 'cname_domain'], 'accounts_cname_subdomain_index');

            $table->string('mailing_address1', 500)->nullable()->default('');
            $table->string('mailing_address2', 500)->nullable()->default('');
            $table->string('mailing_address3', 500)->nullable()->default('');
            $table->string('mailing_city', 500)->nullable()->default('');
            $table->string('mailing_state', 500)->nullable()->default('');
            $table->string('mailing_postal_code', 100)->nullable()->default('');
            $table->string('mailing_country_code', 8)->nullable()->default('');
            $table->string('congregation_contact_email', 500)->nullable()->default('');
            $table->string('public_contact_email', 500)->nullable()->default('');
            $table->string('billing_contact_email', 500)->nullable();

            $table->boolean('can_send_emails')->nullable()->default(false);
            $table->boolean('can_send_sms')->nullable()->default(false);
            $table->boolean('can_send_voice_calls')->nullable()->default(false);

            $table->dateTime('canceled_at')->nullable();
            $table->text('canceled_reason')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('accounts');
    }

}
