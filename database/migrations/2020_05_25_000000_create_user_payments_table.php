<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserPaymentsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_payments', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('cleared_at')->nullable();
            $table->dateTime('captured_at')->nullable();
            $table->dateTime('disputed_at')->nullable();
            $table->dateTime('deposited_at')->nullable();
            $table->dateTime('applied_at')->nullable();

            $table->integer('user_id')->unsigned()->nullable()->index();
            $table->integer('user_payment_method_id')->unsigned()->nullable();
            $table->integer('user_payment_schedule_id')->unsigned()->nullable();
//            $table->integer('account_contribution_bucket_id')->unsigned()->nullable()->index();
            $table->integer('account_finance_bucket_id')->unsigned()->nullable()->index();
            $table->integer('account_payout_id')->unsigned()->nullable()->index();

            $table->integer('finance_transaction_id')->unsigned()->nullable()->index();

            $table->string('provider', 16)->nullable()->index();
            $table->string('provider_payment_id', 64)->nullable();

            $table->string('stripe_charge_id', 64)->nullable();
            $table->json('stripe_charge_object')->nullable();
            $table->string('stripe_status', 32)->nullable();
            $table->string('stripe_balance_transaction_id', 32)->nullable();

            $table->string('type', 24)->nullable()->index()->comment('card|bank_account|check|cash');

            $table->string('title', 200)->nullable();
            $table->text('description')->nullable();

            $table->integer('original_amount')->nullable()->comment('cents -- Original amount if a payment failed.');
            $table->integer('amount')->nullable()->comment('cents -- Final amount after it has been cleared of any payment processors, etc. Does not include fees.');
            $table->integer('amount_fee')->nullable()->comment('cents');
            $table->integer('amount_deposited')->nullable()->comment('cents');
            $table->integer('amount_platform_fee')->nullable()->comment('cents');
            $table->integer('amount_refunded')->nullable()->comment('cents');

            $table->boolean('is_contribution')->nullable()->default(false)->index();
            $table->boolean('is_payment')->nullable()->default(false)->index();
            $table->boolean('is_reimbursement')->nullable()->default(false)->index();
            $table->boolean('is_hidden')->nullable()->default(false)->index();

            $table->boolean('is_pending')->nullable()->default(false);
            $table->boolean('is_success')->nullable()->default(false);
            $table->boolean('is_failed')->nullable()->default(false);

            $table->string('status', 32)->nullable()->comment('');
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_payments');
    }

}
