<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visitors', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable()->index();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('archived_at')->nullable()->index();

            $table->dateTime('reminder_at')->nullable()->index();
            $table->dateTime('first_contact_at')->nullable()->index();
            $table->dateTime('last_contact_at')->nullable()->index();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->integer('family_id')->nullable()->unsigned()->index();
            $table->integer('created_by_user_id')->nullable()->unsigned();
            $table->integer('archived_by_user_id')->nullable()->unsigned();

            $table->integer('visitor_status_id')->unsigned()->index();

            $table->text('background_info')->nullable();
            $table->text('notes')->nullable();

            $table->integer('sort_id')->unsigned()->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('visitors');
    }
}
