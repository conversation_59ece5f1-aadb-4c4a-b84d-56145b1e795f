<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateWebsiteSettingValuesTable extends Migration
{
    public function up()
    {
        Schema::create('website_setting_values', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('website_setting_id')->unsigned()->index();

            $table->integer('created_by_id')->nullable()->unsigned();
            $table->integer('updated_by_id')->nullable()->unsigned();

            $table->text('value')->nullable();
        });
    }
}
