<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateCalendarEventOccurrencesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('calendar_event_occurrences', function (Blueprint $table) {
            $table->increments('id');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('start_at')->nullable()->index();
            $table->dateTime('end_at')->nullable()->index();
            $table->date('start_at_date')->nullable()->index();
            $table->date('end_at_date')->nullable()->index();
            $table->time('start_at_time')->nullable();
            $table->time('end_at_time')->nullable();

            $table->smallInteger('account_id')->unsigned()->index();
            $table->mediumInteger('calendar_id')->unsigned()->index();
            $table->integer('calendar_event_id')->unsigned()->index();
            $table->integer('user_group_id')->nullable()->unsigned()->index();

            $table->boolean('is_hidden')->nullable()->default(false);
            $table->boolean('is_private')->nullable()->default(false);
            $table->boolean('is_group_only')->nullable()->default(false);
            $table->boolean('is_public')->nullable()->default(false);

            $table->boolean('enable_responses')->nullable()->default(false);
            $table->boolean('show_responses')->nullable()->default(false);

            $table->boolean('enable_payments')->nullable()->default(false);

            // Below are new fields as of 2025-04
            $table->uuid('uuid')->nullable()->index();
            $table->string('timezone', 32)->nullable();
            $table->time('duration')->nullable()->comment('HH:MM:SS');
            $table->boolean('is_all_day')->default(false);

            $table->dateTime('is_occurrence_modified')->nullable()->comment('Event has been modified from the original event and may not follow a recurring pattern');

            $table->date('local_start_at_date')->nullable()->comment('What date the user expects in their local date.');
            $table->date('local_end_at_date')->nullable()->comment('What date the user expects in their local date.');
            $table->time('local_start_at_time')->nullable()->comment('What time the user expects in their local time.');
            $table->time('local_end_at_time')->nullable()->comment('What time the user expects in their local time.');

            $table->mediumInteger('max_signups')->unsigned()->nullable()->comment('The maximum number of people allowed to sign up for this event before we start putting them on waitlists, if set.');
            $table->dateTime('enable_waitlist')->nullable();

            $table->string('title', 200)->default('');
            $table->string('overview', 500)->nullable();
            $table->text('description')->nullable();
            $table->string('location', 300)->nullable();
            $table->text('internal_notes')->nullable()->comment('Internal notes for admins only.');

            $table->dateTime('enable_comments')->nullable()->comment('Allow logged in users to comment on this event');

            $table->dateTime('responses_require_name')->nullable();
            $table->dateTime('responses_require_email')->nullable();
            $table->dateTime('responses_require_phone')->nullable();

            $table->integer('created_by_user_id')->unsigned()->nullable();
            $table->integer('updated_by_user_id')->unsigned()->nullable();
            $table->integer('owner_user_id')->unsigned()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('calendar_event_occurrences');
    }

}
