<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_registrations', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->uuid()->nullable()->index();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('program_id')->unsigned()->index();
            $table->integer('program_registration_form_id')->nullable()->unsigned()->index()->comment('A link to the form that links to answers of form questions.');
            $table->integer('program_user_id')->nullable()->unsigned()->index()->comment('The user that is the REGISTRANT. This is not "contacts" for the registrant.');

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('is_approved')->nullable();
            $table->dateTime('is_waitlist')->nullable();

            $table->unsignedSmallInteger('registration_counter')->nullable()->comment('A count of who registered first.');

            $table->text('registrant_notes')->nullable()->comment('Notes about the registrant.');
            $table->text('contact_notes')->nullable()->comment('Notes about the contacts.');
            $table->text('admin_notes')->nullable()->comment('Notes for the admin.');

            $table->text('metadata')->nullable()->comment('JSON metadata for the registration.');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_registrations');
    }
};
