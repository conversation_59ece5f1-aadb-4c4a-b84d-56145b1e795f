<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserGroupNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_group_notifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->dateTime('created_at')->nullable()->index();

            $table->integer('user_id')->unsigned()->index();

            $table->mediumInteger('user_group_id')->unsigned()->index();
            $table->integer('user_group_post_id')->unsigned()->nullable()->index();

            $table->string('message', 200)->nullable();
            $table->string('type', 40)->nullable();

            $table->boolean('is_read')->default(false)->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_group_notifications');
    }
}
