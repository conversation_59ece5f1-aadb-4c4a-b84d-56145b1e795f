<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attendance_card_question_answers', function (Blueprint $table) {
            $table->increments('id')->unsigned()->index('attendance_card_q_a_index');
            $table->dateTime('created_at')->nullable()->index('attendance_card_q_a_created_at');
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index('attendance_card_q_a_deleted_at');

            $table->integer('account_id')->unsigned()->index('attendance_card_q_a_account_id');

            $table->integer('attendance_card_question_id')->nullable()->unsigned()->index('attendance_card_q_a_acq_id');

            $table->text('answer')->nullable();

            $table->string('option1', 200)->nullable();
            $table->string('option2', 200)->nullable();
            $table->string('option3', 200)->nullable();
            $table->string('option4', 200)->nullable();
            $table->string('option5', 200)->nullable();
            $table->string('option6', 200)->nullable();
            $table->string('option7', 200)->nullable();
            $table->string('option8', 200)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attendance_card_question_answers');
    }
};
