<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserNotificationTargetsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_notification_targets', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->integer('user_notification_id')->unsigned()->index();
            $table->integer('user_device_id')->unsigned()->nullable();
            $table->string('provider_transaction_id', 40)->nullable()->index();

            $table->tinyInteger('is_ok')->unsigned()->nullable()->default('0');
            $table->tinyInteger('is_confirmed')->unsigned()->nullable()->default('0')->comment('If we confirmed the delivery status with the provider.');
            $table->string('error_message', 300)->nullable()->comment('If there is an error - record it.');
        });
    }
}
