<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSermonsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sermons', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->nullable()->unsigned()->index();
            $table->integer('user_attendance_type_id')->nullable()->unsigned()->index();
            $table->date('date_sermon')->index('date_sermon');
            $table->string('language', 32)->nullable()->default('en');
            $table->string('speaker', 500)->nullable();
            $table->integer('speaker_user_id')->nullable()->index();
            $table->string('title', 500);
            $table->string('type', 255)->nullable()->index();
            $table->text('summary')->nullable();
            $table->text('description')->nullable();
            $table->text('video_link')->nullable();
            $table->text('podcast_link')->nullable();
            $table->boolean('is_public')->default(true)->index()->comment('Whether this is open to the public (beyond Lightpost users).');
            $table->boolean('is_hidden')->default(false)->index()->comment('Whether this is hidden to Lightpost users.');
            $table->string('youtube_id', 64)->nullable();
            $table->jsonb('youtube_data')->nullable();
            $table->text('youtube_link')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('sermons');
    }

}
