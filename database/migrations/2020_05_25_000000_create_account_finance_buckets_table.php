<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAccountFinanceBucketsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_finance_buckets', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('finance_bucket_category_id')->unsigned()->nullable()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->string('name', 200)->nullable();
            $table->string('url_name', 200)->nullable();
            $table->text('description')->nullable();

            $table->string('account_code', 64)->nullable();

            $table->boolean('is_contribution_bucket')->nullable()->default(false)->index();
            $table->boolean('is_reimbursement_bucket')->nullable()->default(false)->index();

            $table->boolean('is_income')->nullable()->default(false);
            $table->boolean('is_expense')->nullable()->default(true);

            $table->boolean('is_hidden')->nullable()->default(false)->index();
            $table->integer('sort_id')->nullable()->unsigned()->index();

            // DEPRECATED - 2022-12-05
            $table->integer('account_location_id')->unsigned()->nullable();
            $table->integer('yearly_budget')->nullable();
            $table->integer('monthly_budget')->nullable();
            $table->integer('weekly_budget')->nullable();
            $table->date('quarter_start_at')->nullable();
            $table->date('year_start_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('account_finance_buckets');
    }

}
