<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->uuid()->nullable()->index();

            $table->string('squid', 12)->nullable()->index('program_squid_index');

            $table->string('name', 300)->nullable();
            $table->string('url_name', 300)->nullable();
            $table->text('overview')->nullable();
            $table->text('description')->nullable();
            $table->text('location')->nullable();
            $table->text('location_details')->nullable();
            $table->boolean('is_active')->default(false);
            $table->boolean('enable_public_registration')->default(false);
            $table->boolean('enable_waitlist')->nullable();

            $table->dateTime('start_at')->nullable()->index();
            $table->dateTime('end_at')->nullable()->index();
            $table->time('start_at_time')->nullable();
            $table->time('end_at_time')->nullable();

            $table->text('og_image_url')->nullable();
            $table->text('og_image_path')->nullable();
            $table->text('cover_image_url')->nullable();
            $table->text('cover_image_path')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('programs');
    }
};
