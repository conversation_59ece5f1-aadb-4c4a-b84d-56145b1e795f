<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_user_checkins', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('checkin_at')->nullable()->index();
            $table->dateTime('checkout_at')->nullable()->index();

            $table->uuid('uuid')->nullable();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('program_id')->unsigned()->index();
            $table->integer('program_group_id')->unsigned()->nullable()->index();
            $table->integer('program_user_id')->unsigned()->index();

            $table->integer('checkin_by_user_id')->unsigned()->nullable()->comment('Lightpost User ID');
            $table->integer('checkout_by_user_id')->unsigned()->nullable()->comment('Lightpost User ID');

            $table->integer('created_by_user_id')->unsigned()->nullable()->comment('Lightpost User ID');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_user_checkins');
    }
};
