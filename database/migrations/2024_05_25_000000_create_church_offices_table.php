<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('church_offices', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->ulid()->nullable()->index();

            $table->integer('account_id')->nullable()->unsigned()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->string('name', 125)->default('');
            $table->string('plural_name', 125)->default('');
            $table->string('short_name', 60)->nullable();
            $table->string('url_name', 60)->nullable();

            $table->string('subtitle', 100)->nullable();
            $table->text('description')->nullable();

            $table->boolean('show_in_leadership')->default(true)->index();
            $table->boolean('is_public')->default(true)->index();
            $table->smallInteger('sort_id')->unsigned()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('church_offices');
    }

};
