<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountSettingsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_settings', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->string('key', 64)->nullable()->index();
            $table->string('name', 128)->nullable();
            $table->text('description')->nullable();
            $table->string('type', 16)->nullable()->default('bool');
            $table->boolean('allow_account_to_view')->nullable()->default(false)->index()->comment('Should the account be able to see this setting?');
            $table->boolean('allow_account_to_enable')->nullable()->default(false)->index()->comment('Should the account be allowed to enable/disable this setting?');
            $table->boolean('allow_account_to_select_permissions')->nullable()->default(false)->index()->comment('Unused ?');
            $table->string('default', 250)->nullable()->comment('Default value if not set yet.');
            $table->text('values')->nullable()->comment('JSON array of values a customer can choose from.');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_settings');
    }

}
