<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserGroupPostsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_group_posts', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_group_id')->unsigned()->index();
            $table->integer('user_group_post_id')->unsigned()->nullable()->index()->comment('Parent post, if a shared post.');
            $table->integer('creator_id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('published_at')->nullable()->index();
            $table->dateTime('last_active_at')->nullable()->index();
            $table->dateTime('last_edited_at')->nullable();

            $table->string('title', 180)->nullable();
            $table->string('url_title', 180)->nullable();
            $table->text('content')->nullable();

            $table->integer('calendar_event_occurrence_id')->unsigned()->nullable()->index()->comment('Ability to link to a calendar event occurrence.');

            $table->text('link')->nullable()->comment('If sharing a website link.');

            $table->text('poll_question')->nullable();
            $table->jsonb('poll_options')->nullable()->comment('Options keyed starting with 1:  { 1 => "Option 1" }');
            $table->jsonb('poll_responses')->nullable()->comment('Responses keyed with user_id => option_id:  { 455 => 1 }');

            $table->boolean('allow_comments')->nullable()->default(false);

            $table->boolean('is_shared')->default(false);
            $table->boolean('is_pinned')->default(false)->index();
            $table->boolean('is_hidden')->default(false)->index();
//            $table->boolean('is_public')->default(false)->index();
            $table->boolean('is_flagged')->default(false);

            // Types
            $table->boolean('is_poll')->default(false);
            $table->boolean('is_link')->default(false);
            $table->boolean('is_rsvp')->default(false);
        });
    }


    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_group_posts');
    }

}
