<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChildCheckinsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('child_checkins', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('archived_at')->nullable()->index();

            $table->dateTime('checkin_at')->nullable()->index();
            $table->dateTime('checkout_at')->nullable()->index();

            $table->string('uuid', 36)->nullable()->index();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('family_id')->unsigned()->nullable();
            $table->integer('child_user_id')->unsigned()->nullable()->index();

            $table->integer('checkin_by_user_id')->unsigned()->nullable();
            $table->integer('checkout_by_user_id')->unsigned()->nullable();

            $table->integer('created_by_user_id')->unsigned()->nullable();

            $table->string('pager_number', 32)->nullable();

            $table->string('first_name', 80)->nullable();
            $table->string('last_name', 80)->nullable();
            $table->text('allergies')->nullable();
            $table->text('special_needs')->nullable();

            $table->string('adult_1_name', 80)->nullable();
            $table->string('adult_1_phone', 32)->nullable();
            $table->boolean('adult_1_phone_allow_sms')->nullable();

            $table->string('adult_2_name', 80)->nullable();
            $table->string('adult_2_phone', 32)->nullable();
            $table->boolean('adult_2_phone_allow_sms')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('child_checkins');
    }
}
