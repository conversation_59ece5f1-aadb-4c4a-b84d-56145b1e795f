<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePodcastTrackDownloadsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('podcast_track_downloads', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('podcast_id')->unsigned()->index();
            $table->integer('podcast_track_id')->unsigned()->index();


            $table->jsonb('ip_info')->nullable();
            $table->ipAddress('ip_address')->nullable()->index();
            $table->string('region', 64)->nullable();
            $table->string('country', 12)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('podcast_track_downloads');
    }

}
