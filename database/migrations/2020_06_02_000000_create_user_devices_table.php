<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserDevicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_devices', function (Blueprint $table) {
            $table->increments('id');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('last_refreshed_at')->nullable()->index();
            $table->dateTime('last_message_sent_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->string('name', 128)->nullable()->default('');
            $table->string('pusher_id', 128)->nullable()->default('');
            $table->string('fcm_id', 128)->nullable()->default('');
            $table->string('device', 300)->nullable()->default('');
            $table->string('device_version', 32)->nullable();
            $table->string('device_token', 300)->nullable()->index();
            $table->string('type', 160)->nullable()->default('');
            $table->boolean('is_primary')->nullable()->default(false)->index();
            $table->boolean('receives_notifications')->default(true)->index();
            $table->string('deleted_reason', 160)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_devices');
    }
}
