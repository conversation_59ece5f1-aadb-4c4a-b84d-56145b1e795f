<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateInvolvementCategoriesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('involvement_categories', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->unsigned()->nullable()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->string('name', 500);
            $table->text('description')->nullable();
            $table->integer('sort_id')->nullable()->unsigned()->index();
            $table->boolean('men_only')->nullable()->default(true);
            $table->boolean('women_only')->nullable()->default(true);
            $table->boolean('baptized_only')->nullable()->default(false);
            $table->boolean('approved_to_teach_only')->nullable()->default(false);
            $table->boolean('completed_background_check_only')->nullable()->default(false);
            $table->boolean('is_hidden')->nullable()->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('involvement_categories');
    }

}
