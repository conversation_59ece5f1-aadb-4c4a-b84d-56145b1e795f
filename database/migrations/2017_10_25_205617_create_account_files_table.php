<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountFilesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_files', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->dateTime('starts_at')->nullable();
            $table->dateTime('expires_at')->nullable();
            $table->string('title', 500)->nullable();
            $table->string('url_title', 128)->nullable();
            $table->string('type', 32)->default('')->nullable()->index()->comment('\'audio\',\'video\',\'document\'');
            $table->string('storage_service', 128)->nullable()->default('do-spaces');
            $table->string('file_original_name', 500)->nullable();
            $table->integer('file_size')->unsigned()->default(0)->comment('in bytes');
            $table->string('data_separator', 32)->nullable()->default('--');
            $table->string('file_folder', 500)->nullable()->comment('The folder in which the file is stored on the storage service');
            $table->string('file_id', 500)->nullable();
            $table->string('file_name', 255)->comment('without extension')->nullable();
            $table->string('file_extension', 16)->nullable();
            $table->string('file_type', 64)->nullable();
            $table->string('file_sha1', 64)->nullable();

            $table->string('folder', 60)->nullable()->index(); // DELETE LATER
            $table->mediumInteger('sort_id')->unsigned()->nullable()->index();

            $table->dateTime('is_public')->nullable()->index();
            $table->dateTime('is_hidden')->nullable()->index();

            // 2024
            $table->integer('account_file_parent_id')->unsigned()->nullable()->index()->comment('Indicates this is a subfolder or a file in a subfolder.');
            $table->dateTime('is_women_only')->nullable()->default(null)->index();
            $table->dateTime('is_men_only')->nullable()->default(null)->index();
            $table->boolean('is_folder')->nullable()->default(false)->index();
            $table->string('folder_color', 16)->nullable();
            $table->string('folder_icon', 32)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_files');
    }

}
