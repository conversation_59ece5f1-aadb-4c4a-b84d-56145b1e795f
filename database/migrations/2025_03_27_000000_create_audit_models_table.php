<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_models', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->dateTime('created_at')->index();
            $table->dateTime('updated_at')->nullable();
            $table->string('name')->nullable();

            $table->index('name');
        });
    }
};