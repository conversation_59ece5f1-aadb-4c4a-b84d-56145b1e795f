<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserToFinanceBucketTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_to_finance_bucket', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->integer('finance_bucket_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();

            $table->boolean('can_view_details')->nullable()->default(false)->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_to_finance_bucket');
    }

}
