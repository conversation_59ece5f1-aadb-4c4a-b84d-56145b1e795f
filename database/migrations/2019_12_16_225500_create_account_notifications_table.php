<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountNotificationsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_notifications', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->integer('account_id')->nullable()->unsigned()->index();
            $table->integer('message_type_id')->nullable()->unsigned();
            $table->string('source', 32)->nullable();
            $table->integer('user_id')->nullable()->unsigned()->index();
            $table->string('destination', 64)->nullable();
            $table->decimal('charge', 10, 6)->nullable()->comment('CENTS');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_notifications');
    }

}
