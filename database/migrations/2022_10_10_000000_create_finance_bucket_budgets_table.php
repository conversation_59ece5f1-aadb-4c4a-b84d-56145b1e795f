<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFinanceBucketBudgetsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_bucket_budgets', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();

            $table->integer('account_finance_bucket_id')->unsigned()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->dateTime('start_at')->nullable()->index();
            $table->dateTime('end_at')->nullable()->index();
            $table->smallInteger('year')->nullable()->index()->comment('Year this budget is for.');

            $table->string('name', 200)->nullable();
            $table->text('description')->nullable();

            $table->integer('yearly_budget')->nullable()->comment('cents');
            $table->integer('monthly_budget')->nullable()->comment('cents');
            $table->integer('weekly_budget')->nullable()->comment('cents');

            $table->integer('requested_yearly_budget')->nullable()->comment('cents');
            $table->integer('requested_monthly_budget')->nullable()->comment('cents');
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finance_bucket_budgets');
    }

}
