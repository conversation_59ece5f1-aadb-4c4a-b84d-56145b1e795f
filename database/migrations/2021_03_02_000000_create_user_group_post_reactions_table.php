<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserGroupPostReactionsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_group_post_reactions', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('user_group_post_id')->nullable()->unsigned()->index()->comment('Only set if this is a like on a POST.');
            $table->integer('user_group_post_comment_id')->nullable()->unsigned()->index()->comment('Only set if this is a like on a COMMENT.');
            $table->integer('user_id')->unsigned();
            $table->dateTime('created_at')->nullable();

            $table->boolean('is_like')->default(false);
            $table->boolean('is_love')->default(false);
            $table->boolean('is_care')->default(false);
            $table->boolean('is_pray')->default(false);
            $table->boolean('is_laugh')->default(false);

            $table->boolean('is_flag')->unsigned()->nullable()->default(false)->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_group_post_reactions');
    }

}
