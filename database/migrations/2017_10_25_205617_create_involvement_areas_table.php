<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateInvolvementAreasTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('involvement_areas', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->integer('involvement_category_id')->unsigned()->index();
            $table->string('name', 500);
            $table->text('description')->nullable();
            $table->integer('sort_id')->nullable()->index();
            $table->boolean('men_only')->nullable()->default(true);
            $table->boolean('women_only')->nullable()->default(true);
            $table->boolean('baptized_only')->nullable()->default(false);
            $table->boolean('approved_to_teach_only')->nullable()->default(false);
            $table->boolean('completed_background_check_only')->nullable()->default(false);
            $table->boolean('is_hidden')->nullable()->default(false);
            $table->boolean('auto_approve_for_assignments')->nullable()->default(true);
            $table->boolean('auto_show_in_volunteer_list')->nullable()->default(true);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('involvement_areas');
    }

}
