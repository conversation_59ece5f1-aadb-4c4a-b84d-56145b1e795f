<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessageHandlerTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_handlers', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_group_id')->unsigned()->index();
            $table->integer('message_type_id')->unsigned()->index();
            $table->integer('message_provider_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->dateTime('last_received_at')->nullable();
            $table->string('name', 500)->default('');
            $table->string('address', 500)->index();
            $table->string('reply_to', 500)->nullable();
            $table->boolean('is_active')->nullable()->fault(false)->index();
            $table->string('voice_call_voice', 12)->nullable();
            $table->decimal('cost', 10, 6)->unsigned()->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('message_handlers');
    }

}
