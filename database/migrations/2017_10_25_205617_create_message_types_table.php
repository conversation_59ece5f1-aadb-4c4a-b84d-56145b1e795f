<?php

use App\Messages\MessageType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessageTypesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_types', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->string('name', 500)->default('');
            $table->string('code', 500)->default('')->index();
            $table->text('description')->nullable();
            $table->boolean('is_active')->nullable()->default(true)->index();
            $table->boolean('incurs_sending_fee')->nullable()->default(false)->index();
            $table->string('account_plan_sending_fee_field', 255)->nullable()->comment('This is the field on the account_plans table that is the sending fee for this message type.');
            $table->boolean('incurs_monthly_fee')->nullable()->default(false)->index();
            $table->string('account_plan_monthly_fee_field', 255)->nullable()->comment('This is the field on the account_plans table that is the monthly fee.');
            $table->text('sending_fee_description')->nullable();
            $table->text('monthly_fee_description')->nullable();
        });

        // Seed
        foreach (MessageType::$types as $type => $name) {
            DB::table('message_types')->insert([
                'name'        => $name,
                'code'        => $type,
                'description' => 'Default type',
                'is_active'   => 1,
            ]);
        }

        /**
         * Enabling sending by email will allow messages to be sent to all members in this group using their best known email address. Duplicates will <em>not</em> be sent to those using family email addresses.
         * <br>
         * On enabling, a new phone number will be assigned to this group.
         * <br>
         * On disabling, the phone number will be deleted.
         */
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('message_types');
    }

}
