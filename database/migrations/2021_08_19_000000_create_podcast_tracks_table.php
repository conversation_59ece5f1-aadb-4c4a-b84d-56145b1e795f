<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePodcastTracksTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('podcast_tracks', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('podcast_id')->unsigned()->index();
            $table->integer('sermon_id')->unsigned()->nullable()->index();

            $table->dateTime('published_at')->nullable();

            $table->string('title', 250);
            $table->string('url_title', 250);
            $table->string('uuid', 36)->nullable();
            $table->text('summary')->nullable();
            $table->text('description')->nullable();
            $table->text('keywords')->nullable();
            $table->string('track_type', 128)->nullable();

            $table->smallInteger('episode')->nullable();
            $table->smallInteger('season')->nullable();

            $table->string('mp3_url', 500)->nullable();
            $table->string('mp3_file_name', 200)->nullable();
            $table->string('mp3_type', 200)->nullable();
            $table->string('mp3_file_size', 500)->nullable()->comment('in bytes');
            $table->string('duration', 16)->nullable()->comment('HH:MM:SS format');
            $table->mediumInteger('duration_in_seconds')->nullable();

            $table->string('author', 250)->nullable();
            $table->string('author_email', 250)->nullable();

            $table->boolean('comments')->default(0);
            $table->boolean('is_explicit')->default(0);

            $table->boolean('is_published')->default(1);
            $table->boolean('has_sync_error')->default(0);
            $table->string('sync_error_message', 500)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('podcast_tracks');
    }

}
