<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessageHistoryTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_history', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('user_email_id')->unsigned()->nullable()->index();
            $table->integer('user_phone_id')->unsigned()->nullable()->index();
            $table->integer('message_id')->unsigned()->nullable()->index();
            $table->integer('message_status_id')->unsigned()->nullable()->index();
            $table->dateTime('created_at')->nullable()->index();
            $table->string('provider_batch_id', 255)->nullable();
            $table->string('provider_message_id', 255)->nullable()->index();
            $table->string('failed_reason', 255)->nullable();
            $table->decimal('charge', 10, 6)->unsigned()->comment('CENTS');
            $table->decimal('cost', 10, 6)->unsigned()->comment('CENTS');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('message_history');
    }

}
