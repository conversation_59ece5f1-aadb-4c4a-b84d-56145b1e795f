<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateBibleClassRegistrationsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bible_class_registrations', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->integer('bible_class_id')->unsigned();
            $table->integer('user_id')->nullable()->unsigned();
            $table->string('first_name', 300)->nullable();
            $table->string('last_name', 500)->nullable();
            $table->index(['bible_class_id', 'user_id'], 'bible_class_registrations_bible_class_id_index');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('bible_class_registrations');
    }

}
