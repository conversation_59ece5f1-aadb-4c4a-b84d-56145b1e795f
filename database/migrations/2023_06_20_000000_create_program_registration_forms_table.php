<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_registration_forms', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->uuid()->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('program_id')->unsigned()->index();
            $table->integer('created_by_user_id')->unsigned()->nullable();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->string('squid', 12)->nullable()->index('prf_squid_index');

            $table->boolean('is_public')->default(false);
            $table->boolean('allow_waitlist')->default(false);
            $table->boolean('indicate_waitlist')->default(false)->comment('When someone registers and we are already full, should we show they are going to a waitlist?');
            $table->boolean('require_contact_information')->default(false)->comment('If true, require the parent contact information for a registration.');

            $table->string('name', 500)->nullable();
            $table->text('description')->nullable();

            $table->dateTime('start_at')->nullable()->index()->comment('The date and time the form is available/opens for registration.');
            $table->dateTime('end_at')->nullable()->index()->comment('The date and time the form is no longer available for registration.');
            $table->mediumInteger('registration_limit')->unsigned()->nullable()->comment('The maximum number of registrations allowed for this form.');
            $table->mediumInteger('registration_limit_per_registration')->unsigned()->nullable()->comment('The maximum number of registrations allowed for a single form submission.');

            $table->mediumInteger('sort_id')->unsigned()->nullable()->index();

            $table->jsonb('required_user_fields')->nullable()->comment('The fields that are required for the user to provide. This is a JSON array of the field names from the program_user_fields array.');
            $table->jsonb('optional_user_fields')->nullable()->comment('The fields that are optional for the user to provide. This is a JSON array of the field names from the program_user_fields array.');

            $table->jsonb('required_contact_fields')->nullable()->comment('The fields that are required for the contact to provide. This is a JSON array of the field names from the program_user_fields array.');
            $table->jsonb('optional_contact_fields')->nullable()->comment('The fields that are optional for the contact to provide. This is a JSON array of the field names from the program_user_fields array.');

            $table->jsonb('selectable_program_group_ids')->nullable()->comment('If set, a JSON array of program group IDs that the user can select to be added to when they submit their registration.');
            $table->jsonb('auto_assign_program_group_ids')->nullable()->comment('If set, a JSON array of program group IDs to automatically assign the user to when they submit their registration.');
            $table->jsonb('auto_assign_waitlist_program_group_ids')->nullable()->comment('If set, a JSON array of program group IDs to automatically assign the user to when they submit their registration and get WAITLISTED.');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_registration_forms');
    }
};
