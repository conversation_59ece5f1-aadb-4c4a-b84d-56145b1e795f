<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWebhookEventsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('webhook_events', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->integer('account_id')->nullable()->unsigned()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->string('service', 32)->nullable();

            $table->string('source_event_id')->nullable();
            $table->string('source_event_type', 64)->nullable();
            $table->string('source_status', 128)->nullable();
            $table->jsonb('payload')->nullable();

            $table->boolean('is_failure')->nullable()->default(false);
            $table->boolean('is_warning')->nullable()->default(false);
            $table->boolean('is_success')->nullable()->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('webhook_events');
    }

}
