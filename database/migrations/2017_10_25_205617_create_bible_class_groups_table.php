<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateBibleClassGroupsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bible_class_groups', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->nullable()->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->string('name', 500)->nullable();
            $table->text('description')->nullable();
            $table->integer('weeks')->nullable();
            $table->date('start_date')->index();
            $table->date('end_date')->nullable()->index();
            $table->boolean('is_signup_active')->default(false)->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('bible_class_groups');
    }

}
