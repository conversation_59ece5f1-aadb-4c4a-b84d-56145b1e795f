<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateCalendarEventsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('calendar_events', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->nullable()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->integer('created_by_user_id')->unsigned()->nullable();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('calendar_id')->unsigned()->index();
            $table->integer('user_group_id')->unsigned()->nullable()->index();

            $table->string('title')->default('');
            $table->string('url_title', 128)->nullable();
            $table->string('location', 300)->nullable();
            $table->string('overview', 500)->nullable();
            $table->text('description')->nullable();

            $table->boolean('is_hidden')->default(false);
            $table->boolean('is_private')->default(false);
            $table->boolean('is_group_only')->default(false);
            $table->boolean('is_public')->default(false);

            $table->string('timezone', 32)->nullable();

            $table->dateTime('start_at')->nullable()->index();
            $table->dateTime('end_at')->nullable()->index();
            $table->date('start_at_date')->nullable()->index();
            $table->date('end_at_date')->nullable()->index();
            $table->time('start_at_time')->nullable()->index();
            $table->time('end_at_time')->nullable()->index();

            $table->time('duration')->nullable();

            $table->jsonb('days_of_week')->nullable()->comment('0,1,2,3,4,5,6 -- MO, TU, WE, TH, FR, SA, SU');

            $table->boolean('is_all_day')->default(false);
            $table->boolean('is_recurring')->default(false);

            $table->boolean('enable_responses')->default(false);
            $table->boolean('enable_payments')->default(false);
            $table->boolean('remove_event_after_end_date')->default(false);

            $table->string('rrule', 500)->nullable()->default('');

            $table->dateTime('recur_end_at')->nullable();
            $table->string('recur_frequency', 16)->nullable()->comment('');
            $table->integer('recur_interval')->unsigned()->nullable();
            $table->integer('recur_count')->unsigned()->nullable();
            $table->string('recur_week_start_day', 2)->comment('MO, TU, WE, TH, FR, SA, SU')->nullable();
            $table->string('recur_by_day', 32)->comment('MO, TU, WE, TH, FR, SA, SU')->nullable();
            $table->smallInteger('recur_by_month')->unsigned()->comment('')->nullable();
            $table->smallInteger('recur_by_week_number')->unsigned()->nullable();
            $table->mediumInteger('recur_by_day_of_year')->unsigned()->nullable();
            $table->smallInteger('recur_by_day_of_month')->unsigned()->nullable();
            $table->smallInteger('recur_by_set_position')->unsigned()->nullable();
            $table->smallInteger('recur_by_hour')->unsigned()->nullable();
            $table->smallInteger('recur_by_minute')->unsigned()->nullable();
            $table->smallInteger('recur_by_second')->unsigned()->nullable();
            // $table->string('recur_by_set_position', 255)->nullable();

            // Added in 2025-04
            $table->date('local_start_at_date')->nullable()->comment('What date the user expects in their local date.');
            $table->date('local_end_at_date')->nullable()->comment('What date the user expects in their local date.');
            $table->time('local_start_at_time')->nullable()->comment('What time the user expects in their local time.');
            $table->time('local_end_at_time')->nullable()->comment('What time the user expects in their local time.');

            $table->integer('updated_by_user_id')->unsigned()->nullable();
            $table->integer('owner_user_id')->unsigned()->nullable();
        });
        // RRule fields taken from: https://github.com/rlanvin/php-rrule/wiki/RRule
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('calendar_events');
    }

}
