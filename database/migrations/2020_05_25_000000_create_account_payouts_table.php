<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAccountPayoutsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_payouts', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->unsigned()->nullable();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('posted_at')->nullable();

            $table->dateTime('cleared_at')->nullable();
            $table->dateTime('deposited_at')->nullable();

            $table->string('provider', 16)->nullable()->index();
            $table->string('provider_payout_id', 64)->nullable();

            $table->integer('amount')->nullable()->comment('cents');

            $table->string('name', 200)->nullable();
            $table->text('description')->nullable();

            $table->boolean('is_hidden')->nullable()->default(false)->index();
            $table->integer('sort_id')->nullable()->unsigned()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('account_payouts');
    }

}
