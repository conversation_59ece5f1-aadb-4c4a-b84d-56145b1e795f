<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateInvolvementToUserTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('involvement_to_user', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('involvement_category_id')->unsigned()->nullable()->default(null)->index();
            $table->integer('involvement_area_id')->nullable()->unsigned()->index();
            $table->integer('involvement_subarea_id')->nullable()->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->boolean('is_approved_for_assignments')->nullable()->default(true);
            $table->boolean('show_in_volunteer_list')->nullable()->default(true);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('involvement_to_user');
    }

}
