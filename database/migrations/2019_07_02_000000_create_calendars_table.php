<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateCalendarsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('calendars', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->nullable()->index();
            $table->ulid()->nullable()->index();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->unsigned()->nullable()->index();
            $table->integer('user_group_id')->unsigned()->nullable()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->string('name', 128);
            $table->string('url_name', 64)->index();
            $table->string('color', 16)->nullable()->comment('HEX value');
            $table->string('background_color', 16)->nullable()->comment('HEX value');
            $table->string('border_color', 16)->nullable()->comment('HEX value');
            $table->string('text_color', 16)->nullable()->comment('HEX value');

            $table->boolean('is_hidden')->nullable()->default(false);
            $table->boolean('is_private')->nullable()->default(false);
            $table->boolean('is_group_only')->nullable()->default(false);
            $table->boolean('is_public')->nullable()->default(false);

            $table->boolean('auto_show')->nullable()->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('calendars');
    }

}
