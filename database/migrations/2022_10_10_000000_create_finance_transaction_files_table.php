<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFinanceTransactionFilesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_transaction_files', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();

            $table->integer('finance_transaction_id')->unsigned()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->string('title', 200)->nullable();
            $table->text('description')->nullable();

            $table->string('type', 32)->default('')->index()->comment('audio,video,file');
            $table->string('storage_service', 32)->nullable()->default('linode');

            $table->string('file_original_name', 500)->nullable();
            $table->integer('file_size')->unsigned()->default(0)->comment('in bytes');
            $table->string('data_separator', 32)->nullable()->default('--');

            $table->string('file_folder', 500)->nullable();
            $table->string('file_id', 500)->nullable();
            $table->string('file_name', 500)->comment('without extension')->nullable();
            $table->string('file_extension', 16)->nullable();
            $table->string('file_type', 64)->nullable();
            $table->string('file_sha1', 64)->nullable();

            $table->mediumInteger('sort_id')->unsigned()->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finance_transaction_files');
    }

}
