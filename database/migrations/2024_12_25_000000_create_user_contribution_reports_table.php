<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_contribution_reports', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->uuid()->nullable()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('created_by_user_id')->index();

            $table->integer('posted_by_user_id')->unsigned();
            $table->dateTime('posted_at')->nullable()->comment('When the report was posted for user access.');
            $table->dateTime('email_to_user_at')->nullable()->comment('When the report was emailed to the user.');

            $table->smallInteger('year')->nullable()->comment('The year of the report, such as 2024.');
            $table->integer('amount_total')->nullable()->default(0)->comment('cents');

            $table->dateTime('modified_at')->nullable()->comment('When the report was last modified.');
            $table->integer('modified_by_user_id')->unsigned()->nullable()->comment('User ID of the one who changed this report last.');

            $table->text('notes')->nullable();
            $table->text('statement_message')->nullable();
        });
    }
};
