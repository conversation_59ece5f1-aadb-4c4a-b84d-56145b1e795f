<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFinanceReimbursementRequestItemsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_reimbursement_request_items', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index('frri_account_id');

            $table->dateTime('created_at')->nullable()->index('frri_created_at');
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index('frri_deleted_at');

            $table->integer('user_id')->unsigned()->nullable()->index('frri_user_id');
            $table->integer('finance_reimbursement_request_id')->unsigned()->default(null)->index('frri_finance_reimbursement_request_id');
            $table->integer('account_finance_bucket_id')->unsigned()->index('frri_account_finance_bucket_id');
            $table->integer('finance_transaction_id')->unsigned()->index('frri_finance_transaction_id');

            $table->dateTime('spent_at')->nullable()->comment('Date this expense occurred.');

            $table->string('title', 200)->nullable()->comment('Title / Item');
            $table->text('notes')->nullable();

            $table->integer('amount')->nullable()->comment('cents');

            $table->string('currency', 6)->nullable()->default('USD');

            $table->boolean('is_approved')->nullable()->default(false)->index('frri_is_approved');
            $table->boolean('is_declined')->nullable()->default(false)->index('frri_is_declined');
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finance_reimbursement_request_items');
    }

}
