<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserPhotosTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_photos', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->integer('user_id')->unsigned()->default(null)->index();
            $table->integer('created_by')->unsigned()->nullable();
            $table->integer('family_id')->unsigned()->nullable()->default(null)->index();
            $table->text('caption')->nullable();

            $table->integer('width')->nullable()->default(0);
            $table->integer('height')->nullable()->default(0);
            $table->mediumInteger('dpi')->nullable();

            $table->string('storage_service', 128)->nullable()->default('do-spaces');

            $table->string('file_original_name', 500)->nullable();
            $table->integer('file_size')->unsigned()->default(0)->comment('in bytes');
            $table->string('data_separator', 32)->nullable()->default('--');
            $table->string('file_folder', 500)->nullable();
            $table->string('file_id', 500)->nullable();
            $table->string('file_name', 255)->comment('without extension');
            $table->string('file_extension', 16);
            $table->string('file_type', 64)->nullable();
            $table->string('file_sha1', 64)->nullable();

            $table->boolean('has_original')->default(true);
            $table->boolean('has_1024')->default(true);
            $table->boolean('has_512')->default(true);
            $table->boolean('has_256')->default(false);

            $table->mediumInteger('sort_id')->nullable()->index();

            $table->boolean('is_primary')->default(false);
            $table->boolean('is_avatar')->default(false)->nullable();
            $table->boolean('is_hidden')->default(false);

            $table->boolean('needs_approval')->default(false);
            $table->integer('approved_by')->unsigned()->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_photos');
    }

}
