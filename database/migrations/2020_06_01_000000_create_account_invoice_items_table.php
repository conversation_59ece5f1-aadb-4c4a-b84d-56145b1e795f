<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountInvoiceItemsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_invoice_items', function (Blueprint $table) {
            $table->increments('id');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_invoice_id')->nullable()->unsigned()->index();
            $table->string('title', 300)->nullable();
            $table->text('description')->nullable();
            $table->string('type', 64)->nullable()->index();
            $table->decimal('quantity', 12, 6)->nullable();
            $table->decimal('amount', 12, 6)->nullable()->comment('CENTS');
            $table->integer('amount_subtotal')->nullable()->comment('CENTS');
            $table->integer('amount_tax')->nullable()->comment('CENTS');
            $table->integer('amount_total')->nullable()->comment('CENTS');
            $table->boolean('is_taxable')->default(true)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_invoice_items');
    }

}
