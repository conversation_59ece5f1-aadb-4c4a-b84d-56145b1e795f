<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePodcastsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('podcasts', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->nullable()->unsigned();
            $table->integer('sermon_tag_id')->nullable()->unsigned()->comment('For automatic podcast creation.');

            $table->dateTime('published_at')->nullable();

            $table->string('title', 250);
            $table->string('url_title', 64)->index();
            $table->string('subtitle', 500)->nullable();
            $table->text('summary')->nullable();
            $table->text('description')->nullable();

            $table->string('category', 120);
            $table->string('subcategory', 120)->nullable();
            $table->string('category_2', 120)->nullable();
            $table->string('category_3', 120)->nullable();
            $table->string('category_4', 120)->nullable();

            $table->string('image_url', 500)->nullable();
            $table->string('image_title', 200)->nullable();
            $table->string('image_link', 500)->nullable()->comment('Link to the Podcast, basically.');

            $table->string('author', 250)->nullable();
            $table->string('author_email', 250)->nullable();
            $table->string('copyright', 250)->nullable();

            $table->string('location', 200)->nullable()->comment('i.e. Katy, Texas');
            $table->string('country_of_origin', 250)->nullable();
            $table->string('timezone', 32)->nullable();

            $table->string('language', 16)->nullable()->default('en-us')->comment('en-us');
            $table->string('episode_order', 10)->nullable()->comment('asc = Older first, desc = Newer first');
            $table->string('rating', 32)->nullable();
            $table->string('frequency', 32)->nullable();
            $table->string('type', 32)->nullable()->default('episodic');

            $table->boolean('comments')->default(0);
            $table->boolean('is_explicit')->default(0);
            $table->boolean('is_active')->default(1);
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('podcasts');
    }

}
