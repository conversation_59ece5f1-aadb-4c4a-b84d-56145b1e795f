<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessageProvidersTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_providers', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->nullable()->unsigned()->index();
            $table->integer('message_type_id')->nullable()->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->string('name', 500);
            $table->string('short_name', 500)->nullable();
            $table->string('provider_code', 500)->nullable()->index();
            $table->jsonb('provider_info')->nullable();
            $table->string('api_key', 255)->nullable();
            $table->string('api_secret', 255)->nullable();
            $table->string('default_address', 255)->nullable();
            $table->text('description');
            $table->boolean('is_active')->nullable()->index();
            $table->boolean('is_global_default')->nullable()->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('message_providers');
    }

}
