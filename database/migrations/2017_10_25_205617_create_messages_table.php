<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessagesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->unsigned()->nullable()->index();
            $table->integer('created_by_user_id')->unsigned()->nullable()->index();
            $table->integer('message_sender_id')->unsigned()->nullable()->index();
            $table->integer('message_handler_id')->unsigned()->nullable()->index();
            $table->integer('message_type_id')->nullable()->index();
            $table->mediumInteger('message_provider_id')->unsigned()->nullable()->index();
            $table->string('provider_transaction_id', 255)->nullable()->index();
            $table->integer('user_group_id')->unsigned()->nullable()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->dateTime('original_sent_at')->nullable();
            $table->string('from_address', 500)->nullable();
            $table->string('to_address', 500)->nullable();
            $table->string('subject', 500)->nullable();
            $table->mediumText('content')->nullable();
            $table->mediumText('html_content')->nullable();
            $table->integer('messages_sent')->unsigned()->nullable();
            $table->string('status', 255)->nullable()->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('messages');
    }

}
