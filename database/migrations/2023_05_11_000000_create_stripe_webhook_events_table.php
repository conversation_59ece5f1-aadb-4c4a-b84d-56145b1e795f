<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStripeWebhookEventsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stripe_webhook_events', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->jsonb('event')->nullable();
            $table->string('event_type', 64)->nullable();
            $table->string('stripe_event_id')->nullable();
            $table->string('stripe_charge_id')->nullable();

            $table->boolean('is_failure')->nullable()->default(false);
            $table->boolean('is_warning')->nullable()->default(false);
            $table->boolean('is_success')->nullable()->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stripe_webhook_events');
    }

}
