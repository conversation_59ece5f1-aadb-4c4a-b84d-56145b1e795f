<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSermonTagsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sermon_tags', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->nullable()->unsigned()->index();
            $table->string('name', 80);
            $table->string('slug', 64);
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(true)->index();
            $table->boolean('is_hidden')->default(false)->index();
            $table->boolean('is_podcast')->default(false)->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('sermon_tags');
    }

}
