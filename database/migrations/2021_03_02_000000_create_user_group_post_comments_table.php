<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserGroupPostCommentsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_group_post_comments', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_group_id')->unsigned()->index();
            $table->integer('user_group_post_id')->unsigned()->index();
            $table->integer('user_group_post_comment_id')->unsigned()->nullable()->index()->comment('Reference to a parent comment.');
            $table->integer('creator_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();
            $table->dateTime('last_edited_at')->nullable();
            $table->text('content')->nullable();

            $table->boolean('is_pinned')->default(false);
            $table->boolean('is_hidden')->default(false);
            $table->boolean('is_flagged')->default(false);
        });
    }


    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_group_post_comments');
    }

}
