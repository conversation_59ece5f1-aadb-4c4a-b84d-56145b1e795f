<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRoomsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rooms', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->nullable()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->mediumInteger('account_id')->unsigned()->index();
            $table->integer('created_by_user_id')->unsigned()->nullable();
            $table->mediumInteger('building_id')->nullable()->unsigned()->index();

            $table->string('name', 200)->nullable();
            $table->string('short_name', 100)->nullable();

            $table->string('type', 100)->nullable()->comment('conference,classroom,auditorium,office,lab');
            $table->string('location', 250)->nullable()->comment('i.e. Floor 2, Room 201');

            $table->smallInteger('capacity')->unsigned()->nullable()->comment('How many people this room can hold.');

            $table->text('description')->nullable();
            $table->text('instructions')->nullable();
            $table->text('cancellation_policy')->nullable();

            $table->json('features')->nullable()->comment('projector,whiteboard,wifi');

            $table->smallInteger('minimum_booking_time_in_minutes')->nullable();
            $table->smallInteger('maximum_booking_time_in_minutes')->nullable();

            $table->smallInteger('pre_reservation_buffer_in_minutes')->nullable()->comment('The number of minutes before a reservation that can be canceled.');
            $table->smallInteger('post_reservation_buffer_in_minutes')->nullable()->comment('The number of minutes after a reservation that can be canceled.');

            $table->boolean('is_hidden')->default(false);
            $table->boolean('is_bookable')->default(false);
            $table->boolean('is_available')->default(false);
            $table->boolean('is_maintenance')->default(false);
        });
    }
}
