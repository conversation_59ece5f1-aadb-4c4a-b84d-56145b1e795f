<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRoomReservationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('room_reservations', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->nullable()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->mediumInteger('account_id')->unsigned()->index();
            $table->integer('building_id')->nullable()->unsigned()->index();
            $table->integer('room_id')->unsigned()->index();
            $table->integer('calendar_event_occurrence_id')->nullable()->unsigned()->index('r_r_calendar_event_occurrence_id');

            $table->dateTime('start_at');
            $table->dateTime('end_at');

            $table->text('special_instructions')->nullable();
            $table->text('notes')->nullable();

            $table->index(['room_id', 'start_at', 'end_at'], 'idx_room_reservations_dates');
        });
    }
}
