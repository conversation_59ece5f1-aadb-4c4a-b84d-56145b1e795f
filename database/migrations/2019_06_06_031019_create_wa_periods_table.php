<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWaPeriodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wa_periods', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->unsigned()->nullable()->index();
            $table->dateTime('created_at')->nullable();
            $table->integer('wa_group_id')->unsigned()->index();
            $table->string('name', 128)->nullable();
            $table->date('start_at')->nullable()->index();
            $table->date('end_at')->nullable();
            $table->boolean('sent_notifications')->default(false);
            $table->boolean('is_published')->default(false)->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wa_periods');
    }
}
