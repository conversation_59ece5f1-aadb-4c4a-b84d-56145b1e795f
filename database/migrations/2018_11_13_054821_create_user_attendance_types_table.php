<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserAttendanceTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_attendance_types', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->nullable()->unsigned()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->string('name', 500);
            $table->string('short_name', 500)->nullable();
            $table->integer('sort_id')->nullable()->index();

            $table->jsonb('days_of_week')->nullable()->comment('ISO-8601 numeric representation of the day of the week.');
            $table->boolean('is_am')->default(false)->nullable();
            $table->boolean('is_pm')->default(false)->nullable();
            $table->boolean('enable_member_checkin')->default(false)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_attendance_types');
    }
}
