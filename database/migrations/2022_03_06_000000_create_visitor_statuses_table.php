<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitorStatusesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visitor_statuses', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable()->index();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->integer('account_id')->unsigned()->index();

            $table->string('name', 64)->nullable();
            $table->string('url_name', 32)->nullable();

            $table->string('tw_color', 32)->nullable()->comment('TailwindCSS color value:  text-{COLOR}-500');
            $table->string('color', 16)->nullable()->comment('HEX value');
            $table->string('background_color', 16)->nullable()->comment('HEX value');
            $table->string('border_color', 16)->nullable()->comment('HEX value');

            $table->smallInteger('sort_id')->unsigned()->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('visitor_statuses');
    }
}
