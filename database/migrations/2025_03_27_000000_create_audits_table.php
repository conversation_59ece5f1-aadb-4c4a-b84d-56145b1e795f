<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audits', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->dateTime('created_at')->index();
            $table->dateTime('deleted_at')->nullable();
            $table->unsignedSmallInteger('audit_model_id')->index();
            $table->unsignedInteger('model_id')->nullable()->index();
            $table->unsignedInteger('user_id')->nullable()->index();
            $table->string('event');
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->text('notes')->nullable();

            $table->index(['audit_model_id', 'model_id']);
        });
    }
};