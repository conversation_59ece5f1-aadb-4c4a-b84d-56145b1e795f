<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('program_groups', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('program_id')->unsigned()->index();
            $table->uuid('uuid')->nullable();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->string('name', 200)->nullable();
            $table->string('url_name', 200)->nullable();
            $table->text('description')->nullable();
            $table->string('bg_color', 30)->nullable()->comments('HEX value');
            $table->string('text_color', 30)->nullable()->comments('HEX value');
            $table->string('tw_bg_color', 30)->nullable()->comments('TailwindCSS value');
            $table->string('tw_text_color', 30)->nullable()->comments('TailwindCSS value');

            $table->boolean('enable_checkins')->nullable()->default(false);
            $table->boolean('is_program_group')->nullable()->default(false)->comment('Program groups show visible on reports.');
            $table->boolean('is_registration_group')->nullable()->default(false)->comment('Registration groups indicate "registered users", such as campers for a camp session.');

            $table->mediumInteger('sort_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('program_groups');
    }
};
