<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserToUserNotesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_to_user_notes', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index();

            $table->dateTime('created_at')->nullable()->index();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->integer('created_by_user_id')->unsigned()->index();
            $table->integer('on_user_id')->unsigned()->index();
            $table->integer('on_family_id')->nullable()->unsigned()->index();

            $table->text('note')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_to_user_notes');
    }

}
