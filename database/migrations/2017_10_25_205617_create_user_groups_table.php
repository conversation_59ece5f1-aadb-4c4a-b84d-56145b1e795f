<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserGroupsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_groups', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->uuid('uuid')->nullable()->index();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('account_location_id')->unsigned()->nullable()->index();
            $table->integer('creator_id')->unsigned();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index();

            $table->string('name', 500)->nullable();
            $table->string('url_name', 500)->nullable();

            $table->text('description')->nullable();

            $table->boolean('indicates_membership')->unsigned()->nullable()->default(false)->index();
            $table->boolean('indicates_visitor')->unsigned()->nullable()->default(false)->index();

            $table->boolean('is_hidden')->unsigned()->nullable()->default(false)->index()->comment('If members can see this group when they are not a member.');
            $table->boolean('is_suspended')->unsigned()->nullable()->default(false)->index();

            $table->boolean('allow_individual_to_toggle')->unsigned()->nullable()->default(false)->index()->comment('Allow users to join/leave on their own.');
            $table->boolean('allow_all_members_to_send')->unsigned()->nullable()->default(false)->comment('Reference to email.');
            $table->boolean('allow_lightpost_users_to_email')->unsigned()->nullable()->default(false);
            $table->boolean('allow_internet_to_email')->unsigned()->nullable()->default(false);

            $table->boolean('enable_posts')->default(false);
            $table->boolean('allow_members_to_post')->default(false);
            $table->boolean('allow_members_to_comment')->default(false);
            $table->boolean('allow_members_to_view_membership')->default(false);

            $table->boolean('is_default_visitor_group')->default(false)->index();
            $table->boolean('is_default_member_group')->default(false)->index();

            $table->mediumInteger('sort_id')->unsigned()->nullable();

            // Added 2025-03
            $table->boolean('allow_members_to_create_calendar_events')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_groups');
    }

}
