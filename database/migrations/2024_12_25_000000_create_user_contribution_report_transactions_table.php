<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_contribution_report_transactions', function (Blueprint $table) {
            $table->increments('id')->unsigned();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->integer('account_id')->unsigned()->index();
            $table->integer('user_contribution_report_id')->unsigned()->index('ucr_id_index');
            $table->integer('user_id')->unsigned()->index();
            $table->integer('finance_transaction_id')->unsigned()->index('ucr_ft_id_index');

            $table->integer('amount')->nullable()->default(0)->comment('cents');

            $table->dateTime('posted_at')->nullable();

            $table->dateTime('modified_at')->nullable()->comment('When the report was last modified');
            $table->integer('modified_from_user_contribution_report_id')->unsigned()->comment('If this report was modified from another report, this is the ID of the newer item.');

            $table->text('notes')->nullable();
        });
    }
};
