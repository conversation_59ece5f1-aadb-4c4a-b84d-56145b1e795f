<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAccountDomainsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_domains', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->integer('account_id')->unsigned()->index();

            $table->string('domain', 100)->nullable()->index();
            $table->string('forward_url', 200)->nullable();

            $table->tinyInteger('is_active')->unsigned()->nullable()->default('0');
            $table->tinyInteger('is_forwarder')->unsigned()->nullable()->default('0')->comment('If we should return a redirect.');
            $table->tinyInteger('is_managed')->unsigned()->nullable()->default('0')->comment('If we register and renew this domain.');

            $table->dateTime('last_managed_billed_at')->nullable();
            $table->mediumInteger('managed_monthly_cost')->nullable()->default('0')->comment('In cents');
            $table->dateTime('last_renewal_billed_at')->nullable();
            $table->mediumInteger('renewal_yearly_cost')->nullable()->default('0')->comment('In cents');
        });
    }
}
