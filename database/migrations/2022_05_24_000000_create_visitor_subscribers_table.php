<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitorSubscribersTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visitor_subscribers', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('visitor_id')->unsigned()->index();
            $table->integer('user_id')->unsigned()->index();

            // ATTRIBUTES
            $table->boolean('receive_email_updates')->unsigned()->nullable()->default(true);
            $table->boolean('receive_mobile_notification_updates')->unsigned()->nullable()->default(true);
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('visitor_subscribers');
    }

}
