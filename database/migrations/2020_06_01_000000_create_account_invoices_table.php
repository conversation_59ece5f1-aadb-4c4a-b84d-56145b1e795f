<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountInvoicesTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_invoices', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('account_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->date('posted_at')->nullable();
            $table->date('due_at')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->dateTime('paid_at')->nullable();
            $table->integer('amount_credits')->nullable()->default(0)->comment('CENTS');
            $table->integer('amount_charges')->nullable()->default(0)->comment('CENTS');
            $table->integer('amount_discounts')->nullable()->default(0)->comment('CENTS');
            $table->integer('amount_subtotal')->nullable()->default(0)->comment('CENTS');
            $table->integer('amount_tax')->nullable()->default(0)->comment('CENTS');
            $table->integer('amount_total')->nullable()->default(0)->comment('CENTS');
            $table->string('payment_reference_id', 64)->nullable();
            $table->text('description')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_invoices');
    }

}
