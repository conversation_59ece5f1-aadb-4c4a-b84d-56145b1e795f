<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRoomAvailabilityRulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('room_availability_rules', function (Blueprint $table) {
            $table->id('rule_id');
            $table->uuid('uuid')->nullable()->index();

            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();

            $table->mediumInteger('account_id')->unsigned()->index();
            $table->integer('created_by_user_id')->unsigned()->nullable();

            $table->integer('room_id')->unsigned()->index();

            $table->tinyInteger('day_of_week')->unsigned()->nullable()->index()->comment('ISO-8601 numeric representation of the day of the week. MON=1, SUN=7');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->boolean('is_recurring')->default(true);
            $table->date('valid_from')->nullable();
            $table->date('valid_until')->nullable();

            $table->index('room_id', 'idx_room_availability_room_id');
            $table->index('day_of_week', 'idx_room_availability_day');
        });
    }
}
