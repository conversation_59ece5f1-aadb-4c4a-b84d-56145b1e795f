<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserEmailsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_emails', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('user_id')->unsigned()->index();
            $table->integer('family_id')->unsigned()->nullable()->index();
            $table->string('type', 255)->default('');
            $table->string('email', 500);
            $table->boolean('is_family')->default(false)->index();
            $table->boolean('is_primary')->default(false)->index();
            $table->boolean('is_hidden')->default(false);
            $table->boolean('receives_group_emails')->default(false)->index();
            $table->boolean('allow_messages')->default(false)->index();
            $table->smallInteger('sort_id')->nullable()->index();
            $table->dateTime('reported_spam_complaint')->nullable()->index();
            $table->string('spam_complaint_subject_line', 255)->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('user_emails');
    }

}
