<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAccountLocationsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('account_locations', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->string('name', 500)->default('');
            $table->string('short_name', 128)->nullable();
            $table->string('url_name', 128)->nullable();
            $table->string('address1', 500)->nullable()->default('');
            $table->string('address2', 500)->nullable()->default('');
            $table->string('address3', 500)->nullable()->default('');
            $table->string('city', 500)->nullable()->default('');
            $table->string('state', 500)->nullable()->default('');
            $table->string('postal_code', 100)->nullable()->default('');
            $table->string('country_code', 8)->nullable()->default('');
            $table->string('phone_work', 32)->nullable()->default('');
            $table->string('phone_fax', 32)->nullable()->default('');
            $table->string('phone_other', 32)->nullable()->default('');
            $table->string('timezone', 32)->nullable()->default('');
            $table->string('email', 500)->nullable();
            $table->string('status', 64)->default('')->index();
            $table->boolean('is_active')->index();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('account_locations');
    }

}
