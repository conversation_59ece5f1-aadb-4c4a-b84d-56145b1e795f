<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePodcastDestinationsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('podcast_destinations', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->integer('podcast_id')->unsigned()->index();

            $table->string('name', 250);
            $table->string('url_name', 64)->nullable();
            $table->string('key', 32)->nullable();
            $table->text('direct_link')->nullable();

            $table->string('status', 40)->nullable();
            $table->boolean('is_active')->default(0);
            $table->boolean('is_pending')->default(0);
            $table->boolean('is_error')->default(0)->index();

            $table->text('error_message')->nullable();
            $table->text('message_to_client')->nullable();
            $table->text('notes')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('podcast_destinations');
    }

}
