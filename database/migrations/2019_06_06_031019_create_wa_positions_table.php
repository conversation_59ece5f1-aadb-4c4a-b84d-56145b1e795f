<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWaPositionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wa_positions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('account_id')->unsigned()->index();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->integer('wa_group_id')->unsigned()->index();
            $table->string('name', 128);
            $table->tinyInteger('day_of_week')->nullable()->index()->comment('ISO-8601 numeric representation of the day of the week');
            $table->boolean('span_whole_period')->default(false);
            $table->boolean('restrict_user_selections_by_involvement_selections')->default(false);
            $table->tinyInteger('number_of_users')->unsigned()->default(1);
            $table->string('notes', 1000)->nullable();
            $table->integer('involvement_category_id')->nullable()->unsigned()->index();
            $table->integer('involvement_area_id')->nullable()->unsigned()->index();
            $table->integer('involvement_subarea_id')->nullable()->unsigned()->index();
            $table->mediumInteger('sort_id')->nullable()->unsigned()->index();
            $table->tinyInteger('is_blocking')->unsigned()->default(0);
            $table->tinyInteger('is_temporary')->unsigned()->default(0);
            $table->tinyInteger('can_lead_only')->nullable()->unsigned()->default(0);
            $table->tinyInteger('enable_autofill')->nullable()->unsigned()->default(0);

            // WA Reminders per position
            $table->tinyInteger('reminder_1_enabled')->unsigned()->default(0)->index();
            $table->tinyInteger('reminder_1_days_before')->unsigned()->default(1);
            $table->tinyInteger('reminder_1_hour_to_send')->unsigned()->default(12);
            $table->tinyInteger('reminder_1_via_email')->unsigned()->default(0);
            $table->tinyInteger('reminder_1_via_mobile_notification')->unsigned()->default(0);

            $table->tinyInteger('reminder_2_enabled')->unsigned()->default(0)->index();
            $table->tinyInteger('reminder_2_days_before')->unsigned()->default(1);
            $table->tinyInteger('reminder_2_hour_to_send')->unsigned()->default(12);
            $table->tinyInteger('reminder_2_via_email')->unsigned()->default(0);
            $table->tinyInteger('reminder_2_via_mobile_notification')->unsigned()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wa_positions');
    }
}
