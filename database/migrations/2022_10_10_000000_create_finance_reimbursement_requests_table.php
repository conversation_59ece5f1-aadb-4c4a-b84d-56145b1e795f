<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFinanceReimbursementRequestsTable extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('finance_reimbursement_requests', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('account_id')->unsigned()->index('frr_account_id');

            $table->integer('user_id')->unsigned()->index('frr_user_id');

            $table->dateTime('created_at')->nullable()->index('frr_created_at');
            $table->dateTime('updated_at')->nullable();
            $table->dateTime('deleted_at')->nullable()->index('frr_deleted_at');
            $table->dateTime('reimburse_by')->nullable();

            $table->string('name', 200)->nullable();
            $table->text('description')->nullable();
            $table->text('admin_notes')->nullable();

            $table->string('check_number', 24)->nullable();

            $table->boolean('is_approved')->nullable()->default(false)->index('frr_is_approved');
            $table->boolean('is_partially_approved')->nullable()->default(false)->index('frr_is_partially_approved');
            $table->boolean('is_declined')->nullable()->default(false)->index('frr_is_declined');
        });
    }

    /**
     * Reverse the migrations.
     *
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('finance_reimbursement_requests');
    }

}
