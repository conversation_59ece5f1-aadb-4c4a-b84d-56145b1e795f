{"name": "tinybitfarm/lightpost", "description": "Lightpost App", "keywords": [], "license": "Copyright Tiny Bit Farm LLC", "type": "project", "require": {"php": "^8.3", "blade-ui-kit/blade-heroicons": "^2.0", "blade-ui-kit/blade-icons": "^1.2", "brick/money": "^0.8", "chillerlan/php-qrcode": "^4.3", "dompdf/dompdf": "^2.0", "fzaninotto/faker": "^1.5", "guzzlehttp/guzzle": "^7.0.1", "hashids/hashids": "^5.0", "intervention/image": "^3.1.0", "ipinfo/ipinfo": "^2.2", "james-heinrich/getid3": "^1.9", "kreait/laravel-firebase": "^6.0", "laravel-notification-channels/expo": "^2.0", "laravel/framework": "^12.0", "laravel/horizon": "^5.0", "laravel/nightwatch": "^1.7", "laravel/passport": "^12.0", "laravel/tinker": "^2.0", "league/flysystem-aws-s3-v3": "^3.0", "livewire/flux": "^2.0", "livewire/flux-pro": "^2.0", "livewire/livewire": "^3.0", "maatwebsite/excel": "^3.1", "madcoda/php-youtube-api": "^1.2", "moneyphp/money": "^3.3", "openai-php/client": "^0.10.3", "owenvoke/blade-fontawesome": "^2.2", "paddlehq/paddle-php-sdk": "^1.1", "php-http/guzzle7-adapter": "^1.0.0", "phpoffice/phpspreadsheet": "^1.9", "plivo/plivo-php": "^4.0", "predis/predis": "^1.1", "pusher/pusher-php-server": "^7.2", "sabre/vobject": "^4.0", "sentry/sentry": "^4.0", "sentry/sentry-laravel": "^4.0", "simonhamp/the-og": "^0.6.0", "simshaun/recurr": "^5.0", "sleiman/airtable-php": "^3.0", "sparkpost/sparkpost": "^2.2", "spatie/calendar-links": "^1.8", "spatie/laravel-ignition": "^2.0", "sqids/sqids": "^0.4.1", "stripe/stripe-php": "^10.0", "symfony/http-client": "^6.1", "symfony/postmark-mailer": "^6.1", "tecnickcom/tcpdf": "^6.0", "twilio/sdk": "^6.0", "vstelmakh/url-highlight": "^3.0", "wapmorgan/mp3info": "^0.0", "wildbit/postmark-php": "^4.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.0", "beyondcode/laravel-dump-server": "^2.0", "filp/whoops": "^2.0", "mockery/mockery": "^1.4", "nunomaduro/collision": "^8.1", "nunomaduro/larastan": "^3.0", "phpunit/phpunit": "^11.0"}, "autoload": {"files": ["app/Base/Helpers/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "extra": {"laravel": {"dont-discover": []}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"], "compile": ["@php artisan migrate --force", "npm run prod"], "blade-ui": ["php artisan blade-fontawesome:sync-icons --pro"], "reseed": ["composer dump", "@php artisan migrate:reset", "@php artisan migrate", "@php artisan db:seed"], "test": ["@php artisan config:clear", "composer dump", "@php artisan migrate --env=testing", "php artisan passport:install", "vendor/bin/phpunit --colors=always ./tests"], "retest": ["@php artisan config:clear", "vendor/bin/phpunit --colors=always ./tests"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "8.3"}, "allow-plugins": {"php-http/discovery": false}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}, "0": {"type": "vcs", "url": "https://github.com/tinybitfarm/recurr"}, "1": {"type": "vcs", "url": "https://github.com/tinybitfarm/monolog-logdna"}}}