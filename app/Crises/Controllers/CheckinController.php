<?php

namespace App\Crises\Controllers;

use App\Base\Http\Controllers\Controller;
use App\WorshipAssignments\Group;
use Illuminate\Support\Facades\Auth;

class CheckinController extends Controller
{
    public function view(Group $group)
    {
        return view('admin.worship-assignments.groups.view')
            ->with('group', $group);
    }

    public function create()
    {
        return view('admin.worship-assignments.groups.create');
    }

    public function store()
    {
        $this->validate(request(), [
            'name' => 'required|string|max:128',
        ]);

        try {
            $group = Group::create([
                'account_id'          => Auth::user()->account->id,
                'account_location_id' => Auth::user()->account->location ? Auth::user()->account->location->id : null,
                'name'                => request()->input('name'),
            ]);

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.worship-assignments.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Group $group)
    {
        return view('admin.worship-assignments.groups.edit')
            ->with('group', $group);
    }

    public function save(Group $group)
    {
        $this->validate(request(), [
            'name' => 'required|string|max:128',
        ]);

        try {

            $group->name = request()->input('name');
            $group->save();

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.worship-assignments.index')
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Group $group)
    {
        // $group->delete();

        return redirect(route('admin.worship-assignments.index'))
            ->with('message.success', 'NOT Deleted. There is a lot left to delete.');
    }
}
