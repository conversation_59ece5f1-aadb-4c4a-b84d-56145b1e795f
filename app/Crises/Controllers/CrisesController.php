<?php

namespace App\Crises\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Crises\Crisis;
use App\Crises\Services\CreateCrisis;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CrisesController extends Controller
{
    public function index()
    {
        return view('admin.crises.index')->with([
            'crises' => Crisis::visibleTo(Auth::user())
                ->orderBy('created_at', 'DESC')
                ->paginate(15),
        ]);
    }

    public function view(Crisis $crisis)
    {
        return view('admin.crises.view')->with([
            'crisis' => $crisis,
        ]);
    }

    public function create()
    {
        return view('admin.crises.create');
    }

    public function store()
    {
        $this->validate(request(), [
            'name' => 'required|string|max:160',
        ]);

        try {
            (new CreateCrisis())
                ->createdBy(auth()->user())
                ->create([
                    'name' => request('name'),
                ]);
        } catch (\Exception $e) {
            Log::error('CrisesController::store - Failure in creating a Emergency Check-in', [
                'e'       => $e,
                'message' => $e->getMessage(),
            ]);

            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.crises.index'))
            ->with('message.success', 'Created successfully.');
    }

    public function edit(Crisis $crisis)
    {
        return view('admin.crises.edit')
            ->with('crisis', $crisis);
    }

    public function save(Crisis $crisis)
    {
        $this->validate(request(), [
            'name'      => 'required|string|max:160',
            'is_active' => 'nullable',
        ]);

        try {
            $crisis->update([
                'name'      => request('name'),
                'is_active' => request('is_active', 0),
            ]);
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.crises.index')
            ->with('message.success', 'Saved successfully.');
    }

    public function settings()
    {
        return view('admin.crises.settings')->with([
            'settings' => Auth::user()->account->crisisSettings,
        ]);
    }

    public function saveSettings()
    {
        try {
            $settings = Auth::user()->account->crisisSettings;

            $settings->notify_groups                   = request('notify_groups', []);
            $settings->help_notification_groups        = request('help_notification_groups', []);
            $settings->urgent_help_notification_groups = request('urgent_help_notification_groups', []);
            $settings->send_via_email                  = request('send_via_email', false);
            $settings->send_via_mobile_notification    = request('send_via_mobile_notification', false);

            $settings->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.crises.index')
            ->with('message.success', 'Settings saved successfully.');
    }
}
