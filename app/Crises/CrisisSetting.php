<?php

namespace App\Crises;

use App\Accounts\Account;
use Illuminate\Database\Eloquent\Model;

class CrisisSetting extends Model
{
    protected $table = 'crisis_settings';

    protected $attributes = [
        'send_via_email'               => false,
        'send_via_mobile_notification' => false,
    ];

    protected $casts = [
        'created_at'                      => 'datetime',
        'updated_at'                      => 'datetime',
        'notify_groups'                   => 'array',
        'help_notification_groups'        => 'array',
        'urgent_help_notification_groups' => 'array',
    ];

    protected $fillable = [
        'account_id',
        'notify_groups',
        'help_notification_groups',
        'urgent_help_notification_groups',
        'send_via_email',
        'send_via_mobile_notification',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function isMissingSettings()
    {
        if (!$this->notify_groups && !$this->help_notification_groups && !$this->urgent_help_notification_groups) {
            return true;
        }

        return false;
    }
}
