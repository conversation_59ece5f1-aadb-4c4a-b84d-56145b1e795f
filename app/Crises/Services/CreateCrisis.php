<?php

namespace App\Crises\Services;

use App\Crises\Checkin;
use App\Crises\Crisis;
use App\Jobs\Crises\ProcessNewCrisisNotifications;
use App\Users\User;
use Illuminate\Support\Facades\Log;

class CreateCrisis
{
    protected $attributes = [
        'is_active' => true,
    ];
    protected $crisis;
    protected $creator;

    public function create($attributes = []): Crisis
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        // Make all other Crises inactive.
        Crisis::visibleTo($this->creator)->update([
            'is_active' => 0,
        ]);

        // Create this Crisis
        $this->crisis = new Crisis();

        $this->crisis->fill($this->attributes);

        $this->crisis->save();

        try {
            ProcessNewCrisisNotifications::dispatch($this->crisis);
        } catch (\Exception $e) {
        }

        $this->createCheckinRecords();

        return $this->crisis;
    }

    public function createdBy($user)
    {
        $this->creator = $user;

        $this->attributes['account_id'] = $user->account_id;

        return $this;
    }

    public function createCheckinRecords()
    {
        // Get our NotifyGroups from CrisisSettings
        $group_ids = $this->crisis->account->crisisSettings->notify_groups;

        // Get the users that belong to these groups.
        $users_to_notify = User::where('account_id', $this->crisis->account_id)
            ->whereHas('groups', function ($query) use ($group_ids) {
                $query->whereIn('user_groups.id', $group_ids);
            })
            ->select([
                'id',
                'account_id',
                'family_id',
                'first_name',
                'last_name',
                'is_active',
            ])
            ->get();

        $families_to_notify = collect($users_to_notify)->unique('family_id');

        foreach ($families_to_notify as $family) {
            try {
                Checkin::create([
                    'account_id' => $this->crisis->account_id,
                    'crisis_id'  => $this->crisis->id,
                    'user_id'    => $family->id,
                    'family_id'  => $family->family_id,
                    //                'type',
                    'sent_at'    => now(),
                    //                'read_at',
                    //                'responded_at',
                    //                'resolved_at',
                    //                'resolved_by',
                    //                'make_anonymous',
                    //                'notes',
                ]);
            } catch (\Exception $e) {
                Log::error('CreateCrisis::createCheckinRecords - Could not add a family.', [
                    'e'       => $e,
                    'message' => $e->getMessage(),
                    'family'  => $family,
                ]);
            }
        }
    }
}
