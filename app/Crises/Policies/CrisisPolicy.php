<?php

namespace App\Crises\Policies;

use App\Crises\Crisis;
use App\Users\User;

class CrisisPolicy
{
    public function before($user, $ability)
    {
//        if (!$user->account->getSetting('feature.crisis_checkin')) {
//            return false;
//        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('crisis.manage');
    }

    public function create(User $user)
    {
        return $user->hasPermission('crisis.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('crisis.manage');
    }

    public function edit(User $user, Crisis $crisis)
    {
        return $user->hasPermission('crisis.manage');
    }

    public function save(User $user, Crisis $crisis)
    {
        return $user->hasPermission('crisis.manage');
    }

    public function view(User $user)
    {
        return $user->hasPermission('crisis.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('crisis.manage');
    }

    public function delete(User $user)
    {
        return $user->hasPermission('crisis.delete');
    }
}