<?php

namespace App\Crises;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;

class Checkin extends Model
{
    protected $table = 'crisis_checkins';

    protected $casts = [
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'start_at'     => 'datetime',
        'end_at'       => 'datetime',
        'read_at'      => 'datetime',
        'resolved_at'  => 'datetime',
        'responded_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'crisis_id',
        'user_id',
        'family_id',
        'type',
        'sent_at',
        'read_at',
        'responded_at',
        'resolved_at',
        'resolved_by',
        'make_anonymous',
        'notes',
    ];

    public static $types = [
        'urgent_help'   => 'Urgent Help Needed',
        'help'          => 'Help Needed',
        'ok'            => 'OK',
        'not_responded' => 'Not Responded',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('crisis_checkins.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function crisis()
    {
        return $this->hasOne(Crisis::class, 'id', 'crisis_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function family()
    {
        return $this->belongsTo(User::class, 'family_id', 'id');
    }

    public function replies()
    {
        return $this->hasMany(CheckinReply::class, 'crisis_checkin_id')
            ->orderBy('created_at', 'DESC');
    }

    public function isHelpRequest()
    {
        return $this->type == 'help';
    }

    public function isUrgentHelpRequest()
    {
        return $this->type == 'urgent_help';
    }
}
