<?php

namespace App\Crises;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;

class Crisis extends Model
{
    protected $table = 'crises';

    protected $casts = [
        'created_at'            => 'datetime',
        'updated_at'            => 'datetime',
        'start_at'              => 'datetime',
        'end_at'                => 'datetime',
        'notifications_sent_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'name',
        'description',
        'start_at',
        'end_at',
        'notifications_sent_at',
        'is_active',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('crises.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function checkins()
    {
        return $this->hasMany(Checkin::class)
            ->orderByRaw("case type when 'urgent_help' then 1 when 'help' then 2 when 'ok' then 3 when 'not_responded' then 4 end, responded_at");
    }

    public function getOkResponses()
    {
        return $this->hasMany(Checkin::class)
            ->where('type', 'ok');
    }

    public function getHelpResponses()
    {
        return $this->hasMany(Checkin::class)
            ->where('type', 'help');
    }

    public function getUrgentHelpResponses()
    {
        return $this->hasMany(Checkin::class)
            ->where('type', 'urgent_help');
    }

    public function getNotResponded()
    {
        return $this->hasMany(Checkin::class)
            ->where(function ($query) {
                $query->whereNull('type')
                    ->orWhere('type', 'not_responded');
            });
    }

    public function scopeIsActive($query)
    {
        return $query->where('is_active', 1);
    }
}
