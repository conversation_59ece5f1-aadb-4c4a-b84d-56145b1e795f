<?php

namespace App\WorshipAssignments\Policies;

use App\Users\User;
use App\WorshipAssignments\Group;

class GroupPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.worship_assignments')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('worship-assignments.index');
    }

    public function manageAssignments(User $user)
    {
        return $user->hasPermission('worship-assignments.manage');
    }

    public function view(User $user, Group $group)
    {
        return $user->hasPermission('worship-assignments.index')
               && $user->account_id == $group->account_id;
    }

    public function create(User $user)
    {
        return $user->hasPermission('worship-assignments.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('worship-assignments.manage');
    }

    public function edit(User $user, Group $group)
    {
        return $user->hasPermission('worship-assignments.manage')
               && $user->account_id == $group->account_id;
    }

    public function save(User $user, Group $group)
    {
        return $user->hasPermission('worship-assignments.manage')
               && $user->account_id == $group->account_id;
    }

    public function retrieve(User $user, Group $group)
    {
        return $user->hasPermission('worship-assignments.index')
               && $user->account_id == $group->account_id;
    }

    public function delete(User $user, Group $group)
    {
        return $user->hasPermission('worship-assignments.manage')
               && $user->account_id == $group->account_id;
    }
}