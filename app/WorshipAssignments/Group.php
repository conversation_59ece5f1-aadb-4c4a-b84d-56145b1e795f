<?php

namespace App\WorshipAssignments;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;
use App\WorshipAssignments\Scopes\GroupVisibleToScope;

class Group extends Model
{
    protected $table = 'wa_groups';

    protected $casts = [
        'created_at'               => 'datetime',
        'updated_at'               => 'datetime',
        'deleted_at'               => 'datetime',
        'notify_declines_user_ids' => 'array',
        'notify_accepts_user_ids'  => 'array',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'name',
        'notify_declines_user_ids',
        'notify_accepts_user_ids',
        'max_assignments_per_person_per_period',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new GroupVisibleToScope())->getQuery($query, $user);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function periods()
    {
        return $this->hasMany(Period::class, 'wa_group_id');
    }

    public function positions()
    {
        return $this->hasMany(Position::class, 'wa_group_id')
            ->orderBy('sort_id', 'ASC');
    }

    public function getActivePositionCount()
    {
        return $this->positions()->isNotTemporary()->count();
    }

    public function getCurrentPeriod()
    {
        return $this->periods()
            ->orderBy('start_at', 'DESC')
            ->where('start_at', '<=', \Carbon\Carbon::now())
            ->where('end_at', '>=', \Carbon\Carbon::now())
            ->first();
    }

    public function getActivePeriods()
    {
        return $this->periods()
            ->orderBy('start_at', 'DESC')
            ->where('start_at', '<=', \Carbon\Carbon::now())
            ->where('end_at', '>=', \Carbon\Carbon::now())
            ->get();
    }

    public function getNextPeriod()
    {
        return $this->periods()->orderBy('start_at', 'DESC')->where('start_at', '>=', \Carbon\Carbon::now())->first();
    }

    public function picks()
    {
        return $this->hasManyThrough(
            Pick::class,
            Period::class,
            'wa_group_id',  // Foreign key on periods table
            'wa_period_id', // Foreign key on picks table
            'id',           // Local key on groups table
            'id'            // Local key on periods table
        );
    }
}
