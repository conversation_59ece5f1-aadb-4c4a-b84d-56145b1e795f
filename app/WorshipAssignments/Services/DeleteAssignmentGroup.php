<?php

namespace App\WorshipAssignments\Services;

use App\WorshipAssignments\Group;

class DeleteAssignmentGroup
{
    protected $group;

    public function __construct(Group $group)
    {
        $this->group = $group;

        return $this;
    }

    public function delete()
    {
        foreach ($this->group->positions as $position) {
            $position->delete();
        }

        foreach ($this->group->periods as $period) {
            foreach ($period->picks as $pick) {
                $pick->delete();
            }

            $period->delete();
        }

        $this->group->delete();

        return true;
    }
}
