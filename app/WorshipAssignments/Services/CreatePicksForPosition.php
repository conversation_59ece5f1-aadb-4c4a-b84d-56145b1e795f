<?php

namespace App\WorshipAssignments\Services;

use App\WorshipAssignments\Period;
use App\WorshipAssignments\Pick;
use App\WorshipAssignments\Position;
use DateInterval;
use DatePeriod;

class CreatePicksForPosition
{
    protected $period;
    protected $position;

    public function create()
    {
        // Get all the days in this time period.
        $interval = DateInterval::createFromDateString('1 day');

        // End date MUST be more than 00:00:00, otherwise, it counts the end date as the date prior.
        $dates_in_period = new DatePeriod($this->period->start_at, $interval, $this->period->end_at->add('1 hour'));

        // Attributes for creating a Pick for this Position.
        $attributes = [
            'account_id'        => $this->position->account_id,
            'wa_group_id'       => $this->position->wa_group_id,
            'wa_period_id'      => $this->period->id,
            'wa_position_id'    => $this->position->id,
            'user_id'           => null,
            'start_at'          => $this->period->start_at,
            'end_at'            => $this->position->span_whole_period ? $this->period->end_at : $this->period->start_at,
            'span_whole_period' => $this->position->span_whole_period,
            'day_of_week'       => $this->position->day_of_week,
            'token'             => uniqid(),
        ];

        // If this pick spans the whole period, we don't have a start/end date.
        if ($this->position->span_whole_period) {
            // Create a pick for the number of people we need for this position.
            for ($i = 0; $i < $this->position->number_of_users; $i++) {

                $attributes['user_id'] = null;

                if ($this->position->hasAutofillEnabled()) {
                    $attributes['user_id'] = $this->position->autoSelectUserIdForPosition($this->period);
                }

                // Create our pick.
                Pick::create($attributes);
            }
        } else {
            foreach ($dates_in_period as $day) {
                if ($day->format('N') == $this->position->day_of_week) {
                    // Create a pick for the number of people we need for this position.
                    for ($i = 0; $i < $this->position->number_of_users; $i++) {

                        $attributes['user_id'] = null;

                        $attributes['start_at'] = $day;
                        $attributes['end_at']   = $day;

                        if ($this->position->hasAutofillEnabled()) {
                            $attributes['user_id'] = $this->position->autoSelectUserIdForPosition($this->period, $day);
                        }

                        // Create our pick.
                        Pick::create($attributes);
                    }
                }
            }
        }

        return true;
    }

    public function forPeriod(Period $period)
    {
        $this->period = $period;

        return $this;
    }

    public function forPosition(Position $position)
    {
        $this->position = $position;

        return $this;
    }
}
