<?php

namespace App\WorshipAssignments\Services;

use App\WorshipAssignments\Pick;

class DeclineAssignment
{
    protected $pick = null;

    public function __construct(Pick $pick)
    {
        $this->pick = $pick;
    }

    public function decline()
    {
        $this->pick->has_replied  = now();
        $this->pick->is_confirmed = null;
        $this->pick->save();

        $this->sendNotifications();

        return $this->pick;
    }

    public function sendNotifications()
    {
        return (new SendPickDeclinedNotification($this->pick))
            ->viaEmail()
            ->viaMobileNotification()
            ->send();
    }
}
