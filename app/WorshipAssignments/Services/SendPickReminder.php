<?php

namespace App\WorshipAssignments\Services;

use App\Jobs\SendMobileNotification;
use App\WorshipAssignments\Pick;

class SendPickReminder
{
    protected $pick = null;

    public function __construct(Pick $pick)
    {
        $this->pick = $pick;
    }

    public function send()
    {
        if ($this->pick->user) {
            // Send a mobile notification
            SendMobileNotification::dispatch(
                $this->pick->user,
                'Assignment Reminder 📋',
                'You have the upcoming assignment: ' . $this->pick->position->name,
                ['type' => 'WA_new']
            )->onQueue('mobile');

            // Send an email also.
//            if ($user = $this->pick->user->getUserForEmailNotification()) {
//                $email = optional($user->getBestEmail())->email;
//
//                if ($email) {
//                    if ($user->is($this->pick->user)) {
//                        Mail::to($email)
//                            ->queue(new NewAssignment($this->pick->id));
//                    } else {
//                        Mail::to($email)
//                            ->queue(new NewAssignmentForChild($this->pick->id));
//                    }
//
//                    $this->pick->sent_email = now();
//                    $this->pick->save();
//                }
//
//                return response(200);
//            } else {
//                return response(403);
//            }

            return response(200);
        } else {
            return response(403);
        }
    }
}
