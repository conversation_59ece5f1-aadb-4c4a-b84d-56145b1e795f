<?php

namespace App\WorshipAssignments\Services;

use App\Groups\Services\CreateGroupNotification;
use App\Jobs\SendMobileNotification;
use App\Mail\Admin\WorshipAssignments\NewAssignment;
use App\Mail\Admin\WorshipAssignments\NewAssignmentForChild;
use App\WorshipAssignments\Pick;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SendPickNotification
{
    public $mobile_notification_type = 'WA_new';
    protected $pick = null;
    protected $via_email = false;
    protected $via_mobile_notification = false;

    public function __construct(Pick $pick)
    {
        $this->pick = $pick;
    }

    public function viaEmail(bool $value = true)
    {
        $this->via_email = $value;

        return $this;
    }

    public function viaMobileNotification(bool $value = true)
    {
        $this->via_mobile_notification = $value;

        return $this;
    }

    public function recordNotification($user)
    {
        (new CreateGroupNotification())
            ->forUser($user)
            ->forGroupPost($this->post)
            ->type($this->mobile_notification_type)
            ->withMessage($this->post->creator->display_first_name . ' ' . Str::limit($this->post->creator->last_name, 1, null) . ' posted a new message in (' . $this->group->name . ')')
            ->create();
    }

    public function send()
    {
        if ($this->pick->user) {
            // Send a mobile notification
            if ($this->via_mobile_notification) {
                SendMobileNotification::dispatch(
                    $this->pick->user,
                    'New Assignment! 📋',
                    'Please confirm availability or decline to help our organizers.',
                    ['type' => $this->mobile_notification_type]
                )->onQueue('mobile');
            }

            // Send an email.
            if ($this->via_email) {
                if ($user = $this->pick->user->getUserForEmailNotification()) {
                    $email = optional($user->getBestEmail())->email;

                    if ($email) {
                        if ($user->is($this->pick->user)) {
                            Mail::to($email)
                                ->queue(new NewAssignment($this->pick->id));
                        } else {
                            Mail::to($email)
                                ->queue(new NewAssignmentForChild($this->pick->id));
                        }

                        $this->pick->sent_email = now();
                        $this->pick->save();
                    }
                }
            }
        }

        return true;
    }
}
