<?php

namespace App\WorshipAssignments\Services;

use Dompdf\Dompdf;
use Dompdf\Options;

class WorshipAssignmentsPrintable
{
    private $period;

    public function forPeriod($period)
    {
        $this->period = $period;

        return $this;
    }

    public function createPDF()
    {
        $dompdf = new Dompdf();
        $dompdf->setPaper('letter', 'portrait');
        $dompdf->setOptions(
            (new Options())
                ->set('isHtml5ParserEnabled', true)
        );
        $html = view('admin.assignments.printables.worship-assignment-printable')
            ->with('period', $this->period)
            ->with('group', $this->period->group)
            ->render();

        $dompdf->loadHtml($html);

        // Render the HTML as PDF
        $dompdf->render();

        // Output the generated PDF to Browser
        $dompdf->stream('Worship Assignments - ' . $this->period->name . '.pdf');
    }
}