<?php

namespace App\WorshipAssignments\Services;

use App\Jobs\SendMobileNotification;
use App\Mail\Admin\WorshipAssignments\AssignmentDeclined;
use App\Users\User;
use App\WorshipAssignments\Pick;
use Illuminate\Support\Facades\Mail;

class SendPickDeclinedNotification
{
    protected $pick                    = null;
    protected $via_email               = false;
    protected $via_mobile_notification = false;

    public function __construct(Pick $pick)
    {
        $this->pick = $pick;
    }

    public function viaEmail(bool $value = true)
    {
        $this->via_email = $value;

        return $this;
    }

    public function viaMobileNotification(bool $value = true)
    {
        $this->via_mobile_notification = $value;

        return $this;
    }

    public function send()
    {
        $users_to_notify = $this->pick->group->notify_declines_user_ids ?? [];

        foreach ($users_to_notify as $user_id) {
            $user_to_notify = User::find($user_id);

            // Send a mobile notification
            if ($this->via_mobile_notification) {
                SendMobileNotification::dispatch(
                    $user_to_notify,
                    'Assignment Declined ❌',
                    $this->pick->user->name . ' declined the assignment: ' . $this->pick->position->name,
                    ['type' => 'WA_update']
                )->onQueue('mobile');
            }

            // Send an email.
            if ($this->via_email) {
                $email = optional($user_to_notify->getBestEmail())->email;

                if ($email) {
                    Mail::to($email)
                        ->queue(new AssignmentDeclined($this->pick->id));
                }
            }
        }

        return true;
    }
}
