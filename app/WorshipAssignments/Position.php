<?php

namespace App\WorshipAssignments;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Subarea;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Position extends Model
{
    use SoftDeletes;

    protected $table = 'wa_positions';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'wa_group_id',
        'name',
        'day_of_week',
        'span_whole_period',
        'restrict_user_selections_by_involvement_selections',
        'number_of_users',
        'notes',
        'involvement_category_id',
        'involvement_area_id',
        'involvement_subarea_id',
        'sort_id',
        'is_blocking',
        'is_temporary',
        'can_lead_only',
        'enable_autofill',
        'reminder_1_enabled',
        'reminder_1_days_before',
        'reminder_1_hour_to_send',
        'reminder_1_via_email',
        'reminder_1_via_mobile_notification',
        'reminder_2_enabled',
        'reminder_2_days_before',
        'reminder_2_hour_to_send',
        'reminder_2_via_email',
        'reminder_2_via_mobile_notification',
    ];

    static public $days_of_week = [
        7 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'wa_group_id', 'id');
    }

    public function involvementCategory()
    {
        return $this->hasOne(Category::class, 'id', 'involvement_category_id');
    }

    public function picks()
    {
        return $this->hasMany(Pick::class, 'id');
    }

    public function involvementArea()
    {
        return $this->hasOne(Area::class, 'id', 'involvement_area_id');
    }

    public function involvementSubarea()
    {
        return $this->hasOne(Subarea::class, 'id', 'involvement_subarea_id');
    }

    public function hasAutofillEnabled()
    {
        return $this->enable_autofill
               && ($this->involvementCategory || $this->involvementArea || $this->involvementSubarea);
    }

    public function hasAutoselectEnabled()
    {
        return $this->involvementCategory || $this->involvementArea || $this->involvementSubarea;
    }

    public function scopeIsNotTemporary($query)
    {
        return $query->where('is_temporary', false);
    }

    public function dayOfWeek()
    {
        return self::$days_of_week[$this->day_of_week];
    }

    public function remindersEnabled()
    {
        return $this->reminder_1_enabled || $this->reminder_2_enabled;
    }

    public function getCarbonDayOfWeek()
    {
        if ($this->day_of_week == 1) {
            return Carbon::MONDAY;
        }
        if ($this->day_of_week == 2) {
            return Carbon::TUESDAY;
        }
        if ($this->day_of_week == 3) {
            return Carbon::WEDNESDAY;
        }
        if ($this->day_of_week == 4) {
            return Carbon::THURSDAY;
        }
        if ($this->day_of_week == 5) {
            return Carbon::FRIDAY;
        }
        if ($this->day_of_week == 6) {
            return Carbon::SATURDAY;
        }
        if ($this->day_of_week == 7) {
            return Carbon::SUNDAY;
        }
    }

    /**
     * This method gets the ID of a  user who has volunteered for a Category/Area/Subarea that is selected for the current Position.
     * It will leave out users who have already been selected for this position for the Period provided.
     */
    public function autoSelectUserIdForPosition(Period $period, $specific_day = null)
    {
        $possible_users = null;

        // Get a list of User IDs that are already serving for this position for this period.
        // -- WHERE     - They are not serving a position that blocks a specific day of the week, for this period already, for the WHOLE period.
        // -- OR WHERE  - If we're creating a pick for "spans_whole_period" position, then we don't care about the specific day of the week. If we're looking to pick all Sundays, but they have a blocking assignment on ANY Sunday, skip them.
        // -- OR WHERE  - They are not serving a position that blocks this specific DATE.
        $user_ids_not_available_for_this_position_array = User::whereHas('worshipAssignmentPicks', function ($query) use ($period, $specific_day) {
            $query
                ->where(function ($query) use ($period) {
                    $query->where('wa_period_id', $period->id)
                        ->whereHas('position', function ($query) {
                            $query->where('day_of_week', $this->day_of_week)
                                ->where('span_whole_period', 1)
                                ->where('is_blocking', 1);
                        });
                })
                ->when(($specific_day == null), function ($query) use ($period) {
                    $query->orWhere(function ($query) use ($period) {
                        $query->where('wa_period_id', $period->id)
                            ->whereHas('position', function ($query) {
                                $query->where('day_of_week', $this->day_of_week)
                                    ->where('is_blocking', 1);
                            });
                    });
                })
                ->when(($specific_day !== null), function ($query) use ($period, $specific_day) {
                    $query->orWhere(function ($query) use ($period, $specific_day) {
                        $query->where('wa_period_id', $period->id)
                            ->whereHas('position', function ($query) {
                                $query->where('day_of_week', $this->day_of_week)
                                    ->where('is_blocking', 1);
                            })
                            ->where('start_at', $specific_day->format('Y-m-d'));
                    });
                });
        })
            ->pluck('id')
            ->toArray();

        // Get a list of User IDs that have already been picked for ANY position for this period more than the limit we set in.
        $user_ids_having_too_many_assignments_already = [];
        if ($period?->group->max_assignments_per_person_per_period > 0) {
            $user_ids_having_too_many_assignments_already = Pick::where('wa_period_id', $period->id)
                ->where('wa_group_id', $this->wa_group_id)
                ->where('user_id', '!=', null)
                ->groupBy('user_id')
                ->havingRaw('count(user_id) >= ' . intval($period?->group->max_assignments_per_person_per_period))
                ->pluck('user_id')
                ->toArray();
        }

        $possible_users = $this->getQualifiedUsers(viewing_user: auth()->user(), restrict_by_autofill: true)
            ->whereNotIn('id', array_merge($user_ids_not_available_for_this_position_array, $user_ids_having_too_many_assignments_already))
            ->get();

        if (!$possible_users || $possible_users->count() == 0) {
            return null;
        }

        $picked_user = $this->getUserLastPickedOrderforPosition($this, $possible_users->pluck('id'));

        return $picked_user ? $picked_user->id : null;
    }

    public function getUserLastPickedOrderforPosition(Position $position, $user_ids_available_to_pick_from_array)
    {
        // First, from the possible users, pick one that has never been picked before.
        // -- Do not have a PICK for this POSITION
        // -- Are a member
        // -- Are in the list of user_ids
        // -- just get the first result
        $picked_user = User::where('account_id', $position->account->id)
            ->whereDoesntHave('worshipAssignmentPicks', function ($query) use ($position) {
                $query->where('wa_position_id', $position->id);
            })
            ->whereIn('id', $user_ids_available_to_pick_from_array)
            ->membersOnly()
//            ->orderBy('created_at', 'DESC')
            ->inRandomOrder()
            ->first();

        // If we found someone, return them.
        if ($picked_user) {
            return $picked_user;
        }

        // Get people who were picked before, ordered by end_at.
        $latest_user_to_be_picked = Pick::select(['user_id', 'end_at'])
            ->where('wa_position_id', $position->id)
            ->where('account_id', $position->account_id)
            ->whereIn('user_id', $user_ids_available_to_pick_from_array)
            ->orderBy('end_at', 'ASC')
            ->get();

        $pick_uniques = $latest_user_to_be_picked->reverse()->unique('user_id')->reverse();

        $pick = $pick_uniques->first();

        if ($pick) {
            return $pick->user;
        }

        // We didn't find anyone, anywhere. :(
        return null;
    }

    public function getQualifiedUsers($viewing_user, $restrict_by_autofill = false)
    {
        // This captures all users manually marked to not autofill worship assignments.
        // We only do this if the position has an area/subarea associated with it.
        if ($this->involvement_subarea_id || $this->involvement_area_id) {
            $users_not_approved_for_autofill = DB::table('involvement_to_user')
                ->where(function ($query) {
                    if ($this->involvement_subarea_id) {
                        $query->where('involvement_subarea_id', $this->involvement_subarea_id);
                    } elseif ($this->involvement_area_id) {
                        $query->where('involvement_area_id', $this->involvement_area_id);
                    }
                })
                ->where('is_approved_for_assignments', false) // !! IMPORTANT !!
                ->get()->pluck('user_id')->toArray();
        } else {
            $users_not_approved_for_autofill = [];
        }

        return User::visibleTo($viewing_user)
            ->whereNotIn('id', $users_not_approved_for_autofill) // Do NOT include users that have not been approved for autofill.
            ->includeInReports()
            ->membersOnly()
            // Figure out if we should only select members who made certain involvement selections
            ->when(($restrict_by_autofill || $this->restrict_user_selections_by_involvement_selections), function ($query) {
                $query->when($this->involvementSubarea, function ($query) {
                    $query->selectedInvolvementSubarea($this->involvementSubarea);
                })->when((!$this->involvementSubarea && $this->involvementArea), function ($query) {
                    $query->selectedInvolvementArea($this->involvementArea);
                })->when(((!$this->involvementSubarea && !$this->involvementArea) && $this->involvementCategory), function ($query) {
                    $query->selectedInvolvementCategory($this->involvementCategory);
                });
            })
            // Make sure we apply all restrictions from the lowest level up.
            ->when($this->involvementSubarea, function ($query) {
                if ($this->involvementSubarea->baptized_only) {
                    $query->whereNotNull('is_baptized');
                }
                if ($this->involvementSubarea->completed_background_check_only) {
                    $query->whereNotNull('date_background_check');
                }
                if ($this->involvementSubarea->approved_to_teach_only) {
                    $query->whereNotNull('can_teach');
                }
                if ($this->involvementSubarea->men_only || $this->involvementSubarea->women_only) {
                    if ($this->involvementSubarea->men_only && !$this->involvementSubarea->women_only) {
                        $query->where('gender', 'male');
                    } elseif (!$this->involvementSubarea->men_only && $this->involvementSubarea->women_only) {
                        $query->where('gender', 'female');
                    }
                }
            })
            ->when(!$this->involvementSubarea && $this->involvementArea, function ($query) {
                if ($this->involvementArea->baptized_only) {
                    $query->whereNotNull('is_baptized');
                }
                if ($this->involvementArea->completed_background_check_only) {
                    $query->whereNotNull('date_background_check');
                }
                if ($this->involvementArea->approved_to_teach_only) {
                    $query->whereNotNull('can_teach');
                }
                if ($this->involvementArea->men_only || $this->involvementArea->women_only) {
                    if ($this->involvementArea->men_only && !$this->involvementArea->women_only) {
                        $query->where('gender', 'male');
                    } elseif (!$this->involvementArea->men_only && $this->involvementArea->women_only) {
                        $query->where('gender', 'female');
                    }
                }
            })
            ->when((!$this->involvementSubarea && !$this->involvementArea) && $this->involvementCategory, function ($query) {
                if ($this->involvementCategory->baptized_only) {
                    $query->whereNotNull('is_baptized');
                }
                if ($this->involvementCategory->completed_background_check_only) {
                    $query->whereNotNull('date_background_check');
                }
                if ($this->involvementCategory->approved_to_teach_only) {
                    $query->whereNotNull('can_teach');
                }
                if ($this->involvementCategory->men_only || $this->involvementCategory->women_only) {
                    if ($this->involvementCategory->men_only && !$this->involvementCategory->women_only) {
                        $query->where('gender', 'male');
                    } elseif (!$this->involvementCategory->men_only && $this->involvementCategory->women_only) {
                        $query->where('gender', 'female');
                    }
                }
            });
    }
}
