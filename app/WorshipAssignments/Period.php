<?php

namespace App\WorshipAssignments;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;

class Period extends Model
{
    protected $table = 'wa_periods';

    const UPDATED_AT = null;

    protected $casts = [
        'created_at'   => 'datetime',
        'start_at'     => 'datetime',
        'end_at'       => 'datetime',
        'is_published' => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'created_at',
        'wa_group_id',
        'name',
        'start_at',
        'end_at',
        'is_published',
    ];

    static public $days_of_week = [
        7 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    static public $days_of_week_non_standard = [
        0 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('wa_periods.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function group()
    {
        return $this->hasOne(Group::class, 'id', 'wa_group_id');
    }

    public function positions()
    {
        return $this->hasManyThrough(Position::class, Group::class, 'id', 'wa_group_id', 'wa_group_id');
    }

    public function picks()
    {
        return $this->hasMany(Pick::class, 'wa_period_id');
    }

    public function scopeGetActivePeriod($query)
    {
        return $query
            ->where('start_at', '<=', Carbon::now())
            ->where('end_at', '>=', Carbon::now()->subDay());
    }

    public function scopeGetActiveOrFuturePeriod($query)
    {
        return $query->where('end_at', '>=', Carbon::now()->startOfDay()->subDay());
    }

    public function scopeIsPublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeIsNotPublished($query)
    {
        return $query->where('is_published', false);
    }

    public function isPublished()
    {
        return $this->is_published;
    }

    public function isCurrent()
    {
        return self::where('account_id', $this->account_id)
                   ->getActivePeriod()
                   ->first()
                   ?->id == $this->id;
    }

    public function isUpcoming()
    {
        return $this->start_at >= Carbon::now()
               && $this->end_at >= Carbon::now()
               && self::where('account_id', $this->account_id)
                      ->getActivePeriod()
                      ->first()
                      ?->id != $this->id;
    }

    public function isEnded()
    {
        return $this->start_at <= Carbon::now() && $this->end_at <= Carbon::now();
    }

    public function percentPicksFilled()
    {
        if ($this->filledPicks()->count() > 0) {
            return ceil($this->filledPicks()->count() * 100 / $this->picks()->count());
        } else {
            return 0;
        }
    }

    public function notifiablePicks()
    {
        return $this->picks()
            ->whereNull('is_confirmed')
            ->whereNull('sent_email')
            ->whereNull('sent_sms')
            ->whereNotNull('user_id')
            ->get();
    }

    public function filledPicks()
    {
        return $this->picks()
            ->whereNotNull('is_confirmed')
            ->get();
    }

    public function declinedPicks()
    {
        return $this->picks()
            ->whereNull('is_confirmed')
            ->whereNotNull('has_replied')
            ->get();
    }

    public function noResponsePicks()
    {
        return $this->picks()
            ->whereNull('is_confirmed')
            ->whereNull('has_replied')
            ->get();
    }

    // Converts 0-6 to 1-7
    public function convertDayOfWeekToISO8601($day_of_week)
    {
        if ($day_of_week == 0) {
            return 7;
        } else {
            return $day_of_week;
        }
    }

    public function createPicksForPosition(Position $position)
    {
        // Get all the days in this time period.
        $interval = DateInterval::createFromDateString('1 day');
        // End date MUST be more than 00:00:00, otherwise, it counts the end date as the date prior.
        $period = new DatePeriod($this->start_at, $interval, $this->end_at->add('1 hour'));

        $attributes = [
            'account_id'        => $position->account_id,
            'wa_group_id'       => $position->wa_group_id,
            'wa_period_id'      => $this->id,
            'wa_position_id'    => $position->id,
            'user_id'           => null,
            'start_at'          => $this->start_at,
            'end_at'            => $position->span_whole_period ? $this->end_at : $this->start_at,
            'span_whole_period' => $position->span_whole_period,
            'day_of_week'       => $position->day_of_week,
            'token'             => uniqid(),
        ];

        if ($position->span_whole_period) {
            for ($i = 0; $i < $position->number_of_users; $i++) {
                $attributes['user_id'] = $position->autoSelectUserIdForPosition($this);
                Pick::create($attributes);
            }
        } else {
            foreach ($period as $day) {
                if ($day->format('N') == $position->day_of_week) {
                    for ($i = 0; $i < $position->number_of_users; $i++) {
                        $attributes['start_at'] = $day;
                        $attributes['end_at']   = $day;
                        $attributes['user_id']  = $position->autoSelectUserIdForPosition($this, $day);
                        Pick::create($attributes);
                    }
                }
            }
        }

        return true;
    }
}
