<?php

namespace App\WorshipAssignments\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Jobs\SendMobileNotification;
use App\Mail\Admin\WorshipAssignments\NewAssignment;
use App\Mail\Admin\WorshipAssignments\NewAssignmentForChild;
use App\WorshipAssignments\Group;
use App\WorshipAssignments\Period;
use App\WorshipAssignments\Pick;
use App\WorshipAssignments\Position;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class WorshipAssignmentPickController extends Controller
{
    public function view(Group $group)
    {
        return view('admin.worship-assignments.positions.view')
            ->with('group', $group);
    }

    public function create(Group $group)
    {
        return view('admin.worship-assignments.positions.create')
            ->with('group', $group);
    }

    public function store(Group $group)
    {
        $this->validate(request(), [
            'name'                    => 'required|string|max:128',
            'day_of_week'             => 'required|integer',
            'number_of_users'         => 'required|integer',
            'span_whole_period'       => 'nullable|integer',
            'involvement_category_id' => 'nullable|integer',
            'involvement_area_id'     => 'nullable|integer',
            'involvement_subarea_id'  => 'nullable|integer',
        ]);

        try {
            Position::create([
                'account_id'              => Auth::user()->account->id,
                'account_location_id'     => Auth::user()->account->location ? Auth::user()->account->location->id : null,
                'wa_group_id'             => $group->id,
                'name'                    => request()->input('name'),
                'day_of_week'             => request()->input('day_of_week'),
                'number_of_users'         => request()->input('number_of_users'),
                'span_whole_period'       => request()->input('span_whole_period'),
                'involvement_category_id' => request()->input('involvement_category_id'),
                'involvement_area_id'     => request()->input('involvement_area_id'),
                'involvement_subarea_id'  => request()->input('involvement_subarea_id'),
            ]);
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.worship-assignments.groups.positions.view', $group))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Group $group, Period $period, Pick $pick)
    {
        $users = $pick->position->getQualifiedUsers(auth()->user())->get();

        $users = $users->map(function ($user) {
            return [
                'value' => $user->last_name . ', ' . $user->display_first_name,
                'id'    => $user->id,
            ];
        });

        return view('admin.worship-assignments.picks.edit')
            ->with('group', $group)
            ->with('period', $period)
            ->with('pick', $pick)
            ->with('users', $users);
    }

    public function save(Group $group, Period $period, Pick $pick)
    {
        $this->validate(request(), [
            'user_id'      => 'nullable|integer',
            'notes'        => 'nullable|string',
            'is_confirmed' => 'nullable|integer',
        ]);

        try {
            // If we remove/change a user, then reset user specific things.
            if (!request()->input('user_id') || request()->input('user_id') != $pick->user_id) {
                $pick->sent_email  = null;
                $pick->sent_sms    = null;
                $pick->has_replied = null;
            } // Otherwise, if we're "unconfirming" this pick, we should reset those same things.
            elseif (!request()->input('is_confirmed')) {
                $pick->sent_email  = null;
                $pick->sent_sms    = null;
                $pick->has_replied = null;
            }

            $pick->user_id      = request()->input('user_id');
            $pick->notes        = request()->input('notes');
            $pick->is_confirmed = request()->input('is_confirmed') ? Carbon::now() : null;

            $pick->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.worship-assignments.periods.view', [$group, $period])
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Group $group)
    {
        // $group->delete();

        return redirect(route('admin.worship-assignments.index'))
            ->with('message.success', 'NOT Deleted. There is a lot left to delete.');
    }

    public function markConfirmed(Group $group, Period $period, Pick $pick, $is_confirmed = 'true')
    {
        if ($is_confirmed == 'true') {
            $pick->is_confirmed = Carbon::now();
            $pick->save();
        } else {
            $pick->has_replied  = null;
            $pick->sent_email   = null;
            $pick->is_confirmed = null;
            $pick->save();
        }


        return response(200);
    }

    public function resendNotification(Group $group, Period $period, Pick $pick)
    {
        if ($pick->user) {
            // Send a mobile notification
            SendMobileNotification::dispatch(
                $pick->user,
                'New Worship Assignment! 📋',
                'Please confirm availability or decline to help our organizers.',
                ['type' => 'WA_new']
            )->onQueue('mobile');

            // Send an email also.
            if ($user = $pick->user->getUserForEmailNotification()) {
                $email = optional($user->getBestEmail())->email;

                if ($email) {
                    if ($user->is($pick->user)) {
                        Mail::to($email)
                            ->queue(new NewAssignment($pick->id));
                    } else {
                        Mail::to($email)
                            ->queue(new NewAssignmentForChild($pick->id));
                    }

                    $pick->sent_email = Carbon::now();
                    $pick->save();
                }

                return response(200);
            } else {
                return response(403);
            }
        } else {
            return response(403);
        }
    }
}
