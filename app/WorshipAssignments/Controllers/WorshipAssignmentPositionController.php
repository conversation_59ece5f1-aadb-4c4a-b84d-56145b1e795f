<?php

namespace App\WorshipAssignments\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Involvement\Area;
use App\Involvement\Subarea;
use App\WorshipAssignments\Group;
use App\WorshipAssignments\Position;
use Illuminate\Support\Facades\Auth;

class WorshipAssignmentPositionController extends Controller
{
    public function view(Group $group)
    {
        return view('admin.assignments.positions.view')
            ->with('group', $group);
    }

    public function create(Group $group)
    {
        return view('admin.assignments.positions.create')
            ->with('group', $group);
    }

    public function store(Group $group)
    {
        $this->validate(request(), [
            'name'                                               => 'required|string|max:128',
            'notes'                                              => 'nullable|string',
            'day_of_week'                                        => 'required|integer',
            'number_of_users'                                    => 'required|integer',
            'span_whole_period'                                  => 'nullable|integer',
            'restrict_user_selections_by_involvement_selections' => 'nullable|integer',
            'involvement_category_id'                            => 'nullable|integer',
            'involvement_area_id'                                => 'nullable|integer',
            'involvement_subarea_id'                             => 'nullable|integer',
            'is_blocking'                                        => 'nullable|integer',
            'is_temporary'                                       => 'nullable|integer',
            'can_lead_only'                                      => 'nullable|integer',
            'enable_autofill'                                    => 'nullable|integer',
            'reminder_1_enabled'                                 => 'nullable|integer',
            'reminder_1_days_before'                             => 'nullable|integer',
            'reminder_1_hour_to_send'                            => 'nullable|integer',
            'reminder_1_via_email'                               => 'nullable|integer',
            'reminder_1_via_mobile_notification'                 => 'nullable|integer',
            'reminder_2_enabled'                                 => 'nullable|integer',
            'reminder_2_days_before'                             => 'nullable|integer',
            'reminder_2_hour_to_send'                            => 'nullable|integer',
            'reminder_2_via_email'                               => 'nullable|integer',
            'reminder_2_via_mobile_notification'                 => 'nullable|integer',
        ]);

        try {
            Position::create([
                'account_id'                                         => Auth::user()->account->id,
                'account_location_id'                                => Auth::user()->account->location ? Auth::user()->account->location->id : null,
                'wa_group_id'                                        => $group->id,
                'name'                                               => request()->input('name'),
                'notes'                                              => request()->input('notes'),
                'day_of_week'                                        => request()->input('day_of_week'),
                'number_of_users'                                    => request()->input('number_of_users', 1),
                'span_whole_period'                                  => request()->input('span_whole_period', 0),
                'restrict_user_selections_by_involvement_selections' => request()->input('restrict_user_selections_by_involvement_selections', 0),
                'involvement_category_id'                            => request()->input('involvement_category_id'),
                'involvement_area_id'                                => request()->input('involvement_area_id'),
                'involvement_subarea_id'                             => request()->input('involvement_subarea_id'),
                'is_blocking'                                        => request()->input('is_blocking', false),
                'is_temporary'                                       => request()->input('is_temporary', false),
                'can_lead_only'                                      => request()->input('can_lead_only', false),
                'enable_autofill'                                    => request()->input('enable_autofill', false),
                'reminder_1_enabled'                                 => request()->input('reminder_1_enabled'),
                'reminder_1_days_before'                             => request()->input('reminder_1_days_before'),
                'reminder_1_hour_to_send'                            => request()->input('reminder_1_hour_to_send'),
                'reminder_1_via_email'                               => request()->input('reminder_1_via_email'),
                'reminder_1_via_mobile_notification'                 => request()->input('reminder_1_via_mobile_notification'),
                'reminder_2_enabled'                                 => request()->input('reminder_2_enabled'),
                'reminder_2_days_before'                             => request()->input('reminder_2_days_before'),
                'reminder_2_hour_to_send'                            => request()->input('reminder_2_hour_to_send'),
                'reminder_2_via_email'                               => request()->input('reminder_2_via_email'),
                'reminder_2_via_mobile_notification'                 => request()->input('reminder_2_via_mobile_notification'),
            ]);
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.worship-assignments.groups.positions.view', $group))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Group $group, Position $position)
    {
        $areas    = [];
        $subareas = [];

        if ($position->involvement_category_id) {
            $areas = Area::where('involvement_category_id', $position->involvement_category_id)->get();
        }
        if ($position->involvement_area_id) {
            $subareas = Subarea::where('involvement_area_id', $position->involvement_area_id)->get();
        }

        return view('admin.assignments.positions.edit')
            ->with('areas', $areas)
            ->with('subareas', $subareas)
            ->with('group', $group)
            ->with('position', $position);
    }

    public function save(Group $group, Position $position)
    {
        $this->validate(request(), [
            'name'                                               => 'required|string|max:128',
            'notes'                                              => 'nullable|string',
            'day_of_week'                                        => 'required|integer',
            'number_of_users'                                    => 'required|integer',
            'span_whole_period'                                  => 'nullable|integer',
            'restrict_user_selections_by_involvement_selections' => 'nullable|integer',
            'involvement_category_id'                            => 'nullable|integer',
            'involvement_area_id'                                => 'nullable|integer',
            'involvement_subarea_id'                             => 'nullable|integer',
            'is_blocking'                                        => 'nullable|integer',
            'can_lead_only'                                      => 'nullable|integer',
            'enable_autofill'                                    => 'nullable|integer',
            'reminder_1_enabled'                                 => 'nullable|integer',
            'reminder_1_days_before'                             => 'nullable|integer',
            'reminder_1_hour_to_send'                            => 'nullable|integer',
            'reminder_1_via_email'                               => 'nullable|integer',
            'reminder_1_via_mobile_notification'                 => 'nullable|integer',
            'reminder_2_enabled'                                 => 'nullable|integer',
            'reminder_2_days_before'                             => 'nullable|integer',
            'reminder_2_hour_to_send'                            => 'nullable|integer',
            'reminder_2_via_email'                               => 'nullable|integer',
            'reminder_2_via_mobile_notification'                 => 'nullable|integer',
        ]);

        try {
            $position->name                                               = request()->input('name');
            $position->notes                                              = request()->input('notes');
            $position->day_of_week                                        = request()->input('day_of_week');
            $position->number_of_users                                    = request()->input('number_of_users', 1);
            $position->span_whole_period                                  = request()->input('span_whole_period', 0);
            $position->restrict_user_selections_by_involvement_selections = request()->input('restrict_user_selections_by_involvement_selections', 0);
            $position->involvement_category_id                            = request()->input('involvement_category_id');
            $position->involvement_area_id                                = request()->input('involvement_area_id');
            $position->involvement_subarea_id                             = request()->input('involvement_subarea_id');
            $position->is_blocking                                        = request()->input('is_blocking', false);
            $position->can_lead_only                                      = request()->input('can_lead_only', false);
            $position->enable_autofill                                    = request()->input('enable_autofill', false);
            $position->reminder_1_enabled                                 = request()->input('reminder_1_enabled');
            $position->reminder_1_days_before                             = request()->input('reminder_1_days_before');
            $position->reminder_1_hour_to_send                            = request()->input('reminder_1_hour_to_send');
            $position->reminder_1_via_email                               = request()->input('reminder_1_via_email');
            $position->reminder_1_via_mobile_notification                 = request()->input('reminder_1_via_mobile_notification');
            $position->reminder_2_enabled                                 = request()->input('reminder_2_enabled');
            $position->reminder_2_days_before                             = request()->input('reminder_2_days_before');
            $position->reminder_2_hour_to_send                            = request()->input('reminder_2_hour_to_send');
            $position->reminder_2_via_email                               = request()->input('reminder_2_via_email');
            $position->reminder_2_via_mobile_notification                 = request()->input('reminder_2_via_mobile_notification');

            $position->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.worship-assignments.groups.positions.view', $group)
            ->with('message.success', 'Saved successfully.');
    }

    public function destroy(Group $group, Position $position)
    {
        try {
            $position->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.worship-assignments.groups.positions.view', $group))
            ->with('message.success', 'Position removed deleted.');
    }

    public function updateSortId(Group $group, Position $position)
    {
        $new_sort_id = request()->input('new_sort_id');

        // Start from our old sort ID
        $positions = Position::where('wa_group_id', $group->id)
            ->orderBy('sort_id', 'ASC')
            ->get();

        $i = 0;
        foreach ($positions as $current_position) {
            if ($current_position->id == $position->id) {
                $position->sort_id = $new_sort_id;
                $position->save();
            } else {
                // Skip over the new_sort_id
                if ($i == $new_sort_id) {
                    $i++;
                }
                $current_position->sort_id = $i;
                $current_position->save();
                $i++;
            }
        }

        return response(200);
    }
}
