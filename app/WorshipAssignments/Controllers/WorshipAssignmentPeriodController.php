<?php

namespace App\WorshipAssignments\Controllers;

use App\Base\Http\Controllers\Controller;
use App\WorshipAssignments\Group;
use App\WorshipAssignments\Period;
use App\WorshipAssignments\Services\CreateGroupTimePeriod;
use App\WorshipAssignments\Services\SendPickNotification;
use App\WorshipAssignments\Services\WorshipAssignmentsPrintable;
use Illuminate\Support\Facades\Auth;

class WorshipAssignmentPeriodController extends Controller
{
    public function view(Group $group, Period $period)
    {
        if (request()->has('day')) {
            $picks = $period
                ->picks()
                ->where('span_whole_period', true)
                ->where('day_of_week', $period->convertDayOfWeekToISO8601(request()->get('day')))
                ->orderBy('id')
                ->get();
        } elseif (request()->has('date')) {
            $picks = $period
                ->picks()
                ->where('span_whole_period', false)
                ->where('start_at', request()->get('date'))
                ->orderBy('id')
                ->get();
        } else {
            $picks = $period->picks()->orderBy('start_at')->orderBy('id')->get();
        }

        return view('admin.assignments.periods.view')
            ->with('group', $group)
            ->with('period', $period)
            ->with('picks', $picks);
    }

    public function create(Group $group)
    {
        return view('admin.assignments.periods.create')
            ->with('group', $group);
    }

    public function store(Group $group)
    {
        $this->validate(request(), [
            'name'     => 'required|string|max:128',
            'start_at' => 'required|date',
            'end_at'   => 'required|date',
        ]);

        //try {
        $period = (new CreateGroupTimePeriod())
            ->forAccount(Auth::user()->account)
            ->forGroup($group)
            ->create([
                'name'     => request()->input('name'),
                'start_at' => request()->input('start_at'),
                'end_at'   => request()->input('end_at'),
            ]);

//        } catch (\Exception $e) {
//            return back()->with('message.failure', $e->getMessage());
//        }

        return redirect(route('admin.worship-assignments.groups.view', $group, $period))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Group $group, Period $period)
    {
        return view('admin.assignments.periods.edit')
            ->with('period', $period);
    }

    public function save(Group $group, Period $period)
    {
        $this->validate(request(), [
            'name' => 'required|string|max:128',
        ]);

        try {
            $period->name = request()->input('name');
            $period->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.worship-assignments.periods.view', [$group, $period])
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Group $group, Period $period)
    {
        return view('admin.assignments.periods.delete')
            ->with('period', $period);
    }

    public function destroy(Group $group, Period $period)
    {
        try {
            $period->picks()->delete();
            $period->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.worship-assignments.groups.view', $group))
            ->with('message.success', 'Time Period deleted.');
    }

    public function createTemporaryPosition(Group $group, Period $period)
    {
        return view('admin.assignments.periods.create-temporary-position')
            ->with('group', $group)
            ->with('period', $period);
    }

    public function confirmSendNotifications(Group $group, Period $period)
    {
        return view('admin.assignments.periods.confirm-send-notifications')
            ->with('group', $group)
            ->with('period', $period);
    }

    public function publish(Group $group, Period $period)
    {
        try {
            $period->is_published = true;
            $period->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.worship-assignments.periods.view', [$group, $period]))
            ->with('message.success', 'Published successfully.');
    }

    public function unpublish(Group $group, Period $period)
    {
        try {
            $period->is_published = false;
            $period->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.worship-assignments.periods.view', [$group, $period]))
            ->with('message.success', 'Unpublished successfully.');
    }

    public function sendNotifications(Group $group, Period $period)
    {
        foreach ($period->notifiablePicks() as $pick) {
            (new SendPickNotification($pick))
                ->viaEmail(boolval(request('send_via_email', false)))
                ->viaMobileNotification(boolval(request('send_via_mobile_notification', false)))
                ->send();
        }

        return redirect(route('admin.worship-assignments.periods.view', [$group, $period]))
            ->with('message.success', 'All notifications sent.');
    }

    public function print(Group $group, Period $period)
    {
        try {
            (new WorshipAssignmentsPrintable())
                ->forPeriod($period)
                ->createPDF();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }
    }
}
