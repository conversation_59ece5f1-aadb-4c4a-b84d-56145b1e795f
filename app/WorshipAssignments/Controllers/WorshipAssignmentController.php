<?php

namespace App\WorshipAssignments\Controllers;

use App\Accounts\AccountSetting;
use App\Accounts\AccountSettingValue;
use App\Base\Http\Controllers\Controller;
use App\WorshipAssignments\Group;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;

class WorshipAssignmentController extends Controller
{
    public function index()
    {
        Paginator::useTailwind();

        return view('admin.assignments.index')->with([
            'groups' => Group::visibleTo(Auth::user())
                ->orderBy('created_at', 'DESC')
                ->paginate(15),
        ]);
    }

    public function settings()
    {
        return view('admin.assignments.settings');
    }

    public function saveSettings()
    {
        $setting = null;
        $setting = AccountSettingValue::firstOrCreate([
            'account_id'         => auth()->user()->account->id,
            'account_setting_id' => AccountSetting::where('key', 'wa.enable_reminders')->first()?->id,
        ]);

        $setting->value = request('enable_reminders');
        $setting->save();

        $setting = null;
        $setting = AccountSettingValue::firstOrCreate([
            'account_id'         => auth()->user()->account->id,
            'account_setting_id' => AccountSetting::where('key', 'wa.send_mobile_notifications')->first()?->id,
        ]);

        $setting->value = request('send_mobile_notifications');
        $setting->save();

        $setting = null;
        $setting = AccountSettingValue::firstOrCreate([
            'account_id'         => auth()->user()->account->id,
            'account_setting_id' => AccountSetting::where('key', 'wa.send_email_notifications')->first()?->id,
        ]);

        $setting->value = request('send_email_notifications');
        $setting->save();

        $setting = null;
        $setting = AccountSettingValue::firstOrCreate([
            'account_id'         => auth()->user()->account->id,
            'account_setting_id' => AccountSetting::where('key', 'wa.send_days_before')->first()?->id,
        ]);

        $setting->value = request('send_days_before');
        $setting->save();

        $setting = null;
        $setting = AccountSettingValue::firstOrCreate([
            'account_id'         => auth()->user()->account->id,
            'account_setting_id' => AccountSetting::where('key', 'wa.hour_to_send')->first()?->id,
        ]);

        $setting->value = request('hour_to_send');
        $setting->save();

        return back()->with('message.success', 'Worship Assignment settings updated successfully.');
    }
}
