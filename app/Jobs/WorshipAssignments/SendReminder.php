<?php

namespace App\Jobs\WorshipAssignments;

use App\Jobs\SendMobileNotification;
use App\WorshipAssignments\Pick;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendReminder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $pick;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Pick $pick)
    {
        $this->pick = $pick;

        $this->onQueue('mobile');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Only send when in production
        if (config('app.env') == 'production') {
            // Send a mobile notification
            SendMobileNotification::dispatch(
                $this->pick->user,
                'Assignment Reminder 📋',
                'Don\'t forget about your upcoming assignment: ' . $this->pick->position->name,
                ['type' => 'WA_new']
            )->onQueue('mobile');
        } else {
            Log::notice('MOBILE NOTIFICATION ATTEMPT -- but we are not in production.');
        }
    }
}
