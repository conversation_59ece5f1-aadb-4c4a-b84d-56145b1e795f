<?php

namespace App\Jobs;

use App\Users\NotificationTarget;
use App\Users\Services\CreateUserNotification;
use App\Users\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Exception\Messaging\InvalidMessage;
use Kreait\Firebase\Exception\Messaging\NotFound;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class SendMobileNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    protected $title;
    protected $body        = '';
    protected $user;
    protected $data        = [];
    protected $badge_count = 0;

    public function __construct(User $user, $title, $body = '', $data = [], $badge_count = 0)
    {
        // In case we have some data but no type.
        $data = array_merge(['type' => 'general'], $data);

        $this->user        = $user;
        $this->title       = $title;
        $this->body        = $body;
        $this->data        = $data;
        $this->badge_count = $badge_count;
    }

    public function handle()
    {
        // Only send to users who are members of an account.
        if (!$this->user->isMember()) {
            Log::notice('SendMobileNotification::handle -- Attempt to send a notification to a user who is not a member.', [
                'user_id' => $this->user->id,
            ]);
        } else {
            $this->sendWithFirebase($this->getDeviceTokens());
            $this->sendWithExpo($this->getExpoDeviceTokens());
        }
    }

    public function getDeviceTokens()
    {
        // Get all devices that have opted to get notifications.
        return $this->user->devices()
            // Not sure why we had this here. Created and refreshed at are OFTEN the same, especially for new users.
            // ->where('last_refreshed_at', '<>', DB::raw('`created_at`'))
            ->where('receives_notifications', 1)
            ->where('device_token', 'not like', 'ExponentPushToken%') // Do not include Expo tokens for Firebase.
            ->get();
    }

    public function getExpoDeviceTokens()
    {
        // Get all devices that have opted to get notifications.
        return $this->user->devices()
            ->where('receives_notifications', 1)
            ->where('device_token', 'like', 'ExponentPushToken%') // Only include Expo tokens for sending through Expo.
            ->get();
    }

    public function sendWithExpo($device_tokens)
    {
        // If we have devices.
        if (count($device_tokens) > 0) {
            $token_array = [];
            // Put all device tokens for this user in an array.
            // https://docs.expo.dev/push-notifications/sending-notifications/#push-tickets
            foreach ($device_tokens as $device) {
                $token_array[] = $device->device_token;
            }

            Log::info('SendMobileNotification::sendWithExpo -- Attempt: Sent mobile notification to user.', [
                'user_id' => $this->user->id,
                'tokens'  => $token_array,
            ]);

            try {
                $client = new \GuzzleHttp\Client();

                $response = $client->request('POST', 'https://exp.host/--/api/v2/push/send?useFcmV1=true', [
                    'headers' => [
                        'host'            => 'exp.host',
                        'accept'          => 'application/json',
                        'accept-encoding' => 'gzip, deflate',
                        'content-type'    => 'application/json',
                        'Authorization'   => 'Bearer ' . config('services.expo.access_token'),
                    ],
                    'body'    => json_encode([
                        'to'    => $token_array,
                        'title' => substr($this->title, 0, 160),
                        'body'  => substr($this->body, 0, 200),
                        'data'  => $this->data,
                        'badge' => $this->badge_count,
                        'sound' => 'default',
                    ]),
                ]);

                // If our request failed completely.
                if ((int)$response->getStatusCode() != 200) {
                    Log::error('SendMobileNotification::sendWithExpo Failed', [
                        'body' => $response->getBody(),
                    ]);
                }

                // Create our mobile notification record.
                $user_notification = (new CreateUserNotification())
                    ->forUser($this->user)
                    ->withMessage($this->title . ' - ' . $this->body)
                    ->withData($this->data)
                    ->create();

                if ($response->getStatusCode() != 200) {
                    Log::error('SendMobileNotification::sendWithExpo - Expo Request Failed', [
                        'body' => $response->getBody(),
                    ]);
                }

                // If our request was successful, check to see if any specific notifications failed.
                foreach (json_decode($response->getBody())->data as $index => $message_response) {
                    // Expo returns a transaction id for each notification in the order they were sent from the $device_tokens array.
                    $target = NotificationTarget::create([
                        'user_notification_id'    => $user_notification->id,
                        'user_device_id'          => $device_tokens[$index]?->id,
                        'provider_transaction_id' => property_exists($message_response, 'id') ? $message_response->id : null,
                        'is_ok'                   => property_exists($message_response, 'status') && $message_response->status == 'ok',
                    ]);

                    // Check for errors sending
                    if ($message_response?->status != 'ok') {
                        Log::error('SendMobileNotification::sendWithExpo - Specific notification failed', [
                            'user_notification_id' => $user_notification->id,
                            'user_device_id'       => $device_tokens[$index]?->id,
                            'message'              => $message_response?->message,
                            'details'              => $message_response?->details,
                        ]);

                        $target->error_message = $message_response?->message;
                        $target->save();

                        // Disable this device if error is "DeviceNotRegistered". This likely means the user has uninstalled the app or disabled notifications.
                        if (property_exists($message_response, 'details') && $message_response?->details?->error == 'DeviceNotRegistered') {
                            $device = $device_tokens[$index];

                            // If we have a device with this specific error returned, disable it and set the deleted reason.
                            if ($device) {
                                $device->receives_notifications = 0;
                                $device->deleted_reason         = 'DeviceNotRegistered';
                                $device->save();

                                $device->delete();
                            }
                        }
                    }
                }

                Log::info('SendMobileNotification::sendWithExpo -- Complete: Sent mobile notification to user.', [
                    'user_id'           => $this->user->id,
                    'tokens'            => $token_array,
                    'provider_response' => $response->getBody(),
                ]);
            } catch (\Exception $e) {
                Log::error('SendMobileNotification::sendWithExpo Failed', [
                    'error'   => $e,
                    'details' => $e->getMessage(),
                ]);
            }
        } else {
            Log::info('SendMobileNotification::sendWithExpo -- User has no devices to send to.', [
                'user_id' => $this->user->id,
            ]);
        }
    }

    public function sendWithFirebase($device_tokens)
    {
        $messaging = app('firebase.messaging');

        // Array of copies of this message to send to all devices for a user.
        $messages = [];

        // If we have devices.
        if (count($device_tokens) > 0) {
            (new CreateUserNotification())
                ->forUser($this->user)
                ->withMessage($this->title . ' - ' . $this->body)
                ->withData($this->data)
//                ->withUserDeviceId($device?->id)
                ->create();

            // Create a message for each device and put it on our array.
            foreach ($device_tokens as $device) {
                // Notification content (because messages could be a data only message, not a notification.
                $notification = Notification::create($this->title, $this->body);
                // Message
                $messages[] = CloudMessage::withTarget('token', $device->device_token)
                    ->withNotification($notification)
                    ->withData($this->data)
                    ->withDefaultSounds()
                    ->withAndroidConfig(
                        AndroidConfig::new()
                            ->withHighMessagePriority()
                            ->withDefaultSound()
                    )
                    ->withApnsConfig(
                        ApnsConfig::new()
                            ->withBadge($this->badge_count)
                            ->withDefaultSound()
                    );

                try {
                    $temp_messages = array_values($messages);
                    $messaging->validate(end($temp_messages));
                } catch (InvalidMessage $e) {
                    Log::error('Firebase Message: Invalid', [
                        'error'   => $e,
                        'details' => $e->getMessage(),
                    ]);
                } catch (NotFound $e) {
                    $device->receives_notifications = 0;
                    $device->save();

                    $device->delete();

                    Log::error('Firebase Message: Not Found', [
                        'error'   => $e,
                        'details' => $e->getMessage(),
                    ]);
                } catch (\Exception $e) {
                    Log::error('Firebase Message: General Exception', [
                        'error'   => $e,
                        'details' => $e->getMessage(),
                    ]);
                }
            }

            try {
                $messaging->sendAll($messages);

                Log::info('SendMobileNotification::sendWithFirebase -- Sent mobile notification to user.', [$this->user->id]);
            } catch (\Exception $e) {
                Log::error('SendMobileNotification::sendWithFirebase Failed', [
                    'error'   => $e,
                    'details' => $e->getMessage(),
                ]);
            }
        } else {
            Log::info('SendMobileNotification::sendWithFirebase -- User has no devices to send to.', [
                'user_id' => $this->user->id,
            ]);
        }
    }

    public function rawSendWithFirebase($device_tokens)
    {
        $messaging = app('firebase.messaging');

        // If we have devices.
        if (count($device_tokens) > 0) {
            // Create a message for each device and put it on our array.
            foreach ($device_tokens as $device) {
                $message = CloudMessage::fromArray([
                    'token'        => $device->device_token,
                    'notification' => [
                        'title' => $this->title,
                        'body'  => $this->body,
                    ],
                    'data'         => $this->data,
                    'android'      => [
                        'priority'     => 'high',
                        'notification' => [
                            'default_vibrate_timings' => true,
                            'default_sound'           => true,
                            'notification_count'      => 0,
                            'notification_priority'   => 'PRIORITY_HIGH', // PRIORITY_LOW , PRIORITY_DEFAULT , PRIORITY_HIGH , PRIORITY_MAX
                        ],
                    ],
                    'apns'         => [
                        'headers' => [
                            'apns-priority' => '10',
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $this->title,
                                    'body'  => $this->body,
                                ],
                                'sound' => 'default',
                                'badge' => 0,
                            ],
                        ],
                    ],
                ]);

                try {
                    $messaging->send($message);
                } catch (InvalidMessage $e) {
                    Log::error($e);
                    Log::error('Firebase Message: Invalid', [
                        'error'   => $e,
                        'details' => $e->getMessage(),
                    ]);
                } catch (\Exception $e) {
                    Log::error($e);
                    Log::error('Firebase Message: General Exception', [
                        'error'   => $e,
                        'details' => $e->getMessage(),
                    ]);
                }
            }
        } else {
            Log::info('SendMobileNotification::sendWithFirebase -- User ' . $this->user->id . ' has no devices to send to.');
        }
    }
}
