<?php

namespace App\Jobs\Crises;

use App\Crises\Checkin;
use App\Jobs\SendMobileNotification;
use App\Mail\Crises\NewCheckinHelpRequest;
use App\Users\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ProcessHelpRequestCheckin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $crisis;
    protected $checkin;
    protected $account;
    protected $users_to_notify;
    public    $tries = 1;

    public function __construct(Checkin $checkin)
    {
        $this->crisis  = $checkin->crisis;
        $this->checkin = $checkin;
    }

    public function handle()
    {
        /**
         * 1. Get all our recipients (notify_groups from Crisis Settings)
         * 2. Create an EMAIL queue item for each user
         * 3. Create a MOBILE NOTIFICATION queue item for each user
         */
        Log::info('ProcessHelpRequestCheckin -- Beginning notifications process for Crisis ID: ' . $this->crisis->id . '...');

        $this->account = $this->crisis->account;

        // Get our NotifyGroups from CrisisSettings
        $group_ids = [];
        if ($this->checkin->isHelpRequest()) {
            $group_ids = $this->account->crisisSettings->help_notification_groups;
        } elseif ($this->checkin->isUrgentHelpRequest()) {
            $group_ids = $this->account->crisisSettings->urgent_help_notification_groups;
        }

        if (empty($group_ids)) {
            Log::error('Help request received, but there are no notification groups/users! Checkin: ' . $this->checkin->id);
        }

        // Get the users that belong to these groups.
        $this->users_to_notify = User::where('account_id', $this->account->id)
            ->membersOnly()
            ->whereHas('groups', function ($query) use ($group_ids) {
                $query->whereIn('user_groups.id', $group_ids);
            })
            ->select([
                'id',
                'account_id',
                'first_name',
                'last_name',
                'is_active',
            ])
            ->get();

        // Send our emails.
        if ($this->account->crisisSettings->send_via_email) {
            $this->queueEmails();
        }

        // Send our mobile notifications.
        if ($this->account->crisisSettings->send_via_mobile_notification) {
            $this->queueMobileNotifications();
        }

        if (!$this->account->crisisSettings->send_via_email && !$this->account->crisisSettings->send_via_mobile_notification) {
            Log::error('Help request received, but both email and mobile options are disabled! Checkin: ' . $this->checkin->id);
        }
    }

    public function queueMobileNotifications()
    {
        $title = 'Check-in Help Request!';
        if ($this->checkin->isHelpRequest()) {
            $title = '❣️ Check-in Help Request';
        } else {
            if ($this->checkin->isUrgentHelpRequest()) {
                $title = '‼️ URGENT Check-in Help Request!';
            }
        }

        foreach ($this->users_to_notify as $user) {
            SendMobileNotification::dispatch(
                $user,
                $title,
                'A new help request has arrived from the family of: ' . $this->checkin->family->name,
                [
                    'type'              => 'Cr_help',
                    'crisis_checkin_id' => $this->checkin->id,
                ]
            )
                ->onQueue('mobile');
        }
    }

    public function queueEmails()
    {
        // Get all the emails for these users.
        $emails = [];
        foreach ($this->users_to_notify as $user) {
            if ($email = $user->getPrimaryEmail()) {
                $emails[] = [
                    'user'  => $user,
                    'email' => $email,
                ];
            } else {
                Log::info('ProcessHelpRequestCheckin: User # ' . $user->id . ' does not have a getPrimaryEmail.');
            }

            $email = null;
        }

        // Don't send multiple messages to the same address.
        $emails = collect($emails)->unique('email.email');

        // Keep track of domains we're sending to, and add a delay based on how many times we've sent to this domain in this burst.
        // This keeps us from hitting sending limits based on sending too many concurrent emails.
        $sending_domains = [];

        foreach ($emails as $user_email_array) {
            // Get the domain for this email.
            $domain = $this->getEmailDomain(optional($user_email_array['email'])->email);

            // Increment our delay for this domain, or set to zero if we have not encountered this domain yet.
            $sending_domains[$domain] = Arr::get($sending_domains, $domain) === null ? 0 : Arr::get($sending_domains, $domain) + 2;

            // Only use a delay if there's an actual delay.
            if (Arr::get($sending_domains, $domain) > 0) {
                Mail::to(optional($user_email_array['email'])->email)
                    ->later(now()->addSeconds((int)round(Arr::get($sending_domains, $domain))), new NewCheckinHelpRequest($this->checkin, $user_email_array['user']));
            } else {
                Mail::to(optional($user_email_array['email'])->email)
                    ->queue(new NewCheckinHelpRequest($this->checkin, $user_email_array['user']));
            }
        }

        Log::info('ProcessHelpRequestCheckin -- Finished queuing user emails.');
    }

    public function getEmailDomain($address)
    {
        return trim(Arr::last(explode('@', $address)));
    }
}
