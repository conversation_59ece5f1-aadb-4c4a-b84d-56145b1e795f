<?php

namespace App\Jobs\Crises;

use App\Crises\Crisis;
use App\Jobs\SendMobileNotification;
use App\Mail\Crises\NewCrisisCheckin;
use App\Users\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ProcessNewCrisisNotifications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $crisis;
    protected $account;
    protected $users_to_notify;
    public    $tries = 1;

    public function __construct(Crisis $crisis)
    {
        $this->crisis = $crisis;
    }

    public function handle()
    {
        /**
         * 1. Get all our recipients (notify_groups from Crisis Settings)
         * 2. Create an EMAIL queue item for each user
         * 3. Create a MOBILE NOTIFICATION queue item for each user
         */
        Log::info('ProcessNewCrisisNotifications -- Beginning notifications process for Crisis ID: ' . $this->crisis->id . '...');

        $this->account = $this->crisis->account;

        // Get our NotifyGroups from CrisisSettings
        $group_ids = $this->account->crisisSettings->notify_groups;

        // Get the users that belong to these groups.
        $this->users_to_notify = User::where('account_id', $this->account->id)
            ->whereHas('groups', function ($query) use ($group_ids) {
                $query->whereIn('user_groups.id', $group_ids);
            })
            ->select([
                'id',
                'account_id',
                'first_name',
                'last_name',
                'is_active',
            ])
            ->get();

        // Send our emails.
        $this->queueEmails();

        // Send our mobile notifications.
        $this->queueMobileNotifications();
    }

    public function queueMobileNotifications()
    {
        foreach ($this->users_to_notify as $user) {
            SendMobileNotification::dispatch($user, '⚠️ Family Check-in ', 'Let us know your status! Your church family is eager to help if you are in need.', ['type' => 'Cr_new'])
                ->onQueue('mobile');
        }
    }

    public function queueEmails()
    {
        // Get all the emails for these users.
        $emails = [];
        foreach ($this->users_to_notify as $user) {
            if ($email = $user->getPrimaryEmail()) {
                $emails[] = [
                    'user'  => $user,
                    'email' => $email,
                ];
            } else {
                Log::info('ProcessNewCrisisNotifications: User # ' . $user->id . ' does not have a getPrimaryEmail.');
            }

            $email = null;
        }

        // Don't send multiple messages to the same address.
        $emails = collect($emails)->unique('email.email');

        // Keep track of domains we're sending to, and add a delay based on how many times we've sent to this domain in this burst.
        // This keeps us from hitting sending limits based on sending too many concurrent emails.
        $sending_domains = [];

        foreach ($emails as $user_email_array) {
            // Get the domain for this email.
            $domain = $this->getEmailDomain(optional($user_email_array['email'])->email);

            // Increment our delay for this domain, or set to zero if we have not encountered this domain yet.
            $sending_domains[$domain] = Arr::get($sending_domains, $domain) === null ? 0 : Arr::get($sending_domains, $domain) + 2;

            // Only use a delay if there's an actual delay.
            if (Arr::get($sending_domains, $domain) > 0) {
                Mail::to(optional($user_email_array['email'])->email)
                    ->later(now()->addSeconds((int)round(Arr::get($sending_domains, $domain))), new NewCrisisCheckin($this->crisis, $user_email_array['user']));
            } else {
                Mail::to(optional($user_email_array['email'])->email)
                    ->queue(new NewCrisisCheckin($this->crisis, $user_email_array['user']));
            }
        }

        Log::info('ProcessNewCrisisNotifications -- Finished queuing user emails.');
    }

    public function getEmailDomain($address)
    {
        return trim(Arr::last(explode('@', $address)));
    }
}
