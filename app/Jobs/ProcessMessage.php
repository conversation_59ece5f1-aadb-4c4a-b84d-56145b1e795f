<?php

namespace App\Jobs;

use App\Messages\Message;
use App\Messages\MessageHistory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class ProcessMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $message;
    public    $tries = 1;

    public function __construct(Message $message)
    {
        $this->message = $message;
    }

    public function handle()
    {
        /**
         * 1. Get all our recipients
         * 2. Create a message_history item for them all
         * 3. Create a queue item for each message_history item just created
         * 4. Mark the message as sent
         */
        Log::info('Beginning user search for sending messages...');

        $this->message->status = 'sending';
        $this->message->save();

        $emails = [];
        foreach ($this->message->group->users()->with('emails')->get() as $user) {
            if ($user->getGroupWithSettings($this->message->group->id)->settings->receive_group_emails) {
                foreach ($user->getEmailsForMessaging() as $known_email) {
                    $emails[]    = ['user' => $user, 'email' => $known_email];
                    $known_email = null;
                }

                if ($user->getEmailsForMessaging()->count() == 0) {
                    Log::info('ProcessMessage: User # ' . $user->id . ' does not have any EmailsForMessaging, but is included in a group that is receiving messages.');
                }
            } else {
                Log::info('ProcessMessage: User # ' . $user->id . ' has the flag receive_group_emails turned off. Group email Message # ' . $this->message->id . ' will not be send.');
            }

            $known_email = null;
        }

        // Don't send multiple messages to the same address.
        $emails = collect($emails)->unique('email');

        $charge = optional($this->message->account->plan)->price_per_email;

        // Keep track of domains we're sending to, and add a delay based on how many times we've sent to this domain in this burst.
        // This keeps us from hitting sending limits based on sending too many concurrent emails.
        $sending_domains = [];

        foreach ($emails as $email) {
            # Don't send this message to the sender.
//            if (optional($email['user'])->id == $this->message->message_sender_id) {
//                continue;
//            }

            // Get the domain for this email.
            $domain = $this->getEmailDomain(optional($email['email'])->email);
            // Increment our delay for this domain, or set to zero if we have not encountered this domain yet.
            $sending_domains[$domain] = Arr::get($sending_domains, $domain) === null ? 0 : Arr::get($sending_domains, $domain) + 2;

            $mh_result = false;
            $mh_result = MessageHistory::create([
                'account_id'          => $this->message->account_id,
                'user_id'             => optional($email['user'])->id,
                'user_email_id'       => optional($email['email'])->id,
                'message_id'          => $this->message->id,
                'message_status_id'   => null,
                'provider_batch_id'   => null,
                'provider_message_id' => null,
                'charge'              => $charge,
            ]);

            $mh_result->setStatusCreateOnMissing('queued'); // This method saves the message_history.

            if ($mh_result) {
                // Only use a delay if there's an actual delay.
                if (Arr::get($sending_domains, $domain) > 0) {
                    SendMessage::dispatch($mh_result)->delay(now()->addSeconds((int)round(Arr::get($sending_domains, $domain))));
                } else {
                    SendMessage::dispatch($mh_result);
                }
            }
        }

        $this->message->status = 'complete';
        $this->message->save();

        Log::info('Finished queuing user emails.');
    }

    public function getEmailDomain($address)
    {
        return trim(Arr::last(explode('@', $address)));
    }
}
