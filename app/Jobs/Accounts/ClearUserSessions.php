<?php

namespace App\Jobs\Accounts;

use App\Accounts\Services\DeleteUserSessions;
use App\Users\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ClearUserSessions implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user = null;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('ClearUserSessions::Start - User # ' . $this->user->id);

        (new DeleteUserSessions())
            ->forAccount($this->user->account)
            ->forUsers([$this->user->id])
            ->forMobile()
            ->forWeb()
            ->delete();

        Log::info('ClearUserSessions::Completed - User # ' . $this->user->id);
    }
}
