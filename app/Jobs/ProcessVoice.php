<?php

namespace App\Jobs;

use App\Messages\Message;
use App\Messages\MessageHistory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessVoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $message;
    public    $tries = 1;

    public function __construct(Message $message)
    {
        $this->message = $message;
    }

    public function handle()
    {
        /**
         * 1. Get all our recipients
         * 2. Create a message_history item for them all
         * 3. Create a queue item for each message_history item just created
         * 4. Mark the message as sent
         */
        Log::info('Beginning user search for sending voice message...');

        $this->message->status = 'sending';
        $this->message->save();

        $phones = [];

        $users = $this->message->group->users()
            ->with('phones')
//            ->where('users.id', '!=', $this->message->created_by_user_id)
            ->get();

        Log::info('Found ' . $users->count() . ' users possible to send this message to.');

        foreach ($users as $user) {
            if (!$user->getBestPhone()) {
                Log::info('User # ' . $user->id . ' does not have a BestPhone, but is included in a group that is receiving voice messages.');
            } elseif (optional($user->getBestPhone())->messages_opt_out) {
                Log::info('User # ' . $user->id . ' has opted-out of voice, but is included in a group that is receiving voice messages.', [
                    'user_id'       => $user->id,
                    'user_phone_id' => optional($user->getBestPhone())->id,
                    'message_id'    => $this->message->id,
                ]);
            } else {
                Log::info('Added user # ' . $user->id . ' to list to be messaged.');

                $phones[] = ['user' => $user, 'phone' => $user->getBestPhone()];
            }

            $known_phone = null;
        }

        $phones = collect($phones)->unique('phone.number');

        Log::info('ProcessVoice -- Found ' . count($phones) . ' phone numbers to send a message to.');

        $charge = optional($this->message->account->plan)->price_per_voice;

        foreach ($phones as $phone) {
            $mh_result = false;
            $mh_result = MessageHistory::create([
                'account_id'          => $this->message->account_id,
                'user_id'             => optional($phone['user'])->id,
                'user_phone_id'       => optional($phone['phone'])->id,
                'message_id'          => $this->message->id,
                'message_status_id'   => null,
                'provider_batch_id'   => null,
                'provider_message_id' => null,
                'charge'              => $charge,
            ]);

            $mh_result->setStatusCreateOnMissing('sent'); // This method saves the message_history.

            if ($mh_result) {
                SendVoice::dispatch($mh_result);
            }
        }

        $this->message->status = 'complete';
        $this->message->save();

        Log::info('Finished queuing user phone numbers for voice message.');
    }
}
