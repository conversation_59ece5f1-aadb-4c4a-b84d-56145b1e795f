<?php

namespace App\Jobs\Visitors;

use App\Jobs\SendMobileNotification;
use App\Mail\App\Visitors\NewHistoryEmail;
use App\Mail\Crises\NewCrisisCheckin;
use App\Visitors\History;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ProcessNewHistoryEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $history;
    protected $users_to_notify;
    public    $tries = 1;

    public function __construct(History $history)
    {
        $this->history = $history;
    }

    public function handle()
    {
        /**
         * 1. Get all our recipients
         * 2. Create an EMAIL queue item for each user
         * 3. Create a MOBILE NOTIFICATION queue item for each user
         */
        Log::info('ProcessNewHistoryEvent -- Beginning notifications process for Visitor History ID: ' . $this->history->id . '...');

        $this->users_to_notify = $this->history->visitor
            ->subscribers()
            ->where('user_id', '<>', $this->history->created_by_user_id)
            ->get();

        // Send our emails.
        $this->queueEmails();

        // Send our mobile notifications.
        // $this->queueMobileNotifications();
    }

    public function queueMobileNotifications()
    {
        foreach ($this->users_to_notify as $user) {
            SendMobileNotification::dispatch($user, 'New Visitor Activity', 'New activity for ' . $this->visitor->mainUser()->name, ['type' => 'V_history'])
                ->onQueue('mobile');
        }
    }

    public function queueEmails()
    {
        // Get all the emails for these users.
        $emails = [];
        foreach ($this->users_to_notify as $user) {
            if ($email = $user->getPrimaryEmail()) {
                $emails[] = [
                    'user'  => $user,
                    'email' => $email,
                ];
            } else {
                Log::info('ProcessNewHistoryEvent: User # ' . $user->id . ' does not have a getPrimaryEmail.');
            }

            $email = null;
        }

        // Don't send multiple messages to the same address.
        $emails = collect($emails)->unique('email.email');

        // Keep track of domains we're sending to, and add a delay based on how many times we've sent to this domain in this burst.
        // This keeps us from hitting sending limits based on sending too many concurrent emails.
        $sending_domains = [];

        foreach ($emails as $user_email_array) {
            // Get the domain for this email.
            $domain = $this->getEmailDomain(optional($user_email_array['email'])->email);

            // Increment our delay for this domain, or set to zero if we have not encountered this domain yet.
            $sending_domains[$domain] = Arr::get($sending_domains, $domain) === null ? 0 : Arr::get($sending_domains, $domain) + 2;

            // Only use a delay if there's an actual delay.
            if (Arr::get($sending_domains, $domain) > 0) {
                Mail::to(optional($user_email_array['email'])->email)
                    ->later(now()->addSeconds((int)round(Arr::get($sending_domains, $domain))), new NewCrisisCheckin($this->history, $user_email_array['user']));
            } else {
                Mail::to(optional($user_email_array['email'])->email)
                    ->queue(new NewHistoryEmail($this->history, $user_email_array['user']));
            }
        }

        Log::info('ProcessNewHistoryEvent -- Finished queuing user emails.');
    }

    public function getEmailDomain($address)
    {
        return trim(Arr::last(explode('@', $address)));
    }
}
