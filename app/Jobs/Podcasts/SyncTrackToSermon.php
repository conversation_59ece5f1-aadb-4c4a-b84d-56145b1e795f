<?php

namespace App\Jobs\Podcasts;

use App\Podcasts\Services\SyncTrackToSermon as ReSyncTrackToSermon;
use App\Podcasts\Track;
use App\Sermons\Sermon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncTrackToSermon implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $track;
    protected $sermon;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Track $track, Sermon $sermon)
    {
        $this->track  = $track;
        $this->sermon = $sermon;

        $this->onQueue('podcasts');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $result = (new ReSyncTrackToSermon())
            ->fromSermon($this->sermon)
            ->forTrack($this->track)
            ->sync();

        Log::info('Processed Sermon # ' . $this->sermon->id . ' to Track # ' . $this->track->id . ' with the result: ' . $result);
    }
}
