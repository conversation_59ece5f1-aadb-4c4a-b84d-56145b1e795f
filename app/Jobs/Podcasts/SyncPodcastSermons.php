<?php

namespace App\Jobs\Podcasts;

use App\Podcasts\Podcast;
use App\Sermons\Sermon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncPodcastSermons implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->onQueue('podcasts');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        /**
         * 1. Get all podcasts with Sermon tags
         * 2. Create a message_history item for them all
         * 3. Create a queue item for each message_history item just created
         * 4. Mark the message as sent
         */
        Log::info('Beginning search for Podcasts with a sermon tag attached...');

        $podcasts = Podcast::where('is_active', true)
            ->get();

        foreach ($podcasts as $podcast) {

            $sermons = Sermon::whereHas('tags', function ($query) use ($podcast) {
                $query->where('sermon_tags.id', $podcast->sermon_tag_id);
            })
                ->where('is_public', true)
                ->get();

            $existing_tracks = $podcast->tracks()
                ->whereNotNull('sermon_id') // The ->except function freaks out if we have null values for sermon_id
                ->get()
                ->pluck('sermon_id')
                ->toArray();

            $not_added_sermons = $sermons->except($existing_tracks);

            Log::info('Found ' . $not_added_sermons->count() . ' sermons that have not been added to Podcast: ' . $podcast->id . ' -- ' . $podcast->title);

            // Add these to our podcast.
            foreach ($not_added_sermons as $sermon) {
                CreateTrackFromSermon::dispatch($podcast, $sermon);

                Log::info('Added to the queue Sermon # ' . $sermon->id . ' to Podcast # ' . $podcast->id);
            }
        }

        Log::info('Finished :: search for Podcasts with a sermon tag attached.');
    }
}
