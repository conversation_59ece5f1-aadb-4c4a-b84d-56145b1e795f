<?php

namespace App\Jobs\Podcasts;

use App\Podcasts\Podcast;
use App\Podcasts\Services\CreateTrackFromSermon as CreateTrackFromSermonService;
use App\Sermons\Sermon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateTrackFromSermon implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $podcast;
    protected $sermon;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Podcast $podcast, Sermon $sermon)
    {
        $this->podcast = $podcast;
        $this->sermon  = $sermon;

        $this->onQueue('podcasts');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $result = (new CreateTrackFromSermonService())
            ->fromSermon($this->sermon)
            ->forPodast($this->podcast)
            ->create();

        Log::info('Processed Sermon # ' . $this->sermon->id . ' to Podcast # ' . $this->podcast->id . ' with the result: ' . $result);
    }
}
