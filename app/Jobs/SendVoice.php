<?php

namespace App\Jobs;

use App\Messages\MessageHistory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Twilio\Rest\Client;

class SendVoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    protected $message_history;
    protected $provider;

    public function __construct(MessageHistory $message_history)
    {
        $this->message_history = $message_history;
    }

    public function handle()
    {
        $this->provider = $this->message_history->message->provider;

        if ($this->provider->provider_code == 'twilio') {
            return $this->sendWithTwilio();
        } else {
            Log::error('SendVoice:handle -- no handler for the provider: ' . $this->provider->provider_code);
            return false;
        }
    }

    public function sendWithTwilio()
    {
        Log::info('SendVoice::sendWithTwilio()');

        $twilio = new Client($this->provider->api_key, $this->provider->api_secret);

        // Force USA prefix if prefix is not 1
        $to_number = mb_substr($this->message_history->userPhone->number, 0, 1) == '1' ? $this->message_history->userPhone->number : '1' . $this->message_history->userPhone->number;

        $voice = $this->message_history->message->handler->voice_call_voice == 'male' ? 'Polly.Matthew' : 'Polly.Joanna';

        $call = $twilio->calls
            ->create(
                "+" . $to_number,                                        // to
                "+" . $this->message_history->message->handler->address, // from
                [
                    "twiml" => '<Response><Pause length="2"/><Say voice="' . $voice . '">' . $this->message_history->message->content . '</Say><Pause length="1"/></Response>',
                ]
            );

        $response_id = $call->sid;

        $this->message_history->provider_message_id = $response_id;

        $this->message_history->setStatusCreateOnMissing('sent');

        return true;
    }
}
