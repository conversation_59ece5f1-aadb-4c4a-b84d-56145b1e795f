<?php

namespace App\AccountFiles\Services;

use App\AccountFiles\AccountFile;
use App\Accounts\Account;
use App\Users\User;

class AccountFileSorter
{
    protected $user                   = null;
    protected $account_id             = null;
    protected $account_file           = null;
    protected $new_sort_id            = null;
    protected $account_file_parent_id = null;

    public function sort()
    {
        if (!$this->user && !$this->account_id) {
            throw new \Exception('User OR account must be set before sorting.');
        }

        $new_sort_id = $this->new_sort_id;

        // Start from our old sort ID
        $files = AccountFile::IsNotExpired()
            ->when($this->user, function ($query) {
                $query->visibleTo($this->user);
            }, function ($query) {
                $query->where('account_id', $this->account_id);
            })
            ->when(!$this->account_file_parent_id, function ($query) {
                return $query->whereNull('account_file_parent_id');
            }, function ($query) {
                return $query->where('account_file_parent_id', $this->account_file_parent_id);
            })
            ->orderBy('sort_id', 'asc')
            ->get();

        $i = 0;
        foreach ($files as $current_file) {
            if ($current_file->id == $this->account_file->id) {
                $this->account_file->sort_id    = $new_sort_id;
                $this->account_file->timestamps = false; // Don't update our `updated_at` field
                $this->account_file->save();
            } else {
                // Skip over the new_sort_id
                if ($i == $new_sort_id) {
                    $i++;
                }
                $current_file->sort_id    = $i;
                $current_file->timestamps = false; // Don't update our `updated_at` field
                $current_file->save();
                $i++;
            }
        }

        return true;
    }

    public function forAccount(Account|int $account)
    {
        if ($account instanceof Account) {
            $this->account_id = $account->id;
        } else {
            $this->account_id = $account->id;
        }

        return $this;
    }

    public function byUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function inFolder($account_file_parent_id)
    {
        $this->account_file_parent_id = $account_file_parent_id;

        return $this;
    }

    public function forFile(AccountFile $account_file)
    {
        $this->account_file = $account_file;

        return $this;
    }

    public function newSortId($new_sort_id)
    {
        $this->new_sort_id = $new_sort_id;

        return $this;
    }
}
