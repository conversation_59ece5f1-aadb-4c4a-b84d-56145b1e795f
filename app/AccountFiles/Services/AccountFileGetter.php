<?php

namespace App\AccountFiles\Services;

use App\AccountFiles\AccountFile;
use App\Accounts\Account;
use App\Users\User;

class AccountFileGetter
{
    protected $for_user               = null;
    protected $for_account_id         = null;
    protected $exclude_folders        = false;
    protected $exclude_files          = false;
    protected $account_file_parent_id = null;
    protected $public_only            = false;

    public function get()
    {
        if (!$this->for_user && !$this->for_account_id) {
            throw new \Exception('You must specify a user or account to get files for.');
        }

        $files = AccountFile::when($this->for_user, function ($query) {
            $query->visibleTo($this->for_user)
                ->where(function ($query) {
                    $query->when($this->for_user->gender == 'male', function ($query) {
                        return $query->isMenOnly();
                    }, function ($query) {
                        return $query->isWomenOnly();
                    })->orWhere(function ($query) {
                        return $query->whereNull('is_men_only')
                            ->whereNull('is_women_only');
                    });
                });
        })
            ->when($this->for_account_id, function ($query) {
                $query->where('account_id', $this->for_account_id);
            })
            ->isNotHidden()
            ->isNotExpired()
            ->when($this->public_only, function ($query) {
                return $query->IsPublic();
            })
            ->when($this->account_file_parent_id, function ($query) {
                return $query->inFolder($this->account_file_parent_id);
            }, function ($query) {
                return $query->whereNull('account_file_parent_id');
            })
            ->when($this->exclude_folders, function ($query) {
                return $query->FilesOnly();
            })
            ->when($this->exclude_files, function ($query) {
                return $query->FoldersOnly();
            })
            ->orderBy('sort_id');

        return $files;
    }

    public function forUser(User $user)
    {
        $this->for_user = $user;

        return $this;
    }

    public function forAccount(Account|int $account)
    {
        if ($account instanceof Account) {
            $account = $account->id;
        }

        $this->for_account_id = $account;

        return $this;
    }

    public function publicOnly()
    {
        $this->public_only = true;

        return $this;
    }

    public function inFolder(AccountFile|int|null $account_file_parent_id)
    {
        if ($account_file_parent_id == null || $account_file_parent_id == 'null' || $account_file_parent_id == 'NaN') {
            return $this;
        }

        if ($account_file_parent_id instanceof AccountFile) {
            $account_file_parent_id = $account_file_parent_id->id;
        }

        $this->account_file_parent_id = $account_file_parent_id;

        return $this;
    }

    public function excludeFolders()
    {
        $this->exclude_folders = true;

        return $this;
    }

    public function excludeFiles()
    {
        $this->exclude_files = true;

        return $this;
    }
}
