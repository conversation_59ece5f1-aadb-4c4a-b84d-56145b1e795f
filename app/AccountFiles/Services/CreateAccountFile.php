<?php

namespace App\AccountFiles\Services;

use App\AccountFiles\AccountFile;
use App\Accounts\Account;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class CreateAccountFile
{
    protected $account                = null;
    protected $account_file           = null;
    protected $uploaded_file          = null;
    protected $file_title             = null;
    protected $file_type              = null; // "application/pdf"
    protected $type                   = null; // "PDF"
    protected $is_folder              = 0;
    protected $is_public              = null;
    protected $is_hidden              = null;
    protected $account_file_parent_id = null;
    protected $is_women_only          = null;
    protected $is_men_only            = null;
    protected $expires_at             = null;
    protected $folder_color           = null;

    public function create(): AccountFile
    {
        if ($this->is_folder) {
            $this->is_folder = 1;
        } else {
            $this->is_folder = 0;
        }

        // Hidden
        if ($this->is_hidden) {
            $this->is_hidden = $this->is_hidden ? now() : null;
        } else {
            $this->is_hidden = null;
        }
        // Public
        if ($this->is_public) {
            $this->is_public = $this->is_public ? now() : null;
        } else {
            $this->is_public = null;
        }

        $this->account_file = AccountFile::create([
            'account_id'             => $this->account->id,
            'starts_at'              => Carbon::now()->format('Y-m-d'),
            'expires_at'             => $this->expires_at ? Carbon::parse($this->expires_at)->format('Y-m-d 23:59:59') : null,
            'title'                  => $this->file_title,
            'url_title'              => Str::slug($this->file_title, '-'),
            'type'                   => $this->type,
            'is_folder'              => $this->is_folder,
            'is_public'              => $this->is_public ? now() : null,
            'is_hidden'              => $this->is_hidden ? now() : null,
            'account_file_parent_id' => $this->account_file_parent_id,
            'is_women_only'          => $this->is_women_only ? now() : null,
            'is_men_only'            => $this->is_men_only ? now() : null,
            'folder_color'           => $this->folder_color,
        ]);

        $this->saveFile();

        return $this->account_file;
    }

    public function forAccount(Account $account)
    {
        $this->account = $account;

        return $this;
    }

    public function expiresAt($expires_at)
    {
        $this->expires_at = $expires_at;

        return $this;
    }

    /**
     * @param UploadedFile $uploaded_file From request()->file() on the frontend
     * @param              $file_title
     *
     * @return CreateAccountFile
     * @throws \Exception
     */
    public function withFile(UploadedFile $uploaded_file, $file_title = null): CreateAccountFile
    {
        if ($uploaded_file->isValid()) {
            $this->uploaded_file = $uploaded_file;
            $this->file_title    = $file_title;
        } else {
            throw new \Exception('File was invalid.');
        }

        return $this;
    }

    public function withTitle($file_title): CreateAccountFile
    {
        $this->file_title = $file_title;

        return $this;
    }

    public function ofType($type): CreateAccountFile
    {
        $this->type = $type;

        return $this;
    }

    public function isFolder($is_folder = true): CreateAccountFile
    {
        $this->is_folder = $is_folder;

        return $this;
    }

    public function isMenOnly($only = true): CreateAccountFile
    {
        $this->is_men_only = $only;

        return $this;
    }

    public function isWomenOnly($only = true): CreateAccountFile
    {
        $this->is_women_only = $only;

        return $this;
    }

    public function folderColor($color): CreateAccountFile
    {
        $this->folder_color = $color;

        return $this;
    }

    public function inFolder($account_file_parent_id = null)
    {
        if ($account_file_parent_id) {
            $this->account_file_parent_id = $account_file_parent_id;
        }

        return $this;
    }

    public function isPublic($is_public = true)
    {
        $this->is_public = $is_public ? now() : null;

        return $this;
    }

    public function isHidden($is_hidden = true)
    {
        $this->is_hidden = $is_hidden ? now() : null;

        return $this;
    }

    private function saveFile()
    {
        if (!$this->uploaded_file) {
            return;
        }

        // $folder    = Config::get('app.user_image_file_path');
        $extension = '.' . $this->uploaded_file->getClientOriginalExtension();

        $new_file_name                   = $this->account->id . '--' . Str::random(6) . '--' . $this->uploaded_file->getClientOriginalName();
        $new_file_name_without_extension = str_replace($extension, '', $new_file_name);

        // Original -- saved as a JPG
        if (!$this->uploaded_file->storeAs($this->account->id, $new_file_name, 'account-files')) {
            throw new \Exception('Could not write file to cloud server. The database entry has been saved though.');
        }

        $save_title = ($this->file_title ?: $this->account_file->title) ?: $this->uploaded_file->getClientOriginalName();

        $this->account_file->fill([
            'account_id'         => $this->account->id,
            'title'              => $save_title,
            'url_title'          => \Illuminate\Support\Str::slug($save_title, '-'),
            'storage_service'    => 'do-spaces',
            'data_separator'     => '--',
            'file_original_name' => $this->uploaded_file->getClientOriginalName(),
            'file_size'          => $this->uploaded_file->getSize(),
            'file_folder'        => $this->account->id,
            'file_id'            => null,
            'file_name'          => $new_file_name_without_extension,
            'file_extension'     => $this->uploaded_file->getClientOriginalExtension(),
            'file_type'          => $this->uploaded_file->getClientMimeType(),
            'file_sha1'          => sha1(file_get_contents($this->uploaded_file->getRealPath())),
        ]);

        $this->account_file->save();
    }
}
