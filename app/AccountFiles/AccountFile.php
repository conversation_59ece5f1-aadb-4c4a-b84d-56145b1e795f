<?php

namespace App\AccountFiles;

use App\AccountFiles\Scopes\AccountFileVisibleToScope;
use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class AccountFile extends Model
{
    use SoftDeletes;

    protected $table = 'account_files';

    protected $casts = [
        'created_at'             => 'datetime',
        'updated_at'             => 'datetime',
        'deleted_at'             => 'datetime',
        'starts_at'              => 'datetime',
        'expires_at'             => 'datetime',
        'is_women_only'          => 'datetime',
        'is_men_only'            => 'datetime',
        'is_public'              => 'datetime',
        'is_hidden'              => 'datetime',
        'is_folder'              => 'boolean',
        'account_file_parent_id' => 'integer',
    ];

    protected $fillable = [
        'account_id',
        'starts_at',
        'expires_at',
        'title',
        'url_title',
        'type',
        // 'storage_service',
        'file_original_name',
        'file_size',
        'data_separator',
        'file_folder',
        'file_id',
        'file_name',
        'file_extension',
        'file_type',
        'file_sha1',
        'sort_id',
        'folder',
        'is_folder',
        'account_file_parent_id',
        'is_women_only',
        'is_men_only',
        'folder_color',
        'folder_icon',
        'is_public',
        'is_hidden',
    ];

    public static string $default_folder_color = '3B82F6';

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account|int $account_id)
    {
        if ($account_id instanceof Account) {
            $account_id = $account_id->id;
        }

        return $query->where(function ($query) use ($account_id) {
            $query->where($this->table . '.account_id', $account_id);
        });
    }

    public function getVisibleFilesQuery($user)
    {
        return $this->visibleTo($user)
            ->isNotExpired()
            ->isNotHidden()
            ->orderBy('sort_id');
    }

    public function scopeInFolder($query, AccountFile|int $folder)
    {
        if ($folder instanceof AccountFile) {
            $folder = $folder->id;
        }

        return $query->where('account_file_parent_id', $folder);
    }

    public function scopeFilesOnly($query)
    {
        return $query->where('is_folder', '<>', 1);
    }

    public function scopeFoldersOnly($query)
    {
        return $query->where('is_folder', 1);
    }

    public function scopeTopLevelOnly($query)
    {
        return $query->whereNull('account_file_parent_id');
    }

    public function scopeIsNotExpired($query)
    {
        return $query->where(function ($query2) {
            $query2->whereNull('expires_at')
                ->orWhere('expires_at', '>=', now()->format('Y-m-d'));
        });
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where(function ($query2) {
            $query2->whereNull('is_hidden');
        });
    }

    public function scopeIsPublic($query)
    {
        return $query->whereNotNull('is_public');
    }

    public function scopeIsMenOnly($query)
    {
        return $query->whereNotNull('is_men_only');
    }

    public function scopeIsWomenOnly($query)
    {
        return $query->whereNotNull('is_women_only');
    }

    public function parentFolder()
    {
        return $this->belongsTo(AccountFile::class, 'account_file_parent_id', 'id');
    }

    public function folders()
    {
        return $this->hasMany(AccountFile::class, 'account_file_parent_id', 'id')
            ->where('is_folder', 1);
    }

    public function files()
    {
        return $this->hasMany(AccountFile::class, 'account_file_parent_id', 'id')
            ->where('is_folder', '<>', 1);
    }

    public function getDirectoryLevelsDeep()
    {
        $levels = 0;
        $parent = $this->parentFolder;

        while ($parent) {
            $levels++;
            $parent = $parent->parentFolder;
        }

        return $levels;
    }

    public function getFolderSize($unit = 'auto', $get_unit_type = false, $decimals = 0)
    {
        if (!$this->is_folder) {
            return 0;
        }

        $size = $this->files->sum('file_size');

        foreach ($this->folders as $folder) {
            $size += $folder->getFolderSize();
        }

        return $this->getSizeFromBytes($size, $unit, $get_unit_type, $decimals);
    }

    public function getFileSize($unit = 'auto', $get_unit_type = false, $decimals = 0)
    {
        $size = $this->file_size;

        return $this->getSizeFromBytes($size, $unit, $get_unit_type, $decimals);
    }

    protected function getSizeFromBytes($size, $unit = 'auto', $get_unit_type = false, $decimals = 0)
    {
        if ($unit == 'auto') {
            if ($size < 1024) {
                $unit = 'B';
            } elseif ($size < 1024 * 1024) {
                $unit = 'KB';
            } elseif ($size < 1024 * 1024 * 1024) {
                $unit = 'MB';
            } else {
                $unit = 'GB';
            }
        }

        if (strtolower($unit) == 'kb') {
            $size = $size / 1024;
        } elseif (strtolower($unit) == 'mb') {
            $size = $size / 1024 / 1024;
        } elseif (strtolower($unit) == 'gb') {
            $size = $size / 1024 / 1024 / 1024;
        }

        if ($get_unit_type) {
            return $unit;
        }

        if ($size < 1) {
            $decimals = 2;
        }

        return round($size, $decimals);
    }

    public function getUrl($size = null)
    {
        if (config('digital_ocean.account_files.cdn_url')) {
            return config('digital_ocean.account_files.cdn_url') . '/' . $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
        } else {
            return 'https://' . config('digital_ocean.account_files.bucket') . '.' . config('digital_ocean.sermon_files.region') . '.digitaloceanspaces.com/' . $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
        }
    }

    public function getTempUrl($minutes = 10)
    {
        return Storage::disk('account-files')
            ->temporaryUrl(
                $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension,
                now()->addMinutes($minutes)
            );
    }

    public function deleteFile()
    {
        return Storage::disk('account-files')->delete($this->file_folder . '/' . $this->file_name . '.' . $this->file_extension);
    }
}
