<?php

namespace App\AccountFiles\Policies;

use App\AccountFiles\AccountFile;
use App\Users\User;

class AccountFilePolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.files')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('files.index');
    }

    public function search(User $user)
    {
        return $user->isSuper();
    }

    public function create(User $user)
    {
        return $user->hasPermission('files.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('files.manage');
    }

    public function edit(User $user, AccountFile $accountFile)
    {
        return $user->hasPermission('files.manage');
    }

    public function save(User $user, AccountFile $accountFile)
    {
        return $user->hasPermission('files.manage');
    }

    public function retrieve(User $user)
    {
        return $user->isSuper();
    }

    public function delete(User $user)
    {
        return $user->hasPermission('files.manage');
    }
}