<?php

namespace App\AccountFiles\Controllers;

use App\AccountFiles\AccountFile;
use App\AccountFiles\Services\AccountFileSorter;
use App\AccountFiles\Services\CreateAccountFile;
use App\AccountFiles\Services\UpdateAccountFile;
use App\Base\Http\Controllers\Controller;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

class AccountFileController extends Controller
{
    public function index()
    {
        Paginator::useTailwind();

        $files = AccountFile::visibleTo(auth()->user())
            ->whereNull('account_file_parent_id')
            ->orderBy('sort_id')
            ->paginate(1000);

        $files_count   = AccountFile::visibleTo(auth()->user())
            ->whereNull('account_file_parent_id')
            ->count();
        $folders_count = AccountFile::visibleTo(auth()->user())
            ->where('is_folder', 1)
            ->count();
        $space_used    = AccountFile::visibleTo(auth()->user())
            ->where('is_folder', 0)
            ->sum('file_size');

        return view('admin.account-files.index')->with([
            'files'         => $files,
            'files_count'   => $files_count,
            'folders_count' => $folders_count,
            'space_used'    => $space_used,
        ]);
    }

    public function folder(AccountFile $accountFile)
    {
        Paginator::useTailwind();

        if (!$accountFile->is_folder) {
            return back();
        }

        $files = AccountFile::visibleTo(auth()->user())
            ->where('account_file_parent_id', $accountFile->id)
            ->orderBy('sort_id')
            ->paginate(1000);

        return view('admin.account-files.index')->with([
            'files'       => $files,
            'file_folder' => $accountFile,
        ]);
    }

    public function create()
    {
        $folders = AccountFile::visibleTo(auth()->user())
            ->isNotExpired()
            ->where('is_folder', 1)
            ->orderBy('sort_id')
            ->get();

        return view('admin.account-files.create')
            ->with('folders', $folders);
    }

    public function store()
    {
        try {
            $accountFile = DB::transaction(function () {
                return (new CreateAccountFile())
                    ->forAccount(auth()->user()->account)
                    ->withFile(request()->file('user_file'), request()->get('file_title'))
                    ->inFolder(request()->get('account_file_parent_id'))
                    ->withTitle(request()->get('title'))
                    ->ofType(request()->get('type'))
                    ->expiresAt(request()->get('expires_at'))
                    ->isPublic(request()->get('is_public'))
                    ->isHidden(request()->get('is_hidden'))
                    ->create();
            });

            // Sort the new file to the top
            (new AccountFileSorter())
                ->forFile($accountFile)
                ->newSortId(0)
                ->inFolder(request()->get('account_file_parent_id'))
                ->byUser(auth()->user())
                ->sort();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        if ($accountFile->parentFolder) {
            return redirect(route('admin.account-files.folder', $accountFile->parentFolder))
                ->with('message.success', 'Saved successfully.');
        } else {
            return redirect(route('admin.account-files.index'))
                ->with('message.success', 'Saved successfully.');
        }
    }

    public function edit(AccountFile $accountFile)
    {
        if ($accountFile->is_folder) {
            return back();
        }

        // Top level folders only
        $folders = AccountFile::visibleTo(auth()->user())
            ->isNotExpired()
            ->where('is_folder', 1)
            ->whereNull('account_file_parent_id')
            ->orderBy('sort_id')
            ->get();

        return view('admin.account-files.edit')
            ->with('file', $accountFile)
            ->with('folders', $folders);
    }

    public function save(AccountFile $accountFile)
    {
        try {
            DB::transaction(function () use ($accountFile) {
                if (request()->hasFile('user_file')) {
                    return (new UpdateAccountFile($accountFile))
                        ->withTitle(request()->get('title'))
                        ->ofType(request()->get('type'))
                        ->expiresAt(request()->get('expires_at'))
                        ->withFile(request()->file('user_file'), request()->get('file_title'))
                        ->inFolder(request()->get('account_file_parent_id'))
                        ->update();
                } else {
                    return (new UpdateAccountFile($accountFile))
                        ->withTitle(request()->get('title'))
                        ->ofType(request()->get('type'))
                        ->expiresAt(request()->get('expires_at'))
                        ->inFolder(request()->get('account_file_parent_id'))
                        ->update();
                }
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        $accountFile->refresh();

        if ($accountFile->parentFolder) {
            return redirect(route('admin.account-files.folder', $accountFile->parentFolder))
                ->with('message.success', 'Saved successfully.');
        } else {
            return redirect(route('admin.account-files.index'))
                ->with('message.success', 'Saved successfully.');
        }
    }

    public function delete(AccountFile $accountFile)
    {
        $parent_folder          = $accountFile->parentFolder;
        $account_file_parent_id = $accountFile->account_file_parent_id;

        try {
            try {
                $accountFile->deleteFile();
            } catch (\Exception $e) {
//                throw new \Exception('Could not remove file from cloud server.');
            }

            $accountFile->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        if ($account_file_parent_id) {
            return redirect(route('admin.account-files.folder', $parent_folder))
                ->with('message.success', 'Deleted successfully.');
        } else {
            return redirect(route('admin.account-files.index'))
                ->with('message.success', 'Deleted successfully.');
        }
    }

    public function createFolder()
    {
        $folders = AccountFile::visibleTo(auth()->user())
            ->IsNotExpired()
            ->FoldersOnly()
            ->TopLevelOnly()
            ->orderBy('sort_id')
            ->get();

        return view('admin.account-files.create-folder')
            ->with('folders', $folders);
    }

    public function storeFolder()
    {
        try {
            $accountFile = DB::transaction(function () {
                return (new CreateAccountFile())
                    ->forAccount(auth()->user()->account)
                    ->isFolder()
                    ->withTitle(request()->get('title'))
                    ->inFolder(request()->get('account_file_parent_id'))
                    ->isPublic(request()->get('is_public'))
                    ->isHidden(request()->get('is_hidden'))
                    ->isMenOnly(request()->get('is_men_only'))
                    ->isWomenOnly(request()->get('is_women_only'))
                    ->expiresAt(request()->get('expires_at'))
                    ->folderColor(request()->get('folder_color'))
                    ->create();
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        $accountFile->refresh();

        if ($accountFile->parentFolder) {
            return redirect(route('admin.account-files.folder', $accountFile->parentFolder))
                ->with('message.success', 'Saved successfully.');
        } else {
            return redirect(route('admin.account-files.index'))
                ->with('message.success', 'Saved successfully.');
        }
    }

    public function editFolder(AccountFile $accountFile)
    {
        if (!$accountFile->is_folder) {
            return back();
        }

        // Top level folders only
        $folders = AccountFile::visibleTo(auth()->user())
            ->IsNotExpired()
            ->FoldersOnly()
            ->TopLevelOnly()
            ->orderBy('sort_id')
            ->where('id', '<>', $accountFile->id)
            ->get();

        return view('admin.account-files.edit-folder')
            ->with('file', $accountFile)
            ->with('folders', $folders);
    }

    public function saveFolder(AccountFile $accountFile)
    {
        try {
            DB::transaction(function () use ($accountFile) {
                return (new UpdateAccountFile($accountFile))
                    ->withTitle(request()->get('title'))
                    ->inFolder(request()->get('account_file_parent_id'))
                    ->isPublic(request()->get('is_public'))
                    ->isHidden(request()->get('is_hidden'))
                    ->isMenOnly(request()->get('is_men_only'))
                    ->isWomenOnly(request()->get('is_women_only'))
                    ->isHidden(request()->get('is_hidden'))
                    ->expiresAt(request()->get('expires_at'))
                    ->folderColor(request()->get('folder_color'))
                    ->update();
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        $accountFile->refresh();

        if ($accountFile->parentFolder) {
            return redirect(route('admin.account-files.folder', $accountFile->parentFolder))
                ->with('message.success', 'Saved successfully.');
        } else {
            return redirect(route('admin.account-files.index'))
                ->with('message.success', 'Saved successfully.');
        }
    }

    public function deleteFolder(AccountFile $accountFile)
    {
        if (!$accountFile->is_folder) {
            return back();
        }

        if ($accountFile->files()->count() > 0) {
            return back()->with('message.failure', 'Folder is not empty.');
        }

        try {
            $accountFile->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.account-files.index')
            ->with('message.success', 'Folder deleted successfully.');
    }

    public function updateSortId(AccountFile $accountFile)
    {
        try {
            (new AccountFileSorter())
                ->forFile($accountFile)
                ->newSortId(request()->get('new_sort_id'))
                ->inFolder(request()->get('account_file_parent_id'))
                ->byUser(auth()->user())
                ->sort();
        } catch (\Exception $e) {
            return response($e->getMessage(), 500);
        }

        return response(200);
    }
}
