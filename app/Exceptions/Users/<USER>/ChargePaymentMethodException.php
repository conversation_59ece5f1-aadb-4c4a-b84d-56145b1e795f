<?php

namespace App\Exceptions\Users\Payments;

use Exception;

class ChargePaymentMethodException extends Exception
{
    // Define constants for the various error conditions
    const NO_PAYMENT_METHOD          = 1001;
    const NO_FINANCE_BUCKET          = 1002;
    const NO_USER_FOR_PAYMENT_METHOD = 1003;
    const NO_STRIPE_CUSTOMER_ID      = 1004;
    const NO_STRIPE_PAYMENT_METHOD   = 1005;
    const NO_AMOUNT_SET              = 1006;
    const NO_STRIPE_ACCOUNT_ID       = 1007;

    // Optionally, you can override the constructor, but this is similar to the default.
    public function __construct($message, $code = 0, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}