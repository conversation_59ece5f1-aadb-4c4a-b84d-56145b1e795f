<?php

namespace App\Super\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Jobs\SendMobileNotification;
use App\Users\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class UserController extends Controller
{
    public function index()
    {
        if (!Auth::user()->isSuper()) {
            abort(404);
        }

        $users = [];

        if (!empty(request()->get('search_term'))) {
            $users = User::when((request()->has('search_term') && !Str::contains(request()->get('search_term'), '@')), function ($mainQuery) {
                $mainQuery->SearchNameByString(request()->get('search_term'));
            })
                ->when(Str::contains(request()->get('search_term'), '@'), function ($mainQuery) {
                    $mainQuery->whereHas('emails', function ($query) {
                        $query->where('email', 'like', request()->get('search_term') . '%');
                    });
                })
                ->withFamilyLastName()
                ->orderBy('last_name', 'asc')
                ->orderBy('first_name', 'asc')
                ->orderBy('preferred_first_name', 'asc')
                ->get();
        }

        return view('admin.super.users.index')
            ->with('users', $users);
    }

    public function view($user_id)
    {
        $user = User::find($user_id);

        return view('admin.super.users.view')
            ->with('user', $user);
    }

    public function sendMobileNotification($user_id)
    {
        if (!Auth::user()->isSuper() || !request()->has('user_device_id') || !request()->has('message')) {
            abort(404);
        }

        $user   = User::find($user_id);
        $device = $user->devices()->find(request()->get('user_device_id'));

        SendMobileNotification::dispatch(
            $user,
            Str::limit(request()->get('title'), 60),
            Str::limit(request()->get('message'), 140)
        )->onQueue('mobile');

        return back()
            ->with('message.success', 'Notification sent.');
    }
}
