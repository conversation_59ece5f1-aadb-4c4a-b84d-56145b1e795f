<?php

namespace App\Rooms;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Calendars\EventOccurrence;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;

class RoomReservation extends Model
{
    use SoftDeletes;

    protected $table = 'room_reservations';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'start_at' => 'datetime',
        'end_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'building_id',
        'room_id',
        'calendar_event_occurrence_id',
        'start_at',
        'end_at',
        'special_instructions',
        'notes',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('room_reservations.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function room()
    {
        return $this->belongsTo(Room::class);
    }

    public function eventOccurrence()
    {
        return $this->belongsTo(EventOccurrence::class, 'calendar_event_occurrence_id');
    }

    // Scopes
    public function scopeForRoom($query, Room $room)
    {
        return $query->where('room_id', $room->id);
    }

    public function scopeForDate($query, Carbon $date)
    {
        return $query->whereDate('start_at', '<=', $date)
            ->whereDate('end_at', '>=', $date);
    }

    public function scopeOverlapping($query, Carbon $start, Carbon $end)
    {
        return $query->where(function ($query) use ($start, $end) {
            $query->where(function ($q) use ($start, $end) {
                $q->where('start_at', '>=', $start)
                    ->where('start_at', '<', $end);
            })->orWhere(function ($q) use ($start, $end) {
                $q->where('end_at', '>', $start)
                    ->where('end_at', '<=', $end);
            })->orWhere(function ($q) use ($start, $end) {
                $q->where('start_at', '<=', $start)
                    ->where('end_at', '>=', $end);
            });
        });
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_at', '>=', now());
    }

    public function scopePast($query)
    {
        return $query->where('end_at', '<', now());
    }

    // Helper methods
    public function isOverlapping(Carbon $start, Carbon $end): bool
    {
        return static::forRoom($this->room)
            ->where('id', '!=', $this->id)
            ->overlapping($start, $end)
            ->exists();
    }

    public function getDurationInMinutes(): int
    {
        return $this->start_at->diffInMinutes($this->end_at);
    }

    public function isActive(): bool
    {
        $now = now();
        return $this->start_at <= $now && $this->end_at > $now;
    }

    public function hasEnded(): bool
    {
        return $this->end_at < now();
    }

    public function hasStarted(): bool
    {
        return $this->start_at <= now();
    }
} 