<?php

namespace App\Rooms;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;

class RoomAvailabilityRule extends Model
{
    use SoftDeletes;

    protected $table = 'room_availability_rules';
    protected $primaryKey = 'rule_id';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'valid_from' => 'date',
        'valid_until' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_recurring' => 'boolean',
        'day_of_week' => 'integer',
    ];

    protected $fillable = [
        'account_id',
        'created_by_user_id',
        'room_id',
        'day_of_week',
        'start_time',
        'end_time',
        'is_recurring',
        'valid_from',
        'valid_until',
    ];

    // ISO-8601 days of week (1=Monday, 7=Sunday)
    const MONDAY = 1;
    const TUESDAY = 2;
    const WEDNESDAY = 3;
    const THURSDAY = 4;
    const FRIDAY = 5;
    const SATURDAY = 6;
    const SUNDAY = 7;

    public static $days_of_week = [
        self::MONDAY => 'Monday',
        self::TUESDAY => 'Tuesday',
        self::WEDNESDAY => 'Wednesday',
        self::THURSDAY => 'Thursday',
        self::FRIDAY => 'Friday',
        self::SATURDAY => 'Saturday',
        self::SUNDAY => 'Sunday',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('room_availability_rules.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function room()
    {
        return $this->belongsTo(Room::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    // Scopes
    public function scopeForRoom($query, Room $room)
    {
        return $query->where('room_id', $room->id);
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    public function scopeNonRecurring($query)
    {
        return $query->where('is_recurring', false);
    }

    public function scopeForDayOfWeek($query, int $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    public function scopeActive($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('valid_until')
                ->orWhere('valid_until', '>=', now()->startOfDay());
        })->where(function ($query) {
            $query->whereNull('valid_from')
                ->orWhere('valid_from', '<=', now()->endOfDay());
        });
    }

    // Helper methods
    public function isValidForDate(Carbon $date): bool
    {
        if ($this->valid_from && $date->startOfDay()->lt($this->valid_from)) {
            return false;
        }

        if ($this->valid_until && $date->endOfDay()->gt($this->valid_until)) {
            return false;
        }

        if ($this->is_recurring) {
            return $this->day_of_week === $date->dayOfWeekIso;
        }

        return true;
    }

    public function getDayOfWeekName(): string
    {
        return self::$days_of_week[$this->day_of_week] ?? '';
    }

    public function getTimeRange(): string
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }
} 