<?php

namespace App\Rooms;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Calendars\EventOccurrence;
use App\Users\User;
use Illuminate\Database\Eloquent\SoftDeletes;

class Room extends Model
{
    use SoftDeletes;

    protected $table = 'rooms';

    protected $casts = [
        'created_at'                         => 'datetime',
        'updated_at'                         => 'datetime',
        'deleted_at'                         => 'datetime',
        'features'                           => 'array',
        'is_hidden'                          => 'boolean',
        'is_bookable'                        => 'boolean',
        'is_available'                       => 'boolean',
        'is_maintenance'                     => 'boolean',
        'capacity'                           => 'integer',
        'minimum_booking_time_in_minutes'    => 'integer',
        'maximum_booking_time_in_minutes'    => 'integer',
        'pre_reservation_buffer_in_minutes'  => 'integer',
        'post_reservation_buffer_in_minutes' => 'integer',
    ];

    protected $fillable = [
        'account_id',
        'building_id',
        'created_by_user_id',
        'name',
        'short_name',
        'type',
        'location',
        'capacity',
        'description',
        'instructions',
        'cancellation_policy',
        'features',
        'minimum_booking_time_in_minutes',
        'maximum_booking_time_in_minutes',
        'pre_reservation_buffer_in_minutes',
        'post_reservation_buffer_in_minutes',
        'is_hidden',
        'is_bookable',
        'is_available',
        'is_maintenance',
    ];

    const FEATURE_PROJECTOR  = 'projector';
    const FEATURE_WHITEBOARD = 'whiteboard';
    const FEATURE_WIFI       = 'wifi';

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('rooms.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    public function reservations()
    {
        return $this->hasMany(RoomReservation::class);
    }

    public function availabilityRules()
    {
        return $this->hasMany(RoomAvailabilityRule::class);
    }

    public function eventOccurrences()
    {
        return $this->hasManyThrough(
            EventOccurrence::class,
            RoomReservation::class,
            'room_id',
            'id',
            'id',
            'calendar_event_occurrence_id'
        );
    }

    // Scopes
    public function scopeBookable($query)
    {
        return $query->where('is_bookable', true);
    }

    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeUnderMaintenance($query)
    {
        return $query->where('is_maintenance', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeWithCapacityAtLeast($query, int $capacity)
    {
        return $query->where('capacity', '>=', $capacity);
    }

    // Helper methods
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->features ?? []);
    }

    public function isAvailableForBooking(): bool
    {
        return $this->is_bookable && $this->is_available && !$this->is_hidden && !$this->is_maintenance;
    }
} 