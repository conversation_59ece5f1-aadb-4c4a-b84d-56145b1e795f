<?php

namespace App\Open\Controllers;

use App\Accounts\Account;
use App\Base\Http\Controllers\Controller;

class PublicAttendanceCardController extends Controller
{
    public function home()
    {
        return redirect()->away('https://' . config('app.domains.frontend'));
    }

    public function card($account_hash_id, $type = null)
    {
        $account = Account::where('hash_id', $account_hash_id)->firstOrFail();

        if (!$account || !$account->is_active) {
            abort(404);
        }

        return view('public.attendance.card')->with([
            'account' => $account,
        ]);
    }
}
