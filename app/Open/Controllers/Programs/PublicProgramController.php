<?php

namespace App\Open\Controllers\Programs;

use App\Base\Http\Controllers\Controller;
use App\Programs\Program;

class PublicProgramController extends Controller
{
    public function view($program)
    {
        $program = Program::where('squid', $program)->first();

        if (!$program) {
            abort(404);
        }

        return view('open.programs.view')
            ->with('program', $program);
    }
}
