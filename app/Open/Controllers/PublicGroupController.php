<?php

namespace App\Open\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Group;
use App\Users\User;

class PublicGroupController extends Controller
{
    public function publicUnsubscribeEmail(Group $group, User $user, $uuid)
    {
        if (!$uuid || !$user) {
            abort(404);
        }

        if ($group->uuid !== $uuid || !$user->getGroupWithSettings($group->id)) {
            return view('app.public.groups.email-unsubscribe-invalid-token')
                ->with('group', $group)
                ->with('user', $user);
        }

        if ($user->getGroupWithSettings($group->id)->settings->receive_group_emails) {
            return view('app.public.groups.email-unsubscribe')
                ->with('group', $group)
                ->with('user', $user);
        } else {
            return view('app.public.groups.email-resubscribe')
                ->with('group', $group)
                ->with('user', $user);
        }
    }

    public function publicUnsubscribeEmailSave(Group $group, User $user, $uuid, $resubscribe = false)
    {
        if (!$uuid || !$user) {
            abort(404);
        }

        if ($group->uuid !== $uuid || !$user->getGroupWithSettings($group->id)) {
            return view('app.public.groups.email-unsubscribe-invalid-token')
                ->with('group', $group)
                ->with('user', $user);
        }

        $groupWithSettings = $user->getGroupWithSettings($group->id);

        if (!$resubscribe) {
            $groupWithSettings->settings->receive_group_emails = 0;
            $groupWithSettings->settings->save();

            return view('app.public.groups.email-unsubscribe-confirm')
                ->with('group', $group)
                ->with('user', $user);
        } else {
            $groupWithSettings->settings->receive_group_emails = 1;
            $groupWithSettings->settings->save();

            return view('app.public.groups.email-resubscribe-confirm')
                ->with('group', $group)
                ->with('user', $user);
        }
    }

    public function publicEmailsWhy(Group $group, $uuid)
    {
        if ($group->uuid !== $uuid) {
            abort(404);
        }

        return view('app.public.groups.why')
            ->with('group', $group);
    }
}
