<?php

namespace App\Open\Controllers;

use App\Base\Http\Controllers\Controller;
use App\WorshipAssignments\Pick;
use App\WorshipAssignments\Services\DeclineAssignment;
use Carbon\Carbon;

class WorshipAssignmentController extends Controller
{
    public function userConfirm(Pick $pick, $token = null)
    {
        if (!$token || !$pick) {
            abort(404);
        }

        if ($pick->token != $token) {
            return view('app.worship-assignments.invalid-token')
                ->with('pick', $pick);
        }

        $pick->has_replied  = Carbon::now();
        $pick->is_confirmed = Carbon::now();
        $pick->save();

        return view('app.worship-assignments.user-confirm')
            ->with('pick', $pick);
    }

    public function userDecline(Pick $pick, $token = null)
    {
        if (!$token || !$pick) {
            abort(404);
        }

        if ($pick->token != $token) {
            return view('app.worship-assignments.invalid-token')
                ->with('pick', $pick);
        }

        (new DeclineAssignment($pick))->decline();

        return view('app.worship-assignments.user-decline')
            ->with('pick', $pick);
    }
}
