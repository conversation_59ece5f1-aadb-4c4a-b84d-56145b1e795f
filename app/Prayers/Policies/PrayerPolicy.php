<?php

namespace App\Prayers\Policies;

use App\Prayers\Prayer;
use App\Users\User;

class PrayerPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.prayers')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('prayers.index');
    }

    public function search(User $user)
    {
        return $user->hasPermission('prayers.index');
    }

    public function create(User $user)
    {
        return $user->hasPermission('prayers.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('prayers.manage');
    }

    public function edit(User $user, Prayer $prayer)
    {
        return $user->hasPermission('prayers.manage')
            && $prayer->account_id == $user->account_id;
    }

    public function save(User $user, Prayer $prayer)
    {
        return $user->hasPermission('prayers.manage')
            && $prayer->account_id == $user->account_id;
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('prayers.index');
    }

    public function delete(User $user, Prayer $prayer)
    {
        return $user->hasPermission('prayers.delete')
            && $prayer->account_id == $user->account_id;
    }
}