<?php

namespace App\Prayers\Services;

use App\Prayers\Prayer;
use Illuminate\Support\Arr;

class UpdatePrayer
{
    protected $attributes = [];
    protected $user_tags  = [];
    protected $prayer;

    public function __construct(Prayer $prayer)
    {
        $this->prayer = $prayer;
    }

    public function update($attributes): Prayer
    {
        $this->attributes = $attributes;

        if (!Arr::has($this->attributes, 'last_updated_at')) {
            // $this->attributes['last_updated_at'] = Carbon::now();
        }

        $this->prayer->fill($this->attributes);

        $this->prayer->save();

        $this->syncUserTags();

        return $this->prayer;
    }

    public function approve()
    {
        $this->prayer->is_approved = true;
        $this->prayer->is_active   = true;

        $this->prayer->save();

        return $this->prayer;
    }

    public function decline()
    {
        $this->prayer->is_approved = false;
        $this->prayer->is_active   = false;

        $this->prayer->save();

        return $this->prayer;
    }

    public function tagUsers($users)
    {
        $this->user_tags = $users;

        return $this;
    }

    protected function syncUserTags()
    {
        $sync_array = [];

        if (is_array($this->user_tags)) {
            foreach ($this->user_tags as $user_id) {
                $sync_array[$user_id] = [
                    'account_id' => $this->prayer->account_id,
                ];
            }
        }

        $this->prayer->users()->sync($sync_array);
    }
}
