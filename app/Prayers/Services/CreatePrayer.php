<?php

namespace App\Prayers\Services;

use App\Prayers\Prayer;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class CreatePrayer
{
    protected $attributes = [];
    protected $user_tags  = [];
    protected $by_user    = null;
    protected $prayer;

    public function create($attributes): Prayer
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (!Arr::has($this->attributes, 'last_updated_at')) {
            $this->attributes['last_updated_at'] = Carbon::now();
        }

        $this->prayer = Prayer::create($this->attributes);

        $this->syncUserTags();

        $this->prayer->updated_at = now();
        $this->prayer->save();

        return $this->prayer;
    }

    public function byUser($user)
    {
        $this->by_user = $user;

        $this->attributes['account_id'] = $user->account_id;
        $this->attributes['created_by'] = $user->id;

        return $this;
    }

    public function tagUsers($users)
    {
        $this->user_tags = $users;

        return $this;
    }

    protected function syncUserTags()
    {
        if (is_array($this->user_tags)) {
            foreach ($this->user_tags as $user_id) {
                $this->prayer->users()->attach($user_id, ['account_id' => $this->by_user->account_id]);
            }
        }
    }
}
