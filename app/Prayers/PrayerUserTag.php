<?php

namespace App\Prayers;

use App\Accounts\Account;
use App\Prayers\Scopes\PrayerVisibleToAccountScope;
use App\Prayers\Scopes\PrayerVisibleToScope;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;

class PrayerUserTag extends Model
{
    protected $table = 'prayer_user_tags';

    protected $casts = [
        'created_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'prayer_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new PrayerVisibleToScope())->getQuery($query, $user);
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return (new PrayerVisibleToAccountScope())->getQuery($query, $account);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function prayer()
    {
        return $this->belongsTo(Prayer::class);
    }
}
