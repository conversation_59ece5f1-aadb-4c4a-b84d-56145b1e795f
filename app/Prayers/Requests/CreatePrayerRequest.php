<?php

namespace App\Prayers\Requests;

use App\Base\Http\Request;

class CreatePrayerRequest extends Request
{
    public function rules()
    {
        $rules = [
            'last_updated_at' => 'nullable|date',
            'expires_at'      => 'nullable|date',
            'title'           => 'nullable|string',
            'details'         => 'nullable|string',
            'is_for_member'   => 'nullable|boolean',
            'is_active'       => 'nullable|boolean',
            'is_hidden'       => 'nullable|boolean',
        ];

        return $rules;
    }
}
