<?php

namespace App\Prayers;

use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PrayerUpdate extends Model
{
    use SoftDeletes;

    protected $table = 'prayer_updates';

    protected $casts = [
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
        'deleted_at'      => 'datetime',
        'last_updated_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'prayer_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'last_updated_at',
        'created_by',
        'title',
        'details',
        'is_hidden',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function prayer()
    {
        return $this->belongsTo(Prayer::class);
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeIsHidden($query)
    {
        return $query->where('is_hidden', true);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function scopeIsNotExpired($query)
    {
        return $query->whereHas('prayer', function ($query2) {
            $query2->isNotExpired();
        });
    }

    public function scopePrayerIsViewableToMembers($query)
    {
        return $query->whereHas('prayer', function ($query) {
            $query->IsNotARequest();
        });
    }

    public function scopeNotificationNotSent($query)
    {
        return $query->whereNull('notification_sent_at');
    }
}
