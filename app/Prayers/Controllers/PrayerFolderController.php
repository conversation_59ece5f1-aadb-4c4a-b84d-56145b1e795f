<?php

namespace App\Prayers\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Prayers\PrayerFolder;
use App\Prayers\Requests\CreatePrayerRequest;
use Illuminate\Support\Facades\DB;

class PrayerFolderController extends Controller
{
    public function create()
    {
        return view('admin..prayers.folders.create');
    }

    public function store(CreatePrayerRequest $request)
    {
        try {
            $folder = DB::transaction(function () use ($request) {
                return PrayerFolder::create([
                    'account_id'  => auth()->user()->account_id,
                    'name'        => request('name'),
                    'description' => request('description'),
                    'is_hidden'   => request('is_hidden', false),
                ]);
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.prayers.folders.index', $folder))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(PrayerFolder $folder)
    {
        return view('admin.prayers.folders.edit')
            ->with('folder', $folder);
    }

    public function save(PrayerFolder $folder)
    {
        try {
            DB::transaction(function () use ($folder) {
                $folder->update([
                    'name'        => request('name'),
                    'description' => request('description'),
                    'is_hidden'   => request('is_hidden', false),
                ]);
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()
            ->with('message.success', 'Saved successfully.');
    }

    public function destroy(PrayerFolder $folder)
    {
        $folder->delete();

        return redirect(route('admin.prayers.index'))
            ->with('message.success', 'Prayer folder successfully deleted.');
    }

    public function settings()
    {
        return view('admin.prayers.folders.settings')->with([
            'settings' => auth()->user()->account->prayerSettings,
        ]);
    }

    public function saveSettings()
    {
        try {
            $settings = auth()->user()->account->prayerSettings;

            $settings->notification_groups          = request('notification_groups', []);
            $settings->autosend_notifications       = request('autosend_notifications', false);
            $settings->send_via_email               = request('send_via_email', false);
            $settings->send_via_mobile_notification = request('send_via_mobile_notification', false);

            $settings->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.prayers.index')
            ->with('message.success', 'Settings saved successfully.');
    }

    public function sendNotifications()
    {
    }
}
