<?php

namespace App\Prayers\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Prayers\Prayer;
use App\Prayers\PrayerFolder;
use App\Prayers\Requests\CreatePrayerRequest;
use App\Prayers\Requests\UpdatePrayerRequest;
use App\Prayers\Services\CreatePrayer;
use App\Prayers\Services\UpdatePrayer;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;

class PrayerController extends Controller
{
    public function index(PrayerFolder $folder = null)
    {
        Paginator::useTailwind();

        return view('admin.prayers.index')
            ->with([
                'selected_folder' => $folder,
            ]);
    }

    public function approve(Prayer $prayer)
    {
        (new UpdatePrayer($prayer))->approve();

        return back()
            ->with('message.success', 'Prayer request approved.');
    }

    public function decline(Prayer $prayer)
    {
        (new UpdatePrayer($prayer))->decline();

        return back()
            ->with('message.success', 'Prayer request declined and archived.');
    }

    public function view(Prayer $prayer)
    {
        return view('admin.prayers.view')->with([
            'prayer' => $prayer,
        ]);
    }

    public function create()
    {
        return view('admin.prayers.create');
    }

    public function store(CreatePrayerRequest $request)
    {
        try {
            $prayer = DB::transaction(function () use ($request) {
                return (new CreatePrayer())
                    ->tagUsers($request->get('selected_user_ids'))
                    ->byUser(auth()->user())
                    ->create(
                        $request->only([
                            'title',
                            'details',
                            'prayer_folder_id',
                            'expires_at',
                            'is_hidden',
                        ])
                    );
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect($prayer->folder ? route('admin.prayers.folders.index', $prayer->folder) : route('admin.prayers.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Prayer $prayer)
    {
        return view('admin.prayers.edit')
            ->with('prayer', $prayer);
    }

    public function save(UpdatePrayerRequest $request, Prayer $prayer)
    {
        try {
            $prayer = DB::transaction(function () use ($request, $prayer) {
                return (new UpdatePrayer($prayer))
                    ->tagUsers(request('selected_user_ids'))
                    ->update(
                        $request->only([
                            'title',
                            'details',
                            'prayer_folder_id',
                            'expires_at',
                            'is_hidden',
                        ])
                    );
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect($prayer->folder ? route('admin.prayers.folders.index', $prayer->folder) : route('admin.prayers.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Prayer $prayer)
    {
        // $prayer->delete();

        return redirect(route('admin.prayers.index'))
            ->with('message.success', 'Deleted.');
    }

    public function destroy(Prayer $prayer)
    {
        $prayer->delete();

        return redirect(route('admin.prayers.index'))
            ->with('message.success', 'Prayer and updates successfully deleted.');
    }

    public function settings()
    {
        return view('admin.prayers.settings')->with([
            'settings' => auth()->user()->account->prayerSettings,
        ]);
    }

    public function saveSettings()
    {
        try {
            $settings = auth()->user()->account->prayerSettings;

            $settings->notification_groups          = request('notification_groups', []);
            $settings->autosend_notifications       = request('autosend_notifications', false);
            $settings->send_via_email               = request('send_via_email', false);
            $settings->send_via_mobile_notification = request('send_via_mobile_notification', false);

            $settings->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.prayers.index')
            ->with('message.success', 'Settings saved successfully.');
    }

    public function sendNotifications()
    {
    }
}
