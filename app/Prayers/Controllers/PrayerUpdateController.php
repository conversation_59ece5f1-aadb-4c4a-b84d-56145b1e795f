<?php

namespace App\Prayers\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Prayers\Prayer;
use App\Prayers\PrayerUpdate;
use App\Prayers\Requests\CreatePrayerUpdateRequest;
use App\Prayers\Services\CreatePrayerUpdate;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PrayerUpdateController extends Controller
{
    public function store(CreatePrayerUpdateRequest $request, Prayer $prayer)
    {
        try {
            DB::transaction(function () use ($request, $prayer) {
                return (new CreatePrayerUpdate($prayer))
                    ->byUser(Auth::user())
                    ->withDetails(request('update_details'))
                    ->create();
            });

            $prayer->last_updated_at = Carbon::now();
            $prayer->save();

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.prayers.index'))
            ->with('message.success', 'Update saved successfully.');
    }

    public function save(CreatePrayerUpdateRequest $request, Prayer $prayer)
    {
        try {

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.prayers.index')
            ->with('message.success', 'Saved successfully.');
    }

    public function destroy(Prayer $prayer, PrayerUpdate $prayerUpdate)
    {
        try {
            $prayerUpdate->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.prayers.edit', $prayer))
            ->with('message.success', 'Prayer update deleted.');
    }
}
