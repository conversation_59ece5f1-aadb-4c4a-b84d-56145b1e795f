<?php

namespace App\Prayers;

use App\Users\Group;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PrayerFolder extends Model
{
    use SoftDeletes;

    protected $table = 'prayer_folders';

    protected $casts = [
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
        'deleted_at'      => 'datetime',
        'last_updated_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'user_group_id',
        'last_updated_at',
        'created_by',
        'name',
        'description',
        'is_hidden',
        'sort_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'user_group_id');
    }

    public function prayers()
    {
        return $this->hasMany(Prayer::class)->orderBy('last_updated_at', 'desc');
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeIsHidden($query)
    {
        return $query->where('is_hidden', true);
    }
}
