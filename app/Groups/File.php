<?php

namespace App\Groups;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\Group;
use App\Users\User;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class File extends Model
{
    use SoftDeletes;

    protected $table = 'user_group_files';

    public $storage_disk    = 'group-files';
    public $storage_service = 'linode';

    protected $casts = [
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
        'deleted_at'     => 'datetime',
        'is_remote_gif'  => 'boolean',
        'is_image'       => 'boolean',
        'is_video'       => 'boolean',
        'is_document'    => 'boolean',
        'force_download' => 'boolean',
        'is_flagged'     => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'user_group_id',
        'user_group_post_id',
        'user_group_post_comment_id',
        'user_id',
        'storage_service',
        'file_size',
        'data_separator',
        'file_folder',
        'file_id',
        'file_name_original',
        'file_name',
        'file_extension',
        'file_type',
        'file_sha1',
        'remote_gif_url',
        'width',
        'height',
        'has_1024',
        'has_512',
        'has_256',
        'is_remote_gif',
        'is_image',
        'is_video',
        'is_document',
        'force_download',
        'is_flagged',
        'views',
        'downloads',
        'sort_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToPost($query, Post $post)
    {
        return $query->where(function ($query) use ($post) {
            $query->where($this->table . '.user_group_post_id', $post->id);
        });
    }

    public function scopeVisibleToComment($query, Comment $comment)
    {
        return $query->where(function ($query) use ($comment) {
            $query->where($this->table . '.user_group_post_comment_id', $comment->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'user_group_id');
    }

    public function post()
    {
        return $this->belongsTo(Post::class, 'user_group_post_id');
    }

    public function comment()
    {
        return $this->belongsTo(Comment::class, 'user_group_post_comment_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function scopeOnlyImages($query)
    {
        return $query->where('is_image', true);
    }

    public function scopeOnlyDocuments($query)
    {
        return $query->where('is_document', true);
    }

    public function scopeOnlyVideos($query)
    {
        return $query->where('is_video', true);
    }

    public function scopeWithThumbnailUrls($query)
    {
        return $query->addSelect([
            DB::raw($this->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
            DB::raw($this->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
        ]);
    }

    public function getMySQLConcatString($size = 2056)
    {
        // This is database field that we want to use for getting a size.
        $file_name_field = 'file_name';

        if ($size <= 256) {
            $file_name_field = 'has_256';
        } elseif ($size <= 512) {
            $file_name_field = 'has_512';
        } elseif ($size <= 1024) {
            $file_name_field = 'has_1024';
        }

        if (config('filesystems.disks.' . $this->storage_disk . '.cdn_endpoint')) {
            return "CONCAT('" . config('filesystems.disks.' . $this->storage_disk . '.cdn_endpoint') . '/' . "', file_folder, '/', $file_name_field, '.', file_extension)";
        } else {
            return "CONCAT('" . config('filesystems.disks.' . $this->storage_disk . '.url_endpoint') . '/' . "', file_folder, '/', $file_name_field, '.', file_extension)";
        }
    }

    public function getTemporaryUrl($minutes = 60)
    {
        return Storage::disk($this->storage_disk)->temporaryUrl($this->file_folder . '/' . $this->file_name . '.' . $this->file_extension, now()
            ->addMinutes($minutes));
    }

    public function getUrl($size = 2056)
    {
        // This is database field that we want to use for getting a size.
        $file_name_field = 'file_name';

        if ($size <= 256 && !empty($this->has_256)) {
            $file_name_field = 'has_256';
        } elseif ($size <= 512 && !empty($this->has_512)) {
            $file_name_field = 'has_512';
        } elseif ($size <= 1024 && !empty($this->has_1024)) {
            $file_name_field = 'has_1024';
        }

        return config('filesystems.disks.' . $this->storage_disk . '.url_endpoint') . '/' . $this->file_folder . '/' . $this->$file_name_field . '.' . $this->file_extension;
    }

    public function getCdnUrl($size = null)
    {
        return config('filesystems.disks.' . $this->storage_disk . '.cdn_endpoint') . '/' . $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
    }

    public function getImgTag($width = null, $class = null)
    {
        return '<img src="' . $this->getCdnUrl(512) . '" srcset="' . $this->getCdnUrl(512) . ' 1x, ' . $this->getCdnUrl(1024) . ' 2x" class="' . ($class ?: 'rounded img-thumbnail') . '" width="' . $width . '"/>';
    }

    // We actually move files to a "/deleted" folder, just in case.  We can purge this folder later.
    public function deleteAllFiles()
    {
        try {
            // Original -- saved as a JPG
            $location_original = $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
            if (!Storage::disk($this->storage_disk)->move($location_original, 'deleted/' . $location_original)) {
                throw new \Exception('Group File: Could not move file/object to deleted directory. Size: has_original. ID: ' . $this->id);
            }
            // 1024px
            $location_1024 = $this->file_folder . '/' . $this->has_1024 . '.' . $this->file_extension;
            if ($this->has_1024 && !Storage::disk($this->storage_disk)->move($location_1024, 'deleted/' . $location_1024)) {
                throw new \Exception('Group File: Could not move file/object to deleted directory. Size: has_1024. ID: ' . $this->id);
            }
            // 512px
            $location_512 = $this->file_folder . '/' . $this->has_512 . '.' . $this->file_extension;
            if ($this->has_512 && !Storage::disk($this->storage_disk)->move($location_512, 'deleted/' . $location_512)) {
                throw new \Exception('Group File: Could not move file/object to deleted directory. Size: has_512. ID: ' . $this->id);
            }
            // 256px
            $location_256 = $this->file_folder . '/' . $this->has_256 . '.' . $this->file_extension;
            if ($this->has_256 && !Storage::disk($this->storage_disk)->move($location_256, 'deleted/' . $location_256)) {
                throw new \Exception('Group File: Could not move file/object to deleted directory. Size: has_256. ID: ' . $this->id);
            }

            return true;

        } catch (\Exception $e) {
            Log::error($e);
            return false;
        }
    }

    public function deleteFile()
    {
        return Storage::disk($this->storage_disk)->delete($this->file_folder . '/' . $this->file_name . '.' . $this->file_extension);
    }

    public function sanitizeFilename($filename)
    {
        // Allow UTF-8 for now, since Linode Object Store supports it.
        // $filename = Str::ascii($filename);

        // Remove characters that cause problems with cloud provides (Linode Object Store)
        $filename = Str::of($filename)->replaceMatches("/['\"\%\/\<\>&+=]+/", '');

        // Replace other things with an underscore.
        return Str::of($filename)->replaceMatches('|[^a-zA-Z0-9_.-]|', '_');
    }

    public function getExtension($filename)
    {
        if (empty($filename)) {
            return null;
        }

        $parts_array = explode('.', $filename);

        if (count($parts_array) <= 1) {
            return null;
        }

        return end($parts_array);
    }
}
