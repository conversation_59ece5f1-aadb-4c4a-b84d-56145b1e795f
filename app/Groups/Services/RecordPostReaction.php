<?php

namespace App\Groups\Services;

use App\Groups\Post;
use App\Groups\Reaction;

class RecordPostReaction
{
    protected $attributes = [
        'user_group_post_id' => null,
        'title'              => null,
        'url_title'          => null,
        'allow_comments'     => true,
        'is_shared'          => false,
        'is_pinned'          => false,
        'is_hidden'          => false,
        'is_flagged'         => false,
        'is_poll'            => false,
        'is_link'            => false,
        'is_rsvp'            => false,
    ];
    protected $type       = 'like';
    protected $post;
    protected $user;

    public function record()
    {
        if (!$this->user || !$this->post) {
            return false;
        }

        // Delete any previous reaction
        if ($existing_reaction = Reaction::where('user_id', $this->user->id)
            ->where('user_group_post_id', $this->post->id)
            ->whereNull('user_group_post_comment_id')
            ->first()) {
            $existing_reaction->delete();
        }

        // Only add a new reaction if we're not simply deleting an existing one.
        if ($this->type !== 'delete') {
            // If our type is an invalid type, default to a like.  Prevents SQL injection issues.
            if (!in_array('is_' . $this->type, Reaction::$types)) {
                $type = 'like';
            }

            Reaction::create([
                'user_id'            => $this->user->id,
                'user_group_post_id' => $this->post->id,
                'is_' . $this->type  => 1,
            ]);
        }

        return true;
    }

    public function forPost(Post $post)
    {
        $this->post = $post;

        return $this;
    }

    public function forUser($user)
    {
        $this->user = $user;

        return $this;
    }

    public function withType($type = 'like')
    {
        $this->type = $type;

        return $this;
    }
}
