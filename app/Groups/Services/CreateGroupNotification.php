<?php

namespace App\Groups\Services;

use App\Users\Notification;

class CreateGroupNotification
{
    protected $attributes = [
        'user_id'            => null,
        'user_group_id'      => null,
        'user_group_post_id' => null,
        'message'            => null,
        'type'               => null,
        'is_read'            => false,
    ];
    protected $group;
    protected $post;
    protected $user;

    public function create($attributes = [])
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        return Notification::create([
            'user_id' => $this->user->id,
            'p_t'     => 'user_groups',
            'p_t_id'  => $this->attributes['user_group_id'],
            't'       => 'user_group_posts',
            't_id'    => $this->attributes['user_group_post_id'],
            'msg'     => $this->attributes['message'],
            'type'    => $this->attributes['type'],
            'is_read' => false,
        ]);
    }

    public function forGroup($group)
    {
        $this->attributes['user_group_id'] = $group->id;

        return $this;
    }

    public function forUser($user)
    {
        $this->user = $user;

        $this->attributes['user_id']    = $this->user->id;
        $this->attributes['account_id'] = $this->user->account_id;

        return $this;
    }

    public function forGroupPost($post)
    {
        $this->attributes['user_group_post_id'] = $post->id;
        $this->attributes['user_group_id']      = $post->user_group_id;

        return $this;
    }

    public function type($type)
    {
        $this->attributes['type'] = $type;

        return $this;
    }

    public function withMessage($message)
    {
        $this->attributes['message'] = $message;

        return $this;
    }
}
