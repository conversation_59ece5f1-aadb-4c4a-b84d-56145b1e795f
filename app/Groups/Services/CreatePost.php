<?php

namespace App\Groups\Services;

use App\Groups\Post;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class CreatePost
{
    protected $attributes = [
        'user_group_post_id' => null,
        'title'              => null,
        'url_title'          => null,
        'allow_comments'     => true,
        'is_shared'          => false,
        'is_pinned'          => false,
        'is_hidden'          => false,
        'is_flagged'         => false,
        'is_poll'            => false,
        'is_link'            => false,
        'is_rsvp'            => false,
    ];
    protected $group;
    protected $post;
    protected $user;

    public function create($attributes): Post
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (!Arr::has($this->attributes, 'created_at')) {
            $this->attributes['created_at'] = Carbon::now();
        }
        if (!Arr::has($this->attributes, 'published_at')) {
            $this->attributes['published_at'] = Carbon::now();
        }

        $this->post = Post::create($this->attributes);

        return $this->post;
    }

    public function forGroup($group)
    {
        $this->attributes['user_group_id'] = $group->id;

        return $this;
    }

    public function createdBy($user)
    {
        $this->user = $user;

        $this->attributes['creator_id'] = $this->user->id;
        $this->attributes['account_id'] = $this->user->account_id;

        return $this;
    }

    public function allowComments($option = true)
    {
        $this->attributes['allow_comments'] = $option;

        return $this;
    }
}
