<?php

namespace App\Groups\Services;

use App\Groups\Comment;
use App\Groups\Post;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class CreateComment
{
    protected $attributes = [
        'account_id'                 => null,
        'user_group_id'              => null,
        'user_group_post_id'         => null,
        'user_group_post_comment_id' => null,
        'creator_id'                 => null,
        'content'                    => null,
        'is_pinned'                  => false,
        'is_hidden'                  => false,
        'is_flagged'                 => false,
    ];
    protected $group;
    protected $post;
    protected $comment;
    protected $user;

    public function create($attributes = []): Comment
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (!Arr::has($this->attributes, 'created_at')) {
            $this->attributes['created_at'] = Carbon::now();
        }
        if (!Arr::has($this->attributes, 'updated_at')) {
            $this->attributes['updated_at'] = Carbon::now();
        }

        $this->comment = Comment::create($this->attributes);

        return $this->comment;
    }

    public function forPost(Post $post)
    {
        $this->attributes['account_id']         = $post->account_id;
        $this->attributes['user_group_post_id'] = $post->id;
        $this->attributes['user_group_id']      = $post->group->id;

        return $this;
    }

    public function withComment($comment = null)
    {
        $this->attributes['content'] = trim($comment);

        return $this;
    }

    public function createdBy($user)
    {
        $this->user = $user;

        $this->attributes['creator_id'] = $this->user->id;

        return $this;
    }

    public function isPinned($is_pinned = false)
    {
        $this->attributes['is_pinned'] = $is_pinned;

        return $this;
    }
}
