<?php

namespace App\Groups\Services;

use App\Accounts\Account;
use App\Groups\Comment;
use App\Groups\File;
use App\Groups\Post;
use App\Users\Group;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Drivers\Imagick\Driver;
use Intervention\Image\ImageManager;

class CreateGroupFile
{
    protected $attributes = [];
    protected $group      = null;
    protected $post       = null;
    protected $comment    = null;
    protected $file;
    protected $file_type;
    protected $account;
    protected $user;
    protected $file_name;
    protected $file_size  = 0;
    protected $extension  = null;
    protected $image      = null;

    public function __construct()
    {
    }

    /**
     * @param $file_contents
     * @param $is_base64
     *
     * @return File
     * @throws \Exception
     */
    public function create($file_contents, $is_base64 = false)
    {
        if (!$file_contents) {
            throw new \Exception('File size was zero.');
        }

        if ($is_base64) {
            $file_contents = base64_decode($file_contents);
        }

        if (!$this->group) {
            $this->group = $this->post?->group;
        }

        if (!$this->preCheck()) {
            throw new \Exception('Missing needed information to save a file.');
        }

        try {
            // create new manager instance with desired driver
            $manager = new ImageManager(new Driver());

            // read image from filesystem
            $this->image = $manager->read($file_contents);

            $this->getImageAttributes($this->image);
        } catch (\Exception $e) {
            // Ignore for now if it's not an image.
            Log::error($e);
        }

        $folder = $this->getFolder();

        // EXTENSION
        $extension = $this->extension ?: (new File())->getExtension($this->file_name);
        // Double check our extension
        if (empty($extension)) {
            $extension = $this->mime2ext($this->file_type ?: Arr::get($this->attributes, 'file_type'));
        }

        // FILE NAME -- Android does not give us a filename!
        if (empty($this->file_name)) {
            $this->file_name = Str::random(8) . '.' . $extension;
        }

        // FILE SIZE -- Android does not give us a file size!
        if (!$this->file_size) {
            $this->file_size = mb_strlen($file_contents, '8bit');
        }

        $new_file_name                   = (new File())->sanitizeFilename($this->account->id . '--' . Str::random(8) . '--' . $this->file_name);
        $new_file_name_without_extension = str_replace('.' . $extension, '', $new_file_name);

        $public_or_private = 'private';
        if ($this->image) {
            $file_contents     = $this->image->toJpeg(90)->__toString();
            $public_or_private = 'public';
            $this->file_type   = $this->image->toJpeg(90)->mimetype();
        }

        // PUT the file. Store as public.
        Storage::disk((new File())->storage_disk)
            ->put(
                $folder . '/' . $new_file_name,
                $file_contents,
                $public_or_private,
            );

        $this->file = new File();

        $this->file->fill([
            'account_id'                 => $this->account->id,
            'user_group_id'              => $this->group?->id,
            'user_group_post_id'         => $this->post?->id,
            'user_group_post_comment_id' => $this->comment?->id,
            'user_id'                    => $this->user?->id,
            'storage_service'            => (new File())->storage_service,
            'file_size'                  => $this->file_size,
            'data_separator'             => '--',
            'file_folder'                => $folder,
            'file_name_original'         => $this->file_name,
            'file_name'                  => $new_file_name_without_extension,
            'file_extension'             => $extension ?: '',
            'file_type'                  => $this->file_type ?: Arr::get($this->attributes, 'file_type'),
            'file_sha1'                  => sha1($file_contents),
            'width'                      => Arr::get($this->attributes, 'width'),
            'height'                     => Arr::get($this->attributes, 'height'),
            'has_original',
            //            'is_remote_gif',
            'is_image'                   => $this->image ? true : false,
            //            'is_video',
            'is_document'                => $this->image ? false : true,
        ]);

        $this->file->save();

        if ($this->image) {
            $this->createImageVariants();
        }

        return $this->file;
    }

    private function getImageAttributes($image)
    {
        $this->attributes['height']    = $image->height();
        $this->attributes['width']     = $image->width();
        $this->attributes['file_type'] = $this->image->toJpeg(90)->mimetype();
        // $this->attributes['file_size'] = $image->filesize(); -- Could do this, but we already use mb_strlen() to get the file size above and it seems to work.
    }

    private function createImageVariants()
    {
        try {
            // 1024px
            if ($this->image->width() > 1024) {
                // Create our filname specific to this size.
                $fn_1024 = $this->file->file_name . '--1024';

                // PUT the file. Store as public.
                Storage::disk((new File())->storage_disk)
                    ->put(
                        $this->file->file_folder . '/' . $fn_1024 . '.' . $this->file->file_extension,
                        $this->image->scale(width: 1024)->toJpeg(90)->__toString(),
                        'public'
                    );

                // Save this filename to our file.
                $this->file->has_1024 = $fn_1024;
                $this->file->save();
            }
            // 512px
            if ($this->image->width() > 512) {
                // Create our filname specific to this size.
                $fn_512 = $this->file->file_name . '--512';

                // PUT the file. Store as public.
                Storage::disk((new File())->storage_disk)
                    ->put(
                        $this->file->file_folder . '/' . $fn_512 . '.' . $this->file->file_extension,
                        $this->image->scale(width: 512)->toJpeg(90)->__toString(),
                        'public'
                    );

                // Save this filename to our file.
                $this->file->has_512 = $fn_512;
                $this->file->save();
            }
            // 256px
            if ($this->image->width() > 256) {
                // Create our filname specific to this size.
                $fn_256 = $this->file->file_name . '--256';

                // PUT the file. Store as public.
                Storage::disk((new File())->storage_disk)
                    ->put(
                        $this->file->file_folder . '/' . $fn_256 . '.' . $this->file->file_extension,
                        $this->image->scale(width: 256)->toJpeg(90)->__toString(),
                        'public'
                    );

                // Save this filename to our file.
                $this->file->has_256 = $fn_256;
                $this->file->save();
            }
        } catch (\Exception $e) {
            Log::error($e);
        }
    }

    private function preCheck()
    {
        if (!$this->group || !$this->account) {
            return false;
        }

        return true;
    }

    public function forAccount(Account $account)
    {
        $this->account = $account;

        return $this;
    }

    public function byUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function forGroup(Group $group)
    {
        $this->group = $group;
    }

    public function forPost(Post $post)
    {
        $this->post = $post;

        return $this;
    }

    public function forComment(Comment $comment)
    {
        $this->comment = $comment;

        return $this;
    }

    public function setFileName($file_name)
    {
        $this->file_name = $file_name;

        return $this;
    }

    public function setFileSize($file_size)
    {
        $this->file_size = $file_size;

        return $this;
    }

    public function setExtension($extension)
    {
        $this->extension = $extension;

        return $this;
    }

    public function setFileType($file_type)
    {
        $this->file_type = $file_type;

        return $this;
    }

    public function setAttributes($attributes)
    {
        $this->attributes = $attributes;

        return $this;
    }

    private function getFolder()
    {
        $folder = $this->account->id;

        if ($this->group) {
            $folder .= '/' . $this->group->id;
        }

        if ($this->group && $this->post) {
            $folder .= '/' . $this->post->id;
        }

        return $folder;
    }

    private function mime2ext($mime)
    {
        $mime_map = [
            'video/3gpp2'                                                               => '3g2',
            'video/3gp'                                                                 => '3gp',
            'video/3gpp'                                                                => '3gp',
            'application/x-compressed'                                                  => '7zip',
            'audio/x-acc'                                                               => 'aac',
            'audio/ac3'                                                                 => 'ac3',
            'application/postscript'                                                    => 'ai',
            'audio/x-aiff'                                                              => 'aif',
            'audio/aiff'                                                                => 'aif',
            'audio/x-au'                                                                => 'au',
            'video/x-msvideo'                                                           => 'avi',
            'video/msvideo'                                                             => 'avi',
            'video/avi'                                                                 => 'avi',
            'application/x-troff-msvideo'                                               => 'avi',
            'application/macbinary'                                                     => 'bin',
            'application/mac-binary'                                                    => 'bin',
            'application/x-binary'                                                      => 'bin',
            'application/x-macbinary'                                                   => 'bin',
            'image/bmp'                                                                 => 'bmp',
            'image/x-bmp'                                                               => 'bmp',
            'image/x-bitmap'                                                            => 'bmp',
            'image/x-xbitmap'                                                           => 'bmp',
            'image/x-win-bitmap'                                                        => 'bmp',
            'image/x-windows-bmp'                                                       => 'bmp',
            'image/ms-bmp'                                                              => 'bmp',
            'image/x-ms-bmp'                                                            => 'bmp',
            'application/bmp'                                                           => 'bmp',
            'application/x-bmp'                                                         => 'bmp',
            'application/x-win-bitmap'                                                  => 'bmp',
            'application/cdr'                                                           => 'cdr',
            'application/coreldraw'                                                     => 'cdr',
            'application/x-cdr'                                                         => 'cdr',
            'application/x-coreldraw'                                                   => 'cdr',
            'image/cdr'                                                                 => 'cdr',
            'image/x-cdr'                                                               => 'cdr',
            'zz-application/zz-winassoc-cdr'                                            => 'cdr',
            'application/mac-compactpro'                                                => 'cpt',
            'application/pkix-crl'                                                      => 'crl',
            'application/pkcs-crl'                                                      => 'crl',
            'application/x-x509-ca-cert'                                                => 'crt',
            'application/pkix-cert'                                                     => 'crt',
            'text/css'                                                                  => 'css',
            'text/x-comma-separated-values'                                             => 'csv',
            'text/comma-separated-values'                                               => 'csv',
            'application/vnd.msexcel'                                                   => 'csv',
            'application/x-director'                                                    => 'dcr',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'   => 'docx',
            'application/x-dvi'                                                         => 'dvi',
            'message/rfc822'                                                            => 'eml',
            'application/x-msdownload'                                                  => 'exe',
            'video/x-f4v'                                                               => 'f4v',
            'audio/x-flac'                                                              => 'flac',
            'video/x-flv'                                                               => 'flv',
            'image/gif'                                                                 => 'gif',
            'application/gpg-keys'                                                      => 'gpg',
            'application/x-gtar'                                                        => 'gtar',
            'application/x-gzip'                                                        => 'gzip',
            'application/mac-binhex40'                                                  => 'hqx',
            'application/mac-binhex'                                                    => 'hqx',
            'application/x-binhex40'                                                    => 'hqx',
            'application/x-mac-binhex40'                                                => 'hqx',
            'text/html'                                                                 => 'html',
            'image/x-icon'                                                              => 'ico',
            'image/x-ico'                                                               => 'ico',
            'image/vnd.microsoft.icon'                                                  => 'ico',
            'text/calendar'                                                             => 'ics',
            'application/java-archive'                                                  => 'jar',
            'application/x-java-application'                                            => 'jar',
            'application/x-jar'                                                         => 'jar',
            'image/jp2'                                                                 => 'jp2',
            'video/mj2'                                                                 => 'jp2',
            'image/jpx'                                                                 => 'jp2',
            'image/jpm'                                                                 => 'jp2',
            'image/jpeg'                                                                => 'jpg',
            'image/pjpeg'                                                               => 'jpg',
            'application/x-javascript'                                                  => 'js',
            'application/json'                                                          => 'json',
            'text/json'                                                                 => 'json',
            'application/vnd.google-earth.kml+xml'                                      => 'kml',
            'application/vnd.google-earth.kmz'                                          => 'kmz',
            'text/x-log'                                                                => 'log',
            'audio/x-m4a'                                                               => 'm4a',
            'audio/mp4'                                                                 => 'm4a',
            'application/vnd.mpegurl'                                                   => 'm4u',
            'audio/midi'                                                                => 'mid',
            'application/vnd.mif'                                                       => 'mif',
            'video/quicktime'                                                           => 'mov',
            'video/x-sgi-movie'                                                         => 'movie',
            'audio/mpeg'                                                                => 'mp3',
            'audio/mpg'                                                                 => 'mp3',
            'audio/mpeg3'                                                               => 'mp3',
            'audio/mp3'                                                                 => 'mp3',
            'video/mp4'                                                                 => 'mp4',
            'video/mpeg'                                                                => 'mpeg',
            'application/oda'                                                           => 'oda',
            'audio/ogg'                                                                 => 'ogg',
            'video/ogg'                                                                 => 'ogg',
            'application/ogg'                                                           => 'ogg',
            'font/otf'                                                                  => 'otf',
            'application/x-pkcs10'                                                      => 'p10',
            'application/pkcs10'                                                        => 'p10',
            'application/x-pkcs12'                                                      => 'p12',
            'application/x-pkcs7-signature'                                             => 'p7a',
            'application/pkcs7-mime'                                                    => 'p7c',
            'application/x-pkcs7-mime'                                                  => 'p7c',
            'application/x-pkcs7-certreqresp'                                           => 'p7r',
            'application/pkcs7-signature'                                               => 'p7s',
            'application/pdf'                                                           => 'pdf',
            'application/octet-stream'                                                  => 'pdf',
            'application/x-x509-user-cert'                                              => 'pem',
            'application/x-pem-file'                                                    => 'pem',
            'application/pgp'                                                           => 'pgp',
            'application/x-httpd-php'                                                   => 'php',
            'application/php'                                                           => 'php',
            'application/x-php'                                                         => 'php',
            'text/php'                                                                  => 'php',
            'text/x-php'                                                                => 'php',
            'application/x-httpd-php-source'                                            => 'php',
            'image/png'                                                                 => 'png',
            'image/x-png'                                                               => 'png',
            'application/powerpoint'                                                    => 'ppt',
            'application/vnd.ms-powerpoint'                                             => 'ppt',
            'application/vnd.ms-office'                                                 => 'ppt',
            'application/msword'                                                        => 'doc',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
            'application/x-photoshop'                                                   => 'psd',
            'image/vnd.adobe.photoshop'                                                 => 'psd',
            'audio/x-realaudio'                                                         => 'ra',
            'audio/x-pn-realaudio'                                                      => 'ram',
            'application/x-rar'                                                         => 'rar',
            'application/rar'                                                           => 'rar',
            'application/x-rar-compressed'                                              => 'rar',
            'audio/x-pn-realaudio-plugin'                                               => 'rpm',
            'application/x-pkcs7'                                                       => 'rsa',
            'text/rtf'                                                                  => 'rtf',
            'text/richtext'                                                             => 'rtx',
            'video/vnd.rn-realvideo'                                                    => 'rv',
            'application/x-stuffit'                                                     => 'sit',
            'application/smil'                                                          => 'smil',
            'text/srt'                                                                  => 'srt',
            'image/svg+xml'                                                             => 'svg',
            'application/x-shockwave-flash'                                             => 'swf',
            'application/x-tar'                                                         => 'tar',
            'application/x-gzip-compressed'                                             => 'tgz',
            'image/tiff'                                                                => 'tiff',
            'font/ttf'                                                                  => 'ttf',
            'text/plain'                                                                => 'txt',
            'text/x-vcard'                                                              => 'vcf',
            'application/videolan'                                                      => 'vlc',
            'text/vtt'                                                                  => 'vtt',
            'audio/x-wav'                                                               => 'wav',
            'audio/wave'                                                                => 'wav',
            'audio/wav'                                                                 => 'wav',
            'application/wbxml'                                                         => 'wbxml',
            'video/webm'                                                                => 'webm',
            'image/webp'                                                                => 'webp',
            'audio/x-ms-wma'                                                            => 'wma',
            'application/wmlc'                                                          => 'wmlc',
            'video/x-ms-wmv'                                                            => 'wmv',
            'video/x-ms-asf'                                                            => 'wmv',
            'font/woff'                                                                 => 'woff',
            'font/woff2'                                                                => 'woff2',
            'application/xhtml+xml'                                                     => 'xhtml',
            'application/excel'                                                         => 'xl',
            'application/msexcel'                                                       => 'xls',
            'application/x-msexcel'                                                     => 'xls',
            'application/x-ms-excel'                                                    => 'xls',
            'application/x-excel'                                                       => 'xls',
            'application/x-dos_ms_excel'                                                => 'xls',
            'application/xls'                                                           => 'xls',
            'application/x-xls'                                                         => 'xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'         => 'xlsx',
            'application/vnd.ms-excel'                                                  => 'xlsx',
            'application/xml'                                                           => 'xml',
            'text/xml'                                                                  => 'xml',
            'text/xsl'                                                                  => 'xsl',
            'application/xspf+xml'                                                      => 'xspf',
            'application/x-compress'                                                    => 'z',
            'application/x-zip'                                                         => 'zip',
            'application/zip'                                                           => 'zip',
            'application/x-zip-compressed'                                              => 'zip',
            'application/s-compressed'                                                  => 'zip',
            'multipart/x-zip'                                                           => 'zip',
            'text/x-scriptzsh'                                                          => 'zsh',
        ];

        return isset($mime_map[$mime]) ? $mime_map[$mime] : null;
    }
}
