<?php

namespace App\Groups;

use App\Base\Models\Model;
use App\Users\User;

class Reaction extends Model
{
    protected $table = 'user_group_post_reactions';

    const UPDATED_AT = null;

    protected $casts = [
        'created_at' => 'datetime',
    ];

    protected $fillable = [
        'user_group_post_id',
        'user_group_post_comment_id',
        'user_id',
        'created_at',
        'is_like',
        'is_love',
        'is_care',
        'is_pray',
        'is_laugh',
        'is_flag',
    ];

    public static $types = [
        'is_like',
        'is_love',
        'is_care',
        'is_pray',
        'is_laugh',
        'is_flag',
    ];

    public function post()
    {
        return $this->belongsTo(Post::class, 'user_group_post_id');
    }

    public function comment()
    {
        return $this->belongsTo(Comment::class, 'user_group_post_comment_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
