<?php

namespace App\Groups;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\Group;
use App\Users\User;

class Notification extends Model
{
    protected $table = 'user_group_notifications';

    public const UPDATED_AT = null;

    protected $casts = [
        'created_at' => 'datetime',
        'read_at'    => 'datetime',
    ];

    protected $fillable = [
        'created_at',
        'user_id',
        'user_group_id',
        'user_group_post_id',
        'message',
        'type',
        'read_at',
    ];

    public static $types = [
        'new_post'    => 'New Group Post',
        'new_comment' => 'New Post Comment',
    ];

    public static function getTypeKeys()
    {
        return array_keys(self::$types);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'user_group_id');
    }

    public function since($query, $since_date)
    {
        return $query->where('created_at', '>=', $since_date);
    }

    public function scopeIsRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeIsUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeIsType($query, $type)
    {
        return $query->where('type', $type);
    }
}
