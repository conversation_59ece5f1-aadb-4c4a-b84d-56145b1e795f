<?php

namespace App\Groups\Policies;

use App\Groups\Comment;
use App\Groups\Post;
use App\Users\User;

class CommentPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.groups')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('groups.index');
    }

    public function create(User $user)
    {
        return $user->hasPermission('groups.manage');
    }

    public function store(User $user)
    {
        return true;
        // return $user->hasPermission('groups.manage');
    }

    public function edit(User $user, Comment $comment)
    {
        return $user->hasPermission('groups.manage')
               || $user->isGroupAdmin($comment->group)
               || $user->id === $comment->creator_id;
    }

    public function save(User $user, Post $post)
    {
        return $user->hasPermission('groups.manage');
    }

    public function view(User $user)
    {
        return $user->hasPermission('groups.index');
    }

    public function delete(User $user, Comment $comment)
    {
        return $user->hasPermission('groups.manage')
               || $user->isGroupAdmin($comment->group)
               || $user->id === $comment->creator_id;
    }

    public function like(User $user, Comment $comment)
    {
        return ($comment->group->isViewableToUser($user));
    }
}