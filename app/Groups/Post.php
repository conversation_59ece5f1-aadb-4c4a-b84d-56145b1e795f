<?php

namespace App\Groups;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Messages\MessageType;
use App\Users\Group;
use App\Users\User;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\SoftDeletes;

class Post extends Model
{
    use SoftDeletes;

    protected $table = 'user_group_posts';

    protected $casts = [
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
        'deleted_at'     => 'datetime',
        'published_at'   => 'datetime',
        'last_edited_at' => 'datetime',
        'last_active_at' => 'datetime',
        'is_pinned'      => 'boolean',
    ];

    protected $fillable = [
        'id',
        'account_id',
        'user_group_id',
        'user_group_post_id',
        'creator_id',
        'published_at',
        'last_edited_at',
        'last_active_at',
        'title',
        'url_title',
        'content',
        'calendar_event_occurrence_id',
        'link',
        'poll_question',
        'poll_options',
        'poll_responses',
        'allow_comments',
        'is_shared',
        'is_pinned',
        'is_hidden',
        'is_flagged',
        'is_poll',
        'is_link',
        'is_rsvp',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeViewableToUser($query, User $user)
    {
        // Posts must be enabled
        return $query->visibleTo($user)
            ->whereHas('group', function ($query) use ($user) {
                $query->viewableToUser($user);
            });
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'user_group_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function files()
    {
        return $this->hasMany(File::class, 'user_group_post_id');
    }

    public function documents()
    {
        return $this->hasMany(File::class, 'user_group_post_id')
            ->where('is_document', true);
    }

    public function images()
    {
        return $this->hasMany(File::class, 'user_group_post_id')
            ->where('is_image', true);
    }

    public function parent()
    {
        return $this->belongsTo(Post::class, 'user_group_post_id');
    }

    public function comments()
    {
        return $this->hasMany(Comment::class, 'user_group_post_id')
            ->whereHas('creator')
            ->orderBy('created_at', 'asc');
    }

    public function reactions($type = null)
    {
        return $this->hasMany(Reaction::class, 'user_group_post_id')
            ->whereNull('user_group_post_comment_id')
            ->when(($type && in_array('is_' . $type, [Reaction::$types])), function ($query) use ($type) {
                $query->where('is_' . $type, 1);
            });
    }

    public function reactionForUser($user_id)
    {
        return $this->hasMany(Reaction::class, 'user_group_post_id')
            ->whereNull('user_group_post_comment_id')
            ->where('user_id', $user_id);
    }

    public function likes()
    {
        return $this->hasMany(Reaction::class, 'user_group_post_id')
            ->whereNull('user_group_post_comment_id')
            ->where('is_like', 1);
    }

    public function loves()
    {
        return $this->hasMany(Reaction::class, 'user_group_post_id')
            ->whereNull('user_group_post_comment_id')
            ->where('is_love', 1);
    }

    public function cares()
    {
        return $this->hasMany(Reaction::class, 'user_group_post_id')
            ->whereNull('user_group_post_comment_id')
            ->where('is_care', 1);
    }

    public function prays()
    {
        return $this->hasMany(Reaction::class, 'user_group_post_id')
            ->whereNull('user_group_post_comment_id')
            ->where('is_pray', 1);
    }

    public function laughs()
    {
        return $this->hasMany(Reaction::class, 'user_group_post_id')
            ->whereNull('user_group_post_comment_id')
            ->where('is_laugh', 1);
    }

    public function scopeWithReactionCounts($query)
    {
        return $query->withCount([
            'reactions',
            'likes',
            'loves',
            'cares',
            'prays',
            'laughs',
        ]);
    }

    public function hasMessageType($message_type_id)
    {
        return $this->messageTypes->where('id', $message_type_id)->isNotEmpty();
    }

    public function notifications()
    {
        return $this->hasMany(\App\Groups\Notification::class, 'user_group_post_id');
    }

    public function scopeWithUnreadNotificationCountForUser($query, $user_id)
    {
        return $query->withCount([
            'notifications as unread_notifications_count' => function ($query) use ($user_id) {
                $query->where('user_id', $user_id)
                    ->where('is_read', false);
            },
        ]);
    }

    public function availableMessageTypes()
    {
        return MessageType::whereIn('id', $this->receivers()->distinct('message_type_id')->select('message_type_id')->get()->toArray())->get();
    }

    public function getPublishedAtForHumans($timezone)
    {
        $now = Carbon::now();

        if (!$timezone) {
            $timezone = $this->account->timezone;
        }

        if ($this->published_at->diffInDays($now) > 4) {
            return $this->published_at->setTimezone($timezone)->format('F j \a\t g:ia');
        } elseif ($this->published_at->diffInDays($now) > 1) {
            return ucfirst($this->published_at->diffForHumans(['options' => 0])) . ' at ' . $this->published_at->format('g:ia');
        } elseif ($now->diffInDays($this->published_at) > 0) {
            return ucfirst($this->published_at->diffForHumans(['options' => Carbon::ONE_DAY_WORDS])) . ' at ' . $this->published_at->format('g:ia');
        } elseif ($now->diffInDays($this->published_at) == 0) {
            return $this->published_at->diffForHumans($now, ['parts' => 1, 'syntax' => CarbonInterface::DIFF_RELATIVE_TO_NOW]);
        }
    }

    public function scopeAllowsIndividualToToggle($query)
    {
        return $query->where('allow_individual_to_toggle', true);
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeOrdered($query)
    {
        return $query
            ->orderByDesc('is_pinned')
            ->orderByDesc('published_at');
    }

    public function allowIndividualToggle()
    {
        return boolval($this->allow_individual_to_toggle);
    }

    public function isHidden()
    {
        return boolval($this->is_hidden);
    }

    public function isSuspended()
    {
        return boolval($this->is_suspended);
    }
}
