<?php

namespace App\Listeners\Groups;

use App\Events\Groups\PostCreatedBroadcast;
use App\Jobs\SendMobileNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SendPostCreatedNotifications implements ShouldQueue
{
    use InteractsWithQueue;

    public $mobile_notification_type = 'GP_new';
    public $queue                    = 'mobile';
    public $tries                    = 1;
    public $event;
    public $group;
    public $post;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param PostCreatedBroadcast $event
     *
     * @return void
     */
    public function handle(PostCreatedBroadcast $event)
    {
        // The EVENT === POST -- now sure how/why this works like this... but leaving it for now.
        $this->event = $event;
        $this->post  = $event->post;
        $this->group = $event->post->group;

        // Send as mobile notification first.
        foreach ($this->getMobileNotificationUsers() as $user) {
            // Don't send to the user that created it -- only in production.
            if (!config('app.env') == 'production' || $user->id !== $this->post->creator_id) {
                $this->sendNotification($user);
            }
        }
    }

    public function getMobileNotificationUsers()
    {
        return $this->group->users()
            ->wherePivot('receive_group_post_mobile_notifications', true)
            ->get();
    }

    public function sendNotification($user)
    {
        SendMobileNotification::dispatch(
            $user,
            $this->post->creator->display_first_name . ' ' . Str::limit($this->post->creator->last_name, 1, null) . ' (' . $this->group->name . ')',
            Str::limit($this->post->content, 140),
            [
                'type'   => $this->mobile_notification_type,
                'p_t'    => 'user_groups',
                'p_t_id' => $this->post->user_group_id,
                't'      => 'user_group_posts',
                't_id'   => $this->post->id,
            ],
            $user->unreadGroupNotifications()->count()
        )->onQueue('mobile');
    }

    /**
     * Handle a job failure.
     *
     * @param PostCreatedBroadcast $event
     *
     * @return void
     */
    public function failed(PostCreatedBroadcast $event, $exception)
    {
        Log::error($exception);
    }
}
