<?php

namespace App\Listeners\Groups;

use App\Events\Groups\CommentCreatedBroadcast;
use App\Jobs\SendMobileNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SendCommentCreatedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    public $mobile_notification_type = 'GPC_new';
    public $queue                    = 'mobile';
    public $tries                    = 1;
    public $comment;
    public $group;
    public $post;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param CommentCreatedBroadcast $event
     *
     * @return void
     */
    public function handle(CommentCreatedBroadcast $event)
    {
//        $this->event = $event;
        $this->comment = $event->comment;
        $this->post    = $event->comment->post;
        $this->group   = $event->comment->post->group;

        // Send as mobile notification first.
        foreach ($this->getGroupUsers() as $user) {
            // Don't send to the user that created it -- only in production.
            if (!config('app.env') == 'production' || $user->id !== $this->comment->creator_id) {
                $settings = $user->groups->where('id', $this->group->id)->first()->settings;

                if (
                    // If we want to receive comments to our OWN posts, make sure this isn't OUR COMMENT to our OWN POST, or our OWN COMMENT.
                    (
                        $settings->receive_group_own_post_comment_mobile_notifications
                        && $user->id == $this->post?->creator_id
                        && $user->id !== $this->comment?->creator_id
                    )
                    ||
                    // OR -- If we want to receive ANY comment to a post, make sure this isn't OuR OWN COMMENT to a post.
                    (
                        $settings->receive_all_group_post_comment_mobile_notifications
                        && $user->id !== $this->comment?->creator_id
                    )
                ) {
                    $this->sendNotification($user);
                }
            }
        }
    }

    public function getGroupUsers()
    {
        return $this->group->users()->get();
    }

    public function sendNotification($user)
    {
        SendMobileNotification::dispatch(
            $user,
            'New comment by ' . $this->comment->creator->display_first_name . ' ' . Str::limit($this->comment->creator->last_name, 1, null) . ' (' . $this->group->name . ')',
            Str::limit($this->comment->content, 140),
            [
                'type'   => $this->mobile_notification_type,
                'p_t'    => 'user_groups',
                'p_t_id' => $this->post->user_group_id,
                't'      => 'user_group_posts',
                't_id'   => $this->post->id,
            ],
            $user->unreadGroupNotifications()->count() // Do NOT enable until most people are upgraded to Mobile App v4.3
        )->onQueue('mobile');
    }

    /**
     * Handle a job failure.
     *
     * @param CommentCreatedBroadcast $event
     * @param                         $exception
     *
     * @return void
     */
    public function failed(CommentCreatedBroadcast $event, $exception)
    {
        Log::error($exception);
    }
}
