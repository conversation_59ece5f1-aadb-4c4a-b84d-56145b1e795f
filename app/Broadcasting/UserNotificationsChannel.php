<?php

namespace App\Broadcasting;

use App\Users\User;

class UserNotificationsChannel
{
    /**
     * Create a new channel instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Authenticate the user's access to the channel.
     *
     * @param \App\Users\User $user
     * @param                 $account_id
     *
     * @return array|bool
     */
    public function join(User $user, $account_id, $user_id)
    {
        return ($user->account_id == $account_id && $user->id === $user_id);
    }
}
