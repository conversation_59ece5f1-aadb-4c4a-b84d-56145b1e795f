<?php

namespace App\Broadcasting;

use App\Users\User;

class GroupPostCommentsChannel
{
    /**
     * Create a new channel instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Authenticate the user's access to the channel.
     *
     * @param \App\Users\User $user
     * @param                 $account_id
     *
     * @return array|bool
     */
    public function join(User $user, $account_id, $user_group_id = null)
    {
        return $user->account_id == $account_id && ($user_group_id === null || $user->isGroup($user_group_id));
    }
}
