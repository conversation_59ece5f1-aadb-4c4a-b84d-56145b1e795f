<?php

namespace App\Broadcasting\Programs\Users;

use App\Programs\Program;
use App\Users\User;

class ProgramUserCheckinsChannel
{
    /**
     * Create a new channel instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Authenticate the user's access to the channel.
     *
     * @param \App\Users\User $user
     * @param                 $account_id
     *
     * @return array|bool
     */
    public function join(User $user, $account_id, $program_id)
    {
        $program = Program::find($program_id);

        if (!$program) {
            return false;
        }

        return $user->account_id == $account_id
               && $account_id == $program->account_id
               && $user->can('view', $program);
    }
}
