<?php

namespace App\Sermons\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Sermons\Tag;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Str;
use Illuminate\Pagination\Paginator;

class SermonTagController extends Controller
{
    public function index()
    {
        Paginator::useTailwind();
        return view('admin.sermons.tags.index')->with([
            'tags' => Tag::visibleTo(Auth::user())->paginate(50),
        ]);
    }

    public function create()
    {
        return view('admin.sermons.tags.create');
    }

    public function store()
    {
        try {
            $tag = DB::transaction(function () {

                return Tag::create([
                    'name'        => request()->get('name'),
                    'description' => request()->get('description'),
                    'is_public'   => request()->get('is_public'),
                    'is_hidden'   => request()->get('is_hidden'),
                    'slug'        => Str::slug(request()->get('name')),
                    'account_id'  => auth()->user()->account_id,
                ]);
            });

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.sermons.tags.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Tag $tag)
    {
        return view('admin.sermons.tags.edit')
            ->with('tag', $tag);
    }

    public function save(Tag $tag)
    {
        try {

            $tag->fill(request()->all())->update();

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.sermons.tags.index')
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Tag $tag)
    {
        $tag->delete();

        return redirect(route('admin.sermons.tags.index'))
            ->with('message.success', 'Tag deleted.');
    }
}
