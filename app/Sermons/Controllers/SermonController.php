<?php

namespace App\Sermons\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Jobs\Podcasts\SyncPodcastSermons;
use App\Podcasts\Services\UpdateTracksFromSermon;
use App\Sermons\Requests\CreateSermonRequest;
use App\Sermons\Requests\UpdateSermonRequest;
use App\Sermons\Sermon;
use App\Sermons\Services\CreateSermon;
use App\Sermons\Services\CreateSermonFile;
use App\Sermons\Services\DeleteSermon;
use App\Sermons\Services\UpdateSermon;
use App\Sermons\Services\UpdateSermonFile;
use App\Sermons\Tag;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Log;
use Illuminate\Pagination\Paginator;

class SermonController extends Controller
{

    public function index()
    {
        Paginator::useTailwind();

        return view('admin.sermons.index')->with([
            'sermons' => Sermon::visibleTo(auth()->user())
                ->withCount('files')
                ->orderBy('date_sermon', 'DESC')
                ->paginate(15),
        ]);
    }

    public function search()
    {
        Paginator::useTailwind();

        return view('admin.sermons.index')->with([
            'sermons' => Sermon::visibleTo(auth()->user())
                ->where('title', 'like', '%' . request('title') . '%')
                ->withCount('files')
                ->orderBy('id', 'DESC')
                ->paginate(15),
        ]);
    }

    public function show(Sermon $sermon)
    {
        Paginator::useTailwind();

        return view('admin.sermons.show')->with([
            'sermon' => $sermon,
        ]);
    }

    public function create()
    {
        return view('admin.sermons.create')
            ->with('tags', Tag::visibleTo(auth()->user())->get());
    }

    public function store(CreateSermonRequest $request)
    {
        try {
            $sermon = DB::transaction(function () use ($request) {
                return (new CreateSermon())->create(
                    array_merge(
                        $request->all(),
                        [
                            'account_id'          => auth()->user()->account->id,
                            'account_location_id' => auth()->user()->account->location ? auth()->user()->account->location->id : null,
                        ]
                    )
                );
            });

            if (request()->file('user_file')) {
                try {
                    (new CreateSermonFile($sermon))->forUser(auth()->user())->create(request()->file('user_file'), request()->get('file_title'));
                } catch (\Exception $e) {
                    Log::error('Sermon Create Error', [
                        'message' => $e->getMessage(),
                        'trace'   => $e->getTraceAsString(),
                    ]);

                    return back()
                        ->with('message.success', 'The sermon was created successfully!  There was an error saving the file.  Please try updating the sermon with a new file.')
                        ->with('message.failure', $e->getMessage());
                }
            }
        } catch (\Exception $e) {
            Log::error('Sermon Create Error', [
                'message' => $e->getMessage(),
                'trace'   => $e->getTraceAsString(),
            ]);

            return back()->with('message.failure', $e->getMessage());
        }

        // If this Sermon has tags, sync our Podcasts
        if ($sermon->tags()->count() > 0) {
            SyncPodcastSermons::dispatch()
                ->delay(now()->addSeconds(3));
        }

        return redirect(route('admin.sermons.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Sermon $sermon)
    {
        return view('admin.sermons.edit')
            ->withSermon($sermon)
            ->with('tags', Tag::visibleTo(auth()->user())->get());
    }

    public function save(UpdateSermonRequest $request, Sermon $sermon)
    {
        try {
            $sermon = DB::transaction(function () use ($request, $sermon) {
                return (new UpdateSermon($sermon))
                    ->syncTags()
                    ->update($request->all());
            });

            if (request()->file('user_file')) {
                try {
                    (new UpdateSermonFile($sermon))->forUser(auth()->user())->update(request()->file('user_file'), request()->get('file_title'));
                } catch (\Exception $e) {
                    return back()
                        ->with('message.success', 'The sermon was updated successfully!  There was an error saving the file.  Please try updating the sermon with a new file.')
                        ->with('message.failure', $e->getMessage());
                }
            }

            (new UpdateTracksFromSermon())
                ->forSermon($sermon)
                ->update();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        // If this Sermon has tags, sync our Podcasts
        if ($sermon->tags()->count() > 0) {
            SyncPodcastSermons::dispatch()
                ->delay(now()->addSeconds(3));
        }

        return redirect()
            ->route('admin.sermons.index')
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Sermon $sermon)
    {
        (new DeleteSermon($sermon))->delete();

        return redirect(route('admin.sermons.index'))
            ->with('message.success', 'Sermon and files deleted.');
    }
}
