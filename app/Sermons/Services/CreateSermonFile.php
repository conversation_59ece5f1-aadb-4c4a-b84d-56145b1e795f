<?php

namespace App\Sermons\Services;

use App\Sermons\File;
use App\Sermons\Sermon;
use App\Users\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class CreateSermonFile
{
    protected $attributes = [];
    protected $sermon;
    protected $file;
    protected $user;

    public function __construct(Sermon $sermon)
    {
        $this->sermon = $sermon;
    }

    public function forUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * @param UploadedFile $uploaded_file From request()->file() on the frontend
     * @param              $file_title
     *
     * @return File
     * @throws \Exception
     */
    public function create(UploadedFile $uploaded_file, $file_title)
    {
        if ($uploaded_file->isValid()) {
            // $folder    = Config::get('app.user_image_file_path');
            $extension = '.' . $uploaded_file->getClientOriginalExtension();

            $new_file_name                   = (new File())->sanitizeFilename($this->user->account->id . '--' . Str::random(4) . '--' . $uploaded_file->getClientOriginalName());
            $new_file_name_without_extension = str_replace($extension, '', $new_file_name);

            if (!$uploaded_file->storePubliclyAs(
                $this->user->account->id,
                $new_file_name,
                'sermon-files'
            )) {
                throw new \Exception('Could not write file to cloud server.');
            }

            $this->file = new File();

            $this->file->fill([
                'sermon_id'          => $this->sermon->id,
                'title'              => $file_title ?? $this->sermon->title,
                'url_title'          => \Illuminate\Support\Str::slug($file_title ?? $this->sermon->title, '-'),
                'storage_service'    => 'do-spaces',
                'data_separator'     => '--',
                'file_original_name' => $uploaded_file->getClientOriginalName(),
                'file_size'          => $uploaded_file->getSize(),
                'file_folder'        => $this->user->account->id,
                'file_id'            => null,
                'file_name'          => $new_file_name_without_extension,
                'file_extension'     => $uploaded_file->getClientOriginalExtension(),
                'file_type'          => $uploaded_file->getClientMimeType(),
                'file_sha1'          => sha1(file_get_contents($uploaded_file->getRealPath())),
            ]);

            $this->file->save();
        } else {
            throw new \Exception('File size was zero.');
        }

        return $this->file;
    }
}
