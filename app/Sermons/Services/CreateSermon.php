<?php

namespace App\Sermons\Services;

use App\Sermons\Sermon;
use Illuminate\Support\Arr;
use Madcoda\Youtube\Youtube;

class CreateSermon
{
    protected $attributes = [];
    protected $sermon;

    public function create($attributes): Sermon
    {
        $this->attributes = $attributes;

        $this->sermon = Sermon::create($this->attributes);

        if (Arr::has($this->attributes, 'tags')) {
            $this->sermon->tags()->sync($this->attributes['tags']);
        }

        $this->getYoutubeData();

        return $this->sermon;
    }

    private function getYoutubeData()
    {
        if ($this->sermon->youtube_id) {
            $youtube = new Youtube(['key' => config('app.keys.youtube_content_api_key')]);

            $video = $youtube->getVideoInfo($this->sermon->youtube_id);

            if ($video) {
                $this->sermon->youtube_data = json_encode($video);

                $this->sermon->save();
            }
        }
    }
}
