<?php

namespace App\Sermons;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class File extends Model
{
    use SoftDeletes;

    protected $table = 'sermon_files';

    protected $dates = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'sermon_id',
        'title',
        'url_title',
        'type',
        'storage_service',
        'file_original_name',
        'file_size',
        'data_separator',
        'file_folder',
        'file_id',
        'file_name',
        'file_extension',
        'file_type',
        'file_sha1',
        'duration',
        'duration_in_seconds',
    ];

    public static $audio_file_types = [
        'audio/mpeg',
        'audio/mp3',
        'audio/x-mpeg',
        'audio/x-mpeg-3',
        'audio/mpeg3',
        'audio/x-mpeg3',
        'audio/mpg',
        'audio/x-mpg',
        'audio/x-mpegaudio',
        'audio/mp4',
        'audio/x-mp4',
        'audio/ogg',
        'audio/wav',
        'audio/x-wav',
        'audio/wave',
        'audio/x-pn-wav',
        'audio/x-ms-wax',
        'audio/x-ms-wma',
        'audio/x-flac',
        'audio/flac',
        'audio/x-aiff',
        'audio/aiff',
        'audio/aac',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return;
    }

    public function sermon()
    {
        return $this->belongsTo(Sermon::class);
    }

    public function getFileName()
    {
        return $this->file_name . '.' . $this->file_extension;
    }

    public function getFullUrlMySQLConcatString()
    {
        if (config('filesystems.disks.sermon-files.cdn_endpoint')) {
            return "CONCAT('" . config('filesystems.disks.sermon-files.cdn_endpoint') . '/' . "', file_folder, '/', file_name, '.', file_extension)";
        } else {
            return "CONCAT('" . config('filesystems.disks.sermon-files.url_endpoint') . '/' . "', file_folder, '/', file_name, '.', file_extension)";
        }
    }

    public function getUrl()
    {
        return config('filesystems.disks.sermon-files.url_endpoint') . '/' . $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
    }

    public function getCdnUrl()
    {
        return config('filesystems.disks.sermon-files.cdn_endpoint') . '/' . $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
    }

    public function deleteFile()
    {
        return Storage::disk('sermon-files')->delete($this->file_folder . '/' . $this->file_name . '.' . $this->file_extension);
    }

    public function sanitizeFilename($filename)
    {
        // Allow UTF-8 for now, since Linode Object Store supports it.
        // $filename = Str::ascii($filename);

        // Remove characters that cause problems with cloud provides (Linode Object Store)
        $filename = Str::of($filename)->replaceMatches("/['\"\%\/\<\>&+=]+/", '');

        // Replace other things with an underscore.
        return Str::of($filename)->replaceMatches('|[^a-zA-Z0-9_.-]|', '_');
    }
}
