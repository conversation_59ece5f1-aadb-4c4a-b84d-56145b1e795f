<?php

namespace App\Sermons\Requests;

use App\Base\Http\Request;

class UpdateSermonRequest extends Request
{
    public function rules()
    {
        $rules = [
            'date_sermon' => 'required|date',
            'title'       => 'required|string|max:160',
            'description' => 'nullable|sometimes|string',
            'speaker'     => 'nullable|sometimes|string',
            'type'        => 'nullable|sometimes|string',
            'is_hidden'   => 'present|integer',
            'language'    => 'nullable|sometimes|string',
        ];

        return $rules;
    }
}
