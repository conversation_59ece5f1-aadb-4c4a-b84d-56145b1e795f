<?php

namespace App\Users\Traits;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

trait UserScopeTrait
{
    // First Name doesn't work here :/
    public static function getNameRawSql()
    {
        return DB::raw("CONCAT(' ', COALESCE(NULLIF(`preferred_first_name`, \'\'), `first_name`), `last_name`) as `name`");
    }

    public static function getFirstNameRawSql()
    {
        return DB::raw('COALESCE(NULLIF(`preferred_first_name`, \'\'), `first_name`) as `first_name`');
    }

    public static function getOrderByFamilyRoleRawSql()
    {
        return DB::raw("last_name asc, family_id, case family_role when 'head' then 1 when 'spouse' then 2 when 'dependent' then 3 when 'child' then 4 when 'other' then 5 end, birthdate");
    }

    public static function getOrderByFamilyRoleWithFamilyLastNameRawSql()
    {
        // This order_by expects a subselect to have been done to get the `family_last_name` added to all user results for sorting.
        return DB::raw("family_last_name asc, family_id, case family_role when 'head' then 1 when 'spouse' then 2 when 'dependent' then 3 when 'child' then 4 when 'other' then 5 end, birthdate");
    }

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('users.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    // Filters a user query down by trying to be smart about searching across first/last name given a string.
    public function scopeSearchNameByString($query, $query_string)
    {
        // In case we're searching "Drew Johnston"... separate out first and last.
        // Remove commas and periods ("Johnston, Drew"), then explode on spaces.
        //   If we find a comma or period, it will create an empty string in the array. (See $string2/$string3 below)
        $strings = explode(' ', str_replace([',', '.'], ' ', $query_string));

        // Get our separated names from the array.
        // Remove any extra spaces.
        $string1 = trim(Arr::get($strings, 0, null));
        $string2 = trim(Arr::get($strings, 1, null));
        $string3 = trim(Arr::get($strings, 2, null));

        // Adding a double space will cause the second string to be empty.
        if (empty($string2) && !empty($string3)) {
            $string2 = $string3;
        }

        return $query->where(function ($query) use ($string1, $string2, $query_string) {
            // If we have a single string, search first and last name for the same term.
            $query->when((!empty($string1) && empty($string2)), function ($query2) use ($string1) {
                // Search first and last name for the same term
                $query2->where('last_name', 'like', '%' . $string1 . '%')
                    ->orWhere('first_name', 'like', '%' . $string1 . '%')
                    ->orWhere('preferred_first_name', 'like', '%' . $string1 . '%');
            })
                ->when((!empty($string1) && !empty($string2)), function ($query) use ($string1, $string2) {
                    // Match "Drew Johnston" OR "Johnston Drew"
                    $query->where(function ($query) use ($string1, $string2) {
                        $query->where(function ($query) use ($string1) {
                            $query->where('first_name', 'like', '%' . $string1 . '%');
                            $query->orWhere('preferred_first_name', 'like', '%' . $string1 . '%');
                        })
                            ->where('last_name', 'like', '%' . $string2 . '%');
                    })->orWhere(function ($query) use ($string1, $string2) {
                        $query->where(function ($query) use ($string2) {
                            $query->where('first_name', 'like', '%' . $string2 . '%');
                            $query->orWhere('preferred_first_name', 'like', '%' . $string2 . '%');
                        })
                            ->where('last_name', 'like', '%' . $string1 . '%');
                    });
                });
        });
    }

    public function scopeBroadSearchByString($query, $query_string)
    {
        // We MUST wrap all of this in a "where", otherwise the "user.account_id = Y" will
        // fall inside a () block that will start including other account users!
        return $query->where(function ($mainQuery) use ($query_string) {
            $mainQuery->where(function ($query) use ($query_string) {
                $query->SearchNameByString($query_string);
            })->orWhereHas('emails', function ($query) use ($query_string) {
                $query->where('email', 'like', $query_string . '%');
            })->when(preg_replace('/[^0-9]/', '', $query_string), function ($query) use ($query_string) {
                $query->orWhereHas('phones', function ($query2) use ($query_string) {
                    $query2->where('number', 'like', '%' . preg_replace('/[^0-9]/', '', $query_string) . '%');
                });
            })->orWhereHas('addresses', function ($query) use ($query_string) {
                $query->where('address1', 'like', '%' . $query_string . '%');
            });
        });
    }

    public function scopeIsNotDeceased($query)
    {
        return $query->whereNull('date_deceased');
    }

    public function scopeIncludeInReports($query)
    {
        return $query->where('exclude_from_reports', '<>', 1)
            ->whereNull('date_deceased');
    }

    public function scopeAdultsOnly($query)
    {
        return $query->where('family_role', '<>', 'child');
    }

    public function scopeMenOnly($query)
    {
        return $query->where('gender', 'male');
    }

    public function scopeWomenOnly($query)
    {
        return $query->where('gender', 'female');
    }

    public function scopeCanLead($query)
    {
        return $query->where('can_lead', true);
    }

    public function scopeExcludeChildren($query, $exclude = true)
    {
        if ($exclude) {
            return $query->where('family_role', '<>', 'child');
        } else {
            return $query;
        }
    }

    public function scopeChildrenOnly($query)
    {
        return $query->where('family_role', 'child');
    }

    public function scopeExcludeUnbaptizedChildren($query)
    {
        return $query->where(function ($query) {
            $query->where('family_role', '<>', 'child')
                ->orWhere(function ($query2) {
                    $query2->where('family_role', 'child')
                        ->whereNotNull('is_baptized');
                });
        });
    }

    public function scopeMembersOnly($query, $apply = true)
    {
        if (!$apply) {
            return $query;
        }

        return $query->where(function ($query2) {
            $query2->whereHas('roles', function ($query) {
                $query->where('indicates_membership', true);
            })->orWhereHas('groups', function ($query) {
                $query->where('indicates_membership', true);
            });
        });
    }

    public function scopeNonMembersOnly($query)
    {
        return $query->where(function ($query2) {
            $query2->whereDoesntHave('groups', function ($query) {
                $query->where('indicates_membership', true);
            });
        });
    }

    public function scopeMembersAndVisitorsOnly($query)
    {
        return $query->where(function ($query2) {
            $query2->whereHas('roles', function ($query) {
                $query->where('indicates_membership', true);
            })->orWhereHas('groups', function ($query) {
                $query->where('indicates_membership', true);
            });
        })->orWhere(function ($query2) {
            $query2->whereHas('groups', function ($query) {
                $query->where('indicates_visitor', true);
            });
        });
    }

    public function scopeVisitorsOnly($query)
    {
        return $query->where(function ($query2) {
            $query2->whereHas('groups', function ($query) {
                $query->where('indicates_visitor', true);
            });
        });
    }

    public function scopeSelectedInvolvementSubarea($query, $subarea)
    {
        return $query->whereHas('involvementSubareas', function ($query) use ($subarea) {
            $query->where('involvement_subarea_id', $subarea->id);
        });
    }

    public function scopeSelectedInvolvementArea($query, $area)
    {
        return $query->whereHas('involvementAreas', function ($query) use ($area) {
            $query->where('involvement_area_id', $area->id);
        });
    }

    public function scopeSelectedInvolvementCategory($query, $category)
    {
        return $query->whereHas('involvementCategories', function ($query) use ($category) {
            $query->where('involvement_category_id', $category->id);
        });
    }

    public function scopeWithFamilyLastName($query)
    {
        return $query->addSubSelect(
            'family_last_name',
            User::from('users as users2')->select('last_name')
                ->whereColumn('family_id', 'users.family_id')// Where the users family_id is the same
                ->whereColumn('id', 'family_id') // Where the user.id == family_id (head of family)
        );
    }

    public function scopeHeadsOfFamily($query)
    {
        return $query->where('users.family_id', DB::raw('users.id'));
    }

    public function scopeIsMarried($query)
    {
        return $query->where('marital_status', 'married')
            ->whereHas('spouseFromHeadOfHousehold', function ($query) {
                $query->whereNull('date_deceased');
            });
    }

    public function scopeHasRole($query, Role $role)
    {
        return $query->roles()->where('id', $role->id);
    }

    public function scopeDoesntHaveRole($query, Role $role)
    {
        return $query->doesntHave('roles')
            ->orWhereDoesntHave('roles', function ($query) use ($role) {
                $query->where('user_role_id', $role->id);
            });
    }

    // SCOPES SPECIFIC TO INVOLVEMENT AREAS AND SUBAREAS -- based on the involvement_to_user pivot table attributes
    public function scopeVisibleVolunteersOnly($query)
    {
        $query->where('show_in_volunteer_list', true);
    }

    public function scopeApprovedForAssignmentsOnly($query)
    {
        $query->where('is_approved_for_assignments', true);
    }
}
