<?php

namespace App\Users\Services;

use App\Accounts\FinanceBucket;
use App\Users\Payment;
use App\Users\PaymentMethod;
use App\Users\PaymentSchedule;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException as ApiErrorExceptionAlias;
use Stripe\Stripe;

class ChargePaymentMethod
{
    protected PaymentSchedule|null $paymentSchedule = null;
    protected PaymentMethod        $paymentMethod;
    protected FinanceBucket        $financeBucket;
    protected int                  $amount;

    public function charge(): Payment
    {
        $this->checkForRequiredData();

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        Log::info('ChargePaymentMethod::charge() attempt.', [
            'user_id'                => $this->paymentMethod?->user?->id,
            'user_payment_method_id' => $this->paymentMethod?->id,
            'amount'                 => $this->amount,
        ]);

        try {
            $charge = \Stripe\Charge::create([
                'amount'      => $this->amount,
                'currency'    => 'usd',
                'customer'    => $this->paymentMethod->user->stripe_customer_id,
                'source'      => $this->paymentMethod->stripe_payment_method_id,
                //                'on_behalf_of' => $this->paymentMethod->account->stripe_account_id,
                'description' => ($this->financeBucket?->name ?? 'Contribution'),
            ], [
                'stripe_account' => $this->paymentMethod->account->stripe_account_id,
            ]);
        } catch (ApiErrorExceptionAlias $e) {
            Log::error('ChargePaymentMethod::charge() - Stripe call failed.', [
                'user_id'                => $this->paymentMethod?->user?->id,
                'user_payment_method_id' => $this->paymentMethod?->id,
                'amount'                 => $this->amount,
                'error'                  => $e->getMessage(),
                'e'                      => $e,
            ]);

            throw $e;
        }

        try {
            return (new CreateContribution())
                ->forUser($this->paymentMethod->user)
                ->setType($charge?->payment_method_details?->type)
                ->setStripeChargeObject($charge)
                ->create([
                    'title'                         => ($this->financeBucket ? optional($this->financeBucket)->name : 'Contribution'),
                    'user_payment_schedule_id'      => $this->paymentSchedule ? $this->paymentSchedule->id : null,
                    'user_payment_method_id'        => $this->paymentMethod->id,
                    'account_finance_bucket_id'     => $this->financeBucket->id,
                    'provider'                      => 'stripe',
                    'provider_payment_id'           => $charge->id,
                    'stripe_charge_id'              => $charge->id,
                    'stripe_status'                 => $charge->status,
                    'status'                        => $charge->status,
                    'stripe_balance_transaction_id' => $charge->balance_transaction,
                    'amount'                        => $this->amount,
                    'amount_platform_fee'           => $charge->application_fee_amount,
                    'is_captured'                   => $charge->captured,
                ]);
        } catch (\Exception $e) {
            Log::error('ChargePaymentMethod::charge() -- Possible payment without a recorded contribution record!', [
                'user_id' => $this->paymentMethod->user->id,
                'message' => $e->getMessage(),
                'error'   => $e,
            ]);

            throw $e;
        }
    }

    public function setAmountInCents($amount)
    {
        $this->amount = $amount;

        return $this;
    }

    public function setFinanceBucket(FinanceBucket $finance_bucket): self
    {
        $this->financeBucket = $finance_bucket;

        return $this;
    }

    public function setPaymentSchedule(PaymentSchedule $payment_schedule)
    {
        $this->paymentSchedule = $payment_schedule;

        return $this;
    }

    public function setPaymentMethod(PaymentMethod $payment_method)
    {
        $this->paymentMethod = $payment_method;

        return $this;
    }

    private function checkForRequiredData()
    {
        if (!$this->paymentMethod) {
            throw new \Exception('No payment method set.');
        }
        if (!$this->financeBucket) {
            throw new \Exception('No finance bucket set.');
        }
        if (!$this->paymentMethod->user) {
            throw new \Exception('No user found for payment method.');
        }
        if (!$this->paymentMethod->user->stripe_customer_id) {
            throw new \Exception('No Stripe customer id found for user.');
        }
        if (!$this->paymentMethod->stripe_payment_method_id) {
            throw new \Exception('No Stripe payment method id found on the payment_method.');
        }
        if (!$this->amount) {
            throw new \Exception('No amount set.');
        }
        if (!$this->paymentMethod->account->stripe_account_id) {
            throw new \Exception('No Stripe account id found for account.');
        }
    }
}
