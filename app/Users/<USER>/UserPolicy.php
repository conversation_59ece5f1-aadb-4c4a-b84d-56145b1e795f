<?php

namespace App\Users\Policies;

use App\Users\User;

class UserPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.directory')) {
            return false;
        }
    }

    public function search(User $user)
    {
        return $user->hasPermission('users.manage');
    }

    public function create(User $user)
    {
        return $user->hasPermission('users.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('users.manage');
    }

    public function manageRoles(User $user)
    {
        return $user->hasPermission('users.manage.roles');
    }

    public function manageGroups(User $user)
    {
        return $user->hasPermission('users.manage.groups');
    }

    public function edit(User $user, User $user2)
    {
        return ($user->hasPermission('users.manage') && ($user->account_id === $user2->account_id));
    }

    public function view(User $user, User $user2)
    {
        return ($user->hasPermission('users.manage') && ($user->account_id === $user2->account_id));
    }

    public function update(User $user, User $user2)
    {
        return ($user->hasPermission('users.manage') && ($user->account_id === $user2->account_id));
    }

    public function delete(User $user, User $user2)
    {
        return ($user->hasPermission('users.delete') && ($user->account_id === $user2->account_id));
    }
}
