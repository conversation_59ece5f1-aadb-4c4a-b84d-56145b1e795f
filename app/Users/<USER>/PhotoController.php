<?php

namespace App\Users\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Photo;
use App\Users\Services\CreateUserPhoto;
use App\Users\Services\UpdateUserPhoto;
use App\Users\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;

class PhotoController extends Controller
{
    public function index(User $user)
    {
        return view('admin.users.tabs.photos')
            ->with('user', User::visibleTo(auth()->user())->find($user->id))
            ->with('photos', $user->photos);
    }

    public function create(User $user)
    {
        return view('admin.users.photos.create')
            ->with('user', $user);
    }

    public function edit(User $user, Photo $photo)
    {
        return view('admin.users.photos.edit')
            ->with('user', $user)
            ->with('photo', $photo);
    }

    public function store(Request $request, User $user)
    {
        $request->validate([
            'cropped_data' => 'required',
        ]);

        // Convert base64 to file
        $image_data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $request->cropped_data));

        // Create temp file
        $temp_file = tempnam(sys_get_temp_dir(), 'cropped');
        file_put_contents($temp_file, $image_data);

        // Create uploaded file instance
        $file = new \Illuminate\Http\UploadedFile(
            $temp_file,
            'cropped_image.jpg',
            'image/jpeg',
            null,
            true
        );

        if ($file->getSize() > 0) {
            try {
                (new CreateUserPhoto())
                    ->withFile($file->getRealPath())
                    ->createdBy(auth()->user())
                    ->originalClientFileName($file->getClientOriginalName())
                    ->forUser($user)
                    ->create([
                        'family_id'  => request()->input('is_family') ? $user->family_id : null,
                        'is_primary' => request()->input('is_primary', 0),
                        'is_avatar'  => request()->input('is_avatar', 0),
                        'is_hidden'  => request()->input('is_hidden', 0),
                        'is_family'  => request()->input('is_family', 0),
                    ]);
            } catch (Exception $e) {
                return back()->with('message.failure', 'Image upload failed. ' . $e->getMessage());
            }

            return back()->with('message.success', 'Photo saved.');
        } else {
            return back()->with('message.failure', 'It looks like you did not select a file to upload!');
        }

        // Clean up temp file
        @unlink($temp_file);

        return back()->with('message.success', 'Photo added successfully!');
    }

    public function save(User $user, Photo $photo)
    {
        try {
            (new UpdateUserPhoto())
                ->forPhoto($photo)
                ->update([
                    'family_id'      => request()->input('is_family') ? $user->family_id : null,
                    'is_primary'     => request()->input('is_primary', 0),
                    'is_avatar'      => request()->input('is_avatar', 0),
                    'is_hidden'      => request()->input('is_hidden', 0),
                    'is_family'      => request()->input('is_family', 0),
                    'needs_approval' => request()->has('needs_approval') ? request()->get('needs_approval') : $photo->needs_approval,
                    'approved_by'    => request()->has('approved_by') ? request()->get('approved_by') : $photo->approved_by,
                ]);
        } catch (Exception $e) {
            return back()->with('message.failure', 'Photo update failed. ' . $e->getMessage());
        }

        return back()->with('message.success', 'Photo saved.');
    }

    public function rotate(User $user, Photo $photo)
    {
        try {
            (new UpdateUserPhoto())
                ->forPhoto($photo)
                ->rotate(request()->input('direction'))
                ->update([]);
        } catch (Exception $e) {
            return back()->with('message.failure', 'Photo rotation failed. ' . $e->getMessage());
        }

        return back()->with('message.success', 'Photo saved.');
    }

    public function destroy(User $user, Photo $photo)
    {
        try {
            $photo->deleteAllFiles();
            $photo->delete();
        } catch (Exception $e) {
            report($e);
            return back()->with('message.failure', 'Could not delete photo. ' . $e->getMessage());
        }

        return back()->with('message.success', 'Photo deleted!');
    }
}
