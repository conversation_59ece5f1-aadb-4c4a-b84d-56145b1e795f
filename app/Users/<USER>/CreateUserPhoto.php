<?php

namespace App\Users\Services;

use App\Users\Photo;
use App\Users\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Drivers\Imagick\Driver;
use Intervention\Image\Exceptions\DecoderException;
use Intervention\Image\ImageManager;

class CreateUserPhoto
{
    protected $attributes = [
        'is_primary'     => 0,
        'is_avatar'      => 0,
        'is_hidden'      => 0,
        'is_family'      => 0,
        'needs_approval' => 0,
        'created_by'     => null,
    ];
    protected $user;
    protected $file_input;
    protected $original_client_file_name;
    protected $photo;

    public function create($attributes): Photo
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (!$this->file_input || !$this->user) {
            throw new \Exception('No file input or user specified.');
        }

        return $this->uploadFileAndCreate();
    }

    public function withFile($file)
    {
        $this->file_input = $file;

        return $this;
    }

    public function originalClientFileName($file_name)
    {
        $this->original_client_file_name = $file_name;

        return $this;
    }

    public function createdBy(User|null $user = null)
    {
        if (!$user) {
            return $this;
        }

        $this->attributes['created_by'] = $user->id;

        return $this;
    }

    public function forUser(User $user)
    {
        $this->attributes['account_id'] = $user->account_id;
        $this->attributes['user_id']    = $user->id;
        $this->user                     = $user;

        return $this;
    }

    public function needsApproval($needs_approval = true)
    {
        $this->attributes['needs_approval'] = $needs_approval;

        return $this;
    }

    protected function uploadFileAndCreate()
    {
        try {
            // create new manager instance with desired driver
            $manager = new ImageManager(new Driver());

            // read image from filesystem
            $image = $manager->read($this->file_input);
        } catch (DecoderException $e) {
            Log::error($e);
            // Re-throw our exception back out.
            throw $e;
        }

        $original_width  = $image->width();
        $original_height = $image->height();

        // $folder    = Config::get('app.user_image_file_path');
        // $extension = '.' . $image->getClientOriginalExtension();

        $new_image_name           = $this->user->id . '---' . Str::random(8);
        $url_of_new_original_file = $this->user->account->id . '/' . $new_image_name . '---original.jpg';

        // If we can't upload the file, fail
        try {
            // Original -- saved as a JPG
            if (!Storage::disk('user-images')->put(
                $this->user->account->id . '/' . $new_image_name . '---original.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write original file to cloud server.');
            }
            // 1024px
            if ($image->width() > 1024 && !Storage::disk('user-images')->put(
                    $this->user->account->id . '/' . $new_image_name . '---1024.jpg',
                    $image->scale(width: 1024)->toJpeg(95)->__toString(),
                    'public'
                )
            ) {
                throw new \Exception('Could not write file to cloud server.');
            } elseif (!Storage::disk('user-images')->put(
                $this->user->account->id . '/' . $new_image_name . '---1024.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write 1024 file to cloud server.');
            }
            // 512px
            if ($image->width() > 512 && !Storage::disk('user-images')->put(
                    $this->user->account->id . '/' . $new_image_name . '---512.jpg',
                    $image->scale(width: 512)->toJpeg(95)->__toString(),
                    'public'
                )
            ) {
                throw new \Exception('Could not write 512 file to cloud server.');
            } elseif (!Storage::disk('user-images')->put(
                $this->user->account->id . '/' . $new_image_name . '---512.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write file to cloud server.');
            }
            // 256px
            if ($image->width() > 256 && !Storage::disk('user-images')->put(
                    $this->user->account->id . '/' . $new_image_name . '---256.jpg',
                    $image->scale(width: 256)->toJpeg(95)->__toString(),
                    'public'
                )
            ) {
                throw new \Exception('Could not write 256 file to cloud server.');
            } elseif (!Storage::disk('user-images')->put(
                $this->user->account->id . '/' . $new_image_name . '---256.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write file to cloud server.');
            }

            $file_info = Http::head(Storage::disk('user-images')->url($url_of_new_original_file));

//            $retrieved_file = Storage::disk('user-images')->get($url_of_new_original_file);
//            $sha1 = sha1(file_get_contents());

            $this->photo = new Photo();

            $this->photo->fill([
                'user_id'            => $this->user->id,
                'family_id'          => $this->attributes['is_family'] ? $this->user->family_id : null,
                'caption'            => null,
                'width'              => $original_width,
                'height'             => $original_height,
                'dpi'                => null,
                'file_original_name' => $this->original_client_file_name,
                'file_size'          => $file_info->header('content-length'),
                'file_folder'        => $this->user->account->id,
                'file_id'            => null,
                'file_name'          => $new_image_name,
                'file_extension'     => 'jpg',
                'file_type'          => 'image/jpeg',
                'file_sha1'          => null,
                'has_original'       => 1,
                'has_1024'           => 1,
                'has_512'            => 1,
                'has_256'            => 1,
                'is_primary'         => $this->attributes['is_primary'],
                'is_avatar'          => $this->attributes['is_avatar'],
                'is_hidden'          => $this->attributes['is_hidden'],
                'is_family'          => $this->attributes['is_family'],
                'needs_approval'     => $this->attributes['needs_approval'],
                'created_by'         => $this->attributes['created_by'],
            ]);

            $this->photo->save();

            // If this is our new primary photo, remove primary from all others.
            if ($this->attributes['is_primary']) {
                Photo::where(function ($query) {
                    $query->where('user_id', $this->user->id)
                        ->when($this->attributes['is_family'], function ($query2) use ($query) {
                            $query->where('family_id', $this->user->family_id)
                                ->where('is_primary', true);
                        });
                })
                    ->where('id', '<>', $this->photo->id)
                    ->where('is_primary', true)
                    ->update(['is_primary' => false]);
            }

            // If this is our new PROFILE photo, remove avatar from all others.
            if ($this->attributes['is_avatar']) {
                Photo::where(function ($query) {
                    $query->where('user_id', $this->user->id)
                        ->when($this->attributes['is_family'], function ($query2) use ($query) {
                            $query->where('family_id', $this->user->family_id)
                                ->where('is_avatar', true);
                        });
                })
                    ->where('id', '<>', $this->photo->id)
                    ->where('is_avatar', true)
                    ->update(['is_avatar' => false]);
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            throw $e;
        }

        return $this->photo;
    }
}
