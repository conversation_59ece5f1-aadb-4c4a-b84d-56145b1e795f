<?php

namespace App\Users\Services;

use App\Users\Photo;
use App\Users\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Imagick\Driver;
use Intervention\Image\Exceptions\DecoderException;
use Intervention\Image\ImageManager;

class UpdateUserPhoto
{
    protected $attributes = [
        'is_primary'     => 0,
        'is_avatar'      => 0,
        'is_hidden'      => 0,
        'is_family'      => 0,
        'needs_approval' => 0,
    ];
    protected $rotate     = false;
    protected $photo;

    public function update($attributes): Photo
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        $this->photo->fill($this->attributes);

        $this->photo->save();

        return $this->updatePhoto();
    }

    public function forPhoto(Photo $photo)
    {
        $this->photo = $photo;

        return $this;
    }

    public function setApproval($approved = true, User $by_user = null)
    {
        $this->attributes['needs_approval'] = $approved;

        if ($by_user) {
            $this->attributes['approved_by'] = $by_user->id;
        }

        return $this;
    }

    public function rotateRight()
    {
        $this->rotate = 'right';

        return $this;
    }

    public function rotateLeft()
    {
        $this->rotate = 'left';

        return $this;
    }

    public function rotate($direction)
    {
        if ($direction != 'right' && $direction != 'left') {
            throw new \Exception('Invalid rotation direction.');
        }

        $this->rotate = $direction;

        return $this;
    }

    protected function updatePhoto()
    {
        // If this is our new primary photo, remove primary from all others.
        if ($this->attributes['is_primary']) {
            Photo::where(function ($query) {
                $query->where('user_id', $this->photo->user->id)
                    ->when($this->attributes['is_family'], function ($query2) use ($query) {
                        $query->where('family_id', $this->photo->user->family_id)
                            ->where('is_primary', true);
                    });
            })
                ->where('id', '<>', $this->photo->id)
                ->where('is_primary', true)
                ->update(['is_primary' => false]);
        }

        // If this is our new PROFILE photo, remove avatar from all others.
        if ($this->attributes['is_avatar']) {
            Photo::where(function ($query) {
                $query->where('user_id', $this->photo->user->id)
                    ->when($this->attributes['is_family'], function ($query2) use ($query) {
                        $query->where('family_id', $this->photo->user->family_id)
                            ->where('is_avatar', true);
                    });
            })
                ->where('id', '<>', $this->photo->id)
                ->where('is_avatar', true)
                ->update(['is_avatar' => false]);
        }

        if ($this->rotate) {
            $this->rotateImage();
        }

        return $this->photo;
    }

    protected function rotateImage()
    {
        if (!$this->rotate) {
            return $this->photo;
        }

        try {
            // create new manager instance with desired driver
            $manager = new ImageManager(new Driver());

            // read image from filesystem
            $image = $manager->read(file_get_contents($this->photo->getCdnUrl()));
        } catch (DecoderException $e) {
            Log::error($e);
            // Re-throw our exception back out.
            throw $e;
        }

        if ($this->rotate == 'right') {
            $image->rotate(-90);
        } elseif ($this->rotate == 'left') {
            $image->rotate(90);
        }

        if ($image->width()) {
            $this->photo->width  = $image->width();
            $this->photo->height = $image->height();

            $this->photo->save();
        }

        // $folder    = Config::get('app.user_image_file_path');
        // $extension = '.' . $image->getClientOriginalExtension();

        $new_image_name           = $this->photo->file_name;
        $url_of_new_original_file = $this->photo->user->account->id . '/' . $new_image_name . '---original.jpg';

        // If we can't upload the file, fail
        try {
            // Original -- saved as a JPG
            if (!Storage::disk('user-images')->put(
                $this->photo->user->account->id . '/' . $new_image_name . '---original.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write original file to cloud server.');
            }
            // 1024px
            if ($image->width() > 1024 && !Storage::disk('user-images')->put(
                    $this->photo->user->account->id . '/' . $new_image_name . '---1024.jpg',
                    $image->scale(width: 1024)->toJpeg(95)->__toString(),
                    'public'
                )
            ) {
                throw new \Exception('Could not write file to cloud server.');
            } elseif (!Storage::disk('user-images')->put(
                $this->photo->user->account->id . '/' . $new_image_name . '---1024.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write 1024 file to cloud server.');
            }
            // 512px
            if ($image->width() > 512 && !Storage::disk('user-images')->put(
                    $this->photo->user->account->id . '/' . $new_image_name . '---512.jpg',
                    $image->scale(width: 512)->toJpeg(95)->__toString(),
                    'public'
                )
            ) {
                throw new \Exception('Could not write 512 file to cloud server.');
            } elseif (!Storage::disk('user-images')->put(
                $this->photo->user->account->id . '/' . $new_image_name . '---512.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write file to cloud server.');
            }
            // 256px
            if ($image->width() > 256 && !Storage::disk('user-images')->put(
                    $this->photo->user->account->id . '/' . $new_image_name . '---256.jpg',
                    $image->scale(width: 256)->toJpeg(95)->__toString(),
                    'public'
                )
            ) {
                throw new \Exception('Could not write 256 file to cloud server.');
            } elseif (!Storage::disk('user-images')->put(
                $this->photo->user->account->id . '/' . $new_image_name . '---256.jpg',
                $image->toJpeg(95)->__toString(),
                'public'
            )
            ) {
                throw new \Exception('Could not write file to cloud server.');
            }

            $file_info = Http::head(Storage::disk('user-images')->url($url_of_new_original_file));
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            throw $e;
        }

        return $this->photo;
    }
}
