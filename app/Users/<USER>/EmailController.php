<?php

namespace App\Users\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Email;
use App\Users\User;
use Illuminate\Validation\Rule;

class EmailController extends Controller
{
    public function show(User $user)
    {
        return view('admin.users.email.show')
            ->with('user', $user);
    }

    public function create(User $user)
    {
        return view('admin.users.email.create')
            ->withUser($user);
    }

    public function store(User $user)
    {
        $this->validate(request(), [
            'email'                 => 'required|email|max:128|unique:user_emails',
            'type'                  => 'required|in:' . implode(',', Email::getTypeKeys()),
            'is_primary'            => 'nullable|integer',
            'is_family'             => 'nullable|integer',
            'is_hidden'             => 'nullable|integer',
            'receives_group_emails' => 'nullable|integer',
        ], [
            'email.unique'   => 'This email address is already in use. Please contact support with the email and we can free up the address for you.',
            'email.required' => 'The email field is required.',
            'email.email'    => 'Please enter a valid email address.',
            'email.max'      => 'Email cannot be longer than 128 characters.',
        ]);

        try {
            $email = $user->emails()->create(request()->only([
                'email',
                'type',
                'is_primary',
                'is_family',
                'is_hidden',
                'receives_group_emails',
            ]));

            if (request()->input('is_family') == 1) {
                $email->family_id = $user->family_id;
            }

            $email->save();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.view', $user)
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user->id))
            ->with('message.success', 'Saved successfully.');;
    }

    public function edit(User $user, Email $email)
    {
        return view('admin.users.email.edit')
            ->withUser($user)
            ->withEmail($email);
    }

    public function save(User $user, Email $email)
    {
        $this->validate(request(), [
            'email'                 => [
                'required',
                'email',
                'max:128',
                Rule::unique('user_emails')->ignore($email->id),
            ],
            'type'                  => 'required|in:' . implode(',', Email::getTypeKeys()),
            'is_primary'            => 'nullable|integer',
            'is_family'             => 'nullable|integer',
            'is_hidden'             => 'nullable|integer',
            'receives_group_emails' => 'nullable|integer',
        ]);

        try {
            $email->fill(request()->only([
                'email',
                'type',
                'is_primary',
                'is_family',
                'is_hidden',
                'receives_group_emails',
            ]));

            $email->is_primary            = request()->input('is_primary', 0);
            $email->is_family             = request()->input('is_family', 0);
            $email->is_hidden             = request()->input('is_hidden', 0);
            $email->receives_group_emails = request()->input('receives_group_emails', 0);

            if ($email->is_family) {
                $email->family_id = $user->family_id;
            } else {
                $email->family_id = null;
            }

            $email->save();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.view', $user)
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user->id))
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(User $user, Email $email)
    {
        try {
            if ($user->id === $email->user_id) {
                $email->delete();
            }
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.view', $user)
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'Email deleted successfully.');
    }
}
