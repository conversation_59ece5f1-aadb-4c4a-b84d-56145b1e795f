<?php

namespace App\Users\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Messages\Message;
use App\Users\Group;
use Illuminate\Pagination\Paginator;

class GroupMessageHistoryController extends Controller
{
    public function index(Group $group)
    {
        Paginator::useTailwind();

        return view('admin.groups.message-history.index')
            ->with('group', $group)
            ->with(
                'messages',
                $group->messages()
                    ->orderBy('created_at', 'DESC')
                    ->paginate(8)
            );
    }

    public function view(Group $group, Message $message)
    {
        Paginator::useTailwind();

        return view('admin.groups.message-history.view')
            ->with('message', $message)
            ->with('group', $group)
            ->with(
                'messageHistory',
                $message->history()
                    ->when(request('message_status_id'), function ($query) {
                        return $query->where('message_history.message_status_id', request('message_status_id'));
                    })
                    ->with(['messageStatus', 'user'])
                    ->orderBy('created_at', 'DESC')
                    ->get()
            );
    }
}
