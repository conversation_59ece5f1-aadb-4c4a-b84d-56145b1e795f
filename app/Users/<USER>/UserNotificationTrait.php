<?php

namespace App\Users\Traits;

trait UserNotificationTrait
{
    public function getUserForEmailNotification()
    {
        if ($this->family_role == 'child' && !$this->getBestEmail()) {
            return $this->getHeadOfFamily();
        } else {
            return $this;
        }
    }

    public function unreadGroupNotifications()
    {
        return $this->groupNotifications()
            ->where('is_read', false);
    }
}
