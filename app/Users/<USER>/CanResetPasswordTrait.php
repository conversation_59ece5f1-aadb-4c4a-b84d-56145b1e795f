<?php

namespace App\Users\Traits;

use Illuminate\Auth\Notifications\ResetPassword as ResetPasswordNotification;

trait CanResetPasswordTrait
{
    /**
     * Get the e-mail address where password reset links are sent.
     *
     * @return string
     */
    public function getEmailForPasswordReset()
    {
        return $this->email_for_password_reset ?: $this->email;
    }

    /**
     * Send the password reset notification.
     *
     * @param string $token
     *
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }
}
