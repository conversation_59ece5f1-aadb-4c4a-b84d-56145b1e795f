<?php

namespace App\Users\Requests;

use App\Base\Http\Request;

class UpdateGroupRequest extends Request
{
    public function rules()
    {
        $rules = [
            'name'                       => 'sometimes|required|string|max:64',
            'description'                => 'nullable|sometimes|string',
            'message_types'              => 'sometimes|array',
            'allow_individual_to_toggle' => 'sometimes|integer',
            'is_hidden'                  => 'sometimes|integer',
            'allow_all_members_to_send'  => 'sometimes|integer',
            'is_suspended'               => 'sometimes|integer',
            'enable_posts'               => 'sometimes|integer',
        ];

        return $rules;
    }
}
