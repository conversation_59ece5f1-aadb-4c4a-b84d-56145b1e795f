<?php

namespace App\Users\Services;

use App\Users\Photo;
use App\Users\User;

class SubmitFamilyPhotoForReview
{
    protected array     $attributes = [
        'is_primary'     => 1,
        'is_avatar'      => 0,
        'is_hidden'      => 0,
        'is_family'      => 1,
        'needs_approval' => 1,
    ];
    protected User      $user;
    protected User|null $created_by = null;
    protected           $mobile_image;
    protected           $original_client_file_name;
    protected Photo     $photo;

    public function create(): Photo
    {
        if (!$this->mobile_image || !$this->user) {
            throw new \Exception('No file input or user specified.');
        }

        return (new CreateUserPhoto())
            ->needsApproval($this->attributes['needs_approval'])
            ->withFile($this->mobile_image)
            ->originalClientFileName($this->original_client_file_name)
            ->forUser($this->user)
            ->createdBy($this->created_by)
            ->create($this->attributes);
    }

    public function forUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function createdBy(User|null $user = null)
    {
        if (!$user) {
            return $this;
        }

        $this->created_by = $user;

        return $this;
    }

    public function needsApproval($needs_approval = true)
    {
        $this->attributes['needs_approval'] = $needs_approval ? 1 : 0;

        return $this;
    }

    public function originalFileName($original_client_file_name = '')
    {
        $this->original_client_file_name = $original_client_file_name;

        return $this;
    }

    /**
     * We just need the base64 from this.
     *
     * request()->get('images') payload example:
     * [{
     * "base64": "",
     * "fileName": "2945F606-4936-4D21-9B72-5EF41C0076CB.jpg",
     * "fileSize": 1894871,
     * "height": 1062,
     * "type": "image/jpg",
     * "uri": "file:///Users/<USER>/tmp/2945F606-4936-4D21-9B72-5EF41C0076CB.jpg",
     * "width": 1600
     * }]
     */
    public function withMobileImage($image)
    {
        $this->mobile_image = $image;

        return $this;
    }
}
