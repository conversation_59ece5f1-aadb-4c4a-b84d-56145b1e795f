<?php

namespace App\Users\Policies;

use App\Users\Email;
use App\Users\User;

class EmailPolicy
{
    public function before($user, $ability)
    {
        if ($user->isSuper()) {
            return true;
        }
    }

    public function create(User $user)
    {
        return $user->hasPermission('users.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('users.index');
    }

    public function view(User $user, Email $email)
    {
        return $user->id == $email->user_id
               || ($user->hasPermission('users.index') && ($user->account_id === $email->user->account_id));
    }

    public function edit(User $user, Email $email)
    {
        return $user->id == $email->user_id
               || $user->family_id == $email->user->family_id
               || ($user->hasPermission('users.manage') && ($user->account_id === $email->user->account_id));
    }

    public function update(User $user, Email $email)
    {
        return $user->id == $email->user_id
               || $user->family_id == $email->user->family_id
               || ($user->hasPermission('users.manage') && ($user->account_id === $email->user->account_id));
    }

    public function delete(User $user, Email $email)
    {
        return $user->id == $email->user_id
               || $user->family_id == $email->user->family_id
               || ($user->hasPermission('users.manage') && ($user->account_id === $email->user->account_id));
    }
}
