<?php

namespace App\Users\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Role;
use App\Users\User;

class AssignRoleController extends Controller
{
    public function store(Role $role)
    {
        $this->validate(request(), [
            'user_id' => 'required|integer|exists:users,id',
        ]);

        $role->users()->attach(request('user_id'));

        return redirect(route('admin.roles.show', $role->id));
    }

    public function delete(Role $role, User $user)
    {
        $user->roles()->detach($role->id);

        return back()->with('message.success', 'User removed from role successully.');
    }
}
