<?php

namespace App\Users\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Services\MailingLabels;
use App\Users\Services\MemberDirectory;
use App\Users\User;
use Illuminate\Support\Facades\Auth;

class UserDownloadController extends Controller
{
    public function mailingLabels()
    {
        $users = $this->getUsersUsingQueryString();

        (new MailingLabels())->withUsers($users)->createPDF('Mailing Labels.pdf');
    }

    public function paperDirectory()
    {
        $users = User::visibleTo(Auth::user())->HeadsOfFamily()->orderBy('last_name')->get();

        (new MemberDirectory())->withUsers($users)->createPDF('Member Paper Directory.pdf');
    }

    private function getUsersUsingQueryString()
    {
        return User::visibleTo(Auth::user())
            ->when(request()->hasAny(['last_name', 'first_name']), function ($mainQuery) {
                return $mainQuery->where(function ($query) {
                    $query->where('last_name', 'like', request('last_name') . '%');
                    $query->orWhere('first_name', 'like', request('last_name') . '%');
                });
            })
            ->when(request()->has('groups'), function ($mainQuery) {
                $mainQuery->whereHas('groups', function ($query) {
                    $query->whereIn('user_groups.id', request()->input('groups') ?: []);
                });
            })
            ->withFamilyLastName()
            ->with(['emails'])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->get();
    }
}
