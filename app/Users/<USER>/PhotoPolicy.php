<?php

namespace App\Users\Policies;

use App\Users\Photo;
use App\Users\User;

class PhotoPolicy
{
    public function before($user, $ability)
    {
    }

    public function create(User $user)
    {
        return $user->hasPermission('users.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('users.manage');
    }

    public function view(User $user, Photo $photo)
    {
        return $user->id == $photo->user_id
               || ($user->hasPermission('users.index') && ($user->account_id === $photo->user->account_id));
    }

    public function edit(User $user, Photo $photo)
    {
        return ($user->hasPermission('users.manage') && ($user->account_id === $photo->user->account_id));
    }

    public function manageFamilyPhotos(User $user)
    {
        return $user->hasPermission('users.manage')
               || $user->hasPermission('users.manage.photos.family');
    }

    public function editOwnPhoto(User $user, Photo $photo)
    {
        return $user->id == $photo->user_id
               || ($user->hasPermission('users.manage') && ($user->account_id === $photo->user->account_id));
    }

    public function update(User $user, Photo $photo)
    {
        return ($user->hasPermission('users.manage') && ($user->account_id === $photo->user->account_id));
    }

    public function delete(User $user, Photo $photo)
    {
        return ($user->hasPermission('users.manage') && ($user->account_id === $photo->user->account_id));
    }
}
