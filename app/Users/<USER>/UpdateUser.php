<?php

namespace App\Users\Services;

use App\Users\Email;
use App\Users\Notification;
use App\Users\Phone;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class UpdateUser
{
    protected $user;
    protected $attributes           = [];
    protected $roles                = null;
    protected $groups               = null;
    protected $add_groups           = null;
    protected $remove_groups        = null;
    protected $add_roles            = null;
    protected $remove_roles         = null;
    protected $updateExistingGroups = null;

    public function __construct($user)
    {
        $this->user = $user;
    }

    public function update(array $attributes = [])
    {
        $this->attributes = $attributes;

        $this->updateFields();

        $this->checkIfHeadOfFamily();

        return $this->user;
    }

    protected function updateFields()
    {
        $password = Arr::pull($this->attributes, 'password');

        if (Arr::has($this->attributes, 'birthdate')) {
            if (!empty(Arr::get($this->attributes, 'birthdate'))) {
                $this->attributes['birthdate'] = Carbon::parse($this->attributes['birthdate'])->startOfDay()->setTimezone('utc');
            } else {
                $this->attributes['birthdate'] = null;
            }
        }

        if (Arr::has($this->attributes, 'date_married')) {
            if (!empty(Arr::get($this->attributes, 'date_married'))) {
                $this->attributes['date_married'] = Carbon::parse($this->attributes['date_married'])->startOfDay()->setTimezone('utc');
            } else {
                $this->attributes['date_married'] = null;
            }
        }

        if (Arr::has($this->attributes, 'date_membership')) {
            if (!empty(Arr::get($this->attributes, 'date_membership'))) {
                $this->attributes['date_membership'] = Carbon::parse($this->attributes['date_membership'])->startOfDay()->setTimezone('utc');
            } else {
                $this->attributes['date_membership'] = null;
            }
        }

        if (Arr::has($this->attributes, 'date_background_check')) {
            if (!empty(Arr::get($this->attributes, 'date_background_check'))) {
                $this->attributes['date_background_check'] = Carbon::parse($this->attributes['date_background_check'])->startOfDay()->setTimezone('utc');
            } else {
                $this->attributes['date_background_check'] = null;
            }
        }

        if (Arr::has($this->attributes, 'marital_status')) {
            if (!in_array($this->attributes['marital_status'], User::$marital_statuses)) {
                unset($this->attributes['marital_status']);
            }
        }

        $this->checkUsernameIsOk();

        // Set all attributes.
        $this->user->fill($this->attributes);

        // Set some special attributes (bools that are dates).
        if (Arr::has($this->attributes, 'can_teach')) {
            if (Arr::get($this->attributes, 'can_teach') == 0) {
                $this->user->can_teach = null;
            } else {
                $this->user->can_teach = Carbon::now();
            }
        }

        if (Arr::has($this->attributes, 'can_lead')) {
            if (Arr::get($this->attributes, 'can_lead') == 0) {
                $this->user->can_lead = null;
            } else {
                $this->user->can_lead = Carbon::now();
            }
        }

        if (Arr::has($this->attributes, 'is_baptized')) {
            if (Arr::get($this->attributes, 'is_baptized') == 0) {
                $this->user->is_baptized = null;
            } else {
                $this->user->is_baptized = Carbon::now();
            }
        }

        if (Arr::has($this->attributes, 'confirmed_email')) {
            if (Arr::get($this->attributes, 'confirmed_email') == 0) {
                $this->user->confirmed_email = null;
            } else {
                $this->user->confirmed_email = Carbon::now();
            }
        }

        // Set our password through our magic method.
        if (!empty($password)) {
            $this->user->password = $password;
        } elseif (!empty(Arr::get($this->attributes, 'safe_fill_1'))) {
            $this->user->password = Arr::get($this->attributes, 'safe_fill_1');
        }

        // If we saved our user, make any ancillary changes.
        if ($this->user->save()) {
            // Save the anniversary date on the spouse too.
            if (Arr::has($this->attributes, 'date_married')) {
                if ($this->user->spouse) {
                    $spouse               = $this->user->spouse;
                    $spouse->date_married = $this->attributes['date_married'];
                    $spouse->save();
                }
            }
        }

        if ($this->roles !== null) {
            // Roles
            $this->user->roles()->sync(is_array($this->roles) ? $this->roles : []);
        }

        if ($this->groups !== null) {
            // Groups
            $this->user->groups()->sync(is_array($this->groups) ? $this->groups : []);
        }

        if ($this->add_groups !== null) {
            // Groups
            $this->user->groups()->syncWithoutDetaching(is_array($this->add_groups) ? $this->add_groups : []);
        }

        // Remove Groups
        if ($this->remove_groups !== null) {
            // Clear our unread notifications for this user/group
            foreach ($this->remove_groups as $group_id) {
                if (Notification::visibleTo($this->user)
                    ->where('p_t', 'user_groups')
                    ->when($group_id, function ($query) use ($group_id) {
                        $query->where('p_t_id', $group_id);
                    })
                    ->where('is_read', 0)
                    ->exists()) {
                    // Clear our notifications
                    return Notification::visibleTo($this->user)
                        ->where('p_t', 'user_groups')
                        ->when($group_id, function ($query) use ($group_id) {
                            $query->where('p_t_id', $group_id);
                        })
                        ->where('is_read', 0)
                        ->update(['is_read' => 1]);
                }
            }

            // Detach the group
            $this->user->groups()->detach(is_array($this->remove_groups) ? $this->remove_groups : []);
        }

        if ($this->add_roles !== null) {
            // Groups
            $this->user->roles()->syncWithoutDetaching(is_array($this->add_roles) ? $this->add_roles : []);
        }

        if ($this->remove_roles !== null) {
            // Groups
            $this->user->roles()->detach(is_array($this->remove_roles) ? $this->remove_roles : []);
        }

        if ($this->updateExistingGroups !== null) {
            foreach ($this->updateExistingGroups as $group_id => $attributes) {
                $this->user->groups()->updateExistingPivot($group_id, $attributes);
            }
        }
    }

    public function addRolesById($roles = [])
    {
        $this->add_roles = $roles === null ? [] : $roles;

        return $this;
    }

    public function removeRolesById($roles = [])
    {
        $this->remove_roles = $roles === null ? [] : $roles;

        return $this;
    }

    public function withRoles($roles = [])
    {
        $this->roles = $roles === null ? [] : $roles;

        return $this;
    }

    public function addGroupsById($groups = [])
    {
        $this->add_groups = $groups === null ? [] : $groups;

        return $this;
    }

    public function removeGroupsById($groups = [])
    {
        $this->remove_groups = $groups === null ? [] : $groups;

        return $this;
    }

    public function withGroups($groups = [])
    {
        $this->groups = $groups === null ? [] : $groups;

        return $this;
    }

    public function updateExistingGroup($group_id, $attributes)
    {
        $this->updateExistingGroups[$group_id] = $attributes;

        return $this;
    }

    protected function checkIfHeadOfFamily()
    {
        // This checks and overwrites any family selection if we're saying they're head of family.
        if (Arr::get($this->attributes, 'family_role') == 'head') {
            $this->user->family_id = $this->user->id;
            $this->user->save();
        }
    }

    protected function checkUsernameIsOk()
    {
        if (Arr::has($this->attributes, 'user_name') && !empty($this->attributes['user_name'])) {
            // Check that it's not an existing email
            if (Email::where('email', 'like', strtolower($this->attributes['user_name']))->exists()) {
                \Log::warning('UpdateUser::checkUsernameIsOk -- username is email', [
                    'user_name' => $this->attributes['user_name'],
                ]);

                // Clear our username
                $this->attributes['user_name'] = null;
            }
            // Check that it's not an existing phone
            if (Phone::where('number', 'like', strtolower($this->attributes['user_name']))->exists()) {
                \Log::warning('UpdateUser::checkUsernameIsOk -- username is phone number', [
                    'user_name' => $this->attributes['user_name'],
                ]);

                // Clear our username
                $this->attributes['user_name'] = null;
            }
            // Check that it's not an existing username
            if (User::where('user_name', 'like', strtolower($this->attributes['user_name']))->where('id', '<>', $this->user->id)->exists()) {
                \Log::warning('UpdateUser::checkUsernameIsOk -- username is existing username', [
                    'user_name' => $this->attributes['user_name'],
                ]);

                // Clear our username
                $this->attributes['user_name'] = null;
            }
        }
    }
}
