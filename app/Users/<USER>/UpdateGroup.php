<?php

namespace App\Users\Services;

use App\Messages\MessageType;
use App\Messages\Services\CreateMessageHandler;
use App\Users\Group;
use Illuminate\Support\Arr;

class UpdateGroup
{
    protected $attributes           = [];
    protected $update_message_types = false;
    protected $group;
    protected $user;

    public function __construct(Group $group)
    {
        $this->group = $group;
    }

    public function touch()
    {
        $this->group->updated_at = now();
        $this->group->save();

        return $this->group;
    }

    public function update($attributes): Group
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        $this->group->fill($this->attributes);

        $this->group->save();

        if ($this->update_message_types) {
            $message_types = Arr::pull($this->attributes, 'message_types') ?? [];

            // First see if we're removing handlers that exist, but aren't in our update.
            foreach ($this->group->messageHandlers as $handler) {
                // We used to not delete SMS handlers, because setting up SMS was a manual process. Nov 2023
                // Now we do, because everything is coming from one SMS number. Nov 2023
                if (!in_array($handler->messageType->id, $message_types)) {
                    $handler->delete();
                }
            }

            // Create our MessageHandler if we don't already have one for the types selected.
            if ($message_types) {
                foreach ($message_types as $type_id) {
                    if ($this->group->messageHandlers->where('message_type_id', $type_id)->isEmpty()) {
                        (new CreateMessageHandler())
                            ->forUser($this->user)
                            ->forGroup($this->group)
                            ->withType(MessageType::find($type_id))
                            ->create();
                    }
                }
            }

            $this->group->messageTypes()->sync($message_types);
        }

        return $this->group;
    }

    public function forUser($user)
    {
        $this->user = $user;

        return $this;
    }

    public function updateMessageTypes()
    {
        $this->update_message_types = true;

        return $this;
    }
}
