<?php

namespace App\Users\Services;

use App\Users\PaymentMethod;
use App\Users\User;
use Illuminate\Support\Arr;

class CreatePaymentMethod
{
    protected $attributes = [];
    protected $payment_method;
    protected $user;

    public function create($attributes = []): PaymentMethod
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        // Default to not suspended
        if (Arr::exists($this->attributes, 'is_family')) {
            $this->attributes['is_family'] = false;
        }
        if (Arr::exists($this->attributes, 'is_shared')) {
            $this->attributes['is_shared'] = false;
        }
        if (Arr::exists($this->attributes, 'share_with_spouse')) {
            $this->attributes['share_with_spouse'] = now();
        }

        $this->payment_method = PaymentMethod::create($this->attributes);

        return $this->payment_method;
    }

    public function forUser(User $user)
    {
        $this->attributes = [
            'user_id'    => $user->id,
            'account_id' => $user->account->id,
        ];

        return $this;
    }

    public function shareWithSpouse($share_with_spouse = true)
    {
        $this->attributes['share_with_spouse'] = $share_with_spouse;

        return $this;
    }

    public function setStripePaymentMethodId($stripe_payment_method_id)
    {
        $this->attributes['stripe_payment_method_id'] = $stripe_payment_method_id;

        return $this;
    }

    public function setStripeObject($stripe_object)
    {
        $this->attributes['stripe_object'] = json_encode($stripe_object);

        return $this;
    }

    public function setStripeType($stripe_payment_type)
    {
        $this->attributes['stripe_type'] = $stripe_payment_type;

        return $this;
    }

    public function setType($type)
    {
        $this->attributes['type'] = $type;

        return $this;
    }

    public function setLabel($label)
    {
        $this->attributes['label'] = $label;

        return $this;
    }

    public function setLast4($last4)
    {
        $this->attributes['last4'] = $last4;

        return $this;
    }

    public function setExpireDate($month, $year)
    {
        $this->attributes['expire_month'] = $month;
        $this->attributes['expire_year']  = $year;

        return $this;
    }
}
