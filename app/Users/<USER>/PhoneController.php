<?php

namespace App\Users\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Phone;
use App\Users\User;
use Illuminate\Http\Request;

class PhoneController extends Controller
{
    public function show(User $user)
    {
        return view('admin.users.phone.show')
            ->with('user', $user);
    }

    public function create(User $user)
    {
        return view('admin.users.phone.create')
            ->withUser($user);
    }

    public function store(Request $request, User $user)
    {
        $this->validate(request(), [
            'number'           => 'required|string|max:32',
            'is_primary'       => 'integer',
            'is_family'        => 'integer',
            'is_hidden'        => 'integer',
            'messages_opt_out' => 'integer',
            'type'             => 'required|in:' . implode(',', array_keys(Phone::$types)),
        ]);

        try {
            $phone = new Phone;

            $phone->fill(request()->only([
                'number',
                'is_primary',
                'is_family',
                'is_hidden',
                'messages_opt_out',
                'type',
            ]));

            $phone->number  = Phone::format($phone->number);
            $phone->user_id = $user->id;

            if (request()->input('is_family') == 1) {
                $phone->family_id = $user->family_id;
            }

            $phone->save();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.view', $user->id)
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user->id))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(User $user, Phone $phone)
    {
        return view('admin.users.phone.edit')
            ->withUser($user)
            ->withPhone($phone);
    }

    public function save(User $user, Phone $phone)
    {
        $this->validate(request(), [
            'number'           => 'required|string|max:32',
            'is_primary'       => 'integer',
            'is_family'        => 'integer',
            'is_hidden'        => 'integer',
            'messages_opt_out' => 'integer',
            'type'             => 'required|in:' . implode(',', Phone::getTypeKeys()),
        ]);

        try {
            $phone->fill(request()->only([
                'number',
                'type',
                'is_primary',
                'is_family',
                'is_hidden',
                'messages_opt_out',
            ]));

            $phone->number = Phone::format($phone->number);

            $phone->is_primary       = request()->input('is_primary', 0);
            $phone->is_family        = request()->input('is_family', 0);
            $phone->is_hidden        = request()->input('is_hidden', 0);
            $phone->messages_opt_out = request()->input('messages_opt_out', 0);

            if ($phone->is_family) {
                $phone->family_id = $user->family_id;
            } else {
                $phone->family_id = null;
            }

            $phone->save();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.view', $user)
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user->id))
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(User $user, Phone $phone)
    {
        try {
            if ($user->id === $phone->user_id) {
                $phone->delete();
            }
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.view', $user)
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'Phone number deleted successfully.');
    }
}
