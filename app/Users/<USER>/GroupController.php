<?php

namespace App\Users\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Group;
use App\Users\Requests\CreateGroupRequest;
use App\Users\Requests\UpdateGroupRequest;
use App\Users\Services\CreateGroup;
use App\Users\Services\DeleteGroup;
use App\Users\Services\UpdateGroup;
use App\Users\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GroupController extends Controller
{
    public function index()
    {
        return view('admin.groups.index');
    }

    public function view(Group $group)
    {
        return view('admin.groups.view')
            ->with([
                'group' => $group,
                'users' => $group->users()->paginate(30),
            ]);
    }

    public function create()
    {
        return view('admin.groups.create');
    }

    public function store(CreateGroupRequest $request)
    {
        try {
            DB::transaction(function () use ($request) {
                return (new CreateGroup())
                    ->forUser(Auth::user())
                    ->create(
                        array_merge($request->all(), [
                            'allow_individual_to_toggle' => request('public_or_private') == 'public' ? 1 : 0,
                            'is_hidden'                  => request('public_or_private') == 'private' ? 1 : 0,
                        ])
                    );
            });
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.groups.create')
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.groups.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function settings(Group $group)
    {
        return view('admin.groups.settings.index')
            ->with('group', $group);
    }

    public function saveSettings(UpdateGroupRequest $request, Group $group)
    {
        try {
            DB::transaction(function () use ($request, $group) {
                (new UpdateGroup($group))
                    ->forUser(Auth::user())
                    ->update(
                        array_merge($request->all(), [
                            'allow_individual_to_toggle' => request('public_or_private') == 'public' ? 1 : 0,
                            'is_hidden'                  => request('public_or_private') == 'private' ? 1 : 0,
                        ])
                    );
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Saved successfully.');
    }

    public function posts(Group $group)
    {
        return view('admin.groups.posts.index')
            ->with('group', $group);
    }

    public function savePosts(UpdateGroupRequest $request, Group $group)
    {
        try {
            DB::transaction(function () use ($request, $group) {
                (new UpdateGroup($group))
                    ->forUser(Auth::user())
                    ->update($request->all());
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Saved successfully.');
    }

    public function messaging(Group $group)
    {
        return view('admin.groups.messaging.index')
            ->with('group', $group);
    }

    public function saveMessaging(UpdateGroupRequest $request, Group $group)
    {
        try {
            DB::transaction(function () use ($request, $group) {
                (new UpdateGroup($group))
                    ->updateMessageTypes()
                    ->forUser(Auth::user())
                    ->update($request->all());

                if (request()->has('first_email_handler_reply_to_address')
                    && $group->messageHandlers()->where('message_type_id', 1)->exists()) {
                    $handler = $group->messageHandlers()->where('message_type_id', 1)->first();

                    $handler->reply_to = request()->get('first_email_handler_reply_to_address');

                    $handler->save();
                }

                if (request()->has('voice_call_voice')
                    && $group->messageHandlers()->where('message_type_id', 4)->exists()) {
                    $handler = $group->messageHandlers()->where('message_type_id', 4)->first();

                    $handler->voice_call_voice = request()->get('voice_call_voice') == 'male' ? 'male' : 'female';

                    $handler->save();
                }
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Saved successfully.');
    }

    public function members(Group $group)
    {
        return view('admin.groups.users.index')
            ->with('group', $group);
    }

    public function admins(Group $group)
    {
        return view('admin.groups.admins.index')
            ->with('group', $group);
    }

    public function senders(Group $group)
    {
        return view('admin.groups.senders.index')
            ->with('group', $group);
    }

    public function save(UpdateGroupRequest $request, Group $group)
    {
        try {
            DB::transaction(function () use ($request, $group) {
                $group = (new UpdateGroup($group))
                    ->forUser(Auth::user())
                    ->update($request->all());

                if (request()->has('first_email_handler_reply_to_address')
                    && $group->messageHandlers()->where('message_type_id', 1)->exists()) {
                    $handler = $group->messageHandlers()->where('message_type_id', 1)->first();

                    $handler->reply_to = request()->get('first_email_handler_reply_to_address');

                    $handler->save();
                }

                return $group;
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Saved successfully.');
    }

    public function detachUser(Group $group, User $user)
    {
        try {
            DB::transaction(function () use ($user, $group) {
                $user->groups()->detach($group->id);
            });

            (new UpdateGroup($group))->touch();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.groups.view', $group)
                ->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.groups.view', $group)
            ->with('message.success', 'Removed successfully.');
    }

    public function destroy(Group $group)
    {
        try {
            (new DeleteGroup($group))->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.groups.index')
            ->with('message.success', 'Delete successful.');
    }
}
