<?php

namespace App\Users\Services;

use App\Users\PaymentMethod;
use Illuminate\Support\Facades\Log;
use Stripe\Customer;
use Stripe\Stripe;

class DeletePaymentMethod
{
    protected $attributes = [];
    protected $payment_method;

    public function __construct(PaymentMethod $payment_method)
    {
        $this->payment_method = $payment_method;
    }

    public function delete()
    {
        $this->deleteFromStripe();

        $this->payment_method->delete();

        return true;
    }

    public function deleteFromStripe()
    {
        // Attempt to delete source.
        try {
            Stripe::setApiKey(config('services.stripe.connect.secret'));

            try {
                Customer::deleteSource(
                    $this->payment_method->user->stripe_customer_id,
                    $this->payment_method->stripe_payment_method_id,
                    [],
                    ['stripe_account' => $this->payment_method->account->stripe_account_id,]
                );

                $this->payment_method->delete();
            } catch (\Exception $e) {
                Log::error($e);
                return back()->with('message.failure', $e->getMessage());
            }
        } catch (\Exception $e) {
            Log::error('DeletePaymentMethod::deleteFromStripe -- Could not delete Stripe source. ' . $e->getMessage());
        }
    }
}
