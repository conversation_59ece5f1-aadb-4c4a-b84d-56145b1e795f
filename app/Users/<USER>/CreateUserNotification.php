<?php

namespace App\Users\Services;

use App\Users\Notification;
use App\Users\User;
use Illuminate\Support\Arr;

class CreateUserNotification
{
    protected $user;
    protected $message;
    protected $attributes              = [];
    protected $data                    = [];
    protected $is_read                 = false;
    protected $type                    = 'general';
    protected $provider_transaction_id = null;
    protected $user_device_id          = null;

    public function create(): Notification
    {
        $this->attributes['user_id']    = $this->user->id;
        $this->attributes['account_id'] = $this->user->account_id;
        $this->attributes['message']    = $this->message;
        $this->attributes['data']       = $this->data;
        $this->attributes['type']       = $this->type;

        return Notification::create([
            'user_id'                 => $this->user->id,
            'msg'                     => substr($this->message, 0, 200),
            'type'                    => Arr::get($this->data, 'type', $this->type),
            'is_read'                 => $this->is_read,
            'p_t'                     => Arr::get($this->data, 'p_t'),
            'p_t_id'                  => Arr::get($this->data, 'p_t_id'),
            't'                       => Arr::get($this->data, 't'),
            't_id'                    => Arr::get($this->data, 't_id'),
            'provider_transaction_id' => $this->provider_transaction_id,
            'user_device_id'          => $this->user_device_id,
        ]);
    }

    public function forUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    public function withMessage($message = [])
    {
        $this->message = $message;

        return $this;
    }

    public function withData($data = [])
    {
        $this->data = $data;

        return $this;
    }

    public function setProviderTransactionId($transaction_id)
    {
        $this->provider_transaction_id = $transaction_id;

        return $this;
    }

    public function withUserDeviceId($user_device_id)
    {
        $this->user_device_id = $user_device_id;

        return $this;
    }

    public function markAsRead()
    {
        $this->attributes['is_read'] = 1; //Carbon::now();

        return $this;
    }
}
