<?php

namespace App\Users\Traits;

trait UserInteraction
{
    public function assignedInteractions()
    {
        return $this->hasMany(Interaction::class, 'assigned_to_user_id', 'id');
    }

    public function completedInteractions()
    {
        return $this->hasMany(Interaction::class, 'completed_by_user_id', 'id');
    }

    public function createdInteractions()
    {
        return $this->hasMany(Interaction::class, 'created_by_user_id', 'id');
    }

    public function remindInteractions()
    {
        return $this->hasMany(Interaction::class, 'remind_user_id', 'id');
    }

    public function userInteractions()
    {
        return $this->hasMany(Interaction::class, 'user_id', 'id');
    }
}
