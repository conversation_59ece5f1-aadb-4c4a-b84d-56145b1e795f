<?php

namespace App\Users\Services;

use App\Involvement\Area;
use App\Involvement\Subarea;
use App\Users\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateInvolvementSelection
{
    protected $user;
    protected $area                        = null;
    protected $subarea                     = null;
    protected $is_approved_for_assignments = null;
    protected $show_in_volunteer_list      = null;

    public function __construct(User|int $user)
    {
        if (is_int($user)) {
            $this->user = User::where('account_id', auth()->user()->account_id)->find($user);
        } else {
            $this->user = $user;
        }

        return $this;
    }

    public function forArea(Area|null $area = null)
    {
        if ($area) {
            $this->area = $area;
        }

        return $this;
    }

    public function forSubarea(Subarea|null $subarea = null)
    {
        if ($subarea) {
            $this->subarea = $subarea;

            // We always need to know our area for future actions, so set it here if it's not already set.
            if (!$this->area) {
                $this->area = $this->subarea->area;
            }
        }

        return $this;
    }

    public function isApprovedForAssignments($flag = true)
    {
        $this->is_approved_for_assignments = $flag;

        return $this;
    }

    public function showInVolunteerList($flag = true)
    {
        $this->show_in_volunteer_list = $flag;

        return $this;
    }

    public function delete()
    {
        if ($this->area->category->account_id != $this->user->account_id) {
            Log::error('UpdateInvolvementSelection::delete() for area user does not have access to.', [
                'user_id'     => $this->user?->id,
                'category_id' => $this->area?->category ? $this->area?->category->id : null,
                'area_id'     => $this->area ? $this->area->id : null,
                'subarea_id'  => $this->subarea ? $this->subarea->id : null,
            ]);

            return false;
        }

        Log::info('UpdateInvolvementSelection::delete() for user.', [
            'user_id'     => $this->user?->id,
            'category_id' => $this->area->category ? $this->area?->category->id : null,
            'area_id'     => $this->area ? $this->area->id : null,
            'subarea_id'  => $this->subarea ? $this->subarea->id : null,
        ]);

        if ($this->subarea) {
            DB::table('involvement_to_user')
                ->where('user_id', $this->user->id)
                ->where('involvement_subarea_id', $this->subarea->id)
                ->limit(1)
                ->delete();
        } elseif ($this->area) {
            DB::table('involvement_to_user')
                ->where('user_id', $this->user->id)
                ->where('involvement_area_id', $this->area->id)
                ->whereNull('involvement_subarea_id')
                ->limit(1)
                ->delete();
        }
    }

    public function save()
    {
        if ($this->area?->category->account_id != $this->user->account_id) {
            Log::error('UpdateInvolvementSelection::save() for area user does not have access to.', [
                'user_id'     => $this->user?->id,
                'category_id' => $this->area->category ? $this->area?->category->id : null,
                'area_id'     => $this->area ? $this->area->id : null,
                'subarea_id'  => $this->subarea ? $this->subarea->id : null,
            ]);

            return false;
        }

        if ($this->subarea) {
            $record = DB::table('involvement_to_user')
                ->where('user_id', $this->user->id)
                ->where('involvement_area_id', $this->area->id)
                ->where('involvement_subarea_id', $this->subarea->id)
                ->first();

            if (!$record) {
                DB::table('involvement_to_user')
                    ->where('user_id', $this->user->id)
                    ->where('involvement_area_id', $this->area->id)
                    ->where('involvement_subarea_id', $this->subarea->id)
                    ->insert([
                        'created_at'                  => now(),
                        'user_id'                     => $this->user->id,
                        'involvement_category_id'     => $this->area->category->id,
                        'involvement_area_id'         => $this->area->id,
                        'involvement_subarea_id'      => $this->subarea->id,
                        'is_approved_for_assignments' => $this->is_approved_for_assignments ?: $this->subarea->auto_approve_for_assignments,
                        'show_in_volunteer_list'      => $this->show_in_volunteer_list ?: $this->subarea->auto_show_in_volunteer_list,
                    ]);
            } else {
                if ($this->is_approved_for_assignments !== null) {
                    DB::table('involvement_to_user')
                        ->where('id', $record->id)
                        ->update([
                            'updated_at'                  => now(),
                            'is_approved_for_assignments' => $this->is_approved_for_assignments,
                        ]);
                }
                if ($this->show_in_volunteer_list !== null) {
                    DB::table('involvement_to_user')
                        ->where('id', $record->id)
                        ->update([
                            'updated_at'             => now(),
                            'show_in_volunteer_list' => $this->show_in_volunteer_list,
                        ]);
                }
            }
        } elseif ($this->area) {
            $record = DB::table('involvement_to_user')
                ->where('user_id', $this->user->id)
                ->where('involvement_area_id', $this->area->id)
                ->whereNull('involvement_subarea_id')
                ->first();

            if (!$record) {
                DB::table('involvement_to_user')
                    ->where('user_id', $this->user->id)
                    ->where('involvement_area_id', $this->area->id)
                    ->whereNull('involvement_subarea_id')
                    ->insert([
                        'created_at'                  => now(),
                        'user_id'                     => $this->user->id,
                        'involvement_category_id'     => $this->area->category->id,
                        'involvement_area_id'         => $this->area->id,
                        'is_approved_for_assignments' => $this->is_approved_for_assignments ?: $this->area->auto_approve_for_assignments,
                        'show_in_volunteer_list'      => $this->show_in_volunteer_list ?: $this->area->auto_show_in_volunteer_list,
                    ]);
            } else {
                if ($this->is_approved_for_assignments !== null) {
                    DB::table('involvement_to_user')
                        ->where('id', $record->id)
                        ->update([
                            'updated_at'                  => now(),
                            'is_approved_for_assignments' => $this->is_approved_for_assignments,
                        ]);
                }
                if ($this->show_in_volunteer_list !== null) {
                    DB::table('involvement_to_user')
                        ->where('id', $record->id)
                        ->update([
                            'updated_at'             => now(),
                            'show_in_volunteer_list' => $this->show_in_volunteer_list,
                        ]);
                }
            }
        }

        return true;
    }

}
