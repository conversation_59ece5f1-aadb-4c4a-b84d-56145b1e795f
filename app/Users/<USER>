<?php

namespace App\Users;

use App\Base\Models\Model;

class Phone extends Model
{
    protected $table = 'user_phones';

    protected $fillable = [
        'user_id',
        'family_id',
        'type',
        'mobile_provider',
        'number',
        'extension',
        'is_family',
        'is_hidden',
        'is_primary',
        'messages_opt_out',
    ];

    public static $types = array(
        'home'   => 'Home',
        'work'   => 'Work',
        'mobile' => 'Mobile',
        'church' => 'Church',
        'other'  => 'Other',
    );

    public static function getTypeKeys()
    {
        return array_keys(self::$types);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function family()
    {
        return $this->belongsTo(Family::class, 'family_id', 'id');
    }

    // This will include numbers marked "family"
    public function scopeIncludeFamilyPhones($query, User $user)
    {
        return $query->when($user->family_id, function ($query) use ($user) {
            $query->orWhere(function ($query2) use ($user) {
                $query2->where('family_id', $user->family_id);
                $query2->whereNotNull('family_id');
                $query2->where('family_id', '<>', '0');
            });
        });
    }

    // This will include any phone numbers belonging to any family member.
    public function scopeIncludeAllFamilyPhones($query, User $user)
    {
        return $query->when($user->family_id, function ($query) use ($user) {
            return $query->orWhereIn('user_id', User::select('id')
                ->where('users.account_id', $user->account_id)
                ->where('users.family_id', $user->family_id)
                ->get());
        });
    }

    public function scopeIsFamily($query)
    {
        return $query->whereNotNull('family_id');
    }

    public function scopeIsNotFamily($query)
    {
        return $query->where(function ($query2) {
            $query2->whereNull('family_id')
                ->where('is_family', false);
        });
    }

    public function scopeIsPrimary($query)
    {
        return $query->where('is_primary', true);
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeIsMobile($query)
    {
        return $query->where(function ($query) {
            $query->where('type', 'mobile')
                ->orWhere('type', 'cell');
        });
    }

    public function formattedNumber($separator = '-', $convert = false, $trim = true)
    {
        return self::format($this->number, $separator, $convert, $trim);
    }

    public static function format($phone = '', $separator = '', $convert = false, $trim = true)
    {
        // If we have not entered a phone number just return empty
        if (empty($phone)) {
            return '';
        }

        // Strip out any extra characters that we do not need only keep letters and numbers
        $phone = preg_replace("/[^0-9A-Za-z]/", "", $phone);

        // Do we want to convert phone numbers with letters to their number equivalent?
        // Samples are: 1-800-TERMINIX, 1-800-FLOWERS, 1-800-Petmeds
        if ($convert == true) {
            $replace = array(
                '2' => array('a', 'b', 'c'),
                '3' => array('d', 'e', 'f'),
                '4' => array('g', 'h', 'i'),
                '5' => array('j', 'k', 'l'),
                '6' => array('m', 'n', 'o'),
                '7' => array('p', 'q', 'r', 's'),
                '8' => array('t', 'u', 'v'),
                '9' => array('w', 'x', 'y', 'z'),
            );

            // Replace each letter with a number
            // Notice this is case insensitive with the str_ireplace instead of str_replace
            foreach ($replace as $digit => $letters) {
                $phone = str_ireplace($letters, $digit, $phone);
            }
        }

        // If we have a number longer than 11 digits cut the string down to only 11
        // This is also only ran if we want to limit only to 11 characters
        if ($trim == true && strlen($phone) > 11) {
            $phone = substr($phone, 0, 11);
        }

        // Perform phone number formatting here
        if (strlen($phone) == 7) {
            return preg_replace("/([0-9a-zA-Z]{3})([0-9a-zA-Z]{4})/", "$1$separator$2", $phone);
        } elseif (strlen($phone) == 10) {
            return preg_replace("/([0-9a-zA-Z]{3})([0-9a-zA-Z]{3})([0-9a-zA-Z]{4})/", "$1$separator$2$separator$3", $phone);
        } elseif (strlen($phone) == 11) {
            return preg_replace("/([0-9a-zA-Z]{1})([0-9a-zA-Z]{3})([0-9a-zA-Z]{3})([0-9a-zA-Z]{4})/", "$1$separator$2$separator$3$separator$4", $phone);
        }

        // Return original phone if not 7, 10 or 11 digits long
        return $phone;
    }
}
