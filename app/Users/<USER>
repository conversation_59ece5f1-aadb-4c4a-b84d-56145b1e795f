<?php

namespace App\Users;

use App\Base\Models\Model;

class Family extends Model
{
    protected $table = 'user_families';

    protected $fillable = [
        'head_of_house_user_id',
        'name',
        'date_membership',
        'primary_user_address_id',
        'primary_user_phone_id',
        'is_member',
        'is_visitor',
        'is_other',
    ];

    public function address()
    {
        return $this->belongsTo(Address::class, 'primary_user_address_id', 'id');
    }

    public function headOfHouse()
    {
        return $this->belongsTo(User::class, 'head_of_house_user_id', 'id');
    }

    public function phone()
    {
        return $this->belongsTo(UserPhone::class, 'primary_user_phone_id', 'id');
    }

    public function phones()
    {
        return $this->hasMany(Phone::class);
    }

    public function users()
    {
        return $this->hasMany(User::class, 'family_id', 'id');
    }
}
