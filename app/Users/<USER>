<?php

namespace App\Users;

use App\Base\Models\Model;

class Interaction extends Model
{
    protected $table = 'user_interactions';

    protected $fillable = [
        'user_id',
        'remind_at',
        'remind_user_id',
        'created_by_user_id',
        'assigned_to_user_id',
        'due_at',
        'completed_at',
        'completed_by_user_id',
        'action',
        'action_routes',
        'notes',
        'private_notes',
    ];

    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assigned_to_user_id', 'id');
    }

    public function completedByUser()
    {
        return $this->belongsTo(User::class, 'completed_by_user_id', 'id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by_user_id', 'id');
    }

    public function remindUser()
    {
        return $this->belongsTo(User::class, 'remind_user_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
