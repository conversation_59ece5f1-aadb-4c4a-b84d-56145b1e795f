<?php

namespace App\Users;

use App\Base\Models\Model;
use App\Users\Scopes\RoleVisibleToScope;
use Illuminate\Database\Eloquent\SoftDeletes;

class Role extends Model
{
    use SoftDeletes;

    protected $table = 'user_roles';

    protected $fillable = [
        'account_id',
        'account_location_id',
        'name',
        'key',
        'sort_id',
        'description',
        'indicates_membership',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new RoleVisibleToScope())->getQuery($query, $user);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_to_role', 'user_role_id', 'user_id');
    }

    public function safeUserCount(User $user)
    {
        return $this->users()->visibleTo($user)->count();
    }

    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'user_role_permission', 'user_role_id', 'user_role_permission_id');
    }

    public function hasPermission($permission)
    {
        $value = is_int($permission) ? $permission : (is_string($permission) ? $permission : (is_object($permission) ? $permission->id : null));

        return $this->permissions()->where('user_role_permission_id', $value)->count() > 0 ? true : false;
    }
}
