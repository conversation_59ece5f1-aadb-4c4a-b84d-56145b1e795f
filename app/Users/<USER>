<?php

namespace App\Users;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Accounts\Payout;
use App\Base\Models\Model;
use App\Base\Models\Traits\AuditTrait;
use App\Finance\StripeWebhookEvent;
use App\Finance\Transaction;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Payment extends Model
{
    use AuditTrait;

    use SoftDeletes;

    protected $table = 'user_payments';

    protected $casts = [
        'is_pending'   => 'boolean',
        'is_success'   => 'boolean',
        'is_failed'    => 'boolean',
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'deleted_at'   => 'datetime',
        'cleared_at'   => 'datetime',
        'captured_at'  => 'datetime',
        'disputed_at'  => 'datetime',
        'deposited_at' => 'datetime',
        'applied_at'   => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'created_at',
        'cleared_at',
        'deposited_at',
        'applied_at',
        'user_id',
        'user_payment_method_id',
        'user_payment_schedule_id',
        'account_contribution_bucket_id',
        'account_finance_bucket_id',
        'account_payout_id',
        'finance_transaction_id',
        'provider',
        'provider_payment_id',
        'stripe_charge_id',
        'stripe_charge_object',
        'stripe_status',
        'stripe_balance_transaction_id',
        'type',
        'title',
        'description',
        'original_amount',
        'amount',
        'amount_fee',
        'amount_deposited',
        'amount_platform_fee',
        'amount_refunded',
        'is_contribution',
        'is_payment',
        'is_reimbursement',
        'is_hidden',
        'is_pending',
        'is_success',
        'is_failed',
        'status',
    ];

    public static $types = [
        'card'      => 'Card',
        'ach_debit' => 'Bank Account',
        'check'     => 'Check',
        'cash'      => 'Cash',
    ];

    public static $statuses = [
        'succeeded' => 'Completed',
        'pending'   => 'Pending Processing',
        'failed'    => 'Failed',
        'other'     => 'Other',
    ];

    public static $statuses_font_awesome = [
        'succeeded' => '<i class="fas fa-check-circle"></i>',
        'pending'   => '<i class="fas fa-sync-alt"></i>',
        'failed'    => '<i class="fas fa-exclamation-circle"></i>',
        'other'     => null,
    ];

    public static function getTypeKeys()
    {
        return array_keys(self::$types);
    }

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('user_payments.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function bucket()
    {
        return $this->belongsTo(FinanceBucket::class, 'account_finance_bucket_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class, 'user_payment_method_id', 'id');
    }

    public function paymentSchedule()
    {
        return $this->belongsTo(PaymentSchedule::class);
    }

    public function payout()
    {
        return $this->belongsTo(Payout::class, 'account_payout_id', 'id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'finance_transaction_id');
    }

    public function stripeEvents()
    {
        return $this->hasMany(StripeWebhookEvent::class, 'stripe_charge_id', 'stripe_charge_id');
    }

    public function scopeContributionsOnly($query)
    {
        return $query->where('is_contribution', true);
    }

    public function scopeReimbursementsOnly($query)
    {
        return $query->where('is_reimbursement', true);
    }

    public function scopePaymentsOnly($query)
    {
        return $query->where('is_payment', true);
    }

    public function getStripeChargeObject()
    {
        return json_decode($this->stripe_charge_object);
    }

    public function getFontAwesomeIcon()
    {
        if (Arr::has(self::$statuses_font_awesome, $this->status)) {
            return Arr::get(self::$statuses_font_awesome, $this->status);
        }

        return null;
    }
}
