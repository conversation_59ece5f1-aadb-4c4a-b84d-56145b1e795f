<?php

namespace App\Users;

use App\Accounts\Account;
use App\Base\Models\Model;
use Illuminate\Support\Str;

class Activity extends Model
{
    const UPDATED_AT = null;

    protected $table = 'user_activity';

    protected $casts = [
        'created_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'site',
        'method',
        'path',
    ];

    public static $sites = [
        'admin'      => 'Admin',
        'app'        => 'App',
        'mobile_api' => 'Mobile API',
//        'web'        => 'Web App',
        'api'        => 'Standard API',
        'podcast'    => 'Podcast',
        'frontend'   => 'Frontend',
        'other'      => 'Other',
    ];

    public static $methods = [
        'GET'     => 'GET',
        'POST'    => 'POST',
        'PUT'     => 'PUT',
        'PATCH'   => 'PATCH',
        'DELETE'  => 'DELETE',
        'OPTIONS' => 'OPTIONS',
        'OTHER'   => 'OTHER',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public static function getSiteKeys()
    {
        return array_keys(self::$sites);
    }

    public static function getMethodKeys()
    {
        return array_keys(self::$methods);
    }

    public static function getSiteFromString($url)
    {
        foreach (self::getSiteKeys() as $key) {
            if (Str::contains($url, config('app.domains.' . $key))) {
                return $key;
            }
        }

        return 'other';
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
