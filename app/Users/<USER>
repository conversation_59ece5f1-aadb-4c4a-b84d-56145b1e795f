<?php

namespace App\Users;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use App\Base\Models\Model;
use App\Groups\Post;
use App\Messages\Message;
use App\Messages\MessageHandler;
use App\Messages\MessageType;
use App\Prayers\PrayerFolder;
use App\Users\Scopes\GroupVisibleToScope;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class Group extends Model
{
    use SoftDeletes;

    protected $table = 'user_groups';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'name',
        'url_name',
        'description',
        'creator_id',
        'allow_individual_to_toggle',
        'indicates_membership',
        'indicates_visitor',
        'is_hidden',
        'is_suspended',
        'allow_all_members_to_send',
        'allow_lightpost_users_to_email',
        'enable_posts',
        'allow_members_to_post',
        'allow_members_to_comment',
        'allow_members_to_view_membership',
        'uuid',
        'is_default_visitor_group',
        'is_default_member_group',
        'sort_id',
        'allow_members_to_create_calendar_events',
    ];

    static public $settings_pivot_fields = [
        'receive_group_emails',
        'receive_group_sms',
        'receive_group_voice',
        'receive_group_post_email_summaries',
        'receive_group_post_mobile_notifications',
        'receive_all_group_post_comment_mobile_notifications',
        'receive_group_own_post_comment_mobile_notifications',
        //                'receive_group_post_summary_mns',
        //                'summary_mns_frequency',
        'is_favorite',
        'is_admin',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new GroupVisibleToScope())->getQuery($query, $user);
    }

    public function scopeViewableToUser($query, User $user)
    {
        // Posts must be enabled
        return $this->visibleTo($user)
            ->hasPostsEnabled()
            // One of these conditions must be true.
            ->where(function ($parentQuery) use ($user) {
                $parentQuery->where(function ($query) use ($user) {
                    $query->whereHas('users', function ($builder) use ($user) {
                        $builder->where('user_id', $user->id);
                    });
                });
                $parentQuery->orWhere(function ($query) {
                    $query->where('is_hidden', 0)
                        ->where('allow_individual_to_toggle', 1);
                });
            });
    }

    // Determine if an instance of a group if viewable to a user.
    public function isViewableToUser(User $user)
    {
        return self::viewableToUser($user)
            ->where('id', $this->id)
            ->exists();
    }

    public function userIsAdmin(User $user)
    {
        return $this->admins()->where('user_id', $user->id)->exists();
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id', 'id');
    }

    public function accountLocation()
    {
        return $this->belongsTo(AccountLocation::class, 'account_location_id', 'id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function posts()
    {
        return $this->hasMany(Post::class, 'user_group_id')
            ->whereHas('creator')
            ->orderBy('published_at', 'desc');
    }

    public function prayerFolders()
    {
        return $this->hasMany(PrayerFolder::class, 'user_group_id');
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_to_group', 'user_group_id', 'user_id')
            ->as('settings')
            ->withPivot(self::$settings_pivot_fields);
    }

    public function admins()
    {
        return $this->belongsToMany(User::class, 'user_to_group', 'user_group_id', 'user_id')
            ->wherePivot('is_admin', true)
            ->as('settings')
            ->withPivot(self::$settings_pivot_fields);
    }

    public function usersWithoutSettings()
    {
        return $this->belongsToMany(User::class, 'user_to_group', 'user_group_id', 'user_id');
    }

    public function senders()
    {
        return $this->belongsToMany(User::class, 'user_group_senders', 'user_group_id');
    }

    public function messages()
    {
        return $this->hasMany(Message::class, 'user_group_id');
    }

    public function messageHandlers()
    {
        return $this->hasMany(MessageHandler::class, 'user_group_id');
    }

    public function visibleMessageHandlers()
    {
        return $this->hasMany(MessageHandler::class, 'user_group_id');
    }

    public function messageTypes()
    {
        return $this->belongsToMany(MessageType::class, 'user_group_to_message_type', 'user_group_id', 'message_type_id');
    }

    public function hasMessageType($message_type_id)
    {
        // If we're given a string, search for the message type by code.
        if (is_numeric($message_type_id)) {
            $message_type_id = intval($message_type_id);
        } else {
            $message_type_id = MessageType::where('code', $message_type_id)->value('id');
        }

        return $this->messageTypes->where('id', $message_type_id)->isNotEmpty();
    }

    public function availableMessageTypes()
    {
        return MessageType::whereIn('id', $this->receivers()->distinct('message_type_id')->select('message_type_id')->get()->toArray())->get();
    }

    public function markGroupNotificationsAsReadForUser(User $user)
    {
        // We only run an update (which locks tables) if there are notifications to dismiss.
        if (Notification::visibleTo($user)
            ->where('p_t', 'user_groups')
            ->where('p_t_id', $this->id)
            ->where('is_read', 0)
            ->exists()) {
            // Clear our notifications
            return Notification::visibleTo($user)
                ->where('p_t', 'user_groups')
                ->where('p_t_id', $this->id)
                ->update(['is_read' => 1]);
        } else {
            return true;
        }
    }

    public function userTotalUnreadNotificationsCount($user_id)
    {
        return Notification::where('user_id', $user_id)
            ->where('p_t', 'user_groups')
            ->where('is_read', false)
            ->count();
    }

    public function userUnreadNotificationsCount($user_id)
    {
        return Notification::where('user_id', $user_id)
//            ->where('meta->user_group_post_id', $this->id) // If we were to search our JSON column -- not indexed!
            ->where('p_t', 'user_groups')
            ->where('p_t_id', $this->id)
            ->where('is_read', false)
            ->count();
    }

    public function scopeWithUnreadNotificationCountForUser($query, $user_id)
    {
        return $query->withCount([
            'notifications as unread_notifications_count' => function ($query) use ($user_id) {
                $query->where('user_id', $user_id)
                    ->where('p_t', 'user_groups')
                    ->where('p_t_id', DB::raw('user_groups.id'))
                    ->where('is_read', false);
            },
        ]);
    }

    public function notifications()
    {
        return $this->hasMany(\App\Users\Notification::class, 'p_t_id')
            ->where('p_t', 'user_groups');
    }

    public function getCreatedAttribute()
    {
        return $this->created_at;
    }

    public function scopeIndicatesMembership($query)
    {
        return $query->where('indicates_membership', true);
    }

    public function scopeIndicatesVisitorGroup($query)
    {
        return $query->where('indicates_visitor', true);
    }

    public function scopeAllowsIndividualToToggle($query)
    {
        return $query->where('allow_individual_to_toggle', 1);
    }

    public function scopeHasPostsEnabled($query)
    {
        return $query->where('enable_posts', true);
    }

    public function scopeAllowsMembersToPost($query)
    {
        return $query->where('allow_members_to_post', true);
    }

    public function scopeAllowsMembersToComment($query)
    {
        return $query->where('allow_members_to_comment', true);
    }

    public function scopeAllowsMembersToViewMembership($query)
    {
        return $query->where('allow_members_to_view_membership', true);
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where('is_hidden', 0);
    }

    public function allowIndividualToggle()
    {
        return boolval($this->allow_individual_to_toggle);
    }

    public function isHidden()
    {
        return boolval($this->is_hidden);
    }

    public function isSuspended()
    {
        return boolval($this->is_suspended);
    }

    public function getEmailUnsubscribeLinkForUser(User $user)
    {
        if (!$this->uuid) {
            $this->uuid = Str::uuid();
            $this->save();
        }

        return route('app.public.groups.unsubscribe.email', [$this, $user->id, $this->uuid]);
    }

    public function getEmailWhyLink()
    {
        if (!$this->uuid) {
            $this->uuid = Str::uuid();
            $this->save();
        }

        return route('app.public.groups.emails.why', [$this, $this->uuid]);
    }
}
