<?php

namespace App\Users;

use App\Base\Models\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Address extends Model
{
    protected $table = 'user_addresses';

    protected $fillable = [
        'user_id',
        'family_id',
        'type',
        'label',
        'address1',
        'address2',
        'address3',
        'city',
        'state',
        'zip',
        'county',
        'country',
        'is_family',
        'is_mailing',
        'is_primary_for_user',
        'is_hidden',
        'map_thumbnail_url_512',
    ];

    public static $types = array(
        'home'     => 'Home',
        'business' => 'Business',
        'mailing'  => 'Mailing',
        'other'    => 'Other',
    );

    public static function getTypeKeys()
    {
        return array_keys(self::$types);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function family()
    {
        return $this->belongsTo(User::class, 'family_id', 'id');
    }

    public function scopeIncludeFamilyAddresses($query, User $user)
    {
        return $query->when($user->family_id, function ($query) use ($user) {
            $query->orWhere(function ($query2) use ($user) {
                $query2->where('family_id', $user->family_id)
                    ->whereNotNull('family_id')
                    ->where('family_id', '<>', '0');
            });
        })
            ->whereHas('user', function ($query) use ($user) {
                $query->where('account_id', $user->account_id);
            });
    }

    // This will include any addresses belonging to any family member.
    public function scopeIncludeAllFamilyAddresses($query, User $user)
    {
        return $query->when($user->family_id, function ($query) use ($user) {
            return $query->orWhereIn('user_id', User::select('id')
                ->where('users.account_id', $user->account_id)
                ->where('users.family_id', $user->family_id)
                ->get());
        });
    }

    public function scopeIsFamily($query)
    {
        return $query->whereNotNull('family_id');
    }

    public function scopeIsNotFamily($query)
    {
        return $query->whereNull('family_id');
    }

    public function scopeIsMailing($query)
    {
        return $query->where('is_mailing', true);
    }

    public function scopeIsNotMailing($query)
    {
        return $query->where('is_mailing', false);
    }

    public function scopeIsUserPrimary($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('is_primary_for_user', true)
                ->where('user_id', $user->id);
        });
    }

    public function scopeIsNotUserPrimary($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('is_primary_for_user', false)
                ->where('user_id', $user->id);
        });
    }

    public function scopeIsHidden($query)
    {
        return $query->where('is_hidden', true);
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }

    public static function format($addy, $new_line_spacer = '<br>')
    {
        $r_str = null;

        if ($addy->address1) {
            $r_str .= $addy->address1 . $new_line_spacer;
        }
        if ($addy->address2) {
            $r_str .= $addy->address2 . $new_line_spacer;
        }
        if ($addy->city) {
            $r_str .= $addy->city . ', ';
        }
        if ($addy->state) {
            $r_str .= $addy->state . ' ';
        }
        if ($addy->zip) {
            $r_str .= $addy->zip;
        }

        return $r_str;
    }

    public function getFullAddressHTML()
    {
        $return_string = null;

        if (isset($this->company) && $this->company > '') {
            $return_string = @$this->company . '<br>';
        }

        $return_string .= @$this->address1 . '<br>';

        if (isset($this->address2) && $this->address2 > '') {
            $return_string .= $this->address2 . '<br>';
        }

        $return_string .= @$this->city . ', ' . @$this->state . ' ' . @$this->zip;

        return $return_string;
    }

    public function getAddressString()
    {
        $return_string = null;

        $return_string .= @$this->address1 . ', ';

        if (isset($this->address2) && $this->address2 > '') {
            $return_string .= $this->address2 . ', ';
        }

        $return_string .= @$this->city . ', ' . @$this->state . ', ' . @$this->zip;

        return $return_string;
    }

    public function getStateCountryHTML()
    {
        $return_string = null;

        $return_string .= @$this->state . ', ' . @$this->country;

        return $return_string;
    }

    public function getStaticGoogleMapsImage($width = 90, $height = 50, $element_data = null)
    {
        // https://maps.googleapis.com/maps/api/staticmap?center=6810+Cottonwood+Crest+Ln,+Katy,+TX+77493&zoom=18&scale=1&size=600x300&maptype=roadmap&key=10000001&format=png&visual_refresh=true&markers=size:mid%7Ccolor:0xff7f00%7Clabel:1%7CAddress
        # URL to Google Maps to view this address
        $maps_url = 'https://maps.google.com/maps?q=' . urlencode($this->getAddressString()) . '&z=17';
        # URL to the static image from Google Maps
        $maps_image_url = 'https://maps.googleapis.com/maps/api/staticmap?sensor=true&zoom=15&center=' . urlencode($this->getAddressString()) . '&size=' . $width . 'x' . $height . '&key=' . config('google.maps.api_key');
        return '<a href="' . $maps_url . '" target="_blank" ' . $element_data . '>  <img src="' . $maps_image_url . '" width="' . ceil($width / 2) . '" height="' . ceil($height / 2) . '" />  </a>';
    }

    public function getMapThumbnailUrl()
    {
        if ($this->map_thumbnail_url_512) {
            if (config('digital_ocean.account_files.cdn_url')) {

            }
            return $this->map_thumbnail_url_512;
        }

        // Get our PNG from Google
        $new_file_name     = $this->user->account_id . '--' . Str::random(4) . '--' . $this->id . '--' . Str::random(10) . '.png';
        $new_file_contents = file_get_contents('https://maps.googleapis.com/maps/api/staticmap?sensor=true&zoom=17&center=' . urlencode($this->getAddressString()) . '&size=512x512&key=' . config('google.maps.api_key'));

        try {
            if (Storage::disk('account-files')->put($this->user->account_id . '/address/thumbnail/' . $new_file_name, $new_file_contents)) {
                $this->map_thumbnail_url_512 = 'https://' . config('digital_ocean.account_files.bucket') . '.' . config('digital_ocean.account_files.region') . '.digitaloceanspaces.com/' . $this->user->account_id . '/address/thumbnail/' . $new_file_name;
                $this->save();

                return $this->map_thumbnail_url_512;
            }
        } catch (\Exception $e) {
            Log::error($e);
        }

        return null;
    }
}
