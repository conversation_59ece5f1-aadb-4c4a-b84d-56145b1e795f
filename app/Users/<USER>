<?php

namespace App\Users;

use App\Accounts\Account;
use Illuminate\Database\Eloquent\Model;

class GroupSender extends Model
{
    protected $table = 'user_group_senders';

    protected $fillable = [
        'account_id',
        'user_id',
        'user_group_id',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
