<?php

namespace App\Users;

use App\Accounts\Account;
use App\Base\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Device extends Model
{
    use SoftDeletes;

    protected $table = 'user_devices';

    protected $casts = [
        'created_at'           => 'datetime',
        'updated_at'           => 'datetime',
        'deleted_at'           => 'datetime',
        'last_refreshed_at'    => 'datetime',
        'last_message_sent_at' => 'datetime',
    ];

    protected $fillable = [
        'last_message_sent_at',
        'last_refreshed_at',
        'account_id',
        'user_id',
        'name',
        'pusher_id',
        'fcm_id',
        'device',
        'device_version',
        'device_token',
        'type',
        'is_primary',
        'receives_notifications',
        'deleted_reason',
    ];

    public static $types = [
        'ios'     => 'iOS',
        'android' => 'Android',
        'browser' => 'Browser',
        'other'   => 'Other',
    ];

    public static $notification_types = [
        'WA_new'  => 'Worship Assignment',
        'GP_new'  => 'Group Post',
        'GPC_new' => 'Group Post Comment',
    ];

    public static function getTypeKeys()
    {
        return array_keys(self::$types);
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function scopeIsPrimary($query)
    {
        return $query->where('is_primary', true);
    }
}
