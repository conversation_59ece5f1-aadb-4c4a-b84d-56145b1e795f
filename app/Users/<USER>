<?php

namespace App\Users;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Base\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class PaymentSchedule extends Model
{
    use SoftDeletes;

    protected $table = 'user_payment_schedules';

    protected $fillable = [
        'account_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'last_run_at',
        'user_id',
        'user_payment_method_id',
        'account_finance_bucket_id',
        'amount',
        'recur_start_at',
        'recur_end_at',
        'recur_frequency',
        'recur_day_of_week',
        'recur_day_of_month',
        'details',
    ];

    protected $casts = [
        'created_at'                => 'datetime',
        'updated_at'                => 'datetime',
        'deleted_at'                => 'datetime',
        'last_run_at'               => 'datetime',
        'recur_start_at'            => 'datetime',
        'recur_end_at'              => 'datetime',
        'user_id'                   => 'integer',
        'user_payment_method_id'    => 'integer',
        'account_finance_bucket_id' => 'integer',
        'amount'                    => 'integer',
        'is_paused'                 => 'boolean',
    ];

    public static $recur_frequencies = [
        'WEEKLY',
        'MONTHLY',
    ];

    public static $days_of_week = [
        7 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    public function generateUlid()
    {
        if (!$this->ulid) {
            $this->ulid = Str::ulid()->toBase32();
            $this->save();
        }
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function financeBucket()
    {
        return $this->belongsTo(FinanceBucket::class, 'account_finance_bucket_id', 'id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'user_payment_schedule_id');
    }

    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class, 'user_payment_method_id', 'id');
    }

    public function updateLastRunAt()
    {
        $this->last_run_at = now();
        $this->save();
    }

    public function canCharge(): bool|\Exception
    {
        // If the schedule is paused, we cannot charge it.
        if ($this->is_paused) {
            throw new \Exception('Cannot charge a paused schedule.');
        }

        // If the schedule has been run in the last 5 days, we cannot charge it.
        if (!empty($this->last_run_at) && $this->last_run_at->diffInDays() < 5) {
            throw new \Exception('Cannot charge a schedule that has been run in the last 5 days.');
        }

        if ($this->recur_frequency == 'WEEKLY' && $this->recur_day_of_week != now()->timezone($this->account->timezone)->dayOfWeekIso) {
            throw new \Exception('Cannot charge a schedule that is not on the correct day of the week.');
        } elseif ($this->recur_frequency == 'MONTHLY' && $this->recur_day_of_month != now()->timezone($this->account->timezone)->day) {
            throw new \Exception('Cannot charge a schedule that is not on the correct day of the month.');
        }

        return true;
    }

    public function scopeIsPaused($query)
    {
        return $query->where('is_paused', true);
    }

    public function scopeIsNotPaused($query)
    {
        return $query->where('is_paused', false);
    }

}
