<?php

namespace App\Users;

use App\Base\Models\Model;

class Notification extends Model
{
    protected $table = 'user_notifications';

    public const UPDATED_AT = null;

    protected $fillable = [
        'user_id',
        'user_device_id',
        'p_t', // parent_table
        'p_t_id', // parent_table_id
        't', // table
        't_id', // table_id
        'msg', // message
        'type',
        'meta',
        'is_read',
        'provider_transaction_id',
    ];

    protected $casts = [
        'meta'    => 'array',
        'is_read' => 'boolean',
    ];

    public static $types = [
        'WA_new'  => 'Worship Assignment',
        'GP_new'  => 'Group Post',
        'GPC_new' => 'Group Post Comment',
        'general' => 'General',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.user_id', $user->id);
        });
    }

    public static function getTypeKeys()
    {
        return array_keys(self::$types);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeIsRead($query)
    {
        return $query->where('is_read', 1);
    }

    public function scopeIsUnread($query)
    {
        return $query->where('is_read', 0);
    }

    public function scopeIsType($query, $type)
    {
        return $query->where('type', $type);
    }
}
