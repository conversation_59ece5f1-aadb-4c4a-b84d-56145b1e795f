<?php

namespace App\Users\Policies;

use App\Users\Role;
use App\Users\User;

class RolePolicy
{
    public function before($user, $ability)
    {
        if ($user->isSuper()) {
            return true;
        }

        if (!$user->account->getSetting('feature.roles')) {
            return false;
        }
    }

    public function create(User $user)
    {
        return $user->hasPermission('roles.manage');
    }

    public function index(User $user)
    {
        return $user->hasPermission('roles.index');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('roles.index');
    }

    public function edit(User $user, Role $role)
    {
        return $user->hasPermission('roles.manage');
    }

    public function save(User $user, Role $role)
    {
        return $user->hasPermission('roles.manage');
    }

    public function delete(User $user, Role $role)
    {
        return $user->hasPermission('roles.manage');
    }

    public function assign(User $user, Role $role)
    {
        return $user->hasPermission('roles.manage');
    }

    public function unassign(User $user, Role $role)
    {
        return $user->hasPermission('roles.manage');
    }
}
