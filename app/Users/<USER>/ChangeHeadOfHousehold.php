<?php

namespace App\Users\Services;

use App\Users\User;
use Illuminate\Support\Facades\DB;

class ChangeHeadOfHousehold
{
    protected $from_user;
    protected $to_user;

    public function fromUser(User $user)
    {
        $this->from_user = $user;

        return $this;
    }

    public function toUser(User $user)
    {
        $this->to_user = $user;

        return $this;
    }

    public function change()
    {
        if (!$this->checkIfHeadOfFamily()) {
            throw new \Exception('User provided is not a head of household.');
        }

        if (!$this->to_user->id) {
            throw new \Exception('New user is invalid.');
        }

        DB::transaction(function () {
            # Family Members
            foreach ($this->from_user->familyMembers(true)->get() as $member) {
                if ($member->family_id == $this->from_user->family_id) {
                    $member->family_id = $this->to_user->id;
                    $member->save();
                }
            }

            # Addresses
            foreach ($this->from_user->addresses as $address) {
                if ($address->family_id == $this->from_user->id) {
                    $address->user_id   = $this->to_user->id;
                    $address->family_id = $this->to_user->id;
                    $address->save();
                }
            }
            # Phones
            foreach ($this->from_user->phones as $phone) {
                if ($phone->family_id == $this->from_user->id) {
                    $phone->user_id   = $this->to_user->id;
                    $phone->family_id = $this->to_user->id;
                    $phone->save();
                }
            }
            # Emails
            foreach ($this->from_user->emails as $email) {
                if ($email->family_id == $this->from_user->id) {
                    $email->user_id   = $this->to_user->id;
                    $email->family_id = $this->to_user->id;
                    $email->save();
                }
            }

            # Family Photos
            foreach ($this->from_user->familyPhotos()->get() as $photo) {
                $photo->user_id   = $this->to_user->id;
                $photo->family_id = $this->to_user->id;
                $photo->save();
            }

            # Update our main users
            $this->from_user->family_role = 'spouse';
            $this->to_user->family_role   = 'head';
            $this->from_user->family_id   = $this->to_user->id;
            $this->to_user->family_id     = $this->to_user->id;

            $this->from_user->save();
            $this->to_user->save();
        });

        return true;
    }

    protected function checkIfHeadOfFamily()
    {
        return $this->from_user->family_id == $this->from_user->id;
    }
}
