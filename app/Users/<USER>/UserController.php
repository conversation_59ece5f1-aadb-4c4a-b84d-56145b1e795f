<?php

namespace App\Users\Controllers;

use App\Attendance\Attendance;
use App\Base\Http\Controllers\Controller;
use App\Exports\Users\UserIndexExport;
use App\Jobs\Accounts\ClearUserSessions;
use App\Jobs\SendMobileNotification;
use App\Mail\App\ResetPassword;
use App\Users\Email;
use App\Users\Group;
use App\Users\PaymentMethod;
use App\Users\Requests\CreateUserRequest;
use App\Users\Requests\UpdateUserRequest;
use App\Users\Services\ChangeHeadOfHousehold;
use App\Users\Services\CreateUser;
use App\Users\Services\DeleteUser;
use App\Users\Services\UpdateUser;
use App\Users\User;
use DateTime;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;

class UserController extends Controller
{
    public function index()
    {
        $users = User::visibleTo(auth()->user())
            ->when(request()->has('query'), function ($mainQuery) {
                // We MUST wrap all of this in a "where", otherwise the "user.account_id = Y" will
                // fall inside a () block that will start including other account users!
                return $mainQuery->where(function ($mainQuery) {
                    $mainQuery->where(function ($query) {
                        $query->where('last_name', 'like', '%' . request('query') . '%');
                        $query->orWhere('first_name', 'like', '%' . request('query') . '%');
                        $query->orWhere('preferred_first_name', 'like', '%' . request('query') . '%');
                    })->orWhereHas('emails', function ($query) {
                        $query->where('email', 'like', request('query') . '%');
                    })->when(preg_replace('/[^0-9]/', '', request('query')), function ($query) {
                        $query->orWhereHas('phones', function ($query2) {
                            $query2->where('number', 'like', '%' . preg_replace('/[^0-9]/', '', request('query')) . '%');
                        });
                    })->orWhereHas('addresses', function ($query) {
                        $query->where('address1', 'like', request('query') . '%');
                    });
                });
            })
            ->when(request()->has('columns'), function ($mainQuery) {
                $mainQuery->when(Arr::exists(request()->get('columns'), 'is_baptized'), function ($query) {
                    $query->whereNotNull('is_baptized');
                });
                $mainQuery->when(Arr::exists(request()->get('columns'), 'date_background_check'), function ($query) {
                    $query->whereNotNull('date_background_check');
                });
                $mainQuery->when(Arr::exists(request()->get('columns'), 'head_of_household_only'), function ($query) {
                    $query->headsOfFamily();
                });
                $mainQuery->when(Arr::exists(request()->get('columns'), 'children_only'), function ($query) {
                    $query->childrenOnly();
                });
                $mainQuery->when(Arr::exists(request()->get('columns'), 'adults_only'), function ($query) {
                    $query->adultsOnly();
                });
                $mainQuery->when(Arr::exists(request()->get('columns'), 'men_only'), function ($query) {
                    $query->menOnly();
                });
                $mainQuery->when(Arr::exists(request()->get('columns'), 'women_only'), function ($query) {
                    $query->womenOnly();
                });
            })
            ->when(request()->has('family_roles'), function ($mainQuery) {
                $mainQuery->where(function ($subQuery) {
                    foreach (User::$family_roles as $key => $value) {
                        if (Arr::exists(request()->get('family_roles'), $key)) {
                            $subQuery->orWhere('users.family_role', $key);
                        }
                    }
                });
            })
            ->when(request()->has('groups'), function ($mainQuery) {
                $mainQuery->whereHas('groups', function ($query) {
                    $query->whereIn('user_groups.id', request()->input('groups') ?: []);
                });
            })
            ->when(request()->has('exclude_groups'), function ($mainQuery) {
                $mainQuery->whereDoesntHave('groups', function ($query) {
                    $query->whereIn('user_groups.id', request()->input('exclude_groups') ?: []);
                });
            })
            ->withFamilyLastName()
            ->with(['emails', 'photos'])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc');

        // Export as Excel file
        if (request()->get('export') == 'xlsx') {
            return Excel::download(new UserIndexExport($users), 'user-list-export.xlsx');
        }

        return view('admin.users.index')
            ->with('groups', Group::visibleTo(auth()->user())->get())
            ->with('users', $users->paginate(20, ['id', 'preferred_first_name', 'first_name', 'last_name', 'family_last_name', 'family_id', 'family_role']));
    }

    public function view(User $user)
    {
        return view('admin.users.view')
            ->with('user', $user);
    }

    public function attendance(User $user)
    {
        return view('admin.users.tabs.attendance')
            ->with('user', $user)
            ->with('attendance_dates', Attendance::visibleTo(auth()->user())->getDatesAttendanceHappensAfter(new DateTime('3 months ago'))->orderBy('date_attendance', 'desc')->get())
            ->with('attendance_types', Attendance::visibleTo(auth()->user())->with('type')->forUser($user)->getAttendanceTypesAfter(new DateTime('3 months ago'))->get())
            ->with('attendance', Attendance::visibleTo(auth()->user())->forUser($user)->attendanceAfterDate(new DateTime('3 months ago'))->with(['user:id,first_name,last_name', 'type'])->get());
    }

    public function giving(User $user)
    {
        $payment_methods = [];

        $payment_methods = $user->paymentMethods;

        return view('admin.users.tabs.giving')
            ->with('user', $user)
            ->with('payment_methods', $payment_methods);
    }

    public function quickCreate(User $user)
    {
        return view('admin.users.modals.quick-create')
            ->with('user', $user);
    }

    protected function quickCreateSubmit(CreateUserRequest $request, User $user)
    {
        try {
            $user = DB::transaction(function () use ($request, $user) {
                $create_user_service = (new CreateUser())
                    ->forAccount(auth()->user()->account)
                    ->withRoles($request->get('roles'))
                    ->withGroups($request->get('groups'));

                return $create_user_service->create(array_merge($request->all(), [
                    'family_id' => $user->family_id,
                ]));
            });

            Log::info('User::save() --  User # ' . auth()->user()->id . ' has created User # ' . $user->id);
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.create')
                ->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.users.view', $user->id)
            ->with('message.success', 'User created successfully.');
    }

    public function create()
    {
        return view('admin.users.create');
    }

    protected function store(CreateUserRequest $request)
    {
        try {
            $user = DB::transaction(function () use ($request) {
                $create_user_service = (new CreateUser())->forAccount(auth()->user()->account);

                if (auth()->user()->can('manageRoles', User::class)) {
                    $create_user_service->withRoles($request->get('roles'));
                }
                if (auth()->user()->can('manageGroups', User::class)) {
                    $create_user_service->withGroups($request->get('groups'));
                }

                return $create_user_service->create($request->all());
            });

            Log::info('User::save() --  User # ' . auth()->user()->id . ' has created User # ' . $user->id);
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.create')
                ->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.users.view', $user->id)
            ->with('message.success', 'User created successfully.');
    }

    public function edit(User $user)
    {
        return view('admin.users.edit')
            ->with('user', $user);
    }

    public function save(UpdateUserRequest $request, User $user)
    {
        try {
            Log::info('User::save() --  User # ' . auth()->user()->id . ' is modifying User # ' . $user->id);

            DB::transaction(function () use ($request, $user) {
                $update_service = (new UpdateUser($user));

                $update_service->update($request->all());
            });
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.users.view', $user)
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user->id))
            ->with('message.success', 'Saved successfully.');
    }

    public function groupsAndRoles(User $user)
    {
        return view('admin.users.tabs.groups-roles')
            ->with('user', $user);
    }

    public function changeHead(User $user)
    {
        return view('admin.users.modals.change-head-of-household')
            ->with('user', $user);
    }

    public function changeHeadSubmit(User $user)
    {
        $to_user = User::visibleTo(auth()->user())->find(request()->get('to_user'));

        if (!$to_user) {
            abort(403);
        }

        try {
            (new ChangeHeadOfHousehold())
                ->fromUser($user)
                ->toUser($to_user)
                ->change();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $to_user))->with('message.success', 'Head of household changed successfully.');
    }

    public function delete(User $user)
    {
        return view('admin.users.modals.delete')
            ->with('user', $user);
    }

    public function destroy(User $user)
    {
        try {
            Log::info('User::delete() -- User # ' . auth()->user()->id . ' is deleting User # ' . $user->id);

            (new DeleteUser($user))->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.index'))->with('message.success', 'Delete successful.');
    }

    public function showPasswordResetModal(User $user)
    {
        return view('admin.users.modals.password-reset')
            ->withUser($user);
    }

    public function sendResetLinkEmail(User $user)
    {
        request()->validate([
            'user_email_id' => [
                'required',
                'integer',
                'exists:user_emails,id',
                Rule::in($user->emails->pluck('id')->toArray()),
            ],
        ]);

        $email = Email::find(request()->get('user_email_id'));
        $email = $email->email;

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.
//        $response = $this->broker()->sendResetLink(
//            $this->credentials($request)
//        );

        Log::info('UserController::sendResetLinkEmail -- Attempting to email password reset email for email: ' . $email);

        $token = uniqid('', true);

        try {
            // We’re not doing this for now because we’re seeing a lot of people getting stuck on this error. Possible they’re requesting multiple resets, and then clicking on an older reset email.
//            DB::table('password_resets')->where('email', 'LIKE', $email)->delete();

            DB::table('password_resets')->insert([
                'email'      => $email,
                'token'      => $token,
                'created_at' => date('Y-m-d H:i:s'),
            ]);

            Mail::to($email)->queue(new ResetPassword($email, $token));
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'Password reset email sent.');
    }

    public function showWelcomeEmailModal(User $user)
    {
        return view('admin.users.modals.welcome-email')
            ->withUser($user);
    }

    public function sendWelcomeEmail(User $user)
    {
        request()->validate([
            'user_email_id' => [
                'required',
                'integer',
                'exists:user_emails,id',
                Rule::in($user->emails->pluck('id')->toArray()),
            ],
        ]);

        $email = Email::find(request()->get('user_email_id'));
        $email = $email->email;

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.
//        $response = $this->broker()->sendResetLink(
//            $this->credentials($request)
//        );

        Log::info('UserController::sendResetLinkEmail -- Attempting to email password reset email for email: ' . $email);

        $token = uniqid('', true);

        try {
            // We’re not doing this for now because we’re seeing a lot of people getting stuck on this error. Possible they’re requesting multiple resets, and then clicking on an older reset email.
//            DB::table('password_resets')->where('email', 'LIKE', $email)->delete();

            DB::table('password_resets')->insert([
                'email'      => $email,
                'token'      => $token,
                'created_at' => date('Y-m-d H:i:s'),
            ]);

            Mail::to($email)->queue(new ResetPassword($email, $token, true));
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'Welcome email sent.');
    }

    public function showDeletePaymentMethodModal(User $user, PaymentMethod $paymentMethod)
    {
        return view('admin.users.modals.delete-payment-method')
            ->with('user', $user)
            ->with('payment_method', $paymentMethod);
    }

    public function deletePaymentMethod(User $user, PaymentMethod $paymentMethod)
    {
        \Stripe\Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            \Stripe\Customer::deleteSource(
                $user->stripe_customer_id,
                $paymentMethod->stripe_payment_method_id,
                [],
                ['stripe_account' => auth()->user()->account->stripe_account_id,]
            );

            $paymentMethod->delete();
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'Payment method deleted.');
    }

    public function sendMobileNotification(User $user)
    {
        $input_message = request()->input('message');

        SendMobileNotification::dispatch($user, 'Test Message!', $input_message, ['type' => 'WA_new'])->onQueue('mobile');

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'Mobile notification sent.');
    }

    public function startVisitorTracking(User $user)
    {
        return view('admin.users.modals.start-visitor-tracking')
            ->with('user', $user);
    }

    public function startVisitorTrackingSubmit(User $user)
    {
        if (request()->get('family_or_individual') == 'family') {
            $visitor = (new \App\Visitors\Services\CreateVisitor())
                ->createdBy(auth()->user())
                ->forFamilyId($user->family_id)
                ->create();
        } elseif (request()->get('family_or_individual') == 'individual') {
            $visitor = (new \App\Visitors\Services\CreateVisitor())
                ->createdBy(auth()->user())
                ->forUserId($user->id)
                ->create();
        }

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'Success! User or Family is now available available in Visitor Tracking!');
    }

    public function clearSessionsModal(User $user)
    {
        return view('admin.users.modals.clear-sessions')
            ->withUser($user);
    }

    public function submitClearSessions(User $user)
    {
        dispatch(new ClearUserSessions($user))
            ->onQueue('low');

        return redirect(route('admin.users.view', $user))
            ->with('message.success', 'User sessions cleared. It may take a minute or two to take effect.');
    }

}
