<?php

namespace App\Users\Services;

use App\Users\PaymentMethod;
use Illuminate\Support\Arr;

class UpdatePaymentMethod
{
    protected $attributes = [];
    protected $payment_method;

    public function __construct(PaymentMethod $payment_method)
    {
        $this->payment_method = $payment_method;
    }

    public function update($attributes = []): PaymentMethod
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (Arr::exists($this->attributes, 'share_with_spouse')) {
            if ($this->attributes['share_with_spouse'] == true) {
                $this->payment_method->share_with_spouse = now();
            } else {
                $this->payment_method->share_with_spouse = null;
            }
        }

        $this->payment_method->save();

        return $this->payment_method;
    }

    public function shareWithSpouse($share_with_spouse = true)
    {
        $this->attributes['share_with_spouse'] = $share_with_spouse;

        return $this;
    }

    public function setLabel($label)
    {
        $this->attributes['label'] = $label;

        return $this;
    }
}
