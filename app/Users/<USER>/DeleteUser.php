<?php

namespace App\Users\Services;

use App\Prayers\PrayerUserTag;
use App\Users\User;
use App\Visitors\Visitor;
use Illuminate\Support\Facades\DB;

class DeleteUser
{
    protected $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function delete()
    {
        DB::transaction(function () {
            // addresses
            foreach ($this->user->addresses as $address) {
                $address->delete();
            }

            // phone numbers
            foreach ($this->user->phones as $phone) {
                $phone->delete();
            }

            // emails
            foreach ($this->user->emails as $email) {
                $email->delete();
            }

            // involvement
            $this->user->involvementCategories()->detach();
            $this->user->involvementAreas()->detach();
            $this->user->involvementSubareas()->detach();

            // Prayer Tags
            $this->deletePrayerListTags();

            // Delete any visitor records related to this person.
            Visitor::where('user_id', $this->user->id)->delete();

            // attendance
//            foreach ($this->user->attendance as $attendance) {
//                $attendance->delete();
//            }

            // group & role associations
            $this->user->groups()->detach();
            $this->user->roles()->detach();

            // shared family data (if head of family)
            # @TODO

            // Bible classes

            $this->user->delete();
        });

        return true;
    }

    private function deletePrayerListTags()
    {
        PrayerUserTag::where('user_id', $this->user->id)
            ->whereHas('user', function ($query) {
                $query->where('account_id', $this->user->account_id);
            })
            ->get();
    }
}
