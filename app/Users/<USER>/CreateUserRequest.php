<?php

namespace App\Users\Requests;

use App\Base\Http\Request;

class CreateUserRequest extends Request
{
    public function rules()
    {
        $rules = [
            'first_name' => 'required|string|max:48',
            'last_name'  => 'required|string|max:48',
            'user_name'  => 'nullable|string|unique:users|max:48',
            'email'      => 'nullable|sometimes|email|max:255|unique:user_emails,email',
            'gender'     => 'string',
            'notes'      => 'nullable|string|max:2046',
            'roles'      => 'nullable|sometimes|array',
        ];

        return $rules;
    }
}
