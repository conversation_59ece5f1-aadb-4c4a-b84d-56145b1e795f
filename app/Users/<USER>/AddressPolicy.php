<?php

namespace App\Users\Policies;

use App\Users\Address;
use App\Users\User;

class AddressPolicy
{
    public function create(User $user)
    {
        return $user->hasPermission('users.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('users.index');
    }

    public function view(User $user, Address $address)
    {
        return $user->id == $address->user_id
               || (
                   $user->hasPermission('users.index')
                   && ($user->account_id === $address->user->account_id)
               );
    }

    public function edit(User $user, Address $address)
    {
        return $user->id == $address->user_id
               || $user->family_id == $address->user->family_id
               || (
                   $user->hasPermission('users.manage')
                   && ($user->account_id === $address->user->account_id)
               );
    }

    public function update(User $user, Address $address)
    {
        return $user->id == $address->user_id
               || $user->family_id == $address->user->family_id
               || (
                   $user->hasPermission('users.manage')
                   && ($user->account_id === $address->user->account_id)
               );
    }

    public function delete(User $user, Address $address)
    {
        return $user->id == $address->user_id
               || $user->family_id == $address->user->family_id
               || (
                   $user->hasPermission('users.manage')
                   && ($user->account_id === $address->user->account_id)
               );
    }
}
