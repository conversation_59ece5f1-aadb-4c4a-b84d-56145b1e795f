<?php

namespace App\Users\Traits;

use Illuminate\Support\Arr;

trait ChildCheckinTrait
{

    public function createChildCheckinBarcodeString()
    {
        return 'LP:CCHECKIN:' . $this->account_id . ':' . $this->family_id . ':' . $this->id;
    }

    public static function parseChildCheckinBarcodeString($string)
    {
        $array = explode(':', $string);

        if (Arr::get($array, 0) !== 'LP' || Arr::get($array, 1) !== 'CCHECKIN') {
            return false;
        }

        return [
            'account_id'    => Arr::get($array, 2),
            'family_id'     => Arr::get($array, 3),
            'child_user_id' => Arr::get($array, 4),
        ];
    }

    public function getParentsListingField()
    {
        $text = '';
        foreach ($this->parents as $parent) {
            $text .= $parent->name . "\n";
            if ($parent->getBestPhone()) {
                $text .= '  ' . $parent->getBestPhone()?->formattedNumber() . "\n";
            }
        }

        return $text;
    }
}
