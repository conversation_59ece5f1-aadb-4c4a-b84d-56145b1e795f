<?php

namespace App\Users\Services;

use App\Accounts\Account;
use App\Users\Phone;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class CreateUser
{
    protected $attributes = [];
    protected $user;
    protected $status     = 'active';
    protected $roles      = null;
    protected $groups     = null;

    public function create($attributes): User
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        $password = Arr::pull($this->attributes, 'password');

        $this->attributes['status'] = $this->status;

        // Add a UUID for this new user.
        $this->attributes['public_token'] = Str::uuid();

        $this->user = User::create($this->attributes);

        $this->user->refresh();

        // Save the anniversary date from our spouse.
        if ($this->user->spouse) {
            $this->user->date_married = $this->user->spouse->date_married;
            $this->user->save();
        }

        $this->user->generateUlid();

        $this->checkIfHeadOfFamily();

        $this->addEmail();
        $this->addMobileNumber();

        // Set our password through our magic method.
        if (!empty($password)) {
            $this->user->password = $password;
        }

        // Roles
        if ($this->roles !== null) {
            // Roles
            $this->user->roles()->sync(is_array($this->roles) ? $this->roles : []);
        }
        // Groups
        if ($this->groups !== null) {
            // Groups
            $this->user->groups()->sync(is_array($this->groups) ? $this->groups : []);
        }

        return $this->user;
    }

    public function forAccount(Account $account)
    {
        $this->attributes['account_id'] = $account->id;

        return $this;
    }

    protected function addMobileNumber()
    {
        if (Arr::get($this->attributes, 'mobile_number')) {
            $this->user->phones()->create([
                'number'     => Phone::format(Arr::get($this->attributes, 'mobile_number')),
                'type'       => 'mobile',
                'is_primary' => true,
            ]);
        }
    }

    protected function addEmail()
    {
        if (Arr::get($this->attributes, 'email')) {
            $this->user->emails()->create([
                'email'                 => Arr::get($this->attributes, 'email'),
                'type'                  => 'personal',
                'receives_group_emails' => true,
                'is_primary'            => true,
            ]);
        }
    }

    protected function checkIfHeadOfFamily()
    {
        // This checks and overwrites any family selection if we're saying they're head of family.
        if (Arr::get($this->attributes, 'family_role') == 'head') {
            $this->user->family_id = $this->user->id;
            $this->user->save();
        }
    }

    public function withRoles($roles = [])
    {
        $this->roles = $roles === null ? [] : $roles;

        return $this;
    }

    public function withGroups($groups = [])
    {
        $this->groups = $groups === null ? [] : $groups;

        return $this;
    }
}
