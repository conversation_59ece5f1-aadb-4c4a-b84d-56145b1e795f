<?php

namespace App\Users\Services;

use App\Users\Payment;
use Illuminate\Support\Facades\Log;
use Stripe\Charge as ChargeAlias;
use Stripe\Stripe;

class SyncPaymentWithProvider
{
    private $payment             = null;
    private $stripe_response     = null;
    private $balance_transaction = null;

    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    public function sync(): Payment|null
    {
        Stripe::setApiKey(config('services.stripe.connect.secret'));

        $this->stripe_response = ChargeAlias::retrieve(
            $this->payment->stripe_charge_id,
            [
                'stripe_account' => $this->payment->account->stripe_account_id,
            ]
        );

        if (!$this->stripe_response) {
            Log::error('IncomingStripeController::handleChargeSucceededEvent -- Could not find a charge in Stripe with provided ID.', [
                'stripe_response' => $this->stripe_response,
                'user_payment_id' => $this->payment->id,
                'user_id'         => $this->payment->user_id,
                'account_id'      => $this->payment->account_id,
            ]);

            throw new \Exception('Could not find a Stripe charge with the provided ID.');
        }

        if ($this->stripe_response->balance_transaction) {
            $this->balance_transaction = \Stripe\BalanceTransaction::retrieve(
                $this->stripe_response->balance_transaction,
                [
                    'stripe_account' => $this->payment->account->stripe_account_id,
                ]
            );

            if (!$this->stripe_response) {
                Log::error('IncomingStripeController::handleChargeSucceededEvent -- Could not find a charge in Stripe with provided ID.', [
                    'stripe_response' => $this->stripe_response,
                    'user_payment_id' => $this->payment->id,
                    'user_id'         => $this->payment->user_id,
                    'account_id'      => $this->payment->account_id,
                ]);

                throw new \Exception('Could not find a Stripe charge with the provided ID.');
            }
        }

        try {
            // Possible Statuses: pending, succeeded, failed
            // https://docs.stripe.com/api/charges/object?lang=php#charge_object-status
            if ($this->stripe_response->status == 'succeeded') {
                $this->markPaymentAsSuccessful();
            } elseif ($this->stripe_response->status == 'pending') {
                $this->markPaymentAsPending();
            } elseif ($this->stripe_response->status == 'failed') {
                $this->markPaymentAsFailed();
            }
        } catch (\Exception $e) {
            Log::error('IncomingStripeController::handleChargeSucceededEvent -- Could not update the payment with new Stripe data.', [
                'stripe_response' => $this->stripe_response,
                'user_payment_id' => $this->payment->id,
                'user_id'         => $this->payment->user_id,
                'account_id'      => $this->payment->account_id,
                'e'               => $e,
                'message'         => $e->getMessage(),
            ]);

            throw new \Exception('Could not update the payment in the database with new Stripe data.');
        }

        return $this->payment;
    }

    private function markPaymentAsSuccessful()
    {
        $this->payment->update([
            'status'               => $this->stripe_response?->status,
            'stripe_status'        => $this->stripe_response?->status,
            'is_failed'            => false,
            'is_success'           => true,
            'is_pending'           => false,
            'amount'               => $this->stripe_response->amount_captured ?: 0,
            'amount_deposited'     => $this->balance_transaction?->net,
            'amount_fee'           => $this->balance_transaction?->fee,
            'stripe_charge_object' => json_encode($this->stripe_response),
        ]);
    }

    private function markPaymentAsFailed()
    {
        $this->payment->update([
            'status'               => $this->stripe_response?->status,
            'stripe_status'        => $this->stripe_response?->status,
            'is_failed'            => true,
            'is_success'           => false,
            'is_pending'           => false,
            'amount'               => $this->stripe_response->amount_captured ?: 0,
            'amount_deposited'     => $this->balance_transaction?->net,
            'amount_fee'           => $this->balance_transaction?->fee,
            'stripe_charge_object' => json_encode($this->stripe_response),
        ]);
    }

    private function markPaymentAsPending()
    {
        $this->payment->update([
            'status'               => $this->stripe_response?->status,
            'stripe_status'        => $this->stripe_response?->status,
            'is_failed'            => false,
            'is_success'           => false,
            'is_pending'           => true,
            'amount'               => $this->stripe_response->amount_captured ?: 0,
            'amount_deposited'     => $this->balance_transaction?->net,
            'amount_fee'           => $this->balance_transaction?->fee,
            'stripe_charge_object' => json_encode($this->stripe_response),
        ]);
    }
}