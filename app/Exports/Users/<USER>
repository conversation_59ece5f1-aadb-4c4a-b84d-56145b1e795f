<?php

namespace App\Exports\Users;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class UserIndexExport implements FromView
{
    public $users;
    public $users_query;

    public function __construct($users_query)
    {
        $this->users_query = $users_query;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users_query->get();
    }

    public function view(): View
    {
        return view('admin.users.exports.users-index-table', [
            'is_export' => true,
            'users'     => $this->collection(),
        ]);
    }
}
