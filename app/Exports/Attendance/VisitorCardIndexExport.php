<?php

namespace App\Exports\Attendance;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class VisitorCardIndexExport implements FromView
{
    public $users;
    public $collection_query;

    public function __construct($collection_query)
    {
        $this->collection_query = $collection_query;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->collection_query->get();
    }

    public function view(): View
    {
        return view('admin.exports.attendance.visitor-card-index-table', [
            'is_export' => true,
            'rows'      => $this->collection(),
        ]);
    }
}
