<?php

namespace App\Exports\Attendance;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class GeneralAttendanceExport implements FromCollection, WithHeadings
{
    protected $attendance;
    protected $types;

    public function __construct($attendance, $types)
    {
        $this->attendance = $attendance;
        $this->types      = $types;
    }

    public function collection()
    {
        $temp = $this->attendance->map(function ($item) {
            $line = [];

            $line[] = $item->attendance_at->format('M d, Y');

            foreach ($this->types as $type) {
                $line[] = $item->hydrateForTypeGivenDate($type)?->count;
            }

            return $line;
        });

        return $temp;
    }

    public function headings(): array
    {
        $headers = null;

        foreach ($this->types as $type) {
            $headers[] = $type->getShortName();
        }

        return $headers;
    }
} 