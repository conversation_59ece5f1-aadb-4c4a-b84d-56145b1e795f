<?php

namespace App\Exports\Finance;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ContributionReportExport implements FromView
{
    public $contributions;

    public function __construct($contributions)
    {
        $this->contributions = $contributions;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->contributions->get();
    }

    public function view(): View
    {
        return view('admin.exports.finance.contributions-report', [
            'is_export'     => true,
            'contributions' => $this->collection(),
        ]);
    }
}
