<?php

namespace App\Exports\Finance;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class PayoutReportExport implements FromView
{
    public $payout;
    public $payments;

    public function __construct($payout)
    {
        $this->payout   = $payout;
        $this->payments = $payout->userPayments;
    }

    public function view(): View
    {
        return view('admin.exports.finance.payout-report', [
            'is_export' => true,
            'payout'    => $this->payout,
            'payments'  => $this->payments,
        ]);
    }
}
