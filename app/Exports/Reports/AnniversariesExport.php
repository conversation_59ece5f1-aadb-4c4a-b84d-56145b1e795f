<?php

namespace App\Exports\Reports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class AnniversariesExport implements FromView
{
    public $users;
    public $columns = [];

    public function __construct($users, $columns = [])
    {
        $this->users   = $users;
        $this->columns = $columns;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users->get();
    }

    public function view(): View
    {
        return view('admin.reports.exports.anniversaries-table', [
            'is_export' => true,
            'users'     => $this->users->get(),
            'columns'   => $this->columns,
        ]);
    }
}
