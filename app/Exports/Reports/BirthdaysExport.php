<?php

namespace App\Exports\Reports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class BirthdaysExport implements FromView
{
    public $users;
    public $users_query;
    public $columns = [];

    public function __construct($users_query, $columns = [])
    {
        $this->users_query = $users_query;
        $this->columns     = $columns;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users_query->get();
    }

    public function view(): View
    {
        return view('admin.reports.exports.birthdays-table', [
            'is_export' => true,
            'users'     => $this->users_query->get(),
            'columns'   => $this->columns,
        ]);
    }
}
