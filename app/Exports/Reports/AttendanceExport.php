<?php

namespace App\Exports\Reports;

use App\Attendance\AttendanceType;
use Flux\DateRange;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithProperties;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AttendanceExport implements FromView, WithStyles, WithEvents, WithProperties
{
    public                $users;
    public                $users_query;
    public array          $attendance_types  = []; // Array of AttendanceType IDs
    public                $base_col_count    = 6;
    public                $dynamic_col_count = 0;
    public DateRange|null $date_range        = null;

    public function __construct($users_query, $attendance_types = [], DateRange $date_range = null)
    {
        $this->users_query      = $users_query;
        $this->attendance_types = $attendance_types;
        $this->date_range       = $date_range;

        $this->dynamic_col_count = count($this->getAttendanceDates());
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users_query->get();
    }

    public function properties(): array
    {
        return [
            'creator'     => 'Lightpost',
            'title'       => 'Attendance Export',
            'description' => 'List of attendance for users',
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $index = 0;
                $index++; // Start after the header row
                foreach ($this->users_query->get() as $user) {
                    $index++;
                    if ($user->family_role == 'head') {
                        $event->sheet->getStyle('A' . $index . ':' . Coordinate::stringFromColumnIndex($this->base_col_count + $this->dynamic_col_count) . $index)->getFill()
                            ->setFillType(Fill::FILL_SOLID)
                            ->getStartColor()->setRGB('E9E9E9');
                    }
                }

                $event->sheet->getStyle('A1:' . Coordinate::stringFromColumnIndex($this->base_col_count + $this->dynamic_col_count) . $this->users_query->count() + 1)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color'       => ['rgb' => '777777'],
                        ],
                    ],
                ]);
            },
        ];
    }

    public function styles(Worksheet $sheet)
    {
//        $sheet->getStyle('A1:ZZ2000')->getFont()->setSize(12);
//        $sheet->getStyle('A1:ZZ2000')->getFont()->setName('Arial');

        // Make our dates vertical
        $sheet->getStyle('G1:CC1')->getAlignment()->applyFromArray([
            'textRotation' => 90,
            'horizontal'   => Alignment::HORIZONTAL_CENTER,
            'vertical'     => Alignment::VERTICAL_CENTER,
        ]);

        // Center the "X"s in the cells
        $sheet->getStyle('G2:Z2000')
            ->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Autosize our data columns
        $sheet->getColumnDimension('A')->setAutoSize(true);
        $sheet->getColumnDimension('B')->setAutoSize(true);
        $sheet->getColumnDimension('C')->setAutoSize(true);
        $sheet->getColumnDimension('D')->setAutoSize(true);
        $sheet->getColumnDimension('E')->setAutoSize(true);
        $sheet->getColumnDimension('F')->setAutoSize(true);

        // Set our date columns to a width of 3
        for ($i = 1; $i <= ($this->dynamic_col_count + $this->base_col_count); $i++) {
            $sheet->getColumnDimension(Coordinate::stringFromColumnIndex($i))->setWidth(3);
        }
    }

    public function view(): View
    {
        return view('admin.reports.exports.attendance-table', [
            'is_export'  => true,
            'users'      => $this->users_query->get(),
            'dates'      => $this->getAttendanceDates(),
            'date_range' => $this->date_range,
        ]);
    }

    private function getAttendanceDates()
    {
        $days_attendance_happens = [];
        $attendance_types        = AttendanceType::whereIn('id', $this->attendance_types)->get();

        foreach ($attendance_types as $type) {
            $days_attendance_happens = array_unique(array_merge($days_attendance_happens, $type->days_of_week ?: []));
        }

        // All this gets a list of dates that we need to show attendance for based on the AttendanceTypes selected.
        //  It was painful! There's a better way... but this is it for now.
        $dates = [];
        if ($this->date_range) {
            $day      = $this->date_range->end();
            $end_date = $this->date_range->start();
        } else {
            $day      = now();
            $end_date = now()->subWeeks(24);
        }

        // We go backwards here, so end date is the "beginning" (oldest date) and we go forwards to the "day" (newest date).
        while ($day >= $end_date) {
            // If the day of the week we're on now is in the list of days attendance types happen, add it to our list of dates.
            if (in_array(AttendanceType::convertDayOfWeekNumberFromNumericToISO($day->dayOfWeek), $days_attendance_happens)) {
                $dates[] = $day->copy();
            }

            $day->subDay();
        }

        return $dates;
    }
}
