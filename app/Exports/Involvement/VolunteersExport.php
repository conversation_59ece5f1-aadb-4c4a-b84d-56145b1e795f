<?php

namespace App\Exports\Involvement;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class VolunteersExport implements FromView
{
    public $users;
    public $category        = null;
    public $area            = null;
    public $subarea         = null;
    public $users_query;
    public $included_fields = [
        'first_name'      => 'First Name',
        'last_name'       => 'Last Name',
        'email'           => 'Email',
        //        'home_phone' => 'Home Phone',
        'mobile_phone'    => 'Mobile Phone',
        'home_address'    => 'Home Address',
        'mailing_address' => 'Mailing Address',
        //        'date_baptism' => 'Date of Baptism',
        //        'family_role' => 'Family Role',
        //        'marital_status',
        //        'birthdate',
        //        'date_married',
    ];

    public function __construct($users_query, $category = null, $area = null, $subarea = null, $included_fields = null)
    {
        $this->users_query = $users_query;
        $this->category    = $category;
        $this->area        = $area;
        $this->subarea     = $subarea;

        if ($included_fields !== null) {
            $this->included_fields = $included_fields;
        }
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users_query->get();
    }

    public function view(): View
    {
        return view('exports.involvement.volunteers', [
            'is_export'       => true,
            'included_fields' => $this->included_fields,
            'users'           => $this->collection(),
            'category'        => $this->category,
            'area'            => $this->area,
            'subarea'         => $this->subarea,
        ]);
    }
}
