<?php

namespace App\Exports\Programs\Users;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ViewUsersExport implements FromView
{
    public $users;
    public $users_query;

    public function __construct($users_query)
    {
        $this->users_query = $users_query;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->users_query
            ->with([
                'formResponses.formField',
                'groups',
                'registration',
                'contacts',
            ])
            ->get();
    }

    public function view(): View
    {
        $allGroups = $this->collection()->pluck('groups')->flatten()->unique('id')->sortBy('sort_id');

        // Get all unique form fields across all users
        $allFormFields = $this->collection()
            ->pluck('formResponses')
            ->flatten()
            ->pluck('formField')
            ->unique('id')
            ->sortBy('sort_id');

        return view('admin.exports.programs.users.view-users-table', [
            'is_export'   => true,
            'users'       => $this->collection(),
            'groups'      => $allGroups,
            'form_fields' => $allFormFields,
        ]);
    }
}
