<?php

namespace App\BibleClasses;

use App\Base\Models\Model;
use App\Users\User;

class BibleClassRegistration extends Model
{
    protected $table = 'bible_class_registrations';

    protected $casts = [
        'created_at' => 'datetime',
    ];

    protected $fillable = [
        'bible_class_id',
        'user_id',
        'first_name',
        'last_name',
    ];

    public function scopeVisibleTo($query, User $user)
    {

    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function bibleClass()
    {
        return $this->belongsTo(BibleClass::class);
    }
}
