<?php

namespace App\BibleClasses;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use App\Attendance\AttendanceType;
use App\Base\Models\Model;
use App\BibleClasses\Scopes\BibleClassVisibleToScope;
use App\Users\User;

class BibleClass extends Model
{
    protected $table = 'bible_classes';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'bible_class_group_id',
        'user_attendance_type_id',
        'title',
        'description',
        'short_description',
        'location_name',
        'day_of_week',
        'is_new',
        'enable_signup',
        'is_featured',
        'is_hidden',
    ];

    static public $days_of_week = [
        7 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new BibleClassVisibleToScope())->getQuery($query, $user);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function accountLocation()
    {
        return $this->belongsTo(AccountLocation::class);
    }

    public function teachers()
    {
        return $this->belongsToMany(User::class, 'bible_class_teachers', 'bible_class_id', 'user_id');
    }

    public function attendanceType()
    {
        return $this->belongsTo(AttendanceType::class, 'user_attendance_type_id');
    }

    public function group()
    {
        return $this->belongsTo(BibleClassGroup::class, 'bible_class_group_id');
    }

    public function registrations()
    {
        return $this->belongsToMany(User::class, 'bible_class_registrations', 'bible_class_id', 'user_id');
    }

    public function dayOfWeek()
    {
        return self::$days_of_week[$this->day_of_week];
    }

//    public function registered_users()
//    {
//        return BibleClassRegistration::join('users', 'bible_class_registrations.user_id', '=', 'users.id')
//            ->where('bible_class_registrations.bible_class_id', '=', $this->id)
//            ->orderBy('users.last_name', 'ASC')
//            ->orderBy('users.family_id', 'ASC')
//            ->orderBy('users.first_name', 'ASC')
//            ->get();
//    }
//
//    public static function get_user_last_attendance($user_id)
//    {
//        return BibleClass::where('user_id', '=', $user_id)
//            ->orderBy('date_attendance', 'DESC')
//            ->first();
//    }
//
//    public function number_of_registrants()
//    {
//        return BibleClassRegistration::select(array('bible_class_id'))
//            ->where('bible_class_id', '=', $this->id)
//            ->count();
//    }
}
