<?php

namespace App\BibleClasses;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use App\Base\Models\Model;
use App\BibleClasses\Scopes\BibleClassTeacherVisibleToScope;
use App\Users\User;

class BibleClassTeacher extends Model
{
    protected $table = 'bible_class_teachers';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'bible_class_id',
        'user_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new BibleClassTeacherVisibleToScope())->getQuery($query, $user);
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id', 'id');
    }

    public function accountLocation()
    {
        return $this->belongsTo(AccountLocation::class, 'account_location_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function bibleClass()
    {
        return $this->belongsTo(BibleClass::class, 'bible_class_id', 'id');
    }
}
