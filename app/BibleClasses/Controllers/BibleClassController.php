<?php

namespace App\BibleClasses\Controllers;

use App\Base\Http\Controllers\Controller;
use App\BibleClasses\BibleClass;
use App\BibleClasses\BibleClassGroup;
use App\BibleClasses\Services\CreateBibleClass;
use App\BibleClasses\Services\CreateBibleClassRegistration;
use App\BibleClasses\Services\UpdateBibleClass;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;

class BibleClassController extends Controller
{

    public function index()
    {
        Paginator::useTailwind();
        return view('admin.bible-classes.index')
            ->withBibleClassGroups(
                BibleClassGroup::visibleTo(Auth::user())
                    ->withCount('classes')
                    ->orderBy('start_date', 'desc')
                    ->paginate(6)
            );
    }

    public function create(BibleClassGroup $bible_class_group)
    {
        return view('admin.bible-classes.create')
            ->with('group', $bible_class_group);
    }

    public function store(BibleClassGroup $bible_class_group)
    {
        $this->validate(request(), [
            'title'                   => 'required|string',
            'description'             => 'nullable|string',
            'short_description'       => 'nullable|string',
            'location_name'           => 'nullable|string',
            'teacher_ids'             => 'nullable|array',
            'user_attendance_type_id' => 'nullable|integer',
            'day_of_week'             => 'nullable|integer',
            'enable_signup'           => 'required|integer',
            'is_hidden'               => 'required|integer',
        ]);

        try {
            (new CreateBibleClass())->forUser(Auth::user())->create(request()->all());
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.bible-classes.groups.view', $bible_class_group))
            ->with('message.success', 'Bible Class created successfully.');
    }

    public function copy(BibleClassGroup $bible_class_group)
    {
        $previous_classes = [];

        if (request()->get('selected_bible_class_group_id')) {
            $previous_classes = BibleClass::visibleTo(auth()->user())
                ->where('bible_class_group_id', request()->get('selected_bible_class_group_id'))
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return view('admin.bible-classes.copy')
            ->with(
                'groups',
                BibleClassGroup::visibleTo(Auth::user())
                    ->withCount('classes')
                    ->whereNot('id', $bible_class_group->id)
                    ->orderBy('start_date', 'desc')
                    ->limit(20)
                    ->get()
            )
            ->with('previous_classes', $previous_classes)
            ->with('group', $bible_class_group);
    }

    public function copySubmit(BibleClassGroup $bible_class_group)
    {
        $this->validate(request(), [
            'selected_class_id'           => 'required|integer',
            'copy_registrations_class_id' => 'required|integer',
            'copy_registrations'          => 'sometimes|integer',
        ]);

        $previous_class                   = BibleClass::visibleTo(auth()->user())
            ->where('id', request()->get('selected_class_id'))
            ->first();
        $copy_registrations               = (bool)request()->get('copy_registrations');
        $previous_class_for_registrations = BibleClass::visibleTo(auth()->user())
            ->find(request()->get('copy_registrations_class_id'));

        try {
            // Create our new class using data from the previous class selected.
            $new_class = (new CreateBibleClass())
                ->forUser(Auth::user())
                ->create([
                    'bible_class_group_id'    => $bible_class_group->id,
                    'title'                   => $previous_class->title,
                    'description'             => $previous_class->description,
                    'short_description'       => $previous_class->short_description,
                    'location_name'           => $previous_class->location_name,
                    'teacher_ids'             => $previous_class->teacher_ids,
                    'user_attendance_type_id' => $previous_class->user_attendance_type_id,
                    'day_of_week'             => $previous_class->day_of_week,
                    'enable_signup'           => $previous_class->enable_signup,
                    'is_hidden'               => $previous_class->is_hidden,
                ]);

            // If we're copying registrations from another class too.
            if ($copy_registrations) {
                // Use our new class with the registration service.
                $bibleClassRegistrationService = new CreateBibleClassRegistration($new_class);

                // Go through each user in the previous class and add them to the new class.
                foreach ($previous_class_for_registrations->registrations->pluck('id') as $user_id) {
                    $bibleClassRegistrationService->addUser($user_id);
                }

                // Save all the changes using the service.
                $bibleClassRegistrationService->save();
            }
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.bible-classes.groups.view', $bible_class_group))
            ->with('message.success', 'Bible Class copied successfully.');
    }

    public function edit(BibleClassGroup $bible_class_group, BibleClass $bible_class)
    {
        return view('admin.bible-classes.edit')
            ->with('group', $bible_class_group)
            ->withClass($bible_class);
    }

    public function save(BibleClassGroup $bible_class_group, BibleClass $bible_class)
    {
        $this->validate(request(), [
            'title'                   => 'required|string',
            'description'             => 'nullable|string',
            'short_description'       => 'nullable|string',
            'location_name'           => 'nullable|string',
            'user_attendance_type_id' => 'nullable|integer',
            'teacher_ids'             => 'nullable|array',
            'day_of_week'             => 'nullable|integer',
            'enable_signup'           => 'required|integer',
            'is_hidden'               => 'required|integer',
        ]);

        try {
            (new UpdateBibleClass($bible_class))->forUser(Auth::user())->update(request()->all());
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.bible-classes.groups.view', $bible_class_group))
            ->with('message.success', 'Bible Class saved.');
    }
}
