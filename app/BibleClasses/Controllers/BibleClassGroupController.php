<?php

namespace App\BibleClasses\Controllers;

use App\Attendance\AttendanceType;
use App\Base\Http\Controllers\Controller;
use App\BibleClasses\BibleClassGroup;
use Illuminate\Support\Facades\Auth;
use TCPDF;

class BibleClassGroupController extends Controller
{
    public function create()
    {
        return view('admin.bible-classes.groups.create');
    }

    public function view(BibleClassGroup $bible_class_group)
    {
        return view('admin.bible-classes.groups.view')
            ->with('group', $bible_class_group)
//            ->withClassesByAttendanceType($bible_class_group->classes()->selectRaw('DISTINCT ON (user_attendance_type_id) *')->get());
            // MySQL
            ->with('classesByAttendanceType', $bible_class_group->classes()->selectRaw('DISTINCT user_attendance_type_id')->get());
    }

    public function store()
    {
        $this->validate(request(), [
            'name'             => 'required|string|max:64',
            'start_date'       => 'required',
            'end_date'         => 'nullable',
            'is_signup_active' => 'nullable|integer',
        ]);

        try {
            BibleClassGroup::create([
                'account_id'          => Auth::user()->account->id,
                'account_location_id' => null,
                'name'                => request()->input('name'),
                'start_date'          => request()->input('start_date'),
                'end_date'            => request()->input('end_date'),
                'is_signup_active'    => request()->input('is_signup_active'),
            ]);

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.bible-classes.index'))
            ->with('message.success', 'Bible Class Group created successfully.');
    }

    public function edit(BibleClassGroup $bible_class_group)
    {
        return view('admin.bible-classes.groups.edit')
            ->withGroup($bible_class_group);
    }

    public function save(BibleClassGroup $bible_class_group)
    {
        $this->validate(request(), [
            'name'             => 'required|string|max:64',
            'start_date'       => 'required',
            'end_date'         => 'nullable',
            'is_signup_active' => 'nullable|integer',
        ]);

        try {

            $bible_class_group->name             = request()->input('name');
            $bible_class_group->start_date       = request()->input('start_date');
            $bible_class_group->end_date         = request()->input('end_date');
            $bible_class_group->is_signup_active = request()->input('is_signup_active');

            $bible_class_group->save();

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Bible Class Group saved.');
    }

    public function delete(BibleClassGroup $bible_class_group)
    {
        try {

            // Soft delete
            $bible_class_group->delete();

        } catch (\Exception $e) {
            return redirect(route('admin.bible-classes.index'))
                ->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.bible-classes.index'))
            ->with('message.success', 'Bible Class Group deleted.');
    }

    public function membershipByUserPdf(BibleClassGroup $bible_class_group, AttendanceType $type)
    {
        $view = view('admin.bible-classes.pdfs.registrations-by-user')
            ->with('group', $bible_class_group)
            ->with('type', $type)
            ->with('users', $bible_class_group->classes()->with('registrations')->get()->pluck('registrations')->flatten()->sortBy('last_name')->unique('id'));

        $pdf = new TCPDF;

        @$pdf->SetSubject("Membership Registration - ' . $bible_class_group->name . ' - ' . $type->name . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Membership Registration by Member - ' . $bible_class_group->name . ' - ' . $type->name . '.pdf", 'I');
        exit;
    }

    public function membershipByClassPdf(BibleClassGroup $bible_class_group, AttendanceType $type)
    {
        $view = view('admin.bible-classes.pdfs.registrations-by-class')
            ->with('group', $bible_class_group)
            ->with('type', $type)
            ->with('classes', $bible_class_group->classes()->where('user_attendance_type_id', $type->id)->get());

        $pdf = new TCPDF;

        @$pdf->SetSubject("Membership Registration - ' . $bible_class_group->name . ' - ' . $type->name . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Membership Registration by Class - ' . $bible_class_group->name . ' - ' . $type->name . '.pdf", 'I');
        exit;
    }
}
