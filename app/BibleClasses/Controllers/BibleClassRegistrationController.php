<?php

namespace App\BibleClasses\Controllers;

use App\Base\Http\Controllers\Controller;
use App\BibleClasses\BibleClass;
use App\BibleClasses\BibleClassGroup;
use App\BibleClasses\Services\CreateBibleClassRegistration;
use App\Users\User;
use Illuminate\Support\Facades\Auth;
use TCPDF;

class BibleClassRegistrationController extends Controller
{
    public function view(BibleClassGroup $bible_class_group, BibleClass $bible_class)
    {
        return view('admin.bible-classes.registration.edit')
            ->with('group', $bible_class_group)
            ->with('class', $bible_class)
            ->with(
                'users',
                User::visibleTo(Auth::user())
                    ->isNotDeceased()
                    ->membersAndVisitorsOnly()
                    ->get()
                    ->map(function ($user) {
                        return [
                            'value'     => $user->last_name . ', ' . $user->display_first_name,
                            'id'        => $user->id,
                            'family_id' => $user->family_id,
                        ];
                    })
            );
    }

    public function saveNewUsers(BibleClassGroup $bible_class_group, BibleClass $bible_class)
    {
        $this->validate(request(), [
            'selected_user_ids' => 'sometimes|array',
        ]);

        $bibleClassRegistrationService = new CreateBibleClassRegistration($bible_class);

        if (request()->get('selected_user_ids')) {
            foreach (request()->get('selected_user_ids') as $user_id) {
                // @TODO: Check that the user_ids passed are allowed to be used.

                // Convert our string value to bool.
                // Sync records.
                $bibleClassRegistrationService->addUser($user_id);
            }
            try {
                $bibleClassRegistrationService->save();
            } catch (\Exception $e) {
                return back()->with('message.failure', $e->getMessage());
            }
        }

        return redirect(route('admin.bible-classes.groups.view', $bible_class_group))
            ->with('message.success', 'Registration saved successfully.');
    }

    public function removeUser(BibleClassGroup $bible_class_group, BibleClass $bible_class, User $user)
    {
        $this->validate(request(), [
            'selected_user_ids' => 'sometimes|array',
        ]);

        $bibleClassRegistrationService = new CreateBibleClassRegistration($bible_class);

        $bibleClassRegistrationService->removeUser($user->id);
        try {
            $bibleClassRegistrationService->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()
            ->with('message.success', 'Registration saved successfully.');
    }

    public function pdfRegistrationSheet(BibleClassGroup $bible_class_group, BibleClass $bible_class)
    {
        $view = view('admin.bible-classes.pdfs.registration-sheet')
            ->with('group', $bible_class_group)
            ->with('class', $bible_class);

        $pdf = new TCPDF();

        @$pdf->SetSubject('Attendance - ' . $bible_class->title . '.pdf');
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(12);
        @$pdf->SetRightMargin(5);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('L', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output('Attendance - ' . $bible_class->title . '.pdf', 'I');
        exit;
    }
}
