<?php

namespace App\BibleClasses\Policies;

use App\BibleClasses\BibleClassTeacher;
use App\Users\User;

class BibleClassTeacherPolicy
{
    public function before($user, $ability)
    {
        if ($user->isSuper()) {
            return true;
        }

        if (!$user->account->getSetting('feature.bible_classes')) {
            return false;
        }
    }

    public function create(User $user)
    {
        return $user->hasPermission('bible-classes.manage');
    }

    public function index(User $user)
    {
        return $user->hasPermission('bible-classes.index');
    }

    public function view(User $user, BibleClassTeacher $teacher)
    {
        return $user->account_id == $teacher->account_id
            || $user->hasPermission('bible-classes.index');
    }

    public function update(User $user, BibleClassTeacher $teacher)
    {
        return $user->account_id == $teacher->account_id
            || $user->hasPermission('bible-classes.manage');
    }
}
