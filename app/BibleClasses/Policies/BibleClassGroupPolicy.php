<?php

namespace App\BibleClasses\Policies;

use App\BibleClasses\BibleClassGroup;
use App\Users\User;

class BibleClassGroupPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.bible_classes')) {
            return false;
        }
    }

    public function create(User $user)
    {
        return $user->hasPermission('bible-classes.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('bible-classes.manage');
    }

    public function index(User $user)
    {
        return $user->hasPermission('bible-classes.index');
    }

    public function view(User $user, BibleClassGroup $bible_class_group)
    {
        return $user->account_id == $bible_class_group->account_id
            || $user->hasPermission('bible-classes.index');
    }

    public function edit(User $user, BibleClassGroup $bible_class_group)
    {
        return $user->account_id == $bible_class_group->account_id
            || $user->hasPermission('bible-classes.manage');
    }

    public function update(User $user, BibleClassGroup $bible_class_group)
    {
        return $user->account_id == $bible_class_group->account_id
            || $user->hasPermission('bible-classes.manage');
    }

    public function delete(User $user, BibleClassGroup $bible_class_group)
    {
        return $user->account_id == $bible_class_group->account_id
            || $user->hasPermission('bible-classes.manage');
    }
}
