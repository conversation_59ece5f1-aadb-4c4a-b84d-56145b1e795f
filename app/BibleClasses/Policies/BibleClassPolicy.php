<?php

namespace App\BibleClasses\Policies;

use App\BibleClasses\BibleClass;
use App\Users\User;

class BibleClassPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.bible_classes')) {
            return false;
        }
    }

    public function create(User $user)
    {
        return $user->hasPermission('bible-classes.manage');
    }

    public function index(User $user)
    {
        return $user->hasPermission('bible-classes.index');
    }

    public function view(User $user, BibleClass $bible_class)
    {
        return $user->account_id == $bible_class->account_id
            || $user->hasPermission('bible-classes.index');
    }

    public function update(User $user, BibleClass $bible_class)
    {
        return $user->account_id == $bible_class->account_id
            || $user->hasPermission('bible-classes.manage');
    }
}
