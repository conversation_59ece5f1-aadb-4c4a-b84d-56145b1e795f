<?php

namespace App\BibleClasses;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use App\Attendance\AttendanceType;
use App\Base\Models\Model;
use App\BibleClasses\Scopes\BibleClassGroupVisibleToScope;
use App\Users\User;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class BibleClassGroup extends Model
{
    use SoftDeletes;

    protected $table = 'bible_class_groups';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'start_date' => 'date',
        'end_date'   => 'date',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'name',
        'description',
        'weeks',
        'start_date',
        'end_date',
        'is_signup_active',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new BibleClassGroupVisibleToScope())->getQuery($query, $user);
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id', 'id');
    }

    public function accountLocation()
    {
        return $this->belongsTo(AccountLocation::class, 'account_location_id', 'id');
    }

    public function classes()
    {
        return $this->hasMany(BibleClass::class, 'bible_class_group_id');
    }

    // Not currently working
    public function registrations()
    {
        return $this->hasManyThrough(User::class, BibleClass::class, 'bible_class_group_id', 'id');
    }

    public function scopeSignupIsActive($query)
    {
        return $query->where('is_signup_active', true);
    }

    public function scopeIsCurrentlyActive($query)
    {
        return $query->where(function ($query2) {
            $query2->where('start_date', '<=', Carbon::now()->startOfDay())
                ->where('end_date', '>=', Carbon::now()->subDay()->startOfDay());
        });
    }

    public function classesByType($type)
    {
        if (is_object($type) && get_class($type) === AttendanceType::class) {
            return $this->classes()->where('user_attendance_type_id', $type->id)->get();
        } else {
            return $this->classes()->where('user_attendance_type_id', $type)->get();
        }
    }

    public function registrationCount()
    {
        return $this->classes()->withCount('registrations')->get()->sum('registrations_count');
    }

    public function classesByDayOfWeek($day_of_week)
    {
        // $day_of_week == ISO 8901 format (1-7, Mon-Sun)
        return $this->hasMany(BibleClass::class)->where('day_of_week', $day_of_week);
    }
}
