<?php

namespace App\BibleClasses\Services;

use App\BibleClasses\BibleClass;
use App\Users\User;
use Illuminate\Support\Arr;

class UpdateBibleClass
{
    protected $attributes = [];
    protected $bible_class;
    protected $user;

    public function forUser(User $user): UpdateBibleClass
    {
        $this->user = $user;

        return $this;
    }

    public function __construct(BibleClass $bible_class)
    {
        $this->bible_class = $bible_class;
    }

    public function update($attributes): BibleClass
    {
        $this->attributes = $attributes;

        // Get our teacher user IDs, add the account ID to them for syncing
        $teacher_ids = collect(Arr::pull($this->attributes, 'teacher_ids'))->mapWithKeys(function ($item) {
            return [$item => ['account_id' => $this->user->account->id]];
        });

        $this->bible_class->update($this->attributes);

        // Teachers
        $this->bible_class->teachers()->sync($teacher_ids);

        return $this->bible_class;
    }
}
