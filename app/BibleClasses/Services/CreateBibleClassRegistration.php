<?php

namespace App\BibleClasses\Services;

use App\BibleClasses\BibleClass;
use App\Users\User;

class CreateBibleClassRegistration
{
    protected $bible_class                        = null;
    protected $unregister_in_same_attendance_type = true;
    protected $add_users                          = [];
    protected $remove_users                       = [];

    public function __construct(BibleClass $bible_class)
    {
        $this->bible_class = $bible_class;
    }

    public function addUser($user_id, bool $include_family = false)
    {
        $this->add_users[] = $user_id;

        if ($include_family && User::find($user_id)) {
            foreach (User::find($user_id)->getFamilyMembers() as $familyMember) {
                $this->add_users[] = $familyMember->id;
            }
        }

        return $this;
    }

    public function removeUser($user_id, bool $include_family = false)
    {
        $this->remove_users[] = $user_id;

        if ($include_family && User::find($user_id)) {
            foreach (User::find($user_id)->getFamilyMembers() as $familyMember) {
                $this->remove_users[] = $familyMember->id;
            }
        }

        return $this;
    }

    public function keepExistingRegistrations()
    {
        $this->unregister_in_same_attendance_type = false;

        return $this;
    }

    // This will only ADD registrations.
    public function save()
    {
        foreach ($this->remove_users as $user_id) {
            $this->bible_class->registrations()->detach($user_id);
        }

        foreach ($this->add_users as $user_id) {
            if ($this->unregister_in_same_attendance_type) {
                // Get a list of classes in the group and attendance type that we need to unregister.
                $classes = BibleClass::visibleTo(auth()->user())
                    ->where('bible_class_group_id', $this->bible_class->bible_class_group_id)
                    ->where('user_attendance_type_id', $this->bible_class->user_attendance_type_id)
                    ->get();

                foreach ($classes as $class) {
                    $class->registrations()->detach($user_id);
                }
            }

            $this->bible_class->registrations()->attach($user_id);
        }

        return true;
    }

    // This will REMOVE ALL REGISTRATIONS and ADD only users in our array.
    public function sync()
    {
        $this->bible_class->registrations()->sync($this->add_users);

        return true;
    }
}
