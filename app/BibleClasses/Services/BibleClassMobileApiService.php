<?php

namespace App\BibleClasses\Services;

use App\BibleClasses\BibleClass;
use App\BibleClasses\BibleClassGroup;
use Carbon\Carbon;

class BibleClassMobileApiService
{
    // Current Bible Class Group that is active.
    public static function currentGroup($user)
    {
        return BibleClassGroup::visibleTo($user)
            ->where('start_date', '<=', Carbon::now()->startOfDay())
            ->where('end_date', '>=', Carbon::now()->subDay()->startOfDay())
            ->with([
                'classes',
                'classes.attendanceType:id,name,short_name,sort_id',
                'classes.teachers:id,family_id,first_name,last_name',
                'classes.registrations' => function ($query) use ($user) {
                    // Only show registrations for the family of the current user.
                    $query->where('family_id', $user->family_id)
                        ->select([
                            'users.id',
                            'users.account_id',
                            'users.family_id',
                            'users.first_name',
                            'users.preferred_first_name',
                            'users.last_name',
                        ]);
                },
            ])->first();
    }

    // Current Bible Class Group that is active.
    public static function AdminCurrentGroup($user)
    {
        return BibleClassGroup::visibleTo($user)
            ->where('start_date', '<=', Carbon::now()->startOfDay())
            ->where('end_date', '>=', Carbon::now()->subDay()->startOfDay())
            ->with([
                'classes',
                'classes' => function ($query) {
                    $query->withCount('registrations');
                },
                'classes.attendanceType:id,name,short_name,sort_id',
                'classes.teachers:id,family_id,first_name,last_name',
                //                'classes.registrations:id,account_id,family_id,first_name,preferred_first_name,last_name',
            ])
            ->first();
    }

    // Get Bible class.
    public static function AdminBibleClass($user, $bible_class_id)
    {
        return BibleClass::visibleTo($user)
            ->with([
                'attendanceType:id,name,short_name,sort_id',
                'teachers:id,family_id,first_name,last_name',
                'registrations:id,account_id,family_id,first_name,preferred_first_name,last_name',
                'registrations.attendanceToday:id,user_id,date_attendance,user_attendance_type_id,created_at',
            ])
            ->find($bible_class_id);
    }

    // The next Bible Class Group that has an active sign-up.
    // It MAY BE the same group as the currentGroup.
    public static function nextGroup($user, $active_signup = true)
    {
        return BibleClassGroup::visibleTo($user)
            ->when($active_signup, function ($query) {
                $query->signupIsActive();
            })
            ->with([
                'classes',
                'classes.attendanceType:id,name,short_name,sort_id',
                'classes.teachers:id,family_id,first_name,last_name',
                'classes.registrations' => function ($query) use ($user) {
                    // Only show registrations for the family of the current user.
                    $query->where('family_id', $user->family_id)
                        ->select([
                            'users.id',
                            'users.account_id',
                            'users.family_id',
                            'users.first_name',
                            'users.preferred_first_name',
                            'users.last_name',
                        ]);
                },
            ])
            ->select([
                'id',
                'created_at',
                'name',
                'description',
                'weeks',
                'start_date',
                'end_date',
                'is_signup_active',
            ])->first();
    }
}
