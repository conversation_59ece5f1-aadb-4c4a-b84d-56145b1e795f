<?php

namespace App\BibleClasses\Services;

use App\BibleClasses\BibleClass;
use App\Users\User;
use Illuminate\Support\Arr;

class CreateBibleClass
{
    protected $attributes = [];
    protected $bible_class;
    protected $user;

    public function forUser(User $user): CreateBibleClass
    {
        $this->user = $user;

        return $this;
    }

    public function create($attributes): BibleClass
    {
        $this->attributes = $attributes;

        $this->attributes['account_id']          = $this->user->account->id;
        $this->attributes['account_location_id'] = $this->user->accountLocation ? $this->user->accountLocation->id : null;

        // Get our teacher user IDs, add the account ID to them for syncing
        $teacher_ids = collect(Arr::pull($this->attributes, 'teacher_ids'))->mapWithKeys(function ($item) {
            return [$item => ['account_id' => $this->user->account->id]];
        });

        $this->bible_class = BibleClass::create($this->attributes);

        // Teachers
        $this->bible_class->teachers()->sync($teacher_ids);

        return $this->bible_class;
    }
}
