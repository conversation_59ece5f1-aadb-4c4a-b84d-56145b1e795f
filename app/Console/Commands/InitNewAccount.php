<?php

namespace App\Console\Commands;

use App\Accounts\Account;
use App\Accounts\AccountPlan;
use App\Accounts\ChurchOffice;
use App\Accounts\FinanceBucket;
use App\Attendance\AttendanceType;
use App\Calendars\Calendar;
use App\Users\Email;
use App\Users\Group;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class InitNewAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:init-new-account';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('Creating a new account...');

        $congregation_name  = $this->ask('Congregation name');
        $short_name         = $this->ask('Short name');
        $stripe_customer_id = $this->ask('Stripe customer id');
        $address1           = $this->ask('Address 1');
        $address2           = $this->ask('Address 2');
        $city               = $this->ask('City');
        $state              = $this->ask('State');
        $zip                = $this->ask('Postal Code');
        $phone_work         = $this->ask('Work Phone');
        $admin_first_name   = $this->ask('Admin First Name');
        $admin_last_name    = $this->ask('Admin Last Name');
        $admin_email        = $this->ask('Admin Email');
        $timezone           = $this->choice('Timezone', [
            'America/New_York',
            'America/Chicago',
            'America/Denver',
            'America/Phoenix',
            'America/Los_Angeles',
        ], 0);
        $is_active          = $this->confirm('Start as active?');

        // Check that short name is available and unique.
        if (Account::where('short_name', 'LIKE', $short_name)->exists()) {
            $this->error('Short name already exists!');
            return;
        }

        $account_plan = AccountPlan::create([
            "type"                                 => $short_name,
            "name"                                 => $congregation_name,
            "url_name"                             => $short_name,
            "domain"                               => null,
            "max_emails_per_send"                  => 20000,
            "max_emails_per_month"                 => 100000,
            "max_sms_per_send"                     => 350,
            "max_sms_per_month"                    => 3000,
            "max_voice_per_send"                   => 350,
            "max_voice_per_month"                  => 1200,
            "max_notifications_per_send"           => null,
            "max_notifications_per_month"          => null,
            "max_users"                            => 100,
            "max_admins"                           => 100,
            "max_senders"                          => 100,
            "max_storage"                          => 20000000,
            "price_per_month"                      => 0,
            "price_per_year"                       => 0,
            "is_public"                            => 0,
            "is_active"                            => 1,
            "is_recommended"                       => 0,
            "allow_signups"                        => 0,
            "free_emails_per_month"                => 0,
            "free_sms_per_month"                   => 0,
            "free_voice_per_month"                 => 0,
            "free_notifications_per_month"         => 1000000,
            "free_storage_per_month"               => 20000000,
            "free_podcast_downloads_per_month"     => 10000,
            "price_per_email"                      => 0.120000,
            "price_per_sms"                        => 2.700000,
            "price_per_voice"                      => 3.900000,
            "price_per_notification"               => 0.000000,
            "price_per_10000_podcast_downloads"    => 750.000000,
            "price_per_50000_podcast_downloads"    => 3750.000000,
            "monthly_price_per_email_group"        => 0.000000,
            "monthly_price_per_sms_group"          => 100.000000,
            "monthly_price_per_voice_group"        => 50.000000,
            "monthly_price_per_notification_group" => 0.000000,
            "monthly_price_per_gb_storage"         => 12.000000,
            "monthly_price_online_giving"          => 3000.000000,
            "monthly_price_podcasts"               => 900.000000,
            "price_per_100_users"                  => 1000.000000,
            "included_users"                       => 100,
            "monthly_price_base"                   => 2900,
            "monthly_price_attendance"             => 900,
            "monthly_price_assignments"            => 1400,
            "monthly_price_visitor_tracking"       => 1400,
            "monthly_price_child_checkin"          => 1400,
            "monthly_price_financial_management"   => 2000,
            "monthly_price_sms_enabled"            => 900,
            "monthly_price_website"                => 2500,
            "monthly_price_domain_management"      => 1000,
        ]);

        $this->info('Account Plan created.');

        $account = Account::create([
            "account_plan_id"                => $account_plan->id,
            "last_payment_at"                => null,
            "last_invoice_at"                => null,
            "next_invoice_at"                => null,
            "billing_frequency"              => "monthly",
            "name"                           => $congregation_name,
            "short_name"                     => $short_name,
            "url_name"                       => $short_name,
            "group_email_prefix"             => $short_name,
            "static_short_name"              => $short_name,
            "podcasts_prefix"                => $short_name,
            "domain"                         => null,
            "subdomain"                      => null,
            "cname_domain"                   => null,
            "cname_subdomain"                => null,
            "info_separator"                 => null,
            "use_ssl"                        => 1,
            "file_storage_service"           => 'do',
            "s3_base_url"                    => null,
            "s3_region"                      => null,
            "s3_bucket"                      => null,
            "s3_folder"                      => null,
            "require_verified_emails"        => 0,
            "require_verified_sms"           => 0,
            "stripe_customer_id"             => $stripe_customer_id,
            "address1"                       => $address1,
            "address2"                       => $address2,
            "address3"                       => "",
            "city"                           => $city,
            "state"                          => $state,
            "postal_code"                    => $zip,
            "country_code"                   => "US",
            "phone_work"                     => $phone_work,
            "timezone"                       => $timezone,
            "email"                          => $admin_email,
            "status"                         => "active",
            "is_active"                      => $is_active ? 1 : 0,
            "billing_active"                 => $is_active ? 1 : 0,
            "is_suspended"                   => 0,
            "api_token"                      => "lp_api_live_" . Str::random(32),
            "stripe_account_id"              => null,
            "stripe_granted_scope"           => null,
            "stripe_account_publishable_key" => null,
            "is_tax_exempt"                  => 1,
            "use_v2_billing"                 => 1,
            'can_send_emails'                => 1,
            'can_send_sms'                   => 1,
            'can_send_voice_calls'           => 1,
        ]);

        $account->generateUlid();
        $account->generateHashId();

        $this->info('Account created.');

        $bucket = FinanceBucket::create([
            "account_id"                 => $account->id,
            "account_location_id"        => null,
            "finance_bucket_category_id" => null,
            "name"                       => "Regular Contribution",
            "url_name"                   => "regular",
            "description"                => null,
            "account_code"               => null,
            "yearly_budget"              => null,
            "monthly_budget"             => null,
            "weekly_budget"              => null,
            "quarter_start_at"           => null,
            "year_start_at"              => null,
            "is_contribution_bucket"     => 1,
            "is_reimbursement_bucket"    => 0,
            "is_expense"                 => 0,
            "is_income"                  => 1,
            "is_hidden"                  => 0,
            "sort_id"                    => 1,
        ]);

        $this->info('Account primary financial bucket created.');

        $account_setting_values = [
            [null, $account->id, null, 5, 1, 1, 1],
            [null, $account->id, null, 6, 1, 1, 1],
            [null, $account->id, null, 49, 0, 0, 0],
            [null, $account->id, null, 37, 1, 1, 1],
            [null, $account->id, null, 15, 1, 1, 1],
            [null, $account->id, null, 35, 16, 0, 0],
            [null, $account->id, null, 4, 1, 1, 1],
            [null, $account->id, null, 33, 1, 1, 1],
            [null, $account->id, null, 17, 0, 0, 0],
            [null, $account->id, null, 31, 1, 1, 1],
            [null, $account->id, null, 30, 0, 0, 0],
            [null, $account->id, null, 41, 0, 0, 0],
            [null, $account->id, null, 45, 1, 1, 1],
            [null, $account->id, null, 47, 0, 0, 0],
            [null, $account->id, null, 46, 1, 1, 1],
            [null, $account->id, null, 38, 1, 1, 1],
            [null, $account->id, null, 24, 0, 0, 0],
            [null, $account->id, null, 23, 0, 0, 0],
            [null, $account->id, null, 42, 1, 1, 1],
            [null, $account->id, null, 3, 1, 1, 1],
            [null, $account->id, null, 19, 0, 0, 0],
            [null, $account->id, null, 44, 1, 1, 1],
            [null, $account->id, null, 11, 1, 1, 1],
            [null, $account->id, null, 10, 1, 1, 1],
            [null, $account->id, null, 9, 1, 1, 1],
            [null, $account->id, null, 1, 1, 1, 1],
            [null, $account->id, null, 26, 1, 1, 1],
            [null, $account->id, null, 22, 0, 0, 0],
            [null, $account->id, null, 21, 0, 0, 0],
            [null, $account->id, null, 40, 1, 1, 1],
            [null, $account->id, null, 36, 1, 1, 1],
            [null, $account->id, null, 48, 1, 1, 1],
            [null, $account->id, null, 20, 0, 0, 0],
            [null, $account->id, null, 13, 1, 1, 1],
            [null, $account->id, null, 7, 1, 1, 1],
            [null, $account->id, null, 16, 1, 1, 1],
            [null, $account->id, null, 34, 2, 0, 0],
            [null, $account->id, null, 14, 0, 0, 0],
            [null, $account->id, null, 28, 1, 1, 1],
            [null, $account->id, null, 29, 1, 1, 1],
            [null, $account->id, null, 25, 0, 0, 0],
            [null, $account->id, null, 39, 1, 0, 0],
            [null, $account->id, null, 18, 0, 0, 0],
            [null, $account->id, null, 32, 0, 0, 0],
            [null, $account->id, null, 8, 0, 0, 0],
            [null, $account->id, null, 12, 1, 1, 1],
            [null, $account->id, null, 2, 0, 0, 0],
            [null, $account->id, null, 43, 1, 1, 1],
        ];
        foreach ($account_setting_values as $setting_row) {
            DB::table('account_setting_values')->insert([
                'account_id'          => $setting_row[1],
                'account_location_id' => null,
                'account_setting_id'  => $setting_row[3],
                'value'               => $setting_row[4],
                'enable_for_member'   => $setting_row[5],
                'enable_for_admin'    => $setting_row[6],
            ]);
        }

        $this->info('Account setting values created.');

        $attendance_types = [
            [
                "account_id"            => $account->id,
                "account_location_id"   => null,
                "name"                  => "AM Class",
                "short_name"            => "AM-C",
                "sort_id"               => 1,
                "days_of_week"          => ["7"],
                "is_am"                 => 1,
                "is_pm"                 => 0,
                "enable_member_checkin" => 0,
            ],
            [
                "account_id"            => $account->id,
                "account_location_id"   => null,
                "name"                  => "AM Worship",
                "short_name"            => "AM-W",
                "sort_id"               => 2,
                "days_of_week"          => ["7"],
                "is_am"                 => 1,
                "is_pm"                 => 0,
                "enable_member_checkin" => 0,
            ],
            [
                "account_id"            => $account->id,
                "account_location_id"   => null,
                "name"                  => "PM Worship",
                "short_name"            => "PM-W",
                "sort_id"               => 3,
                "days_of_week"          => ["7"],
                "is_am"                 => 0,
                "is_pm"                 => 1,
                "enable_member_checkin" => 0,
            ],
            [
                "account_id"            => $account->id,
                "account_location_id"   => null,
                "name"                  => "Wednesday Class",
                "short_name"            => "WED",
                "sort_id"               => 4,
                "days_of_week"          => ["3"],
                "is_am"                 => 0,
                "is_pm"                 => 1,
                "enable_member_checkin" => 0,
            ],
        ];
        foreach ($attendance_types as $type) {
            AttendanceType::create($type);
        }

        $this->info('Account Attendance Types created.');

        $default_groups = [
            [
                "name"                       => "Members",
                "url_name"                   => "members",
                "allow_individual_to_toggle" => 0,
                "indicates_membership"       => 1,
                "is_hidden"                  => 1,
                "indicates_visitor"          => 0,
                "is_default_member_group"    => 1,
                "is_default_visitor_group"   => 0,
            ],
            [
                "name"                       => "Deacons",
                "url_name"                   => "deacon",
                "allow_individual_to_toggle" => 0,
                "indicates_membership"       => 0,
                "is_hidden"                  => 1,
                "indicates_visitor"          => 0,
                "is_default_member_group"    => 0,
                "is_default_visitor_group"   => 0,
            ],
            [
                "name"                       => "Elders",
                "url_name"                   => "elder",
                "allow_individual_to_toggle" => 0,
                "indicates_membership"       => 0,
                "is_hidden"                  => 1,
                "indicates_visitor"          => 0,
                "is_default_member_group"    => 0,
                "is_default_visitor_group"   => 0,
            ],
            [
                "name"                       => "Visitors",
                "url_name"                   => "visitors",
                "allow_individual_to_toggle" => 0,
                "indicates_membership"       => 0,
                "is_hidden"                  => 1,
                "indicates_visitor"          => 1,
                "is_default_member_group"    => 0,
                "is_default_visitor_group"   => 1,
            ],

        ];
        $member_group   = null;
        foreach ($default_groups as $index => $group) {
            $group = Group::create(
                array_merge($group, [
                    "account_id"                       => $account->id,
                    "account_location_id"              => null,
                    "creator_id"                       => 1,
                    "description"                      => null,
                    "is_suspended"                     => 0,
                    "allow_all_members_to_send"        => 0,
                    "allow_lightpost_users_to_email"   => 0,
                    "allow_internet_to_email"          => 0,
                    "enable_posts"                     => 0,
                    "allow_members_to_post"            => 0,
                    "allow_members_to_comment"         => 0,
                    "allow_members_to_view_membership" => 0,
                    "uuid"                             => Str::uuid(),
                    "sort_id"                          => $index,
                ])
            );

            if ($group['url_name'] == 'members') {
                $member_group = $group;
            }
        }

        $this->info('Account default Groups created.');

        $default_roles = [
            [
                "key"                  => "admin",
                "name"                 => "Account Admin",
                "sort_id"              => 1,
                "can_delete"           => 0,
                "indicates_membership" => 0,
            ],
            [
                "key"                  => "member",
                "name"                 => "Member",
                "sort_id"              => 2,
                "can_delete"           => 0,
                "indicates_membership" => 1,
            ],
            [
                "key"                  => "deacon",
                "name"                 => "Deacon",
                "sort_id"              => 3,
                "can_delete"           => 1,
                "indicates_membership" => 0,
            ],
            [
                "key"                  => "elder",
                "name"                 => "Elder",
                "sort_id"              => 4,
                "can_delete"           => 1,
                "indicates_membership" => 0,
            ],
        ];
        $admin_role    = null;
        $member_role   = null;
        foreach ($default_roles as $index => $role) {
            $role = Role::create(
                array_merge($role, [
                    "account_id"          => $account->id,
                    "account_location_id" => null,
                    "description"         => null,
                ])
            );

            if ($role['key'] == 'admin') {
                $admin_role = $role;
            }
            if ($role['key'] == 'member') {
                $member_role = $role;
            }
        }

        $this->info('Account default Roles created.');

        if (!$admin_role) {
            $this->error('Admin Role not created or found.');
        }

        // Admin Role permissions
        $role_to_permissions = [
            25,
            58,
            54,
            47,
            30,
            20,
            67,
            54,
            53,
            58,
            43,
            22,
            51,
            64,
            49,
            37,
            42,
            23,
            34,
            4,
            53,
            24,
            39,
            50,
            51,
            61,
            18,
            2,
            19,
            16,
            66,
            1,
            59,
            10,
            17,
            49,
            15,
            26,
            31,
            38,
            27,
            65,
            50,
            40,
            59,
            41,
            28,
            55,
            52,
            29,
            60,
            32,
            5,
            3,
            42,
            55,
            21,
            56,
            57,
            6,
            62,
        ];
        foreach ($role_to_permissions as $role_permission_id) {
            DB::table('user_role_permission')->insert([
                'user_role_id'            => $admin_role?->id,
                'user_role_permission_id' => $role_permission_id,
            ]);
        }

        $this->info('Permissions for Account Admin created.');

        // Create Admin User
        $admin_user = User::create([
            "account_id"               => $account->id,
            "account_location_id"      => null,
            "remember_token"           => null,
            "date_registered"          => null,
            "date_background_check"    => null,
            "date_membership"          => null,
            "date_baptism"             => null,
            "date_married"             => null,
            "date_deceased"            => null,
            "confirmed_email"          => null,
            "last_login"               => null,
            "birthdate"                => null,
            "can_teach"                => null,
            "can_lead"                 => null,
            "is_baptized"              => null,
            "family_id"                => null,
            "family_role"              => "head",
            "prefix_name"              => null,
            "suffix_name"              => null,
            "preferred_first_name"     => null,
            "first_name"               => $admin_first_name,
            "last_name"                => $admin_last_name,
            "gender"                   => "male",
            "marital_status"           => "single",
            "timezone"                 => "UM6",
            "change_password_on_login" => 0,
            "exclude_from_reports"     => 0,
            "status"                   => "active",
            "is_active"                => 1,
            "is_super"                 => 0,
            "public_token"             => Str::uuid(),
        ]);

        $admin_user->family_id = $admin_user->id;

        $admin_user->save();

        $this->info('Account Admin User created.');

        $admin_user->roles()->attach($admin_role->id);
        $admin_user->roles()->attach($member_role->id);
        $admin_user->groups()->attach($member_group?->id);

        Email::create([
            "user_id"                     => $admin_user->id,
            "family_id"                   => null,
            "type"                        => "personal",
            "email"                       => $admin_email,
            "is_family"                   => 0,
            "is_primary"                  => 1,
            "is_hidden"                   => 0,
            "receives_group_emails"       => 1,
            "allow_messages"              => 1,
            "sort_id"                     => null,
            "reported_spam_complaint"     => null,
            "spam_complaint_subject_line" => null,
        ]);

        $this->info('Account Admin email added.');

        $calendar = Calendar::create([
            "uuid"                => Str::uuid(),
            "account_id"          => $account->id,
            "account_location_id" => null,
            "user_group_id"       => null,
            "name"                => "Member",
            "url_name"            => "member",
            "color"               => null,
            "background_color"    => "3b82f6",
            "border_color"        => null,
            "text_color"          => "ffffff",
            "is_hidden"           => 0,
            "is_private"          => 0,
            "is_group_only"       => 0,
            "is_public"           => 0,
            "auto_show"           => 0,
        ]);

        $calendar->generateUlid();

        $this->info('Account default calendar created.');

        $office = ChurchOffice::create([
            'account_id'         => $account->id,
            'name'               => 'Elder',
            'plural_name'        => 'Elders',
            'short_name'         => 'elders',
            'url_name'           => 'elders',
            'show_in_leadership' => true,
            'is_public'          => true,
            'sort_id'            => 0,
        ]);
        $office->generateUlid();
        $office = ChurchOffice::create([
            'account_id'         => $account->id,
            'name'               => 'Minister',
            'plural_name'        => 'Ministers',
            'short_name'         => 'ministers',
            'url_name'           => 'ministers',
            'show_in_leadership' => true,
            'is_public'          => true,
            'sort_id'            => 1,
        ]);
        $office->generateUlid();
        $office = ChurchOffice::create([
            'account_id'         => $account->id,
            'name'               => 'Deacon',
            'plural_name'        => 'Deacons',
            'short_name'         => 'deacons',
            'url_name'           => 'deacons',
            'show_in_leadership' => true,
            'is_public'          => true,
            'sort_id'            => 2,
        ]);
        $office->generateUlid();

        $this->info('Account default church offices created.');

        $this->newLine();
        $this->info('Account setup completed!');
        $this->newLine();

        $this->comment('----- Account information -----');
        $this->comment('Account ID: ' . $account->id);
        $this->comment('Admin User ID: ' . $admin_user->id);

        return true;
    }
}
