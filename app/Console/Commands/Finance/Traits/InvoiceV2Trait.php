<?php

namespace App\Console\Commands\Finance\Traits;

use App\Accounts\AccountNotification;
use App\Accounts\Invoice;
use App\Accounts\Services\CreateInvoiceItem;
use App\Messages\MessageHandler;
use App\Messages\MessageHistory;
use App\Sermons\File;
use App\Sermons\Sermon;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait InvoiceV2Trait
{
    public function generateV2Invoice($account)
    {
        return DB::transaction(function () use ($account) {
            $invoice = new Invoice();

            $invoice->account_id = $account->id;
            $invoice->posted_at  = Carbon::now();

            $invoice->save();

            // General Account Billing
            $this->createMonthlyChargeLineItems2($invoice);
            $this->createConsumablesLineItems2($invoice);
            $this->createAccountNotificationConsumablesLineItems2($invoice);
            $this->createMessageHandlersLineItems2($invoice);
            $this->createStorageLineItem2($invoice);

            // Additional Features / Services
            $this->createOnlineGivingLineItem2($invoice);
            $this->createPodcastsLineItem2($invoice);
            $this->createAttendanceLineItem2($invoice);
            $this->createAssignmentsLineItem2($invoice);
            $this->createVisitorTrackingLineItem2($invoice);
            $this->createChildCheckinLineItem2($invoice);
            $this->createFinancialManagementLineItem2($invoice);

            $this->createWebsiteLineItem($invoice);
            $this->createWebsiteDomainsLineItems($invoice);

            // Any manually added pending invoice items.
            $invoice->addPendingItems();

            // Figure our totals.
            $invoice->calculateAndSaveTotals();

            // Update our account for our last invoice date
            $account->last_invoice_at = now();

            // Set our "next invoice at"
            if ($account->billing_frequency == 'monthly') {
                $account->next_invoice_at = now()->addMonth();
            } elseif ($account->billing_frequency == 'yearly') {
                $account->next_invoice_at = now()->addYear();
            }

            $account->save();

            return $invoice;
        }, 1);
    }

    public function createMonthlyChargeLineItems2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createMonthlyChargeLineItem2 -- Account # ' . $invoice->account_id);

        $extra_member_packs = $invoice->account->getUserOverageUnitCount();

        $account_plan = $invoice->account->plan;

        (new CreateInvoiceItem($invoice))
            ->setTitle($account_plan->name)
            ->setDescription('Base monthly price for Lightpost')
            ->setQuantity(1)
            ->setType('monthly_plan')
            ->isTaxable(!$invoice->account->is_tax_exempt)
            ->setAmount($account_plan->monthly_price_base)
            ->create();

        if ($extra_member_packs > 0) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Extra members - ' . $extra_member_packs * 100)
                ->setQuantity($extra_member_packs)
                ->setType('monthly_plan')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($account_plan->price_per_100_users)
                ->create();
        }

        // Make our line item for our extra members.
//        if ($extra_member_packs > 0) {
//            $item = (new CreateInvoiceItem($invoice))
//                ->setTitle('Additional Members')
//                ->setDescription('Extra members @ $' . number_format($account_plan->price_per_100_users / 100, 2) . ' per 100')
//                ->setQuantity($extra_member_packs)
//                ->setType('monthly_plan')
//                ->isTaxable(!$invoice->account->is_tax_exempt)
//                ->setAmount($account_plan->price_per_100_users)
//                ->create();
//        }

        Log::info('CreateAccountInvoices:createMonthlyChargeLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createOnlineGivingLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createOnlineGivingLineItem2 -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.online_giving')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Online Giving')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_online_giving)
                ->create();
        }

        Log::info('CreateAccountInvoices:createOnlineGivingLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createAttendanceLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createAttendanceLineItem2 -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.attendance')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Attendance')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_attendance)
                ->create();
        }

        Log::info('CreateAccountInvoices:createAttendanceLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createAssignmentsLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createAssignmentsLineItem2 -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.worship_assignments') || $invoice->account->hasFeature('feature.assignments')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Assignments')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_assignments)
                ->create();
        }

        Log::info('CreateAccountInvoices:createAssignmentsLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createPodcastsLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createPodcastsLineItem2 -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.podcasts')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Podcasts')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_podcasts)
                ->create();
        }

        // @TODO: Bill for extra downloads

        Log::info('CreateAccountInvoices:createPodcastsLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createVisitorTrackingLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createVisitorTrackingLineItem2 -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.visitor_tracking')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Visitor Tracking')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_visitor_tracking)
                ->create();
        }

        Log::info('CreateAccountInvoices:createVisitorTrackingLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createChildCheckinLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createChildCheckinLineItem2 -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.child_checkin')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Child Check-in')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_child_checkin)
                ->create();
        }

        Log::info('CreateAccountInvoices:createChildCheckinLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createFinancialManagementLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createFinancialManagementLineItem2 -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.finance')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Financial Management')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_financial_management)
                ->create();
        }

        Log::info('CreateAccountInvoices:createFinancialManagementLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createWebsiteLineItem(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createWebsiteLineItem -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.website')) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Feature - Website')
                ->setType('feature')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($invoice->account->plan?->monthly_price_website)
                ->create();
        }

        Log::info('CreateAccountInvoices:createWebsiteLineItem -- Finished for account # ' . $invoice->account_id);
    }

    public function createWebsiteDomainsLineItems(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createWebsiteDomainsLineItems -- Account # ' . $invoice->account_id);

        $domains_with_management = DB::table('account_domains')
            ->where('account_id', $invoice->account_id)
            ->where('is_managed', 1)
            ->get();

        foreach ($domains_with_management as $domain) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Managed Domain - ' . $domain->domain)
                ->setType('service')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($domain->managed_monthly_cost)
                ->create();

            // If we bill for domain registration (cost > 0)
            // And we've never billed before OR if the last renewal was more than a year ago
//            if ($domain->renewal_yearly_cost > 0 &&
//                (!$domain->last_renewal_billed_at || $domain->last_renewal_billed_at->lt(Carbon::now()->subYear()))) {
//                // Create registrar fee renewal
//                (new CreateInvoiceItem($invoice))
//                    ->setTitle('Domain Registrar Renewal (yearly) - ' . $domain->domain)
//                    ->setType('service')
//                    ->isTaxable(!$invoice->account->is_tax_exempt)
//                    ->setAmount($domain->renewal_yearly_cost)
//                    ->create();
//            }
        }

        Log::info('CreateAccountInvoices:createWebsiteLineItem -- Finished for account # ' . $invoice->account_id);
    }

    public function createConsumablesLineItems2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createConsumablesLineItems2 -- Account # ' . $invoice->account_id);

        $last_month   = Carbon::now()->subMonth();
        $account_plan = $invoice->account->plan;

        // EMAIL //
        $email_charge = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 1); // 1 == email
                    });
            })
            ->sum('charge');

        $email_count = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 1); // 1 == email
                    });
            })
            ->count();

        $email_charge = round($email_charge);

        if ($email_count > 0) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Email usage for ' . $last_month->format('F Y'))
                ->setQuantity($email_count)
                ->setType('consumable')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($account_plan->price_per_email)
                ->setAmountSubtotal($email_charge) // We override any calculated amount, because some messages might be free.
                ->create();
        }

        // SMS //
        $sms_charge = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 2); // 2 == sms
                    });
            })
            ->sum('charge');
        $sms_count  = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 2); // 2 == sms
                    });
            })
            ->count();

        $sms_charge = round($sms_charge);

        if ($sms_count > 0) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('SMS usage for ' . $last_month->format('F Y'))
                ->setQuantity($sms_count)
                ->setType('consumable')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($account_plan->price_per_sms)
                ->setAmountSubtotal($sms_charge) // We override any calculated amount, because some messages might be free.
                ->create();
        }

        // VOICE //
        $voice_charge = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 4); // 4 == voice
                    });
            })
            ->sum('charge');
        $voice_count  = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 4); // 4 == voice
                    });
            })
            ->count();

        $voice_charge = round($voice_charge);

        if ($voice_count > 0) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Voice usage for ' . $last_month->format('F Y'))
                ->setQuantity($voice_count)
                ->setType('consumable')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($account_plan->price_per_voice)
                ->setAmountSubtotal($voice_charge) // We override any calculated amount, because some messages might be free.
                ->create();
        }

        Log::info('CreateAccountInvoices:createConsumablesLineItems2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createAccountNotificationConsumablesLineItems2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createAccountNotificationConsumablesLineItems2 -- Account # ' . $invoice->account_id);

        $last_month   = Carbon::now()->subMonth();
        $account_plan = $invoice->account->plan;

        // EMAIL //
        $email_charge = AccountNotification::where('account_id', $invoice->account_id)
            ->whereMonth('created_at', $last_month->format('n'))
            ->whereYear('created_at', $last_month->format('Y'))
            ->where('message_type_id', 1) // email
            ->sum('charge');

        $email_count = AccountNotification::where('account_id', $invoice->account_id)
            ->whereMonth('created_at', $last_month->format('n'))
            ->whereYear('created_at', $last_month->format('Y'))
            ->where('message_type_id', 1) // email
            ->count();

        $email_charge = round($email_charge);

        if ($email_count > 0) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Account email notifications usage for ' . $last_month->format('F Y'))
                ->setDescription('This includes emails for crisis check-in.')
                ->setQuantity($email_count)
                ->setType('consumable')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($account_plan->price_per_email)
                ->setAmountSubtotal($email_charge) // We override any calculated amount, because some messages might be free.
                ->create();
        }

        Log::info('CreateAccountInvoices:createAccountNotificationConsumablesLineItems2 -- Finished for account # ' . $invoice->account_id);
    }

    public function createStorageLineItem2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createStorageLineItem2 -- Account # ' . $invoice->account_id);

        $account_plan = $invoice->account->plan;

        $sub           = File::select(DB::raw('SUM(`file_size`)'))->where('sermon_id', DB::raw('sermons.id'))->getQuery();
        $storage_usage = Sermon::where('account_id', $invoice->account_id)->selectSub($sub, 'total_file_size')->get();

        $total_storage_used = round($storage_usage->sum('total_file_size') / **********, 2);

        $included_storage = round($account_plan->max_storage / 1000000, 2);

        $overage = $total_storage_used - $included_storage;

        if ($overage > 1) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Extra storage - ' . $total_storage_used . 'GB / ' . $included_storage . 'GB ')
                ->setQuantity(round($overage))
                ->setType('storage')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($account_plan->monthly_price_per_gb_storage)
                ->create();
        }

        Log::info('CreateAccountInvoices:createStorageLineItem2 -- Finished for account # ' . $invoice->account_id);
    }

    /* NOTES:
    - ~~Only charge for SMS handlers where a `cost` value is provided.  Free handlers should not be charged.~~
    - Nov 2023: We now bill only $5/month for any number of SMS handlers.  They all use one from number now.
    */
    public function createMessageHandlersLineItems2(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createMessageHandlersLineItems2 -- Start for account # ' . $invoice->account_id);

        $sms_handler_price = $invoice->account->plan?->monthly_price_sms_enabled ?? 0;
        $last_month        = now()->subMonth()->day(1);

        // SMS MESSAGE HANDLERS //
        $sms_handlers_count = MessageHandler::where('account_id', $invoice->account_id)
            ->where('is_active', true)
            //            ->where('cost', '>', 0) // Nov 2023
            ->where('message_type_id', 2) // 2 == SMS
            ->count();

        if ($sms_handlers_count) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Group message handlers (SMS) for ' . $last_month->format('F Y'))
                ->setQuantity(1)
                ->setType('service')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($sms_handler_price)
                ->create();
        }

        // TRASHED MESSAGE HANDLERS //
        // This is only Message Handlers that were deleted last month, since we still incur a cost with our SMS provider.
        $sms_trashed_handlers_count = MessageHandler::where('account_id', $invoice->account_id)
            ->where('deleted_at', '>', $last_month)
            ->where('deleted_at', '<', now()->format('Y-m-d'))
//            ->where('cost', '>', 0) // Nov 2023
            ->onlyTrashed()
            ->where('message_type_id', 2) // 2 == SMS
            ->count();

        // We only write a line item for a deleted handler if there are no current handlers.
        if ($sms_handlers_count == 0 && $sms_trashed_handlers_count > 0) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Deleted group message handlers (SMS) for ' . $last_month->format('F Y'))
                ->setQuantity(1)
                ->setType('service')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount($sms_handler_price)
                ->create();
        }

        // EMAIL MESSAGE HANDLERS - FREE //
        $email_handlers_count = MessageHandler::where('account_id', $invoice->account_id)
            ->where('is_active', true)
            ->where('cost', '>', 0)
            ->where('message_type_id', 2) // 2 == SMS
            ->count();

        if ($email_handlers_count) {
            (new CreateInvoiceItem($invoice))
                ->setTitle('Group message handlers (E-mail) for ' . $last_month->format('F Y'))
                ->setQuantity($email_handlers_count)
                ->setType('service')
                ->isTaxable(!$invoice->account->is_tax_exempt)
                ->setAmount(0)
                ->create();
        }

        Log::info('CreateAccountInvoices:createMessageHandlersLineItems2 -- Finished for account # ' . $invoice->account_id);
    }
}
