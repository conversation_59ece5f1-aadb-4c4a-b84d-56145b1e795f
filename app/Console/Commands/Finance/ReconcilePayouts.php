<?php

namespace App\Console\Commands\Finance;

use App\Accounts\Account;
use App\Accounts\Payout;
use App\Users\Payment;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ReconcilePayouts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:reconcile-payouts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates/updates payouts and ties payments to them.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        \Stripe\Stripe::setApiKey(config('services.stripe.connect.secret'));

        Log::info('-- START ReconcilePayouts command --');
        $this->info('-- START ReconcilePayouts command --');

        $accounts = Account::whereNotNull('stripe_account_id')->get();

        Log::info('ReconcilePayouts: Found ' . $accounts->count() . ' accounts with a Stripe account to update payouts from.');
        $this->info('ReconcilePayouts: Found ' . $accounts->count() . ' accounts with a Stripe account to update payouts from.');

        foreach ($accounts as $account) {
            $stripe_payouts = $this->getNewPayoutsFromStripe($account);

            Log::info('ReconcilePayouts: Looking at payouts for Account # ' . $account->id);
            $this->info('ReconcilePayouts: Looking at payouts for Account # ' . $account->id);

            Log::info('ReconcilePayouts: Found ' . count($stripe_payouts) . ' payouts for Account # ' . $account->id);
            $this->info('ReconcilePayouts: Found ' . count($stripe_payouts) . ' payouts for Account # ' . $account->id);

            foreach ($stripe_payouts as $stripe_payout) {
                try {
                    $current_payout = Payout::firstOrCreate([
                        'account_id'         => $account->id,
                        'provider_payout_id' => $stripe_payout->id,
                        'provider'           => 'stripe',
                    ], [
                        'name'         => $stripe_payout->description,
                        'amount'       => $stripe_payout->amount,
                        'posted_at'    => Carbon::createFromTimestamp($stripe_payout->created),
                        'deposited_at' => Carbon::createFromTimestamp($stripe_payout->arrival_date),
                        'status'       => $stripe_payout->status,
                    ]);
                } catch (\Exception $e) {
                    Log::error('ReconcilePayouts::handle - Error getting/creating a new payout for account', [
                        'account_id' => $account->id,
                        'error'      => $e->getMessage(),
                        'trace'      => $e->getTraceAsString(),
                        'e'          => $e,
                    ]);

                    Log::info('ReconcilePayouts: Error getting/creating a new payout for account ' . $account->id);
                    $this->info('ReconcilePayouts: Error getting/creating a new payout for account ' . $account->id . ' - ' . $e->getMessage());
                }

                $this->getAndProcessTransactionsForPayout($account, $current_payout);
            }
        }

        Log::info('-- END ReconcilePayouts command --');
        $this->info('-- END ReconcilePayouts command --');
    }

    public function getNewPayoutsFromStripe($account)
    {
        Log::info('ReconcilePayouts: Getting latest payouts from Stripe for account ' . $account->id);
        $this->info('ReconcilePayouts: Getting latest payouts from Stripe for account ' . $account->id);

        try {
            $result = \Stripe\Payout::all(['limit' => 100], [
                'stripe_account' => $account->stripe_account_id,
            ]);
        } catch (\Exception $e) {
            Log::error('ReconcilePayouts::getNewPayoutsFromStripe - Error getting the payouts from Stripe.', [
                'account_id' => $account->id,
                'error'      => $e->getMessage(),
                'trace'      => $e->getTraceAsString(),
                'e'          => $e,
            ]);

            Log::info('ReconcilePayouts: Error getting the payouts from Stripe.');
            $this->info('ReconcilePayouts: Error getting the payouts from Stripe. ' . $e->getMessage());
        }

        return $result->data;
    }

    public function getAndProcessTransactionsForPayout(Account $account, Payout $payout)
    {
        Log::info('ReconcilePayouts: Getting balance transactions / payments for payout ' . $payout->id . ' for account ' . $account->id);
        $this->info('ReconcilePayouts: Getting balance transactions / payments for payout ' . $payout->id . ' for account ' . $account->id);

        $payments = null;

        try {
            // Get 50 transactions per call.
            $payments = \Stripe\BalanceTransaction::all(['limit' => 50, 'payout' => $payout->provider_payout_id], [
                'stripe_account' => $account->stripe_account_id,
            ]);
        } catch (\Exception $e) {
            Log::error('ReconcilePayouts::getAndProcessTransactionsForPayout - Error getting the balance transactions for a specific payout from Stripe.', [
                'account_id' => $account->id,
                'payout_id'  => $payout->id,
                'error'      => $e->getMessage(),
                'trace'      => $e->getTraceAsString(),
            ]);

            Log::info('ReconcilePayouts: Error getting the balance transactions for a specific payout from Stripe.');
            $this->info('ReconcilePayouts: Error getting the balance transactions for a specific payout from Stripe. ' . $e->getMessage());
        }

        if (!$payments) {
            Log::warning('ReconcilePayouts::getAndProcessTransactionsForPayout - No payments found for payout.', [
                'account_id' => $account->id,
                'payout_id'  => $payout->id,
                'payments'   => $payments,
            ]);
        }

        // Process all our possible transactions -- Stripe's paging iterator will make additional calls as needed.
        if ($payments !== null) {
            Log::info('ReconcilePayouts: -Found ' . count($payments?->data) . ' payments to look through.');
            $this->info('ReconcilePayouts: -Found ' . count($payments?->data) . ' payments to look through.');

            foreach ($payments->autoPagingIterator() as $payment) {
                if ($payment->type == 'payout') {
                    continue;
                }

                try {
                    Log::info('ReconcilePayouts: --Looking at payment: ' . $payment->id);
                    $this->info('ReconcilePayouts: --Looking at payment: ' . $payment->id);

                    $user_payment = Payment::where('stripe_balance_transaction_id', $payment->id)
                        ->where('account_id', $account->id)
                        ->whereNull('account_payout_id')
                        ->first();

                    if ($user_payment) {
                        Log::info('ReconcilePayouts: --- Found in database, updating...');
                        $this->info('ReconcilePayouts: --- Found in database, updating...');
                        $user_payment->account_payout_id = $payout->id;
                        $user_payment->amount_fee        = $payment->fee;
                        $user_payment->amount_deposited  = $payment->net;
                        $user_payment->status            = $payment->status;
                        $user_payment->stripe_status     = $payment->status;

                        $user_payment->save();
                    } else {
                        Log::info('ReconcilePayouts:  --- Did not find unreconciled payment in database.');
                        $this->info('ReconcilePayouts:  --- Did not find unreconciled payment in database.');
                    }
                } catch (\Exception $e) {
                    Log::error('ReconcilePayouts::getAndProcessTransactionsForPayout - Error finding and updating a user_payment.', [
                        'account_id' => $account->id,
                        'payout_id'  => $payout->id,
                        'payment_id' => $payment->id,
                        'error'      => $e->getMessage(),
                    ]);

                    Log::info('ReconcilePayouts: Error finding and updating a user_payment: ' . optional($user_payment)->id);
                    $this->info('ReconcilePayouts: Error finding and updating a user_payment: ' . optional($user_payment)->id . ' - ' . $e->getMessage());
                }
            }
        } else {
            Log::warning('ReconcilePayouts::getAndProcessTransactionsForPayout - No payments found for payout.', [
                'account_id' => $account->id,
                'payout_id'  => $payout->id,
                'payments'   => $payments,
            ]);
        }
    }
}

// Sample Payment
//      id: "txn_0GmmTtiQpTYhhNdh"
//      object: "balance_transaction"
//      amount: 1600
//      available_on: **********
//      created: **********
//      currency: "usd"
//      description: "Regular Contribution"
//      exchange_rate: null
//      fee: 13
//      fee_details: array:1 [▶]
//      net: 1587
//      reporting_category: "charge"
//      source: "py_HLTQe2bpX"
//      status: "available"
//      type: "payment"

// Sample Payout Data:
//      "id" => "tr_2FLoKlg18p"
//      "object" => "payout"
//      "amount" => 100
//      "arrival_date" => **********
//      "automatic" => true
//      "balance_transaction" => null
//      "created" => **********
//      "currency" => "usd"
//      "description" => "STRIPE PAYOUT"
//      "destination" => "ba_TT8kbmNAC6"
//      "failure_balance_transaction" => null
//      "failure_code" => null
//      "failure_message" => null
//      "livemode" => false
//      "metadata" => []
//      "method" => "standard"
//      "source_type" => "card"
//      "statement_descriptor" => null
//      "status" => "paid"
//      "type" => "bank_account"