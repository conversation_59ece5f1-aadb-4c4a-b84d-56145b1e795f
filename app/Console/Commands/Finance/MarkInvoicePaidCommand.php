<?php

namespace App\Console\Commands\Finance;

use App\Accounts\Invoice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MarkInvoicePaidCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounts:mark-invoice-paid {account_invoice_id} {charge_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marks an account invoice as paid and records the transaction id from the payment provider.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('-- START MarkInvoicePaid command --');

        $account_invoice_id = $this->argument('account_invoice_id');

        $invoice = Invoice::find($account_invoice_id);

        // No invoice found?
        if (!$invoice) {
            $this->error('Invoice not found.');

            return false;
        }

        try {
            $invoice->paid_at              = now();
            $invoice->payment_reference_id = $this->argument('charge_id');

            $invoice->save();
        } catch (\Exception $e) {
            Log::error('MarkInvoicePaid: Charge failed.', [
                'account_invoice_id' => $account_invoice_id,
                'error'              => $e->getMessage(),
                'amount_total'       => $invoice->amount_total,
            ]);

            $this->error('Save failed: ' . $e->getMessage());

            return false;
        }

        $this->info('✅ Marked paid successful!');

        Log::info('-- END MarkInvoicePaid command --');

        return true;
    }
}
