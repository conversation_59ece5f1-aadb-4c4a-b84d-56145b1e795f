<?php

namespace App\Console\Commands\Finance;

use App\Accounts\Account;
use App\Accounts\Invoice;
use App\Accounts\InvoiceItem;
use App\Accounts\Services\ChargeInvoice;
use App\Console\Commands\Finance\Traits\InvoiceV2Trait;
use App\Messages\MessageHandler;
use App\Messages\MessageHistory;
use App\Sermons\File;
use App\Sermons\Sermon;
use Brick\Math\RoundingMode;
use Brick\Money\Money;
use DateTimeZone;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CreateAccountInvoices extends Command
{
    use InvoiceV2Trait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'accounts:generate-invoices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates new invoices for accounts on a monthly basis. Should be run on the 1st of the month.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('-- START CreateAccountInvoices command --');

        $accounts = Account::where('is_active', true)
            ->where('billing_active', true)
            ->get();

        Log::info('CreateAccountInvoices: Found ' . $accounts->count() . ' active accounts to create invoices for.');

        foreach ($accounts as $account) {
            // Do not run billing for our test accounts.  Actual accounts start at 10.
            if ($account->id < 10) {
                continue;
            }

            $last_invoice = $account->invoices()->orderBy('created_at', 'desc')->first();

            // SAFTEY CHECKS -- not business logic for "we should definitely invoice" but a check for "we should definitely NOT invoice"
            // Make sure our last invoice didn't happen this month/year.  (avoid double billing)
            if ($account->billing_frequency == 'monthly' && $last_invoice && $last_invoice->created_at->format('M') == now()->format('M')) {
                Log::warning('CreateAccountInvoices: Last invoice for account # ' . $account->id . ' is in the same month as the current month.  Skipping creating a new invoice.');
                $this->info('Account ' . $account->id . ' has already been invoiced this month!');
                continue;
            } elseif ($account->billing_frequency == 'yearly' && $last_invoice && $last_invoice->created_at->format('Y') == now()->format('Y')) {
                Log::warning('CreateAccountInvoices: Last invoice for account # ' . $account->id . ' is in the same YEAR as the current YEAR.  Skipping creating a new invoice.');
                $this->info('Account ' . $account->id . ' has already been invoiced this year!');
                continue;
            }

            // Only do billing on the 1st of the month.
//            if ($last_invoice && Carbon::now(new DateTimeZone('America/Chicago'))->format('j') != 1) {
            if (Carbon::now(new DateTimeZone('America/Chicago'))->format('j') != 1) {
                Log::warning('CreateAccountInvoices: It is not the first day of the month.  Skipping creating a new invoice for account # ' . $account->id);
                $this->info('It is not the first day of the month.  Skipping creating a new invoice for account # ' . $account->id . '. The current day is: ' . Carbon::now()->format('j'));
                continue;
            }

            // If billing monthly, make sure it's time to bill.
            // This should only apply to the MONTHLY LIGHTPOST FEE. Not consumables and add-ons.
//            if ($last_invoice && $account->billing_frequency == 'monthly' && $last_invoice->created_at->format('M') != now()->format('M')) {
//                Log::warning('CreateAccountInvoices: It is not the in the NEXT month for monthly billing.  Skipping creating a new invoice for account # ' . $account->id);
//                $this->info('It is not the first day of the month.  Skipping creating a new invoice for account # ' . $account->id . '. The current day is: ' . Carbon::now()->format('j'));
//                // continue;
//            }

            // If billing yearly, make sure it's the next year and correct month.
//            if ($last_invoice && $account->billing_frequency == 'yearly'
//                && $last_invoice->created_at->format('Y') < now()->format('Y')
//                && $last_invoice->created_at->format('M') == now()->format('M')
//            ) {
//                Log::warning('CreateAccountInvoices: It is not the next year *and* correct month for yearly billing.  Skipping creating a new invoice for account # ' . $account->id);
//                $this->info('It is not the first day of the month.  Skipping creating a new invoice for account # ' . $account->id . '. The current day is: ' . Carbon::now()->format('j'));
//                // continue;
//            }

            // Use new billing or continue doing it the old way.
            if ($account->use_v2_billing) {
                $invoice = $this->generateV2Invoice($account);
            } else {
                $invoice = new Invoice();

                $invoice->account_id = $account->id;
                $invoice->posted_at  = Carbon::now();

                $invoice->save();

                $this->createMonthlyChargeLineItem($invoice);
                $this->createOnlineGivingLineItem($invoice);
                $this->createPodcastsLineItem($invoice);
                $this->createConsumablesLineItems($invoice);
                $this->createStorageLineItem($invoice);

                $invoice->calculateAndSaveTotals();

                // Update our account for our last invoice date
                $account->last_invoice_at = now();

                // Set our "next invoice at"
                if ($account->billing_frequency == 'monthly') {
                    $account->next_invoice_at = now()->addMonth();
                } elseif ($account->billing_frequency == 'yearly') {
//                $account->next_invoice_at = now()->addYear();
                }

                $account->save();
            }

            $this->info('Account #' . $account->id . ' - ' . $account->name . ' has been invoiced for $' . number_format($invoice->amount_total / 100, 2) . '.');

            // Attempt to charge this newly created invoice.
            // Only charge if we have a credit card on file -- ACH payments break via API for some reason unexplored right now.
            if (config('app.env') == 'production') {
                if ($account->billing_active && $account->is_active && $invoice->amount_total > 0 && !Str::startsWith($account->stripe_payment_method_id, 'ba_')) {
                    try {
                        (new ChargeInvoice($invoice))
                            ->charge();

                        $this->info('Charge successful.');
                    } catch (\Exception $e) {
                        Log::error('CreateAccountInvoices:ChargeInvoice: Charge failed.', [
                            'account_invoice_id' => $invoice->id,
                            'error'              => $e->getMessage(),
                            'amount_total'       => $invoice->amount_total,
                        ]);

                        $this->warn('Charge failed for account #' . $account->id . ' - ' . $account->name . '. Error: ' . $e->getMessage());
                    }
                }
            }
        }

        Log::info('-- END CreateAccountInvoices command --');

        return true;
    }

    public function createMonthlyChargeLineItem(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createMonthlyChargeLineItem -- Account # ' . $invoice->account_id);

        $item = new InvoiceItem();

        $item->account_id         = $invoice->account_id;
        $item->account_invoice_id = $invoice->id;

        $account_plan = $invoice->account->plan;

        $item->title           = $account_plan->name;
        $item->description     = 'Regular monthly charge for Lightpost.';
        $item->quantity        = 1;
        $item->type            = 'monthly_plan';
        $item->is_taxable      = true;
        $item->amount          = $account_plan->price_per_month;
        $item->amount_subtotal = Money::ofMinor($item->amount, 'USD')->multipliedBy($item->quantity, RoundingMode::DOWN)->getMinorAmount()->toInt();
        $item->amount_total    = $item->amount_subtotal;

        $item->save();

        Log::info('CreateAccountInvoices:createMonthlyChargeLineItem -- Finished for account # ' . $invoice->account_id);
    }

    public function createOnlineGivingLineItem(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createOnlineGivingLineItem -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.online_giving')) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'Online Giving',
                'quantity'           => 1,
                'type'               => 'feature',
                'is_taxable'         => true,
                'amount'             => $invoice->account->plan?->monthly_price_online_giving, // CENTS
                'amount_subtotal'    => $invoice->account->plan?->monthly_price_online_giving, // CENTS
                'amount_total'       => $invoice->account->plan?->monthly_price_online_giving, // CENTS
            ]);
        }

        Log::info('CreateAccountInvoices:createOnlineGivingLineItem -- Finished for account # ' . $invoice->account_id);
    }

    public function createPodcastsLineItem(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createPodcastsLineItem -- Account # ' . $invoice->account_id);

        if ($invoice->account->hasFeature('feature.podcasts')) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'Podcasts',
                'quantity'           => 1,
                'type'               => 'feature',
                'is_taxable'         => true,
                'amount'             => $invoice->account->plan?->monthly_price_podcasts, // CENTS
                'amount_subtotal'    => $invoice->account->plan?->monthly_price_podcasts, // CENTS
                'amount_total'       => $invoice->account->plan?->monthly_price_podcasts, // CENTS
            ]);
        }

        // @TODO: Bill for extra downloads

        Log::info('CreateAccountInvoices:createPodcastsLineItem -- Finished for account # ' . $invoice->account_id);
    }

    public function createConsumablesLineItems(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createConsumablesLineItems -- Account # ' . $invoice->account_id);

        $last_month   = Carbon::now()->subMonth();
        $account_plan = $invoice->account->plan;

        // MESSAGE HANDLERS //
        $this->createMessageHandlersLineItems($invoice);

        // EMAIL //
        $email_charge = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 1); // 1 == email
                    });
            })
            ->sum('charge');

        $email_count = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 1); // 1 == email
                    });
            })
            ->count();

        $email_charge = round($email_charge);

        if ($email_count > 0) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'Email usage for ' . $last_month->format('F Y'),
                'quantity'           => $email_count,
                'type'               => 'consumable',
                'is_taxable'         => true,
                'amount'             => $account_plan->price_per_email,
                'amount_subtotal'    => Money::ofMinor($email_charge, 'USD')->getMinorAmount()->toInt(),
                'amount_total'       => Money::ofMinor($email_charge, 'USD')->getMinorAmount()->toInt(),
            ]);
        }

        // SMS //
        $sms_charge = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 2); // 2 == sms
                    });
            })
            ->sum('charge');
        $sms_count  = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 2); // 2 == sms
                    });
            })
            ->count();

        $sms_charge = round($sms_charge);

        if ($sms_count > 0) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'SMS usage for ' . $last_month->format('F Y'),
                'quantity'           => $sms_count,
                'type'               => 'consumable',
                'is_taxable'         => true,
                'amount'             => $account_plan->price_per_sms,
                'amount_subtotal'    => Money::ofMinor($sms_charge, 'USD')->getMinorAmount()->toInt(),
                'amount_total'       => Money::ofMinor($sms_charge, 'USD')->getMinorAmount()->toInt(),
            ]);
        }

        // VOICE //
        $voice_charge = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 4); // 4 == voice
                    });
            })
            ->sum('charge');
        $voice_count  = MessageHistory::where('account_id', $invoice->account_id)
            ->whereHas('message', function ($query) use ($last_month) {
                $query->whereMonth('created_at', $last_month->format('n'))
                    ->whereYear('created_at', $last_month->format('Y'))
                    ->whereHas('type', function ($query2) {
                        $query2->where('id', 4); // 4 == voice
                    });
            })
            ->count();

        $voice_charge = round($voice_charge);

        if ($voice_count > 0) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'Voice usage for ' . $last_month->format('F Y'),
                'quantity'           => $voice_count,
                'type'               => 'consumable',
                'is_taxable'         => true,
                'amount'             => $account_plan->price_per_voice,
                'amount_subtotal'    => Money::ofMinor($voice_charge, 'USD')->getMinorAmount()->toInt(),
                'amount_total'       => Money::ofMinor($voice_charge, 'USD')->getMinorAmount()->toInt(),
            ]);
        }

        Log::info('CreateAccountInvoices:createConsumablesLineItems -- Finished for account # ' . $invoice->account_id);
    }

    public function createStorageLineItem(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createStorageLineItem -- Account # ' . $invoice->account_id);

        $account_plan = $invoice->account->plan;

        $sub           = File::select(DB::raw('SUM(`file_size`)'))->where('sermon_id', DB::raw('sermons.id'))->getQuery();
        $storage_usage = Sermon::where('account_id', $invoice->account_id)->selectSub($sub, 'total_file_size')->get();

        $total_storage_used = round($storage_usage->sum('total_file_size') / **********, 2);

        $included_storage = round($account_plan->max_storage / 1000000, 2);

        $overage = $total_storage_used - $included_storage;

        if ($overage > 1) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'Extra storage - ' . $total_storage_used . 'GB / ' . $included_storage . 'GB ',
                'quantity'           => round($overage),
                'type'               => 'storage',
                'is_taxable'         => true,
                'amount'             => $account_plan->monthly_price_per_gb_storage,
                'amount_subtotal'    => Money::ofMinor($account_plan->monthly_price_per_gb_storage, 'USD')->multipliedBy($overage, RoundingMode::DOWN)->getMinorAmount()->toInt(),
                'amount_total'       => Money::ofMinor($account_plan->monthly_price_per_gb_storage, 'USD')->multipliedBy($overage, RoundingMode::DOWN)->getMinorAmount()->toInt(),
            ]);
        }

        Log::info('CreateAccountInvoices:createStorageLineItem -- Finished for account # ' . $invoice->account_id);
    }

    /* NOTES:
    - ~~Only charge for SMS handlers where a `cost` value is provided.  Free handlers should not be charged.~~
    - Nov 2023:
    */
    public function createMessageHandlersLineItems(Invoice $invoice)
    {
        Log::info('CreateAccountInvoices:createMessageHandlersLineItems -- Start for account # ' . $invoice->account_id);

        $sms_handler_price = $invoice->account->plan?->monthly_price_sms_enabled;
        $last_month        = now()->subMonth()->day(1);

        // SMS MESSAGE HANDLERS //
        $sms_handlers_count = MessageHandler::where('account_id', $invoice->account_id)
            ->where('is_active', true)
            //            ->where('cost', '>', 0) // Nov 2023
            ->where('message_type_id', 2) // 2 == SMS
            ->count();

        $sms_handlers_charge = round($sms_handler_price * $sms_handlers_count);

        if ($sms_handlers_count) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'Group message handlers (SMS) - ' . $sms_handlers_count . ' total - for ' . $last_month->format('F Y'),
                'quantity'           => 1,
                'type'               => 'service',
                'is_taxable'         => true,
                'amount'             => $sms_handler_price,
                'amount_subtotal'    => Money::ofMinor($sms_handlers_charge, 'USD')->getMinorAmount()->toInt(),
                'amount_total'       => Money::ofMinor($sms_handlers_charge, 'USD')->getMinorAmount()->toInt(),
            ]);
        }

        // TRASHED MESSAGE HANDLERS //
        // This is only Message Handlers that were deleted last month, since we still incur a cost with our SMS provider.
        $sms_trashed_handlers_count = MessageHandler::where('account_id', $invoice->account_id)
            ->where('deleted_at', '>', $last_month)
            ->where('deleted_at', '<', now()->format('Y-m-d'))
//            ->where('cost', '>', 0) // Nov 2023
            ->onlyTrashed()
            ->where('message_type_id', 2) // 2 == SMS
            ->count();

        $sms_trashed_handlers_charge = round($sms_handler_price * $sms_trashed_handlers_count);

        if ($sms_handlers_count == 0 && $sms_trashed_handlers_count > 0) {
            InvoiceItem::create([
                'account_id'         => $invoice->account_id,
                'account_invoice_id' => $invoice->id,
                'title'              => 'Deleted group message handlers (SMS) - ' . $sms_trashed_handlers_count . ' total - for ' . $last_month->format('F Y'),
                'quantity'           => 1,
                'type'               => 'service',
                'is_taxable'         => true,
                'amount'             => $sms_handler_price,
                'amount_subtotal'    => Money::ofMinor($sms_trashed_handlers_charge, 'USD')->getMinorAmount()->toInt(),
                'amount_total'       => Money::ofMinor($sms_trashed_handlers_charge, 'USD')->getMinorAmount()->toInt(),
            ]);
        }

        Log::info('CreateAccountInvoices:createMessageHandlersLineItems -- Finished for account # ' . $invoice->account_id);
    }
}
