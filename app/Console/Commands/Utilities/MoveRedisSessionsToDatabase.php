<?php

namespace App\Console\Commands\Utilities;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class MoveRedisSessionsToDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'utility:move-redis-sessions-to-database';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // WEB LOGINS
        $keys = Redis::keys('lightpost:*');

        $this->info('Found ' . count($keys) . ' key with "lightpost:"');

        $sessions_count = 0;

        foreach ($keys as $redis_key) {
            // Get this specific key, returns as an array with one item, with the data in the first index.
            $redis_record = Redis::mget($redis_key);
            try {
                // Unserialize the data, then unserialize it again to get the data as an array.
                $data = unserialize($redis_record[0]);
                if (is_string($data)) {

                    $data2 = unserialize($data);

                    foreach ($data2 as $key => $line_item) {
                        // If we're on the line with our login_token => userId, then we want to get the userId.
                        if (is_string($key) && Str::startsWith($key, 'login_web_') && is_int($line_item)) {
                            $user_id = $line_item;
                        }
                    }

                    $sessions_count++;

                    DB::table('sessions')->insert([
                        'id'            => Str::remove('lightpost:', $redis_key),
                        'user_id'       => $user_id,
                        'ip_address'    => null,
                        'user_agent'    => null,
                        'payload'       => base64_encode($data),
                        'last_activity' => now()->timestamp,
                    ]);
                }

            } catch (\Exception $e) {
                Log::error($e, ['redis_key' => $redis_key, 'redis_record' => $redis_record, 'data2' => $data2]);
            }
        }

        $this->info('Processed ' . $sessions_count . ' session records.');

        $this->info('Completed.');
    }
}
