<?php

namespace App\Console\Commands\DataImport\UniversityChurchAL;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'university-church-al:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Individual Id',
        2  => 'Label',
        3  => 'Title',
        4  => 'First Name',
        5  => 'Preferred Name',
        6  => 'Last Name',
        7  => 'Suffix',
        8  => 'Primary Email',
        9  => 'Date Marked Deceased',
        10 => 'Primary City',
        11 => 'Primary Region',
        12 => 'Primary PostalCode',
        13 => 'Primary Country',
        14 => 'Primary Is Mailing',
        15 => 'Home Phone Extension',
        16 => 'Home Phone Privacy',
        17 => 'Mobile Phone Number',
        18 => 'Mobile Phone Extension',
        19 => 'Mobile Phone Privacy',
        20 => 'Work Phone Number',
        21 => 'Work Phone Extension',
        22 => 'Work Phone Privacy',
        23 => 'Other Phone Number',
        24 => 'Other Phone Extension',
        25 => 'Other Phone Privacy',
        26 => 'Group 1',
        27 => 'Group 2',
        28 => 'Group 3',
        29 => 'Group 4',
        30 => 'Group 5',
        31 => 'Group 6',
        32 => 'Group 7',
        33 => 'Group 8',
        34 => 'Group 9',
        35 => 'Group 10',
        36 => 'Group 11',
        37 => 'Group 12',
        38 => 'Group 13',
        39 => 'Group 14',
        40 => 'Family Id',
        41 => 'Primary Phone Number',
        42 => 'Primary Phone Number Extension',
        43 => 'Mailing Address 1',
        44 => 'Mailing Address 2',
        45 => 'Mailing City',
        46 => 'Mailing Region',
        47 => 'Mailing Postal Code',
        48 => 'Mailing Country',
        49 => 'Individual Status',
        50 => 'Date Last Logged In',
        51 => 'Profile',
        52 => 'Primary Address1',
        53 => 'Primary Address2',
        54 => 'Primary Address Privacy',
        55 => 'Address Line 1 (Primary)',
        56 => 'Address Line 2 (Primary)',
        57 => 'Address City (Primary)',
        58 => 'Address Country (Primary)',
        59 => 'Address Postal Code (Primary)',
        60 => 'Address State (Primary)',
        61 => 'Primary Email Address',
        62 => 'Primary Email Address Privacy',
        63 => 'Alternate Email Address',
        64 => 'Alternate Email Address Privacy',
        65 => 'Family Name',
        66 => 'Middle Name',
        67 => 'Last Name',
        68 => 'First Name',
        69 => 'Home Phone Number',
        70 => 'Preferred Name',
        71 => 'Date of Birth',
        72 => 'Anniversary',
        73 => 'Allergies',
        74 => 'Gender',
        75 => 'Family Position',
        76 => 'Marital Status',
        77 => 'Member Status',
        78 => 'Address Line 1 (Alternate)',
        79 => 'Address Line 1 (Mailing)',
        80 => 'Address Line 2 (Alternate)',
        81 => 'Address Line 2 (Mailing)',
        82 => 'Address City (Alternate)',
        83 => 'Address City (Mailing)',
        84 => 'Address Country (Alternate)',
        85 => 'Address Country (Mailing)',
        86 => 'Address Postal Code (Alternate)',
        87 => 'Address Postal Code (Mailing)',
        88 => 'Address State (Alternate)',
        89 => 'Address State (Mailing)',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id        = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id         = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id        = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id       = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;
        $this->former_member_group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => 'Former Members',
            'creator_id' => 1,
        ], [])?->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            $family_role_sort_order = null;
            if ($worksheet->getCell([75, $r])->getValue() == 'Head') {
                $family_role_sort_order = 1;
            } elseif ($worksheet->getCell([75, $r])->getValue() == 'Spouse') {
                $family_role_sort_order = 2;
            } elseif ($worksheet->getCell([75, $r])->getValue() == 'Child') {
                $family_role_sort_order = 3;
            } else {
                $family_role_sort_order = 4;
            }

            // Family ID is 40
            $families[$worksheet->getCell([40, $r])->getValue()][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
                21 => $worksheet->getCell([21, $r])->getValue(),
                22 => $worksheet->getCell([22, $r])->getValue(),
                23 => $worksheet->getCell([23, $r])->getValue(),
                24 => $worksheet->getCell([24, $r])->getValue(),
                25 => $worksheet->getCell([25, $r])->getValue(),
                26 => $worksheet->getCell([26, $r])->getValue(),
                27 => $worksheet->getCell([27, $r])->getValue(),
                28 => $worksheet->getCell([28, $r])->getValue(),
                29 => $worksheet->getCell([29, $r])->getValue(),
                30 => $worksheet->getCell([30, $r])->getValue(),
                31 => $worksheet->getCell([31, $r])->getValue(),
                32 => $worksheet->getCell([32, $r])->getValue(),
                33 => $worksheet->getCell([33, $r])->getValue(),
                34 => $worksheet->getCell([34, $r])->getValue(),
                35 => $worksheet->getCell([35, $r])->getValue(),
                36 => $worksheet->getCell([36, $r])->getValue(),
                37 => $worksheet->getCell([37, $r])->getValue(),
                38 => $worksheet->getCell([38, $r])->getValue(),
                39 => $worksheet->getCell([39, $r])->getValue(),
                40 => $worksheet->getCell([40, $r])->getValue(),
                41 => $worksheet->getCell([41, $r])->getValue(),
                42 => $worksheet->getCell([42, $r])->getValue(),
                43 => $worksheet->getCell([43, $r])->getValue(),
                44 => $worksheet->getCell([44, $r])->getValue(),
                45 => $worksheet->getCell([45, $r])->getValue(),
                46 => $worksheet->getCell([46, $r])->getValue(),
                47 => $worksheet->getCell([47, $r])->getValue(),
                48 => $worksheet->getCell([48, $r])->getValue(),
                49 => $worksheet->getCell([49, $r])->getValue(),
                50 => $worksheet->getCell([50, $r])->getValue(),
                51 => $worksheet->getCell([51, $r])->getValue(),
                52 => $worksheet->getCell([52, $r])->getValue(),
                53 => $worksheet->getCell([53, $r])->getValue(),
                54 => $worksheet->getCell([54, $r])->getValue(),
                55 => $worksheet->getCell([55, $r])->getValue(),
                56 => $worksheet->getCell([56, $r])->getValue(),
                57 => $worksheet->getCell([57, $r])->getValue(),
                58 => $worksheet->getCell([58, $r])->getValue(),
                59 => $worksheet->getCell([59, $r])->getValue(),
                60 => $worksheet->getCell([60, $r])->getValue(),
                61 => $worksheet->getCell([61, $r])->getValue(),
                62 => $worksheet->getCell([62, $r])->getValue(),
                63 => $worksheet->getCell([63, $r])->getValue(),
                64 => $worksheet->getCell([64, $r])->getValue(),
                65 => $worksheet->getCell([65, $r])->getValue(),
                66 => $worksheet->getCell([66, $r])->getValue(),
                67 => $worksheet->getCell([67, $r])->getValue(),
                68 => $worksheet->getCell([68, $r])->getValue(),
                69 => $worksheet->getCell([69, $r])->getValue(),
                70 => $worksheet->getCell([70, $r])->getValue(),
                71 => $worksheet->getCell([71, $r])->getValue(),
                72 => $worksheet->getCell([72, $r])->getValue(),
                73 => $worksheet->getCell([73, $r])->getValue(),
                74 => $worksheet->getCell([74, $r])->getValue(),
                75 => $worksheet->getCell([75, $r])->getValue(),
                76 => $worksheet->getCell([76, $r])->getValue(),
                77 => $worksheet->getCell([77, $r])->getValue(),
                78 => $worksheet->getCell([78, $r])->getValue(),
                79 => $worksheet->getCell([79, $r])->getValue(),
                80 => $worksheet->getCell([80, $r])->getValue(),
                81 => $worksheet->getCell([81, $r])->getValue(),
                82 => $worksheet->getCell([82, $r])->getValue(),
                83 => $worksheet->getCell([83, $r])->getValue(),
                84 => $worksheet->getCell([84, $r])->getValue(),
                85 => $worksheet->getCell([85, $r])->getValue(),
                86 => $worksheet->getCell([86, $r])->getValue(),
                87 => $worksheet->getCell([87, $r])->getValue(),
                88 => $worksheet->getCell([88, $r])->getValue(),
                89 => $worksheet->getCell([89, $r])->getValue(),
                90 => $family_role_sort_order, // Family Role Sort Order
            ];
            $r++;

        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            // Resort our family members by their family role sort order.
            uasort($family_members, function ($a, $b) {
                return ($a[90] < $b[90]) ? -1 : 1;
            });

            $family_id          = null;
            $new_family_members = [];
            $family_has_spouse  = false;

            foreach ($family_members as $member):

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name   = $member[4];
                $user->last_name    = $member[6];
                $user->status       = 'active';
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = $member[1];

                // Only set our preferred first name if it's different than our first name.
                if ($member[4] != $member[5]) {
                    $user->preferred_first_name = $member[5];
                }

                // Double check genders
                if (!$user->gender) {
                    if (Str::contains('Male', $member[74])) {
                        $user->gender = 'male';
                    }
                    if (Str::contains('Female', $member[74])) {
                        $user->gender = 'female';
                    }
                }

                // Default to single.
                $user->marital_status = 'single';

                // Anniversary
                if ($member[72] > '') {
                    $temp_date = $this->getDateArray($member[72]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Birthdate
                if ($member[71] > '') {
                    $temp_date = $this->getDateArray($member[71]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                // -----------------------------------------------------

                // Relationship -- Possible Values:  Spouse, Head of Household, Daughter, Son, Grandson, Granddaughter
                if ($member[75] == 'Head') {
                    $user->family_id   = $user->id;
                    $user->family_role = 'head';

                    // Set our global family_id for this round.
                    $family_id = $user->id;
                } elseif ($member[75] == 'Spouse') {
                    $user->family_role = 'spouse';
                    $family_has_spouse = true;
                } elseif ($member[75] == 'Child') {
                    $user->family_role = 'child';
                }

                if (Str::contains($member[76], 'Married', true)) {
                    $user->marital_status = 'married';
                } elseif (Str::contains($member[76], 'Single', true)) {
                    $user->marital_status = 'single';
                } elseif (Str::contains($member[76], 'Widowed', true)) {
                    $user->marital_status = 'single';
                } elseif (empty($member[76])) {
                    $user->marital_status = 'single';
                }

                // Save our progress
                $user->save();


                // Add this user to any groups listed on their row.
                foreach ([26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39] as $group_index) {
                    if (!empty($member[$group_index])) {
                        $group = Group::firstOrCreate([
                            'account_id' => $this->account_id,
                            'name'       => $member[$group_index],
                            'creator_id' => 1,
                        ], []);

                        if ($group) {
                            $user->groups()->attach($group->id);
                        }
                    }
                }

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if ($member[17] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[17]),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format($member[17]),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);
                }
                // Home
                if ($member[69] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[69]),
                    ], [
                        'user_id'    => $user->id,
                        'family_id'  => $family_id,
                        'number'     => Phone::format($member[69]),
                        'is_primary' => true,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }
                // Work
                if ($member[20] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[20]),
                    ], [
                        'user_id'    => $user->id,
                        'family_id'  => $family_id,
                        'number'     => Phone::format($member[20]),
                        'extension'  => $member[21],
                        'is_primary' => false,
                        'is_family'  => false,
                        'is_hidden'  => true,
                        'type'       => 'work',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                if ($member[8] > '') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[8]),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($member[8]),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($member[55] > '') {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[55],
                        'city'      => $member[57],
                        'family_id' => $family_id,
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => $member[55] ?: '',
                        'address2'  => $member[56] ?: '',
                        'city'      => $member[57] ?: '',
                        'state'     => $member[60] ?: '',
                        'zip'       => $member[59] ?: '',
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }
                // Mailing
                if ($member[79] > '') {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[79],
                        'city'      => $member[83],
                        'family_id' => $family_id,
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => $member[79] ?: '',
                        'address2'  => $member[81] ?: '',
                        'city'      => $member[83] ?: '',
                        'state'     => $member[89] ?: '',
                        'zip'       => $member[87] ?: '',
                        'country'   => 'US',
                        'is_family' => true,
                        'is_hidden' => true,
                    ]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Everyone is a NOT member in this import
                // Members
                if (empty($member[77])
                    || Str::contains($member[77], 'Member', true)
                    || Str::contains($member[77], 'Child of Member', true)
                    || Str::contains($member[77], 'Child of Member (Child)', true)
                    || Str::contains($member[77], 'Attending Only', true)
                    || Str::contains($member[77], 'College Student', true)
                ) {
                    $user->groups()->attach($this->member_group_id);
                    $user->roles()->attach($this->member_role_id);
                } // Former Members
                elseif (Str::contains($member[77], 'Changed congregation', true)
                    || Str::contains($member[77], 'Former Member', true)
                    || Str::contains($member[77], 'Moved', true)
                ) {
                    $user->groups()->attach($this->former_member_group_id);
                }


                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $new_family_members[] = $user;

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

            // To figure out:
            // 1. Is the head of house single or married?
            // 2. Who is the head of house?
            // 3. Family address and phone based on head of household.
            foreach ($new_family_members as $temp_user) {
                $temp_user->family_id = $family_id;

                if (($temp_user->family_role == 'head' || ($temp_user->family_role == 'spouse') && $family_has_spouse)) {
                    $temp_user->marital_status = 'married';
                }

                $temp_user->save();
            }

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 23;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year > 23 ? intval('19' . $year) : intval('20' . $year),
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
