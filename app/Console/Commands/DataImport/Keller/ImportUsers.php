<?php

namespace App\Console\Commands\DataImport\Keller;

use App\Accounts\ChurchOffice;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-keller {account_id} {sqlite_file_location}';

    protected $description = 'Import users from the Keller SQLite database created from their CSV export files.';

    protected $account_id;
    protected $sqlite_file_location;

    protected $member_group_id = null;
    protected $elder_group_id  = null;
    protected $deacon_group_id = null;
    protected $member_role_id  = null;

    protected $columns = [
        1   => 'FamilyNumber',
        2   => 'IndividualNumber',
        3   => 'FamilyLabelName',
        4   => 'IndividualLabelName',
        5   => 'FamilyLabelWithChildren',
        6   => 'FamilySalutation',
        7   => 'IndividualSalutation',
        8   => 'ContribLabelName',
        9   => 'ContribSalutation',
        10  => 'LastNameFirst',
        11  => 'CustomLabelName',
        12  => 'LastName',
        13  => 'FirstName',
        14  => 'MiddleName',
        15  => 'GoesByName',
        16  => 'Title',
        17  => 'Suffix',
        18  => 'AddressType',
        19  => 'IndividualAddress',
        20  => 'Company',
        21  => 'Address1',
        22  => 'Address2',
        23  => 'City',
        24  => 'State',
        25  => 'ZIPCode',
        26  => 'ZIP5',
        27  => 'ZIP4',
        28  => 'CarrierCode',
        29  => 'BarCode',
        30  => 'Country',
        31  => 'CityStateZIP-Formatted',
        32  => 'DeliveryPoint',
        33  => 'LOT',
        34  => 'AD',
        35  => 'DPSort',
        36  => 'GeographicZone',
        37  => 'SubZone',
        38  => 'MapPage',
        39  => 'MapX',
        40  => 'MapY',
        41  => 'Latitude',
        42  => 'Longitude',
        43  => 'HomePhone',
        44  => 'HomePhoneUnlisted',
        45  => 'Gender',
        46  => 'FamilyPosition',
        47  => 'MaritalStatus',
        48  => 'MemberID',
        49  => 'MemberStatus',
        50  => 'DateOfBirth',
        51  => 'DOBMonth',
        52  => 'DOBDay',
        53  => 'DOBYear',
        54  => 'Age',
        55  => 'DateJoined',
        56  => 'JoinedHow',
        57  => 'EntryDate',
        58  => 'DateLastChanged',
        59  => 'EnvelopeNumber',
        60  => 'RecordType',
        61  => 'ContribRecordType',
        62  => 'ActiveRecord',
        63  => 'ActiveContributor',
        64  => 'ReceiveStatement',
        65  => 'NewsletterFlag',
        66  => 'SortField',
        67  => 'FamilySortField',
        68  => 'PreferredPhone',
        69  => 'PreferredContactMethod',
        70  => 'SSN',
        71  => 'Spouse',
        72  => 'SpouseLastName',
        73  => 'SpouseFirstName',
        74  => 'SpouseGoesByName',
        75  => 'SpouseTitle',
        76  => 'Children',
        77  => 'Parents',
        78  => 'OtherFamilyMembers',
        79  => 'DateLastAttended',
        80  => 'LastInwardContact',
        81  => 'LastOutwardContact',
        82  => 'DateLastContributed',
        83  => 'FamilyLastInwardContact',
        84  => 'FamilyLastOutwardContact',
        85  => 'FamilyReviewDate',
        86  => 'IndividualReviewDate',
        87  => 'ProspectSource',
        88  => 'AssignedTo',
        89  => 'IndividualId',
        90  => 'FamilyPicture',
        91  => 'IndividualPicture',
        92  => 'FamOpenField1',
        93  => 'FamOpenField2',
        94  => 'FamOpenField3',
        95  => 'FamElder',
        96  => 'FamOpenCategory2',
        97  => 'FamOpenCategory3',
        98  => 'IndGuestOf',
        99  => 'IndDateVisited',
        100 => 'IndLetterSent',
        101 => 'IndLetterType',
        102 => 'IndLeadership',
        103 => 'IndMinistryArea1',
        104 => 'IndMinistryArea2',
        105 => 'IndOpenCategory4',
        106 => 'IndCoordinator',
        107 => 'IndCoordAssignment1',
        108 => 'IndCoordAssignment2',
        109 => 'IndReligousBackground',
        110 => 'IndOtherAssignments',
        111 => 'IndBaptized',
        112 => 'IndReasonDeactivated',
        113 => 'IndOccupation',
        114 => 'IndBaptismDate',
        115 => 'IndMembershipClass',
        116 => 'IndAnniversary',
        117 => 'IndOpenDate4',
        118 => 'IndOpenDate5',
        119 => 'IndDateDeactivated',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id           = $this->argument('account_id');
        $this->sqlite_file_location = $this->argument('sqlite_file_location');

        if (!file_exists($this->sqlite_file_location)) {
            $this->error('SQLite file not found');
            return 1;
        }

        $this->info('Opening SQLite file for import...');

        // Add the temp connection
        Config::set("database.connections.dataimportsqlite", [
            'driver'   => 'sqlite',
            'database' => $this->sqlite_file_location,
            'prefix'   => '',
        ]);

        // Use the connection
        $sqlite = DB::connection('dataimportsqlite');

        // Get our family units
        $families = $sqlite->table('users')->groupBy('FamilyNumber')->get();

        // Progress bar creation
        $this->info('Found ' . $families->count() . ' families to import...');
        $bar = $this->output->createProgressBar($families->count());

        // Wrap everything in a transaction
        DB::beginTransaction();

        $users = $sqlite->table('users')->get();

        $emails_added = 0;

        foreach ($users as $member) {
            // Users who have duplicate entries and we should not look for an email.
            $result = $sqlite->select(
                "SELECT FamilyNumber, IndividualNumber, FirstName, LastName, COUNT(*) as count
                FROM users
                GROUP BY FirstName, LastName
                HAVING COUNT(*) > 1
                ORDER BY LastName, FirstName;"
            );

            $duplicate_user = collect($result)->where('FamilyNumber', $member->FamilyNumber)->where('IndividualNumber', $member->IndividualNumber)->first();

            if ($duplicate_user) {
                continue;
                $this->info('Duplicate user found: ' . $duplicate_user->FirstName . ' ' . $duplicate_user->LastName);
            }

            // Find our user in Lightpost
            $lightpost_user = User::where('account_id', $this->account_id)
                ->where('first_name', 'like', $member->FirstName)
                ->where('last_name', 'like', $member->LastName)
                ->first();

            // Lightpost: If we found our user and they have no email in Lightpost.
            if ($lightpost_user && $lightpost_user->emails()->count() > 0) {
                $email = null;
                $email = $sqlite->table('emails')
                    ->where('FamilyNumber', $member->FamilyNumber)
                    ->where('IndividualNumber', $member->IndividualNumber)
                    ->where('Description', 'LIKE', 'Personal%')
                    ->first();
                if (!$email) {
                    $email = $sqlite->table('emails')
                        ->where('FamilyNumber', $member->FamilyNumber)
                        ->where('IndividualNumber', $member->IndividualNumber)
                        ->where('Description', 'LIKE', 'Prefer%')
                        ->first();
                }
                if ($email && $email->EmailAddr) {
                    $this->info('Found and added email: ' . $lightpost_user->first_name . ' ' . $lightpost_user->last_name . ' :: ' . $email->EmailAddr);
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email->EmailAddr),
                    ], [
                        'user_id'               => $lightpost_user->id,
                        'email'                 => strtolower($email->EmailAddr),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => $email->EmailUnlisted == 1 ? true : false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);

                    if ($email->user_id != $lightpost_user->id) {
                        $this->warn('Email ' . $email->EmailAddr . ' existed already and was assigned to ' . $email->user_id);
                    } else {
                        $emails_added++;
                    }
                }
            }
        }

        $this->info('Added ' . $emails_added . ' emails to Lightpost.');

        exit;

        try {
            $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()?->id;
            $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()?->id;
            $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()?->id;

            if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
                $this->error('Could not find all groups listed.');
                exit;
            }

            $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
            $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
            $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

            if (!$this->member_role_id) {
                $this->error('Could not find all roles listed.');
                exit;
            }

            $bar->start();

            // Go through each family.
            foreach ($families as $family_head):

                $family_id = null;

                $members = $sqlite->table('users')
                    ->where('FamilyNumber', $family_head->FamilyNumber)
                    ->orderBy('IndividualNumber', 'asc')
                    ->get();

                $bar->advance();

//                $this->info('Importing family: ' . $family_head->FamilyNumber . '...');

                foreach ($members as $member):

                    $user = new User();

                    // -----------------------------------------------------

                    $user->account_id = $this->account_id;

                    $user->first_name           = $member->FirstName;
                    $user->last_name            = $member->LastName;
                    $user->preferred_first_name = $member->GoesByName ? $member->GoesByName : null;
                    $user->gender               = $member->Gender == 'Female' ? 'female' : 'male';
                    $user->status               = 'active';
                    $user->is_active            = true;
                    $user->family_role          = strtolower($member->FamilyPosition);
                    $user->timezone             = $this->timezone;
                    $user->public_token         = Str::uuid();

                    $user->job_title = $member->IndOccupation;

                    $user->is_baptized     = $member->IndBaptized == 'Yes' ? now() : null;
                    $user->date_baptism    = $member->IndBaptismDate ? Carbon::parse($member->IndBaptismDate) : null;
                    $user->date_married    = $member->IndAnniversary ? Carbon::parse($member->IndAnniversary) : null;
                    $user->date_departed   = $member->IndDateDeactivated ? Carbon::parse($member->IndDateDeactivated) : null;
                    $user->date_membership = $member->DateJoined ? Carbon::parse($member->DateJoined) : null;
                    $user->birthdate       = $member->DateOfBirth ? Carbon::parse($member->DateOfBirth) : null;

                    if ($member->IndReasonDeactivated == 'Deceased') {
                        $user->date_deceased = $member->IndDateDeactivated ? Carbon::parse($member->IndDateDeactivated) : null;
                    } else {
                        $user->departed_reason = $member->IndReasonDeactivated;
                    }

                    if ($member->MaritalStatus == 'Married') {
                        $user->marital_status = 'married';
                    } elseif ($member->MaritalStatus == 'Divorced') {
                        $user->marital_status = 'divorced';
                    } elseif ($member->MaritalStatus == 'Widow(er)') {
                        $user->marital_status = 'widowed';
                    } elseif ($member->MaritalStatus == 'Separated') {
                        $user->marital_status = 'separated';
                    } elseif ($member->MaritalStatus == 'Single') {
                        $user->marital_status = 'single';
                    } elseif ($member->MaritalStatus == 'Engaged') {
                        $user->marital_status = 'engaged';
                    } elseif ($member->MaritalStatus == 'Unknown') {
                        $user->marital_status = 'unknown';
                    } else {
                        $user->marital_status = 'single';
                    }

                    $user->notes = $member->Parents ? 'Parents: ' . $member->Parents : null;
                    $user->notes = $user->notes . $member->JoinedHow ? PHP_EOL . 'Joined by: ' . $member->JoinedHow : null;

                    $bg_checks = $sqlite->table('background_checks')
                        ->where('IndvID', $member->IndividualId)
                        ->get();
                    if ($bg_checks->count() > 0) {
                        $user->notes = $user->notes . PHP_EOL . PHP_EOL . 'Background Checks: ';
                        foreach ($bg_checks as $bg_check) {
                            $user->notes = $user->notes . PHP_EOL . $bg_check->Task . ' - ' . $bg_check->Status . ' - ' . $bg_check->DateCompleted;
                        }
                    }

                    // Save our user and get an ID.
                    $user->save();

                    // Get and record our new Family ID for our head of household.
                    if (strtolower($member->FamilyPosition) == 'head' || $members->count() == 1) {
                        $user->family_role = 'head';
                        $family_id         = $user->id;
                    }
                    // If there is only one member, we will assign them to the head of household.
                    if ($members->count() == 1) {
                        $user->family_role = 'head';
                        $family_id         = $user->id;
                    }
                    // If there is no head of household, we will assign the first member to the head of household.
                    $has_head = false;
                    foreach ($members as $member) {
                        if (strtolower($member->FamilyPosition) == 'head') {
                            $has_head = true;
                        }
                    }
                    if (!$family_id && !$has_head) {
                        $user->family_role = 'head';
                        $family_id         = $user->id;
                    }

                    // If we have a head of household, we will assign them to the family.
                    $user->family_id = $family_id;

                    $user->generateUlid();

                    // Save our progress
                    $user->save();

                    $sqlite->table('users')
                        ->where('IndividualNumber', $member->IndividualNumber)
                        ->where('FamilyNumber', $member->FamilyNumber)
                        ->update([
                            'lightpost_id'           => $user->id,
                            'lightpost_public_token' => $user->public_token,
                            'lightpost_ulid'         => $user->ulid,
                        ]);

                    // -----------------------------------------------------
                    // PHONES

                    // Mobile
                    $mobile_phone = null;
                    $mobile_phone = $sqlite->table('phones')
                        ->where('FamilyNumber', $member->FamilyNumber)
                        ->where('IndividualNumber', $member->IndividualNumber)
                        ->where('Description', 'LIKE', 'Cell')
                        ->first();
                    if ($mobile_phone) {
                        $phone = Phone::firstOrCreate([
                            'number' => Phone::format($mobile_phone->Phone),
                        ], [
                            'user_id'          => $user->id,
                            'number'           => Phone::format($mobile_phone->Phone),
                            'messages_opt_out' => false,
                            'is_primary'       => true,
                            'is_family'        => false,
                            'is_hidden'        => false,
                            'type'             => 'mobile',
                        ]);
                    }
                    // Home
                    if ($member->HomePhone) {
                        $phone = Phone::firstOrCreate([
                            'number' => Phone::format($member->HomePhone),
                        ], [
                            'user_id'    => $user->family_id,
                            'number'     => Phone::format($member->HomePhone),
                            'is_primary' => false,
                            'is_family'  => true,
                            'is_hidden'  => false,
                            'type'       => 'home',
                        ]);
                    }

                    // -----------------------------------------------------
                    // EMAILS

                    $email = null;
                    $email = $sqlite->table('emails')
                        ->where('FamilyNumber', $member->FamilyNumber)
                        ->where('IndividualNumber', $member->IndividualNumber)
                        ->where('Description', 'LIKE', 'Personal%')
                        ->first();
                    if (!$email) {
                        $email = $sqlite->table('emails')
                            ->where('FamilyNumber', $member->FamilyNumber)
                            ->where('IndividualNumber', $member->IndividualNumber)
                            ->where('Description', 'LIKE', 'Prefer%')
                            ->first();
                    }
                    if ($email) {
                        $email = Email::firstOrCreate([
                            'email' => strtolower($email->EmailAddr),
                        ], [
                            'user_id'               => $user->id,
                            'email'                 => strtolower($email->EmailAddr),
                            'is_primary'            => true,
                            'is_family'             => false,
                            'is_hidden'             => $email->EmailUnlisted == 1 ? true : false,
                            'type'                  => 'personal',
                            'receives_group_emails' => true,
                        ]);
                    }

                    // -----------------------------------------------------
                    // ADDRESSES

                    if (!empty($member->Address1) && !empty($member->City)) {
                        $address = Address::firstOrCreate([
                            'user_id'  => $user->family_id,
                            'address1' => $member->Address1,
                            'city'     => $member->City,
                        ], [
                            'user_id'    => $member->IndividualAddress == 1 ? $user->id : $user->family_id,
                            'family_id'  => $family_id,
                            'type'       => $member->AddressType == 'Home' ? 'home' : ($member->AddressType == 'Mailing' ? 'mailing' : 'other'),
                            'label'      => $member->AddressType,
                            'address1'   => $member->Address1,
                            'address2'   => $member->Address2,
                            'city'       => $member->City,
                            'state'      => $member->State,
                            'zip'        => $member->ZIPCode,
                            'country'    => $member->Country,
                            'is_mailing' => $member->AddressType == 'Mailing' ? true : false,
                            'is_family'  => true,
                        ]);
                    }

                    // -----------------------------------------------------

                    // Save our progress
                    $user->save();

                    // -----------------------------------------------------

                    if (strtolower($member->RecordType) == 'member' && strtolower($member->ActiveRecord) == 'a') {
                        // Groups
                        $user->groups()->attach($this->member_group_id);
                        $user->roles()->attach($this->member_role_id);
                    } else {
                        $this->attachGroupByName($user, $member->RecordType);
                    }

                    // Various statuses that we want to assign groups to.
                    if ($member->MemberStatus != 'Member' && $member->MemberStatus != 'Deceased') {
                        $status_names = explode(',', $member->MemberStatus);
                        foreach ($status_names as $status_name) {
                            $this->attachGroupByName($user, $status_name);
                        }
                    }

                    // -----------------------------------------------------

                    if ($member->NewsletterFlag == 'F') {
                        $this->attachGroupByName($user, 'Newsletter');
                    }

                    if ($member->FamElder && !$user->date_departed) {
                        $this->attachGroupByName($user, 'Elder Group - ' . $member->FamElder);
                    }

                    if ($member->IndLeadership == 'Minister' && !$user->date_departed) {
                        $this->attachGroupByName($user, 'Ministers');
                        $this->assignChurchOffice($user, 'Ministers', $member->IndMinistryArea1 . ($member->IndMinistryArea2 ? ' / ' . $member->IndMinistryArea2 : null));
                    }
                    if ($member->IndLeadership == 'Elder' && !$user->date_departed) {
                        $this->attachGroupByName($user, 'Elders');
                        $this->assignChurchOffice($user, 'Elders', $member->IndMinistryArea1 . ($member->IndMinistryArea2 ? ' / ' . $member->IndMinistryArea2 : null));
                        $user->roles()->attach($this->elder_role_id);
                    }
                    if ($member->IndLeadership == 'Deacon' && !$user->date_departed) {
                        $this->attachGroupByName($user, 'Deacons');
                        $this->assignChurchOffice($user, 'Deacons', $member->IndMinistryArea1 . ($member->IndMinistryArea2 ? ' / ' . $member->IndMinistryArea2 : null));
                        $user->roles()->attach($this->deacon_role_id);
                    }

                    // -----------------------------------------------------

                    // Save our progress
                    $user->save();

                    // $this->info('Imported user ' . $user->name);
                    // $this->info('---------------------------');

                endforeach;

            endforeach;

            $bar->finish();

            DB::commit();
            $this->info('Import complete.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Import failed: ' . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'STATUS Members') {
                    continue;
                } elseif ($group_string == 'ADMINISTRATION') {
                    $actual_group_name = 'Admin';
                } else {
                    $name_split = explode(' ', $group_string, 2);

                    $actual_group_name = $name_split[1];
                }
            } catch (\Exception $e) {
                dd($groups, $group_string);
            }

            $this->attachGroupByName($new_user_record, $actual_group_name);
        }
    }

    private function assignChurchOffice($user, $office_name, $subtitle = null)
    {
        $office = ChurchOffice::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $office_name,
        ], [
            'plural_name'        => \Illuminate\Support\Str::plural($office_name),
            'short_name'         => \Illuminate\Support\Str::kebab($office_name),
            'url_name'           => \Illuminate\Support\Str::kebab($office_name),
            'is_public'          => true,
            'show_in_leadership' => true,
            'sort_id'            => ChurchOffice::where('account_id', $this->account_id)->max('sort_id') + 1,
        ]);

        if (!$office->ulid) {
            $office->generateUlid();
        }

        // Attach the office to the user with the subtitle if provided
        $user->churchOffices()->syncWithoutDetaching([
            $office->id => [
                'subtitle'   => $subtitle,
                'account_id' => $this->account_id,
            ],
        ]);
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        if (DB::table('user_to_group')->where('user_id', $user->id)->where('user_group_id', $group_id)->exists()) {
            return;
        } else {
            $user->groups()->attach($group_id);
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
