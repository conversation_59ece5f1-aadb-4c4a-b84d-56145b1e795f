<?php

namespace App\Console\Commands\DataImport\Sycamore;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-sycamore {account_id} {excel_file_location} {genderize_api_key?}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        1   => 'FamilyId',
        2   => 'LastName',
        3   => 'AdultFirstNames',
        4   => 'Address',
        5   => 'Address2',
        6   => 'City',
        7   => 'State',
        8   => 'Zip',
        9   => 'FamilyPhone',
        10  => 'FamilyEmail',
        11  => 'ImageId',
        12  => 'AdditionalDetails',
        13  => 'Notes',
        14  => 'AnniversaryDate',
        15  => 'IsAddressPrivate',
        16  => 'DateCreatedFamily',
        17  => 'DateModifiedFamily',
        // -----------------------------------------------------
        18  => 'Adult1PersonId',
        19  => 'Adult1AltLastName',
        20  => 'Adult1PersonType',
        21  => 'Adult1FirstName',
        22  => 'Adult1Birthday',
        23  => 'Adult1BirthYear',
        24  => 'Adult1DisplayBirthdayInPdf',
        25  => 'Adult1EmailAddress',
        26  => 'Adult1AllowEmailMessage',
        27  => 'Adult1MobilePhoneNumber',
        28  => 'Adult1AllowTextMessage',
        29  => 'Adult1IsEmailPrivate',
        30  => 'Adult1IsMobilePhonePrivate',
        31  => 'Adult1IsMemberOfChurch',
        32  => 'Adult1IsPictured',
        33  => 'Adult1SortOrder',
        34  => 'Adult1DateCreated',
        35  => 'Adult1DateModified',
        // -----------------------------------------------------
        36  => 'Adult2PersonId',
        37  => 'Adult2AltLastName',
        38  => 'Adult2PersonType',
        39  => 'Adult2FirstName',
        40  => 'Adult2Birthday',
        41  => 'Adult2BirthYear',
        42  => 'Adult2DisplayBirthdayInPdf',
        43  => 'Adult2EmailAddress',
        44  => 'Adult2AllowEmailMessage',
        45  => 'Adult2MobilePhoneNumber',
        46  => 'Adult2AllowTextMessage',
        47  => 'Adult2IsEmailPrivate',
        48  => 'Adult2IsMobilePhonePrivate',
        49  => 'Adult2IsMemberOfChurch',
        50  => 'Adult2IsPictured',
        51  => 'Adult2SortOrder',
        52  => 'Adult2DateCreated',
        53  => 'Adult2DateModified',
        // -----------------------------------------------------
        54  => 'Child1PersonId',
        55  => 'Child1AltLastName',
        56  => 'Child1PersonType',
        57  => 'Child1FirstName',
        58  => 'Child1Birthday',
        59  => 'Child1BirthYear',
        60  => 'Child1DisplayBirthdayInPdf',
        61  => 'Child1EmailAddress',
        62  => 'Child1AllowEmailMessage',
        63  => 'Child1MobilePhoneNumber',
        64  => 'Child1AllowTextMessage',
        65  => 'Child1IsEmailPrivate',
        66  => 'Child1IsMobilePhonePrivate',
        67  => 'Child1IsMemberOfChurch',
        68  => 'Child1IsPictured',
        69  => 'Child1SortOrder',
        70  => 'Child1DateCreated',
        71  => 'Child1DateModified',
        // -----------------------------------------------------
        72  => 'Child2PersonId',
        73  => 'Child2AltLastName',
        74  => 'Child2PersonType',
        75  => 'Child2FirstName',
        76  => 'Child2Birthday',
        77  => 'Child2BirthYear',
        78  => 'Child2DisplayBirthdayInPdf',
        79  => 'Child2EmailAddress',
        80  => 'Child2AllowEmailMessage',
        81  => 'Child2MobilePhoneNumber',
        82  => 'Child2AllowTextMessage',
        83  => 'Child2IsEmailPrivate',
        84  => 'Child2IsMobilePhonePrivate',
        85  => 'Child2IsMemberOfChurch',
        86  => 'Child2IsPictured',
        87  => 'Child2SortOrder',
        88  => 'Child2DateCreated',
        89  => 'Child2DateModified',
        // -----------------------------------------------------
        90  => 'Child3PersonId',
        91  => 'Child3AltLastName',
        92  => 'Child3PersonType',
        93  => 'Child3FirstName',
        94  => 'Child3Birthday',
        95  => 'Child3BirthYear',
        96  => 'Child3DisplayBirthdayInPdf',
        97  => 'Child3EmailAddress',
        98  => 'Child3AllowEmailMessage',
        99  => 'Child3MobilePhoneNumber',
        100 => 'Child3AllowTextMessage',
        101 => 'Child3IsEmailPrivate',
        102 => 'Child3IsMobilePhonePrivate',
        103 => 'Child3IsMemberOfChurch',
        104 => 'Child3IsPictured',
        105 => 'Child3SortOrder',
        106 => 'Child3DateCreated',
        107 => 'Child3DateModified',
        // -----------------------------------------------------
        108 => 'Child4PersonId',
        109 => 'Child4AltLastName',
        110 => 'Child4PersonType',
        111 => 'Child4FirstName',
        112 => 'Child4Birthday',
        113 => 'Child4BirthYear',
        114 => 'Child4DisplayBirthdayInPdf',
        115 => 'Child4EmailAddress',
        116 => 'Child4AllowEmailMessage',
        117 => 'Child4MobilePhoneNumber',
        118 => 'Child4AllowTextMessage',
        119 => 'Child4IsEmailPrivate',
        120 => 'Child4IsMobilePhonePrivate',
        121 => 'Child4IsMemberOfChurch',
        122 => 'Child4IsPictured',
        123 => 'Child4SortOrder',
        124 => 'Child4DateCreated',
        125 => 'Child4DateModified',
        // -----------------------------------------------------
        126 => 'Child5PersonId',
        127 => 'Child5AltLastName',
        128 => 'Child5PersonType',
        129 => 'Child5FirstName',
        130 => 'Child5Birthday',
        131 => 'Child5BirthYear',
        132 => 'Child5DisplayBirthdayInPdf',
        133 => 'Child5EmailAddress',
        134 => 'Child5AllowEmailMessage',
        135 => 'Child5MobilePhoneNumber',
        136 => 'Child5AllowTextMessage',
        137 => 'Child5IsEmailPrivate',
        138 => 'Child5IsMobilePhonePrivate',
        139 => 'Child5IsMemberOfChurch',
        140 => 'Child5IsPictured',
        141 => 'Child5SortOrder',
        142 => 'Child5DateCreated',
        143 => 'Child5DateModified',
        // -----------------------------------------------------
        144 => 'Child6PersonId',
        145 => 'Child6AltLastName',
        146 => 'Child6PersonType',
        147 => 'Child6FirstName',
        148 => 'Child6Birthday',
        149 => 'Child6BirthYear',
        150 => 'Child6DisplayBirthdayInPdf',
        151 => 'Child6EmailAddress',
        152 => 'Child6AllowEmailMessage',
        153 => 'Child6MobilePhoneNumber',
        154 => 'Child6AllowTextMessage',
        155 => 'Child6IsEmailPrivate',
        156 => 'Child6IsMobilePhonePrivate',
        157 => 'Child6IsMemberOfChurch',
        158 => 'Child6IsPictured',
        159 => 'Child6SortOrder',
        160 => 'Child6DateCreated',
        161 => 'Child6DateModified',
        // -----------------------------------------------------
        162 => 'Child7PersonId',
        163 => 'Child7AltLastName',
        164 => 'Child7PersonType',
        165 => 'Child7FirstName',
        166 => 'Child7Birthday',
        167 => 'Child7BirthYear',
        168 => 'Child7DisplayBirthdayInPdf',
        169 => 'Child7EmailAddress',
        170 => 'Child7AllowEmailMessage',
        171 => 'Child7MobilePhoneNumber',
        172 => 'Child7AllowTextMessage',
        173 => 'Child7IsEmailPrivate',
        174 => 'Child7IsMobilePhonePrivate',
        175 => 'Child7IsMemberOfChurch',
        176 => 'Child7IsPictured',
        177 => 'Child7SortOrder',
        178 => 'Child7DateCreated',
        179 => 'Child7DateModified',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                // $this->info('Adding row to families: ' . $r . '...');
            }

            $family_id = $worksheet->getCell([1, $r])->getValue();

            $user1 = null;
            $user2 = null;
            $user3 = null;
            $user4 = null;
            $user5 = null;
            $user6 = null;
            $user7 = null;
            $user8 = null;
            $user9 = null;

            $user1 = [
                'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                'family_role'     => 'head',
                'public_token'    => $worksheet->getCell([18, $r])->getValue(),
                'first_name'      => $worksheet->getCell([21, $r])->getValue(),
                'last_name'       => $worksheet->getCell([2, $r])->getValue(),
                'date_married'    => $worksheet->getCell([14, $r])->getValue(),
                'gender'          => 'male',
                'email'           => strtolower($worksheet->getCell([25, $r])->getValue()),
                'home_phone'      => $worksheet->getCell([9, $r])->getValue(),
                'cell_phone'      => $worksheet->getCell([27, $r])->getValue(),
                'allow_sms'       => $worksheet->getCell([28, $r])->getValue(),
                'address1'        => $worksheet->getCell([4, $r])->getValue(),
                'address2'        => $worksheet->getCell([5, $r])->getValue(),
                'city'            => $worksheet->getCell([6, $r])->getValue(),
                'state'           => $worksheet->getCell([7, $r])->getValue(),
                'postal_code'     => $worksheet->getCell([8, $r])->getValue(),
                'country'         => 'US',
                'birth_month_day' => $worksheet->getCell([22, $r])->getValue(),
                'birth_year'      => $worksheet->getCell([23, $r])->getValue() ?: 1000,
                'is_baptized'     => $worksheet->getCell([31, $r])->getValue(),
                'marital_status'  => !empty($worksheet->getCell([36, $r])->getValue()) ? 'married' : 'single',
                'notes'           => $worksheet->getCell([13, $r])->getValue(),
            ];

            $families[$family_id][] = $user1;

            if (!empty($worksheet->getCell([36, $r])->getValue())) {
                $user2 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'spouse',
                    'public_token'    => $worksheet->getCell([36, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([39, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([2, $r])->getValue(),
                    'date_married'    => $worksheet->getCell([14, $r])->getValue(),
                    'gender'          => 'female',
                    'email'           => strtolower($worksheet->getCell([43, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([45, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([46, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([40, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([41, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([49, $r])->getValue(),
                    'marital_status'  => 'married',
                ];

                $families[$family_id][] = $user2;
            }

            if (!empty($worksheet->getCell([54, $r])->getValue())) {
                $user3 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([54, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([57, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([55, $r])->getValue() ?: $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => $this->getGender($worksheet->getCell([57, $r])->getValue()),
                    'email'           => strtolower($worksheet->getCell([61, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([63, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([61, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([58, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([59, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([67, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user3;
            }
            if (!empty($worksheet->getCell([72, $r])->getValue())) {
                $user4 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([72, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([75, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([73, $r])->getValue() ?: $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => $this->getGender($worksheet->getCell([75, $r])->getValue()),
                    'email'           => strtolower($worksheet->getCell([79, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([81, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([82, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([76, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([77, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([85, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user4;
            }
            if (!empty($worksheet->getCell([90, $r])->getValue())) {
                $user5 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([90, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([93, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([91, $r])->getValue() ?: $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => $this->getGender($worksheet->getCell([93, $r])->getValue()),
                    'email'           => strtolower($worksheet->getCell([97, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([99, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([100, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([94, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([95, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([103, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user5;
            }
            if (!empty($worksheet->getCell([108, $r])->getValue())) {
                $user6 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([108, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([111, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([109, $r])->getValue() ?: $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => $this->getGender($worksheet->getCell([111, $r])->getValue()),
                    'email'           => strtolower($worksheet->getCell([115, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([117, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([118, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([112, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([113, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([121, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user6;
            }
            if (!empty($worksheet->getCell([126, $r])->getValue())) {
                $user7 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([126, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([129, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([127, $r])->getValue() ?: $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => $this->getGender($worksheet->getCell([129, $r])->getValue()),
                    'email'           => strtolower($worksheet->getCell([133, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([135, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([136, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([130, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([131, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([139, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user7;
            }
            if (!empty($worksheet->getCell([144, $r])->getValue())) {
                $user8 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([144, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([147, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([145, $r])->getValue() ?: $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => $this->getGender($worksheet->getCell([147, $r])->getValue()),
                    'email'           => strtolower($worksheet->getCell([151, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([153, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([154, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([148, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([149, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([157, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user8;
            }
            if (!empty($worksheet->getCell([162, $r])->getValue())) {
                $user9 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([162, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([165, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([163, $r])->getValue() ?: $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => $this->getGender($worksheet->getCell([165, $r])->getValue()),
                    'email'           => strtolower($worksheet->getCell([169, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([171, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([172, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([166, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([167, $r])->getValue() ?: 1000,
                    'is_baptized'     => $worksheet->getCell([175, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user9;
            }

            // We were just echoing our birthdays to try to do a SpecialDaysImport - but found other issues that warrants a re-import.
//            if ($user1) {
//                $this->info($user1['first_name'] . ',' . $user1['last_name'] . ',' . $user1['birth_month_day'] . '/' . $user1['birth_year']);
//            }
//            if ($user2) {
//                $this->info($user2['first_name'] . ',' . $user2['last_name'] . ',' . $user2['birth_month_day'] . '/' . $user2['birth_year']);
//            }
//            if ($user3) {
//                $this->info($user3['first_name'] . ',' . $user3['last_name'] . ',' . $user3['birth_month_day'] . '/' . $user3['birth_year']);
//            }
//            if ($user4) {
//                $this->info($user4['first_name'] . ',' . $user4['last_name'] . ',' . $user4['birth_month_day'] . '/' . $user4['birth_year']);
//            }
//            if ($user5) {
//                $this->info($user5['first_name'] . ',' . $user5['last_name'] . ',' . $user5['birth_month_day'] . '/' . $user5['birth_year']);
//            }
//            if ($user6) {
//                $this->info($user6['first_name'] . ',' . $user6['last_name'] . ',' . $user6['birth_month_day'] . '/' . $user6['birth_year']);
//            }
//            if ($user7) {
//                $this->info($user7['first_name'] . ',' . $user7['last_name'] . ',' . $user7['birth_month_day'] . '/' . $user7['birth_year']);
//            }
//            if ($user8) {
//                $this->info($user8['first_name'] . ',' . $user8['last_name'] . ',' . $user8['birth_month_day'] . '/' . $user8['birth_year']);
//            }
//            if ($user9) {
//                $this->info($user9['first_name'] . ',' . $user9['last_name'] . ',' . $user9['birth_month_day'] . '/' . $user9['birth_year']);
//            }

            $r++;
        }

        // RE-IMPORT

        foreach ($families as $family_name => $family_members):

            $family_id = null;

            foreach ($family_members as $member):

                $this->info('Importing ' . $member['first_name'] . ' ' . $member['last_name'] . '...');

                $user = User::where('public_token', $member['public_token'])
                    ->where('account_id', $this->account_id)
                    ->first();

                if (!$user) {
                    continue;
                }

                // We missed this on Child1 the first time around.
                $user->is_baptized = Arr::get($member, 'is_baptized') == 'TRUE' ? now() : null;

                // We missed this on Child1 the first time around.
                // Mobile
                if (Arr::get($member, 'cell_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'cell_phone'),
                    ], [
                        'user_id'          => $user->id,
                        'number'           => Arr::get($member, 'cell_phone'),
                        'messages_opt_out' => Arr::get($member, 'allow_sms') == 'TRUE' ? true : false,
                        'is_primary'       => true,
                        'is_family'        => false,
                        'is_hidden'        => false,
                        'type'             => 'mobile',
                    ]);
                }

                // We missed this on everyone the first time around.
                // Birthdate Date
                if (Arr::get($member, 'birth_month_day')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'birth_month_day') . '/' . Arr::get($member, 'birth_year'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                $user->save();

                // We missed this on Child1 the first time around.
                $email = null;
                $email = Arr::get($member, 'email');
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }

            endforeach;

        endforeach;

        exit;

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            $family_id = null;

            foreach ($family_members as $member):

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name     = $member['first_name'];
                $user->last_name      = $member['last_name'];
                $user->gender         = strtolower($member['gender']);
                $user->status         = 'active';
                $user->is_active      = true;
                $user->family_role    = Arr::get($member, 'family_role');
                $user->timezone       = $this->timezone;
                $user->public_token   = $member['public_token'];
                $user->notes          = Arr::get($member, 'notes');
                $user->is_baptized    = Arr::get($member, 'is_baptized') == 'TRUE' ? now() : null;
                $user->marital_status = Arr::get($member, 'marital_status');

                // Birthdate Date
                if (Arr::get($member, 'birth_month_day')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'birth_month_day'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create(Arr::get($member, 'birth_year') ?: $temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Anniversary
                if (Arr::get($member, 'date_married')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'date_married'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                if ($user->family_role == 'head') {
                    $family_id = $user->id;
                }

                $user->family_id = $family_id;

                $user->generateUlid();

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if (Arr::get($member, 'cell_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'cell_phone'),
                    ], [
                        'user_id'          => $user->id,
                        'number'           => Arr::get($member, 'cell_phone'),
                        'messages_opt_out' => Arr::get($member, 'allow_sms') == 'TRUE' ? true : false,
                        'is_primary'       => true,
                        'is_family'        => false,
                        'is_hidden'        => false,
                        'type'             => 'mobile',
                    ]);
                }
                // Home
                if (Arr::get($member, 'home_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'home_phone'),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Arr::get($member, 'home_phone'),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                $email = null;
                $email = Arr::get($member, 'email');
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }

                // -----------------------------------------------------
                // ADDRESSES

                if (!empty(Arr::get($member, 'address1')) && !empty(Arr::get($member, 'city'))) {
                    $address = Address::firstOrCreate([
                        'address1' => Arr::get($member, 'address1'),
                        'city'     => Arr::get($member, 'city'),
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => Arr::get($member, 'address1'),
                        'address2'  => Arr::get($member, 'address2'),
                        'city'      => Arr::get($member, 'city'),
                        'state'     => Arr::get($member, 'state'),
                        'zip'       => Arr::get($member, 'postal_code'),
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'STATUS Members') {
                    continue;
                } elseif ($group_string == 'ADMINISTRATION') {
                    $actual_group_name = 'Admin';
                } else {
                    $name_split = explode(' ', $group_string, 2);

                    $actual_group_name = $name_split[1];
                }
            } catch (\Exception $e) {
                dd($groups, $group_string);
            }

            $this->attachGroupByName($new_user_record, $actual_group_name);
        }
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        if (DB::table('user_to_group')->where('user_id', $user->id)->where('user_group_id', $group_id)->exists()) {
            return;
        } else {
            $user->groups()->attach($group_id);
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }

    private function getGender($name)
    {
        return 'male';

        $apiKey = $this->argument('genderize_api_key');
        $url    = "https://api.genderize.io?name=" . urlencode($name) . "&apikey=" . $apiKey;

        $response = file_get_contents($url);
        $data     = json_decode($response, true);

        if (isset($data['gender'])) {
            return $data['gender'];
        } else {
            return 'male';
        }
    }
}
