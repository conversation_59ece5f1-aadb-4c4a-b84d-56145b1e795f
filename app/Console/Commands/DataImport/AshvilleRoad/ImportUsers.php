<?php

namespace App\Console\Commands\DataImport\AshvilleRoad;

use App\Accounts\Account;
use App\Accounts\Grade;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-ashville-road {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $account;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        1  => 'famID',
        2  => 'First Name',
        3  => 'Last Name',
        4  => 'Family Relationship',
        5  => 'Email Address',
        6  => 'Baptized',
        7  => 'Grade',
        8  => 'Gender',
        9  => 'Date of Birth',
        10 => 'Marital Status',
        11 => 'Anniversary',
        12 => 'Home Number',
        13 => 'Mobile Number',
        14 => 'Mailing Address',
        15 => 'Mailing Address Line 2',
        16 => 'Mailing City',
        17 => 'Mailing State',
        18 => 'Mailing Zip Code',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');
        $this->account             = Account::find($this->account_id);

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id || !$this->elder_role_id || !$this->deacon_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        $this->timezone = $this->account->timezone;

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            $user = null;

            $columns = [
                1  => 'famID',
                2  => 'First Name',
                3  => 'Last Name',
                4  => 'Family Relationship',
                5  => 'Email Address',
                6  => 'Baptized',
                7  => 'Grade',
                8  => 'Gender',
                9  => 'Date of Birth',
                10 => 'Marital Status',
                11 => 'Anniversary',
                12 => 'Home Number',
                13 => 'Mobile Number',
                14 => 'Mailing Address',
                15 => 'Mailing Address Line 2',
                16 => 'Mailing City',
                17 => 'Mailing State',
                18 => 'Mailing Zip Code',
            ];

            $user = [
                'user_id'              => Str::uuid(),
                'family_id'            => $worksheet->getCell([1, $r])->getValue(),
                'family_role'          => $worksheet->getCell([4, $r])->getValue(),
                'first_name'           => $worksheet->getCell([2, $r])->getValue(),
                'preferred_first_name' => null,
                'last_name'            => $worksheet->getCell([3, $r])->getValue(),
                'gender'               => strtolower($worksheet->getCell([8, $r])->getValue()),
                'email'                => strtolower($worksheet->getCell([5, $r])->getValue()),
                'home_phone'           => $worksheet->getCell([12, $r])->getValue(),
                'cell_phone'           => $worksheet->getCell([13, $r])->getValue(),
                'work_phone'           => null,
                'address1'             => $worksheet->getCell([14, $r])->getValue(),
                'address2'             => $worksheet->getCell([15, $r])->getValue(),
                'city'                 => $worksheet->getCell([16, $r])->getValue(),
                'state'                => $worksheet->getCell([17, $r])->getValue(),
                'postal_code'          => $worksheet->getCell([18, $r])->getValue(),
                'country'              => 'US',
                'birthday'             => $worksheet->getCell([9, $r])->getValue(),
                'date_baptism'         => null,
                'is_baptized'          => $worksheet->getCell([6, $r])->getValue(),
                'date_anniversary'     => $worksheet->getCell([11, $r])->getValue(),
                'marital_status'       => $worksheet->getCell([10, $r])->getValue(),
                'grade'                => $worksheet->getCell([7, $r])->getValue(),
                'family_sort_id'       => 5,
            ];

            // Determine our family sort_id
            if (empty($worksheet->getCell([4, $r])->getValue())) {
                $user['family_sort_id'] = 1;
                $user['family_role']    = 'head';
            } elseif ($worksheet->getCell([4, $r])->getValue() == 'Primary' || $worksheet->getCell([4, $r])->getValue() == 'Husband') {
                $user['family_sort_id'] = 1;
                $user['family_role']    = 'head';
            } elseif ($worksheet->getCell([4, $r])->getValue() == 'Spouse' || $worksheet->getCell([4, $r])->getValue() == 'Mother') {
                $user['family_sort_id'] = 2;
                $user['family_role']    = 'spouse';
            } elseif ($worksheet->getCell([4, $r])->getValue() == 'Child' || $worksheet->getCell([4, $r])->getValue() == 'Daughter') {
                $user['family_sort_id'] = 3;
                $user['family_role']    = 'child';
            } else {
                $user['family_sort_id'] = 4;
                $user['family_role']    = 'dependent';
            }

            // Add our row to our families array based on a family ID.  If no family ID, create one.
            if (!empty($worksheet->getCell([1, $r])->getValue())) {
                $families[$worksheet->getCell([1, $r])->getValue()][] = $user;
            } else {
                $families[uniqid()][] = $user;
            }

            $r++;
        }

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            $family_id           = null;
            $family_member_index = 0;
            $has_spouse          = false;

            // Resort our family members by their family role sort order.
            // We expect the Head of Household to be first to set family_id values.
            uasort($family_members, function ($a, $b) {
                return ($a['family_sort_id'] < $b['family_sort_id']) ? -1 : 1;
            });

            foreach ($family_members as $temp) {
                if ($temp['family_role'] == 'spouse') {
                    $has_spouse = true;
                }
            }

            // If we already exist, skip this whole family.
            $exists = null;
            $exists = User::where('account_id', $this->account_id)
                ->where('first_name', 'like', $family_members[0]['first_name'])
                ->where('last_name', 'like', $family_members[0]['last_name'])
                ->first();
            if ($exists) {
                continue;
            }

            foreach ($family_members as $member):

                $family_member_index++; // Start at 1

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name   = $member['first_name'];
                $user->last_name    = $member['last_name'];
                $user->gender       = $member['gender'];
                $user->status       = 'active';
                $user->family_role  = Arr::get($member, 'family_role');
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid()->toString();

                // Default to single.
                // If we have a husband and wife, they're married.
//                if ($has_spouse && ($member['family_role'] == 'head' || $member['family_role'] == 'spouse')) {
//                    $user->marital_status = 'married';
//                } else {
//                    $user->marital_status = 'single';
//                }

                $user->marital_status = strtolower($member['marital_status']);

                // Date
                if (Arr::get($member, 'birthday')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'birthday'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Date
                if (Arr::get($member, 'date_anniversary')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'date_anniversary'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                // Set our family_id
                if ($family_member_index == 1) {
                    $family_id       = $user->id;
                    $user->family_id = $family_id;
                } else {
                    $user->family_id = $family_id;
                }

                if ($member['is_baptized'] == 'Yes') {
                    $user->is_baptized = now();
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if (Arr::get($member, 'cell_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Phone::format(Arr::get($member, 'cell_phone')),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format(Arr::get($member, 'cell_phone')),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);
                }
                // Home
                if (Arr::get($member, 'home_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Phone::format(Arr::get($member, 'home_phone')),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format(Arr::get($member, 'home_phone')),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                $email = null;
                $email = Arr::get($member, 'email');
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }

                // -----------------------------------------------------
                // ADDRESSES

                if ($family_member_index == 1 && !empty(Arr::get($member, 'address1')) && !empty(Arr::get($member, 'city'))) {
                    $address = Address::firstOrCreate([
                        'address1' => Arr::get($member, 'address1'),
                        'city'     => Arr::get($member, 'city'),
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => Arr::get($member, 'address1'),
                        'address2'  => Arr::get($member, 'address2'),
                        'city'      => Arr::get($member, 'city'),
                        'state'     => Arr::get($member, 'state'),
                        'zip'       => Arr::get($member, 'postal_code'),
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

//                $this->assignMemberSpecificGroups($user, $member);
                if ($member['grade']) {
                    $this->assignGradeByName($user, $member['grade']);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'Member Directory') {
                    continue;
                }
            } catch (\Exception $e) {
                dd($groups, $group_string, $e);
            }

            $this->attachGroupByName($new_user_record, $group_string);
        }
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function assignGradeByName($user, $grade_name)
    {
        $grade_id = Grade::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $grade_name,
        ], [])?->id;

        $user->user_grade_id = $grade_id;

        $user->save();
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
