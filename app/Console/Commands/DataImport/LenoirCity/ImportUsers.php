<?php

namespace App\Console\Commands\DataImport\LenoirCity;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-lenoir-city {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        1  => 'Last_Name',
        2  => 'First_Name',
        3  => 'Child_Names',
        4  => 'Address',
        5  => 'Address2',
        6  => 'City',
        7  => 'State',
        8  => 'ZIP',
        9  => 'Phone_Numbers',
        10 => 'Emails',
        11 => 'AnniversaryDate',
        12 => 'Additional_Details',
        13 => 'Notes',
        14 => 'FamilyId',
        15 => 'Active',
    ];

    protected $timezone = 'America/New_York';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            // Each row is a whole family, so we need to break it down.

            // Head and spouse
            $head_and_spouse_first_names = explode(' and ', $worksheet->getCell([2, $r])->getValue());
            $head_first_name             = Arr::get($head_and_spouse_first_names, 0);
            $spouse_first_name           = Arr::get($head_and_spouse_first_names, 1);

            $children = [];
            if (!empty($worksheet->getCell([3, $r])->getValue())) {
                $children = explode(', ', $worksheet->getCell([3, $r])->getValue());
            }

            $phones = [];
            if (!empty($worksheet->getCell([9, $r])->getValue())) {
                $phones = explode(', ', $worksheet->getCell([9, $r])->getValue());
            }
            $emails = [];
            if (!empty($worksheet->getCell([10, $r])->getValue())) {
                $emails = explode(', ', $worksheet->getCell([10, $r])->getValue());
            }

            $home_phone = null;
            if (!Str::contains(Arr::get($phones, 0), '(')) {
                $home_phone = trim(Arr::get($phones, 0));
            }

            // Head
            $families[$worksheet->getCell([14, $r])->getValue()][] = [
                'first_name'       => $head_first_name,
                'last_name'        => Str::ucfirst(Str::lower($worksheet->getCell([1, $r])->getValue())),
                'role'             => 'head',
                'home_phone'       => $home_phone ? Phone::format($home_phone) : null,
                'phone'            => Phone::format($this->findValueInArrayByName($phones, $head_first_name)),
                'email'            => $this->findValueInArrayByName($emails, $head_first_name) ?: $this->findFamilyValueInArray($emails),
                'address1'         => $worksheet->getCell([4, $r])->getValue(),
                'address2'         => $worksheet->getCell([5, $r])->getValue(),
                'city'             => $worksheet->getCell([6, $r])->getValue(),
                'state'            => $worksheet->getCell([7, $r])->getValue(),
                'zip'              => $worksheet->getCell([8, $r])->getValue(),
                'anniversary_date' => $worksheet->getCell([11, $r])->getValue(),
                'notes'            => $worksheet->getCell([12, $r])->getValue() . ($worksheet->getCell([12, $r])->getValue() ? ' ' . $worksheet->getCell([13, $r])->getValue() : null),
            ];

            // Spouse
            if ($spouse_first_name) {
                $families[$worksheet->getCell([14, $r])->getValue()][] = [
                    'first_name'       => $spouse_first_name,
                    'last_name'        => Str::ucfirst(Str::lower($worksheet->getCell([1, $r])->getValue())),
                    'role'             => 'spouse',
                    'phone'            => Phone::format($this->findValueInArrayByName($phones, $spouse_first_name)),
                    'email'            => $this->findValueInArrayByName($emails, $spouse_first_name),
                    'anniversary_date' => $worksheet->getCell([11, $r])->getValue(),
                    //                    'notes'            => $worksheet->getCell([12, $r])->getValue(),
                ];
            }

            foreach ($children as $child_name) {
                $families[$worksheet->getCell([14, $r])->getValue()][] = [
                    'first_name' => $child_name,
                    'last_name'  => Str::ucfirst(Str::lower($worksheet->getCell([1, $r])->getValue())),
                    'role'       => 'child',
                    'phone'      => Phone::format($this->findValueInArrayByName($phones, $child_name)),
                    'email'      => $this->findValueInArrayByName($emails, $child_name),
                    //                    'notes'      => $worksheet->getCell([12, $r])->getValue(),
                ];
            }

            $r++;
        }

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            $family_id           = null;
            $family_member_index = 0;

            foreach ($family_members as $member):

                $family_member_index++; // Start at 1

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name   = $member['first_name'];
                $user->last_name    = $member['last_name'];
                $user->status       = 'active';
                $user->family_role  = Arr::get($member, 'role');
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid()->toString();
                $user->notes        = Arr::get($member, 'notes');

                // Double check genders
                if ($member['role'] == 'head' && Arr::get(Arr::get($family_members, 1), 'role') == 'spouse') {
                    $user->gender = 'male';
                }
                if ($member['role'] == 'spouse' && Arr::get(Arr::get($family_members, 0), 'role') == 'head') {
                    $user->gender = 'female';
                }

                // Default to single.
                // If we have a husband and wife, they're married.
                if ($member['role'] != 'child' && (Arr::get(Arr::get($family_members, 0), 'role') == 'head' && Arr::get(Arr::get($family_members, 1), 'role') == 'spouse')) {
                    $user->marital_status = 'married';
                } else {
                    $user->marital_status = 'single';
                }

                // Anniversary Date
                if (Arr::get($member, 'anniversary_date')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'anniversary_date'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                if ($family_member_index == 1) {
                    $family_id       = $user->id;
                    $user->family_id = $family_id;
                } else {
                    $user->family_id = $family_id;
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if (Arr::get($member, 'phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'phone'),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Arr::get($member, 'phone'),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);

                    // If this belongs to someone else, assign it to this user.  Or if this is a home phone, but also a mobile phone, mobile overrides home.
                    if (!$phone->user) {
                        $phone->user_id = $user->id;
                        $phone->save();
                    }
                }
                // Home
                if (Arr::get($member, 'home_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'home_phone'),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Arr::get($member, 'home_phone'),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);

                    // If this belongs to someone else, assign it to this user.  Or if this is a home phone, but also a mobile phone, mobile overrides home.
                    if (!$phone->user) {
                        $phone->user_id = $user->id;
                        $phone->save();
                    }
                }

                // -----------------------------------------------------
                // EMAILS

                $email = null;
                $email = Arr::get($member, 'email');
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);

                    if ($email->user_id != $user->id) {
                        $email->user_id = $user->id;
                        $email->save();
                    }
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($family_member_index == 1 && Arr::get($member, 'address1')) {
                    $address = Address::firstOrCreate([
                        'address1' => Arr::get($member, 'address1'),
                        'city'     => Arr::get($member, 'city'),
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => Arr::get($member, 'address1'),
                        'address2'  => Arr::get($member, 'address2'),
                        'city'      => Arr::get($member, 'city'),
                        'state'     => Arr::get($member, 'state'),
                        'zip'       => Arr::get($member, 'zip'),
                        'country'   => 'US',
                        'is_family' => true,
                    ]);

                    if ($address->user_id != $user->id) {
                        $address->user_id   = $user->id;
                        $address->family_id = $family_id;
                        $address->save();
                    }
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, '(')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)"
        foreach ($array as $value) {
            $parts = explode(' (', $value);

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . ')') {
                return trim(Arr::get($parts, 0), ' .');
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
