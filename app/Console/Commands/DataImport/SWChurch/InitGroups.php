<?php

namespace App\Console\Commands\DataImport\SWChurch;

use App\Users\Group;
use Illuminate\Console\Command;

class InitGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sw-church:init-groups {account_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->account_id = $this->argument('account_id');

        ## Elders
        if (!Group::where('account_id', $this->account_id)->where('url_name', 'elder')->exists()) {
            $role = Group::create([
                'account_id'                 => $this->account_id,
                'creator_id'                 => 1,
                'name'                       => 'Elder',
                'url_name'                   => 'elder',
                'allow_individual_to_toggle' => 0,
                'is_hidden'                  => 0,
                'indicates_membership'       => 1,
            ]);
        }

        ## Deacon
        if (!Group::where('account_id', $this->account_id)->where('url_name', 'deacon')->exists()) {
            $role = Group::create([
                'account_id'                 => $this->account_id,
                'creator_id'                 => 1,
                'name'                       => 'Deacon',
                'url_name'                   => 'deacon',
                'allow_individual_to_toggle' => 0,
                'is_hidden'                  => 0,
                'indicates_membership'       => 1,
            ]);
        }

        ## Member
        if (!Group::where('account_id', $this->account_id)->where('url_name', 'member')->exists()) {
            $role = Group::create([
                'account_id'                 => $this->account_id,
                'creator_id'                 => 1,
                'name'                       => 'Member',
                'url_name'                   => 'member',
                'allow_individual_to_toggle' => 0,
                'is_hidden'                  => 0,
                'indicates_membership'       => 1,
            ]);
        }

        ## Former Member
        if (!Group::where('account_id', $this->account_id)->where('url_name', 'former-member')->exists()) {
            $role = Group::create([
                'account_id'                 => $this->account_id,
                'creator_id'                 => 1,
                'name'                       => 'Former Member',
                'url_name'                   => 'former-member',
                'allow_individual_to_toggle' => 0,
                'is_hidden'                  => 0,
            ]);
        }

        ## Youth
        if (!Group::where('account_id', $this->account_id)->where('url_name', 'youth')->exists()) {
            $role = Group::create([
                'account_id'                 => $this->account_id,
                'creator_id'                 => 1,
                'name'                       => 'Youth',
                'url_name'                   => 'youth',
                'allow_individual_to_toggle' => 0,
                'is_hidden'                  => 0,
            ]);
        }

        ## Visitor
        if (!Group::where('account_id', $this->account_id)->where('url_name', 'visitor')->exists()) {
            $role = Group::create([
                'account_id'                 => $this->account_id,
                'creator_id'                 => 1,
                'name'                       => 'Visitor',
                'url_name'                   => 'visitor',
                'allow_individual_to_toggle' => 0,
                'is_hidden'                  => 0,
                'indicates_visitor'          => 1,
            ]);
        }

        ## Shut-in
        if (!Group::where('account_id', $this->account_id)->where('url_name', 'shut-in')->exists()) {
            $role = Group::create([
                'account_id'                 => $this->account_id,
                'creator_id'                 => 1,
                'name'                       => 'Shut-in',
                'url_name'                   => 'shut-in',
                'allow_individual_to_toggle' => 0,
                'is_hidden'                  => 0,
                'indicates_membership'       => 1,
            ]);
        }
    }
}
