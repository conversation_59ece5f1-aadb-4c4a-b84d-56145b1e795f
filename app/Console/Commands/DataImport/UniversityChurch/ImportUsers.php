<?php

namespace App\Console\Commands\DataImport\UniversityChurch;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'univ-church:import-users {account_id} {json_file_location}';

    protected $description = '';

    protected $account_id;
    protected $json_file_location;

    protected $member_group_id;
    protected $former_member_group_id;
    protected $elder_group_id;
    protected $deacon_group_id;
    protected $visitor_group_id;
    protected $member_role_id;
    protected $elder_role_id;
    protected $deacon_role_id;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id         = $this->argument('account_id');
        $this->json_file_location = $this->argument('json_file_location');

        $this->info('Opening JSON file for import...');

        if (!$this->account_id) {
            $this->error('No account_id given!');
            exit;
        }

        /** Load $inputFileName **/
        $json = json_decode(file_get_contents($this->json_file_location));

        $this->member_group_id        = Group::where('name', 'LIKE', 'Member')->where('account_id', $this->account_id)->first()->id;
        $this->former_member_group_id = Group::where('name', 'LIKE', 'Former Member')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id         = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id        = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id       = Group::where('name', 'LIKE', 'Visitor')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->former_member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id || !$this->elder_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        // -----------------------------------------------------

        foreach ($json as $family_unit) {
            $family_info = $family_unit->family;
            $members     = $family_unit->members;

            $head_array_index = false;
            $family_id        = null;

            if (!$members) {
                $this->info('---------------------------');
                $this->info('---------------------------');
                $this->info('⚠️ NO FAMILY MEMBERS: ' . $family_info->FamilyId . ' - ' . $family_info->FamilyName);
                $this->info('---------------------------');
                $this->info('---------------------------');

                continue;
            }
            // Find our "head of household"
            foreach ($members as $index => $member) {
                if ($member->HomeRole == 'Husband') {
                    $head_array_index = $index;
                }
            }
            // If no Husband, but someone is Single, they're the head of household.
            if ($head_array_index === false) {
                foreach ($members as $index => $member) {
                    if ($member->HomeRole == 'Single') {
                        $head_array_index = $index;
                    }
                }
            }
            // If no Husband or Single, but someone is Wife, they're the head of household.
            if (!$head_array_index === false) {
                foreach ($members as $index => $member) {
                    if ($member->HomeRole == 'Wife') {
                        $head_array_index = $index;
                    }
                }
            }

            // Create our head user first
            if (!User::where('notes', 'OldUserID:' . $members[$head_array_index]->UserId)->exists()) {
                $head_user = $this->createUser($members[$head_array_index], $family_info, null, true);
                $family_id = $head_user->family_id;
            } else {
                $tmp       = User::where('notes', $members[$head_array_index]->UserId)->first();
                $family_id = $tmp->family_id;
            }

            // Create our users
            foreach ($members as $index => $member) {
                // Skip if this is head of household, they've already been created.
                if ($index != $head_array_index) {
                    if (!User::where('notes', 'OldUserID:' . $member->UserId)->exists()) {
                        $user = $this->createUser($member, $family_info, $family_id, false);
                    }
                }
            }

            $this->info('Imported Family ' . $family_info->FamilyId . ' - ' . $family_info->FamilyName);
            $this->info('---------------------------');
        }

        // Remove our notes field.
        foreach (User::where('account_id', $this->account_id)->get() as $user) {
            $user->notes = '';
            $user->save();
        }
    }

    public function createUser($member, $family_data, $family_id = null, $is_head = false)
    {
        $user = new User();

        // -----------------------------------------------------

        $user->account_id = $this->account_id;

        // REMOVE LATER
        $user->notes = 'OldUserID:' . $member->UserId;

        if ($family_id) {
            $user->family_id = $family_id;
        }

        $user->last_name   = $member->LastName;
        $user->middle_name = $member->MiddleName;
        $user->first_name  = $member->FirstName;
        $user->timezone    = 'America/Chicago';

        if ($member->ActiveStatus || !$member->IsDisabled) {
            $user->status    = 'active';
            $user->is_active = true;
        }

        if ($member->IsDisabled && !$member->IsMember) {
            $user->exclude_from_reports = true;
            $user->status               = 'inactive';
            $user->is_active            = false;
        }

        if ($member->Gender == 'Male') {
            $user->gender = 'male';
        }
        if ($member->Gender == 'Female') {
            $user->gender = 'female';
        }

        // Dates
        try {
            $user->birthdate = Carbon::createFromFormat('m/d/y', $member->BirthDate);
        } catch (\Exception $e) {
        }
        if ($member->HomeRole == 'Husband' || $member->HomeRole == 'Wife') {
            try {
                $user->date_married   = Carbon::createFromFormat('m/d/y', $family_data->Anniversary);
                $user->marital_status = 'married';
            } catch (\Exception $e) {
            }
        }

        // SAVE to get a User ID.
        $user->save();

        // ------------------------------

        // If this is our head of household, set that, and set our global family id.
        if ($is_head) {
            $user->family_id   = $user->id;
            $user->family_role = 'head';
            // Set our family_id for this family.
            $family_id = $user->id;
        }

        // Relationship/Role -- Possible Values:  Husband, Wife, Child, Single
        if ($member->HomeRole == 'Wife' && $user->family_role != 'head') {
            $user->family_role = 'spouse';
        } elseif ($member->HomeRole == 'Single' && $user->family_role == 'head') {
            $user->marital_status = 'single';
        } elseif ($member->HomeRole == 'Single' && $user->family_role != 'head') {
            $user->family_role    = 'other';
            $user->marital_status = 'single';
        } elseif ($member->HomeRole == 'Wife' && $user->family_role != 'head') {
            $user->family_role = 'spouse';
        } elseif ($member->HomeRole == 'Child') {
            $user->family_role    = 'child';
            $user->marital_status = 'single';
        }

        // MISC -----------------------

        if ($member->IsBaptized) {
            $user->is_baptized  = now();
            $user->date_baptism = now();
        }

        // GROUPS ---------------------------

        if (!$member->IsMember && $member->IsDisabled) {
            if (Str::contains(strtolower(trim($member->DisabledComment)), 'move')) {
                // Add to Former Member list
                $user->date_departed = now();
                $user->notes         = 'moved membership';
            }
            if (strtolower(trim($member->DisabledComment)) == 'deceased') {
                $user->date_deceased = now();
                $user->status        = 'inactive';
            } else {
                $user->groups()->attach($this->former_member_group_id);
            }
        }

        if ($member->ActiveStatus || !$member->IsDisabled) {
            $user->groups()->attach($this->member_group_id);
            $user->roles()->attach($this->member_role_id);
        }

        $user->save();

        // -------------------------------

        // PHONES, ADDRESSES, ETC

        if ($is_head) {
            if ($family_data->HomeNumber) {
                Phone::create([
                    'user_id'    => $user->id,
                    'family_id'  => $user->family_id,
                    'number'     => Phone::format($family_data->HomeNumber),
                    'is_primary' => false,
                    'is_family'  => true,
                    'is_hidden'  => false,
                    'type'       => 'home',
                ]);
            }

            if ($family_data->Address1) {
                $address = Address::create([
                    'user_id'   => $user->id,
                    'family_id' => $user->family_id,
                    'type'      => 'home',
                    'label'     => null,
                    'address1'  => $family_data->Address1,
                    'address2'  => explode(',', $family_data->{'PO Box (Address2)'})[0], // Line 2 is formatted as "P.O. Box 621, San Marcos, TX 78667", so we strip out the extra.
                    'city'      => $family_data->City,
                    'state'     => $family_data->State,
                    'zip'       => $family_data->ZipCode,
                    'country'   => $family_data->Country,
                    'county'    => $family_data->County,
                    'is_family' => true,
                ]);
            }
        }

        if ($member->FullEmail && $member->FullEmail != 'NULL') {
            Email::create([
                'user_id'               => $user->id,
                'email'                 => $member->FullEmail,
                'is_primary'            => true,
                'is_family'             => false,
                'is_hidden'             => false,
                'type'                  => 'personal',
                'receives_group_emails' => true,
                'allow_messages'        => true,
            ]);
        }

        if ($member->WorkNumber) {
            Phone::create([
                'user_id'    => $user->id,
                'number'     => Phone::format($member->WorkNumber),
                'is_primary' => false,
                'is_family'  => true,
                'is_hidden'  => false,
                'type'       => 'work',
            ]);
        }

        if ($member->CellNumber) {
            Phone::create([
                'user_id'    => $user->id,
                'number'     => Phone::format($member->CellNumber),
                'is_primary' => false,
                'is_family'  => true,
                'is_hidden'  => false,
                'type'       => 'mobile',
            ]);
        }

        $this->info('Imported user ' . $user->name);
        $this->info('---------------------------');

        return $user;
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 2021;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => intval($year),
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
