<?php

namespace App\Console\Commands\DataImport\KatyChurch;

use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Subarea;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportInvolvement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'katy-church:import-involvement';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        foreach (DB::connection('katy_church')->table('involvement_categories')->orderBy('id', 'asc')->get() as $old_cat) {
            $new_cat             = new Category();
            $new_cat->id         = $old_cat->id;
            $new_cat->account_id = 10;
            $new_cat->created_at = $old_cat->date_created;
            $new_cat->name       = $old_cat->name;
            $new_cat->sort_id    = $old_cat->sort_id;

            $new_cat->save();

            foreach (DB::connection('katy_church')->table('involvement_areas')->where('involvement_category_id', $old_cat->id)->orderBy('id', 'asc')->get() as $old_area) {
                $new_area                          = new Area();
                $new_area->id                      = $old_area->id;
                $new_area->involvement_category_id = $new_cat->id;
                $new_area->created_at              = $old_area->date_created;
                $new_area->name                    = $old_area->name;
                $new_area->sort_id                 = $old_area->sort_id;

                $new_area->save();

                foreach (DB::connection('katy_church')->table('involvement_subareas')->where('involvement_area_id', $old_area->id)->orderBy('id', 'asc')->get() as $old_subarea) {
                    $new_subarea                      = new Subarea();
                    $new_subarea->id                  = $old_subarea->id;
                    $new_subarea->involvement_area_id = $new_area->id;
                    $new_subarea->created_at          = $old_subarea->date_created;
                    $new_subarea->name                = $old_subarea->name;
                    $new_subarea->sort_id             = $old_subarea->sort_id;

                    $new_subarea->save();
                }
            }
        }

        foreach (DB::connection('katy_church')->table('involvement_rels')->orderBy('id', 'asc')->get() as $old_rel) {
            $user = User::find($old_rel->user_id);

            $user->involvementCategories()->attach($old_rel->involvement_category_id, [
                'involvement_area_id'    => (!$old_rel->involvement_area_id || $old_rel->involvement_area_id == 0) ? null : $old_rel->involvement_area_id,
                'involvement_subarea_id' => (!$old_rel->involvement_subarea_id || $old_rel->involvement_subarea_id == 0) ? null : $old_rel->involvement_subarea_id,
            ]);
        }

        # Reset our autoincrement value
        DB::select("SELECT setval('involvement_categories_id_seq', (SELECT MAX(id) from \"involvement_categories\"))");
        DB::select("SELECT setval('involvement_areas_id_seq', (SELECT MAX(id) from \"involvement_areas\"))");
        DB::select("SELECT setval('involvement_subareas_id_seq', (SELECT MAX(id) from \"involvement_subareas\"))");


        $this->info('Import completed.');
    }
}
