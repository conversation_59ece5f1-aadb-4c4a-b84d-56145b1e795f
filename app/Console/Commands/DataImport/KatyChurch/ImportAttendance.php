<?php

namespace App\Console\Commands\DataImport\KatyChurch;

use App\Attendance\Attendance;
use App\Attendance\AttendanceType;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'katy-church:import-attendance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->createTypes();

        foreach (User::where('account_id', 10)->get() as $user) {
            foreach (DB::connection('katy_church')->table('user_attendance')->where('user_id', $user->id)->orderBy('id', 'asc')->get() as $old_attendance) {
                if ($old_attendance->date_attendance == '0000-00-00') {
                    continue;
                }

                $type_id = null;
                if ($old_attendance->type == 'class' && $old_attendance->ampm == 'am') {
                    $type_id = 1;
                } elseif ($old_attendance->type == 'worship' && $old_attendance->ampm == 'am') {
                    $type_id = 2;
                } elseif ($old_attendance->type == 'worship' && $old_attendance->ampm == 'pm' && Carbon::parse($old_attendance->date_attendance)->dayOfWeek == 0) {
                    $type_id = 3;
                } elseif ($old_attendance->type == 'class' && $old_attendance->ampm == 'pm' && Carbon::parse($old_attendance->date_attendance)->dayOfWeek == 0) {
                    $type_id = 3;
                } elseif ($old_attendance->type == 'class' && $old_attendance->ampm == 'pm') {
                    $type_id = 4;
                }

                if ($type_id && !Attendance::where('user_id', $user->id)->where('date_attendance', $old_attendance->date_attendance)->where('user_attendance_type_id', $type_id)->exists()) {

                    $new_record                          = new Attendance();
                    $new_record->created_at              = $old_attendance->date_created;
                    $new_record->user_id                 = $user->id;
                    $new_record->date_attendance         = $old_attendance->date_attendance;
                    $new_record->user_attendance_type_id = $type_id;

                    $new_record->save();
                }
            }

            $this->info('Imported for user # ' . $user->id . ' -- ' . $user->name);
        }

        # Reset our autoincrement value
        DB::select("SELECT setval('user_attendance_id_seq', (SELECT MAX(id) from \"user_attendance\"))");

        $this->info('Attendance import completed.');
    }

    public function createTypes()
    {
        if (!AttendanceType::where('name', 'AM Class')->where('account_id', 10)->exists()) {
            AttendanceType::create([
                'account_id' => 10,
                'name'       => 'AM Class',
            ]);
        }
        if (!AttendanceType::where('name', 'AM Worship')->where('account_id', 10)->exists()) {
            AttendanceType::create([
                'account_id' => 10,
                'name'       => 'AM Worship',
            ]);
        }
        if (!AttendanceType::where('name', 'PM Worship')->where('account_id', 10)->exists()) {
            AttendanceType::create([
                'account_id' => 10,
                'name'       => 'PM Worship',
            ]);
        }
        if (!AttendanceType::where('name', 'Wednesday Class')->where('account_id', 10)->exists()) {
            AttendanceType::create([
                'account_id' => 10,
                'name'       => 'Wednesday Class',
            ]);
        }

        # Reset our autoincrement value
        DB::select("SELECT setval('user_attendance_types_id_seq', (SELECT MAX(id) from \"user_attendance_types\"))");

    }
}
