<?php

namespace App\Console\Commands\DataImport\KatyChurch;

use App\BibleClasses\BibleClass;
use App\BibleClasses\BibleClassGroup;
use App\BibleClasses\Services\CreateBibleClassRegistration;
use App\Users\BibleClassTeacher;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportBibleClasses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'katy-church:import-bible-classes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        foreach (DB::connection('katy_church')->table('bible_class_quarters')->orderBy('id', 'asc')->get() as $old_bcq) {

            if (BibleClassGroup::where('id', $old_bcq->id)->exists()) {
                $this->info('Bible Class Group already exists... skipping.');
                continue;
            }

            $group = new BibleClassGroup();

            $this->info('Importing group # ' . $old_bcq->id . ': ' . $old_bcq->year . ' - Quarter ' . $old_bcq->quarter);

            $this->info($old_bcq->created_at);

            $group->id = $old_bcq->id;
//            $group->created_at       = ($old_bcq->created_at && $old_bcq->created_at != '0000-00-00 00:00:00') ?: null;
            $group->account_id       = 10;
            $group->name             = $old_bcq->year . ' - Quarter ' . $old_bcq->quarter;
            $group->weeks            = $old_bcq->weeks;
            $group->start_date       = $old_bcq->sunday_start_date;
            $group->end_date         = $old_bcq->sunday_start_date ? Carbon::parse($old_bcq->sunday_start_date)->addWeeks($old_bcq->weeks) : null;
            $group->is_signup_active = $old_bcq->signup_active ? true : false;

            $group->save();

            foreach (DB::connection('katy_church')->table('bible_classes')->where('year', $old_bcq->year)->where('quarter', $old_bcq->quarter)->orderBy('id', 'asc')->get() as $old_bc) {
                $bible_class = new BibleClass();

                $this->info(' - Importing class: ' . $old_bc->title);

                $type_id = null;
                if ($old_bc->day_of_week == 'sunday') {
                    $type_id = 1;
                } elseif ($old_bc->day_of_week == 'wednesday') {
                    $type_id = 4;
                }

                $bible_class->id                      = $old_bc->id;
                $bible_class->account_id              = 10;
                $bible_class->created_at              = ($old_bc->date_created && $old_bc->date_created != '0000-00-00 00:00:00') ? $old_bc->date_created : null;
                $bible_class->bible_class_group_id    = $group->id;
                $bible_class->user_attendance_type_id = $type_id;
                $bible_class->title                   = $old_bc->title;
                $bible_class->description             = $old_bc->description;
                $bible_class->location_name           = $old_bc->room_number;
                $bible_class->day_of_week             = $old_bc->day_of_week == 'sunday' ? 7 : 3;
                $bible_class->is_new                  = $old_bc->is_new ? true : false;
                $bible_class->enable_signup           = $old_bc->allow_signup ? true : false;

                $bible_class->save();

                foreach (json_decode($old_bc->teacher_ids) as $user_id) {
                    $teacher = new BibleClassTeacher();

                    $this->info(' --- Importing teacher: ' . $user_id);

                    $teacher->account_id     = 10;
                    $teacher->user_id        = $user_id;
                    $teacher->bible_class_id = $bible_class->id;

                    $teacher->save();
                }

                $this->info('Importing registrations...');

                $registrationService = (new CreateBibleClassRegistration($bible_class));

                foreach (DB::connection('katy_church')->table('bible_class_registrations')->where('bible_class_id', $old_bc->id)->orderBy('id', 'asc')->get() as $old_reg) {
                    $registrationService->addUser($old_reg->user_id);
                }

                $registrationService->sync();
            }
        }

        # Reset our autoincrement value
        DB::select("SELECT setval('bible_classes_id_seq', (SELECT MAX(id) from \"bible_classes\"))");
        DB::select("SELECT setval('bible_class_groups_id_seq', (SELECT MAX(id) from \"bible_class_groups\"))");
//        DB::select("SELECT setval('bible_class_teachers_id_seq', (SELECT MAX(id) from \"bible_class_teachers\"))");
//        DB::select("SELECT setval('bible_class_registrations_id_seq', (SELECT MAX(id) from \"bible_class_registrations\"))");

        $this->info('Import complete.');
    }
}
