<?php

namespace App\Console\Commands\DataImport\KatyChurch;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Photo;
use App\Users\Role;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManagerStatic as Image;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'katy-church:import-users';

    protected $description = 'Command description';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        foreach (DB::connection('katy_church')->table('users')->orderBy('id', 'asc')->get() as $user) {

            if (User::where('id', $user->id)->exists()) {
                $this->info('User already exists... skipping.');
                continue;
            }

            try {
                $new_user = new User();

                $new_user->fill([
                    'account_id'               => 10,
                    'date_registered'          => ($user->date_registered == '0000-00-00 00:00:00' || !$user->date_registered) ? null : $user->date_registered,
                    'date_membership'          => ($user->date_membership == '0000-00-00' || !$user->date_membership) ? null : $user->date_membership,
                    'date_baptism'             => ($user->date_baptism == '0000-00-00' || !$user->date_baptism) ? null : $user->date_baptism,
                    'date_deceased'            => ($user->date_departed != '0000-00-00' && $user->date_departed && stristr($user->departed_reason, 'decease') !== false) ? $user->date_departed : null,
//                'date_married'             => $user->date_married,
//                'last_login'               => $user->last_login,
                    'family_id'                => $user->family_id,
                    'family_role'              => $user->family_role,
//                    'church_office'            => (strlen($user->church_office) > ,
                    'first_name'               => $user->first_name,
                    'middle_name'              => $user->middle_name,
                    'last_name'                => $user->last_name,
//                'maiden_name'              => $user->maiden_name,
                    'birthdate'                => (stristr($user->birthdate, '0000-') !== false) ? null : $user->birthdate,
                    'gender'                   => $user->gender == 'm' ? 'male' : 'female',
                    'marital_status'           => $user->marital_status,
                    'user_name'                => $user->user_name,
                    'notes'                    => $user->church_office . ' - ' . $user->notes,
                    'visitation_notes'         => $user->visitation_notes,
//                    'old_password'             => $user->password,
//                'secret_question'          => $user->secret_question,
//                'secret_answer'            => $user->secret_answer,
                    'can_teach'                => $user->can_teach ? Carbon::now() : null,
                    'is_baptized'              => $user->is_baptized ? Carbon::now() : null,
                    'blood_type'               => $user->blood_type,
                    'timezone'                 => $user->timezone,
                    'change_password_on_login' => $user->change_password_on_login ? true : false,
                    'confirmed_email'          => $user->confirmed_email ? Carbon::now() : null,
                    'validation_code'          => $user->validation_code,
                    'school_grade'             => $user->school_grade,
                    'school_attending'         => $user->school_attending,
                    'employer'                 => $user->employer,
                    'job_title'                => $user->job_title,
                    'job_keywords'             => $user->job_keywords,
                    'date_departed'            => ($user->date_departed == '0000-00-00' || !$user->date_departed) ? null : $user->date_departed,
                    'departed_reason'          => $user->departed_reason,
                    'member_by'                => $user->member_by,
                    'exclude_from_reports'     => $user->exclude_from_reports ? true : false,
                    'status'                   => $user->status,
                    'is_active'                => $user->status == 'active' ? true : false,
                ]);
            } catch (\Exception $e) {
                throw new \Exception($e->getMessage() . '--' . $user->date_registered);
            }

            $new_user->id = $user->id;
            $new_user->save();

            if ($user->id !== $new_user->id) {
                throw new \Exception('New user ID does not match old. -- Old: ' . $user->id . ' -- New: ' . $new_user->id);
            }

            $new_user->created_at = ($user->date_created == '0000-00-00 00:00:00' || !$user->date_created) ? null : $user->date_created;

            $this->setupRolesAndGroups($new_user, $user);
            $this->setupEmails($new_user, $user);
            $this->setupPhones($new_user, $user);
            $this->setupAddresses($new_user, $user);
            $this->setupPhotos($new_user, $user);

            $new_user->save();

            $this->info('Imported user #' . $new_user->id . ' - ' . $new_user->name);
        }

        $this->info('User import complete.');

        // Reset our autoincrement value
        DB::select("SELECT setval('users_id_seq', (SELECT MAX(id) from \"users\"))");
        DB::select("SELECT setval('user_photos_id_seq', (SELECT MAX(id) from \"user_photos\"))");
        DB::select("SELECT setval('user_phones_id_seq', (SELECT MAX(id) from \"user_phones\"))");
        DB::select("SELECT setval('user_addresses_id_seq', (SELECT MAX(id) from \"user_addresses\"))");
        DB::select("SELECT setval('user_emails_id_seq', (SELECT MAX(id) from \"user_emails\"))");
    }

    public function setupPhotos($new_user, $user)
    {
        $photos = DB::connection('katy_church')->table('user_photos')->where('user_id', $user->id)->get();

        foreach ($photos as $old_photo) {

            $photo_address = 'https://katychurchofchrist.com/site/media/members/photos/' . $old_photo->file_name;

            $image = Image::make($photo_address);

            // $folder    = Config::get('app.user_image_file_path');
            // $extension = '.' . $image->getClientOriginalExtension();

            $new_image_name = $new_user->id . '---' . Str::random(8) . '-' . $old_photo->raw_name;

            // Original -- saved as a JPG
            if (!Storage::disk('user-images')->put($new_user->account_id . '/' . $new_image_name . '---original.jpg', $image->encode('jpg')->__toString())) {
                throw new \Exception('Could not write file to cloud server.');
            }
            // 1024px
            if ($image->width() > 1024 && !Storage::disk('user-images')->put($new_user->account_id . '/' . $new_image_name . '---1024.jpg', $image->widen(1024)->encode('jpg')->__toString())) {
                throw new \Exception('Could not write file to cloud server.');
            } elseif (!Storage::disk('user-images')->put($new_user->account_id . '/' . $new_image_name . '---1024.jpg', $image->encode('jpg')->__toString())) {
                throw new \Exception('Could not write file to cloud server.');
            }
            // 512px
            if ($image->width() > 512 && !Storage::disk('user-images')->put($new_user->account_id . '/' . $new_image_name . '---512.jpg', $image->widen(512)->encode('jpg')->__toString())) {
                throw new \Exception('Could not write file to cloud server.');
            } elseif (!Storage::disk('user-images')->put($new_user->account_id . '/' . $new_image_name . '---512.jpg', $image->encode('jpg')->__toString())) {
                throw new \Exception('Could not write file to cloud server.');
            }

            $photo = new Photo();

            $photo->fill([
                'user_id'            => $new_user->id,
                'family_id'          => ($old_photo->is_family && $old_photo->is_family > 0) ? $new_user->family_id : null,
                'caption'            => $old_photo->caption != 0 ? $old_photo->caption : null,
                'width'              => $image->width(),
                'height'             => $image->height(),
                'dpi'                => null,
                'file_original_name' => $old_photo->file_name,
                'file_size'          => $image->filesize(),
                'file_folder'        => $new_user->account_id,
                'file_id'            => null,
                'file_name'          => $new_image_name,
                'file_extension'     => 'jpg',
                'file_type'          => 'image/jpeg',
                'file_sha1'          => sha1(file_get_contents($photo_address)),
                'has_original'       => true,
                'has_1024'           => true,
                'has_512'            => true,
                'has_256'            => false,
                'is_primary'         => $old_photo->is_primary ? true : false,
                'is_hidden'          => false,
            ]);

            $photo->save();

            $this->info(' - Imported photo # ' . $photo->id);

            unset($image);
        }
    }

    public function setupEmails($new_user, $user)
    {
        $emails = DB::connection('katy_church')->table('user_emails')->where('user_id', $user->id)->get();

        foreach ($emails as $email) {
            $new_email = Email::create([
                'user_id'        => $new_user->id,
                'family_id'      => $email->family_id,
                'type'           => $email->email_type,
                'email'          => $email->email,
                'is_family'      => optional($email)->is_family ? true : false,
                'is_primary'     => $email->is_primary ? true : false,
                'is_hidden'      => $email->is_hidden ? true : false,
                'allow_messages' => 1,
            ]);

            // Add them to the newsflash group if they were getting it before... but only for members
            if ($email->news_flash && $user->is_member) {
                $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'Newsflash')->first())->id);
            } elseif ($email->news_flash && $user->is_former_member && !$new_user->date_deceased) {
                $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'Newsflash Former%')->first())->id);
            }

            $this->info(' - Imported email # ' . $new_email->id);
        }
    }

    public function setupAddresses($new_user, $user)
    {
        $addresses = DB::connection('katy_church')->table('user_addresses')->where('user_id', $user->id)->get();

        foreach ($addresses as $address) {
            $new_address = Address::create([
                'user_id'   => $new_user->id,
                'family_id' => $address->family_id,
                'type'      => $address->address_type,
                'label'     => $address->title,
                'address1'  => $address->address1,
                'address2'  => $address->address2,
                'city'      => $address->city,
                'state'     => $address->state,
                'zip'       => $address->zip,
                'country'   => $address->country,
                'is_family' => $address->is_family ? true : false,
            ]);

            $this->info(' - Imported address # ' . $new_address->id);
        }
    }

    public function setupPhones($new_user, $user)
    {
        $phones = DB::connection('katy_church')->table('user_phones')->where('user_id', $user->id)->get();

        foreach ($phones as $phone) {
            $new_phone = Phone::create([
                'user_id'         => $new_user->id,
                'family_id'       => ($phone->family_id && $phone->family_id > 0) ? $phone->family_id : null,
                'type'            => $phone->phone_type,
                'mobile_provider' => $phone->mobile_provider,
                'number'          => $phone->number,
                'is_family'       => optional($phone)->is_family ? true : false,
            ]);

            $this->info(' - Imported phone # ' . $new_phone->id);
        }
    }

    public function setupRolesAndGroups($new_user, $user)
    {
        if ($user->is_admin) {
            $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'like', 'admin')->first())->id);
            $new_user->is_super = 1;
//                $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'like', 'admin%')->first())->id);
        }
        if ($user->is_elder) {
            $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'elder')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'elder%')->first())->id);
        }
        if ($user->is_deacon) {
            $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'deacon')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'deacon%')->first())->id);
        }
        if ($user->is_member) {
            $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'member')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'member%')->first())->id);
        }
        if ($user->is_former_member && !$new_user->date_deceased) {
            $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'former-member')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'former%')->first())->id);
        }
        if ($user->is_visitor) {
            $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'visitor')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'visitor%')->first())->id);
        }
        if ($user->is_frequent_visitor) {
            $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'visitor')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'frequent%')->first())->id);
        }
        if ($user->is_shutin) {
//                $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'shut')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'shut%')->first())->id);
        }
        if ($user->is_other) {
//                $new_user->roles()->syncWithoutDetaching(optional(Role::where('key', 'ilike', 'other')->first())->id);
            $new_user->groups()->syncWithoutDetaching(optional(Group::where('name', 'ilike', 'other%')->first())->id);
        }
    }
}
