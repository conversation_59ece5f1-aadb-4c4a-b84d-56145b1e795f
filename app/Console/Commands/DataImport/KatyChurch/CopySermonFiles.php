<?php

namespace App\Console\Commands\DataImport\KatyChurch;

use App\Sermons\File;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CopySermonFiles extends Command
{
    protected $signature = 'katy-church:copy-sermon-files {sermon_file_id_start=1} {--only=0}';

    protected $description = '';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        if ($this->option('only') > 0) {
            $sermon_files = File::where('id', $this->option('only'))->get();
        } else {
            $sermon_files = File::where('id', '>=', $this->argument('sermon_file_id_start'))->orderBy('id', 'ASC')->get();
        }

        $this->info('Found ' . $sermon_files->count() . ' sermon files to copy.');

        foreach ($sermon_files as $sermon_file) {

            $this->info('Copying sermon file # ' . $sermon_file->id);

            $file_address = 'https://katychurchofchrist.com/site/media/sermons/audio/' . rawurlencode($sermon_file->file_original_name);

            $new_file_name = $sermon_file->file_name . '.' . $sermon_file->file_extension;

            $sermon_file_contents = file_get_contents($file_address);

            if (!Storage::disk('sermon-files')->put($sermon_file->sermon->account_id . '/' . $new_file_name, $sermon_file_contents)) {
                throw new \Exception('Could not write file to cloud server.');
                exit;
            }

            $sermon_file->file_size = strlen($sermon_file_contents);
            $sermon_file->file_type = 'audio/mpeg';
            $sermon_file->file_sha1 = sha1($sermon_file_contents);

            $sermon_file->save();
        }

        $this->info('Sermon import complete.');
    }
}
