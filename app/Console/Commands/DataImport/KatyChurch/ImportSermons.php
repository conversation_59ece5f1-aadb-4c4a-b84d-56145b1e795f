<?php

namespace App\Console\Commands\DataImport\KatyChurch;

use App\Sermons\File;
use App\Sermons\Sermon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImportSermons extends Command
{
    protected $signature = 'katy-church:import-sermons';

    protected $description = '';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $sermons = DB::connection('katy_church')->table('sermons')->orderBy('id', 'asc')->get();

        $this->info('Found ' . $sermons->count() . ' sermons to import.');

        foreach ($sermons as $old_sermon) {

            $this->info('Importing sermon # ' . $old_sermon->id);

            if (Sermon::where('id', $old_sermon->id)->exists()) {
                $this->info('Sermon already exists... skipping.');
                continue;
            }

            $new_sermon = (new Sermon())->fill([
                'account_id'      => 10,
                'date_sermon'     => ($old_sermon->date_sermon == '-0001-11-30' || $old_sermon->date_sermon == '0000-00-00') ? '2000-01-01' : $old_sermon->date_sermon,
                'language'        => $old_sermon->language == 'english' ? 'en' : 'es',
                'speaker'         => $old_sermon->speaker,
                'speaker_user_id' => null,
                'title'           => $old_sermon->title,
                'type'            => null,
                'description'     => $old_sermon->details,
                'is_hidden'       => $old_sermon->hidden ? true : false,
            ]);

            $new_sermon->id         = $old_sermon->id;
            $new_sermon->created_at = $old_sermon->date_created;
            $new_sermon->updated_at = $old_sermon->date_modified != '0000-00-00 00:00:00' ? $old_sermon->date_modified : null;

            $new_sermon->save();

            foreach (DB::connection('katy_church')->table('sermon_files')->where('sermon_id', $old_sermon->id)->orderBy('id', 'asc')->get() as $old_sermon_file) {
                if ($old_sermon_file->type == 'audio') {

                    $file_address = 'https://katychurchofchrist.com/site/media/sermons/audio/' . $old_sermon_file->file_name;

                    $this->info('Importing sermon file # ' . $old_sermon_file->id);

                    $extension          = substr($old_sermon_file->file_name, -3, 3);
                    $original_file_name = explode('.', $old_sermon_file->file_name)[0];

                    $new_file_name_without_extension = $new_sermon->account_id . '--' . Str::random(4) . '--' . $original_file_name;
                    $new_file_name                   = $new_file_name_without_extension . '.' . $extension;

                    try {
                        $sermon_file = file_get_contents(str_replace(' ', '%20', $file_address));

                        if (!Storage::disk('sermon-files')->put($new_sermon->account_id . '/' . $new_file_name, $sermon_file)) {
                            throw new \Exception('Could not write file to cloud server.');
                        }

                        $sermon_file = (new File())->fill([
                            'sermon_id'          => $new_sermon->id,
                            'title'              => $old_sermon_file->title,
                            'url_title'          => Str::slug($old_sermon_file->title, '-'),
                            'type'               => $old_sermon_file->type,
                            'storage_service'    => 'do-spaces',
                            'file_original_name' => $old_sermon_file->file_name,
                            'file_size'          => 0,
                            'data_separator'     => '--',
                            'file_folder'        => '10',
                            'file_id'            => null,
                            'file_name'          => $new_file_name_without_extension,
                            'file_extension'     => $extension,
                            'file_type'          => null,
                            'file_sha1'          => null,
                        ]);

                        $sermon_file->id         = $old_sermon_file->id;
                        $sermon_file->created_at = $old_sermon_file->date_created;
                        $sermon_file->updated_at = $old_sermon_file->updated_at != '0000-00-00 00:00:00' ? $old_sermon_file->updated_at : null;

                        $sermon_file->save();
                    } catch (\Exception $e) {
                        $this->info('Could not get file # ' . $old_sermon_file->id . ': ' . $file_address);
                    }
                }
            }
        }

        // Reset our autoincrement value
//        DB::select("SELECT setval('sermons_id_seq', (SELECT MAX(id) from \"sermons\"))");
//        DB::select("SELECT setval('sermon_files_id_seq', (SELECT MAX(id) from \"sermon_files\"))");

        $this->info('Sermon import complete.');
    }
}
