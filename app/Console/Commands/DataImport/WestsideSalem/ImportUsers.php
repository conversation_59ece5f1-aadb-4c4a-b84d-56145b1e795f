<?php

namespace App\Console\Commands\DataImport\WestsideSalem;

use App\Accounts\Account;
use App\Accounts\ChurchOffice;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-westside-salem {account_id} {sqlite_file_location}';

    protected $description = 'Import users from the SQLite database created from their CSV export file.';

    protected $account_id;
    protected $account;
    protected $sqlite_file_location;

    protected $member_group_id = null;
    protected $elder_group_id  = null;
    protected $deacon_group_id = null;
    protected $member_role_id  = null;

    protected $columns = [
        'mail_no',
        'category',
        'lastname',
        'firstname',
        'nameline',
        'salut',
        'address',
        'address2',
        'city',
        'state',
        'zip',
        'country',
        'cr_route',
        'del_pt',
        'special1',
        'special2',
        'special3',
        'date_reg',
        'mailout',
        'pubadd',
        'incl_memb',
        'phone1',
        'phone1_typ',
        'phone1_unl',
        'phone2',
        'phone2_typ',
        'phone2_unl',
        'phone3',
        'phone3_typ',
        'phone3_unl',
        'visitarea',
        'note1',
        'whovisits',
        'updated',
        'matagged',
        'curaddress',
        'alt_add',
        'alt_add2',
        'alt_city',
        'alt_state',
        'alt_zip',
        'alt_cntry',
        'alt_cr',
        'alt_del_pt',
        'alt_phone1',
        'alt_start',
        'alt_stop',
        'pers_no',
        'status',
        'melastname',
        'mefirstname',
        'middlename',
        'suffix',
        'prefname',
        'mesalut',
        'title',
        'm_f',
        'marital',
        'mephn1',
        'mephn1_typ',
        'mephn1_unl',
        'mephn2',
        'mephn2_typ',
        'mephn2_unl',
        'mephn3',
        'mephn3_typ',
        'mephn3_unl',
        'mephn4',
        'mephn4_typ',
        'mephn4_unl',
        'mephn5',
        'mephn5_typ',
        'mephn5_unl',
        'e_mail',
        'adult',
        'user1',
        'user2',
        'user3',
        'user4',
        'user5',
        'user6',
        'user7',
        'user8',
        'user9',
        'user10',
        'birthdate',
        'married_at',
        'baptized_at',
        'membership_date',
        'deceased_at',
        'date6',
        'date7',
        'date8',
        'env_no',
        'occupation',
        'menote2',
        'menote3',
        'menote4',
        'menote5',
        'metagged',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id           = $this->argument('account_id');
        $this->sqlite_file_location = $this->argument('sqlite_file_location');

        if (!file_exists($this->sqlite_file_location)) {
            $this->error('SQLite file not found');
            return 1;
        }

        $this->info('Opening SQLite file for import...');

        // Add the temp connection
        Config::set("database.connections.dataimportsqlite", [
            'driver'   => 'sqlite',
            'database' => $this->sqlite_file_location,
            'prefix'   => '',
        ]);

        $this->account = Account::find($this->account_id);

        // Use the connection
        $sqlite = DB::connection('dataimportsqlite');

        // Get our family units
        $families = $sqlite->table('users')->groupBy('mail_no')->get();

        // Progress bar creation
        $this->info('Found ' . $families->count() . ' families to import...');
        $bar = $this->output->createProgressBar($families->count());

        // Wrap everything in a transaction
        DB::beginTransaction();

        try {
            $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()?->id;
            $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()?->id;
            $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()?->id;

            if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
                $this->error('Could not find all groups listed.');
                exit;
            }

            $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
            $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
            $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

            if (!$this->member_role_id) {
                $this->error('Could not find all roles listed.');
                exit;
            }

            $bar->start();

            // Go through each family.
            foreach ($families as $index => $family_head):

                $family_id                   = null;
                $family_is_member            = false;
                $head_is_deceased            = false;
                $family_members_user_records = [];

                $positionOrder = [
                    'Head of Household' => 1,
                    'Spouse'            => 2,
                    'Child'             => 3,
                ];

                $members = $sqlite->table('users')
                    ->where('mail_no', $family_head->mail_no)
                    ->orderBy('pers_no', 'asc')
                    ->get();

                $bar->advance();

                foreach ($members as $member):

                    if ($member->lightpost_id) {
                        // $this->warn('Skipping user ' . $member->mefirstname . ' ' . $member->melastname . ' because they already have a Lightpost ID. Family ID: ' . $member->mail_no . ' and Person ID: ' . $member->pers_no);
                        // continue;
                    }

                    $family_role = null;

//                    if ($member->MemberStatus == 'Active Member') {
                    $family_is_member = true;
//                    }

                    if ($member->pers_no == 1 && $member->status == 'Deceased') {
                        $family_role      = 'spouse';
                        $head_is_deceased = true;
                    } elseif ($member->pers_no == 1) {
                        $family_role = 'head';
                    } elseif ($member->pers_no == 2 && $member->adult == 'Y' && $head_is_deceased) {
                        $family_role = 'head';
                    } elseif ($member->pers_no == 2 && $member->adult == 'Y') {
                        $family_role = 'spouse';
                    } else {
                        $family_role = 'child';
                    }

//                    if ($member->FamilyRole == 'Head of Household' && $member->MemberStatus == 'Deceased') {
//                        $head_is_deceased = true;
//                    }

                    $user = new User();

                    // -----------------------------------------------------

                    $user->account_id = $this->account_id;

                    $user->first_name           = $member->mefirstname;
                    $user->last_name            = $member->melastname;
                    $user->middle_name          = $member->middlename;
                    $user->preferred_first_name = $member->prefname;
                    $user->gender               = strtolower($member->m_f) == 'f' ? 'female' : 'male';
                    $user->status               = 'active';
                    $user->is_active            = true;
                    $user->family_role          = $family_role;
                    $user->timezone             = $this->account->timezone;
                    $user->public_token         = Str::uuid();
                    $user->notes                = $member->menote5;

                    $user->save();

                    if ($user->family_role == 'head') {
                        $family_id       = $user->id;
                        $user->family_id = $user->id;
                    }

                    $user->job_title = $member->occupation;

                    if ($member->status == 'Deceased') {
                        try {
                            $user->date_deceased = $member->deceased_at != '/  /' ? Carbon::parse($member->deceased_at) : now();
                        } catch (\Exception $e) {
                        }
                    }
                    if ($member->baptized_at != '/  /') {
                        try {
                            $user->date_baptism = $member->baptized_at != '/  /' ? Carbon::parse($member->baptized_at) : now();
                            $user->is_baptized  = now();
                        } catch (\Exception $e) {
                        }
                    }
                    if ($member->married_at != '/  /') {
                        try {
                            $user->date_married = Carbon::parse($member->married_at);
                        } catch (\Exception $e) {
                        }
                    }
                    if ($member->membership_date != '/  /') {
                        try {
                            $user->date_membership = Carbon::parse($member->membership_date);
                        } catch (\Exception $e) {
                        }
                    }
                    if ($member->birthdate != '/  /') {
                        try {
                            $user->birthdate = Carbon::parse($member->birthdate);
                        } catch (\Exception $e) {
                        }
                    }

//                    if ($member->MemberStatus == 'Deceased' || $member->PassedAway) {
//                        $date_deceased = null;
//                        if ($member->PassedAway) {
//                            if (Str::contains($member->PassedAway, '    ')) {
//                                $date_deceased = Str::replaceFirst('    ', '1900', $member->PassedAway);
//                            } else {
//                                $date_deceased = $member->PassedAway;
//                            }
//                        }
//                        try {
//                            $user->date_deceased = $date_deceased ? Carbon::parse($date_deceased) : now();
//                        } catch (\Exception $e) {
//                        }
//                    }

                    if ($member->marital == 'Married') {
                        $user->marital_status = 'married';
                    } elseif ($member->marital == 'Divorced') {
                        $user->marital_status = 'divorced';
                    } elseif ($member->marital == 'Widow/Widower' || $member->marital == 'Widower') {
                        $user->marital_status = 'widowed';
                    } elseif ($member->marital == 'Separated') {
                        $user->marital_status = 'separated';
                    } elseif ($member->marital == 'Single') {
                        $user->marital_status = 'single';
                    } elseif ($member->marital == 'Engaged') {
                        $user->marital_status = 'engaged';
                    } elseif ($member->marital == 'Unknown' || $member->marital == 'Not Known') {
                        $user->marital_status = 'unknown';
                    } else {
                        $user->marital_status = 'unknown';
                    }

                    // Save our user and get an ID.
                    $user->save();

                    // If we have a head of household, we will assign them to the family.
                    $user->family_id = $family_id;

                    $user->generateUlid();

                    // Save our progress
                    $user->save();

                    if (!$family_id) {
                        $this->error('Could not find family ID for user ' . $user->name . 'With PersonId: ' . $member->pers_no);
                    }

                    // Save our Lightpost data back to the SQLite file in case we need it later.
                    $sqlite->table('users')
                        ->where('mail_no', $member->mail_no)
                        ->where('pers_no', $member->pers_no)
                        ->where('e_mail', $member->e_mail)
                        ->update([
                            'lightpost_id'   => $user->id,
                            //                            'lightpost_public_token' => $user->public_token,
                            'lightpost_ulid' => $user->ulid,
                        ]);

                    // -----------------------------------------------------
                    // PHONES

                    // Mobile
                    $mobile_phone = null;
                    if ($member->mephn1 && strlen($member->mephn1) >= 8) {
                        $mobile_phone = $member->mephn1;

                        Phone::firstOrCreate([
                            'number' => Phone::format($mobile_phone),
                        ], [
                            'user_id'          => $user->id,
                            'number'           => Phone::format($mobile_phone),
                            'messages_opt_out' => false,
                            'is_primary'       => true,
                            'is_family'        => false,
                            'is_hidden'        => false,
                            'type'             => 'mobile',
                        ]);
                    }
                    // Home
                    $home_phone = null;
                    if ($member->phone1 && strlen($member->phone1) >= 8 && $member->phone1_typ == 'Home Phone' && $user->family_role == 'head') {
                        $home_phone = $member->phone1;

                        Phone::firstOrCreate([
                            'number' => Phone::format($home_phone),
                        ], [
                            'user_id'    => $user->id,
                            'family_id'  => $user->family_id,
                            'number'     => Phone::format($home_phone),
                            'is_primary' => false,
                            'is_family'  => true,
                            'is_hidden'  => false,
                            'type'       => 'home',
                        ]);
                    }

                    // -----------------------------------------------------
                    // EMAILS

                    $email = null;
                    $email = $member->e_mail;
                    if ($email) {
                        Email::firstOrCreate([
                            'email' => strtolower($email),
                        ], [
                            'user_id'               => $user->id,
                            'email'                 => strtolower($email),
                            'is_primary'            => true,
                            'is_family'             => false,
                            'is_hidden'             => false,
                            'type'                  => 'personal',
                            'receives_group_emails' => true,
                            'allow_messages'        => true,
                        ]);
                    }

                    // -----------------------------------------------------
                    // ADDRESSES

                    if ($family_id && !empty($member->address) && !empty($member->city)) {
                        Address::firstOrCreate([
                            'user_id'  => $family_id,
                            'address1' => $member->address,
                            'city'     => $member->city,
                        ], [
                            'user_id'    => $family_id,
                            'family_id'  => $family_id,
                            'type'       => 'home',
                            'label'      => 'Home',
                            'address1'   => $member->address,
                            'address2'   => $member->address2,
                            'city'       => $member->city,
                            'state'      => $member->state,
                            'zip'        => $member->zip,
                            'country'    => 'US',
                            'is_mailing' => false,
                            'is_family'  => true,
                        ]);
                    }

                    // -----------------------------------------------------

                    // Save our progress
                    $user->save();

                    // -----------------------------------------------------

//                    if ($family_is_member) {
                    // Groups
                    if (!$user->date_deceased) {
                        if ($member->status == 'Member - Active') {
                            $user->groups()->attach($this->member_group_id);
                            $user->roles()->attach($this->member_role_id);

                            if ($member->user2) {
                                $this->attachGroupByName($user, $member->user2);
                            }
                            if ($member->user4) {
                                $this->attachGroupByName($user, $member->user4);
                            }
                            if ($member->special1) {
                                $this->attachGroupByName($user, $member->special1);
                            }
                        }
                    }
//                    } elseif ($member->MemberStatus) {
//                        if ($member->MemberStatus != 'Deceased' && $member->MemberStatus != 'Child' && $member->MemberStatus != 'Regular Attendee') {
//                            $this->attachGroupByName($user, $member->MemberStatus);
//                        }
//                    }

                    // Save our progress
                    $user->save();

                    // INVOLVEMENT

                    $involvement_rows = $sqlite->table('involvement')->get();

                    $category   = $this->getOrCreateInvolvementCategory('Involvement Areas');
                    $found_user = false;
                    foreach ($involvement_rows as $row) {
                        if ($found_user && empty($row->column0)) {
                            break; // Stop
                        }

                        if ($found_user) {
                            $area = $this->getOrCreateInvolvementArea($category, $row->column1);

                            $user->involvementCategories()->attach($category->id, [
                                'involvement_area_id'    => $area->id,
                                'involvement_subarea_id' => null,
                            ]);
                        }

                        if (!$found_user && strtolower($row->column0) == strtolower($user->last_name . ', ' . $user->first_name)) {
                            $found_user = true;
                        }
                    }

                    // $this->info('Imported user ' . $user->name);
                    // $this->info('---------------------------');

                endforeach;

                $members2 = $sqlite->table('users')
                    ->where('mail_no', $family_head->mail_no)
                    ->orderBy('pers_no', 'asc')
                    ->get();

                // Make sure they all have a family ID.
                foreach ($members2 as $member) {
                    $user_temp            = User::where('ulid', $member->lightpost_ulid)->first();
                    $user_temp->family_id = $family_id;
                    $user_temp->save();
                }

                // Make the spouse the head if the husband is deceased, but only if this is a current member.
//                if ($head_is_deceased && $family_is_member) {
//                    $this->info('Head of household is deceased, reversing roles.');
//                    $user_to_swap        = User::find($family_id);
//                    $user_to_swap_spouse = $user_to_swap->spouse;
//
//                    (new ChangeHeadOfHousehold())
//                        ->fromUser($user_to_swap)
//                        ->toUser($user_to_swap_spouse)
//                        ->change();
//
//                    $head_is_deceased = false;
//                }

            endforeach;

            $bar->finish();

            DB::commit();
            $this->info('Import complete.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Import failed: ' . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'STATUS Members') {
                    continue;
                } elseif ($group_string == 'ADMINISTRATION') {
                    $actual_group_name = 'Admin';
                } else {
                    $name_split = explode(' ', $group_string, 2);

                    $actual_group_name = $name_split[1];
                }
            } catch (\Exception $e) {
                dd($groups, $group_string);
            }

            $this->attachGroupByName($new_user_record, $actual_group_name);
        }
    }

    private function assignChurchOffice($user, $office_name, $subtitle = null)
    {
        $office = ChurchOffice::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $office_name,
        ], [
            'plural_name'        => \Illuminate\Support\Str::plural($office_name),
            'short_name'         => \Illuminate\Support\Str::kebab($office_name),
            'url_name'           => \Illuminate\Support\Str::kebab($office_name),
            'is_public'          => true,
            'show_in_leadership' => true,
            'sort_id'            => ChurchOffice::where('account_id', $this->account_id)->max('sort_id') + 1,
        ]);

        if (!$office->ulid) {
            $office->generateUlid();
        }

        // Attach the office to the user with the subtitle if provided
        $user->churchOffices()->syncWithoutDetaching([
            $office->id => [
                'subtitle'   => $subtitle,
                'account_id' => $this->account_id,
            ],
        ]);
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function getOrCreateInvolvementCategory($name)
    {
        return Category::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $name,
        ]);
    }

    private function getOrCreateInvolvementArea($category, $name)
    {
        return Area::firstOrCreate([
            'involvement_category_id' => $category->id,
            'name'                    => $name,
        ]);
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        if (DB::table('user_to_group')->where('user_id', $user->id)->where('user_group_id', $group_id)->exists()) {
            return;
        } else {
            $user->groups()->attach($group_id);
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
