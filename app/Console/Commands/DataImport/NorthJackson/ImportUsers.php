<?php

namespace App\Console\Commands\DataImport\NorthJackson;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'north-jackson:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Family ID',
        2  => 'Last Name',
        3  => 'First Name',
        4  => 'Preferred Name',
        5  => 'Relationship',
        6  => 'Gender',
        7  => 'E-Mail',
        8  => 'Baptized',
        9  => 'Baptized Date',
        10 => 'Birth Date',
        11 => 'Marital Status',
        12 => 'Wedding Date',
        13 => 'Member Status',
        14 => 'Group #:',
        15 => 'Address',
        16 => 'Address Line 2',
        17 => 'City',
        18 => 'Zip Code',
        19 => 'Home Phone',
        20 => 'Cell Phone',
    ];

    protected $timezone = 'America/Chicago';

    protected $member_group_id;
    protected $elder_group_id;
    protected $deacon_group_id;
    protected $visitor_group_id;

    protected $member_role_id;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id  = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id   = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id  = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            // FamilyID
            $family_id = $worksheet->getCell([1, $r])->getValue();

            // Family ID is 40
            $families[$family_id][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
            ];
            $r++;
        }

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_members):

            // If this user already exists, skip it and make a note.
            $existing_user = null;
            $existing_user = User::where('account_id', $this->account_id)
                ->where('first_name', 'like', $family_members[0][3])
                ->where('last_name', 'like', $family_members[0][2])
                ->first();

            if ($existing_user) {
                $this->info('Skip existing family for: ' . $existing_user->name);
                continue;
            }

            $family_id = null;

            foreach ($family_members as $index => $member):

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name           = $member[3];
                $user->last_name            = $member[2];
                $user->preferred_first_name = $member[4] != $member[3] ? $member[4] : null;
                $user->gender               = $member[6] == 'Female' ? 'female' : 'male';
                $user->is_baptized          = $member[8] == 'Yes' ? now() : null;

                $user->status       = 'active';
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid();

                // Default to single.
                if ($member[11] == 'Married') {
                    $user->marital_status = 'married';
                } elseif ($member[11] == 'Single') {
                    $user->marital_status = 'single';
                } elseif ($member[11] == 'Widow') {
                    $user->marital_status = 'widowed';
                } elseif ($member[11] == 'Divorced') {
                    $user->marital_status = 'divorced';
                } else {
                    $user->marital_status = 'single';
                }

                // Baptism Date
                if ($member[9] > '') {
                    $temp_date = $this->getDateArray($member[9]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_baptism = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                        $user->is_baptized  = now();
                    }
                }
                // Birthdate
                if ($member[10] > '') {
                    $temp_date = $this->getDateArray($member[10]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Anniversary
                if ($member[12] > '') {
                    $temp_date = $this->getDateArray($member[12]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                // -----------------------------------------------------

                // Relationship -- Possible Values:  Spouse, Head of Household, Child
                if ($member[5] == 'Head of Household' || $index == 0) {
                    $user->family_role = 'head';
                    // Set our global family_id for this round.
                    $family_id = $user->id;
                } elseif ($member[5] == 'Spouse') {
                    $user->family_role = 'spouse';
                } elseif ($member[5] == 'Child') {
                    $user->family_role = 'child';
                } else {
                    $user->family_role = 'dependent';
                }

                $user->family_id = $family_id;

                // Save our progress
                $user->save();


                // -----------------------------------------------------
                // PHONES

                // Mobile
                if ($member[20] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[20]),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format($member[20]),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);
                }
                // Home
                if ($member[19] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[19]),
                    ], [
                        'user_id'    => $family_id,
                        'family_id'  => $family_id,
                        'number'     => Phone::format($member[19]),
                        'is_primary' => true,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                if ($member[7] > '') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[7]),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($member[7]),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($index == 0 && $member[15] > '') {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[15],
                        'city'      => $member[17],
                        'family_id' => $family_id,
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => $member[15] ?: '',
                        'address2'  => $member[16] ?: '',
                        'city'      => $member[17] ?: '',
                        'state'     => 'TN',
                        'zip'       => $member[18] ?: '',
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Members
                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

                // Additional Group Assignments
                if ($member[14] > 0) {
                    $group = Group::firstOrCreate([
                        'account_id' => $this->account_id,
                        'name'       => 'Group ' . $member[14],
                    ], [
                        'creator_id' => 1,
                    ]);

                    $user->groups()->attach($group->id);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER
        endforeach; // EACH FAMILY

        $this->info('User import complete.');
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2];
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 1900;
            $day   = $date[1];
            $month = $date[0];
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => intval($year),
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
