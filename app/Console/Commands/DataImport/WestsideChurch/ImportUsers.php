<?php

namespace App\Console\Commands\DataImport\WestsideChurch;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ws-church:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Family ID',
        2  => 'Include in Directory',
        3  => 'Directory Name',
        4  => 'Mailing Name',
        5  => 'Emergency Contact',
        6  => 'Emergency Phone',
        7  => 'Emergency Phone Unlisted',
        8  => 'Last Name',
        9  => 'Title',
        10 => 'First Name',
        11 => 'Middle Name',
        12 => 'Preferred Name',
        13 => 'Salutation',
        14 => 'E-Mail',
        15 => 'Email 1 Unlisted',
        16 => 'Gender',
        17 => 'Marital Status',
        18 => 'Wedding Date',
        19 => 'Relationship',
        20 => 'Contact',
        21 => 'Home Phone',
        22 => 'Cell Phone',
        23 => 'Cell Phone Unlisted',
        24 => 'Pager',
        25 => 'Address',
        26 => 'City',
        27 => 'State',
        28 => 'Zip Code',
        29 => 'Country',
        30 => 'Include in Directory',
        31 => 'Member Status',
        32 => 'Baptized',
        33 => 'Baptized Date',
        34 => 'Birth Date',
        35 => 'Date Joined',
        36 => 'How Joined',
        37 => 'Leadership Position',
        38 => 'Employer',
        39 => 'Occupation',
        40 => 'Work Phone',
        41 => 'Work Phone Ext',
        42 => 'Work Phone Unlisted',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        // Get or Create our Groups
        $member_group_id        = Group::firstOrCreate(
            ['account_id' => $this->account_id, 'name' => 'Members', 'creator_id' => 1],
            ['indicates_membership' => 1, 'is_default_member_group' => 1],
        )->id;
        $former_member_group_id = Group::firstOrCreate(
            ['account_id' => $this->account_id, 'name' => 'Former Member', 'creator_id' => 1]
        )->id;
        $elder_group_id         = Group::firstOrCreate(
            ['account_id' => $this->account_id, 'name' => 'Elders', 'creator_id' => 1],
            ['indicates_membership' => 1],
        )->id;
        $deacon_group_id        = Group::firstOrCreate(
            ['account_id' => $this->account_id, 'name' => 'Deacons', 'creator_id' => 1],
            ['indicates_membership' => 1],
        )->id;
        $visitor_group_id       = Group::firstOrCreate(
            ['account_id' => $this->account_id, 'name' => 'Visitors', 'creator_id' => 1],
            ['is_default_visitor_group' => 1],
        )->id;

        if (!$member_group_id || !$former_member_group_id || !$elder_group_id || !$deacon_group_id || !$visitor_group_id) {
            $this->error('Could not find or create all groups listed.');
            exit;
        }

        $elder_role_id  = Role::firstOrCreate(['name' => 'Elder', 'key' => 'elder', 'account_id' => $this->account_id])->id;
        $deacon_role_id = Role::firstOrCreate(['name' => 'Deacon', 'key' => 'deacon', 'account_id' => $this->account_id])->id;
        $member_role_id = Role::firstOrCreate(['name' => 'Member', 'key' => 'member', 'account_id' => $this->account_id])->id;
        $admin_role_id  = Role::firstOrCreate(['name' => 'Admin', 'key' => 'admin', 'account_id' => $this->account_id])->id;

        if (!$member_role_id || !$elder_role_id || !$deacon_role_id) {
            $this->error('Could not find or create all roles listed.');
            exit;
        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

//        $this->info($worksheet->getCellByColumnAndRow(27, 63)->getValue());
//        exit;

        // -----------------------------------------------------

        $last_family_id      = null;
        $last_family_user_id = null;
        $r                   = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Importing row ' . $r . '...');
            }

            $user = new User();

            // -----------------------------------------------------

            $user->account_id = $this->account_id;

            $user->last_name   = $worksheet->getCellByColumnAndRow(8, $r)->getValue();
            $user->middle_name = $worksheet->getCellByColumnAndRow(11, $r)->getValue();
            $user->first_name  = $worksheet->getCellByColumnAndRow(10, $r)->getValue();
            $user->employer    = $worksheet->getCellByColumnAndRow(38, $r)->getValue();
            $user->job_title   = $worksheet->getCellByColumnAndRow(39, $r)->getValue();
//            $user->allergies   = $worksheet->getCellByColumnAndRow(67, $r)->getValue();
            $user->member_by = $worksheet->getCellByColumnAndRow(36, $r)->getValue();
            $user->status    = 'active';
            $user->is_active = true;
            $user->timezone  = 'America/Chicago';

            // Temp store our Family ID from the imported data in the notes field.
            $user->visitation_notes = $worksheet->getCellByColumnAndRow(1, $r)->getValue();

            // Preferred Name
            if ($worksheet->getCellByColumnAndRow(12, $r)->getValue() !== $worksheet->getCellByColumnAndRow(10, $r)->getValue()) {
                $user->preferred_first_name = $worksheet->getCellByColumnAndRow(12, $r)->getValue();
            }

            // NOTES
            $notes = null;
//            if ($worksheet->getCellByColumnAndRow(36, $r)->getValue()) {
//                $notes .= 'How joined: ' . $worksheet->getCellByColumnAndRow(36, $r)->getValue() . '
//';
//            }

//            $user->notes = $notes;

            // Save our user and get an ID.
            $user->save();

            // -----------------------------------------------------

            // Figure out our family ID stuff.
            if ($last_family_id != $worksheet->getCellByColumnAndRow(1, $r)->getValue()) {
                $last_family_id      = $worksheet->getCellByColumnAndRow(1, $r)->getValue();
                $last_family_user_id = $user->id;
            }

            // Tie user to the raw Family ID from the imported data.
            $back_reference[$user->id] = $worksheet->getCellByColumnAndRow(1, $r)->getValue();

            // -----------------------------------------------------

            // Relationship -- Possible Values:  Spouse, Head of Household, Daughter, Son, Grandson, Granddaughter
            if (Str::contains('Head of Household', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                $user->family_id   = $user->id;
                $user->family_role = 'head';

                // See if family members have already been added and backfill them.
                foreach (User::where('account_id', $this->account_id)->where('visitation_notes', $worksheet->getCellByColumnAndRow(1, $r)->getValue())->whereNull('family_id')->get() as $fam_member) {
                    $fam_member->family_id = $user->id;
                    $fam_member->save();
                }
            } else {
                if (Str::contains('Spouse', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this below in the Family ID stuff.
                    $user->family_role = 'spouse';
                    $user->gender      = 'female';
                }
                if (Str::contains('Daughter', $worksheet->getCellByColumnAndRow(19, $r)->getValue()) || Str::contains('Son', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this below in the Family ID stuff.
                    $user->family_role = 'child';
                    if (Str::contains('Daughter', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                        $user->gender = 'female';
                    } else {
                        $user->gender = 'male';
                    }
                }
                if (Str::contains('Niece', $worksheet->getCellByColumnAndRow(19, $r)->getValue()) || Str::contains('Nephew', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this below in the Family ID stuff.
                    $user->family_role = 'child';
                    if (Str::contains('Niece', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                        $user->gender = 'female';
                    } else {
                        $user->gender = 'male';
                    }
                }
                if (Str::contains('Child', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this below in the Family ID stuff.
                    $user->family_role = 'child';
                }
                if (Str::contains('Grandson', $worksheet->getCellByColumnAndRow(19, $r)->getValue()) || Str::contains('Granddaughter', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this below in the Family ID stuff.
                    // $user->family_role = 'child';
                    if (Str::contains('Granddaughter', $worksheet->getCellByColumnAndRow(19, $r)->getValue())) {
                        $user->gender = 'female';
                    } else {
                        $user->gender = 'male';
                    }
                }
                // Set our family ID
                $head_of_house = null;
                $head_of_house = User::where('account_id', $this->account_id)
                    ->where('visitation_notes', $worksheet->getCellByColumnAndRow(1, $r)->getValue())
                    ->whereNotNull('family_id')
                    ->whereRaw('id = family_id')
                    ->first();
                if ($head_of_house) {
                    $user->family_id = $head_of_house->family_id;
                }
            }

            // Double check genders
            if (!$user->gender) {
                if (Str::contains('Male', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                    $user->gender = 'male';
                }
                if (Str::contains('Female', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                    $user->gender = 'female';
                }
            }

            // -----------------------------------------------------
            // ADDRESSES

            if ($user->family_id == $user->id && $worksheet->getCellByColumnAndRow(25, $r)->getValue() > '') {
                $address = Address::create([
                    'user_id'   => $user->id,
                    'type'      => 'home',
                    'label'     => null,
                    'address1'  => $worksheet->getCellByColumnAndRow(25, $r)->getValue() ?: '',
                    'city'      => $worksheet->getCellByColumnAndRow(26, $r)->getValue() ?: '',
                    'state'     => $worksheet->getCellByColumnAndRow(27, $r)->getValue() ?: '',
                    'zip'       => $worksheet->getCellByColumnAndRow(28, $r)->getValue() ?: '',
                    'country'   => $worksheet->getCellByColumnAndRow(29, $r)->getValue() ?: '',
                    'is_family' => ($user->family_id == $user->id ? true : false),
                ]);

                $address->user_id = $user->id;
                $address->is_family ? $address->family_id = $user->id : null;

                $address->save();
            }

            // -----------------------------------------------------
            // PHONES

            if ($user->family_id == $user->id && $worksheet->getCellByColumnAndRow(21, $r)->getValue() > '') {
                Phone::create([
                    'user_id'    => $user->id,
                    'family_id'  => $user->family_id,
                    'number'     => Phone::format($worksheet->getCellByColumnAndRow(21, $r)->getValue()),
                    'is_primary' => false,
                    'is_family'  => true,
                    'is_hidden'  => false,
                    'type'       => 'home',
                ]);
            }
            // Mobile
            if ($worksheet->getCellByColumnAndRow(22, $r)->getValue() > '' && $worksheet->getCellByColumnAndRow(22, $r)->getValue() != 'Unlisted') {
                Phone::create([
                    'user_id'    => $user->id,
                    'number'     => Phone::format($worksheet->getCellByColumnAndRow(22, $r)->getValue()),
                    'is_primary' => true,
                    'is_family'  => false,
                    'is_hidden'  => false,
                    'type'       => 'mobile',
                ]);
            }
            // Work
            if ($worksheet->getCellByColumnAndRow(40, $r)->getValue() > '' && !Phone::where('number', Phone::format($worksheet->getCellByColumnAndRow(40, $r)->getValue()))->exists()) {
                Phone::create([
                    'user_id'    => $user->id,
                    'number'     => Phone::format($worksheet->getCellByColumnAndRow(40, $r)->getValue()),
                    'extension'  => Phone::format($worksheet->getCellByColumnAndRow(41, $r)->getValue()),
                    'is_primary' => false,
                    'is_family'  => false,
                    'is_hidden'  => false,
                    'type'       => 'work',
                ]);
            }

            // -----------------------------------------------------
            // EMAILS

            if ($worksheet->getCellByColumnAndRow(14, $r)->getValue() > '' && !Email::where('email', $worksheet->getCellByColumnAndRow(14, $r)->getValue())->exists()) {
                Email::create([
                    'user_id'               => $user->id,
                    'email'                 => $worksheet->getCellByColumnAndRow(14, $r)->getValue(),
                    'is_primary'            => true,
                    'is_family'             => false,
                    'is_hidden'             => false,
                    'type'                  => 'personal',
                    'receives_group_emails' => true,
                ]);
//            } elseif (Email::where('email', $worksheet->getCellByColumnAndRow(14, $r)->getValue())->exists()) {
//                $temp_email             = Email::where('email', $worksheet->getCellByColumnAndRow(37, $r)->getValue())->first();
//                $temp_email->is_family  = true;
//                $temp_email->is_primary = false;
//                $temp_email->type       = 'family';
//                $temp_email->save();
            }
            // Work Email
//            if ($worksheet->getCellByColumnAndRow(5, $r)->getValue() > '') {
//                Email::create([
//                    'user_id'               => $user->id,
//                    'email'                 => $worksheet->getCellByColumnAndRow(5, $r)->getValue(),
//                    'is_primary'            => false,
//                    'is_family'             => false,
//                    'is_hidden'             => true,
//                    'type'                  => 'work',
//                    'receives_group_emails' => true,
//                ]);
//            }

            // -----------------------------------------------------

            // Save our progress
            $user->save();

            // -----------------------------------------------------

            // Deceased
//            if (Str::contains('Deceased', $worksheet->getCellByColumnAndRow(18, $r)->getValue()) ||
//                Str::contains('Yes', $worksheet->getCellByColumnAndRow(42, $r)->getValue())) {
//                $date_deceased              = $this->getDateArray($worksheet->getCellByColumnAndRow(41, $r)->getValue());
//                $user->date_deceased        = !$date_deceased ? now() : Carbon::create($date_deceased['year'], $date_deceased['month'], $date_deceased['day']);
//                $user->status               = 'inactive';
//                $user->exclude_from_reports = true;
//            }

            // -----------------------------------------------------

            // Everyone is a member in this import
            $user->groups()->attach($member_group_id);
            $user->roles()->attach($member_role_id);

            // -----------------------------------------------------


            // Wedding Date
            $date_married = $this->getDateArray($worksheet->getCellByColumnAndRow(18, $r)->getValue());
            if ($date_married && $date_married !== '  /  /    ') {
                $user->date_married = Carbon::create($date_married['year'], $date_married['month'], $date_married['day']);
            }
            // Birthdate
            $birthdate = $this->getDateArray($worksheet->getCellByColumnAndRow(34, $r)->getValue());
            if ($birthdate && $birthdate !== '  /  /    ') {
                $user->birthdate = Carbon::create($birthdate['year'], $birthdate['month'], $birthdate['day']);
            }
            // Membership
            $date_membership = $this->getDateArray($worksheet->getCellByColumnAndRow(35, $r)->getValue());
            if ($date_membership && $date_membership !== '  /  /    ') {
                $user->date_membership = Carbon::create($date_membership['year'], $date_membership['month'], $date_membership['day']);
            }

            // Baptism
            $date_baptism = $this->getDateArray($worksheet->getCellByColumnAndRow(33, $r)->getValue());
            if ($date_baptism && $date_baptism !== '  /  /    ') {
                $user->date_baptism = Carbon::create($date_baptism['year'], $date_baptism['month'], $date_baptism['day']);
                $user->is_baptized  = Carbon::now();
            } elseif ($worksheet->getCellByColumnAndRow(32, $r)->getValue() == 'Yes') {
                $user->date_baptism = Carbon::now();
                $user->is_baptized  = Carbon::now();
            }

            // -----------------------------------------------------

            // Save our progress
            $user->save();

            $this->info('Imported user ' . $user->name);
            $this->info('---------------------------');

            $r++;
        }

        $this->info('Removing Family ID from notes...');

        foreach (User::where('account_id', $this->account_id)->get() as $temp_user) {
            $temp_user->visitation_notes = '';
            $temp_user->save();
        }

        $this->info('User import complete.');
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 2021;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => intval($year),
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
