<?php

namespace App\Console\Commands\DataImport\Soddy;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-soddy {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        1   => 'FamilyId',
        2   => 'LastName',
        3   => 'AdultFirstNames',
        4   => 'Address',
        5   => 'Address2',
        6   => 'City',
        7   => 'State',
        8   => 'Zip',
        9   => 'FamilyPhone',
        10  => 'FamilyEmail',
        11  => 'ImageId',
        12  => 'AdditionalDetails',
        13  => 'Notes',
        14  => 'AnniversaryDate',
        15  => 'IsAddressPrivate',
        16  => 'DateCreatedFamily',
        17  => 'DateModifiedFamily',
        18  => 'Adult1PersonId',
        19  => 'Adult1AltLastName',
        20  => 'Adult1FirstName',
        21  => 'Adult1Birthday',
        22  => 'Adult1BirthYear',
        23  => 'Adult1DisplayBirthdayInPdf',
        24  => 'Adult1EmailAddress',
        25  => 'Adult1AllowEmailMessage',
        26  => 'Adult1MobilePhoneNumber',
        27  => 'Adult1AllowTextMessage',
        28  => 'Adult1IsEmailPrivate',
        29  => 'Adult1IsMobilePhonePrivate',
        30  => 'Adult1IsMemberOfChurch',
        31  => 'Adult1IsPictured',
        32  => 'Adult1SortOrder',
        33  => 'Adult1DateCreated',
        34  => 'Adult1DateModified',
        35  => 'Adult2PersonId',
        36  => 'Adult2AltLastName',
        37  => 'Adult2FirstName',
        38  => 'Adult2Birthday',
        39  => 'Adult2BirthYear',
        40  => 'Adult2DisplayBirthdayInPdf',
        41  => 'Adult2EmailAddress',
        42  => 'Adult2AllowEmailMessage',
        43  => 'Adult2MobilePhoneNumber',
        44  => 'Adult2AllowTextMessage',
        45  => 'Adult2IsEmailPrivate',
        46  => 'Adult2IsMobilePhonePrivate',
        47  => 'Adult2IsMemberOfChurch',
        48  => 'Adult2IsPictured',
        49  => 'Adult2SortOrder',
        50  => 'Adult2DateCreated',
        51  => 'Adult2DateModified',
        52  => 'Child1PersonId',
        53  => 'Child1AltLastName',
        54  => 'Child1FirstName',
        55  => 'Child1Birthday',
        56  => 'Child1BirthYear',
        57  => 'Child1DisplayBirthdayInPdf',
        58  => 'Child1EmailAddress',
        59  => 'Child1AllowEmailMessage',
        60  => 'Child1MobilePhoneNumber',
        61  => 'Child1AllowTextMessage',
        62  => 'Child1IsEmailPrivate',
        63  => 'Child1IsMobilePhonePrivate',
        64  => 'Child1IsMemberOfChurch',
        65  => 'Child1IsPictured',
        66  => 'Child1SortOrder',
        67  => 'Child1DateCreated',
        68  => 'Child1DateModified',
        69  => 'Child2PersonId',
        70  => 'Child2AltLastName',
        71  => 'Child2FirstName',
        72  => 'Child2Birthday',
        73  => 'Child2BirthYear',
        74  => 'Child2DisplayBirthdayInPdf',
        75  => 'Child2EmailAddress',
        76  => 'Child2AllowEmailMessage',
        77  => 'Child2MobilePhoneNumber',
        78  => 'Child2AllowTextMessage',
        79  => 'Child2IsEmailPrivate',
        80  => 'Child2IsMobilePhonePrivate',
        81  => 'Child2IsMemberOfChurch',
        82  => 'Child2IsPictured',
        83  => 'Child2SortOrder',
        84  => 'Child2DateCreated',
        85  => 'Child2DateModified',
        86  => 'Child3PersonId',
        87  => 'Child3AltLastName',
        88  => 'Child3FirstName',
        89  => 'Child3Birthday',
        90  => 'Child3BirthYear',
        91  => 'Child3DisplayBirthdayInPdf',
        92  => 'Child3EmailAddress',
        93  => 'Child3AllowEmailMessage',
        94  => 'Child3MobilePhoneNumber',
        95  => 'Child3AllowTextMessage',
        96  => 'Child3IsEmailPrivate',
        97  => 'Child3IsMobilePhonePrivate',
        98  => 'Child3IsMemberOfChurch',
        99  => 'Child3IsPictured',
        100 => 'Child3SortOrder',
        101 => 'Child3DateCreated',
        102 => 'Child3DateModified',
    ];

    protected $timezone = 'America/New_York';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            $family_id = $worksheet->getCell([1, $r])->getValue();

            $user1 = null;

            $user1 = [
                'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                'family_role'     => 'head',
                'public_token'    => $worksheet->getCell([18, $r])->getValue(),
                'first_name'      => $worksheet->getCell([20, $r])->getValue(),
                'last_name'       => $worksheet->getCell([2, $r])->getValue(),
                'date_married'    => $worksheet->getCell([14, $r])->getValue(),
                'gender'          => 'male',
                'email'           => strtolower($worksheet->getCell([24, $r])->getValue()),
                'home_phone'      => $worksheet->getCell([9, $r])->getValue(),
                'cell_phone'      => $worksheet->getCell([26, $r])->getValue(),
                'allow_sms'       => $worksheet->getCell([27, $r])->getValue(),
                'address1'        => $worksheet->getCell([4, $r])->getValue(),
                'address2'        => $worksheet->getCell([5, $r])->getValue(),
                'city'            => $worksheet->getCell([6, $r])->getValue(),
                'state'           => $worksheet->getCell([7, $r])->getValue(),
                'postal_code'     => $worksheet->getCell([8, $r])->getValue(),
                'country'         => 'US',
                'birth_month_day' => $worksheet->getCell([21, $r])->getValue(),
                'birth_year'      => $worksheet->getCell([22, $r])->getValue(),
                'is_baptized'     => $worksheet->getCell([30, $r])->getValue(),
                'marital_status'  => !empty($worksheet->getCell([37, $r])->getValue()) ? 'married' : 'single',
                'notes'           => $worksheet->getCell([13, $r])->getValue(),
            ];

            $families[$family_id][] = $user1;

            if (!empty($worksheet->getCell([37, $r])->getValue())) {
                $user2 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'spouse',
                    'public_token'    => $worksheet->getCell([35, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([37, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([2, $r])->getValue(),
                    'date_married'    => $worksheet->getCell([14, $r])->getValue(),
                    'gender'          => 'female',
                    'email'           => strtolower($worksheet->getCell([41, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([43, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([44, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([38, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([39, $r])->getValue(),
                    'is_baptized'     => $worksheet->getCell([47, $r])->getValue(),
                    'marital_status'  => 'married',
                ];

                $families[$family_id][] = $user2;
            }

            if (!empty($worksheet->getCell([54, $r])->getValue())) {
                $user3 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([52, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([54, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => 'male',
                    'email'           => strtolower($worksheet->getCell([58, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([60, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([61, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([55, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([56, $r])->getValue(),
                    'is_baptized'     => $worksheet->getCell([64, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user3;
            }
            if (!empty($worksheet->getCell([71, $r])->getValue())) {
                $user4 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([69, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([71, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => 'male',
                    'email'           => strtolower($worksheet->getCell([75, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([77, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([78, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([72, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([73, $r])->getValue(),
                    'is_baptized'     => $worksheet->getCell([81, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user4;
            }
            if (!empty($worksheet->getCell([88, $r])->getValue())) {
                $user5 = [
                    'family_id'       => $worksheet->getCell([1, $r])->getValue(),
                    'family_role'     => 'child',
                    'public_token'    => $worksheet->getCell([66, $r])->getValue(),
                    'first_name'      => $worksheet->getCell([88, $r])->getValue(),
                    'last_name'       => $worksheet->getCell([2, $r])->getValue(),
                    'gender'          => 'male',
                    'email'           => strtolower($worksheet->getCell([92, $r])->getValue()),
                    'cell_phone'      => $worksheet->getCell([94, $r])->getValue(),
                    'allow_sms'       => $worksheet->getCell([95, $r])->getValue(),
                    'birth_month_day' => $worksheet->getCell([89, $r])->getValue(),
                    'birth_year'      => $worksheet->getCell([90, $r])->getValue(),
                    'is_baptized'     => $worksheet->getCell([98, $r])->getValue(),
                    'marital_status'  => 'single',
                ];

                $families[$family_id][] = $user5;
            }

            $r++;
        }

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            $family_id = null;

            foreach ($family_members as $member):

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name     = $member['first_name'];
                $user->last_name      = $member['last_name'];
                $user->gender         = strtolower($member['gender']);
                $user->status         = 'active';
                $user->is_active      = true;
                $user->family_role    = Arr::get($member, 'family_role');
                $user->timezone       = $this->timezone;
                $user->public_token   = $member['public_token'];
                $user->notes          = Arr::get($member, 'notes');
                $user->is_baptized    = Arr::get($member, 'is_baptized') == 'TRUE' ? now() : null;
                $user->marital_status = Arr::get($member, 'marital_status');

                // Birthdate Date
                if (Arr::get($member, 'birth_month_day')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'birth_month_day'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create(Arr::get($member, 'birth_year') ?: $temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Anniversary
                if (Arr::get($member, 'date_married')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'date_married'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                if ($user->family_role == 'head') {
                    $family_id = $user->id;
                }

                $user->family_id = $family_id;

                $user->generateUlid();

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if (Arr::get($member, 'cell_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'cell_phone'),
                    ], [
                        'user_id'          => $user->id,
                        'number'           => Arr::get($member, 'cell_phone'),
                        'messages_opt_out' => Arr::get($member, 'allow_sms') == 'TRUE' ? true : false,
                        'is_primary'       => true,
                        'is_family'        => false,
                        'is_hidden'        => false,
                        'type'             => 'mobile',
                    ]);
                }
                // Home
                if (Arr::get($member, 'home_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'home_phone'),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Arr::get($member, 'home_phone'),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                $email = null;
                $email = Arr::get($member, 'email');
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }

                // -----------------------------------------------------
                // ADDRESSES

                if (!empty(Arr::get($member, 'address1')) && !empty(Arr::get($member, 'city'))) {
                    $address = Address::firstOrCreate([
                        'address1' => Arr::get($member, 'address1'),
                        'city'     => Arr::get($member, 'city'),
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => Arr::get($member, 'address1'),
                        'address2'  => Arr::get($member, 'address2'),
                        'city'      => Arr::get($member, 'city'),
                        'state'     => Arr::get($member, 'state'),
                        'zip'       => Arr::get($member, 'postal_code'),
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'STATUS Members') {
                    continue;
                } elseif ($group_string == 'ADMINISTRATION') {
                    $actual_group_name = 'Admin';
                } else {
                    $name_split = explode(' ', $group_string, 2);

                    $actual_group_name = $name_split[1];
                }
            } catch (\Exception $e) {
                dd($groups, $group_string);
            }

            $this->attachGroupByName($new_user_record, $actual_group_name);
        }
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        if (DB::table('user_to_group')->where('user_id', $user->id)->where('user_group_id', $group_id)->exists()) {
            return;
        } else {
            $user->groups()->attach($group_id);
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
