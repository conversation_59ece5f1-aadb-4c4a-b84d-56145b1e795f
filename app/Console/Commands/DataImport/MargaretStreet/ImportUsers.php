<?php

namespace App\Console\Commands\DataImport\MargaretStreet;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-margaret-street {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        1  => 'User ID',
        2  => 'Family ID',
        3  => 'Family Relationship',
        4  => 'First Name',
        5  => 'Preferred Name',
        6  => 'Last Name',
        7  => 'Gender',
        8  => 'Email',
        9  => 'Subscribed Status',
        10 => 'Secondary Email',
        11 => 'Rfid',
        12 => 'Alternate Rfid',
        13 => 'Home Phone',
        14 => 'Cell Phone',
        15 => 'Work Phone',
        16 => 'Address',
        17 => 'City',
        18 => 'State',
        19 => 'Zip Code',
        20 => 'Country',
        21 => 'Label',
        22 => 'Start',
        23 => 'End',
        24 => 'Birthday',
        25 => 'Age',
        26 => 'Baptism Date',
        27 => 'Died On',
        28 => 'Last Attended Date',
        29 => 'Group Giving With Family',
        30 => 'Lead Prayer',
        31 => 'Song Leader',
        32 => 'Comments at Lord\'s Table',
        33 => 'Serve Communion',
        34 => 'Make Announcements',
        35 => 'Preach',
        36 => 'Greeters',
        37 => 'Group Leader',
        38 => 'Teacher',
        39 => 'Read Scripture',
        40 => 'Technology Skills',
        41 => 'Electrical Skills',
        42 => 'Carpentry Skills',
        43 => 'Landscaping Skills',
        44 => 'Plumbing Experience',
        45 => 'Visitors 2023',
        46 => 'Check-in Notes',
        47 => 'notes',
        48 => 'Active Groups',
        49 => 'Archive Groups',
        50 => 'Family Role Sort Order',
    ];

    protected $timezone = 'America/New_York';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            $user = null;

            $user = [
                'user_id'                  => $worksheet->getCell([1, $r])->getValue(),
                'family_id'                => $worksheet->getCell([2, $r])->getValue(),
                'family_role'              => $worksheet->getCell([3, $r])->getValue(),
                'first_name'               => $worksheet->getCell([4, $r])->getValue(),
                'preferred_first_name'     => $worksheet->getCell([5, $r])->getValue(),
                'last_name'                => $worksheet->getCell([6, $r])->getValue(),
                'gender'                   => strtolower($worksheet->getCell([7, $r])->getValue()),
                'email'                    => strtolower($worksheet->getCell([8, $r])->getValue()),
                'Subscribed Status'        => $worksheet->getCell([9, $r])->getValue(),
                'email2'                   => strtolower($worksheet->getCell([10, $r])->getValue()),
                'rfid'                     => $worksheet->getCell([11, $r])->getValue(),
                'rfid_2'                   => $worksheet->getCell([12, $r])->getValue(),
                'home_phone'               => $worksheet->getCell([13, $r])->getValue(),
                'cell_phone'               => $worksheet->getCell([14, $r])->getValue(),
                'work_phone'               => $worksheet->getCell([15, $r])->getValue(),
                'address1'                 => $worksheet->getCell([16, $r])->getValue(),
                'city'                     => $worksheet->getCell([17, $r])->getValue(),
                'state'                    => $worksheet->getCell([18, $r])->getValue(),
                'postal_code'              => $worksheet->getCell([19, $r])->getValue(),
                'country'                  => $worksheet->getCell([20, $r])->getValue(),
                'label'                    => $worksheet->getCell([21, $r])->getValue(),
                'start'                    => $worksheet->getCell([22, $r])->getValue(),
                'end'                      => $worksheet->getCell([23, $r])->getValue(),
                'birthday'                 => $worksheet->getCell([24, $r])->getValue(),
                'age'                      => $worksheet->getCell([25, $r])->getValue(),
                'date_baptism'             => $worksheet->getCell([26, $r])->getValue(),
                'deceased_at'              => $worksheet->getCell([27, $r])->getValue(),
                'Last Attended Date'       => $worksheet->getCell([28, $r])->getValue(),
                'Group Giving With Family' => $worksheet->getCell([29, $r])->getValue(),
                'Lead Prayer'              => $worksheet->getCell([30, $r])->getValue(),
                'Song Leader'              => $worksheet->getCell([31, $r])->getValue(),
                'Comments at Lords Table'  => $worksheet->getCell([32, $r])->getValue(),
                'Serve Communion'          => $worksheet->getCell([33, $r])->getValue(),
                'Make Announcements'       => $worksheet->getCell([34, $r])->getValue(),
                'Preach'                   => $worksheet->getCell([35, $r])->getValue(),
                'Greeters'                 => $worksheet->getCell([36, $r])->getValue(),
                'Group Leader'             => $worksheet->getCell([37, $r])->getValue(),
                'Teacher'                  => $worksheet->getCell([38, $r])->getValue(),
                'Read Scripture'           => $worksheet->getCell([39, $r])->getValue(),
                'Technology Skills'        => $worksheet->getCell([40, $r])->getValue(),
                'Electrical Skills'        => $worksheet->getCell([41, $r])->getValue(),
                'Carpentry Skills'         => $worksheet->getCell([42, $r])->getValue(),
                'Landscaping Skills'       => $worksheet->getCell([43, $r])->getValue(),
                'Plumbing Experience'      => $worksheet->getCell([44, $r])->getValue(),
                'Visitors 2023'            => $worksheet->getCell([45, $r])->getValue(),
                'Check-in Notes'           => $worksheet->getCell([46, $r])->getValue(),
                'notes'                    => $worksheet->getCell([47, $r])->getValue(),
                'active_groups'            => $worksheet->getCell([48, $r])->getValue(),
                'archive_groups'           => $worksheet->getCell([49, $r])->getValue(),
                'family_sort_id'           => 5,
            ];

            // Determine our family sort_id
            if (empty($worksheet->getCell([3, $r])->getValue())) {
                $user['family_sort_id'] = 1;
                $user['family_role']    = 'head';
            } elseif ($worksheet->getCell([3, $r])->getValue() == 'Primary' || $worksheet->getCell([3, $r])->getValue() == 'Husband') {
                $user['family_sort_id'] = 1;
                $user['family_role']    = 'head';
            } elseif ($worksheet->getCell([3, $r])->getValue() == 'Wife' || $worksheet->getCell([3, $r])->getValue() == 'Mother') {
                $user['family_sort_id'] = 2;
                $user['family_role']    = 'spouse';
            } elseif ($worksheet->getCell([3, $r])->getValue() == 'Son' || $worksheet->getCell([3, $r])->getValue() == 'Daughter') {
                $user['family_sort_id'] = 3;
                $user['family_role']    = 'child';
            } else {
                $user['family_sort_id'] = 4;
                $user['family_role']    = 'dependent';
            }

            // Add our row to our families array based on a family ID.  If no family ID, create one.
            if (!empty($worksheet->getCell([2, $r])->getValue())) {
                $families[$worksheet->getCell([2, $r])->getValue()][] = $user;
            } else {
                $families[uniqid()][] = $user;
            }

            $r++;
        }

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            $family_id           = null;
            $family_member_index = 0;
            $has_spouse          = false;

            // Resort our family members by their family role sort order.
            // We expect the Head of Household to be first to set family_id values.
            uasort($family_members, function ($a, $b) {
                return ($a['family_sort_id'] < $b['family_sort_id']) ? -1 : 1;
            });

            foreach ($family_members as $temp) {
                if ($temp['family_role'] == 'spouse') {
                    $has_spouse = true;
                }
            }

            foreach ($family_members as $member):

                $family_member_index++; // Start at 1

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name   = $member['first_name'];
                $user->last_name    = $member['last_name'];
                $user->gender       = $member['gender'];
                $user->status       = 'active';
                $user->family_role  = Arr::get($member, 'family_role');
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid()->toString();
                $user->notes        = Arr::get($member, 'notes');

                // Default to single.
                // If we have a husband and wife, they're married.
                if ($has_spouse && ($member['family_role'] == 'head' || $member['family_role'] == 'spouse')) {
                    $user->marital_status = 'married';
                } else {
                    $user->marital_status = 'single';
                }

                // Anniversary Date
                if (Arr::get($member, 'birthday')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'birthday'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                // Set our family_id
                if ($family_member_index == 1) {
                    $family_id       = $user->id;
                    $user->family_id = $family_id;
                } else {
                    $user->family_id = $family_id;
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if (Arr::get($member, 'cell_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'cell_phone'),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Arr::get($member, 'cell_phone'),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);

                    // If this belongs to someone else, assign it to this user.  Or if this is a home phone, but also a mobile phone, mobile overrides home.
//                    if (!$phone->user) {
//                        $phone->user_id = $user->id;
//                        $phone->save();
//                    }
                }
                // Home
                if (Arr::get($member, 'home_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'home_phone'),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Arr::get($member, 'home_phone'),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);

                    // If this belongs to someone else, assign it to this user.  Or if this is a home phone, but also a mobile phone, mobile overrides home.
//                    if (!$phone->user) {
//                        $phone->user_id = $user->id;
//                        $phone->save();
//                    }
                }

                // -----------------------------------------------------
                // EMAILS

                $email = null;
                $email = Arr::get($member, 'email');
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);

//                    if ($email->user_id != $user->id) {
//                        $email->user_id = $user->id;
//                        $email->save();
//                    }
                }

                // -----------------------------------------------------
                // ADDRESSES

                if ($family_member_index == 1 && !empty(Arr::get($member, 'address1')) && !empty(Arr::get($member, 'city'))) {
                    $address = Address::firstOrCreate([
                        'address1' => Arr::get($member, 'address1'),
                        'city'     => Arr::get($member, 'city'),
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => Arr::get($member, 'address1'),
                        'address2'  => Arr::get($member, 'address2'),
                        'city'      => Arr::get($member, 'city'),
                        'state'     => Arr::get($member, 'state'),
                        'zip'       => Arr::get($member, 'postal_code'),
                        'country'   => 'US',
                        'is_family' => true,
                    ]);

//                    if ($address->user_id != $user->id) {
//                        $address->user_id   = $user->id;
//                        $address->family_id = $family_id;
//                        $address->save();
//                    }
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

                $this->assignMemberSpecificGroups($user, $member);

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'STATUS Members') {
                    continue;
                } elseif ($group_string == 'ADMINISTRATION') {
                    $actual_group_name = 'Admin';
                } else {
                    $name_split = explode(' ', $group_string, 2);

                    $actual_group_name = $name_split[1];
                }
            } catch (\Exception $e) {
                dd($groups, $group_string);
            }

            $this->attachGroupByName($new_user_record, $actual_group_name);
        }
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
