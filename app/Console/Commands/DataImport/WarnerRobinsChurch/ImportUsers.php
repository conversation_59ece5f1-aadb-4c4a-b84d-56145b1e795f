<?php

namespace App\Console\Commands\DataImport\WarnerRobinsChurch;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\Services\CreateUserPhoto;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'warner-robins-church:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Breeze ID',
        2  => 'First Name',
        3  => 'Last Name',
        4  => 'Middle Name',
        5  => 'Nickname',
        6  => 'Maiden Name',
        7  => 'Gender',
        8  => 'Status',
        9  => 'Marital Status',
        10 => 'Birthdate',
        11 => 'Birthdate Month/Day',
        12 => 'Age',
        13 => 'Family',
        14 => 'Family Role',
        15 => 'School',
        16 => 'Graduation Year',
        17 => 'Grade',
        18 => 'Employer',
        19 => 'Mobile',
        20 => 'Home',
        21 => 'Work',
        22 => 'Email',
        23 => 'Street Address',
        24 => 'City',
        25 => 'State',
        26 => 'Zip',
        27 => 'Secondary Address',
        28 => 'Secondary City, State, Zip',
        29 => 'Visitation Team',
        30 => 'Added Date',
    ];

    protected $timezone = 'America/New_York';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id        = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id         = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id        = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id       = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;
        $this->former_member_group_id = Group::firstOrCreate([
                'account_id' => $this->account_id,
                'name'       => 'Former Members',
                'creator_id' => 1,
            ]
        )?->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            // Family ID or Breeze ID for single individuals.
            $family_id = !empty($worksheet->getCell([13, $r])->getValue()) ? $worksheet->getCell([13, $r])->getValue() : $worksheet->getCell([1, $r])->getValue();

            // Key is the Family Photo URL or a unique value
            $families[$family_id][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
                21 => $worksheet->getCell([21, $r])->getValue(),
                22 => $worksheet->getCell([22, $r])->getValue(),
                23 => $worksheet->getCell([23, $r])->getValue(),
                24 => $worksheet->getCell([24, $r])->getValue(),
                25 => $worksheet->getCell([25, $r])->getValue(),
                26 => $worksheet->getCell([26, $r])->getValue(),
                27 => $worksheet->getCell([27, $r])->getValue(),
                28 => $worksheet->getCell([28, $r])->getValue(),
                29 => $worksheet->getCell([29, $r])->getValue(),
                30 => $worksheet->getCell([30, $r])->getValue(),
            ];
            $r++;
        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_members):

            $family_id           = null;
            $family_member_index = 0;
            $is_member_family    = false;

            foreach ($family_members as $member):

                $family_member_index++; // Start at 1

                if ($member[8] == 'Member' && $family_member_index == 1) {
                    $is_member_family = true;
                }

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name  = $member[2];
                $user->last_name   = $member[3];
                $user->middle_name = $member[4];
                if ($member[5] > '') {
                    $user->preferred_first_name = $member[5];
                }
                $user->status       = 'active';
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid()->toString();

                // Double check genders
                if (Str::contains('Male', $member[7])) {
                    $user->gender = 'male';
                }
                if (Str::contains('Female', $member[7])) {
                    $user->gender = 'female';
                }

                // Save our user and get an ID.
                $user->save();

                if ($member[14] == 'Head of Household') {
                    $user->family_role = 'head';
                    $family_id         = $user->id;
                    $user->family_id   = $family_id;
                } elseif ($member[14] == 'Spouse') {
                    $user->family_role = 'spouse';
                    $user->family_id   = $family_id;
                    if (!$user->gender) {
                        $user->gender = 'female';
                    }
                } elseif ($member[14] == 'Adult Dependent') {
                    $user->family_role = 'dependent';
                    $user->family_id   = $family_id;
                } elseif ($member[14] == 'Child') {
                    $user->family_role = 'child';
                    $user->family_id   = $family_id;
                }

                // Default to single.
                $user->marital_status = 'single';

                // Marital Status
                if ($user->family_role == 'spouse') {
                    $user->marital_status = 'married';
                }
                if ($member[9] == 'Widowed') {
                    $user->marital_status = 'widowed';
                } elseif ($member[9] == 'Single') {
                    $user->marital_status = 'single';
                } elseif ($member[9] == 'Separated') {
                    $user->marital_status = 'separated';
                } elseif ($member[9] == 'Divorced') {
                    $user->marital_status = 'divorced';
                }

                // Birthdate
                if (!empty($member[10])) {
                    $temp_date = $this->getDateArray($member[10]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                if (!empty($member[18])) {
                    $user->notes = $member[18];
                }

                if ($is_member_family && !empty($member[29]) && is_numeric($member[29])) {
                    $this->attachGroupByName($user, 'Visitation Team ' . $member[29]);
                } elseif ($is_member_family && !empty($member[29])) {
                    $this->attachGroupByName($user, $member[29]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Home
                if ($member[20] > '' && $family_member_index == 1) {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[20]),
                    ],
                        [
                            'user_id'    => $user->id,
                            'family_id'  => $family_id,
                            'number'     => Phone::format($member[20]),
                            'is_primary' => false,
                            'is_family'  => true,
                            'is_hidden'  => false,
                            'type'       => 'home',
                        ]
                    );
                }
                // Mobile
                if (!empty($member[19])) {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[19]),
                    ],
                        [
                            'user_id'    => $user->id,
                            'number'     => Phone::format($member[19]),
                            'is_primary' => true,
                            'is_family'  => false,
                            'is_hidden'  => false,
                            'type'       => 'mobile',
                        ]
                    );
                }

                // -----------------------------------------------------
                // EMAILS

                if ($member[22] > '') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[22]),
                    ],
                        [
                            'user_id'               => $user->id,
                            'email'                 => strtolower($member[22]),
                            'is_primary'            => true,
                            'is_family'             => false,
                            'is_hidden'             => false,
                            'type'                  => 'personal',
                            'receives_group_emails' => true,
                        ]
                    );
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($member[23] > '' && $family_member_index == 1) {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[23],
                        'city'      => $member[24],
                        'family_id' => $family_id,
                    ],
                        [
                            'user_id'   => $user->id,
                            'family_id' => $family_id,
                            'type'      => 'home',
                            'label'     => 'Home',
                            'address1'  => $member[23] ?: '',
                            //                        'address2'  => $member[19] ?: '',
                            'city'      => $member[24] ?: '',
                            'state'     => $member[25] ?: '',
                            'zip'       => $member[26] ?: '',
                            'country'   => 'US',
                            'is_family' => true,
                        ]
                    );
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                if ($is_member_family) {
                    $user->groups()->attach($this->member_group_id);
                    $user->roles()->attach($this->member_role_id);
                } else {
                    $user->groups()->attach($this->former_member_group_id);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

//            // Go back through this family and see if we can identify the head as a male.
//            $family_head = User::find($family_id);
//            if (!$family_id) {
//                $this->warn('Could not find family head for family: '.$family_id);
//                continue;
//            }
//            if ($family_head->spouseFromHeadOfHousehold) {
//                $family_head->gender = 'male';
//                $family_head->save();
//            }

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 23;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
