<?php

namespace App\Console\Commands\DataImport;

use App\Users\Services\UpdateUser;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class ImportSpecialDays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-specials-days {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1 => 'Date',
        2 => 'First Name',
        3 => 'Last Name',
        4 => 'user_id',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();
        $data      = [];

        if (!$worksheet) {
            $this->error('Unable to load worksheet.');
            exit;
        }

        $import_type = $this->choice('Import Type?', [
            'Birthdays',
            'Anniversaries',
            'Baptism Birthdays',
        ], false);

        if (!$import_type) {
            $this->error('No import type selected.');
            exit;
        }

        $this->info('Importing ' . $import_type . '...');

        $r = 0;
        foreach ($worksheet->getRowIterator() as $row) {
            $r++;
            $user = null;

            // If this is our first row, and it looks like a heading, skip it.
            if ($r == 1 &&
                (strtolower($worksheet->getCell([1, $r])->getValue()) == 'date' || strtolower($worksheet->getCell([1, $r])->getValue()) == 'first name')) {
                $this->info('🔁 First row looks like a header. Skipping row 1...');
                $r++;
                continue;
            }

            // If we have a user ID.
            if (is_numeric($worksheet->getCell([4, $r])->getValue()) && $worksheet->getCell([4, $r])->getValue() > 0) {
                $user = User::where('account_id', $this->account_id)
                    ->where('id', (int)$worksheet->getCell([4, $r])->getValue())
                    ->first();
            }

            if (!$user) {
                $user = User::where('account_id', $this->account_id)
                    ->where('first_name', 'LIKE', $worksheet->getCell([2, $r])->getValue())
                    ->where('last_name', 'LIKE', $worksheet->getCell([3, $r])->getValue())
                    ->first();
            }

            if ($user) {
                $temp_date = $this->getDateArray($worksheet->getCell([1, $r])->getValue());

                // Baptism Date
                if ($import_type == 'Baptism Birthdays') {
                    if ($temp_date && $temp_date !== '/  /') {
                        if (empty($user->date_baptism)) {
                            (new UpdateUser($user))->update([
                                'date_baptism' => Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']),
                            ]);
                            if (!$user->is_baptized) {
                                (new UpdateUser($user))->update([
                                    'is_baptized' => now(),
                                ]);
                            }
                        } else {
                            $this->comment('⏭️ User already has a baptism date: ' . $user->date_baptism . ' - ' . $user->name);
                            continue;
                        }
                    }
                }
                // Birthdate
                if ($import_type == 'Birthdays') {
                    if ($temp_date && $temp_date !== '/  /') {
                        if (empty($user->birthdate)) {
                            (new UpdateUser($user))->update([
                                'birthdate' => Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']),
                            ]);
                        } else {
                            $this->comment('⏭️ User already has a birthdate: ' . $user->birthdate . ' - ' . $user->name);
                            continue;
                        }
                    }
                }
                // Anniversary
                if ($import_type == 'Anniversaries') {
                    if ($temp_date && $temp_date !== '/  /') {
                        if (empty($user->date_married)) {
                            (new UpdateUser($user))->update([
                                'date_married' => Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']),
                            ]);
                        } else {
                            $this->comment('⏭️ User already has an anniversary: ' . $user->date_married . ' - ' . $user->name);
                            continue;
                        }
                    }
                }

                $this->info('✅ Imported: ' . $user->name);
            } else {
                $this->error('❌ Unable to find user: ' . $worksheet->getCell([2, $r])->getValue() . ' ' . $worksheet->getCell([3, $r])->getValue() . ' - ' . $worksheet->getCell([4, $r])->getValue());
                continue;
            }
        }

        $this->info('Specials days import complete.');
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2];
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 1900;
            $day   = $date[1];
            $month = $date[0];
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => (int)$year,
            'month' => (int)$month,
            'day'   => (int)$day,
        ];
    }
}
