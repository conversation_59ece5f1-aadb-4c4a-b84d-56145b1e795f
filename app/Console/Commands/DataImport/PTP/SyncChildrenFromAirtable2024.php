<?php

namespace App\Console\Commands\DataImport\PTP;

use App\Accounts\Account;
use App\Programs\Program;
use App\Programs\ProgramGroup;
use App\Programs\ProgramUser;
use App\Programs\Services\CreateProgramUser;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SyncChildrenFromAirtable2024 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature   = 'ptp:sync-program-children-2024 {program_url_name}';
    protected $description = '';

    protected $account_id       = 59;
    protected $account;
    protected $program          = null;
    protected $parent_group     = null;
    protected $uuid_prefix      = 'PTP-2024-2-';
    protected $airtable_base_id = null;
    protected $airtable_view_id = null;

    protected $fields = [

    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account      = Account::find($this->account_id);
        $this->program      = Program::where('url_name', $this->argument('program_url_name'))->first();
        $this->parent_group = ProgramGroup::where('account_id', $this->account->id)
            ->where('program_id', $this->program->id)
            ->where('name', 'LIKE', 'Parent%')
            ->first();

        // If we don't have a parents groups, create it.
        if (!$this->parent_group) {
            $this->parent_group = ProgramGroup::create([
                'account_id'            => $this->account->id,
                'program_id'            => $this->program->id,
                'name'                  => 'Parents',
                'is_program_group'      => true,
                'enable_checkins'       => false,
                'is_registration_group' => false,
            ]);
        }

        // Determine our base table ID.
        if ($this->argument('program_url_name') == 'ptp-2024-1') {
            $this->airtable_base_id = 'app1ydUcpM8TtkdZ1';
            $this->airtable_view_id = 'Children-PTP-Session-1';
        } elseif ($this->argument('program_url_name') == 'ptp-2024-2') {
//            $this->airtable_base_id = 'appDnYAP8Ce8gmviM';
            $this->airtable_base_id = 'appDOUPXCVHubMSIQ';
            $this->airtable_view_id = 'Sevierville Only Children for Lightpost';
        } else {
            $this->error('Could not determine base ID for program!');
            return;
        }

        /**
         * 1. Get our records
         * 2. Go through records
         * 3. Check if record exists.
         * 4. If record does not exist, create it.
         * 5. If record does exist, update it.
         */

        $airtable = new \TANIOS\Airtable\Airtable([
            'api_key' => config('app.keys.ptp_airtable_api_key'),
            'base'    => $this->airtable_base_id,
        ]);

        try {
            $request = $airtable->getContent('Attendees', [
                'view'     => $this->airtable_view_id,
                'pageSize' => 100,
            ]);
        } catch (\Exception $e) {
            $this->error('Could not get records from Airtable!');
            dd($e);
            return;
        }

        $index = 0;
        do {
            $this->info('Processing page ' . $index + 1 . ' of registrations...');

            $response = $request->getResponse();

            foreach ($response['records'] as $record) {
                if ($this->getRecordField($record, 'Duplicated')) {
                    $this->error('Found duplicate entry according to PTP!  Entry ID: ' . $this->getRecordField($record, 'Entry Id'));
                    continue;
                }

                // See if this records exists.
                $existing_user = ProgramUser::where('account_id', $this->account->id)
                    ->where('uuid', 'LIKE', $this->uuid_prefix . $this->getRecordField($record, 'Entry Id'))
                    ->where('first_name', 'LIKE', $this->getRecordField($record, 'First Name'))
                    ->where('last_name', 'LIKE', $this->getRecordField($record, 'Last Name') . '%')
                    ->first();

                // If we don't exist, create this registration.
                if (!$existing_user) {
                    $this->addNewRegistration($record);
                    $this->info('New User!  CREATED entry.');
                } // Otherwise, update the existing user.
                else {
                    // Let make sure they belong to a group.  This happened when maybe they didn't belong to a group on first import?
                    $group_id = $this->getGroupAssignment($record);

//                    if (!$existing_user->groups->contains($group_id)) {
//                        $existing_user->groups()->syncWithPivotValues(
//                            [$group_id],
//                            ['account_id' => $this->account->id]
//                        );
////                        $existing_user->groups()->attach($group_id);
//                    }
//                    $this->indicateSelfCheckout($existing_user);
//                    $this->info('Found user already!  Skipping for now. ' . $this->getRecordField($record, 'Entry Id') . ' -- ' . $this->getRecordField($record, 'First Name') . ' ' . $this->getRecordField($record, 'Last Name'));
                }
            }

            $index++;
        } while ($request = $response->next());

        $this->info('Program registration sync complete.');
    }

    private function getGroupAssignment($record)
    {
        // 8/18/23 -- Just put all new kids into an "Unassigned" group.
//        $group_id = ProgramGroup::where('account_id', $this->account->id)
//            ->where('name', 'LIKE', '%Unassigned%')
//            ->first()?->id;
//
//        return $group_id;

        ///  ORIGINAL CODE
        $class_lookup = $this->getRecordField($record, 'Children\'s Class');

        if (!$class_lookup && $this->getRecordField($record, 'Special Needs') == 'Special Needs') {
            $class_lookup          = 'Special Needs';
            $original_class_lookup = 'Special Needs';
        } else {
            $original_class_lookup = $class_lookup;
        }

        // Asked by D2 for 10, 11 and 12 year olds to be in a single group called "Camp".  BRANSON
        // Asked by D2 for 11 and 12 year olds to be in a single group called "Camp". SEVEIRVILLE
        if ($class_lookup == 11 || $class_lookup == 12) {
            $class_lookup = 'Camp';
        } elseif ($class_lookup == 'Special Needs') {
            $class_lookup = 'Special Needs';
        } else {
            $class_lookup = $class_lookup . '-A';
        }

        // Find the group this user belongs to.
        $group_1 = ProgramGroup::where('account_id', $this->account->id)
            ->where('program_id', $this->program->id)
            ->where('name', 'LIKE', $class_lookup)
            ->first();

        // If now groups created yet, create them an pick the first one
        if (!$group_1) {
            $group_1_create = ProgramGroup::create([
                'account_id'            => $this->account->id,
                'program_id'            => $this->program->id,
                'name'                  => $class_lookup,
                'is_program_group'      => true,
                'enable_checkins'       => true,
                'is_registration_group' => true,
            ]);

            if ($class_lookup != 'Camp' && $class_lookup != 'Special Needs') {
                $group_2_create = ProgramGroup::create([
                    'account_id'            => $this->account->id,
                    'program_id'            => $this->program->id,
                    'name'                  => $original_class_lookup . '-B',
                    'is_program_group'      => true,
                    'enable_checkins'       => true,
                    'is_registration_group' => true,
                ]);
            }

            $group_id = $group_1_create->id;
        } // If we have a group and it's not camp, figure out which group to put them in
        elseif ($class_lookup != 'Camp' && $class_lookup != 'Special Needs') {
            // Get group 2 also
            $group_2 = ProgramGroup::where('account_id', $this->account->id)
                ->where('program_id', $this->program->id)
                ->where('name', 'LIKE', $original_class_lookup . '-B')
                ->first();

            // This makes a 50/50 split for the groups
            if ($group_1->users()->count() > $group_2->users()->count()) {
                $group_id = $group_2->id;
            } else {
                $group_id = $group_1->id;
            }
        } // Otherwise, if it's camp, just use that group.
        else {
            $group_id = $group_1->id;
        }

        if (!$group_id) {
            $this->error('Could not get or create group for child! -- ' . $class_lookup);
        }

        return $group_id;
    }

    private function addNewRegistration($record)
    {
        $group_id = $this->getGroupAssignment($record);

        $notes_section =
            ($this->getRecordField($record, 'Children\'s Class') ? 'Original Group: ' . PHP_EOL . $this->getRecordField($record, 'Children\'s Class') : null)
            . ($this->getRecordField($record, 'Can the child self check-out?') ? PHP_EOL . PHP_EOL . 'Can the child self check-out? ' . PHP_EOL . $this->getRecordField($record, 'Can the child self check-out?') : null)
            . ($this->getRecordField($record, 'Who else, besides parents, can pick the child up from class?') ? PHP_EOL . PHP_EOL . 'Who else, besides parents, can pick the child up from class? ' . PHP_EOL . $this->getRecordField($record, 'Who else, besides parents, can pick the child up from class?') : null)
            . ($this->getRecordField($record, 'Name and contact number of parent/guardian at PTP:') ? PHP_EOL . PHP_EOL . 'Name and contact number of parent/guardian at PTP: ' . PHP_EOL . $this->getRecordField($record, 'Name and contact number of parent/guardian at PTP:') : null)
            . ($this->getRecordField($record, 'Secondary name and contact number of parent/guardian at PTP:') ? PHP_EOL . PHP_EOL . 'Secondary name and contact number of parent/guardian at PTP: ' . PHP_EOL . $this->getRecordField($record, 'Secondary name and contact number of parent/guardian at PTP:') : null);

        $special_needs =
            ($this->getRecordField($record, 'Please describe the exceptional needs:') ?: null)
            . ($this->getRecordField($record, 'What type of support does this child need?') ? PHP_EOL . PHP_EOL . $this->getRecordField($record, 'What type of support does this child need?') : null);

        $user = (new CreateProgramUser())
            ->forAccount($this->account)
            ->forProgram($this->program)
            ->withGroups([$group_id])
            ->create([
                'first_name'    => $this->getRecordField($record, 'First Name'),
                'last_name'     => $this->getRecordField($record, 'Last Name'),
                'family_role'   => 'child',
                'allergies'     => $this->getRecordField($record, 'Please describe the allergies:'),
                'special_needs' => $special_needs,
                'notes'         => $notes_section,
                'uuid'          => $this->uuid_prefix . $this->getRecordField($record, 'Entry Id'),
            ]);

        Log::info('CreateProgramUser::create() --  ProgramUser # ' . $user->id);

        if (!$user) {
            $this->error('Count not create child user!');
        }

        $this->indicateSelfCheckout($user);

        if ($this->getRecordField($record, 'Contact First Name')) {
            (new CreateProgramUser())
                ->forAccount($this->account)
                ->forProgram($this->program)
                ->forRegistration($user->registration)
                ->withGroups([$this->parent_group?->id])
                ->create([
                    'first_name'   => $this->getRecordField($record, 'Contact First Name'),
                    'last_name'    => $this->getRecordField($record, 'Contact Last Name'),
                    'family_role'  => 'parent',
                    'email'        => $this->getRecordField($record, 'Email (from Parent Entry ID)'),
                    'mobile_phone' => $this->getRecordField($record, 'Contact Phone'),
                ]);
        }
    }

    private function getRecordField($record, $field)
    {
        if (property_exists($record->fields, $field)) {
            if (is_array($record->fields->{$field})) {
                return $record->fields->{$field}[0];
            } else {
                return $record->fields->{$field};
            }
        }

        return null;
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 23;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year > 23 ? intval('19' . $year) : intval('20' . $year),
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }

    private function indicateSelfCheckout($user)
    {
        if (Str::contains($user->notes, 'Yes—he/she can leave')
            || Str::contains($user->notes, 'he/she can leave')
            || Str::contains($user->notes, 'self check-out? Yes')
            || Str::contains($user->notes, 'self check-out?Yes')
        ) {
            // Remove any previous asterisk.
            $user->last_name = Str::remove('*', $user->last_name);
            // Add one in.
            $user->last_name = $user->last_name . '*';

            $user->save();
        }
    }
}
