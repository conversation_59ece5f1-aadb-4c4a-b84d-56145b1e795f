<?php

namespace App\Console\Commands\DataImport\LeagueCity;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-league-city {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        1  => 'Last Name',
        2  => 'First Name',
        3  => 'Home Phone',
        4  => 'Family ID',
        5  => 'Family Members',
        6  => 'Address',
        7  => 'Alt Address',
        8  => 'City',
        9  => 'Children',
        10 => 'Email',
        11 => 'Email 2',
        12 => 'Phone',
        13 => 'Primary Contact',
        14 => 'Zip Code',
        15 => 'Birth Date',
        16 => 'Birth Month and Day',
        17 => 'Birthdate Year',
        18 => 'Immersed for remission of sins',
        19 => 'Gender',
        20 => 'Cell Phone',
        21 => 'Cell Phone Unlisted',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            // Head and spouse
            $head_and_spouse_first_names = explode(' and ', $worksheet->getCell([2, $r])->getValue());
            $head_first_name             = Arr::get($head_and_spouse_first_names, 0);
            $spouse_first_name           = Arr::get($head_and_spouse_first_names, 1);

            $kids = explode(', ', $worksheet->getCell([9, $r])->getValue());

            // Head
            $this_user = [
                'family_id'       => $worksheet->getCell([4, $r])->getValue(),
                'first_name'      => $worksheet->getCell([2, $r])->getValue(),
                'last_name'       => $worksheet->getCell([1, $r])->getValue(),
                'home_phone'      => $worksheet->getCell([3, $r])->getValue() ? Phone::format($worksheet->getCell([3, $r])->getValue()) : null,
                'mobile_phone'    => $worksheet->getCell([20, $r])->getValue() ? Phone::format($worksheet->getCell([20, $r])->getValue()) : null,
                'email'           => $worksheet->getCell([10, $r])->getValue(),
                'address1'        => $worksheet->getCell([6, $r])->getValue(),
                'city'            => $worksheet->getCell([8, $r])->getValue(),
                'state'           => 'Texas',
                'zip'             => $worksheet->getCell([14, $r])->getValue(),
                'birthdate'       => $worksheet->getCell([15, $r])->getValue(),
                'gender'          => $worksheet->getCell([19, $r])->getValue(),
                'is_baptized'     => $worksheet->getCell([18, $r])->getValue() == 'Yes' ? true : false,
                'primary_contact' => $worksheet->getCell([13, $r])->getValue(),
                'children'        => $worksheet->getCell([9, $r])->getValue(),
                'family_members'  => $worksheet->getCell([5, $r])->getValue(),
            ];

            if (in_array($this_user['first_name'], $kids)) {
                $this_user['family_role'] = 'child';
                $this_user['sort_id']     = 3;
            } elseif (($this_user['primary_contact'] == $this_user['first_name'] . ' ' . $this_user['last_name']) || empty($this_user['primary_contact'])) {
                $this_user['family_role'] = 'head';
                $this_user['sort_id']     = 1;
            } else {
                $this_user['family_role'] = 'spouse';
                $this_user['sort_id']     = 2;
            }

            $families[$worksheet->getCell([4, $r])->getValue()][] = $this_user;

            $r++;
        }

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            // Order our family to make the head of household first.
            $family_array = Arr::sort($family_members, function ($value) {
                return $value['sort_id'];
            });

            $family_id           = null;
            $family_member_index = 0;

            foreach ($family_array as $member):

                $family_member_index++; // Start at 1

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name   = $member['first_name'];
                $user->last_name    = $member['last_name'];
                $user->status       = 'active';
                $user->family_role  = Arr::get($member, 'family_role');
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid()->toString();
                $user->notes        = Arr::get($member, 'notes');
                $user->gender       = Arr::get($member, 'gender') == 'Male' ? 'male' : 'female';

                // Default to single.
                // If we have a husband and wife, they're married.
                if (Arr::get(Arr::get($family_members, 0), 'role') == 'head' && Arr::get(Arr::get($family_members, 1), 'role') == 'spouse') {
                    $user->marital_status = 'married';
                } else {
                    $user->marital_status = 'single';
                }

                if ($member['is_baptized']) {
                    $user->is_baptized = now();
                }

                // birthdate
                if (Arr::get($member, 'birthdate')) {
                    $temp_date = $this->getDateArray(Arr::get($member, 'birthdate'));
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                if ($family_member_index == 1) {
                    $family_id       = $user->id;
                    $user->family_id = $family_id;
                } else {
                    $user->family_id = $family_id;
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if (Arr::get($member, 'mobile_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'mobile_phone'),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Arr::get($member, 'mobile_phone'),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);
                }
                // Home
                if (Arr::get($member, 'home_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Arr::get($member, 'home_phone'),
                    ], [
                        'user_id'    => $family_id,
                        'number'     => Arr::get($member, 'home_phone'),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                $email = null;
                $email = Arr::get($member, 'email');
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($family_member_index == 1 && Arr::get($member, 'address1')) {
                    $address = Address::firstOrCreate([
                        'address1' => Arr::get($member, 'address1'),
                        'city'     => Arr::get($member, 'city'),
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => Arr::get($member, 'address1'),
                        //                        'address2'  => Arr::get($member, 'address2'),
                        'city'      => Arr::get($member, 'city'),
                        'state'     => Arr::get($member, 'state'),
                        'zip'       => Arr::get($member, 'zip'),
                        'country'   => 'US',
                        'is_family' => true,
                    ]);

//                    if ($address->user_id != $user->id) {
//                        $address->user_id   = $user->id;
//                        $address->family_id = $family_id;
//                        $address->save();
//                    }
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)"
        foreach ($array as $value) {
            $parts = explode(' (', $value);

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . ')') {
                return trim(Arr::get($parts, 0), ' .');
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 23;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
