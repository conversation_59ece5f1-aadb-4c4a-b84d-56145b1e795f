<?php

namespace App\Console\Commands\DataImport\Wylie;

use App\Involvement\Area;
use App\Involvement\Category;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportInvolvement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-involvement-wylie {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        1 => 'LastName',
        2 => 'Firstname',
        3 => 'Volunteer Categories',
        4 => 'Volunteer Areas',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row: ' . $r . '...');
            }

            $user = User::where('account_id', $this->account_id)
                ->where('first_name', $worksheet->getCell([2, $r])->getValue())
                ->where('last_name', $worksheet->getCell([1, $r])->getValue())
                ->first();

            if ($user) {
                $this->attachInvolvementByName($user, $worksheet->getCell([3, $r])->getValue(), $worksheet->getCell([4, $r])->getValue());
            } else {
                $this->info('User not found: ' . $worksheet->getCell([2, $r])->getValue() . ' ' . $worksheet->getCell([1, $r])->getValue());
            }

            $r++;
        }

        // -----------------------------------------------------

        $this->info('User import complete.');
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachInvolvementByName($user, $category_name, $area_name)
    {
        $category = Category::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $category_name,
        ], []);

        $area = Area::firstOrCreate([
            'involvement_category_id' => $category->id,
            'name'                    => $area_name,
        ], []);

        // If this user already belongs to this area, skip it.
        if (DB::table('involvement_to_user')->where('user_id', $user->id)->where('involvement_area_id', $area->id)->exists()) {
            return;
        } else {
            DB::table('involvement_to_user')
                ->insert([
                    'user_id'                     => $user->id,
                    'involvement_category_id'     => $category->id,
                    'involvement_area_id'         => $area->id,
                    'is_approved_for_assignments' => 1,
                    'show_in_volunteer_list'      => 1,
                ]);
        }
    }
}
