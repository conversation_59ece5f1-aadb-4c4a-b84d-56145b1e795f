<?php

namespace App\Console\Commands\DataImport\SouthTrail;

use App\Accounts\Grade;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-south-trail-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    public $relationship_values = [
        'Organization Record',
        'Head of Household',
        'Spouse',
        'Son',
        'Individual',
        'Granddaughter',
        'Sister',
        'Daughter',
        'Friend',
        'Fiance',
        'Brother',
        'Mother',
        '<PERSON><PERSON>',
        'Child',
        'Cousin',
        'Caregiver',
        'Son-in-<PERSON>',
        'Great Granddaughter',
        'Grandmother',
        'Mother of Wife',
        '<PERSON> <PERSON>',
        'Daughter-in-<PERSON>',
        'Father',
        '<PERSON><PERSON><PERSON>',
        '<PERSON> Grandson',
        'Partner',
        'Relative',
    ];

    protected $columns = [
        1  => 'Last Name',
        2  => 'First Name',
        3  => 'Primary Phone',
        4  => 'Address',
        5  => 'City',
        6  => 'State',
        7  => 'Zip Code',
        8  => 'Age',
        9  => 'Alt Address (I)',
        10 => 'Alt City (I)',
        11 => 'Alt Country (I)',
        12 => 'Alt Home Phone (I)',
        13 => 'Alt State',
        14 => 'Alt Zip Code (I)',
        15 => 'Baptized',
        16 => 'Baptized Date',
        17 => 'Birth Date',
        18 => 'Cell Phone',
        19 => 'Date Created',
        20 => 'Date Joined',
        21 => 'Date Last Attended',
        22 => 'E-Mail',
        23 => 'Elder/Deacon/Ministry Ldr',
        24 => 'Email 02 (I)',
        25 => 'Family ID',
        26 => 'Gender',
        27 => 'Include A Cross Reference In Directory',
        28 => 'Include in Directory',
        29 => 'Individual ID',
        30 => 'Maiden Name',
        31 => 'Marital Status',
        32 => 'Member Status (I)',
        33 => 'Middle Name',
        34 => 'Minister',
        35 => 'Office / Administration',
        36 => 'Phone 01 (I)',
        37 => 'Phone 02 (I)',
        38 => 'Picture',
        39 => 'Primary Activities',
        40 => 'Primary Leadership',
        41 => 'Profile Notes',
        42 => 'Relationship',
        43 => 'Title',
        44 => 'Wedding Date',
        45 => 'Years Married',
        46 => 'Family Circle',
        47 => 'Allergy',
        48 => 'Country',
    ];

    protected $timezone = 'America/New_York';

    protected $member_group_id;
    protected $elder_group_id;
    protected $deacon_group_id;
    protected $visitor_group_id;

    protected $member_role_id;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id  = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id   = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id  = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
//                $this->info('Adding row to families: ' . $r . '...');
            }

            // FamilyID
            $family_id = $worksheet->getCell([25, $r])->getValue();

            $family_role_sort_order = null;
            if ($worksheet->getCell([42, $r])->getValue() == 'Head of Household') {
                $family_role_sort_order = 1;
            } elseif ($worksheet->getCell([42, $r])->getValue() == 'Spouse') {
                $family_role_sort_order = 2;
            } elseif (in_array($worksheet->getCell([42, $r])->getValue(), ['Son', 'Daughter', 'Grandson', 'Granddaughter', 'Step Son', 'Child'])) {
                $family_role_sort_order = 3;
            } else {
                $family_role_sort_order = 4;
            }

            // Family ID is 30
            $families[$family_id][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
                21 => $worksheet->getCell([21, $r])->getValue(),
                22 => $worksheet->getCell([22, $r])->getValue(),
                23 => $worksheet->getCell([23, $r])->getValue(),
                24 => $worksheet->getCell([24, $r])->getValue(),
                25 => $worksheet->getCell([25, $r])->getValue(),
                26 => $worksheet->getCell([26, $r])->getValue(),
                27 => $worksheet->getCell([27, $r])->getValue(),
                28 => $worksheet->getCell([28, $r])->getValue(),
                29 => $worksheet->getCell([29, $r])->getValue(),
                30 => $worksheet->getCell([30, $r])->getValue(),
                31 => $worksheet->getCell([31, $r])->getValue(),
                32 => $worksheet->getCell([32, $r])->getValue(),
                33 => $worksheet->getCell([33, $r])->getValue(),
                34 => $worksheet->getCell([34, $r])->getValue(),
                35 => $worksheet->getCell([35, $r])->getValue(),
                36 => $worksheet->getCell([36, $r])->getValue(),
                37 => $worksheet->getCell([37, $r])->getValue(),
                38 => $worksheet->getCell([38, $r])->getValue(),
                39 => $worksheet->getCell([39, $r])->getValue(),
                40 => $worksheet->getCell([40, $r])->getValue(),
                41 => $worksheet->getCell([41, $r])->getValue(),
                42 => $worksheet->getCell([42, $r])->getValue(),
                43 => $worksheet->getCell([43, $r])->getValue(),
                44 => $worksheet->getCell([44, $r])->getValue(),
                45 => $worksheet->getCell([45, $r])->getValue(),
                46 => $worksheet->getCell([46, $r])->getValue(),
                47 => $worksheet->getCell([47, $r])->getValue(),
                48 => $worksheet->getCell([48, $r])->getValue(),
                49 => $family_role_sort_order,
            ];
            $r++;
        }

        // -----------------------------------------------------
        $member_count = 0;
        // Go through each family and get them sorted out.
        foreach ($families as $family_members):

            // If this user already exists, skip it and make a note.
//            $existing_user = null;
//            $existing_user = User::where('account_id', $this->account_id)
//                ->where('first_name', 'like', $family_members[0][3])
//                ->where('last_name', 'like', $family_members[0][2])
//                ->first();
//
//            if ($existing_user) {
//                $this->info('Skip existing family for: ' . $existing_user->name);
//                continue;
//            }

            // Resort our family members by their family role sort order.
            // We expect the Head of Household to be first to set family_id values.
            uasort($family_members, function ($a, $b) {
                return ($a[49] < $b[49]) ? -1 : 1;
            });

            $family_id = null;

            foreach ($family_members as $index => $member):

                // If we have a "friend of the family" attached to a family unit, skip it.
                if (strtolower($member[28]) != 'yes' && !Str::contains($member[32], 'Past Member')) {
                    continue;
                }

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name  = $member[2];
                $user->middle_name = $member[33];
                $user->last_name   = $member[1];
                $user->gender      = $member[26] == 'Female' ? 'female' : 'male';
                $user->is_baptized = $member[15] == 'Yes' ? now() : null;

                $user->status       = 'active';
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid();

                // Default to single.
                if ($member[31] == 'Married') {
                    $user->marital_status = 'married';
                } elseif ($member[31] == 'Single') {
                    $user->marital_status = 'single';
                } elseif ($member[31] == 'Widow' || $member[31] == 'Widowed') {
                    $user->marital_status = 'widowed';
                } elseif ($member[31] == 'Divorced') {
                    $user->marital_status = 'divorced';
                } else {
                    $user->marital_status = 'single';
                }

                // If they are deceased, set them as inactive.
                if ($member[32] == 'Deceased') {
                    $user->status        = 'inactive';
                    $user->is_active     = false;
                    $user->date_deceased = now();
                } // If we have a reason for being inactive, set it.
//                elseif ($member[14] > '') {
//                    $user->status          = 'inactive';
//                    $user->is_active       = false;
//                    $user->departed_reason = $member[14];
//
//                    if ($member[15] > '') {
//                        $temp_date = $this->getDateArray($member[15]);
//                        if ($temp_date && $temp_date !== '/  /') {
//                            $user->date_departed = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
//                        }
//                    }
//                }

                // Baptism Date
                if ($member[16] > '') {
                    $temp_date = $this->getDateArray($member[16]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_baptism = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                        $user->is_baptized  = now();
                    }
                }
                // Birthdate
                if ($member[17] > '') {
                    $temp_date = $this->getDateArray($member[17]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Anniversary
                if ($member[44] > '') {
                    $temp_date = $this->getDateArray($member[44]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // Save our user and get an ID.
                $user->save();

                // -----------------------------------------------------

                // Relationship -- Possible Values:  Spouse, Head of Household, Child
                if ($member[42] == 'Head of Household' || $index == 0) {
                    $user->family_role = 'head';
                    // Set our global family_id for this round.
                    $family_id = $user->id;
                } elseif ($member[42] == 'Spouse' && $index == 1) {
                    $user->family_role = 'spouse';
                } elseif (in_array($member[42], ['Son', 'Daughter', 'Grandson', 'Granddaughter', 'Step Son', 'Child'])) {
                    $user->family_role = 'child';
                } else {
                    $user->family_role = 'dependent';
                }

                $user->family_id = $family_id;

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Mobile
                if ($member[18] > '' && $member[18] != 'Unlisted') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[18]),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format($member[18]),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);
                }
                // Home
                if ($member[3] > '' && $member[3] != 'Unlisted' && $index == 0 && $family_id) {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[3]),
                    ], [
                        'user_id'    => $family_id,
                        'family_id'  => $family_id,
                        'number'     => Phone::format($member[3]),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                if ($member[22] > '' && $member[22] != 'Unlisted') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[22]),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($member[22]),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }
                if ($member[24] > '' && $member[24] != 'Unlisted') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[24]),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($member[24]),
                        'is_primary'            => false,
                        'is_family'             => false,
                        'is_hidden'             => true,
                        'type'                  => 'other',
                        'receives_group_emails' => false,
                    ]);
                }

                // -----------------------------------------------------
                // ADDRESSES

                if ($index == 0 && $member[4] > '') {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[4],
                        'city'      => $member[5],
                        'family_id' => $family_id,
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => $member[4] ?: '',
                        'city'      => $member[5] ?: '',
                        'state'     => $member[6],
                        'zip'       => $member[7] ?: null,
                        'country'   => $member[48] ?: null,
                        'is_family' => true,
                    ]);
                }
                if ($index == 0 && $member[9] > '') {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[9],
                        'city'      => $member[10],
                        'family_id' => $family_id,
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'other',
                        'label'     => 'Other',
                        'address1'  => $member[9] ?: '',
                        'city'      => $member[10] ?: '',
                        'state'     => $member[13],
                        'zip'       => $member[14] ?: null,
                        'country'   => $member[11] ?: null,
                        'is_hidden' => true,
                        'is_family' => false,
                    ]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Members
                if (strtolower($member[28]) == 'yes') {
                    $user->groups()->attach($this->member_group_id);
                    $user->roles()->attach($this->member_role_id);
                }
                if (in_array($member[23], ['Elder'])) {
                    $this->attachGroupByName($user, 'Elders');
                    $user->roles()->attach($this->elder_role_id);
                }
                if (in_array($member[23], ['Deacon'])) {
                    $this->attachGroupByName($user, 'Deacons');
                    $user->roles()->attach($this->deacon_role_id);
                }
                if (in_array($member[32], ['Past Member'])) {
                    $this->attachGroupByName($user, 'Former Members');
                }
                if (in_array($member[32], ['Visitor'])) {
                    $this->attachGroupByName($user, 'Visitors');
                }

                // Additional Group Assignments
//                if ($member[14] > 0) {
//                    $group = Group::firstOrCreate([
//                        'account_id' => $this->account_id,
//                        'name'       => 'Group ' . $member[14],
//                    ], [
//                        'creator_id' => 1,
//                    ]);
//
//                    $user->groups()->attach($group->id);
//                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER
        endforeach; // EACH FAMILY

        $this->info('User import complete.');
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function attachGradeByName($user, $grade_name)
    {
        $grade_id = Grade::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $grade_name,
        ], [])?->id;

        $user->user_grade_id = $grade_id;
        $user->save();
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
