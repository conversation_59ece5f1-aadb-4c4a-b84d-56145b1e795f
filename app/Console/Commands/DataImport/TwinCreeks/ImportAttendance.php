<?php

namespace App\Console\Commands\DataImport\TwinCreeks;

use App\Attendance\Attendance;
use App\Attendance\AttendanceGeneralCount;
use App\Attendance\AttendanceType;
use App\Attendance\Services\CreateAttendanceRecordService;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class ImportAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twin-creeks:import-attendance {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();

        set_time_limit(600); // 10 minutes
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        // go through each user
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            $col_iterator = 2; // We start at 2 because the first cell is an empty header (where user column is).

            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } elseif ($r == 2) { // OVERALL
                $this->info('Starting row 2...');

                while (!empty($worksheet->getCell([$col_iterator, 1])->getValue())) {
                    // Get the date and type from the column header
                    $date_and_type = explode(' - ', $worksheet->getCell([$col_iterator, 1])->getValue());
                    $cur_date      = Carbon::createFromFormat('m/d/y', $date_and_type[0]);
                    $cur_type      = $this->getAttendanceTypeByName(trim($date_and_type[1]));

                    $current_general_count = AttendanceGeneralCount::where('account_id', $this->account_id)
                        ->where('attendance_at', $cur_date->format('Y-m-d'))
                        ->where('user_attendance_type_id', $cur_type->id)
                        ->first();

                    if (!$current_general_count) {
                        AttendanceGeneralCount::create([
                            'account_id'              => $this->account_id,
                            'created_by_user_id'      => 1,
                            'attendance_at'           => $cur_date->format('Y-m-d'),
                            'user_attendance_type_id' => $cur_type->id,
                            'count'                   => $worksheet->getCell([$col_iterator, $r])->getValue(),
                        ]);

                        $this->info('Adding general count for ' . $cur_date->format('Y-m-d') . ' - ' . $cur_type->name . ' - ' . $worksheet->getCell([$col_iterator, $r])->getValue() . '...');
                    } else {
                        // Do nothing for now.
                    }

                    $col_iterator++;
                }
            } elseif ($r == 3) { // VISITORS
                // Do nothing for now.
            } else {
                $this->info('Looking up user: ' . $r . '...');

                if ($worksheet->getCell([18, $r])->getValue()) {
                    $user = User::where('account_id', $this->account_id)
                        ->where('notes', $worksheet->getCell([1, $r])->getValue())
                        ->first();

                    if ($user) {
                        // Go through each column (a column is an attendance date)
                        while (!empty($worksheet->getCell([$col_iterator, 1])->getValue())) {
                            // Get the date and type from the column header
                            $date_and_type = explode(' - ', $worksheet->getCell([$col_iterator, 1])->getValue());
                            $cur_date      = Carbon::createFromFormat('m/d/y', $date_and_type[0]);
                            $cur_type      = $this->getAttendanceTypeByName(trim($date_and_type[1]));

                            if ($worksheet->getCell([$col_iterator, $r])->getValue() == 'Y'
                                || $worksheet->getCell([$col_iterator, $r])->getValue() == 'Aiding the Sick') {
                                // Find an existing one or create a new one
                                $attendance = Attendance::where('user_id', $user->id)
                                    ->where('user_attendance_type_id', $cur_type->id)
                                    ->where('date_attendance', $cur_date)
                                    ->first();

                                if (!$attendance) {
                                    (new CreateAttendanceRecordService())
                                        ->forUser(User::find($user->id))
                                        ->forDate(now()->setTimezone($this->timezone)->setDateFrom($cur_date))
                                        ->forAttendanceTypes([$cur_type->id])
                                        ->withComment(($worksheet->getCell([$col_iterator, $r])->getValue() == 'Aiding the Sick' ? 'Aiding the Sick' : null))
                                        ->create();

                                    $this->info('User found. ' . $user->name . '. Attendance created.');
                                } else {
                                    $this->info('User found. ' . $user->name . '. Attendance already existed.');
                                }
                            }

                            $col_iterator++;
                        }
                    } else {
                        $this->info('User not found. ' . $worksheet->getCell([1, $r])->getValue());
                    }
                }
            }

            $r++;
        }

        $this->info('User import complete.');
    }

    private function getAttendanceTypeByName($name)
    {
        try {
            return AttendanceType::firstOrCreate([
                'account_id' => $this->account_id,
                'name'       => trim($name),
            ]);
        } catch (\Exception $e) {
            $this->error('Could not create attendance type: ' . $name);
            return;
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
