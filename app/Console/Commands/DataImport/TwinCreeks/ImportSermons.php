<?php

namespace App\Console\Commands\DataImport\TwinCreeks;

use App\Attendance\AttendanceType;
use App\Sermons\File;
use App\Sermons\Sermon;
use App\Sermons\Services\CreateSermon;
use App\Sermons\Tag;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ImportSermons extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twin-creeks:import-sermons {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();

        set_time_limit(0); // Unlimited
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $headers = [
            1 => 'DATE',
            2 => 'Sermon Title',
            3 => 'URL',
            4 => 'SPEAKER',
            5 => 'SERIES',
            6 => 'SERVICE',
            7 => 'FILES',
            8 => 'PLAY',
        ];

        // go through each user
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            }

            $this->info('Looking up sermon: ' . $r . '...');

            $this->info($worksheet->getCell([3, $r])->getValue());

            $sermon_date = $this->getDateArray($worksheet->getCell([1, $r])->getValue());

            // Get/create our sermon tag
            if (!empty($worksheet->getCell([5, $r])->getValue())) {
                $sermon_tag_id = $this->getSermonTagByName($worksheet->getCell([5, $r])->getValue())?->id;
            } else {
                $sermon_tag_id = null;
            }
            // Get/create our attendance type
            if (!empty($worksheet->getCell([6, $r])->getValue())) {
                $attendance_type_id = $this->getAttendanceTypeByName($worksheet->getCell([6, $r])->getValue())?->id;
            } else {
                $attendance_type_id = null;
            }

            // See if this exists already
            $sermon = Sermon::where('account_id', $this->account_id)
                ->where('title', $worksheet->getCell([2, $r])->getValue())
                ->where('date_sermon', $sermon_date['year'] . '-' . $sermon_date['month'] . '-' . $sermon_date['day'])
                ->first();

            if (!empty($sermon) && $sermon && $sermon->files()->count() > 0) {
                $this->info('Sermon and file already exists: "' . $worksheet->getCell([2, $r])->getValue() . '"  Skipping...');
                $r++;
                continue;
            } elseif (!$sermon) {
                $sermon = (new CreateSermon())->create([
                    'account_id'         => $this->account_id,
                    'title'              => $worksheet->getCell([2, $r])->getValue(),
                    'type'               => 'sermon',
                    'language'           => 'en',
                    'speaker'            => $worksheet->getCell([4, $r])->getValue(),
                    'attendance_type_id' => $attendance_type_id,
                    'date_sermon'        => $sermon_date['year'] . '-' . $sermon_date['month'] . '-' . $sermon_date['day'],
                ]);

                if ($sermon_tag_id) {
                    $sermon->tags()->attach($sermon_tag_id);
                }
            }

            // Get our MP3 URL
            $page     = file_get_contents($worksheet->getCell([3, $r])->getValue());
            $file_url = null;
            $file_url = explode('href="/media/uploads/sermons', $page);
            if (is_array($file_url) && count($file_url) > 1) {
                $file_url           = Str::before($file_url[1], '"');
                $file_url           = 'https://www.tccoc.org/media/uploads/sermons' . $file_url;
                $file_original_name = Str::afterLast($file_url, '/');
            } else {
                $file_url = null;
            }

            try {
                if ($file_url) {
                    try {
                        $new_file_name                   = (new File())->sanitizeFilename($this->account_id . '--' . Str::random(4) . '--' . $file_original_name);
                        $new_file_name_without_extension = str_replace('.mp3', '', $new_file_name);

                        $client                 = new Client([
                            // Base URI is used with relative requests
                            // You can set any number of default request options.
                            'timeout' => 45.0, // 45 Seconds
                            'headers' => [
                                'User-Agent' => 'Mozilla/5.0 (Android 4.4; Mobile; rv:41.0) Gecko/41.0 Firefox/41.0 LightpostImport/1.0',
                            ],
                        ]);
                        $file_contents_response = $client->request('GET', $file_url);

                        Storage::disk('sermon-files')->put($this->account_id . '/' . $new_file_name, (string)$file_contents_response->getBody(), 'public');

                        $file = new File();

                        $file->fill([
                            'sermon_id'          => $sermon->id,
                            'title'              => $sermon->title,
                            'url_title'          => Str::slug($sermon->title),
                            'storage_service'    => 'do-spaces',
                            'data_separator'     => '--',
                            'file_original_name' => $file_original_name,
                            'file_size'          => $file_contents_response->getHeader('Content-Length')[0],
                            'file_folder'        => $this->account_id,
                            'file_id'            => null,
                            'file_name'          => $new_file_name_without_extension,
                            'file_extension'     => 'mp3',
                            'file_type'          => 'audio/mpeg',
                            'file_sha1'          => sha1((string)$file_contents_response->getBody()),
                        ]);

                        $file->save();

                        unset($file_contents);
                    } catch (\Exception $e) {
                        dd($e);
                    }
                }
            } catch (\Exception $e) {
                $this->error('Could not save file: ' . $file_url);
                $r++;
                continue;
            }

            $this->info('Row ' . $r . ' complete: ' . $sermon->title);

            $r++;
        }

        $this->info('Sermon import complete.');
    }

    private function getAttendanceTypeByName($name)
    {
        try {
            return AttendanceType::where('account_id', $this->account_id)
                ->where('name', trim($name))
                ->first();
        } catch (\Exception $e) {
            $this->error('Could not create attendance type: ' . $name);
            return;
        }
    }

    private function getSermonTagByName($name)
    {
        try {
            return Tag::firstOrCreate([
                'account_id' => $this->account_id,
                'name'       => trim($name),
                'slug'       => Str::slug(trim($name)),
            ]);
        } catch (\Exception $e) {
            $this->error('Could not create sermon tag: ' . $name);
            return;
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
