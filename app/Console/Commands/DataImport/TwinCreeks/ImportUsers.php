<?php

namespace App\Console\Commands\DataImport\TwinCreeks;

use App\Involvement\Area;
use App\Involvement\Category;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\Services\CreateUserPhoto;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twin-creeks:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Family_ID',
        2  => 'User_ID',
        3  => 'First_Name',
        4  => 'Last_Name',
        5  => 'Family_Role', // H / S / C (Head / Spouse / Child)
        6  => 'Gender',
        7  => 'Street',
        8  => 'Suite_Apt',
        9  => 'City',
        10 => 'State',
        11 => 'Zip',
        12 => 'Home_Phone',
        13 => 'Cell_Phone',
        14 => 'Work_Phone',
        15 => 'Prefered_Phone',
        16 => 'Email_Address',
        17 => 'Birthday',
        18 => 'Anniversary',
        19 => 'Baptism_Status', // B / N (Baptized / Not Baptized)
        20 => 'Member_Status', // C / F (Current / Former)
        21 => 'Photo_Individual',
        22 => '',
        23 => 'Group-Deacons', // Y / N
        24 => 'Group-deaconwives',
        25 => 'Group-Elders',
        26 => 'Group-Encouragement',
        27 => 'Group-Group 1 Q1/Q2 2024',
        28 => 'Group-Group 2 Q1/Q2 2024',
        29 => 'Group-Group 3 Q1/Q2 2024',
        30 => 'Group-info',
        31 => 'Group-Members',
        32 => 'Group-men',
        33 => 'Group-OSHdevelopers',
        34 => 'Group-Preachers',
        35 => 'Group-Seniors',
        36 => 'Group-singles',
        37 => 'Group-songleaders',
        38 => 'Group-teachers',
        39 => 'Group-ViewClasses',
        40 => 'Group-women',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();

        set_time_limit(300); // 5 minutes
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id        = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id         = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id        = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id       = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;
        $this->former_member_group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => 'Former Members',
            'creator_id' => 1,
        ], [])?->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            // Key is the Family Photo URL or a unique value
            $families[$worksheet->getCell([1, $r])->getValue()][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
                21 => $worksheet->getCell([21, $r])->getValue(),
                22 => $worksheet->getCell([22, $r])->getValue(),
                23 => $worksheet->getCell([23, $r])->getValue(),
                24 => $worksheet->getCell([24, $r])->getValue(),
                25 => $worksheet->getCell([25, $r])->getValue(),
                26 => $worksheet->getCell([26, $r])->getValue(),
                27 => $worksheet->getCell([27, $r])->getValue(),
                28 => $worksheet->getCell([28, $r])->getValue(),
                29 => $worksheet->getCell([29, $r])->getValue(),
                30 => $worksheet->getCell([30, $r])->getValue(),
                31 => $worksheet->getCell([31, $r])->getValue(),
                32 => $worksheet->getCell([32, $r])->getValue(),
                33 => $worksheet->getCell([33, $r])->getValue(),
                34 => $worksheet->getCell([34, $r])->getValue(),
                35 => $worksheet->getCell([35, $r])->getValue(),
                36 => $worksheet->getCell([36, $r])->getValue(),
                37 => $worksheet->getCell([37, $r])->getValue(),
                38 => $worksheet->getCell([38, $r])->getValue(),
                39 => $worksheet->getCell([39, $r])->getValue(),
                40 => $worksheet->getCell([40, $r])->getValue(),
            ];
            $r++;
        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $import_family_id => $family_members):

            $family_id           = null;
            $family_member_index = 0;

            foreach ($family_members as $member):

                $family_member_index++; // Start at 1
                $anniv_date = null;

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name   = $member[3];
                $user->last_name    = $member[4];
                $user->status       = 'active';
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid()->toString();

                $user->gender = ($member[6] == 'M' ? 'male' : 'female');

                // Default to single.
                $user->marital_status = 'single';

                //  Dates
                if ($member[17]) {
                    $temp_date = $this->getDateArray($member[17]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Because $anniv_date only shows for Head of Household, but we want to put it on the spouse too.
                if ($family_member_index == 1 && $member[18]) {
                    $anniv_date = $member[18];
                    $temp_date  = $this->getDateArray($member[18]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                if ($member[5] == 'S' && $anniv_date) {
                    $temp_date = $this->getDateArray($anniv_date);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                $user->is_baptized = $member[19] == 'B' ? now() : null;

                $user->notes = $member[2];

                // Save our user and get an ID.
                $user->save();

                if ($member[5] == 'H') {
                    $user->family_role = 'head';
                    $family_id         = $user->id;
                    $user->family_id   = $family_id;
                } elseif ($member[5] == 'S') {
                    $user->family_role = 'spouse';
                    $user->family_id   = $family_id;
                } elseif ($member[5] == 'C') {
                    $user->family_role = 'child';
                    $user->family_id   = $family_id;
                }

                // Marital Status
                if ($user->family_role == 'spouse') {
                    $user->marital_status = 'married';

                    // Set our husband as married too
                    $original_head = null;
                    $original_head = User::find($family_id);
                    if ($original_head) {
                        $original_head->marital_status = 'married';
                        $original_head->save();
                    }
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Home
                if ($member[12] > '' && $family_member_index == 1) {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[12]),
                    ], [
                        'user_id'    => $user->id,
                        'family_id'  => $family_id,
                        'number'     => Phone::format($member[12]),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }
                // Mobile
                if ($member[13] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[13]),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format($member[13]),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);
                }
                // Mobile
                if ($member[14] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[14]),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format($member[14]),
                        'is_primary' => false,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'work',
                    ]);
                }

                // -----------------------------------------------------
                // EMAILS

                if ($member[16] > '') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[16]),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($member[16]),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }

                // -----------------------------------------------------
                // ADDRESSES

                if ($member[7] > '' && $family_member_index == 1) {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[7],
                        'city'      => $member[9],
                        'family_id' => $family_id,
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => $member[7] ?: '',
                        'address2'  => $member[8] ?: '',
                        'city'      => $member[9] ?: '',
                        'state'     => $member[10] ?: '',
                        'zip'       => $member[11] ?: '',
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                if (Str::contains($member[2], 'deceased')) {
                    $user->date_deceased = now();
                    $user->is_active     = false;
                    $user->save();
                } else {
                    if ($member[20] == 'C') {
                        $user->groups()->attach($this->member_group_id);
                        $user->roles()->attach($this->member_role_id);
                    } else {
                        $user->groups()->attach($this->former_member_group_id);
                    }

                    if ($member[23] == 'Y') {
                        $this->attachGroupByName($user, 'Deacons');
                    }
                    if ($member[24] == 'Y') {
                        $this->attachGroupByName($user, 'Deacon Wives');
                    }
                    if ($member[25] == 'Y') {
                        $this->attachGroupByName($user, 'Elders');
                    }
                    if ($member[26] == 'Y') {
                        $this->attachGroupByName($user, 'Encouragement');
                    }
                    if ($member[27] == 'Y') {
                        $this->attachGroupByName($user, 'Group 1 Q1/Q2 2024');
                    }
                    if ($member[28] == 'Y') {
                        $this->attachGroupByName($user, 'Group 2 Q1/Q2 2024');
                    }
                    if ($member[29] == 'Y') {
                        $this->attachGroupByName($user, 'Group 3 Q1/Q2 2024');
                    }
                    if ($member[30] == 'Y') {
                        $this->attachGroupByName($user, 'Info');
                    }
                    if ($member[31] == 'Y') {
                        $this->attachGroupByName($user, 'Members (Original)');
                    }
                    if ($member[32] == 'Y') {
                        $this->attachGroupByName($user, 'Men');
                    }
                    if ($member[33] == 'Y') {
                        $this->attachGroupByName($user, 'OSH Developers');
                    }
                    if ($member[34] == 'Y') {
                        $this->attachGroupByName($user, 'Preachers');
                    }
                    if ($member[35] == 'Y') {
                        $this->attachGroupByName($user, 'Seniors');
                    }
                    if ($member[36] == 'Y') {
                        $this->attachGroupByName($user, 'Singles');
                    }
                    if ($member[37] == 'Y') {
                        $this->attachGroupByName($user, 'Song Leaders');
                    }
                    if ($member[38] == 'Y') {
                        $this->attachGroupByName($user, 'Teachers');
                    }
                    if ($member[39] == 'Y') {
                        $this->attachGroupByName($user, 'View Classes');
                    }
                    if ($member[40] == 'Y') {
                        $this->attachGroupByName($user, 'Women');
                    }
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);

                // Try to add our family photo
                if ($family_member_index == 1 && Str::contains($member[21], 'http')) {
                    try {
                        (new CreateUserPhoto())
                            ->withFile(file_get_contents($member[21]))
                            ->originalClientFileName($user->last_name . '_family_photo')
                            ->forUser($user)
                            ->create([
                                'family_id'  => request()->input('is_family') ? $family_id : null,
                                'is_primary' => request()->input('is_primary', 1),
                                'is_avatar'  => request()->input('is_avatar', 0),
                                'is_hidden'  => request()->input('is_hidden', 0),
                                'is_family'  => request()->input('is_family', 1),
                            ]);
                    } catch (\Exception $e) {
                        $this->warn('Could not save family photo for user: ' . $user->id . ' -- ' . $user->last_name);
                    }

                    $this->info('Family photo added.');
                }

                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function attachInvolvementByTitle($user, $title)
    {
        $parts = explode(' - ', $title);

        try {
            $category = Category::firstOrCreate([
                'account_id' => $this->account_id,
                'name'       => trim($parts[0]),
            ], []);

            $area = Area::firstOrCreate([
                'involvement_category_id' => $category->id,
                'name'                    => trim($parts[1]),
            ], []);
        } catch (\Exception $e) {
            $this->error('Could not attach involvement area: ' . $title);
            dd($title);
            return;
        }

        $user->involvementCategories()->attach($category->id, [
            'involvement_area_id' => $area->id,
        ]);
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
