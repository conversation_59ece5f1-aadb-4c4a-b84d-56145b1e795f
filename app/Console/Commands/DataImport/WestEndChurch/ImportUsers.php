<?php

namespace App\Console\Commands\DataImport\WestEndChurch;

use App\Accounts\Grade;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'west-end-church:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;

    protected $columns = [
        1  => 'Salutation',
        2  => 'Last Name',
        3  => 'First Name',
        4  => 'Middle Name',
        5  => 'Preferred Name',
        6  => 'Home Phone',
        7  => 'Address',
        8  => 'Address Line 2',
        9  => 'City',
        10 => 'State',
        11 => 'Zip Code',
        12 => 'Focus Group',
        13 => 'Willing to Serve',
        14 => 'E-Mail',
        15 => 'Email',
        16 => 'Member Status',
        17 => 'Fam Member Status',
        18 => 'Children',
        19 => 'Include in Directory',
        20 => 'Include in Directory',
        21 => 'Allergy',
        22 => 'Baptized',
        23 => 'Baptized Date',
        24 => 'Birth Date',
        25 => 'Cell Phone',
        26 => 'Cell Phone Unlisted',
        27 => 'Church email list',
        28 => 'City',
        29 => 'Date Joined',
        30 => 'Email 1 Unlisted',
        31 => 'Email Bulletin',
        32 => 'Employer',
        33 => 'Family ID',
        34 => 'Marital Status',
        35 => 'Family Members/Relatives',
        36 => 'Family Members',
        37 => 'Gender',
        38 => 'Occupation',
        39 => 'School Grade',
        40 => 'Shut-In/Homebound',
        41 => 'Sunday School',
        42 => 'Wedding Date',
        43 => 'Work Phone',
        44 => 'Degrees',
        45 => 'Individual ID',
        46 => 'Relationship',
    ];

    protected $timezone = 'America/New_York';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id  = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id   = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id  = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;
//        $this->former_member_group_id = Group::firstOrCreate([
//            'account_id' => $this->account_id,
//            'name'       => 'Former Members',
//            'creator_id' => 1,
//        ], [])?->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            // Family ID is 18
            $families[$worksheet->getCell([33, $r])->getValue()][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
                21 => $worksheet->getCell([21, $r])->getValue(),
                22 => $worksheet->getCell([22, $r])->getValue(),
                23 => $worksheet->getCell([23, $r])->getValue(),
                24 => $worksheet->getCell([24, $r])->getValue(),
                25 => $worksheet->getCell([25, $r])->getValue(),
                26 => $worksheet->getCell([26, $r])->getValue(),
                27 => $worksheet->getCell([27, $r])->getValue(),
                28 => $worksheet->getCell([28, $r])->getValue(),
                29 => $worksheet->getCell([29, $r])->getValue(),
                30 => $worksheet->getCell([30, $r])->getValue(),
                31 => $worksheet->getCell([31, $r])->getValue(),
                32 => $worksheet->getCell([32, $r])->getValue(),
                33 => $worksheet->getCell([33, $r])->getValue(),
                34 => $worksheet->getCell([34, $r])->getValue(),
                35 => $worksheet->getCell([35, $r])->getValue(),
                36 => $worksheet->getCell([36, $r])->getValue(),
                37 => $worksheet->getCell([37, $r])->getValue(),
                38 => $worksheet->getCell([38, $r])->getValue(),
                39 => $worksheet->getCell([39, $r])->getValue(),
                40 => $worksheet->getCell([40, $r])->getValue(),
                41 => $worksheet->getCell([41, $r])->getValue(),
                42 => $worksheet->getCell([42, $r])->getValue(),
                43 => $worksheet->getCell([43, $r])->getValue(),
                44 => $worksheet->getCell([44, $r])->getValue(),
                45 => $worksheet->getCell([45, $r])->getValue(),
                46 => $worksheet->getCell([46, $r])->getValue(),
            ];
            $r++;

        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            $sorted_family = [];

            // Figure out family sort order.
            // Head
            foreach ($family_members as $member) {
                if (strtolower($member[46]) == 'head of household') {
                    $sorted_family[] = $member;
                }
            }
            // Spouse
            foreach ($family_members as $member) {
                if (strtolower($member[46]) == 'spouse') {
                    $sorted_family[] = $member;
                }
            }
            // Everyone else
            foreach ($family_members as $member) {
                if (strtolower($member[46]) != 'head of household' && strtolower($member[46]) != 'spouse') {
                    $sorted_family[] = $member;
                }
            }

            $family_id           = null;
            $family_member_index = 0;

            foreach ($sorted_family as $member):

                $family_member_index++; // Start at 1

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name           = $member[3];
                $user->middle_name          = $member[4];
                $user->preferred_first_name = $member[3] !== $member[5] ? $member[5] : null;
                $user->last_name            = $member[2];
                $user->status               = 'active';
                $user->is_active            = true;
                $user->timezone             = $this->timezone;
                $user->public_token         = Str::uuid()->toString();

                // Double check genders
                if (!$user->gender) {
                    if (Str::contains('Male', $member[37])) {
                        $user->gender = 'male';
                    }
                    if (Str::contains('Female', $member[37])) {
                        $user->gender = 'female';
                    }
                }

                // Default to single.
                $user->marital_status = 'single';

                // Birthdate
                if ($member[24] > '') {
                    $temp_date = $this->getDateArray($member[24]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Membership Date
                if ($member[29] > '') {
                    $temp_date = $this->getDateArray($member[29]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_membership = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Anniversary Date
                if ($member[42] > '') {
                    $temp_date = $this->getDateArray($member[42]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }
                // Baptism Date
                if ($member[23] > '') {
                    $temp_date = $this->getDateArray($member[23]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_baptism = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                    $user->is_baptized = now();
                }
                if (Str::contains('Yes', $member[22], true)) {
                    $user->is_baptized = now();
                }

                // Marital Status
                if (Str::contains('Married', $member[34], true)) {
                    $user->marital_status = 'married';
                } elseif (Str::contains('Divorced', $member[34], true)) {
                    $user->marital_status = 'divorced';
                } elseif (Str::contains('Widow', $member[34], true)) {
                    $user->marital_status = 'widowed';
                } elseif (Str::contains('Single', $member[34], true)) {
                    $user->marital_status = 'single';
                }

                // Occupation
                if ($member[38] > '') {
                    $user->job_title = $member[38];
                }
                if ($member[32] > '') {
                    $user->employer = $member[32];
                }
                // Degrees
                if ($member[44] > '') {
                    $user->job_keywords = $member[44];
                }
                // Allergies
                if ($member[21] > '') {
                    $user->allergies = $member[21];
                }
                // Grade
                if ($member[39] > '' && strtolower($member[39] != 'graduated')) {
                    $this->attachSchoolGrade($user, $member[39]);
                }

                // Save our user and get an ID.
                $user->save();

                if ($family_member_index == 1) {
                    $user->family_role = 'head';
                    $family_id         = $user->id;
                    $user->family_id   = $family_id;
                } elseif ($family_member_index == 2 && Str::contains($member[34], 'married', true)) {
                    $user->family_role = 'spouse';
                    $user->family_id   = $family_id;
                } elseif (($family_member_index == 2 && !Str::contains($member[34], 'married', true)) || $family_member_index > 2) {
                    $user->family_role = 'child';
                    $user->family_id   = $family_id;
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Home
                if ($member[6] > '' && $family_member_index == 1) {
                    $phone = Phone::firstOrCreate([
                        'number' => Phone::format($member[6]),
                    ], [
                        'user_id'    => $user->id,
                        'family_id'  => $family_id,
                        'number'     => Phone::format($member[6]),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);

                    if (!$phone->user && $phone->type != 'mobile') {
                        $phone->user_id = $user->id;
                        $phone->save();
                    }
                }
                // Mobile
                if (!empty($member[25]) && $member[25] != 'Unlisted') {
                    $phone = Phone::firstOrCreate([
                        'number' => Phone::format($member[25]),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format($member[25]),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => strtolower($member[26]) == 'yes' ? true : false,
                        'type'       => 'mobile',
                    ]);

                    // If this belongs to someone else, assign it to this user.  Or if this is a home phone, but also a mobile phone, mobile overrides home.
                    if (!$phone->user || $phone->type == 'home') {
                        $phone->type      = 'mobile';
                        $phone->user_id   = $user->id;
                        $phone->is_hidden = strtolower($member[26]) == 'yes' ? true : false;
                        $phone->save();
                    }
                }

                // -----------------------------------------------------
                // EMAILS

                $email = null;
                $email = $member[14];
                if (!empty($email)) {
                    $email = Email::firstOrCreate([
                        'email' => strtolower($email),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($email),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);

                    if ($email->user_id != $user->id) {
                        $email->user_id = $user->id;
                        $email->save();
                    }
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($member[7] > '' && $family_member_index == 1) {
                    $address = Address::firstOrCreate([
                        'address1' => $member[7],
                        'city'     => $member[9],
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => $member[7] ?: '',
                        'address2'  => $member[8] ?: '',
                        'city'      => $member[9] ?: '',
                        'state'     => $member[10] ?: '',
                        'zip'       => $member[11] ?: '',
                        'country'   => 'US',
                        'is_family' => true,
                    ]);

                    if ($address->user_id != $user->id) {
                        $address->user_id   = $user->id;
                        $address->family_id = $family_id;
                        $address->save();
                    }
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Involvement Areas
                $selections = [];
                $selections = explode(';', $member[13]);
                foreach ($selections as $selection) {
                    if ($selection) {
                        $this->attachInvolvementByTitle($user, trim($selection));
                    }
                }

                // Groups

                // Focus Group
                if (!empty($member[12])) {
                    $this->attachGroupByName($user, $member[12]);
                }
                // Church Email List
                if (strtolower($member[27]) == 'yes') {
                    $this->attachGroupByName($user, 'Church Email List');
                }
                // Email Bulletin
                if (strtolower($member[31]) == 'yes') {
                    $this->attachGroupByName($user, 'Email Bulletin');
                }
                // Shut-in
                if (strtolower($member[40]) == 'yes') {
                    $this->attachGroupByName($user, 'Shut-in / Homebound');
                }
                // Others
                $selections = [];
                $selections = explode(';', $member[41]);
                foreach ($selections as $selection) {
                    $this->attachGroupByName($user, trim($selection));
                }

                $user->groups()->attach($this->member_group_id);
                $user->roles()->attach($this->member_role_id);

                // Homebound
                if (Str::contains('Homebound', $member[5], true)) {
                    $this->attachGroupByName($user, $member[5]);
                }


                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function attachSchoolGrade($user, $grade_title)
    {
        $grade = Grade::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $grade_title,
        ], []);

        $user->user_grade_id = $grade->id;
    }

    private function attachInvolvementByTitle($user, $title)
    {
        $parts = explode(' - ', $title);

        try {
            $category = Category::firstOrCreate([
                'account_id' => $this->account_id,
                'name'       => trim($parts[0]),
            ], []);

            $area = Area::firstOrCreate([
                'involvement_category_id' => $category->id,
                'name'                    => trim($parts[1]),
            ], []);
        } catch (\Exception $e) {
            $this->error('Could not attach involvement area: ' . $title);
            dd($title);
            return;
        }

        $user->involvementCategories()->attach($category->id, [
            'involvement_area_id' => $area->id,
        ]);
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 23;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
