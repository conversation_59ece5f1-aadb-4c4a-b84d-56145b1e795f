<?php

namespace App\Console\Commands\DataImport\Summerdale;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Finance\Services\CreateTransaction;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class ImportPastContributions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-contributions-summerdale {account_id} {sqlite_file_location}';

    protected $description = 'Import users from the SQLite database created from their CSV export file.';

    protected $account_id;
    protected $account;
    protected $sqlite_file_location;

    protected $member_group_id = null;

    protected $columns = [
        'TransactionNum',
        'FirstNames',
        'HouseholdName',
        'AmountGiven',
        'Category',
        'CheckNum',
        'DateGiven',
        'SubCategory',
        'lightpost_transaction_id',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id           = $this->argument('account_id');
        $this->sqlite_file_location = $this->argument('sqlite_file_location');

        if (!file_exists($this->sqlite_file_location)) {
            $this->error('SQLite file not found');
            return 1;
        }

        $this->info('Opening SQLite file for import...');

        // Add the temp connection
        Config::set("database.connections.dataimportsqlite", [
            'driver'   => 'sqlite',
            'database' => $this->sqlite_file_location,
            'prefix'   => '',
        ]);

        $this->account = Account::find($this->account_id);

        // Use the connection
        $sqlite = DB::connection('dataimportsqlite');

        // Get our family units
        $transactions = $sqlite->table('transactions')->orderBy('DateGiven')->get();

        // Get the default bucket
        $finance_bucket = FinanceBucket::where('account_id', $this->account_id)->orderBy('id', 'asc')->first();

        // Progress bar creation
        $this->info('Found ' . $transactions->count() . ' transactions to import...');
        $bar = $this->output->createProgressBar($transactions->count());

        // Wrap everything in a transaction
        DB::beginTransaction();

        try {
            $bar->start();

            // Go through each family.
            foreach ($transactions as $index => $transaction):
//                $bar->advance();

                // $first_name = explode(' ', $transaction->FirstNames)[0];
                $first_name = preg_split('/\s+|&|\bAnd\b|\bOr\b/i', $transaction->FirstNames)[0];

                $last_name = $transaction->HouseholdName;

                $user = null;
                $user = User::where('account_id', $this->account_id)
                    ->where('first_name', 'like', $first_name . '%')
                    ->where('last_name', 'like', $last_name . '%')
                    ->get();

                // If there's more than one user with the same name, skip it
                if ($user->count() > 1) {
//                    $this->newLine();
//                    $this->warn('Found multiple users with the same name. Skipping...');
                    echo $transaction->TransactionNum . ',' .
                         $transaction->FirstNames . ',' .
                         $transaction->HouseholdName . ',' .
                         $transaction->AmountGiven . ',' .
                         $transaction->Category . ',' .
                         $transaction->CheckNum . ',' .
                         $transaction->DateGiven . ',' .
                         $transaction->SubCategory . ',';
                    $this->newLine();
                    continue;
                } elseif ($user->count() === 0) {
//                    $this->newLine();
//                    $this->warn('Found no users with the same name. Skipping...');
                    echo $transaction->TransactionNum . ',' .
                         $transaction->FirstNames . ',' .
                         $transaction->HouseholdName . ',' .
                         $transaction->AmountGiven . ',' .
                         $transaction->Category . ',' .
                         $transaction->CheckNum . ',' .
                         $transaction->DateGiven . ',' .
                         $transaction->SubCategory . ',';
                    $this->newLine();
                    continue;
                } else {
                    // Get the user object
                    $user = $user->first();
                }

                // Get the transaction date into a Carbon object
                $transaction_date = Carbon::parse($transaction->DateGiven);

                $lightpost_transaction = (new CreateTransaction())
                    ->setTitle('Contribution')
                    ->isContribution()
                    ->setSource('check')
                    ->setPostedAt($transaction_date)
                    ->forAccount($this->account)
                    ->forUser($user)
                    ->setAmount($transaction->AmountGiven * 100) // Convert to cents
                    ->setAmountFee(0)
                    ->setFinanceBucket($finance_bucket)
                    ->setNotes('Original Name: ' . $transaction->FirstNames . ' ' . $transaction->HouseholdName . ' (' . $first_name . ' ' . $last_name . ')')
                    ->create([
                        'check_number' => $transaction->CheckNum,
                    ]);

                // Save our transaction ID back to the source.
                $sqlite->table('transactions')
                    ->where('HouseholdName', $transaction->HouseholdName)
                    ->where('AmountGiven', $transaction->AmountGiven)
                    ->where('DateGiven', $transaction->DateGiven)
                    ->update([
                        'lightpost_transaction_id' => $lightpost_transaction->id,
                    ]);

            endforeach;

            $bar->finish();

            DB::commit();
            $this->info('Import complete.');

            return 0;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Import failed: ' . $e->getMessage());
//            $this->error($e->getTraceAsString());
            dd($e);
            return 1;
        }
    }
}
