<?php

namespace App\Console\Commands\DataImport\WOChurch;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wo-church:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Last Name',
        2  => 'Preferred Name',
        3  => 'Zip Code',
        4  => 'Work Phone',
        5  => 'Work Email',
        6  => 'Wedding year',
        7  => 'Wedding Month and Day',
        8  => 'Wedding Month',
        9  => 'Visiting Who',
        10 => 'Visiting',
        11 => 'Title',
        12 => 'Suffix',
        13 => 'State',
        14 => 'Search Committee Member',
        15 => 'School Grade',
        16 => 'Relationship',
        17 => 'Out of Town Visitor',
        18 => 'Other Status',
        19 => 'Occupation',
        20 => 'NTLTC Parent',
        21 => 'Middle Name',
        22 => 'Member Status',
        23 => 'Marital Status',
        24 => 'Maiden Name',
        25 => 'Leadership Role',
        26 => 'Ladies\' Bible class',
        27 => 'Joined WOCC',
        28 => 'Include in Directory',
        29 => 'Important Event',
        30 => 'Home Phone',
        31 => 'Home Congregation',
        32 => 'Gender',
        33 => 'First Name',
        34 => 'Family ID',
        35 => 'Enabled Profile',
        36 => 'Employer',
        37 => 'E-Mail',
        38 => 'Elderly or Shut-in',
        39 => 'Elder',
        40 => 'Deacon',
        41 => 'Date of Death date',
        42 => 'Date of Death',
        43 => 'Date Background checked',
        44 => 'Country',
        45 => 'Contact',
        46 => 'City',
        47 => 'Child Abuse Form Signed',
        48 => 'Cell Phone Unlisted',
        49 => 'Cell Phone',
        50 => 'Birthdate Year',
        51 => 'Birthdate Month',
        52 => 'Birth Month and Day',
        53 => 'Birth Date',
        54 => 'Baptized Date',
        55 => 'Baptized By :',
        56 => 'Baptized',
        57 => 'Baptism Location :',
        58 => 'BACKGROUND CHECK DONE',
        59 => 'Attends Community Bible Class',
        60 => 'Anniversary Date',
        61 => 'Alternate Email',
        62 => 'Age',
        63 => 'Address Unlisted',
        64 => 'Address',
        65 => '1st Visit Date',
        66 => '# of Visits',
        67 => 'Allergy',
        68 => 'Individual Mailing List',
        69 => 'Willing to Serve',
        70 => 'Serve - Date',
        71 => 'Legal Guardians',
        72 => 'Comments for Involvement',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();


        $member_group_id        = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $former_member_group_id = Group::where('name', 'LIKE', 'Former Member')->where('account_id', $this->account_id)->first()->id;
        $elder_group_id         = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $deacon_group_id        = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $shutin_group_id        = Group::where('name', 'LIKE', 'Shut-ins')->where('account_id', $this->account_id)->first()->id;
        $hannahs_group_id       = Group::where('name', 'LIKE', 'Hannah\'s Hope')->where('account_id', $this->account_id)->first()->id;
        $visitor_group_id       = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;

        if (!$member_group_id || !$former_member_group_id || !$elder_group_id || !$deacon_group_id || !$shutin_group_id || !$hannahs_group_id || !$visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
        $elder_role_id  = Role::where('name', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $deacon_role_id = Role::where('name', 'Deacons')->where('account_id', $this->account_id)->first()->id;

        if (!$member_role_id || !$elder_role_id || !$deacon_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

//        $this->info($worksheet->getCellByColumnAndRow(27, 63)->getValue());
//        exit;

        // -----------------------------------------------------

        $last_family_id      = null;
        $last_family_user_id = null;
        $r                   = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Importing row ' . $r . '...');
            }

            $user = new User();

            // -----------------------------------------------------

            $user->account_id = $this->account_id;

            $user->last_name   = $worksheet->getCellByColumnAndRow(1, $r)->getValue();
            $user->middle_name = $worksheet->getCellByColumnAndRow(21, $r)->getValue();
            $user->first_name  = $worksheet->getCellByColumnAndRow(2, $r)->getValue();
            $user->employer    = $worksheet->getCellByColumnAndRow(36, $r)->getValue();
            $user->job_title   = $worksheet->getCellByColumnAndRow(19, $r)->getValue();
            $user->allergies   = $worksheet->getCellByColumnAndRow(67, $r)->getValue();
//            $user->member_by  = $worksheet->getCellByColumnAndRow(10, $r)->getValue();
            $user->status    = 'active';
            $user->is_active = true;
            $user->timezone  = 'America/Chicago';

            // Temp store our Family ID from the imported data in the notes field.
            $user->visitation_notes = $worksheet->getCellByColumnAndRow(34, $r)->getValue();

            // NOTES
            $notes = null;
            if ($worksheet->getCellByColumnAndRow(10, $r)->getValue()) {
                $notes .= 'Visiting Notes: ' . $worksheet->getCellByColumnAndRow(10, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(17, $r)->getValue()) {
                $notes .= 'Visiting Out of Town: ' . $worksheet->getCellByColumnAndRow(17, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(9, $r)->getValue()) {
                $notes .= 'Visiting Who: ' . $worksheet->getCellByColumnAndRow(9, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(31, $r)->getValue()) {
                $notes .= 'Home Congregation: ' . $worksheet->getCellByColumnAndRow(31, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(24, $r)->getValue()) {
                $notes .= 'Maiden Name: ' . $worksheet->getCellByColumnAndRow(24, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(43, $r)->getValue()) {
                $notes .= 'Date of Background Check: ' . $worksheet->getCellByColumnAndRow(43, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(58, $r)->getValue() && $worksheet->getCellByColumnAndRow(58, $r)->getValue() == 'Yes') {
                $notes .= 'Background Check Done: ' . $worksheet->getCellByColumnAndRow(58, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(47, $r)->getValue() && $worksheet->getCellByColumnAndRow(47, $r)->getValue() == 'Yes') {
                $notes .= 'Child Abuse Form Signed: ' . $worksheet->getCellByColumnAndRow(47, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(65, $r)->getValue() && $this->getDateArray($worksheet->getCellByColumnAndRow(65, $r)->getValue()) !== false) {
                $notes .= 'First Visit Date: ' . $worksheet->getCellByColumnAndRow(65, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(71, $r)->getValue()) {
                $notes .= 'Legal Guardians: ' . $worksheet->getCellByColumnAndRow(71, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(55, $r)->getValue() && $worksheet->getCellByColumnAndRow(55, $r)->getValue() != 'Unknown') {
                $notes .= 'Baptized by: ' . $worksheet->getCellByColumnAndRow(55, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(57, $r)->getValue()) {
                $notes .= 'Baptism Location: ' . $worksheet->getCellByColumnAndRow(57, $r)->getValue() . '
';
            }
            if ($worksheet->getCellByColumnAndRow(61, $r)->getValue()) {
                $notes .= 'Alternate Email: ' . $worksheet->getCellByColumnAndRow(61, $r)->getValue() . '
';
            }

            $user->notes = $notes;

            // Save our user and get an ID.
            $user->save();

            // -----------------------------------------------------

            // Figure out our family ID stuff.
            if ($last_family_id != $worksheet->getCellByColumnAndRow(34, $r)->getValue()) {
                $last_family_id      = $worksheet->getCellByColumnAndRow(34, $r)->getValue();
                $last_family_user_id = $user->id;
            }

            // Tie user to the raw Family ID from the imported data.
            $back_reference[$user->id] = $worksheet->getCellByColumnAndRow(34, $r)->getValue();

            // -----------------------------------------------------

            // Relationship -- Possible Values:  Spouse, Head of Household, Daughter, Son, Grandson, Granddaughter
            if (Str::contains('Head of Household', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                $user->family_id   = $user->id;
                $user->family_role = 'head';

                // See if family members have already been added and backfill them.
                foreach (User::where('account_id', $this->account_id)->where('visitation_notes', $worksheet->getCellByColumnAndRow(34, $r)->getValue())->whereNull('family_id')->get() as $fam_member) {
                    $fam_member->family_id = $user->id;
                    $fam_member->save();
                }
            } else {
                if (Str::contains('Spouse', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this above in the Family ID stuff.
                    $user->family_role = 'spouse';
                    $user->gender      = 'female';
                }
                if (Str::contains('Daughter', $worksheet->getCellByColumnAndRow(16, $r)->getValue()) || Str::contains('Son', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this above in the Family ID stuff.
                    $user->family_role = 'child';
                    if (Str::contains('Daughter', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                        $user->gender = 'female';
                    } else {
                        $user->gender = 'male';
                    }
                }
                if (Str::contains('Niece', $worksheet->getCellByColumnAndRow(16, $r)->getValue()) || Str::contains('Nephew', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this above in the Family ID stuff.
                    $user->family_role = 'child';
                    if (Str::contains('Niece', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                        $user->gender = 'female';
                    } else {
                        $user->gender = 'male';
                    }
                }
                if (Str::contains('Child', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this above in the Family ID stuff.
                    $user->family_role = 'child';
                }
                if (Str::contains('Grandson', $worksheet->getCellByColumnAndRow(16, $r)->getValue()) || Str::contains('Granddaughter', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                    // $user->family_id = $user->id; -- We do this above in the Family ID stuff.
                    // $user->family_role = 'child';
                    if (Str::contains('Granddaughter', $worksheet->getCellByColumnAndRow(16, $r)->getValue())) {
                        $user->gender = 'female';
                    } else {
                        $user->gender = 'male';
                    }
                }
                // Set our family ID
                $head_of_house = null;
                $head_of_house = User::where('account_id', $this->account_id)
                    ->where('visitation_notes', $worksheet->getCellByColumnAndRow(34, $r)->getValue())
                    ->whereNotNull('family_id')
                    ->whereRaw('id = family_id')
                    ->first();
                if ($head_of_house) {
                    $user->family_id = $head_of_house->family_id;
                }
            }

            // -----------------------------------------------------
            // ADDRESSES

            if ($user->family_id == $user->id && $worksheet->getCellByColumnAndRow(64, $r)->getValue() > '') {
                $address = Address::create([
                    'user_id'   => $user->id,
                    'type'      => 'home',
                    'label'     => null,
                    'address1'  => $worksheet->getCellByColumnAndRow(64, $r)->getValue() ?: '',
                    'city'      => $worksheet->getCellByColumnAndRow(46, $r)->getValue() ?: '',
                    'state'     => $worksheet->getCellByColumnAndRow(13, $r)->getValue() ?: '',
                    'zip'       => $worksheet->getCellByColumnAndRow(3, $r)->getValue() ?: '',
                    'country'   => $worksheet->getCellByColumnAndRow(44, $r)->getValue() ?: '',
                    'is_family' => ($user->family_id == $user->id ? true : false),
                ]);

                $address->user_id = $user->id;
                $address->is_family ? $address->family_id = $user->id : null;

                $address->save();
            }

            // -----------------------------------------------------
            // PHONES

            if ($user->family_id == $user->id && $worksheet->getCellByColumnAndRow(30, $r)->getValue() > '') {
                Phone::create([
                    'user_id'    => $user->id,
                    'family_id'  => $user->family_id,
                    'number'     => Phone::format($worksheet->getCellByColumnAndRow(30, $r)->getValue()),
                    'is_primary' => false,
                    'is_family'  => true,
                    'is_hidden'  => false,
                    'type'       => 'home',
                ]);
            }
            // Mobile
            if ($worksheet->getCellByColumnAndRow(49, $r)->getValue() > '' && $worksheet->getCellByColumnAndRow(49, $r)->getValue() != 'Unlisted') {
                Phone::create([
                    'user_id'    => $user->id,
                    'number'     => Phone::format($worksheet->getCellByColumnAndRow(49, $r)->getValue()),
                    'is_primary' => true,
                    'is_family'  => false,
                    'is_hidden'  => false,
                    'type'       => 'mobile',
                ]);
            }
            if ($worksheet->getCellByColumnAndRow(4, $r)->getValue() > '' && !Phone::where('number', Phone::format($worksheet->getCellByColumnAndRow(4, $r)->getValue()))->exists()) {
                Phone::create([
                    'user_id'    => $user->id,
                    'number'     => Phone::format($worksheet->getCellByColumnAndRow(4, $r)->getValue()),
                    'is_primary' => false,
                    'is_family'  => false,
                    'is_hidden'  => false,
                    'type'       => 'work',
                ]);
            }

            // -----------------------------------------------------
            // EMAILS

            if ($worksheet->getCellByColumnAndRow(37, $r)->getValue() > '' && !Email::where('email', $worksheet->getCellByColumnAndRow(37, $r)->getValue())->exists()) {
                Email::create([
                    'user_id'               => $user->id,
                    'email'                 => $worksheet->getCellByColumnAndRow(37, $r)->getValue(),
                    'is_primary'            => true,
                    'is_family'             => false,
                    'is_hidden'             => false,
                    'type'                  => 'personal',
                    'receives_group_emails' => true,
                ]);
            } elseif (Email::where('email', $worksheet->getCellByColumnAndRow(37, $r)->getValue())->exists()) {
                $temp_email             = Email::where('email', $worksheet->getCellByColumnAndRow(37, $r)->getValue())->first();
                $temp_email->is_family  = true;
                $temp_email->is_primary = false;
                $temp_email->type       = 'family';
                $temp_email->save();
            }
            if ($worksheet->getCellByColumnAndRow(5, $r)->getValue() > '') {
                Email::create([
                    'user_id'               => $user->id,
                    'email'                 => $worksheet->getCellByColumnAndRow(5, $r)->getValue(),
                    'is_primary'            => false,
                    'is_family'             => false,
                    'is_hidden'             => true,
                    'type'                  => 'work',
                    'receives_group_emails' => true,
                ]);
            }

            // -----------------------------------------------------

            // Save our progress
            $user->save();

            // -----------------------------------------------------

            // Deceased
            if (Str::contains('Deceased', $worksheet->getCellByColumnAndRow(18, $r)->getValue()) ||
                Str::contains('Yes', $worksheet->getCellByColumnAndRow(42, $r)->getValue())) {
                $date_deceased              = $this->getDateArray($worksheet->getCellByColumnAndRow(41, $r)->getValue());
                $user->date_deceased        = !$date_deceased ? now() : Carbon::create($date_deceased['year'], $date_deceased['month'], $date_deceased['day']);
                $user->status               = 'inactive';
                $user->exclude_from_reports = true;
            }

            // -----------------------------------------------------

            // Member Groups AND Roles
            if (!$user->date_deceased) {
                if (!$worksheet->getCellByColumnAndRow(18, $r)->getValue() || $worksheet->getCellByColumnAndRow(18, $r)->getValue() == '' ||
                    Str::contains('Child of a Member', $worksheet->getCellByColumnAndRow(18, $r)->getValue()) ||
                    Str::contains('Spouse of', $worksheet->getCellByColumnAndRow(18, $r)->getValue())) {
                    $user->groups()->attach($member_group_id);
                    $user->roles()->attach($member_role_id);
                }
                // Former Member Group
                if (Str::contains('Former Member', $worksheet->getCellByColumnAndRow(18, $r)->getValue())) {
                    $user->groups()->attach($former_member_group_id);
                }
                // Shut-In Group
                if (Str::contains('Yes', $worksheet->getCellByColumnAndRow(38, $r)->getValue())) {
                    $user->groups()->attach($shutin_group_id);
                }
                // Hannah's Hope Group
                if (Str::contains('Hannah\'s Hope', $worksheet->getCellByColumnAndRow(68, $r)->getValue())) {
                    $user->groups()->attach($hannahs_group_id);
                }
                // Visitor Group
                if (Str::contains('Visitor', $worksheet->getCellByColumnAndRow(18, $r)->getValue())) {
                    $user->groups()->attach($visitor_group_id);
                }
                // Elder Group
                if ($worksheet->getCellByColumnAndRow(39, $r)->getValue() == 'Yes') {
                    $user->groups()->attach($elder_group_id);
                    $user->roles()->attach($elder_role_id);
                }
                // Deacon Group
                if ($worksheet->getCellByColumnAndRow(40, $r)->getValue() == 'Yes') {
                    $user->groups()->attach($deacon_group_id);
                    $user->roles()->attach($deacon_role_id);
                }
            }

            // -----------------------------------------------------


            // Wedding Date
            $date_married = $this->getDateArray($worksheet->getCellByColumnAndRow(7, $r)->getValue() . '/' . $worksheet->getCellByColumnAndRow(6, $r)->getValue());
            if ($date_married) {
                $user->date_married = Carbon::create($date_married['year'], $date_married['month'], $date_married['day']);
            }
            // Birthdate
            $birthdate = $this->getDateArray($worksheet->getCellByColumnAndRow(53, $r)->getValue());
            if ($birthdate) {
//                $this->info(print_r($birthdate, true));
                $user->birthdate = Carbon::create($birthdate['year'], $birthdate['month'], $birthdate['day']);
            }
            // Membership
            $date_membership = $this->getDateArray($worksheet->getCellByColumnAndRow(27, $r)->getValue());
            if ($date_membership) {
                $user->date_membership = Carbon::create($date_membership['year'], $date_membership['month'], $date_membership['day']);
            }

            // Baptism
            $date_baptism = $this->getDateArray($worksheet->getCellByColumnAndRow(54, $r)->getValue());
            if ($date_baptism) {
//                $this->info(print_r($date_baptism, true));
                $user->date_baptism = Carbon::create($date_baptism['year'], $date_baptism['month'], $date_baptism['day']);
                $user->is_baptized  = Carbon::now();
            } elseif ($worksheet->getCellByColumnAndRow(56, $r)->getValue() == 'Yes') {
                $user->date_baptism = Carbon::now();
                $user->is_baptized  = Carbon::now();
            }

            // -----------------------------------------------------

            // Save our progress
            $user->save();

            $this->info('Imported user ' . $user->name);
            $this->info('---------------------------');

            $r++;
        }

        $this->info('Removing Family ID from notes...');

        foreach (User::where('account_id', $this->account_id)->get() as $temp_user) {
            $temp_user->visitation_notes = '';
            $temp_user->save();
        }

        $this->info('User import complete.');
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 2021;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => intval($year),
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
