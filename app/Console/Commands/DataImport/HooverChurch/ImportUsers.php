<?php

namespace App\Console\Commands\DataImport\HooverChurch;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hoover-church:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Household ID',
        2  => 'Contact ID',
        3  => 'Title',
        4  => 'Salutation',
        5  => 'Salutation by Last Name',
        6  => 'First Name',
        7  => 'Middle Name',
        8  => 'Last Name',
        9  => 'Suffix',
        10 => 'Full Address',
        11 => 'Mailing Address',
        12 => 'Street',
        13 => 'City',
        14 => 'State',
        15 => 'Zip',
        16 => 'IMBC',
        17 => 'Country',
        18 => 'Default Phone',
        19 => 'All Phones',
        20 => 'Default Email',
        21 => 'Kids Notes',
        22 => 'Birthdate',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = 19; // $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $member_role_id  = optional(Role::where('name', 'Member')->where('account_id', $this->account_id)->first())->id;
        $member_group_id = optional(Group::where('name', 'Member')->where('account_id', $this->account_id)->first())->id;

        // -----------------------------------------------------

        $families_array = [];

        $this->info('Putting users into families...');

        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Getting row ' . $r . ' data...');
            }

            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);

            $row_array = [];
            foreach ($cellIterator as $cell) {
                $row_array[] = $cell->getValue();
            }

            $families_array[$row_array[0]][] = $row_array;
        }

        foreach ($families_array as $family_array) {
            // Now we have all the family members
            $this->info('Importing ' . $family_array[0][7] . '... ' . count($family_array) . ' family members total.');

            // Order our family by who was added first in the old system.
            $family_array = Arr::sort($family_array, function ($value) {
                return $value[1];
            });

            $new_family_id = null;

            $i = 0;
            foreach ($family_array as $family_member) {

                $user = new User();

                $user->account_id  = $this->account_id;
                $user->last_name   = $family_member[7];
                $user->middle_name = $family_member[6];
                $user->first_name  = $family_member[5];

                $user->status    = 'active';
                $user->is_active = true;
                $user->timezone  = 'America/Chicago';

                // Save our user and get an ID.
                $user->save();

                $user->groups()->attach($member_group_id);
                $user->roles()->attach($member_role_id);

                $this->info('Imported user ' . $user->name);

                // Save our user and get an ID.
                $user->save();

                // Set our family_id
                if ($new_family_id == null) {
                    $user->family_role = 'head';
                    $user->family_id   = $user->id;
                    $user->save();

                    $new_family_id = $user->id;
                } else {
                    $user->family_id = $new_family_id;
                    $user->save();
                }

                // Birthday
                if ($family_member[21]) {
                    $user->birthdate = Carbon::createFromFormat('n/j/Y g:i:s A', trim($family_member[21]))->format('Y-m-d');
                    $user->save();
                }

                // If this is our first family member, and there is an address, add the address.
                if ($i == 0 && $family_member[11] && $family_member[12]) {
                    $address = new Address();

                    $address->created_at = now();
                    $address->type       = 'home';
                    $address->label      = 'Home Address';
                    $address->address1   = $family_member[11];
                    $address->city       = $family_member[12];
                    $address->state      = $family_member[13];
                    $address->zip        = $family_member[14];
                    $address->country    = 'US';
                    $address->user_id    = $user->id;

                    $address->is_family = 1;
                    $address->family_id = $new_family_id;

                    $address->save();
                }

                // PHONES
                if ($family_member[18]) {
                    foreach (preg_split('/\r\n|\r|\n/', $family_member[18]) as $current_number_string) {
                        $values_array = explode(':', $current_number_string);
                        $type         = $values_array[0];
                        $raw_number   = $values_array[1];

                        // home, mobile, work, other
                        if (Str::contains('Home', $type)) {
                            $new_type = 'home';
                        } elseif (Str::contains('Mobile', $type)) {
                            $new_type = 'mobile';
                        } elseif (Str::contains('Work', $type)) {
                            $new_type = 'work';
                        } else {
                            $new_type = 'other';
                        }

                        // First make sure we haven't added it already.
                        if (!Phone::where('number', Phone::format($raw_number))->first()) {
                            $phone = new Phone();

                            $phone->created_at       = now();
                            $phone->number           = Phone::format($raw_number);
                            $phone->is_primary       = $new_type == 'mobile' ? 1 : 0;
                            $phone->is_family        = $new_type == 'home' ? 1 : 0;
                            $phone->is_hidden        = 0;
                            $phone->messages_opt_out = $new_type == 'home' ? 1 : 0;
                            $phone->type             = $new_type;
                            $phone->number           = Phone::format($phone->number);
                            $phone->user_id          = $user->id;

                            if ($new_type == 'home') {
                                $phone->family_id = $user->family_id;
                            }

                            $phone->save();
                        }
                    }
                }

                if ($family_member[19]) {
                    if (!Email::where('email', trim($family_member[19]))->first()) {
                        $email = new Email();

                        $email->email                 = trim($family_member[19]);
                        $email->created_at            = now();
                        $email->user_id               = $user->id;
                        $email->type                  = 'personal';
                        $email->is_primary            = 1;
                        $email->receives_group_emails = 1;
                        $email->allow_messages        = 1;

                        $email->save();
                    }
                }


                $i++;
            }
        }

        $this->info('User import complete.');
    }
}
