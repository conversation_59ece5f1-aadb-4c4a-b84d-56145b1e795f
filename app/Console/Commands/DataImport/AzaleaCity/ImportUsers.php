<?php

namespace App\Console\Commands\DataImport\AzaleaCity;

use App\Accounts\Account;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-azalea-city {account_id} {sqlite_file_location} {gender_api_key?}';

    protected $description = 'Import users from the SQLite database created from their CSV export file.';

    protected $account_id;
    protected $excel_file_location;

    protected $member_group_id  = null;
    protected $elder_group_id   = null;
    protected $deacon_group_id  = null;
    protected $visitor_group_id = null;
    protected $member_role_id   = null;

    protected $columns = [
        'FamilyId',
        'LastName',
        'AdultFirstNames',
        'Address',
        'Address2',
        'City',
        'State',
        'Zip',
        'FamilyPhone',
        'FamilyEmail',
        'ImageId',
        'AdditionalDetails',
        'Notes',
        'AnniversaryDate',
        'IsAddressPrivate',
        'DateCreatedFamily',
        'DateModifiedFamily',
        // -----------------------------------------------------
        'Adult1PersonId',
        'Adult1AltLastName',
        'Adult1PersonType',
        'Adult1FirstName',
        'Adult1Birthday',
        'Adult1BirthYear',
        'Adult1DisplayBirthdayInPdf',
        'Adult1EmailAddress',
        'Adult1AllowEmailMessage',
        'Adult1MobilePhoneNumber',
        'Adult1AllowTextMessage',
        'Adult1IsEmailPrivate',
        'Adult1IsMobilePhonePrivate',
        'Adult1IsMemberOfChurch',
        'Adult1IsPictured',
        'Adult1SortOrder',
        'Adult1DateCreated',
        'Adult1DateModified',
        // -----------------------------------------------------
        'Adult2PersonId',
        'Adult2AltLastName',
        'Adult2PersonType',
        'Adult2FirstName',
        'Adult2Birthday',
        'Adult2BirthYear',
        'Adult2DisplayBirthdayInPdf',
        'Adult2EmailAddress',
        'Adult2AllowEmailMessage',
        'Adult2MobilePhoneNumber',
        'Adult2AllowTextMessage',
        'Adult2IsEmailPrivate',
        'Adult2IsMobilePhonePrivate',
        'Adult2IsMemberOfChurch',
        'Adult2IsPictured',
        'Adult2SortOrder',
        'Adult2DateCreated',
        'Adult2DateModified',
        // -----------------------------------------------------
        'Child1PersonId',
        'Child1AltLastName',
        'Child1PersonType',
        'Child1FirstName',
        'Child1Birthday',
        'Child1BirthYear',
        'Child1DisplayBirthdayInPdf',
        'Child1EmailAddress',
        'Child1AllowEmailMessage',
        'Child1MobilePhoneNumber',
        'Child1AllowTextMessage',
        'Child1IsEmailPrivate',
        'Child1IsMobilePhonePrivate',
        'Child1IsMemberOfChurch',
        'Child1IsPictured',
        'Child1SortOrder',
        'Child1DateCreated',
        'Child1DateModified',
        // -----------------------------------------------------
        'Child2PersonId',
        'Child2AltLastName',
        'Child2PersonType',
        'Child2FirstName',
        'Child2Birthday',
        'Child2BirthYear',
        'Child2DisplayBirthdayInPdf',
        'Child2EmailAddress',
        'Child2AllowEmailMessage',
        'Child2MobilePhoneNumber',
        'Child2AllowTextMessage',
        'Child2IsEmailPrivate',
        'Child2IsMobilePhonePrivate',
        'Child2IsMemberOfChurch',
        'Child2IsPictured',
        'Child2SortOrder',
        'Child2DateCreated',
        'Child2DateModified',
        // -----------------------------------------------------
        'Child3PersonId',
        'Child3AltLastName',
        'Child3PersonType',
        'Child3FirstName',
        'Child3Birthday',
        'Child3BirthYear',
        'Child3DisplayBirthdayInPdf',
        'Child3EmailAddress',
        'Child3AllowEmailMessage',
        'Child3MobilePhoneNumber',
        'Child3AllowTextMessage',
        'Child3IsEmailPrivate',
        'Child3IsMobilePhonePrivate',
        'Child3IsMemberOfChurch',
        'Child3IsPictured',
        'Child3SortOrder',
        'Child3DateCreated',
        'Child3DateModified',
        // -----------------------------------------------------
        'Child4PersonId',
        'Child4AltLastName',
        'Child4PersonType',
        'Child4FirstName',
        'Child4Birthday',
        'Child4BirthYear',
        'Child4DisplayBirthdayInPdf',
        'Child4EmailAddress',
        'Child4AllowEmailMessage',
        'Child4MobilePhoneNumber',
        'Child4AllowTextMessage',
        'Child4IsEmailPrivate',
        'Child4IsMobilePhonePrivate',
        'Child4IsMemberOfChurch',
        'Child4IsPictured',
        'Child4SortOrder',
        'Child4DateCreated',
        'Child4DateModified',
        'Child5PersonId',
        // -----------------------------------------------------
        'Child5AltLastName',
        'Child5PersonType',
        'Child5FirstName',
        'Child5Birthday',
        'Child5BirthYear',
        'Child5DisplayBirthdayInPdf',
        'Child5EmailAddress',
        'Child5AllowEmailMessage',
        'Child5MobilePhoneNumber',
        'Child5AllowTextMessage',
        'Child5IsEmailPrivate',
        'Child5IsMobilePhonePrivate',
        'Child5IsMemberOfChurch',
        'Child5IsPictured',
        'Child5SortOrder',
        'Child5DateCreated',
        'Child5DateModified',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id           = $this->argument('account_id');
        $this->sqlite_file_location = $this->argument('sqlite_file_location');

        if (!file_exists($this->sqlite_file_location)) {
            $this->error('SQLite file not found');
            return 1;
        }

        $this->info('Opening SQLite file for import...');

        // Add the temp connection
        Config::set("database.connections.data-import-sqlite", [
            'driver'   => 'sqlite',
            'database' => $this->sqlite_file_location,
            'prefix'   => '',
        ]);

        $this->account = Account::find($this->account_id);

        // If our account is different than the default.
        $this->timezone = $this->account->timezone;

        // Use the connection
        $sqlite = DB::connection('data-import-sqlite');

        // Get our family units
        $families = $sqlite->table('users')->orderBy('LastName')->get();

        // Progress bar creation
        $this->info('Found ' . $families->count() . ' families to import...');
        $bar = $this->output->createProgressBar($families->count());

        // Wrap everything in a transaction
        DB::beginTransaction();

        try {
            $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()?->id;
            $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()?->id;
            $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()?->id;

            if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
                $this->error('Could not find all groups listed.');
                exit;
            }

            $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
            $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
            $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

            if (!$this->member_role_id) {
                $this->error('Could not find all roles listed.');
                exit;
            }

            $bar->start();

            // Go through each family.
            foreach ($families as $index => $family) {
                $family_id        = null;
                $family_is_member = false;
                $head_is_deceased = false;

                $family_id = $family->FamilyId;

                $user1 = null;
                $user2 = null;
                $user3 = null;
                $user4 = null;
                $user5 = null;
                $user6 = null;
                $user7 = null;
                $user8 = null;
                $user9 = null;

                $user1 = [
                    'import_family_id' => $family->FamilyId,
                    'family_role'      => 'head',
                    'public_token'     => $family->Adult1PersonId,
                    'first_name'       => $family->Adult1FirstName ?: '[unknown]',
                    'last_name'        => $family->Adult1AltLastName ?: $family->LastName,
                    'date_married'     => $family->AnniversaryDate,
                    'gender'           => 'male',
                    'email'            => $family->Adult1EmailAddress,
                    'home_phone'       => $family->FamilyPhone,
                    'cell_phone'       => $family->Adult1MobilePhoneNumber,
                    'allow_sms'        => $family->Adult1AllowTextMessage,
                    'address1'         => $family->Address,
                    'address2'         => $family->Address2,
                    'city'             => $family->City,
                    'state'            => $family->State,
                    'postal_code'      => $family->Zip,
                    'country'          => 'US',
                    'birth_month_day'  => $family->Adult1Birthday,
                    'birth_year'       => $family->Adult1BirthYear ?: 1000,
                    'is_baptized'      => $family->Adult1IsMemberOfChurch,
                    'marital_status'   => ($family->Adult1PersonId && $family->Adult2PersonId) ? 'married' : 'single',
                    'adult_sort_id'    => $family->Adult1SortOrder,
                ];

                $user2 = null;
                if (property_exists($family, 'Adult2PersonId') && $family->Adult2PersonId) {
                    $user2 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'spouse',
                        'public_token'     => $family->Adult2PersonId,
                        'first_name'       => $family->Adult2FirstName ?: '[unknown]',
                        'last_name'        => $family->Adult2AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'female',
                        'email'            => $family->Adult2EmailAddress,
                        'cell_phone'       => $family->Adult2MobilePhoneNumber,
                        'allow_sms'        => $family->Adult2AllowTextMessage,
                        'birth_month_day'  => $family->Adult2Birthday,
                        'birth_year'       => $family->Adult2BirthYear ?: 1000,
                        'is_baptized'      => $family->Adult2IsMemberOfChurch,
                        'marital_status'   => ($family->Adult1PersonId && $family->Adult2PersonId) ? 'married' : 'single',
                        'adult_sort_id'    => $family->Adult2SortOrder,
                    ];
                }

                $user3 = null;
                if (property_exists($family, 'Child1PersonId') && $family->Child1PersonId) {
                    $user3 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'child',
                        'public_token'     => $family->Child1PersonId,
                        'first_name'       => $family->Child1FirstName ?: '[unknown]',
                        'last_name'        => $family->Child1AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'male',
                        'email'            => $family->Child1EmailAddress,
                        'cell_phone'       => $family->Child1MobilePhoneNumber,
                        'allow_sms'        => $family->Child1AllowTextMessage,
                        'birth_month_day'  => $family->Child1Birthday,
                        'birth_year'       => $family->Child1BirthYear ?: 1000,
                        'is_baptized'      => $family->Child1IsMemberOfChurch,
                        'marital_status'   => 'single',
                        'adult_sort_id'    => $family->Child1SortOrder,
                    ];
                }

                $user4 = null;
                if (property_exists($family, 'Child2PersonId') && $family->Child2PersonId) {
                    $user4 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'child',
                        'public_token'     => $family->Child2PersonId,
                        'first_name'       => $family->Child2FirstName ?: '[unknown]',
                        'last_name'        => $family->Child2AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'male',
                        'email'            => $family->Child2EmailAddress,
                        'cell_phone'       => $family->Child2MobilePhoneNumber,
                        'allow_sms'        => $family->Child2AllowTextMessage,
                        'birth_month_day'  => $family->Child2Birthday,
                        'birth_year'       => $family->Child2BirthYear ?: 1000,
                        'is_baptized'      => $family->Child2IsMemberOfChurch,
                        'marital_status'   => 'single',
                        'child_sort_id'    => $family->Child2SortOrder,
                    ];
                }

                $user5 = null;
                if (property_exists($family, 'Child3PersonId') && $family->Child3PersonId) {
                    $user5 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'child',
                        'public_token'     => $family->Child3PersonId,
                        'first_name'       => $family->Child3FirstName ?: '[unknown]',
                        'last_name'        => $family->Child3AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'male',
                        'email'            => $family->Child3EmailAddress,
                        'cell_phone'       => $family->Child3MobilePhoneNumber,
                        'allow_sms'        => $family->Child3AllowTextMessage,
                        'birth_month_day'  => $family->Child3Birthday,
                        'birth_year'       => $family->Child3BirthYear ?: 1000,
                        'is_baptized'      => $family->Child3IsMemberOfChurch,
                        'marital_status'   => 'single',
                        'child_sort_id'    => $family->Child3SortOrder,
                    ];
                }

                $user6 = null;
                if (property_exists($family, 'Child4PersonId') && $family->Child4PersonId) {
                    $user6 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'child',
                        'public_token'     => $family->Child4PersonId,
                        'first_name'       => $family->Child4FirstName ?: '[unknown]',
                        'last_name'        => $family->Child4AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'male',
                        'email'            => $family->Child4EmailAddress,
                        'cell_phone'       => $family->Child4MobilePhoneNumber,
                        'allow_sms'        => $family->Child4AllowTextMessage,
                        'birth_month_day'  => $family->Child4Birthday,
                        'birth_year'       => $family->Child4BirthYear ?: 1000,
                        'is_baptized'      => $family->Child4IsMemberOfChurch,
                        'marital_status'   => 'single',
                        'child_sort_id'    => $family->Child4SortOrder,
                    ];
                }

                $user7 = null;
                if (property_exists($family, 'Child5PersonId') && $family->Child5PersonId) {
                    $user7 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'child',
                        'public_token'     => $family->Child5PersonId,
                        'first_name'       => $family->Child5FirstName ?: '[unknown]',
                        'last_name'        => $family->Child5AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'male',
                        'email'            => $family->Child5EmailAddress,
                        'cell_phone'       => $family->Child5MobilePhoneNumber,
                        'allow_sms'        => $family->Child5AllowTextMessage,
                        'birth_month_day'  => $family->Child5Birthday,
                        'birth_year'       => $family->Child5BirthYear ?: 1000,
                        'is_baptized'      => $family->Child5IsMemberOfChurch,
                        'marital_status'   => 'single',
                        'child_sort_id'    => $family->Child5SortOrder,
                    ];
                }

                $user8 = null;
                if (property_exists($family, 'Child6PersonId') && $family->Child6PersonId) {
                    $user8 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'child',
                        'public_token'     => $family->Child6PersonId,
                        'first_name'       => $family->Child6FirstName ?: '[unknown]',
                        'last_name'        => $family->Child6AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'male',
                        'email'            => $family->Child6EmailAddress,
                        'cell_phone'       => $family->Child6MobilePhoneNumber,
                        'allow_sms'        => $family->Child6AllowTextMessage,
                        'birth_month_day'  => $family->Child6Birthday,
                        'birth_year'       => $family->Child6BirthYear ?: 1000,
                        'is_baptized'      => $family->Child6IsMemberOfChurch,
                        'marital_status'   => 'single',
                        'child_sort_id'    => $family->Child6SortOrder,
                    ];
                }

                $user9 = null;
                if (property_exists($family, 'Child7PersonId') && $family->Child7PersonId) {
                    $user9 = [
                        'import_family_id' => $family->FamilyId,
                        'family_role'      => 'child',
                        'public_token'     => $family->Child7PersonId,
                        'first_name'       => $family->Child7FirstName ?: '[unknown]',
                        'last_name'        => $family->Child7AltLastName ?: $family->LastName,
                        'date_married'     => $family->AnniversaryDate,
                        'gender'           => 'male',
                        'email'            => $family->Child7EmailAddress,
                        'cell_phone'       => $family->Child7MobilePhoneNumber,
                        'allow_sms'        => $family->Child7AllowTextMessage,
                        'birth_month_day'  => $family->Child7Birthday,
                        'birth_year'       => $family->Child7BirthYear ?: 1000,
                        'is_baptized'      => $family->Child7IsMemberOfChurch,
                        'marital_status'   => 'single',
                        'child_sort_id'    => $family->Child7SortOrder,
                    ];
                }

                $family_user = null;

                // If we're clearly the first adult, or we're marked #2, but there is no second adult.
                if ($user1['adult_sort_id'] == 1 || !$family->Adult2PersonId) {
                    $family_user = $this->addUser($user1);

                    $family_user->family_id = $family_user->id;
                    $family_user->save();

                    if ($user2) {
                        $user2['family_id'] = $family_user->id;

                        $this->addUser($user2);
                    }
                } else {
                    // Switch our roles.
                    $user2['family_role'] = 'head';
                    $family_user          = $this->addUser($user2);

                    // Save our head family_id
                    $family_user->family_id = $family_user->id;
                    $family_user->save();

                    $user1['family_role'] = 'spouse';
                    $user1['family_id']   = $family_user->id;

                    $this->addUser($user1);
                }

                $family_user->notes = $family->Notes;
                $family_user->save();

                // Add our children, if they exist
                for ($i = 3; $i <= 9; $i++) {
                    $userChild = "user{$i}";
                    if (${$userChild} && ${$userChild}['public_token']) {
                        ${$userChild}['family_id'] = $family_user->family_id;

                        $this->addUser(${$userChild});
                    }
                }

                // Add family info

                // Home Phone
                if (Arr::get($user1, 'home_phone')) {
                    $phone = Phone::firstOrCreate([
                        'number' => Phone::format(Arr::get($user1, 'home_phone')),
                    ], [
                        'user_id'    => $family_user->id,
                        'number'     => Phone::format(Arr::get($user1, 'home_phone')),
                        'is_primary' => false,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }

                // -----------------------------------------------------
                // HOME ADDRESS

                if (!empty(Arr::get($user1, 'address1')) && !empty(Arr::get($user1, 'city'))) {
                    $address = Address::firstOrCreate([
                        'address1' => Arr::get($user1, 'address1'),
                        'city'     => Arr::get($user1, 'city'),
                    ], [
                        'user_id'   => $family_user->id,
                        'family_id' => $family_user->family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => Arr::get($user1, 'address1'),
                        'address2'  => Arr::get($user1, 'address2'),
                        'city'      => Arr::get($user1, 'city'),
                        'state'     => Arr::get($user1, 'state'),
                        'zip'       => Arr::get($user1, 'postal_code'),
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }

                $bar->advance();
            } // End foreach($families)

            $bar->finish();
            // -----------------------------------------------------

            DB::commit();
            $this->info('Import complete.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Import failed: ' . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }

    private function addUser($member)
    {
        $user = new User();

        // -----------------------------------------------------

        $user->account_id = $this->account_id;

        $user->family_id      = array_key_exists('family_id', $member) ? $member['family_id'] : null;
        $user->first_name     = $member['first_name'];
        $user->last_name      = $member['last_name'];
        $user->gender         = $this->getGenderForName($member['first_name']); // strtolower($member['gender']) ?: 'male';
        $user->status         = 'active';
        $user->is_active      = true;
        $user->family_role    = Arr::get($member, 'family_role');
        $user->timezone       = $this->timezone;
        $user->public_token   = $member['public_token'] ?: Str::uuid();
        $user->notes          = Arr::get($member, 'notes');
        $user->is_baptized    = Arr::get($member, 'is_baptized') == 'TRUE' ? now() : null;
        $user->marital_status = Arr::get($member, 'marital_status');

        // Birthdate Date
        if (Arr::get($member, 'birth_month_day')) {
            $temp_date = $this->getDateArray(Arr::get($member, 'birth_month_day'));
            if ($temp_date && $temp_date !== '/  /') {
                $user->birthdate = Carbon::create(Arr::get($member, 'birth_year') ?: $temp_date['year'], $temp_date['month'], $temp_date['day']);
            }
        }
        // Anniversary
        if (Arr::get($member, 'date_married')) {
            $temp_date = $this->getDateArray(Arr::get($member, 'date_married'));
            if ($temp_date && $temp_date !== '/  /') {
                $user->date_married = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
            }
        }

        // Save our user and get an ID.
        $user->save();

        $user->generateUlid();

        // Save our progress
        $user->save();

        // -----------------------------------------------------
        // PHONES

        // Mobile
        if (Arr::get($member, 'cell_phone')) {
            $phone = Phone::firstOrCreate([
                'number' => Phone::format(Arr::get($member, 'cell_phone')),
            ], [
                'user_id'          => $user->id,
                'number'           => Phone::format(Arr::get($member, 'cell_phone')),
                'messages_opt_out' => Arr::get($member, 'allow_sms') == 'TRUE' ? false : true,
                'is_primary'       => true,
                'is_family'        => false,
                'is_hidden'        => false,
                'type'             => 'mobile',
            ]);
        }

        // -----------------------------------------------------
        // EMAILS

        $email = null;
        $email = Arr::get($member, 'email');
        if (!empty($email)) {
            $email = Email::firstOrCreate([
                'email' => strtolower($email),
            ], [
                'user_id'               => $user->id,
                'email'                 => strtolower($email),
                'is_primary'            => true,
                'is_family'             => false,
                'is_hidden'             => false,
                'type'                  => 'personal',
                'receives_group_emails' => true,
            ]);
        }

        // -----------------------------------------------------

        // Save our progress
        $user->save();

        // -----------------------------------------------------

        // Groups
        $user->groups()->attach($this->member_group_id);
        $user->roles()->attach($this->member_role_id);

        // -----------------------------------------------------

        // Save our progress
        $user->save();

        return $user;
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'STATUS Members') {
                    continue;
                } elseif ($group_string == 'ADMINISTRATION') {
                    $actual_group_name = 'Admin';
                } else {
                    $name_split = explode(' ', $group_string, 2);

                    $actual_group_name = $name_split[1];
                }
            } catch (\Exception $e) {
                dd($groups, $group_string);
            }

            $this->attachGroupByName($new_user_record, $actual_group_name);
        }
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        if (DB::table('user_to_group')->where('user_id', $user->id)->where('user_group_id', $group_id)->exists()) {
            return;
        } else {
            $user->groups()->attach($group_id);
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }

    function getGenderForName($name)
    {
        $apiKey = $this->argument('gender_api_key');                        // Replace with your OpenAI API key
        $apiUrl = 'https://api.openai.com/v1/chat/completions';             // OpenAI Chat API endpoint

        if (!$apiKey) {
            $apiKey = config('services.openai.api_key');
        }

        // Instantiate Guzzle HTTP client
        $client = new Client();

        try {
            // Make the API request
            $response = $client->post($apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type'  => 'application/json',
                ],
                'json'    => [
                    'model'       => 'gpt-4o-mini', // Specify the model
                    'messages'    => [
                        [
                            'role'    => 'system',
                            'content' => 'You are a helpful assistant that identifies the likely gender of a name.',
                        ],
                        [
                            'role'    => 'user',
                            'content' => "What is the gender typically associated with the name \"$name\"? Respond with only \"male\" or \"female\".",
                        ],
                    ],
                    'max_tokens'  => 1, // Keep the response concise
                    'temperature' => 0, // Ensure deterministic responses
                ],
            ]);

            // Parse the JSON response
            $data = json_decode($response->getBody(), true);

            return $data['choices'][0]['message']['content'] == 'male' ? 'male' : 'female';
        } catch (\Exception $e) {
            // Handle errors
            return 'Error: ' . $e->getMessage();
        }
    }

    private function getGender($name)
    {
        $client = new Client();

        // Limit of 100 API requests, even though docs say 1,0000
        $response = $client->request('GET', 'https://genderizeio.p.rapidapi.com/?name=' . $name, [
            'headers' => [
                'x-rapidapi-host' => 'genderizeio.p.rapidapi.com',
                'x-rapidapi-key'  => $this->argument('gender_api_key'),
            ],
        ]);

        return trim($response->getBody()->getContents() == 'male' ? 'male' : 'female');
    }

    private function getGender2($name)
    {
        $client = new Client();

        $response = $client->request('GET', 'https://gender-from-name.p.rapidapi.com/gender/' . $name, [
            'headers' => [
                'x-rapidapi-host' => 'gender-from-name.p.rapidapi.com',
                'x-rapidapi-key'  => $this->argument('gender_api_key'),
            ],
        ]);

        return trim($response->getBody()->getContents() == 'F' ? 'female' : 'male');
    }

    private function getGender3($name)
    {
        $apiKey = $this->argument('gender_api_key');
        $url    = "https://api.genderize.io?name=" . urlencode($name) . "&apikey=" . $apiKey;

        if (!$apiKey) {
            return 'male';
        }

        $response = file_get_contents($url);
        $data     = json_decode($response, true);

        if (isset($data['gender'])) {
            return $data['gender'];
        } else {
            return 'male';
        }
    }
}
