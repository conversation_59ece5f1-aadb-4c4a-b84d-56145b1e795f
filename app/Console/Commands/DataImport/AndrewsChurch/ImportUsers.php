<?php

namespace App\Console\Commands\DataImport\AndrewsChurch;

use App\Users\Address;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'andrews-church:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'HOF Initial Date', // A
        2  => 'HOF Last Name', // B
        3  => 'HOF First Name', // C
        4  => 'HOF Birthday', // D
        5  => 'HOF Birthday w/Yr', // E
        6  => 'HOF', // F
        7  => 'HOF', // G
        8  => 'HOF Association', // H
        9  => 'HOF Option Date', // I
        10 => 'HOF Gender', // J
        11 => 'HOF Custom 1', // K
        12 => 'HOF Custom 2', // L
        13 => 'Spouse First Name', // M
        14 => 'Spouse Last Name', // N
        15 => 'Spouse Diff LN', // O
        16 => 'Spouse Birthday', // P
        17 => 'Spouse Birthday w/YR', // Q
        18 => 'Spouse', // R
        19 => 'Spouse', // S
        20 => 'Spouse Assocation', // T
        21 => 'Spouse Option Date', // U
        22 => 'Spouse Gender', // V
        23 => 'Spouse Custom 1', // W
        24 => 'Spouse Custom 2', // X
        25 => 'Children with Birthdays', // Y
        26 => 'Children without Birthdays', // Z
        27 => 'Children with age', // AA
        28 => 'Mailing Name', // AB
        29 => 'Current Add 1', // AC
        30 => 'Current Add 2', // AD
        31 => 'Current City', // AE
        32 => 'Current State', // AF
        33 => 'Current Postal Code', // AG
        34 => 'Current Primary Phone', // AH
        35 => 'Current Alt. Phone', // AI
        36 => 'Shepherd Area', // AJ
        37 => 'Wedding Date', // AK
        38 => 'Local Add1', // AL
        39 => 'Local Add2', // AM
        40 => 'Local City', // AM
        41 => 'Local State', // AO
        42 => 'Local Postal Code', // AP
        43 => 'Local Phone 1', // AQ
        44 => 'Local Phone 2', // AR
        45 => 'Alternate Add1', // AS
        46 => 'Alternate Add2', // AT
        47 => 'Alternate City', // AU
        48 => 'Alternate State', // AV
        49 => 'Alternate Postal Code', // AW
        50 => 'Alternate Phone 1', // AX
        51 => 'Alternate Phone 2', // AY
        52 => 'Alternate Address Start', // AZ
        53 => 'Alternate Address End', // BA
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        // An array of User IDs to Rows, so we can go back and make sure families are connected correctly.
        $back_reference = [];

        $member_group_id = Group::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
        $member_role_id  = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;

        // -----------------------------------------------------

        $last_family_id      = null;
        $last_family_user_id = null;
        $r                   = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Importing row ' . $r . '...');
            }
            $user    = null;
            $address = null;
            $phone   = null;
            $child   = null;

//            $cellIterator = $row->getCellIterator();
//            $cellIterator->setIterateOnlyExistingCells(false); // This loops through all cells, even if a cell value is not set.
            // By default, only cells that have a value set will be iterated.

            $user = new User();

            // -----------------------------------------------------

            $user->account_id = $this->account_id;

            $user->last_name   = $worksheet->getCellByColumnAndRow(2, $r)->getValue();
            $user->first_name  = $worksheet->getCellByColumnAndRow(3, $r)->getValue();
            $user->status      = 'active';
            $user->is_active   = true;
            $user->timezone    = 'UM6';
            $user->gender      = strtolower($worksheet->getCellByColumnAndRow(10, $r)->getValue());
            $user->family_role = $user->gender == 'male' ? 'head' : 'other';

            $birthdate = $this->getBirthdate($worksheet->getCellByColumnAndRow(4, $r)->getValue(), $worksheet->getCellByColumnAndRow(5, $r)->getValue());
            if ($birthdate !== false) {
                $user->birthdate = $birthdate->format('Y-m-d');
            }
            $anniversary = $this->getBirthdate($worksheet->getCellByColumnAndRow(37, $r)->getValue(), null);
            if ($anniversary !== false) {
                $user->date_married = $anniversary->format('Y-m-d');
            }


            // Temp store our Family ID from the imported data in the notes field.
            $user->notes = $worksheet->getCellByColumnAndRow(17, $r)->getValue();

            // Save our user and get an ID.
            $user->save();

            $family_id = $user->id;

            $user->groups()->attach($member_group_id);
            $user->roles()->attach($member_role_id);

            // Set as Head of Household
            $user->family_id = $user->id;

            $user->save();

            // Addresses
            if (!empty($worksheet->getCellByColumnAndRow(29, $r)->getValue())) {
                $address = new Address();

                $address->type     = 'home';
                $address->address1 = $worksheet->getCellByColumnAndRow(29, $r)->getValue();
                $address->address2 = $worksheet->getCellByColumnAndRow(30, $r)->getValue();
                $address->city     = $worksheet->getCellByColumnAndRow(31, $r)->getValue() ?: '';
                $address->state    = $worksheet->getCellByColumnAndRow(32, $r)->getValue();
                $address->zip      = $worksheet->getCellByColumnAndRow(33, $r)->getValue();
                $address->country  = 'US';

                $address->user_id   = $family_id;
                $address->family_id = $family_id;
                $address->is_family = true;

                $address->save();
            }
            if (!empty($worksheet->getCellByColumnAndRow(38, $r)->getValue()) && $worksheet->getCellByColumnAndRow(29, $r)->getValue() != $worksheet->getCellByColumnAndRow(38, $r)->getValue()) {
                $address = new Address();

                $address->type     = 'home';
                $address->address1 = $worksheet->getCellByColumnAndRow(38, $r)->getValue();
                $address->address2 = $worksheet->getCellByColumnAndRow(39, $r)->getValue();
                $address->city     = $worksheet->getCellByColumnAndRow(40, $r)->getValue() ?: '';
                $address->state    = $worksheet->getCellByColumnAndRow(41, $r)->getValue();
                $address->zip      = $worksheet->getCellByColumnAndRow(42, $r)->getValue();
                $address->country  = 'US';

                $address->user_id   = $family_id;
                $address->family_id = $family_id;
                $address->is_family = true;

                $address->save();
            }

            // Phones
            if (!empty($worksheet->getCellByColumnAndRow(34, $r)->getValue())) {
                $phone = new Phone();

                $phone->user_id   = $family_id;
                $phone->family_id = $family_id;
                $phone->type      = 'home';
                $phone->is_family = true;

                $phone->number = Phone::format($worksheet->getCellByColumnAndRow(34, $r)->getValue());

                $phone->save();
            }
            if (!empty($worksheet->getCellByColumnAndRow(35, $r)->getValue()) && Phone::format($worksheet->getCellByColumnAndRow(34, $r)->getValue()) != Phone::format($worksheet->getCellByColumnAndRow(35, $r)->getValue())) {
                $phone = new Phone();

                $phone->user_id   = $family_id;
                $phone->family_id = $family_id;
                $phone->type      = 'home';
                $phone->is_family = true;

                $phone->number = Phone::format($worksheet->getCellByColumnAndRow(34, $r)->getValue());

                $phone->save();
            }

            // Spouse?
            if (!empty($worksheet->getCellByColumnAndRow(13, $r)->getValue())) {
                $user->marital_status = 'married';
                $user->save();

                $spouse_user = new User();

                $spouse_user->account_id     = $this->account_id;
                $spouse_user->family_id      = $family_id;
                $spouse_user->family_role    = 'spouse';
                $spouse_user->marital_status = 'married';

                $spouse_user->last_name  = $worksheet->getCellByColumnAndRow(2, $r)->getValue();
                $spouse_user->first_name = $worksheet->getCellByColumnAndRow(13, $r)->getValue();
                $spouse_user->status     = 'active';
                $spouse_user->is_active  = true;
                $spouse_user->timezone   = 'UM6';
                $spouse_user->gender     = strtolower($worksheet->getCellByColumnAndRow(22, $r)->getValue());

                $birthdate = $this->getBirthdate($worksheet->getCellByColumnAndRow(16, $r)->getValue(), $worksheet->getCellByColumnAndRow(17, $r)->getValue());
                if ($birthdate !== false) {
                    $spouse_user->birthdate = $birthdate->format('Y-m-d');
                }
                $anniversary = $this->getBirthdate($worksheet->getCellByColumnAndRow(37, $r)->getValue(), null);
                if ($anniversary !== false) {
                    $spouse_user->date_married = $anniversary->format('Y-m-d');
                }

                $spouse_user->save();

                $spouse_user->groups()->attach($member_group_id);
                $spouse_user->roles()->attach($member_role_id);
            }

            // Children
            if (!empty($worksheet->getCellByColumnAndRow(25, $r)->getValue())) {
                $kids = explode(', ', $worksheet->getCellByColumnAndRow(25, $r)->getValue());

                foreach ($kids as $kid_with_birthday) {
                    $kab = explode(' ', $kid_with_birthday);

                    $birthdate = null;
                    $birthdate = end($kab);

                    $first_name = null;
                    foreach ($kab as $value) {
                        if ($value != $birthdate) {
                            $first_name .= ' ' . $value;
                        }
                    }
                    $first_name = trim($first_name);
                    $first_name = trim($first_name, '*');

                    $child = new User();

                    $child->account_id  = $this->account_id;
                    $child->family_id   = $family_id;
                    $child->family_role = 'child';

                    $child->last_name  = $user->last_name;
                    $child->first_name = $first_name;
                    $child->status     = 'active';
                    $child->is_active  = true;
                    $child->timezone   = 'UM6';

                    if (Str::contains($birthdate, '?')) {
                        $birthdate = trim($birthdate, '/????');
                    }
                    try {
                        $birthdate = Carbon::parse($birthdate);
                        if ($birthdate) {
                            $child->birthdate = $birthdate->format('Y-m-d');
                        }
                    } catch (\Exception $e) {

                    }

                    $child->save();

                    $child->groups()->attach($member_group_id);
                }
            }

            // -----------------------------------------------------

            $this->info('Imported user ' . $user->name);
            $this->info('---------------------------');

            $r++;
        }

        $this->info('User import complete.');
    }

    public function getBirthdate($birthdate, $birthdate_with_year)
    {
        if (!$birthdate_with_year && !$birthdate) {
            return false;
        }

        if (!$birthdate_with_year or Str::contains($birthdate_with_year, '?')) {
            return Carbon::parse($birthdate);
        }

        return Carbon::parse($birthdate_with_year);
    }
}
