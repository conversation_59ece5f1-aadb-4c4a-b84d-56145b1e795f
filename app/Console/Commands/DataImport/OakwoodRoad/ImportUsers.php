<?php

namespace App\Console\Commands\DataImport\OakwoodRoad;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'oakwood-road:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Family ID',
        2  => 'Head of Household',
        3  => 'Email',
        4  => 'Last Name',
        5  => 'First Name',
        6  => 'Home Phone',
        7  => 'Address',
        8  => 'Address',
        9  => 'Age',
        10 => 'Allergy',
        11 => 'Alt Address',
        12 => 'Alt Address',
        13 => 'Alt Carrier Sort',
        14 => 'Alt Carrier Sort',
        15 => 'Alt City',
        16 => 'Alt City',
        17 => 'Alt Country',
        18 => 'Alt Country',
        19 => 'Alt Delivery Point',
        20 => 'Alt Delivery Point',
        21 => 'Alt Home Phone',
        22 => 'Alt Phone',
        23 => 'Alt Phone',
        24 => 'Alt Phone',
        25 => 'Alt State',
        26 => 'Alt State',
        27 => 'Alt Zip Code',
        28 => 'Alt Zip Code',
        29 => 'Baptized',
        30 => 'Baptized Date',
        31 => 'Birth Date',
        32 => 'Birth Month and Day',
        33 => 'Birthdate Month',
        34 => 'Birthdate Year',
        35 => 'Cell Phone',
        36 => 'Children',
        37 => 'City',
        38 => 'City',
        39 => 'Contact',
    ];

    protected $timezone = 'America/New_York';

    protected $member_group_id;
    protected $elder_group_id;
    protected $deacon_group_id;
    protected $visitor_group_id;
    protected $former_member_group_id;

    protected $member_role_id;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id        = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id         = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id        = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id       = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;
        $this->former_member_group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => 'Former Members',
            'creator_id' => 1,
        ], [])?->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r                  = 1;
        $previous_family_id = null;
        $previous_address1  = null;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            // FamilyID + Address1
            $family_id = $worksheet->getCell([1, $r])->getValue() . '-' . $worksheet->getCell([7, $r])->getValue();

            // Family ID is 40
            $families[$family_id][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
                21 => $worksheet->getCell([21, $r])->getValue(),
                22 => $worksheet->getCell([22, $r])->getValue(),
                23 => $worksheet->getCell([23, $r])->getValue(),
                24 => $worksheet->getCell([24, $r])->getValue(),
                25 => $worksheet->getCell([25, $r])->getValue(),
                26 => $worksheet->getCell([26, $r])->getValue(),
                27 => $worksheet->getCell([27, $r])->getValue(),
                28 => $worksheet->getCell([28, $r])->getValue(),
                29 => $worksheet->getCell([29, $r])->getValue(),
                30 => $worksheet->getCell([30, $r])->getValue(),
                31 => $worksheet->getCell([31, $r])->getValue(),
                32 => $worksheet->getCell([32, $r])->getValue(),
                33 => $worksheet->getCell([33, $r])->getValue(),
                34 => $worksheet->getCell([34, $r])->getValue(),
                35 => $worksheet->getCell([35, $r])->getValue(),
                36 => $worksheet->getCell([36, $r])->getValue(),
                37 => $worksheet->getCell([37, $r])->getValue(),
                38 => $worksheet->getCell([38, $r])->getValue(),
                39 => $worksheet->getCell([39, $r])->getValue(),
            ];
            $r++;

        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_name => $family_members):

            $family_id          = null;
            $new_family_members = [];
            $family_has_spouse  = false;

            foreach ($family_members as $index => $member):

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name   = $member[5];
                $user->last_name    = $member[4];
                $user->status       = 'active';
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid();

                // Double check genders
//                if (!$user->gender) {
//                    if (Str::contains('Male', $member[74])) {
//                        $user->gender = 'male';
//                    }
//                    if (Str::contains('Female', $member[74])) {
//                        $user->gender = 'female';
//                    }
//                }

                // Default to single.
                $user->marital_status = 'single';

                // Baptism Date
                if ($member[30] > '') {
                    $temp_date = $this->getDateArray($member[30]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->date_baptism = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                        $user->is_baptized  = now();
                    }
                }
                // Birthdate
                if ($member[31] > '') {
                    $temp_date = $this->getDateArray($member[31]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                if ($member[29] == 'Yes') {
                    $user->is_baptized = now();
                }

                // Save our user and get an ID.
                $user->save();

                // -----------------------------------------------------

                // Relationship -- Possible Values:  Spouse, Head of Household, Daughter, Son, Grandson, Granddaughter
                if ($member[2] == '1' || $member[2] == 1) {
                    $user->family_id   = $user->id;
                    $user->family_role = 'head';

                    // Set our global family_id for this round.
                    $family_id = $user->id;
                } elseif ($index == 1 && ($member[39] == 'Primary' || $member[39] == 'Secondary' || $member[9] > 25)) {
                    $user->family_role = 'spouse';
                    $family_has_spouse = true;
                } else {
                    $user->family_role = 'child';
                }

                $user->family_id = $family_id;

                // Save our progress
                $user->save();


                // -----------------------------------------------------
                // PHONES

                // Mobile
                if ($member[35] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[35]),
                    ], [
                        'user_id'    => $user->id,
                        'number'     => Phone::format($member[35]),
                        'is_primary' => true,
                        'is_family'  => false,
                        'is_hidden'  => false,
                        'type'       => 'mobile',
                    ]);
                }
                // Home
                if ($index == 0 && $member[6] > '') {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[6]),
                    ], [
                        'user_id'    => $user->id,
                        'family_id'  => $family_id,
                        'number'     => Phone::format($member[6]),
                        'is_primary' => true,
                        'is_family'  => true,
                        'is_hidden'  => false,
                        'type'       => 'home',
                    ]);
                }
                // Work
//                if ($member[20] > '') {
//                    Phone::firstOrCreate([
//                        'number' => Phone::format($member[20]),
//                    ], [
//                        'user_id'    => $user->id,
//                        'family_id'  => $family_id,
//                        'number'     => Phone::format($member[20]),
//                        'extension'  => $member[21],
//                        'is_primary' => false,
//                        'is_family'  => false,
//                        'is_hidden'  => true,
//                        'type'       => 'work',
//                    ]);
//                }

                // -----------------------------------------------------
                // EMAILS

                if ($index == 0 && $member[3] > '') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[3]),
                    ], [
                        'user_id'               => $user->id,
                        'email'                 => strtolower($member[3]),
                        'is_primary'            => true,
                        'is_family'             => false,
                        'is_hidden'             => false,
                        'type'                  => 'personal',
                        'receives_group_emails' => true,
                    ]);
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($index == 0 && $member[7] > '') {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[7],
                        'city'      => $member[37],
                        'family_id' => $family_id,
                    ], [
                        'user_id'   => $user->id,
                        'family_id' => $family_id,
                        'type'      => 'home',
                        'label'     => 'Home',
                        'address1'  => $member[7] ?: '',
//                        'address2'  => $member[8] ?: '',
                        'city'      => $member[37] ?: '',
                        'state'     => 'WV',
                        'zip'       => '26554',
                        'country'   => 'US',
                        'is_family' => true,
                    ]);
                }
                // Mailing
//                if ($member[79] > '') {
//                    $address = Address::firstOrCreate([
//                        'address1'  => $member[79],
//                        'city'      => $member[83],
//                        'family_id' => $family_id,
//                    ], [
//                        'user_id'   => $user->id,
//                        'family_id' => $family_id,
//                        'type'      => 'home',
//                        'label'     => 'Home',
//                        'address1'  => $member[79] ?: '',
//                        'address2'  => $member[81] ?: '',
//                        'city'      => $member[83] ?: '',
//                        'state'     => $member[89] ?: '',
//                        'zip'       => $member[87] ?: '',
//                        'country'   => 'US',
//                        'is_family' => true,
//                        'is_hidden' => true,
//                    ]);
//                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Everyone is a NOT member in this import
                // Former Members
                if (Str::contains($member[1], 'FM', true)) { // FM = Former Member
                    $user->groups()->attach($this->former_member_group_id);
                } // Members
                else {
                    $user->groups()->attach($this->member_group_id);
                    $user->roles()->attach($this->member_role_id);
                }


                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $new_family_members[] = $user;

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

            // To figure out:
            // 1. Is the head of house single or married?
            // 2. Who is the head of house?
            // 3. Family address and phone based on head of household.
            foreach ($new_family_members as $temp_user) {
                $temp_user->family_id = $family_id;

                if (($temp_user->family_role == 'head' || $temp_user->family_role == 'spouse') && $family_has_spouse) {
                    $temp_user->marital_status = 'married';
                }

                // Try to figure out a gender.
                if ($temp_user->family_role == 'head' && $family_has_spouse) {
                    $temp_user->gender = 'male';
                } elseif ($temp_user->family_role == 'spouse' && $family_has_spouse) {
                    $temp_user->gender = 'female';
                } else {
                    $gender_input = $this->ask('Gender for ' . $temp_user->name . '?');

                    if ($gender_input == 'm') {
                        $temp_user->gender = 'male';
                    } elseif ($gender_input == 'f') {
                        $temp_user->gender = 'female';
                    }
                }

                $temp_user->save();
            }

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            $year  = $date[2] > 0 ? $date[2] : 23;
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = $date[0] . $date[1];
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year > 23 ? intval('19' . $year) : intval('20' . $year),
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
