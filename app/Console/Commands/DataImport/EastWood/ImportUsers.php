<?php

namespace App\Console\Commands\DataImport\EastWood;

use App\Accounts\Account;
use App\Accounts\ChurchOffice;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\Services\ChangeHeadOfHousehold;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-east-wood {account_id} {sqlite_file_location}';

    protected $description = 'Import users from the SQLite database created from their CSV export file.';

    protected $account_id;
    protected $account;
    protected $sqlite_file_location;

    protected $member_group_id = null;
    protected $elder_group_id  = null;
    protected $deacon_group_id = null;
    protected $member_role_id  = null;

    protected $columns = [
        1  => 'LastName',
        2  => 'FirstName',
        3  => 'Address',
        4  => 'City',
        5  => 'State',
        6  => 'ZipCode',
        7  => 'MemberStatus',
        8  => 'DateLastAttended',
        9  => 'DateLastEdited',
        10 => 'ToDate',
        11 => 'BirthDate',
        12 => 'Children',
        13 => 'City',
        14 => 'DateCreated',
        15 => 'DescriptionOfMembership',
        16 => 'Email',
        17 => 'EmergencyContactPhoneNumber',
        18 => 'EmergencyPhone',
        19 => 'EmergencyContactName',
        20 => 'FamilyID',
        21 => 'Gender',
        22 => 'HomePhone',
        23 => 'IndividualID',
        24 => 'MaritalStatus',
        25 => 'MiddleName',
        26 => 'Occupation',
        27 => 'PassedAway',
        28 => 'Phone',
        29 => 'Picture',
        30 => 'PreferredName',
        31 => 'State',
        32 => 'WeddingDate',
        33 => 'WorkPhone',
        34 => 'Worship',
        35 => 'YouthTalents',
        36 => 'Cell',
        37 => 'EmergencyContactNumber',
        38 => 'Baptized',
        39 => 'BaptizedDate',
        40 => 'Relationship',
    ];

    protected $timezone = 'America/Chicago';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id           = $this->argument('account_id');
        $this->sqlite_file_location = $this->argument('sqlite_file_location');

        if (!file_exists($this->sqlite_file_location)) {
            $this->error('SQLite file not found');
            return 1;
        }

        $this->info('Opening SQLite file for import...');

        // Add the temp connection
        Config::set("database.connections.dataimportsqlite", [
            'driver'   => 'sqlite',
            'database' => $this->sqlite_file_location,
            'prefix'   => '',
        ]);

        $this->account = Account::find($this->account_id);

        // Use the connection
        $sqlite = DB::connection('dataimportsqlite');

        // Get our family units
        $families = $sqlite->table('users')->groupBy('FamilyID')->orderBy('LastName')->get();

        // Progress bar creation
        $this->info('Found ' . $families->count() . ' families to import...');
        $bar = $this->output->createProgressBar($families->count());

        // Wrap everything in a transaction
        DB::beginTransaction();

        try {
            $this->member_group_id = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()?->id;
            $this->elder_group_id  = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()?->id;
            $this->deacon_group_id = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()?->id;

            if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id) {
                $this->error('Could not find all groups listed.');
                exit;
            }

            $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
            $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
            $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

            if (!$this->member_role_id) {
                $this->error('Could not find all roles listed.');
                exit;
            }

            $bar->start();

            // Go through each family.
            foreach ($families as $index => $family_head):

                $family_id        = null;
                $family_is_member = false;
                $head_is_deceased = false;

                $positionOrder = [
                    'Head of Household'   => 1,
                    'Father'              => 2,
                    'Spouse'              => 3,
                    'Wife'                => 4,
                    'Mother'              => 5,
                    'Step-Mother'         => 6,
                    'Individual'          => 7,
                    'Child'               => 8,
                    'Son'                 => 9,
                    'Daughter'            => 10,
                    'Step Son'            => 11,
                    'Brother'             => 12,
                    'Step Daughter'       => 13,
                    'Great Granddaughter' => 14,
                    'Daughter In-Law'     => 15,
                    'Granddaughter'       => 16,
                    'Relative'            => 17,
                    'Friend'              => 18,
                    'Grandson'            => 19,
                    'Sister'              => 20,
                    'Girlfriend'          => 21,
                    'Mother In-Law'       => 22,
                    'Boyfriend'           => 23,
                    'Grandfather'         => 24,
                    'Nephew'              => 25,
                    'Aunt'                => 26,
                    'Son In-Law'          => 27,
                    'Father In-Law'       => 28,
                    'Uncle'               => 29,
                    'Cousin'              => 30,
                    'Sister In-Law'       => 31,
                    'Grandmother'         => 32,
                    'Niece'               => 33,
                    'Great Grandson'      => 34,
                    ''                    => 35,
                    'Organization Record' => 36,
                ];

                $members = $sqlite->table('users')
                    ->where('FamilyID', $family_head->FamilyID)
                    ->orderBy('IndividualID', 'asc')
                    ->get()
                    ->sort(function ($a, $b) use ($positionOrder) {
                        $posA = $positionOrder[$a->Relationship] ?? 999;
                        $posB = $positionOrder[$b->Relationship] ?? 999;
                        return $posA - $posB;
                    });

                $bar->advance();

//                $this->info('Importing family: ' . $family_head->FamilyNumber . '...');

                foreach ($members as $member):

                    if ($member->lightpost_id) {
                        $this->warn('Skipping user ' . $member->FirstName . ' ' . $member->LastName . ' because they already have a Lightpost ID. IndividualID: ' . $member->IndividualID);
                        continue;
                    }

                    $family_role = null;

                    if ($member->MemberStatus == 'Active Member') {
                        $family_is_member = true;
                    }

                    if ($member->Relationship == 'Head of Household') {
                        $family_role = 'head';
                    } elseif ($member->Relationship == 'Spouse') {
                        $family_role = 'spouse';
                    } elseif (in_array($member->Relationship, ['Son', 'Child', 'Daughter', 'Step Son', 'Step Daughter', 'Great Granddaughter', 'Granddaughter', 'Great Grandson', 'Grandson'])) {
                        $family_role = 'child';
                    } else {
                        $family_role = 'other';
                    }

                    if ($member->Relationship == 'Head of Household' && $member->MemberStatus == 'Deceased') {
                        $head_is_deceased = true;
                    }

                    $user = new User();

                    // -----------------------------------------------------

                    $user->account_id = $this->account_id;

                    $user->first_name           = $member->FirstName;
                    $user->last_name            = $member->LastName;
                    $user->preferred_first_name = ($member->PreferredName && $member->FirstName != $member->PreferredName) ? $member->PreferredName : null;
                    $user->gender               = strtolower($member->Gender);
                    $user->status               = 'active';
                    $user->is_active            = true;
                    $user->family_role          = $family_role;
                    $user->timezone             = $this->account->timezone;
                    $user->public_token         = Str::uuid();

                    $user->job_title = $member->Occupation;

                    $user->is_baptized = $member->Baptized == 'Yes' ? now() : null;

                    $date_baptism = null;
                    if ($member->BaptizedDate) {
                        if (Str::contains($member->BaptizedDate, '    ')) {
                            $date_baptism = Str::replaceFirst('    ', '1900', $member->BaptizedDate);
                        } else {
                            $date_baptism = $member->BaptizedDate;
                        }

                        try {
                            $user->date_baptism = $date_baptism ? Carbon::parse($date_baptism) : null;
                        } catch (\Exception $e) {
                        }
                    }
                    $date_married = null;
                    if ($member->WeddingDate) {
                        if (Str::contains($member->WeddingDate, '    ')) {
                            $date_married = Str::replaceFirst('    ', '1900', $member->WeddingDate);
                        } else {
                            $date_married = $member->WeddingDate;
                        }

                        try {
                            $user->date_married = $date_married ? Carbon::parse($date_married) : null;
                        } catch (\Exception $e) {
                        }
                    }
                    $birthdate = null;
                    if ($member->BirthDate) {
                        if (Str::contains($member->BirthDate, '    ')) {
                            $birthdate = Str::replaceFirst('    ', '1900', $member->BirthDate);
                        } else {
                            $birthdate = $member->BirthDate;
                        }

                        try {
                            $user->birthdate = $birthdate ? Carbon::parse($birthdate) : null;
                        } catch (\Exception $e) {
                        }
                    }

                    if ($member->MemberStatus == 'Deceased' || $member->PassedAway) {
                        $date_deceased = null;
                        if ($member->PassedAway) {
                            if (Str::contains($member->PassedAway, '    ')) {
                                $date_deceased = Str::replaceFirst('    ', '1900', $member->PassedAway);
                            } else {
                                $date_deceased = $member->PassedAway;
                            }
                        }
                        try {
                            $user->date_deceased = $date_deceased ? Carbon::parse($date_deceased) : now();
                        } catch (\Exception $e) {
                        }
                    }

                    if ($member->MaritalStatus == 'Married') {
                        $user->marital_status = 'married';
                    } elseif ($member->MaritalStatus == 'Divorced') {
                        $user->marital_status = 'divorced';
                    } elseif ($member->MaritalStatus == 'Widow' || $member->MaritalStatus == 'Widower') {
                        $user->marital_status = 'widowed';
                    } elseif ($member->MaritalStatus == 'Separated') {
                        $user->marital_status = 'separated';
                    } elseif ($member->MaritalStatus == 'Single') {
                        $user->marital_status = 'single';
                    } elseif ($member->MaritalStatus == 'Engaged') {
                        $user->marital_status = 'engaged';
                    } elseif ($member->MaritalStatus == 'Unknown' || $member->MaritalStatus == 'Not Known') {
                        $user->marital_status = 'unknown';
                    } else {
                        $user->marital_status = 'unknown';
                    }

                    // Save our user and get an ID.
                    $user->save();

                    // Get and record our new FamilyID for our head of household.
                    if ($user->family_role == 'head' || $members->count() == 1) {
                        $user->family_role = 'head';
                        $family_id         = $user->id;
                    }

                    // If we have a head of household, we will assign them to the family.
                    $user->family_id = $family_id;

                    $user->generateUlid();

                    // Save our progress
                    $user->save();

                    if (!$family_id) {
                        $this->error('Could not find family ID for user ' . $user->name . 'With IndividualID: ' . $member->IndividualID);
                    }

                    // Save our Lightpost data back to the SQLite file in case we need it later.
                    $sqlite->table('users')
                        ->where('IndividualID', $member->IndividualID)
                        ->update([
                            'lightpost_id'           => $user->id,
                            'lightpost_public_token' => $user->public_token,
                            'lightpost_ulid'         => $user->ulid,
                        ]);

                    // -----------------------------------------------------
                    // PHONES

                    // Mobile
                    $mobile_phone = null;
                    if ($member->Cell && strlen($member->Cell) >= 8) {
                        // If we only have 7 digits, but we know where we live, manually put the area code in.
                        if (strlen($mobile_phone) == 8 && $member->City == 'Paris') {
                            $mobile_phone = '731' . $member->Cell;
                        } else {
                            $mobile_phone = $member->Cell;
                        }

                        Phone::firstOrCreate([
                            'number' => Phone::format($mobile_phone),
                        ], [
                            'user_id'          => $user->id,
                            'number'           => Phone::format($mobile_phone),
                            'messages_opt_out' => false,
                            'is_primary'       => true,
                            'is_family'        => false,
                            'is_hidden'        => false,
                            'type'             => 'mobile',
                        ]);
                    }
                    // Home
                    $home_phone = null;
                    if ($member->HomePhone && strlen($member->HomePhone) >= 8) {
                        // If we only have 7 digits, but we know where we live, manually put the area code in.
                        if (strlen($member->HomePhone) == 8 && $member->City == 'Paris') {
                            $home_phone = '731' . $member->HomePhone;
                        } else {
                            $home_phone = $member->HomePhone;
                        }

                        Phone::firstOrCreate([
                            'number' => Phone::format($home_phone),
                        ], [
                            'user_id'    => $user->family_id,
                            'number'     => Phone::format($home_phone),
                            'is_primary' => false,
                            'is_family'  => true,
                            'is_hidden'  => false,
                            'type'       => 'home',
                        ]);
                    }

                    // -----------------------------------------------------
                    // EMAILS

                    $email = null;
                    $email = $member->Email;
                    if ($email) {
                        Email::firstOrCreate([
                            'email' => strtolower($email),
                        ], [
                            'user_id'               => $user->id,
                            'email'                 => strtolower($email),
                            'is_primary'            => true,
                            'is_family'             => false,
                            'is_hidden'             => false,
                            'type'                  => 'personal',
                            'receives_group_emails' => true,
                            'allow_messages'        => true,
                        ]);
                    }

                    // -----------------------------------------------------
                    // ADDRESSES

                    if ($family_id && !empty($member->Address) && !empty($member->City)) {
                        Address::firstOrCreate([
                            'user_id'  => $family_id,
                            'address1' => $member->Address,
                            'city'     => $member->City,
                        ], [
                            'user_id'    => $family_id,
                            'family_id'  => $family_id,
                            'type'       => 'home',
                            'label'      => 'Home',
                            'address1'   => $member->Address,
                            'city'       => $member->City,
                            'state'      => $member->State,
                            'zip'        => $member->ZipCode,
                            'country'    => 'US',
                            'is_mailing' => false,
                            'is_family'  => true,
                        ]);
                    }

                    // -----------------------------------------------------

                    // Save our progress
                    $user->save();

                    // -----------------------------------------------------

                    if ($family_is_member) {
                        // Groups
                        $user->groups()->attach($this->member_group_id);
                        $user->roles()->attach($this->member_role_id);
                    } elseif ($member->MemberStatus) {
                        if ($member->MemberStatus != 'Deceased' && $member->MemberStatus != 'Child' && $member->MemberStatus != 'Regular Attendee') {
                            $this->attachGroupByName($user, $member->MemberStatus);
                        }
                    }

                    // Save our progress
                    $user->save();

                    // $this->info('Imported user ' . $user->name);
                    // $this->info('---------------------------');

                endforeach;

                // Make the spouse the head if the husband is deceased, but only if this is a current member.
                if ($head_is_deceased && $family_is_member) {
                    $this->info('Head of household is deceased, reversing roles.');
                    $user_to_swap        = User::find($family_id);
                    $user_to_swap_spouse = $user_to_swap->spouse;

                    (new ChangeHeadOfHousehold())
                        ->fromUser($user_to_swap)
                        ->toUser($user_to_swap_spouse)
                        ->change();

                    $head_is_deceased = false;
                }

            endforeach;

            $bar->finish();

            DB::commit();
            $this->info('Import complete.');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Import failed: ' . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }

    private function assignMemberSpecificGroups($new_user_record, $data_import_record)
    {
        $groups = explode(',', Arr::get($data_import_record, 'active_groups'));

        foreach ($groups as $group_string) {
            $group_string = trim($group_string);

            try {
                // Skip the STATUS Members group, we already assigned users to the Member group.
                if ($group_string === '' || $group_string == 'STATUS Members') {
                    continue;
                } elseif ($group_string == 'ADMINISTRATION') {
                    $actual_group_name = 'Admin';
                } else {
                    $name_split = explode(' ', $group_string, 2);

                    $actual_group_name = $name_split[1];
                }
            } catch (\Exception $e) {
                dd($groups, $group_string);
            }

            $this->attachGroupByName($new_user_record, $actual_group_name);
        }
    }

    private function assignChurchOffice($user, $office_name, $subtitle = null)
    {
        $office = ChurchOffice::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $office_name,
        ], [
            'plural_name'        => \Illuminate\Support\Str::plural($office_name),
            'short_name'         => \Illuminate\Support\Str::kebab($office_name),
            'url_name'           => \Illuminate\Support\Str::kebab($office_name),
            'is_public'          => true,
            'show_in_leadership' => true,
            'sort_id'            => ChurchOffice::where('account_id', $this->account_id)->max('sort_id') + 1,
        ]);

        if (!$office->ulid) {
            $office->generateUlid();
        }

        // Attach the office to the user with the subtitle if provided
        $user->churchOffices()->syncWithoutDetaching([
            $office->id => [
                'subtitle'   => $subtitle,
                'account_id' => $this->account_id,
            ],
        ]);
    }

    private function findFamilyValueInArray($array)
    {
        // Array of "email" (without name) or "phone" (without name)
        $i = 0;
        foreach ($array as $index => $value) {
            if ($i == 0 && !Str::contains($value, ' ')) {
                return $value;
            } else {
                return null;
            }
        }

        return null;
    }

    private function findValueInArrayByName($array, $name)
    {
        // Array of "email (name)" or "phone (name)" or "phone name"
        foreach ($array as $value) {
            if (Str::contains($value, '(')) {
                $parts = explode(' (', $value);
            } else {
                $parts = explode(' ', $value);
            }

            // If we have a name
            if (Arr::get($parts, 1) && Arr::get($parts, 1) == $name . (Str::contains($value, '(') ? ')' : null)) {
                return trim(Arr::get($parts, 0), ' .');
            } elseif (($name === '' || $name == null) && !Arr::get($parts, 1)) {
                return trim(Arr::get($parts, 0));
            }
        }

        return null;
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        if (DB::table('user_to_group')->where('user_id', $user->id)->where('user_group_id', $group_id)->exists()) {
            return;
        } else {
            $user->groups()->attach($group_id);
        }
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
