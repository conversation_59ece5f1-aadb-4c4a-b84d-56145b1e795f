<?php

namespace App\Console\Commands\DataImport\OakHill;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:import-users-oak-hill {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1  => 'Breeze ID',
        2  => 'Member Status',
        3  => 'Family',
        4  => 'Family Role',
        5  => 'First Name',
        6  => 'Last Name',
        7  => 'Middle Name',
        8  => 'Nickname',
        9  => 'Maiden Name',
        10 => 'Nickname',
        11 => 'Gender',
        12 => 'Anniversary',
        13 => 'Anniversary Month/Day',
        14 => 'Years Since Anniversary',
        15 => 'Birthdate',
        16 => 'Birthdate Month/Day',
        17 => 'Age',
        18 => 'Mobile',
        19 => 'Home',
        20 => 'Work',
        21 => 'Email',
        22 => 'Street Address',
        23 => 'City',
        24 => 'State',
        25 => 'Zip',
        26 => 'Added Date',
    ];

    protected $timezone = 'America/New_York';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $this->member_group_id        = Group::where('name', 'LIKE', 'Members')->where('account_id', $this->account_id)->first()->id;
        $this->elder_group_id         = Group::where('name', 'LIKE', 'Elders')->where('account_id', $this->account_id)->first()->id;
        $this->deacon_group_id        = Group::where('name', 'LIKE', 'Deacons')->where('account_id', $this->account_id)->first()->id;
        $this->visitor_group_id       = Group::where('name', 'LIKE', 'Visitors')->where('account_id', $this->account_id)->first()->id;
        $this->former_member_group_id = Group::firstOrCreate([
                'account_id' => $this->account_id,
                'name'       => 'Former Members',
                'creator_id' => 1,
            ]
        )?->id;

        if (!$this->member_group_id || !$this->elder_group_id || !$this->deacon_group_id || !$this->visitor_group_id) {
            $this->error('Could not find all groups listed.');
            exit;
        }

        $this->member_role_id = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
//        $this->elder_role_id  = Role::where('name', 'Elder')->where('account_id', $this->account_id)->first()->id;
//        $this->deacon_role_id = Role::where('name', 'Deacon')->where('account_id', $this->account_id)->first()->id;

        if (!$this->member_role_id) {
            $this->error('Could not find all roles listed.');
            exit;
        }

        $families = [];

        // Group into our family units.
        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Adding row to families: ' . $r . '...');
            }

            $family_role_sort_order = null;
            if ($worksheet->getCell([4, $r])->getValue() == 'Head of Household') {
                $family_role_sort_order = 1;
            } elseif ($worksheet->getCell([4, $r])->getValue() == 'Spouse') {
                $family_role_sort_order = 2;
            } elseif ($worksheet->getCell([4, $r])->getValue() == 'Adult') {
                $family_role_sort_order = 3;
            } elseif ($worksheet->getCell([4, $r])->getValue() == 'Child') {
                $family_role_sort_order = 4;
            } else {
                $family_role_sort_order = 5;
            }

            // Family ID
            $family_id = $worksheet->getCell([3, $r])->getValue();

            // Key is the Family Photo URL or a unique value
            $families[$family_id][] = [
                1  => $worksheet->getCell([1, $r])->getValue(),
                2  => $worksheet->getCell([2, $r])->getValue(),
                3  => $worksheet->getCell([3, $r])->getValue(),
                4  => $worksheet->getCell([4, $r])->getValue(),
                5  => $worksheet->getCell([5, $r])->getValue(),
                6  => $worksheet->getCell([6, $r])->getValue(),
                7  => $worksheet->getCell([7, $r])->getValue(),
                8  => $worksheet->getCell([8, $r])->getValue(),
                9  => $worksheet->getCell([9, $r])->getValue(),
                10 => $worksheet->getCell([10, $r])->getValue(),
                11 => $worksheet->getCell([11, $r])->getValue(),
                12 => $worksheet->getCell([12, $r])->getValue(),
                13 => $worksheet->getCell([13, $r])->getValue(),
                14 => $worksheet->getCell([14, $r])->getValue(),
                15 => $worksheet->getCell([15, $r])->getValue(),
                16 => $worksheet->getCell([16, $r])->getValue(),
                17 => $worksheet->getCell([17, $r])->getValue(),
                18 => $worksheet->getCell([18, $r])->getValue(),
                19 => $worksheet->getCell([19, $r])->getValue(),
                20 => $worksheet->getCell([20, $r])->getValue(),
                21 => $worksheet->getCell([21, $r])->getValue(),
                22 => $worksheet->getCell([22, $r])->getValue(),
                23 => $worksheet->getCell([23, $r])->getValue(),
                24 => $worksheet->getCell([24, $r])->getValue(),
                25 => $worksheet->getCell([25, $r])->getValue(),
                26 => $worksheet->getCell([26, $r])->getValue(),
                27 => $family_role_sort_order,
            ];
            $r++;
        }

        // An array of User IDs to Families, so we can go back and make sure families are connected correctly.
        $back_reference = [];

        // -----------------------------------------------------

        // Go through each family and get them sorted out.
        foreach ($families as $family_members):

            // Resort our family members by their family role sort order.
            // We expect the Head of Household to be first to set family_id values.
            uasort($family_members, function ($a, $b) {
                return ($a[27] < $b[27]) ? -1 : 1;
            });

            $family_id              = null;
            $family_member_index    = 0;
            $is_member_family       = false;
            $is_married_with_spouse = false;

            // Determine if married/spouse
            $has_head   = false;
            $has_spouse = false;
            foreach ($family_members as $member) {
                if ($member[4] == 'Head of Household') {
                    $has_head = true;
                }
                if ($member[4] == 'Spouse') {
                    $has_spouse = true;
                }
                // If anyone in the family is a member, make them all members.  Otherwise we are separating family units to make the spouse the head of household.
                if ($member[2] == 'Member') {
                    $is_member_family = true;
                }
            }
            if ($has_head && $has_spouse) {
                $is_married_with_spouse = true;
            }

            foreach ($family_members as $member):

                $family_member_index++; // Start at 1

                $user = new User();

                // -----------------------------------------------------

                $user->account_id = $this->account_id;

                $user->first_name  = $member[5];
                $user->last_name   = $member[6];
                $user->middle_name = $member[7];
                if ($member[10] > '') {
                    $user->preferred_first_name = $member[10];
                }
                $user->status       = 'active';
                $user->is_active    = true;
                $user->timezone     = $this->timezone;
                $user->public_token = Str::uuid()->toString();

                // Double check genders
                if (Str::contains('Male', $member[11])) {
                    $user->gender = 'male';
                }
                if (Str::contains('Female', $member[11])) {
                    $user->gender = 'female';
                }

                // Save our user and get an ID.
                $user->save();

                if ($member[4] == 'Head of Household') {
                    $user->family_role = 'head';
                    $family_id         = $user->id;
                    $user->family_id   = $family_id;
                } elseif ($member[4] == 'Spouse') {
                    $user->family_role = 'spouse';
                    $user->family_id   = $family_id;
                    if (!$user->gender && $is_married_with_spouse) {
                        $user->gender = 'female';
                    }
                } elseif ($member[4] == 'Adult Dependent' || $member[4] == 'Adult') {
                    $user->family_role = 'dependent';
                    $user->family_id   = $family_id;
                } elseif ($member[4] == 'Child') {
                    $user->family_role = 'child';
                    $user->family_id   = $family_id;
                }

                // Default to single.
                $user->marital_status = 'single';

                // Marital Status
                if ($user->family_role == 'spouse' && $is_married_with_spouse) {
                    $user->marital_status = 'married';
                }
                if ($user->family_role == 'head' && $is_married_with_spouse) {
                    $user->marital_status = 'married';
                }
//                if ($member[9] == 'Widowed') {
//                    $user->marital_status = 'widowed';
//                } elseif ($member[9] == 'Single') {
//                    $user->marital_status = 'single';
//                } elseif ($member[9] == 'Separated') {
//                    $user->marital_status = 'separated';
//                } elseif ($member[9] == 'Divorced') {
//                    $user->marital_status = 'divorced';
//                }

                // Birthdate
                if (!empty($member[15])) {
                    $temp_date = $this->getDateArray($member[15]);
                    if ($temp_date && $temp_date !== '/  /') {
                        $user->birthdate = Carbon::create($temp_date['year'], $temp_date['month'], $temp_date['day']);
                    }
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------
                // PHONES

                // Home
                if ($member[19] > '' && $family_member_index == 1) {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[19]),
                    ],
                        [
                            'user_id'    => $user->id,
                            'family_id'  => $family_id,
                            'number'     => Phone::format($member[19]),
                            'is_primary' => false,
                            'is_family'  => true,
                            'is_hidden'  => false,
                            'type'       => 'home',
                        ]
                    );
                }
                // Mobile
                if (!empty($member[18])) {
                    Phone::firstOrCreate([
                        'number' => Phone::format($member[18]),
                    ],
                        [
                            'user_id'    => $user->id,
                            'number'     => Phone::format($member[18]),
                            'is_primary' => true,
                            'is_family'  => false,
                            'is_hidden'  => false,
                            'type'       => 'mobile',
                        ]
                    );
                }

                // -----------------------------------------------------
                // EMAILS

                if ($member[21] > '') {
                    Email::firstOrCreate([
                        'email' => strtolower($member[21]),
                    ],
                        [
                            'user_id'               => $user->id,
                            'email'                 => strtolower($member[21]),
                            'is_primary'            => true,
                            'is_family'             => false,
                            'is_hidden'             => false,
                            'type'                  => 'personal',
                            'receives_group_emails' => true,
                        ]
                    );
                }


                // -----------------------------------------------------
                // ADDRESSES

                if ($member[22] > '' && $family_member_index == 1) {
                    $address = Address::firstOrCreate([
                        'address1'  => $member[22],
                        'city'      => $member[23],
                        'family_id' => $family_id,
                    ],
                        [
                            'user_id'   => $user->id,
                            'family_id' => $family_id,
                            'type'      => 'home',
                            'label'     => 'Home',
                            'address1'  => $member[22] ?: '',
                            //                        'address2'  => $member[19] ?: '',
                            'city'      => $member[23] ?: '',
                            'state'     => $member[24] ?: '',
                            'zip'       => $member[25] ?: '',
                            'country'   => 'US',
                            'is_family' => true,
                        ]
                    );
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                // -----------------------------------------------------

                // Groups
                if ($is_member_family) {
                    $user->groups()->attach($this->member_group_id);
                    $user->roles()->attach($this->member_role_id);
                } else {
                    $user->groups()->attach($this->former_member_group_id);
                }

                // -----------------------------------------------------

                // Save our progress
                $user->save();

                $this->info('Imported user ' . $user->name);
                $this->info('---------------------------');

            endforeach; // EACH FAMILY MEMBER

//            // Go back through this family and see if we can identify the head as a male.
//            $family_head = User::find($family_id);
//            if (!$family_id) {
//                $this->warn('Could not find family head for family: '.$family_id);
//                continue;
//            }
//            if ($family_head->spouseFromHeadOfHousehold) {
//                $family_head->gender = 'male';
//                $family_head->save();
//            }

        endforeach; // EACH FAMILY ARRAY

        $this->info('User import complete.');
    }

    private function attachGroupByName($user, $group_name)
    {
        $group_id = Group::firstOrCreate([
            'account_id' => $this->account_id,
            'name'       => $group_name,
            'creator_id' => 1,
        ], [])?->id;

        $user->groups()->attach($group_id);
    }

    private function getDateArray($input)
    {
        $date = explode('/', $input);

        $year  = null;
        $month = null;
        $day   = null;

        // "  /  /    "
        if ($date[0] == '' || $date[0] == '  ') {
            $year  = null;
            $month = null;
            $day   = null;
        } // 08/11/1975
        elseif (array_key_exists(2, $date)) {
            // If the year is only 2 digits
            if ($date[2] > 0 && $date[2] < 100) {
                if ($date[2] <= date('y')) {
                    $year = '20' . $date[2];
                } else {
                    $year = '19' . $date[2];
                }
            } elseif ($date[2] > 1800) {
                $year = $date[2];
            } else {
                $year = 0000;
            }
            $day   = $date[1];
            $month = $date[0];
        } // 19/75/
        elseif (!array_key_exists(2, $date) && array_key_exists(1, $date)) {
            $year  = 0000;
            $day   = 1;
            $month = 1;
        }

        if ($year === null && $month === null && $day === null) {
            return false;
        }

        return [
            'year'  => $year,
            'month' => intval($month),
            'day'   => intval($day),
        ];
    }
}
