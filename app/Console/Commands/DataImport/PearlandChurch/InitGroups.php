<?php

namespace App\Console\Commands\DataImport\PearlandChurch;

use App\Users\Group;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class InitGroups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pearland-church:init-groups {account_id} {excel_file_location}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        $all_groups = [];

        $r = 1;
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Importing row ' . $r . '...');
            }

            $groups = explode(',', $worksheet->getCellByColumnAndRow(25, $r)->getValue());
            $groups = array_map(function ($value) {
                return trim($value);
            }, $groups);

            foreach ($groups as $group) {
                if (!empty($group)) {
                    $all_groups = Arr::add($all_groups, $group, true);
                }
            }

            $r++;
        }

        foreach ($all_groups as $group_name => $value) {
            if (!Group::where('account_id', $this->account_id)->where('name', $group_name)->exists()) {
                $role = Group::create([
                    'account_id'                 => $this->account_id,
                    'creator_id'                 => 1,
                    'name'                       => $group_name,
                    'url_name'                   => Str::slug($group_name),
                    'allow_individual_to_toggle' => 0,
                    'is_hidden'                  => 0,
                    'indicates_membership'       => 0,
                ]);
            }
        }
    }
}
