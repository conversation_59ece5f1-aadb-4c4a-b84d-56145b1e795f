<?php

namespace App\Console\Commands\DataImport\GraeberChurch;

use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'graeber-church:import-users {account_id} {excel_file_location}';

    protected $description = '';

    protected $account_id;
    protected $excel_file_location;

    protected $columns = [
        1 => 'Directory Name',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->account_id          = $this->argument('account_id');
        $this->excel_file_location = $this->argument('excel_file_location');

        $this->info('Opening Excel file for import...');

        /** Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->excel_file_location);

        $worksheet = $spreadsheet->getActiveSheet();

        // An array of User IDs to Rows, so we can go back and make sure families are connected correctly.
        $back_reference       = [];
        $old_family_id_to_new = []; // $old_id => $new_id

        $member_role_id  = Role::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;
        $member_group_id = Group::where('name', 'Member')->where('account_id', $this->account_id)->first()->id;

        // -----------------------------------------------------


        $r = 1;
        // One row == One User
        foreach ($worksheet->getRowIterator() as $row) {
            // If this is our first row, skip it.
            if ($r == 1) {
                $this->info('Skipping row 1...');
                $r++;
                continue;
            } else {
                $this->info('Importing row ' . $r . '...');
            }

            // -----------------------------------------------------

            /**
             * 1 = First Name
             * 2 = Last Name
             * 3 = Email
             * 4 = Cell Phone
             * 5 = Home Phone
             * 6 = Belongs To Family (Last, First)
             * 7 = Head of Household (X)
             * 8 = Is Spouse (X)
             * 9 = Is Baptized (X)
             * 10 = Member (X)
             * 11 = Birthdate (MM/DD/YY)
             * 12 = Anniversary (MM/DD/YY)
             * 13 = Street
             * 14 = Apartment
             * 15 = City
             * 16 = State
             * 17 = Zip
             * 18 = [Address] Belongs to Whole Family
             */

            // Don't import a user that exists.
            if ($user = User::where('account_id', $this->account_id)
                ->where('first_name', trim($worksheet->getCellByColumnAndRow(1, $r)->getValue()))
                ->where('last_name', trim($worksheet->getCellByColumnAndRow(2, $r)->getValue()))
                ->first()) {

                $this->warn('Exact name match!  User ' . $worksheet->getCellByColumnAndRow(1, $r)->getValue() . ' ' . $worksheet->getCellByColumnAndRow(2, $r)->getValue() . ' is already in the database. Skipping...');

                if (!Email::where('email', trim($worksheet->getCellByColumnAndRow(3, $r)->getValue()))->exists()) {
                    $email = new Email();

                    $email->email                 = trim($worksheet->getCellByColumnAndRow(3, $r)->getValue());
                    $email->created_at            = now();
                    $email->user_id               = $user->id;
                    $email->type                  = 'personal';
                    $email->is_primary            = 1;
                    $email->receives_group_emails = 1;
                    $email->allow_messages        = 1;

                    $email->save();

                    $this->info('Added email.');
                } elseif (!empty($worksheet->getCellByColumnAndRow(3, $r)->getValue())) {
                    $this->warn('Email: ' . $worksheet->getCellByColumnAndRow(3, $r)->getValue() . ' for user ID: ' . $user->id . ' is already in the database.');
                }

                // First make sure we haven't added it already.
                if (!Phone::where('number', Phone::format($worksheet->getCellByColumnAndRow(4, $r)->getValue()))->exists()) {
                    $phone = new Phone();

                    $phone->created_at       = now();
                    $phone->number           = Phone::format($worksheet->getCellByColumnAndRow(4, $r)->getValue());
                    $phone->is_primary       = 1;
                    $phone->is_family        = 0;
                    $phone->is_hidden        = 0;
                    $phone->messages_opt_out = 0;
                    $phone->type             = 'mobile';
                    $phone->user_id          = $user->id;

                    $phone->save();

                    $this->info('Added mobile phone.');
                } elseif (!empty($worksheet->getCellByColumnAndRow(4, $r)->getValue())) {
                    $this->warn('Mobile Phone: ' . $worksheet->getCellByColumnAndRow(4, $r)->getValue() . ' for user ID: ' . $user->id . ' is already in the database.');
                }
                // First make sure we haven't added it already.
                if (!Phone::where('number', Phone::format($worksheet->getCellByColumnAndRow(5, $r)->getValue()))->exists()) {
                    $phone = new Phone();

                    $phone->created_at       = now();
                    $phone->number           = Phone::format($worksheet->getCellByColumnAndRow(5, $r)->getValue());
                    $phone->is_primary       = 0;
                    $phone->is_family        = 1;
                    $phone->is_hidden        = 0;
                    $phone->messages_opt_out = 0;
                    $phone->type             = 'home';
                    $phone->user_id          = $user->id;
                    $phone->family_id        = $user->id;

                    $phone->save();

                    $this->info('Added home phone.');
                } elseif (!empty($worksheet->getCellByColumnAndRow(5, $r)->getValue())) {
                    $this->warn('Home Phone: ' . $worksheet->getCellByColumnAndRow(5, $r)->getValue() . ' for user ID: ' . $user->id . ' is already in the database.');
                }

                if (!Address::where('address1', Phone::format($worksheet->getCellByColumnAndRow(13, $r)->getValue()))->exists()
                    && !empty($worksheet->getCellByColumnAndRow(13, $r)->getValue())) {
                    $address = new Address();

                    $address->created_at = now();
                    $address->type       = 'home';
                    $address->label      = 'Home Address';
                    $address->address1   = $worksheet->getCellByColumnAndRow(13, $r)->getValue();
                    $address->address2   = $worksheet->getCellByColumnAndRow(14, $r)->getValue();
                    $address->city       = $worksheet->getCellByColumnAndRow(15, $r)->getValue();
                    $address->state      = $worksheet->getCellByColumnAndRow(16, $r)->getValue();
                    $address->zip        = $worksheet->getCellByColumnAndRow(17, $r)->getValue();
                    $address->country    = 'US';
                    $address->user_id    = $user->id;

                    if ($worksheet->getCellByColumnAndRow(18, $r)->getValue() == 'X') {
                        $address->is_family = 1;
                        $address->family_id = $user->id;
                    }

                    $address->save();

                    $this->info('Added address.');
                }

                $r++;

                continue;
            }

            $user             = new User();
            $user->account_id = $this->account_id;

            // Core info
            $user->status    = 'active';
            $user->is_active = true;
            $user->timezone  = 'America/Chicago';

            $user->first_name = $worksheet->getCellByColumnAndRow(1, $r)->getValue();
            $user->last_name  = $worksheet->getCellByColumnAndRow(2, $r)->getValue();

            // If this belongs to a family.
            if (!empty($worksheet->getCellByColumnAndRow(6, $r)->getValue())) {
                $family_name = explode(',', $worksheet->getCellByColumnAndRow(6, $r)->getValue());

                $head_user_id = User::where('account_id', $this->account_id)
                    ->where('first_name', trim($family_name[1]))
                    ->where('last_name', trim($family_name[0]))
                    ->first()?->id;

                $user->family_id = $head_user_id;
            }

            // Save our user and get an ID.
            $user->save();

            // Head of house
            if ($worksheet->getCellByColumnAndRow(7, $r)->getValue() == 'X') {
                $user->family_role = 'head';
                $user->family_id   = $user->id;
            } // Spouse
            elseif ($worksheet->getCellByColumnAndRow(8, $r)->getValue() == 'X') {
                $user->family_role = 'spouse';
            } else {
                $user->family_role = 'child';
            }
            // Baptized
            if ($worksheet->getCellByColumnAndRow(9, $r)->getValue() == 'X') {
                $user->is_baptized = now();
            }

            // Is a Member
            if ($worksheet->getCellByColumnAndRow(10, $r)->getValue() == 'X') {
                // Assigned our groups/roles.
                $user->groups()->attach($member_group_id);
                $user->roles()->attach($member_role_id);
            }

            if (!empty($worksheet->getCellByColumnAndRow(11, $r)->getValue())) {
                try {
                    $user->birthdate = Carbon::createFromFormat('n/j/y', $worksheet->getCellByColumnAndRow(11, $r)->getValue())->format('Y-m-d');
                } catch (\Carbon\Exceptions\InvalidFormatException $e) {
                    $this->warn('Bad date for birthday on user ' . $user->id . ' -- ' . $worksheet->getCellByColumnAndRow(11, $r)->getValue());
                }
            }
            if (!empty($worksheet->getCellByColumnAndRow(12, $r)->getValue())) {
                try {
                    $user->date_married = Carbon::createFromFormat('n/j/y', $worksheet->getCellByColumnAndRow(12, $r)->getValue())->format('Y-m-d');
                } catch (\Carbon\Exceptions\InvalidFormatException $e) {
                    $this->warn('Bad date for anniversary on user ' . $user->id . ' -- ' . $worksheet->getCellByColumnAndRow(12, $r)->getValue());
                }
            }

            if (!empty($worksheet->getCellByColumnAndRow(13, $r)->getValue())) {
                $address = new Address();

                $address->created_at = now();
                $address->type       = 'home';
                $address->label      = 'Home Address';
                $address->address1   = $worksheet->getCellByColumnAndRow(13, $r)->getValue();
                $address->address2   = $worksheet->getCellByColumnAndRow(14, $r)->getValue();
                $address->city       = $worksheet->getCellByColumnAndRow(15, $r)->getValue();
                $address->state      = $worksheet->getCellByColumnAndRow(16, $r)->getValue();
                $address->zip        = $worksheet->getCellByColumnAndRow(17, $r)->getValue();
                $address->country    = 'US';
                $address->user_id    = $user->id;

                if ($worksheet->getCellByColumnAndRow(18, $r)->getValue() == 'X') {
                    $address->is_family = 1;
                    $address->family_id = $user->id;
                }

                $address->save();
            }

            $user->save();

            if (!Email::where('email', trim($worksheet->getCellByColumnAndRow(3, $r)->getValue()))->exists()) {
                $email = new Email();

                $email->email                 = trim($worksheet->getCellByColumnAndRow(3, $r)->getValue());
                $email->created_at            = now();
                $email->user_id               = $user->id;
                $email->type                  = 'personal';
                $email->is_primary            = 1;
                $email->receives_group_emails = 1;
                $email->allow_messages        = 1;

                $email->save();

                $this->info('Added email.');
            } elseif (!empty($worksheet->getCellByColumnAndRow(3, $r)->getValue())) {
                $this->warn('Email: ' . $worksheet->getCellByColumnAndRow(3, $r)->getValue() . ' for user ID: ' . $user->id . ' is already in the database.');
            }

            // First make sure we haven't added it already.
            if (!Phone::where('number', Phone::format($worksheet->getCellByColumnAndRow(4, $r)->getValue()))->exists()) {
                $phone = new Phone();

                $phone->created_at       = now();
                $phone->number           = Phone::format($worksheet->getCellByColumnAndRow(4, $r)->getValue());
                $phone->is_primary       = 1;
                $phone->is_family        = 0;
                $phone->is_hidden        = 0;
                $phone->messages_opt_out = 0;
                $phone->type             = 'mobile';
                $phone->user_id          = $user->id;

                $phone->save();

                $this->info('Added mobile phone.');
            } elseif (!empty($worksheet->getCellByColumnAndRow(4, $r)->getValue())) {
                $this->warn('Mobile Phone: ' . $worksheet->getCellByColumnAndRow(4, $r)->getValue() . ' for user ID: ' . $user->id . ' is already in the database.');
            }
            // First make sure we haven't added it already.
            if (!Phone::where('number', Phone::format($worksheet->getCellByColumnAndRow(5, $r)->getValue()))->exists()) {
                $phone = new Phone();

                $phone->created_at       = now();
                $phone->number           = Phone::format($worksheet->getCellByColumnAndRow(5, $r)->getValue());
                $phone->is_primary       = 0;
                $phone->is_family        = 1;
                $phone->is_hidden        = 0;
                $phone->messages_opt_out = 0;
                $phone->type             = 'home';
                $phone->user_id          = $user->id;
                $phone->family_id        = $user->id;

                $phone->save();

                $this->info('Added home phone.');
            } elseif (!empty($worksheet->getCellByColumnAndRow(5, $r)->getValue())) {
                $this->warn('Home Phone: ' . $worksheet->getCellByColumnAndRow(5, $r)->getValue() . ' for user ID: ' . $user->id . ' is already in the database.');
            }

            // Save our user and get an ID.
            $user->save();

            $this->info('Imported user ' . $user->name);
            $this->info('---------------------------');

            $r++;
        }

        $this->info('User import complete.');
    }
}
