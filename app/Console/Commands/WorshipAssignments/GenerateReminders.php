<?php

namespace App\Console\Commands\WorshipAssignments;

use App\Accounts\Account;
use App\Jobs\WorshipAssignments\SendReminder;
use App\WorshipAssignments\Pick;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wa:generate-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Finds and sends Worship Assignment reminders based on account settings.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * 1. Get active accounts.
     * 2. For each account:
     *   - Check that reminders are enabled.
     *   - Check if it's time to send notifications
     *   - Get WA Picks that are active right now.
     *   - Get the date occurrences for the picks.
     *   - If an occurrence is going to happen are the time we think ("days before"), send a notification.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('-- START GenerateReminders command --');
        $this->info('-- START GenerateReminders command --');

        // We set this so that if it takes more than a minute to initiate notifications, our start time doesn't change in later calculations.
        $start_time = now();

        $accounts = Account::where('is_active', 1)
            ->where('is_suspended', 0)
            ->where('status', 'active')
            ->get();

        Log::info('GenerateReminders: Found ' . $accounts->count() . ' active accounts.');
        $this->info('GenerateReminders: Found ' . $accounts->count() . ' active accounts.');

        foreach ($accounts as $account) {
            $start_time->setTimezone($account->timezone);

            // SEND our general reminders.
            $this->sendGeneralReminders($account, $start_time);

            // SEND our position specific reminders.
            $this->sendPositionSpecificReminders($account, $start_time);
        }

        Log::info('-- END GenerateReminders command --');
        $this->info('-- END GenerateReminders command --');
    }

    private function getValidGeneralPicks($account)
    {
        return Pick::where('account_id', $account->id)
            ->has('user')
            ->isActiveOrFuture()
            ->isPublished()
            ->isConfirmedOrNoResponse()
            ->where(function ($query) {
                // Last reminder was never, or before today.
                $query->whereNull('reminder_last_sent_at')
                    ->orWhere('reminder_last_sent_at', '<', now()->format('Y-m-d') . ' 00:00:00');
            })
            ->get();
    }

    private function getValidPositionSpecificPicks($account)
    {
        return Pick::where('account_id', $account->id)
            ->has('user')
            ->isActiveOrFuture()
            ->isPublished()
            ->isConfirmedOrNoResponse()
            ->where(function ($query) {
                // Last reminder was never for one of our 2 possible reminders.
                $query->whereNull('reminder_1_sent_at')
                    ->orWhereNull('reminder_2_sent_at');
            })
            ->get();
    }

    private function sendPositionSpecificReminders($account, $start_time)
    {
        Log::info('GenerateReminders::PositionSpecificReminders: Looking to send position specific reminders...');
        $this->info('GenerateReminders::PositionSpecificReminders: Looking to send position specific reminders...');

        $picks = $this->getValidPositionSpecificPicks($account);

        if (!$picks) {
            Log::info('GenerateReminders::PositionSpecificReminders: -- There are no picks to check for Position Specific Reminders!');
            $this->info('GenerateReminders::PositionSpecificReminders: -- There are no picks to check for Position Specific Reminders!');
        }

        foreach ($picks as $pick) {
            // If this position has no reminders, cancel.
            if (!$pick->position->remindersEnabled()) {
                continue;
            }

            foreach ($pick->getOccurrences() as $occurrence_date) {
                // REMINDER 1
                // If this occurrence minus the number of days in advance we should send a notification is TODAY.
                if (
                    $pick->position->reminder_1_enabled
                    &&
                    $pick->position->reminder_1_via_mobile_notification
                    &&
                    $start_time->format('G') == $pick->position->reminder_1_hour_to_send
                    &&
                    $occurrence_date->subDays($pick->position->reminder_1_days_before)->format('Y-m-d')
                    == $start_time->format('Y-m-d')
                    &&
                    !$pick->reminder_1_sent_at
                ) {
                    SendReminder::dispatch($pick);

                    $pick->reminder_1_sent_at = now();
                    $pick->save();

                    Log::info('QUEUE MOBILE NOTIFICATION');
                    Log::info('GenerateReminders::PositionSpecificReminders: -- Queued reminder_1 notification to user # ' . $pick->user_id);
                    $this->info('GenerateReminders::PositionSpecificReminders: -- Queued reminder_1 notification to user # ' . $pick->user_id);
                } else {
                    Log::info('GenerateReminders::PositionSpecificReminders: -- Checked reminder_1 but did not match conditions to send. -- Pick # ' . $pick->id . ' @ ' . $occurrence_date->format('Y-m-d'));
                    $this->info('GenerateReminders::PositionSpecificReminders: -- Checked reminder_1 but did not match conditions to send. -- Pick # ' . $pick->id . ' @ ' . $occurrence_date->format('Y-m-d'));
                }

                // REMINDER 2
                // If this occurrence minus the number of days in advance we should send a notification is TODAY.
                if (
                    $pick->position->reminder_2_enabled
                    &&
                    $pick->position->reminder_2_via_mobile_notification
                    &&
                    $start_time->format('G') == $pick->position->reminder_2_hour_to_send
                    &&
                    $occurrence_date->subDays($pick->position->reminder_2_days_before)->format('Y-m-d')
                    == $start_time->format('Y-m-d')
                    &&
                    !$pick->reminder_2_sent_at
                ) {
                    SendReminder::dispatch($pick);

                    $pick->reminder_2_sent_at = now();
                    $pick->save();

                    Log::info('QUEUE MOBILE NOTIFICATION');
                    Log::info('GenerateReminders::PositionSpecificReminders: -- Queued reminder_2 notification to user # ' . $pick->user_id);
                    $this->info('GenerateReminders::PositionSpecificReminders: -- Queued reminder_2 notification to user # ' . $pick->user_id);
                } else {
                    Log::info('GenerateReminders::PositionSpecificReminders: -- Checked reminder_2 but did not match conditions to send. -- Pick # ' . $pick->id . ' @ ' . $occurrence_date->format('Y-m-d'));
                    $this->info('GenerateReminders::PositionSpecificReminders: -- Checked reminder_2 but did not match conditions to send. -- Pick # ' . $pick->id . ' @ ' . $occurrence_date->format('Y-m-d'));
                }
            }
        }
    }

    private function sendGeneralReminders($account, $start_time)
    {
        if (!$this->remindersAreEnabled($account)) {
            return;
        }

        if (!$this->isTimeToSend($account, $start_time)) {
            return;
        }

        Log::info('GenerateReminders: Account # ' . $account->id . ' is active, enabled and time to send!');
        $this->info('GenerateReminders: Account # ' . $account->id . ' is active, enabled and time to send!');

        $picks = $this->getValidGeneralPicks($account);

        if (!$picks) {
            Log::info('GenerateReminders: -- There are no picks to check!');
            $this->info('GenerateReminders: -- There are no picks to check!');
        }

        foreach ($picks as $pick) {
            // If this position has reminders, do not send general ones.
            if ($pick->position->remindersEnabled()) {
                continue;
            }

            foreach ($pick->getOccurrences() as $occurrence_date) {
                // If this occurrence minus the number of days in advance we should send a notification is TODAY.
                if (
                    $occurrence_date->subDays($account->getSettingValue('wa.send_days_before'))->format('Y-m-d')
                    == $start_time->format('Y-m-d')
                ) {
                    SendReminder::dispatch($pick);

                    $pick->reminder_last_sent_at = now();
                    $pick->save();

                    Log::info('QUEUE MOBILE NOTIFICATION');
                    Log::info('GenerateReminders: -- Queued notification to user # ' . $pick->user_id);
                    $this->info('GenerateReminders: -- Queued notification to user # ' . $pick->user_id);
                }
            }
        }
    }

    private function isTimeToSend($account, $start_time)
    {
        // Current hour compared to the hour we're supposed to send notifications.
        if ($start_time->format('G') == $account->getSettingValue('wa.hour_to_send')) {
            return true;
        }

        Log::info('GenerateReminders: Account # ' . $account->id . ' -- not time to send. Hour is ' . $start_time->format('G') . ' but account send hour is ' . $account->getSettingValue('wa.hour_to_send') . '.  Skipping...');
        $this->info('GenerateReminders: Account # ' . $account->id . ' -- not time to send. Hour is ' . $start_time->format('G') . ' but account send hour is ' . $account->getSettingValue('wa.hour_to_send') . '.  Skipping...');

        return false;
    }

    private function remindersAreEnabled($account)
    {
        // If this account doesn't have worship assignments enabled, skip it.
        if (!$account->hasFeature('feature.worship_assignments')) {
            Log::info('GenerateReminders: Account does not have Worship Assignments enabled.  Skipping...', [
                'account_id' => $account->id,
            ]);
            $this->info('GenerateReminders: Account #' . $account->id . ' does not have Worship Assignments enabled.  Skipping...');

            return false;
        }

        // If we have disabled reminders, skip this account.
        if (!$account->getSettingValue('wa.enable_reminders')) {
            Log::info('GenerateReminders: Account does not have Worship Assignment reminders enabled.  Skipping...', [
                'account_id' => $account->id,
            ]);
            $this->info('GenerateReminders: Account #' . $account->id . ' does not have Worship Assignment reminders enabled.  Skipping...');

            return false;
        }

        // If we enabled reminders, but disabled both mobile and email notifications, skip this account and generate a warning.
        if (!$account->getSettingValue('wa.send_mobile_notifications') && !$account->getSettingValue('wa.send_email_notifications')) {
//            Log::warning('GenerateReminders: Account has Worship Assignment reminders enabled, BUT has disabled both mobile and email notifications.  Skipping...', [
//                'account_id' => $account->id,
//            ]);
            $this->info('GenerateReminders: Account #' . $account->id . ' has Worship Assignment reminders enabled, BUT has disabled both mobile and email notifications.  Skipping...');

            return false;
        }

        return true;
    }
}
