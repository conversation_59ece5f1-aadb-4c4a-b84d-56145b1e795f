<?php

namespace App\Console\Commands;

use App\Jobs\ProcessMessage;
use App\Messages\Message;
use Illuminate\Console\Command;

class PickUpNewMessages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'messages:process-new';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $messages = Message::where('status', 'new')->get();

        foreach ($messages as $message) {
            $message->status = 'processing';
            $message->save();
            ProcessMessage::dispatch($message)->onQueue('emails');
        }
    }
}
