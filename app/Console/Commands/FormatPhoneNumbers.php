<?php

namespace App\Console\Commands;

use App\Users\Phone;
use Illuminate\Console\Command;

class FormatPhoneNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'utility:format-phone-numbers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $formatted_count = 0;
        $phones          = Phone::get();

        $this->info('Found ' . $phones->count() . ' phone numbers to format.');

        foreach ($phones as $phone) {
            $formatted = null;
            $formatted = Phone::format($phone->number);

            if ($phone->number !== $formatted) {
                $this->info('#' . $phone->id . ' -- Changing ' . $phone->number . ' to ' . $formatted);

                $phone->number = $formatted;

                $phone->save();

                $formatted_count++;
            }
        }

        $this->info('Completed.  Formatted & updated ' . $formatted_count . ' phone numbers.');
    }
}
