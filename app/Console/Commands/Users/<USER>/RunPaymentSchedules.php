<?php

namespace App\Console\Commands\Users\Payments;

use App\Jobs\Users\Payments\ChargePaymentScheduleJob;
use App\Users\PaymentSchedule;
use App\Users\Services\ChargePaymentSchedule;
use Illuminate\Console\Command;

class RunPaymentSchedules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:user-payment-schedules {--charge} {--stats} {--list}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List, stat or find and attempt to charge all active user payment schedules via job queues.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // Get all active payment schedules
        $payment_schedules = PaymentSchedule::IsNotPaused()->orderBy('account_id', 'asc')->get();

        // If we're just wanting to list all active payment schedules, do that and return.
        // Could be a LOT!
        if ($this->option('list')) {
            return $this->list($payment_schedules);
        }

        if ($this->option('stats')) {
            return $this->stats($payment_schedules);
        }

        if ($this->option('charge')) {
            $this->newLine();
            $this->info('Total active payment schedules: ' . $payment_schedules->count());
            $this->info('Running payment schedules and putting them on the queue...');

            $bar = $this->output->createProgressBar($payment_schedules->count());
            $bar->start();

            // Loop through each payment schedule
            foreach ($payment_schedules as $payment_schedule) {
                // If we have recurring giving turned off account-wide, don't process this payment schedule.
                if (!$payment_schedule->account->hasFeature('account.setting.giving.enable_payment_schedules')) {
                    $this->error('Recurring giving is not enabled for this account: ' . $payment_schedule->account_id);
                    continue;
                }

                try {
                    $charge_payment_schedule = (new ChargePaymentSchedule($payment_schedule));

                    // Run this as a job on the queue.
                    // Once we have hundreds of scheduled payments, this command will take too long to run.
                    ChargePaymentScheduleJob::dispatch($charge_payment_schedule);
                } catch (\Exception $e) {
                    $this->error('  Error for Schedule ID # ' . $payment_schedule->id . ' for ' . $payment_schedule?->user->name . ': ' . $e->getMessage());
                }

                $bar->advance();
            }

            $bar->finish();

            $this->newLine();
            $this->info('All payment schedules have been queued for processing.');

            $this->newLine();
            return 0;
        }

        return 1;
    }

    private function stats($payment_schedules): int
    {
        $this->newLine();

        $this->table(
            ['Active', 'Weekly', 'Monthly'],
            [
                [
                    $payment_schedules->count(),
                    $payment_schedules->where('recur_frequency', 'WEEKLY')->count(),
                    $payment_schedules->where('recur_frequency', 'MONTHLY')->count(),
                ],
            ],
        );

        $this->info('Across ' . $payment_schedules->unique('account_id')->count() . ' accounts.');

        $this->newLine();
        return 0;
    }

    private function list($payment_schedules): int
    {
        $this->newLine();

        // If we have more than 50 active payment schedules, limit what we print out to the last 50.
        if ($payment_schedules->count() > 50) {
            $this->warn('There are ' . $payment_schedules->count() . ' active payment schedules! Limiting output to the last 50 schedules...');
        }

        $this->table(
            ['ID', 'User', 'Amount', 'Frequency', 'Last Run'],
            $payment_schedules->sortByDesc('id')->take(50)->map(function ($payment_schedule) {
                return [
                    $payment_schedule->id,
                    $payment_schedule->user->name,
                    $payment_schedule->amount > 1000000 ?: '-', // Only show an amount over 10,000 as something to look into.
                    $payment_schedule->recur_frequency,
                    $payment_schedule->last_run_at,
                ];
            }),
        );

        $this->newLine();
        return 0;
    }
}
