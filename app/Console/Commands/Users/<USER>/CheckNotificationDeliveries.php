<?php

namespace App\Console\Commands\Users\Notifications;

use App\Users\NotificationTarget;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CheckNotificationDeliveries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lightpost:user-check-notification-deliveries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Look up any unchecked user notification targets and verify delivery with the provider.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // Get all unchecked targets that have a provider transaction id and were created more than 3 minutes ago.
        $unchecked_targets = NotificationTarget::where('is_confirmed', 0)
            ->whereNotNull('provider_transaction_id')
            ->where('created_at', '<=', now()->subMinutes(3))
            ->orderBy('created_at', 'asc')
            ->limit(500) // Limit is 1,000: https://docs.expo.dev/push-notifications/sending-notifications/#request-errors
            ->get();

        $this->newLine();
        $this->info('New notifications targets to check: ' . $unchecked_targets->count());
        $this->info('Connecting to provider to get delivery status...');

        // SETUP
        $errors = 0;
        $bar    = $this->output->createProgressBar($unchecked_targets->count());
        $bar->start();
        $client = new \GuzzleHttp\Client();

        $response = $client->request('POST', 'https://exp.host/--/api/v2/push/getReceipts', [
            'headers' => [
                'host'            => 'exp.host',
                'accept'          => 'application/json',
                'accept-encoding' => 'gzip, deflate',
                'content-type'    => 'application/json',
                'Authorization'   => 'Bearer ' . config('services.expo.access_token'),
            ],
            'body'    => json_encode([
                'ids' => $unchecked_targets->pluck('provider_transaction_id'),
            ]),
        ]);

        // If our request failed completely.
        if ((int)$response->getStatusCode() != 200) {
            Log::error('CheckNotificationDeliveries::handle - Expo Request Failed', [
                'body' => $response->getBody()->getContents(),
            ]);

            $this->error('Expo query was not successful. Response content: ' . $response->getBody()->getContents());

            return 1;
        }

        // Check each notification (target) with the provider.
        // This is SPECIFIC TO EXPO MOBILE NOTIFICATIONS
        // @TODO: This needs to be updated when we start sending other types of notifications.
        foreach (json_decode($response->getBody()->getContents())?->data as $message_id => $message_status) {
            $target = $unchecked_targets->where('provider_transaction_id', $message_id)->first();

            if ($target) {
                // Check for errors sending
                if ($message_status?->status != 'ok') {
                    Log::warning('CheckNotificationDeliveries::handle - Specific notification delivery failed', [
                        'user_notification_id' => $target->user_notification_id,
                        'expo_id'              => $message_id,
                        'message'              => $message_status,
                        'details'              => $message_status?->details,
                    ]);

                    // We only have 200 chars to store a message
                    $target->error_message = Str::limit($message_status?->details?->error . ':' . $message_status?->message, 300, '');

                    // Disable this device if error is "DeviceNotRegistered". This likely means the user has uninstalled the app or disabled notifications.
                    if (property_exists($message_status, 'details') && $message_status?->details?->error == 'DeviceNotRegistered') {
                        $device = $target->device;

                        // If we have a device with this specific error returned, disable it and set the deleted reason.
                        if ($device) {
                            $device->receives_notifications = 0;
                            $device->deleted_reason         = 'DeviceNotRegistered';
                            $device->save();

                            $device->delete();
                        }
                    } elseif (property_exists($message_status, 'details') && $message_status?->details?->error == 'ProviderError') {
                        Log::error('CheckNotificationDeliveries::handle - ProviderError', [
                            'user_notification_id' => $target->user_notification_id,
                            'expo_id'              => $message_id,
                            'message'              => $message_status?->message,
                            'details'              => $message_status?->details->error,
                        ]);
                    }

                    $target->is_ok = 0;
                    $errors++;
                }

                $target->is_confirmed = 1;
                $target->save();
            }
            $bar->advance();
        }

        $bar->finish();

        $this->newLine();
        $this->info('Operation complete. Found ' . $errors . ' problems.');

        $this->newLine();
        return 0;
    }
}
