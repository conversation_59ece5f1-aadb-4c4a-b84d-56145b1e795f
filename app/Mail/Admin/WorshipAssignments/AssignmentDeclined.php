<?php

namespace App\Mail\Admin\WorshipAssignments;

use App\Accounts\AccountNotification;
use App\WorshipAssignments\Pick;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AssignmentDeclined extends Mailable
{
    use Queueable, SerializesModels;

    public $pick;
    public $group;
    public $period;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($pick_id)
    {
        $pick = Pick::find($pick_id);

        $this->pick   = $pick;
        $this->group  = $pick->group;
        $this->period = $pick->period;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        try {
            AccountNotification::quickCreate($this->group->account_id, 1, 'wa.assignmentDeclined', $this->pick->user_id, null);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        return $this->view('emails.admin.worship-assignments.assignment-declined')
            ->subject('❌ Assignment Declined - ' . $this->pick?->user?->name)
            ->with('pick', $this->pick);
    }
}
