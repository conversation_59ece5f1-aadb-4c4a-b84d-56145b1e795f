<?php

namespace App\Mail\Admin\Groups;

use App\Accounts\AccountNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ConfirmGroupEmailReceived extends Mailable
{
    use Queueable, SerializesModels;

    public $pick;
    public $group;
    public $period;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($pick)
    {
        $this->pick   = $pick;
        $this->group  = $pick->group;
        $this->period = $pick->period;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        try {
            AccountNotification::quickCreate($this->group->account_id, 1, 'groups.confirmGroupEmail', $this->pick->user_id, null);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        return $this->view('emails.admin.groups.group-email-confirmation')
            ->subject('Group Email Sent')
            ->sentEmailSubject();
    }
}
