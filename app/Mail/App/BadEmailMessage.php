<?php

namespace App\Mail\App;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BadEmailMessage extends Mailable
{
    use Queueable, SerializesModels;

    public $from_user;
    public $message;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($message)
    {
        $this->message = $message;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.app.bad-email-message')
            ->from('<EMAIL>', '<PERSON>')
            ->subject('Oops!  Your Group email failed to send.')
            ->with('message', $this->message);
    }
}
