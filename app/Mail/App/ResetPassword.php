<?php

namespace App\Mail\App;

use App\Accounts\AccountNotification;
use App\Users\Email;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ResetPassword extends Mailable
{
    use Queueable, SerializesModels;

    public $email;
    public $token;
    public $is_welcome_email;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(string $email, string $token, bool $is_welcome_email = false)
    {
        $this->email            = $email;
        $this->token            = $token;
        $this->is_welcome_email = $is_welcome_email;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $emailObject = Email::where('email', 'LIKE', $this->email)->first();

        if (!$emailObject) {
            Log::info("Could not find user for password reset for email: " . $this->email);
            return;
        }

        $user = $emailObject->user;

        try {
            AccountNotification::quickCreate($user->account_id, 1, ($this->is_welcome_email ? 'welcomeEmail' : 'resetPassword'), $user->id, null);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        $this->view('emails.app.reset-password')
            ->subject(($this->is_welcome_email ? $user?->account?->name . ' - Welcome to Lightpost!' : 'Password Reset'))
            ->with('email', $this->email)
            ->with('user', $user)
            ->with('token', $this->token)
            ->with('is_welcome_email', $this->is_welcome_email)
            ->with('reset_link', 'https://' . config('app.domains.app') . '/password/reset/' . $this->token . '?email=' . $this->email);

        // Set our MessageStream
        $this->withSymfonyMessage(function (\Symfony\Component\Mime\Email $message) {
            $message->getHeaders()
                ->addTextHeader('X-PM-Message-Stream', config('mail.streams.password_resets'));
        });

        return $this;
    }
}
