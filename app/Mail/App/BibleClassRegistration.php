<?php

namespace App\Mail\App;

use App\BibleClasses\BibleClass;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BibleClassRegistration extends Mailable
{
    use Queueable, SerializesModels;

    public $name;
    public $email;
    public $classes;
    public $group_id = null;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($name, $email, $selected_classes)
    {
        $this->name  = $name;
        $this->email = $email;

        $classes = [];

        if (is_array($selected_classes)) {
            foreach ($selected_classes as $group_id => $classes_selected):

                foreach ($classes_selected as $class_id):
                    if (is_numeric($class_id)) {
                        $bible_class = BibleClass::find($class_id);
                        $classes[]   = $bible_class;
                        // Set our group_id for use in the groups view route below.
                        $this->group_id = $bible_class->bible_class_group_id;
                    } elseif ($class_id == 'teaching') {
                        // Create a fake class so we can pull ->title in the email.
                        $temp_class        = new \stdClass();
                        $temp_class->title = 'I am teaching a class.';
                        $classes[]         = $temp_class;
                    }
                endforeach;

            endforeach;
        }

        $this->classes = $classes;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.app.bible-class-registration')
            ->with('name', $this->name)
            ->with('email', $this->email)
            ->with('classes', $this->classes)
            ->with('goto_link', $this->group_id === null ? route('admin.bible-classes.index') : route('admin.bible-classes.groups.view', $this->group_id));
    }
}
