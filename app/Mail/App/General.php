<?php

namespace App\Mail\App;

use App\Users\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class General extends Mailable
{
    use Queueable, SerializesModels;

    public User $from_user;
    public      $message;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $from_user, $message)
    {
        $this->from_user = $from_user;
        $this->message   = $message;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.app.general')
            ->subject('Secure Message from ' . $this->from_user->first_name)
            ->with('message', $this->message)
            ->with('from_user', $this->from_user)
            ->with('account_title', $this->from_user?->account?->name);
    }
}
