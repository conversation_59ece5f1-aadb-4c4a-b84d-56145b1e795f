<?php

namespace App\Mail\App\Visitors;

use App\Accounts\AccountNotification;
use App\Users\User;
use App\Visitors\History;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class NewHistoryEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $history;
    public $user_to_notify;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(History $history, User $user_to_notify)
    {
        $this->history        = $history;
        $this->user_to_notify = $user_to_notify;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        try {
            $charge = optional($this->history->account->plan)->price_per_email;

            AccountNotification::quickCreate($this->user_to_notify->account_id, 1, 'visitors.HistoryNotification', $this->user_to_notify->id, null, $charge);
        } catch (\Exception $e) {
            Log::error($e);
        }

        $this->markdown('emails.app.visitors.new-history')
            ->subject('Visitor Update -- ' . $this->history->visitor->mainUser()->name)
            ->with('history', $this->history)
            ->with('user', $this->user_to_notify)
            ->with('visitor_url', route('app.visitors.view', $this->history->visitor));

        $this->withSymfonyMessage(function (\Symfony\Component\Mime\Email $message) {
            $message->getHeaders()
                ->addTextHeader(
                    'X-PM-Message-Stream', config('mail.streams.visitor_notifications')
                );
        });

        return $this;
    }
}
