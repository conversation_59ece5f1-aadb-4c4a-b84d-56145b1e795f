<?php

namespace App\Mail\Crises;

use App\Accounts\AccountNotification;
use App\Crises\Checkin;
use App\Users\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class NewCheckinHelpRequest extends Mailable
{
    use Queueable, SerializesModels;

    public $send_to_user;
    public $checkin;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Checkin $checkin, User $send_to_user)
    {
        $this->checkin      = $checkin;
        $this->send_to_user = $send_to_user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        try {
            $charge = optional($this->checkin->account->plan)->price_per_email;

            AccountNotification::quickCreate($this->send_to_user->account_id, 1, 'crisis.CheckinHelpResponse', $this->send_to_user->id, null, $charge);
        } catch (\Exception $e) {
            Log::error($e);
        }

        $subject = 'Check-in Help Request!';
        if ($this->checkin->isHelpRequest()) {
            $subject = '❣️ Check-in Help Request';
        } elseif ($this->checkin->isUrgentHelpRequest()) {
            $subject = '❗️️URGENT Check-in Help Request';
        }

        $this->markdown('emails.app.crises.crisis-help-request')
            ->subject($subject . ' from family: ' . $this->checkin->family->name)
            ->with('checkin', $this->checkin)
            ->with('checkin_url', route('app.crises.checkins.view', [$this->checkin->crisis, $this->checkin]))
            ->with('crisis_url', route('app.crises.view', $this->checkin->crisis));

        $this->withSymfonyMessage(function (\Symfony\Component\Mime\Email $message) {
            $message->getHeaders()
                ->addTextHeader(
                    'X-PM-Message-Stream', config('mail.streams.crisis_checkins')
                );
        });

        return $this;
    }
}
