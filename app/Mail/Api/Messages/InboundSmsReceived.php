<?php

namespace App\Mail\Api\Messages;

use App\Accounts\AccountNotification;
use App\Users\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class InboundSmsReceived extends Mailable
{
    use Queueable, SerializesModels;

    public $account_id;
    public $from_phone;
    public $best_email;
    public $sender;
    public $text;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $sender, string $from_phone, string $text)
    {
        $this->sender     = $sender;
        $this->account_id = $sender->account_id;
        $this->from_phone = $from_phone;
        $this->text       = $text;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if ($this->sender) {
            $this->best_email = optional($this->sender->getBestEmail())->email;
        }

        try {
            AccountNotification::quickCreate($this->account_id, 1, 'notifyInboundSms', null, null);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        // We need to enable this again for the sender.
//        return $this->from('<EMAIL>', 'Drew Johnston')
//            ->subject('Lightpost - Text Message Reply')
//            ->markdown('emails.api.messages.inbound-sms-received ', [
//                'from_phone' => $this->from_phone,
//                'best_email' => $this->best_email,
//                'sender'     => $this->sender,
//                'text'       => $this->text,
//            ]);
    }
}
