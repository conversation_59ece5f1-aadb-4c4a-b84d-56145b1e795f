<?php

namespace App\Podcasts\Policies;

use App\Podcasts\Podcast;
use App\Users\User;

class PodcastPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.podcasts')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('podcasts.index');
    }

    public function search(User $user)
    {
        return $user->hasPermission('podcasts.index');
    }

    public function create(User $user)
    {
        return $user->hasPermission('podcasts.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('podcasts.manage');
    }

    public function edit(User $user, Podcast $podcast)
    {
        return $user->hasPermission('podcasts.manage');
    }

    public function save(User $user, Podcast $podcast)
    {
        return $user->hasPermission('podcasts.manage');
    }

    public function view(User $user, Podcast $podcast)
    {
        return $user->hasPermission('podcasts.index');
    }

    public function delete(User $user)
    {
        return $user->hasPermission('podcasts.delete');
    }
}