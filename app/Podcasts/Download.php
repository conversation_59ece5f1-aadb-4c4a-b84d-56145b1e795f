<?php

namespace App\Podcasts;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Download extends Model
{
    use SoftDeletes;

    protected $table = 'podcast_track_downloads';

    const UPDATED_AT = null;

    protected $casts = [
        'created_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'podcast_id',
        'podcast_track_id',
        'ip_info',
        'ip_address',
        'region',
        'country',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function podcast()
    {
        return $this->belongsTo(Podcast::class);
    }

    public function track()
    {
        return $this->belongsTo(Track::class);
    }
}
