<?php

namespace App\Podcasts\Services;

use App\Podcasts\Podcast;
use Illuminate\Support\Str;
use SimpleXMLElement;

class CreatePodcastXml
{
    protected $attributes = [];
    protected $podcast;
    protected $download;

    public function create($attributes)
    {
        $this->attributes = $attributes;

        $this->attributes['account_id']       = $this->track->account_id;
        $this->attributes['podcast_id']       = $this->track->podcast_id;
        $this->attributes['podcast_track_id'] = $this->track->id;

        return $this->download;
    }

    public function forPodcast(Podcast $podcast)
    {
        $this->podcast = $podcast;

        return $this;
    }

    public function getXml()
    {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8" ?><rss version="2.0" xmlns:itunes="http://www.itunes.com/dtds/podcast-1.0.dtd" xmlns:content="http://purl.org/rss/1.0/modules/content/" />');

        $channel = $xml->addChild('channel');

        $channel->addChild('title', htmlentities($this->podcast->title ?: '', ENT_XML1));
        $channel->addChild('subtitle', htmlentities($this->podcast->subtitle ?: '', ENT_XML1));
//        $channel->addChild('author', htmlentities($this->podcast->author, ENT_XML1));
//        $channel->addChild('email', htmlentities($this->podcast->author_email, ENT_XML1));
        $channel->addChild('description', htmlentities($this->podcast->description ?: '', ENT_XML1));
        $channel->addChild('link', $this->podcast->getPodcastUrl());
        $channel->addChild('pubDate', optional($this->podcast->published_at)->format('r'));
        $channel->addChild('language', $this->podcast->language);
        $channel->addChild('generator', 'Lightpost');
        $channel->addChild('copyright', htmlentities($this->podcast->copyright ?: '', ENT_XML1));
        $channel->addChild('category', htmlentities($this->podcast->category ?: '', ENT_XML1))->addAttribute('text', $this->podcast->category);
//        $channel->addChild('__GOOGLEPLAY__author', $this->podcast->author);

        $image = $channel->addChild('image');
        $image->addChild('url', $this->podcast->image_url);
        $image->addChild('title', htmlentities($this->podcast->image_title ?: '', ENT_XML1));
        $image->addChild('link', $this->podcast->image_link);

        $channel->addChild('__ITUNES__image')->addAttribute('href', $this->podcast->image_url);
//        $channel->addChild('__GOOGLEPLAY__image')->addAttribute('href', $this->podcast->image_url);

//        $channel->addChild('__GOOGLEPLAY__image', '');

        // iTunes
        $channel->addChild('__ITUNES__subtitle', htmlentities($this->podcast->subtitle ?: '', ENT_XML1));
        $channel->addChild('__ITUNES__summary', htmlentities($this->podcast->description ?: '', ENT_XML1));
        $channel->addChild('__ITUNES__author', htmlentities($this->podcast->author ?: '', ENT_XML1));
        $channel->addChild('__ITUNES__email', htmlentities($this->podcast->author_email ?: '', ENT_XML1));
        $channel->addChild('__ITUNES__category')->addAttribute('text', $this->podcast->category);
        $channel->addChild('__ITUNES__type', Str::ucfirst($this->podcast->type));
        $channel->addChild('__ITUNES__episodeType', 'Full');

        $itunes_owner = $channel->addChild('__ITUNES__owner');
        $itunes_owner->addChild('__ITUNES__name', 'Lightpost Podcasts');
        $itunes_owner->addChild('__ITUNES__email', '<EMAIL>');

        // Owner
//        $owner = $channel->addChild('owner');
//        $owner->addChild('name', 'Lightpost Podcasts');
//        $owner->addChild('email', '<EMAIL>');

        // Explicit
        $channel->addChild('__ITUNES__explicit', $this->podcast->is_explicit ? 'yes' : 'no');
        $channel->addChild('explicit', $this->podcast->is_explicit ? 'yes' : 'no');

        if ($this->podcast->frequency == 'complete') {
            $channel->addChild('__ITUNES__complete', 'Yes');
        }

        // Use our specified sort order from the Podcast attributes.
        $tracks = $this->podcast->tracks()
            ->orderBy('published_at', $this->podcast->episoder_order ?: 'desc')
            ->with([
                'podcast',
                'account',
            ])
            ->published()
            ->get();

        foreach ($tracks as $track) {
            $item = $channel->addChild('item');

            $item->addChild('author', htmlentities($track->author ?: '', ENT_XML1));
            $item->addChild('__ITUNES__author', htmlentities($track->author ?: '', ENT_XML1));
            $item->addChild('__ITUNES__duration', $track->duration);
            $item->addChild('__ITUNES__explicit', $track->is_explicit ? 'yes' : 'no');
            $item->addChild('__ITUNES__title', htmlentities($track->title ?: '', ENT_XML1));
            $item->addChild('__ITUNES__subtitle', htmlentities($track->summary ?: '', ENT_XML1));
            $item->addChild('__ITUNES__summary', htmlentities($track->summary ?: '', ENT_XML1));

            $item->addChild('title', htmlentities($track->title ?: '', ENT_XML1));
            $item->addChild('summary', htmlentities($track->summary ?: '', ENT_XML1));
            $item->addChild('link', $track->getPodcastTrackUrl());
            $item->addChild('pubDate', optional($track->published_at)->format('r'));
            $item->addChild('guid', $track->uuid);
            $item->addChild('description', htmlentities($track->description ?: '', ENT_XML1));

            $enclosure = $item->addChild('enclosure', '');
            $enclosure->addAttribute('url', str_replace(' ', '+', $track->getMP3TrackingUrl()));
            $enclosure->addAttribute('type', $track->mp3_type ?: 'audio/mpeg');
            $enclosure->addAttribute('length', $track->mp3_file_size); // File size ??

            $item->addChild('__CONTENT__encoded', htmlentities($track->description ?: '', ENT_XML1));

            if ($track->episode) {
                $item->addChild('__ITUNES__episode', $track->episode);
            }
            if ($track->season) {
                $item->addChild('__ITUNES__season', $track->season);
            }
        }

        $xml_string = $xml->asXml();

        // SimpleXML doesn't allow entities with a semicolon, so we have to manually modify our XML to do this.
        $xml_string = str_replace('__ITUNES__', 'itunes:', $xml_string);
        $xml_string = str_replace('__CONTENT__', 'content:', $xml_string);
//        $xml_string = str_replace('__GOOGLEPLAY__', 'googleplay:', $xml_string);

        return $xml_string;
    }
}
