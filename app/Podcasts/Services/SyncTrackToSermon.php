<?php

namespace App\Podcasts\Services;

use App\Podcasts\Track;
use App\Sermons\Sermon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SyncTrackToSermon
{
    protected $attributes = [];
    protected $sermon     = null;
    protected $podcast    = null;
    protected $track      = null;

    public function sync($attributes = [])
    {
        if (!$this->track || $this->sermon->id !== $this->track->sermon_id) {
            return false;
        }

        // If we no longer have the tag on the sermon, delete the track.
        if (!$this->sermon) {
            $this->podcast = $this->track->podcast;
        }

        // If we missed filling in data before, fill that automatically.
        if (!$this->track->summary) {
            $this->attributes['summary'] = $this->sermon->summary;
        }
        if (!$this->track->description) {
            $this->attributes['description'] = $this->sermon->description;
        }
        if (!$this->track->author) {
            $this->attributes['author'] = $this->sermon->speaker;
        }

        $sermon_file = $this->sermon->files()->first();

        // If we don't have a duration, get that
        if ($sermon_file) {
            try {
                // Temp file name for local storage, since our MP3 library only reads local files.
                $temp_name = uniqid(true) . '.' . $sermon_file->file_extension;

                // Download and store
                Storage::disk('local')->put($temp_name, file_get_contents(str_replace(" ", '%20', $sermon_file->getUrl())));
                // Why urlencode doesn't work, I have no idea!  Resorting to a str_replace for spaces.

                // Get MP3 data
                $audio_info = $this->getAudioInfo(Storage::disk('local')->path($temp_name));

                // Save duration
                $sermon_file->duration            = Track::getDurationFromSeconds((int) Arr::get($audio_info, 'playtime_seconds'));
                $sermon_file->duration_in_seconds = Arr::get($audio_info, 'playtime_seconds');
                $sermon_file->file_size           = Storage::disk('local')->size($temp_name);
                $sermon_file->save();

                // Delete the file
                Storage::disk('local')->delete($temp_name);
            } catch (\Exception $e) {
                // Delete the file regardless.
                Storage::disk('local')->delete($temp_name);

                Log::error('Resync: Could not download/read MP3 file for Sermon.', [
                    'sermon_id'      => $this->sermon->id,
                    'sermon_file_id' => $sermon_file?->id,
                    'message'        => $e->getMessage(),
                ]);
            }
        }

        if ($sermon_file) {
            $this->attributes['mp3_url']       = $sermon_file->getUrl();
            $this->attributes['mp3_file_name'] = $sermon_file->getFileName();
            $this->attributes['mp3_type']      = 'audio/mpeg';
            $this->attributes['mp3_file_size'] = $sermon_file->file_size;
            $this->attributes['duration']      = $sermon_file->duration;
        }

        // If we were given attributes, override our defaults set above.
        foreach ($attributes as $key => $value) {
            $this->attributes[$key] = $value;
        }

        $this->track
            ->fill($this->attributes)
            ->save();

        // If we STILL don't have the MP3 file information that we need, report an error.
        if (!$this->track->duration || !$this->track->mp3_url || !$this->track->mp3_file_size) {
            $this->track->has_sync_error     = true;
            $this->track->sync_error_message =
                'Re-sync failed. Unable to read or find the audio file attached to the sermon to get the size and duration of the audio. This might be an unsupported audio type or a missing file. Please try re-uploading the audio file, and then re-syncing this Podcast track.';

            $this->track->is_published = false;

            $this->track->save();

            Log::error('Resync: 2 - Could not download/read MP3 file for Sermon.', [
                'sermon_id' => $this->sermon->id,
            ]);

            return false;
        } else {
            $this->track->has_sync_error     = false;
            $this->track->sync_error_message = null;

            $this->track->is_published = true;

            $this->track->save();
        }

        return true;
    }

    public function fromSermon(Sermon $sermon)
    {
        $this->sermon = $sermon;

        return $this;
    }

    public function forTrack(Track $track)
    {
        $this->track = $track;

        return $this;
    }

    private function getAudioInfo($file_path)
    {
        // Initialize getID3 engine
        $getID3 = new \getID3;

        return $getID3->analyze($file_path);
    }
}
