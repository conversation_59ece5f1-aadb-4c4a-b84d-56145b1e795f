<?php

namespace App\Podcasts\Services;

use App\Podcasts\Track;
use App\Sermons\Sermon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UpdateTracksFromSermon
{
    protected $attributes = [];
    protected $sermon     = null;

    public function update($attributes = [])
    {
        if (!$this->sermon) {
            return false;
        }
        // Get any tracks for this sermon across all podcasts.
        $tracks = Track::where('sermon_id', $this->sermon->id)
            ->where('account_id', $this->sermon->account_id)
            ->get();

        // If we already have a track for this sermon/podcast, don't create another!
        if (!$tracks) {
            return false;
        }

        // Get our audio info first.
        $sermon_file = $this->sermon->files()->first();

        // If we don't have a duration, get that
        if ($sermon_file && !$sermon_file->duration) {
            try {
                // Temp file name for local storage, since our MP3 library only reads local files.
                $temp_name = uniqid(true) . '.' . $sermon_file->file_extension;

                // Download and store
                Storage::disk('local')->put($temp_name, file_get_contents(str_replace(" ", '%20', $sermon_file->getUrl())));
                // Why urlencode doesn't work, I have no idea!  Resorting to a str_replace for spaces.

                $audio_info = $this->getAudioInfo(Storage::disk('local')->path($temp_name));

                // Save duration
                $sermon_file->duration            = Track::getDurationFromSeconds((int) Arr::get($audio_info, 'playtime_seconds'));
                $sermon_file->duration_in_seconds = Arr::get($audio_info, 'playtime_seconds');
                $sermon_file->file_size           = Storage::disk('local')->size($temp_name);
                $sermon_file->save();

                // Delete the file
                Storage::disk('local')->delete($temp_name);
            } catch (\Exception $e) {
                // Delete the file regardless.
                Storage::disk('local')->delete($temp_name);

                Log::error('Could not download/read MP3 file for Sermon.', [
                    'sermon_id'      => $this->sermon->id,
                    'sermon_file_id' => $sermon_file->id,
                    'message'        => $e->getMessage(),
                ]);
            }
        }

        if ($sermon_file) {
            $mp3_url       = $sermon_file->getUrl();
            $mp3_file_name = $sermon_file->getFileName();
            $mp3_type      = 'audio/mpeg';
            $mp3_file_size = $sermon_file->file_size;
            $duration      = $sermon_file->duration;
        }

        foreach ($tracks as $track):
            $track->description  = $this->sermon->description;
            $track->title        = $this->sermon->title;
            $track->url_title    = Str::slug($this->sermon->title);
            $track->summary      = $this->sermon->summary;
            $track->description  = $this->sermon->description;
            $track->published_at = $this->sermon->date_sermon->format('Y-m-d') . ' 12:00:00';
            $track->keywords     = '';
            $track->track_type   = '';

            $track->author       = $this->sermon->speaker;
            $track->author_email = $track->podcast->author_email;
            $track->comments     = false;
            $track->is_explicit  = false;

            if ($sermon_file) {
                $track->mp3_url       = $mp3_url;
                $track->mp3_file_name = $mp3_file_name;
                $track->mp3_type      = $mp3_type;
                $track->mp3_file_size = $mp3_file_size;
                $track->duration      = $duration;
            }

            // If we were given attributes, override our defaults set above.
            foreach ($attributes as $key => $value) {
                $this->attributes[$key] = $value;
            }

            $track->save();
        endforeach;

        return true;
    }

    public function forSermon(Sermon $sermon)
    {
        $this->sermon = $sermon;

        return $this;
    }

    private function getAudioInfo($file_path)
    {
        // Initialize getID3 engine
        $getID3 = new \getID3;

        return $getID3->analyze($file_path);
    }
}
