<?php

namespace App\Podcasts\Services;

use App\Podcasts\Podcast;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CreatePodcast
{
    protected $attributes    = [];
    protected $uploaded_file = null;
    protected $podcast;

    public function create($attributes): Podcast
    {
        $this->attributes = $attributes;

        // If we have a category and subcategory, so those correctly.
        if (Arr::get($this->attributes, 'category')) {
            $category_split = Podcast::splitCategory(Arr::get($this->attributes, 'category'));

            $this->attributes['category']    = $category_split['category'];
            $this->attributes['subcategory'] = !empty($category_split['subcategory']) ? $category_split['subcategory'] : null;
        }

        $this->uploadCoverImage();

        $this->podcast = Podcast::create($this->attributes);

        $this->podcast->image_link = $this->podcast->getPodcastUrl();
        $this->podcast->save();

        return $this->podcast;
    }

    public function withCoverImage(UploadedFile $uploaded_file)
    {
        $this->uploaded_file = $uploaded_file;

        return $this;
    }

    private function uploadCoverImage()
    {
        if ($this->uploaded_file->isValid()) {

            $extension = '.' . $this->uploaded_file->getClientOriginalExtension();

            $new_file_name = Arr::get($this->attributes, 'account_id') . '--' . Str::random(4) . '--' . $this->uploaded_file->getClientOriginalName();

            $url = config('filesystems.disks.podcast-files.url_endpoint') . '/images/' . Arr::get($this->attributes, 'account_id') . '/' . $new_file_name;

            $this->attributes['image_url'] = $url;

            if (!$this->uploaded_file->storePubliclyAs(
                'images/' . Arr::get($this->attributes, 'account_id'),
                $new_file_name,
                'podcast-files'
            )) {
                Log::error('uploadCoverImage::Could not write file to cloud server.');
            }

        }
    }
}
