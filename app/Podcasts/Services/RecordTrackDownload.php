<?php

namespace App\Podcasts\Services;

use App\Podcasts\Download;
use App\Podcasts\Track;

class RecordTrackDownload
{
    protected $attributes = [];
    protected $track;
    protected $download;

    public function create($attributes): Download
    {
        $this->attributes = $attributes;

        $this->attributes['account_id']       = $this->track->account_id;
        $this->attributes['podcast_id']       = $this->track->podcast_id;
        $this->attributes['podcast_track_id'] = $this->track->id;

        $this->download = Download::create($this->attributes);

        return $this->download;
    }

    public function forTrack(Track $track)
    {
        $this->track = $track;

        return $this;
    }
}
