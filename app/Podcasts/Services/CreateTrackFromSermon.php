<?php

namespace App\Podcasts\Services;

use App\Podcasts\Podcast;
use App\Podcasts\Track;
use App\Sermons\Sermon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CreateTrackFromSermon
{
    protected $attributes = [];
    protected $sermon     = null;
    protected $podcast    = null;
    protected $track      = null;

    public function create($attributes = [])
    {
        if (!$this->sermon || !$this->podcast) {
            return false;
        }

        // If we already have a track for this sermon/podcast, don't create another!
        if (Track::where('sermon_id', $this->sermon->id)->where('podcast_id', $this->podcast->id)->exists()) {
            return false;
        }

        $this->attributes['description'] = $this->sermon->description;

        $this->attributes['account_id']   = $this->podcast->account_id;
        $this->attributes['uuid']         = uniqid();
        $this->attributes['title']        = $this->sermon->title;
        $this->attributes['url_title']    = Str::slug($this->sermon->title);
        $this->attributes['summary']      = $this->sermon->summary;
        $this->attributes['description']  = $this->sermon->description;
        $this->attributes['podcast_id']   = $this->podcast->id;
        $this->attributes['sermon_id']    = $this->sermon->id;
        $this->attributes['published_at'] = $this->sermon->date_sermon->format('Y-m-d') . ' 12:00:00';
        $this->attributes['keywords']     = '';
        $this->attributes['track_type']   = '';

        if ($this->podcast->type == 'episodic') {
            $this->attributes['episode'] = $this->podcast->tracks()->first()?->episode + 1;
        }
//        $this->attributes['season']        = '';

        $sermon_file = $this->sermon->files()->first();

        // If we don't have a duration, get that
        if ($sermon_file && !$sermon_file->duration) {
            try {
                // Temp file name for local storage, since our MP3 library only reads local files.
                $temp_name = uniqid(true) . '.' . $sermon_file->file_extension;

                // Download and store
                Storage::disk('local')->put($temp_name, file_get_contents(str_replace(" ", '%20', $sermon_file->getUrl())));
                // Why urlencode doesn't work, I have no idea!  Resorting to a str_replace for spaces.

                $audio_info = $this->getAudioInfo(Storage::disk('local')->path($temp_name));

                // Save duration
                $sermon_file->duration            = Track::getDurationFromSeconds((int) Arr::get($audio_info, 'playtime_seconds'));
                $sermon_file->duration_in_seconds = Arr::get($audio_info, 'playtime_seconds');
                $sermon_file->file_size           = Storage::disk('local')->size($temp_name);
                $sermon_file->save();

                // Delete the file
                Storage::disk('local')->delete($temp_name);
            } catch (\Exception $e) {
                // Delete the file regardless.
                Storage::disk('local')->delete($temp_name);

                Log::error('Could not download/read MP3 file for Sermon.', [
                    'sermon_id'      => $this->sermon->id,
                    'sermon_file_id' => $sermon_file->id,
                    'message'        => $e->getMessage(),
                ]);
            }
        }

        if ($sermon_file) {
            $this->attributes['mp3_url']       = $sermon_file->getUrl();
            $this->attributes['mp3_file_name'] = $sermon_file->getFileName();
            $this->attributes['mp3_type']      = 'audio/mpeg';
            $this->attributes['mp3_file_size'] = $sermon_file->file_size;
            $this->attributes['duration']      = $sermon_file->duration;
        }

        $this->attributes['author']       = $this->sermon->speaker;
        $this->attributes['author_email'] = $this->podcast->author_email;
        $this->attributes['comments']     = false;
        $this->attributes['is_explicit']  = false;

        // If we were given attributes, override our defaults set above.
        foreach ($attributes as $key => $value) {
            $this->attributes[$key] = $value;
        }

        $this->attributes['uuid'] = uniqid();

        $this->track = Track::create($this->attributes);

        // If we don't have the MP3 file information that we need, report an error.
        if (!$this->track->duration || !$this->track->mp3_url || !$this->track->mp3_file_size) {
            $this->track->has_sync_error     = true;
            $this->track->sync_error_message = 'Unable to read the MP3 file attached to the sermon to get the size and duration of the audio. This likely means the MP3 file was corrupted by the program that created it. Please try re-uploading the MP3 file, and then re-syncing this Podcast track. If help is needed, please reach out to support!';

            $this->track->is_published = false;

            $this->track->save();

            return false;
        }

        return true;
    }

    public function fromSermon(Sermon $sermon)
    {
        $this->sermon = $sermon;

        return $this;
    }

    public function forPodast(Podcast $podcast)
    {
        $this->podcast = $podcast;

        return $this;
    }

    private function getAudioInfo($file_path)
    {
        // Initialize getID3 engine
        $getID3 = new \getID3;

        return $getID3->analyze($file_path);
    }
}
