<?php

namespace App\Podcasts;

use App\Accounts\Account;
use App\Sermons\Tag;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

class Podcast extends Model
{
    use SoftDeletes;

    protected $table = 'podcasts';

    protected $casts = [
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'deleted_at'   => 'datetime',
        'published_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'sermon_tag_id',
        'published_at',
        'title',
        'url_title',
        'subtitle',
        'summary',
        'description',
        'category',
        'subcategory',
        'category_2',
        'category_3',
        'category_4',
        'image_url',
        'image_title',
        'image_link',
        'author',
        'author_email',
        'copyright',
        'location',
        'country_of_origin',
        'timezone',
        'language',
        'episode_order',
        'rating',
        'frequency',
        'type',
        'comments',
        'is_explicit',
        'is_active',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function tracks()
    {
        return $this->hasMany(Track::class)
            ->orderBy('published_at', $this->episode_order ?: 'desc');
    }

    public function downloads()
    {
        return $this->hasMany(Download::class);
    }

    public function destinations()
    {
        return $this->hasMany(Destination::class);
    }

    public function getDestination($type)
    {
        return $this->destinations()->where('key', $type)->first();
    }

    public function sermonTag()
    {
        return $this->belongsTo(Tag::class);
    }

    public function hasActiveDestination($key)
    {
        return $this->destinations()
            ->where('key', $key)
            ->where('is_active', true)
            ->exists();
    }

    public function getPodcastUrl()
    {
        return route('podcasts.podcast.view.withAccount', [$this->account->podcasts_prefix, $this->url_title]);
//        return 'https://' . config('app.domains.podcast') . '/' . $this->url_title;
    }

    public function getPodcastFeedUrl()
    {
        return $this->getPodcastUrl() . '/feed';
    }

    // Gets the category and subcategory (if exists) from a form submit where they selected a string.
    public static function splitCategory($category_string)
    {
        $temp = explode(' :: ', $category_string);

        return [
            'category'    => Arr::get($temp, 0),
            'subcategory' => Arr::get($temp, 1),
        ];
    }

    public static $frequency = [
        ''            => 'No Set Schedule',
        'daily'       => 'Daily',
        'weekly'      => 'Weekly',
        'semiweekly'  => 'Semiweekly',
        'biweekly'    => 'Biweekly',
        'monthly'     => 'Monthly',
        'semimonthly' => 'Semimonthly',
        'bimonthly'   => 'Bimonthly',
        'complete'    => 'Complete (No more new episodes)',
    ];

    public static $timezones = [
        'America/New_York'    => 'Eastern',
        'America/Chicago'     => 'Central',
        'America/Denver'      => 'Mountain',
        'America/Phoenix'     => 'Mountain no DST',
        'America/Los_Angeles' => 'Pacific',
        'America/Anchorage'   => 'Alaska',
        'America/Adak'        => 'Hawaii',
        'Pacific/Honolulu'    => 'Hawaii no DST',
    ];

    public static $categories = [
        'Arts',
        'Arts :: Books',
        'Arts :: Design',
        'Arts :: Fashion & Beauty',
        'Arts :: Food',
        'Arts :: Performing Arts',
        'Arts :: Visual Arts',
        'Business',
        'Business :: Careers',
        'Business :: Entrepreneurship',
        'Business :: Investing',
        'Business :: Management',
        'Business :: Marketing',
        'Business :: Non-Profit',
        'Comedy',
        'Comedy :: Comedy Interviews',
        'Comedy :: Improv',
        'Comedy :: Stand-Up',
        'Education',
        'Education :: Courses',
        'Education :: How To',
        'Education :: Language Learning',
        'Education :: Self-Improvement',
        'Fiction',
        'Fiction :: Comedy Fiction',
        'Fiction :: Drama',
        'Fiction :: Science Fiction',
        'Fiction :: Government',
        'History',
        'Health & Fitness',
        'Health & Fitness :: Alternative Health',
        'Health & Fitness :: Fitness',
        'Health & Fitness :: Medicine',
        'Health & Fitness :: Mental Health',
        'Health & Fitness :: Nutrition',
        'Health & Fitness :: Sexuality',
        'Kids & Family',
        'Kids & Family :: Education for Kids',
        'Kids & Family :: Parenting',
        'Kids & Family :: Pets & Animals',
        'Kids & Family :: Stories for Kids',
        'Leisure',
        'Leisure :: Animation & Manga',
        'Leisure :: Automotive',
        'Leisure :: Aviation',
        'Leisure :: Crafts',
        'Leisure :: Games',
        'Leisure :: Hobbies',
        'Leisure :: Home & Garden',
        'Leisure :: Video Games',
        'Music',
        'Music :: Music Commentary',
        'Music :: Music History',
        'Music :: Music Interviews',
        'News',
        'News :: Business News',
        'News :: Daily News',
        'News :: Entertainment News',
        'News :: News Commentary',
        'News :: Politics',
        'News :: Sports News',
        'News :: Tech News',
        'Religion & Spirituality',
        'Religion & Spirituality :: Buddhism',
        'Religion & Spirituality :: Christianity',
        'Religion & Spirituality :: Hinduism',
        'Religion & Spirituality :: Islam',
        'Religion & Spirituality :: Judaism',
        'Religion & Spirituality :: Religion',
        'Religion & Spirituality :: Spirituality',
        'Science',
        'Science :: Astronomy',
        'Science :: Chemistry',
        'Science :: Earth Sciences',
        'Science :: Life Sciences',
        'Science :: Mathematics',
        'Science :: Natural Sciences',
        'Science :: Nature',
        'Science :: Physics',
        'Science :: Social Sciences',
        'Society & Culture',
        'Society & Culture :: Documentary',
        'Society & Culture :: Personal Journals',
        'Society & Culture :: Philosophy',
        'Society & Culture :: Places & Travel',
        'Society & Culture :: Relationships',
        'Sports',
        'Sports :: Baseball',
        'Sports :: Basketball',
        'Sports :: Cricket',
        'Sports :: Fantasy Sports',
        'Sports :: Football',
        'Sports :: Golf',
        'Sports :: Hockey',
        'Sports :: Rugby',
        'Sports :: Running',
        'Sports :: Soccer',
        'Sports :: Swimming',
        'Sports :: Tennis',
        'Sports :: Volleyball',
        'Sports :: Wilderness',
        'Sports :: Wrestling',
        'Technology',
        'True Crime',
        'TV & Film',
        'TV & Film :: After Shows',
        'TV & Film :: Film History',
        'TV & Film :: Film Interviews',
        'TV & Film :: Film Reviews',
        'TV & Film :: TV Reviews',
    ];

    public static $available_languages = [
        'en' => 'English',
        'es' => 'Spanish',
    ];

    public static $languages = [
        'ab' => 'Abkhazian',
        'aa' => 'Afar',
        'af' => 'Afrikaans',
        'ak' => 'Akan',
        'sq' => 'Albanian',
        'am' => 'Amharic',
        'ar' => 'Arabic',
        'an' => 'Aragonese',
        'hy' => 'Armenian',
        'as' => 'Assamese',
        'av' => 'Avaric',
        'ae' => 'Avestan',
        'ay' => 'Aymara',
        'az' => 'Azerbaijani',
        'bm' => 'Bambara',
        'ba' => 'Bashkir',
        'eu' => 'Basque',
        'be' => 'Belarusian',
        'bn' => 'Bengali',
        'bh' => 'Bihari languages',
        'bi' => 'Bislama',
        'bs' => 'Bosnian',
        'br' => 'Breton',
        'bg' => 'Bulgarian',
        'my' => 'Burmese',
        'ca' => 'Catalan, Valencian',
        'ch' => 'Chamorro',
        'ce' => 'Chechen',
        'ny' => 'Chichewa, Chewa, Nyanja',
        'zh' => 'Chinese',
        'cv' => 'Chuvash',
        'kw' => 'Cornish',
        'co' => 'Corsican',
        'cr' => 'Cree',
        'hr' => 'Croatian',
        'cs' => 'Czech',
        'da' => 'Danish',
        'dv' => 'Divehi, Dhivehi, Maldivian',
        'nl' => 'Dutch, Flemish',
        'dz' => 'Dzongkha',
        'en' => 'English',
        'eo' => 'Esperanto',
        'et' => 'Estonian',
        'ee' => 'Ewe',
        'fo' => 'Faroese',
        'fj' => 'Fijian',
        'fi' => 'Finnish',
        'fr' => 'French',
        'ff' => 'Fulah',
        'gl' => 'Galician',
        'ka' => 'Georgian',
        'de' => 'German',
        'el' => 'Greek, Modern (1453-)',
        'gn' => 'Guarani',
        'gu' => 'Gujarati',
        'ht' => 'Haitian, Haitian Creole',
        'ha' => 'Hausa',
        'he' => 'Hebrew',
        'hz' => 'Herero',
        'hi' => 'Hindi',
        'ho' => 'Hiri Motu',
        'hu' => 'Hungarian',
        'ia' => 'Interlingua(International Auxiliary Language Association)',
        'id' => 'Indonesian',
        'ie' => 'Interlingue, Occidental',
        'ga' => 'Irish',
        'ig' => 'Igbo',
        'ik' => 'Inupiaq',
        'io' => 'Ido',
        'is' => 'Icelandic',
        'it' => 'Italian',
        'iu' => 'Inuktitut',
        'ja' => 'Japanese',
        'jv' => 'Javanese',
        'kl' => 'Kalaallisut, Greenlandic',
        'kn' => 'Kannada',
        'kr' => 'Kanuri',
        'ks' => 'Kashmiri',
        'kk' => 'Kazakh',
        'km' => 'Central Khmer',
        'ki' => 'Kikuyu, Gikuyu',
        'rw' => 'Kinyarwanda',
        'ky' => 'Kirghiz, Kyrgyz',
        'kv' => 'Komi',
        'kg' => 'Kongo',
        'ko' => 'Korean',
        'ku' => 'Kurdish',
        'kj' => 'Kuanyama, Kwanyama',
        'la' => 'Latin',
        'lb' => 'Luxembourgish, Letzeburgesch',
        'lg' => 'Ganda',
        'li' => 'Limburgan, Limburger, Limburgish',
        'ln' => 'Lingala',
        'lo' => 'Lao',
        'lt' => 'Lithuanian',
        'lu' => 'Luba-Katanga',
        'lv' => 'Latvian',
        'gv' => 'Manx',
        'mk' => 'Macedonian',
        'mg' => 'Malagasy',
        'ms' => 'Malay',
        'ml' => 'Malayalam',
        'mt' => 'Maltese',
        'mi' => 'Maori',
        'mr' => 'Marathi',
        'mh' => 'Marshallese',
        'mn' => 'Mongolian',
        'na' => 'Nauru',
        'nv' => 'Navajo, Navaho',
        'nd' => 'North Ndebele',
        'ne' => 'Nepali',
        'ng' => 'Ndonga',
        'nb' => 'Norwegian Bokmål',
        'nn' => 'Norwegian Nynorsk',
        'no' => 'Norwegian',
        'ii' => 'Sichuan Yi, Nuosu',
        'nr' => 'South Ndebele',
        'oc' => 'Occitan',
        'oj' => 'Ojibwa',
        'cu' => 'Church Slavic, Old Slavonic, Church Slavonic, Old Bulgarian, Old Church Slavonic',
        'om' => 'Oromo',
        'or' => 'Oriya',
        'os' => 'Ossetian, Ossetic',
        'pa' => 'Panjabi, Punjabi',
        'pi' => 'Pali',
        'fa' => 'Persian',
        'pl' => 'Polish',
        'ps' => 'Pashto, Pushto',
        'pt' => 'Portuguese',
        'qu' => 'Quechua',
        'rm' => 'Romansh',
        'rn' => 'Rundi',
        'ro' => 'Romanian, Moldavian, Moldovan',
        'ru' => 'Russian',
        'sa' => 'Sanskrit',
        'sc' => 'Sardinian',
        'sd' => 'Sindhi',
        'se' => 'Northern Sami',
        'sm' => 'Samoan',
        'sg' => 'Sango',
        'sr' => 'Serbian',
        'gd' => 'Gaelic, Scottish Gaelic',
        'sn' => 'Shona',
        'si' => 'Sinhala, Sinhalese',
        'sk' => 'Slovak',
        'sl' => 'Slovenian',
        'so' => 'Somali',
        'st' => 'Southern Sotho',
        'es' => 'Spanish, Castilian',
        'su' => 'Sundanese',
        'sw' => 'Swahili',
        'ss' => 'Swati',
        'sv' => 'Swedish',
        'ta' => 'Tamil',
        'te' => 'Telugu',
        'tg' => 'Tajik',
        'th' => 'Thai',
        'ti' => 'Tigrinya',
        'bo' => 'Tibetan',
        'tk' => 'Turkmen',
        'tl' => 'Tagalog',
        'tn' => 'Tswana',
        'to' => 'Tonga (Tonga Islands)',
        'tr' => 'Turkish',
        'ts' => 'Tsonga',
        'tt' => 'Tatar',
        'tw' => 'Twi',
        'ty' => 'Tahitian',
        'ug' => 'Uighur, Uyghur',
        'uk' => 'Ukrainian',
        'ur' => 'Urdu',
        'uz' => 'Uzbek',
        've' => 'Venda',
        'vi' => 'Vietnamese',
        'vo' => 'Volapük',
        'wa' => 'Walloon',
        'cy' => 'Welsh',
        'wo' => 'Wolof',
        'fy' => 'Western Frisian',
        'xh' => 'Xhosa',
        'yi' => 'Yiddish',
        'yo' => 'Yoruba',
        'za' => 'Zhuang, Chuang',
        'zu' => 'Zulu',
    ];
}
