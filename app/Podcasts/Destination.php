<?php

namespace App\Podcasts;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Destination extends Model
{
    use SoftDeletes;

    protected $table = 'podcast_destinations';

    protected $casts = [
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'deleted_at'   => 'datetime',
        'published_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'podcast_id',
        'name',
        'url_name',
        'key',
        'direct_link',
        'status',
        'is_active',
        'is_pending',
        'is_error',
        'error_message',
        'message_to_client',
        'notes',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function podcast()
    {
        return $this->belongsTo(Podcast::class);
    }
}
