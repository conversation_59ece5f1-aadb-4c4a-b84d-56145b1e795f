<?php

namespace App\Podcasts;

use App\Accounts\Account;
use App\Sermons\Sermon;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Track extends Model
{
    use SoftDeletes;

    protected $table = 'podcast_tracks';

    protected $casts = [
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
        'deleted_at'     => 'datetime',
        'published_at'   => 'datetime',
        'is_published'   => 'boolean',
        'is_explicit'    => 'boolean',
        'has_sync_error' => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'podcast_id',
        'sermon_id',
        'published_at',
        'title',
        'url_title',
        'uuid',
        'summary',
        'description',
        'keywords',
        'track_type',
        'mp3_url',
        'mp3_type',
        'mp3_file_size',
        'mp3_file_name',
        'duration',
        'duration_in_seconds',
        'author',
        'author_email',
        'comments',
        'is_published',
        'is_explicit',
        'has_sync_error',
        'sync_error_message',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function downloads()
    {
        return $this->hasMany(Download::class, 'podcast_track_id', 'id');
    }

    public function podcast()
    {
        return $this->belongsTo(Podcast::class);
    }

    public function sermon()
    {
        return $this->belongsTo(Sermon::class);
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function getMP3TrackingUrl()
    {
        return 'https://' . config('app.domains.podcast') . '/mp3/' . $this->id . '/' . $this->uuid . '/' . $this->mp3_file_name;
    }

    public function getPodcastTrackUrl()
    {
        return route('podcasts.podcast.viewTrack', [$this->account->podcasts_prefix, $this->podcast, $this->url_title]);
//        return 'https://' . config('app.domains.podcast') . '/' . $this->podcast->url_title . '/' . $this->url_title;
    }

    public function getMP3Url()
    {
        return $this->mp3_url;
    }

    public function getDurationFormatted()
    {
        $segments = explode(':', $this->duration);

        if (count($segments) == 0) {
            return null;
        }

        if (count($segments) == 2) {
            return $segments[0] . Str::plural('minute', $segments[0]);
        }

        if (count($segments) == 3) {
            return ($segments[0] > 0 ? (floor($segments[0]) . ' ' . Str::plural('hour', $segments[0])) . ' ' : null) . floor($segments[1]) . ' ' . Str::plural('minute', $segments[1]);
        }
    }

    public static function getDurationFromSeconds($seconds)
    {
        $hours   = 0;
        $minutes = floor($seconds / 60);
        $seconds = floor($seconds % 60);

        if ($minutes > 59) {
            $hours   = floor($minutes / 60);
            $minutes = $minutes - ($hours * 60);
        }

        return str_pad($hours, 2, '0', STR_PAD_LEFT) . ':' . str_pad($minutes, 2, '0', STR_PAD_LEFT) . ':' . str_pad($seconds, 2, '0', STR_PAD_LEFT);
    }
}
