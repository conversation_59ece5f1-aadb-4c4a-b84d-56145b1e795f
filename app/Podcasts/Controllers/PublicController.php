<?php

namespace App\Podcasts\Controllers;

use App\Accounts\Account;
use App\Base\Http\Controllers\Controller;
use App\Podcasts\Download;
use App\Podcasts\Podcast;
use App\Podcasts\Services\CreatePodcastXml;
use App\Podcasts\Services\RecordTrackDownload;
use App\Podcasts\Track;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Log;
use ipinfo\ipinfo\IPinfo;
use ipinfo\ipinfo\IPinfoException;

class PublicController extends Controller
{
    public function home()
    {
        return redirect()->away('https://' . config('app.domains.frontend'));
    }

    public function view(Podcast $podcast)
    {
        Paginator::useTailwind();

        if (!$podcast->is_active) {
            abort(404);
        }

        return view('podcasts.view')->with([
            'podcast' => $podcast,
            'tracks'  => $podcast->tracks()->published()->simplePaginate(15),
        ]);
    }

    public function viewWithAccount($account_prefix, $podcast_url_title)
    {
        $account = Account::where('podcasts_prefix', $account_prefix)->first();
        $podcast = Podcast::where('account_id', $account?->id)
            ->where('url_title', $podcast_url_title)
            ->first();

        Paginator::useTailwind();

        if (!$account || !$podcast || !$podcast->is_active) {
            abort(404);
        }

        return view('podcasts.view')->with([
            'podcast' => $podcast,
            'tracks'  => $podcast->tracks()->published()->simplePaginate(15),
        ]);
    }

    public function viewTrack($account_prefix, Podcast $podcast, Track $track)
    {
        $account = Account::where('podcasts_prefix', $account_prefix)->first();

        if (!$account || !$podcast || !$podcast->is_active || !$track->is_published) {
            abort(404);
        }

        return view('podcasts.view-track')->with([
            'podcast' => $podcast,
            'track'   => $track,
        ]);
    }

    public function viewTrackWithPodcastTitle($account_prefix, $podcast_url_title, Track $track)
    {
        $account = Account::where('podcasts_prefix', $account_prefix)->first();
        $podcast = Podcast::where('account_id', $account?->id)
            ->where('url_title', $podcast_url_title)
            ->first();

        if (!$account || !$podcast || !$podcast->is_active || !$track->is_published) {
            abort(404);
        }

        return view('podcasts.view-track')->with([
            'podcast' => $podcast,
            'track'   => $track,
        ]);
    }

    public function rss(Podcast $podcast)
    {
        if (!$podcast->is_active) {
            abort(404);
        }

        return response((new CreatePodcastXml())
            ->forPodcast($podcast)->getXml())
            ->withHeaders([
                'Content-Type' => 'text/xml',
            ]);
    }

    public function rssWithAccount($account_prefix, $podcast_url_title)
    {
        $account = Account::where('podcasts_prefix', $account_prefix)->first();
        $podcast = Podcast::where('account_id', $account?->id)
            ->where('url_title', $podcast_url_title)
            ->first();

        if (!$account || !$podcast || !$podcast->is_active || $account->id != $podcast->account_id) {
            abort(404);
        }

        return response((new CreatePodcastXml())
            ->forPodcast($podcast)->getXml())
            ->withHeaders([
                'Content-Type' => 'text/xml',
            ]);
    }

    public function download(Track $track, $uuid, $track_name)
    {
        if ($track->uuid !== $uuid || !$track->podcast->is_active || !$track->is_published) {
            abort(404);
        }

        // Record our data after we return a response.
        dispatch(function () use ($track) {

            // We can lookup recent IP addresses from other downloads and get the info that way, instead of wasting an API call to IPInfo.
            // Only lookup 2 weeks ago or sooner, so we'll refresh data if it's been a while.
            $existing_track = Download::where('ip_address', request()->ip())
                ->where('created_at', '>', now()->subWeeks(2))
                ->first();

            $details = null;

            // If we found no lookup.
            if (!$existing_track) {
                try {
                    $client  = new IPinfo(config('services.ipinfo.token'));
                    $details = $client->getDetails(request()->ip());
                } catch (IPinfoException $e) {
                    Log::error($e);
                }
            }

            $ip_info    = $existing_track ? $existing_track->ip_info : ($details?->all ? json_encode($details?->all) : null);
            $ip_address = $existing_track ? $existing_track->ip_address : ($details?->ip ?? null);
            $region     = $existing_track ? $existing_track->region : ($details?->region ?? null);
            $country    = $existing_track ? $existing_track->country : ($details?->country ?? null);

            (new RecordTrackDownload())
                ->forTrack($track)
                ->create([
                    'ip_info'    => $ip_info,
                    'ip_address' => $ip_address,
                    'region'     => $region,
                    'country'    => $country,
                ]);

        })->afterResponse();

        return redirect(
            $track->getMP3Url()
        );
    }
}
