<?php

namespace App\Podcasts\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Jobs\Podcasts\SyncPodcastSermons;
use App\Podcasts\Podcast;
use App\Podcasts\Services\CreatePodcast;
use App\Podcasts\Services\SyncTrackToSermon;
use App\Podcasts\Services\UpdatePodcast;
use App\Podcasts\Track;
use App\Sermons\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PodcastController extends Controller
{
    public function index()
    {
        return view('admin.podcasts.index')->with([
            'podcasts' => Podcast::visibleTo(Auth::user())
                ->orderBy('published_at', 'DESC')
                ->paginate(15),
        ]);
    }

    public function search()
    {
        return view('admin.podcasts.index')->with([
            'podcasts' => Podcast::visibleTo(Auth::user())
                ->where('title', 'like', '%' . request('title') . '%')
                ->orderBy('published_at', 'DESC')
                ->paginate(15),
        ]);
    }

    public function view(Podcast $podcast)
    {
        return view('admin.podcasts.view')->with([
            'podcast' => $podcast,
        ]);
    }

    public function create()
    {
        return view('admin.podcasts.create')
            ->with('tags', Tag::visibleTo(auth()->user())->get());
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cover_image' => 'required',
            'title'       => 'required',

        ]);

        if ($validator->fails()) {
            return redirect('podcasts/create')
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $podcast = DB::transaction(function () {
                return (new CreatePodcast())
                    ->withCoverImage(request()->file('cover_image', 'title'))
                    ->create(
                        array_merge(
                            request()->all(),
                            [
                                'account_id'   => Auth::user()->account->id,
                                'is_explicit'  => false,
                                'comments'     => false,
                                'url_title'    => Str::slug(request()->get('title')),
                                'published_at' => request()->get('published_at') . ' 12:00:00',
                            ]
                        )
                    );
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        // Start the process for importing this Podcast from Sermons.
        SyncPodcastSermons::dispatch()
            ->delay(now()->addSeconds(3));

        return redirect(route('admin.podcasts.index'))
            ->with('message.success', 'New podcast created! If sermons exist already for this podcast, it will take a short time to sync sermons to the podcast. Please be patient.');
    }

    public function edit(Podcast $podcast)
    {
        return view('admin.podcasts.edit')
            ->with('podcast', $podcast)
            ->with('tags', Tag::visibleTo(auth()->user())->get());
    }

    public function save(Podcast $podcast)
    {
        try {
            DB::transaction(function () use ($podcast) {
                return (new UpdatePodcast($podcast))
                    ->withCoverImage(request()->file('cover_image'))
                    ->update(
                        array_merge(
                            request()->all(),
                            [
                                'url_title'    => Str::slug(request()->get('title')),
                                'published_at' => request()->get('published_at') . ' 12:00:00',
                            ]
                        )
                    );
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        // Start the process for importing this Podcast from Sermons.
        SyncPodcastSermons::dispatch()
            ->delay(now()->addSeconds(3));

        return redirect()
            ->route('admin.podcasts.index')
            ->with('message.success', 'Saved successfully. If you changed the sermon tag for this podcast, it will take a short time to sync the sermons to the podcast. Please be patient.');
    }

    public function resync(Podcast $podcast, Track $track)
    {
        // Start the process for importing this Podcast from Sermons.
        $result = (new SyncTrackToSermon())
            ->fromSermon($track->sermon)
            ->forTrack($track)
            ->sync();

        if ($result) {
            return redirect()
                ->route('admin.podcasts.view', $podcast)
                ->with('message.success', 'Sync successful. Podcast platforms will take a little while to receive any updates.');
        } else {
            return back()->with('message.failure', 'Re-sync failed.');
        }
    }
}
