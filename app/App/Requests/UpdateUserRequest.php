<?php

namespace App\App\Requests;

use App\Base\Http\Request;
use App\Users\User;

class UpdateUserRequest extends Request
{
    public function rules()
    {
        $rules = [
            'first_name'       => 'sometimes|required|string|max:48',
            'last_name'        => 'sometimes|required|string|max:48',
            'user_name'        => 'nullable|sometimes|string|max:48',
            'gender'           => 'required|in:' . implode(',', array_keys(User::$genders)),
            'notes'            => 'nullable|string|max:2046',
            'birthdate'        => 'nullable|sometimes|date',
            'marital_status'   => 'required|in:' . implode(',', array_keys(User::$marital_statuses)),
            'password'         => 'sometimes',
            // 'family_role'              => 'sometimes|in:' . implode(',', array_keys(User::$family_roles)),
            // 'church_office'            => 'nullable',
            'blood_type'       => 'nullable|sometimes|in:' . implode(',', array_keys(User::$blood_types)),
            'school_attending' => 'nullable',
            'user_grade_id'    => 'nullable',
            'employer'         => 'nullable',
            'job_title'        => 'nullable',
            'job_keywords'     => 'nullable',
        ];

        return $rules;
    }
}
