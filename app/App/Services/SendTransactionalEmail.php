<?php

namespace App\App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Arr;

class SendTransactionalEmail
{
    public $to;
    public $type;
    public $attachments = [];
    public $merge_data;

    private $zepto_templates = [
        'podcast_created'                        => '2d6f.5e413d9411258701.k1.5a0a5420-a8d9-11ee-9568-5254004d4100.18cc66ec262',
        'account_payment_receipt'                => '2d6f.5e413d9411258701.k1.5d28df60-ca9d-11ee-b1b0-5254004d4100.18da3b82856',
        'website.forms.contact'                  => 'lightpost-website-forms-contact',
        'website.forms.prayer_request'           => 'lightpost-website-forms-prayer-request',
        'website.forms.bible_class_registration' => 'lightpost-website-forms-bible-class-registration',
    ];
    private $zeptop_subjects = [
        'podcast_created'                        => 'New podcast created!',
        'account_payment_receipt'                => 'Lightpost - Payment Receipt',
        'website.forms.contact'                  => 'Lightpost Website - New Contact! 🎉',
        'website.forms.prayer_request'           => 'Lightpost Website - New Prayer Request! 🙏🏼',
        'website.forms.bible_class_registration' => 'Lightpost Website - New Bible Class Registration! 📚',
    ];
    private $from_emails     = [
        'podcast_created'                        => '<EMAIL>',
        'account_payment_receipt'                => '<EMAIL>',
        'website.forms.contact'                  => '<EMAIL>',
        'website.forms.prayer_request'           => '<EMAIL>',
        'website.forms.bible_class_registration' => '<EMAIL>',
    ];

    public function send()
    {
        $this->sendViaZepto();
    }

    public function to($to)
    {
        $this->to = $to;

        return $this;
    }

    public function type($type)
    {
        $this->type = $type;

        return $this;
    }

    public function withAttachment($file_name, $mime_type, $base64_content)
    {
        $this->attachments[] = [
            'name'      => $file_name,
            'mime_type' => $mime_type,
            'content'   => $base64_content,
        ];

        return $this;
    }

    public function mergeData($merge_data)
    {
        $this->merge_data = $merge_data;

        return $this;
    }

    private function sendViaZepto()
    {
        $client = new Client([
            'base_uri' => 'https://api.zeptomail.com',
        ]);

        $content = [
            'headers' => [
                'Accept'        => 'application/json',
                'Content-Type'  => 'application/json',
                'Authorization' => 'Zoho-enczapikey ' . config('services.zoho.zepto.api_key'),
            ],
            'json'    => [
                'from'         => [
                    'address' => Arr::get($this->from_emails, $this->type) ?: '<EMAIL>',
                    'name'    => 'Lightpost',
                ],
                'to'           => [
                    [
                        'email_address' => [
                            'address' => $this->to,
                            'name'    => '',
                        ],
                    ],
                ],
                'subject'      => $this->zeptop_subjects[$this->type],
                'template_key' => $this->zepto_templates[$this->type],
                'merge_info'   => $this->merge_data,
            ],
        ];

        if ($this->attachments) {
            $content['json']['attachments'] = $this->attachments;
        }

        $response = $client->request('POST', '/v1.1/email/template', $content);

        return $response;
    }
}
