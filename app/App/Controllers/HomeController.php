<?php

namespace App\App\Controllers;

use App\Accounts\VerseOfTheDay;
use App\Attendance\Services\AttendanceService;
use App\Attendance\Services\CreateAttendanceRecordService;
use App\Attendance\Services\DeleteAttendanceRecordService;
use App\Base\Http\Controllers\Controller;
use App\Users\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class HomeController extends Controller
{
    public function index()
    {
        return view('app.home.index')
            ->with('currentBCGroup', auth()->user()->account->currentBibleClassGroup())
            ->with('votd', (new VerseOfTheDay())->getVerse(null, auth()->user()))
            ->with('checkin_attendance_types_now', (new AttendanceService())->forUser(auth()->user())->getCheckinAttendanceTypesForNow());
    }

    public function familyAttendanceCheckin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'users'                 => 'nullable|array',
            'attendance_types'      => 'nullable|array',
            'attendance_selections' => 'nullable|array',
        ], [
            'users'            => 'At least one family member must be selected.',
            'attendance_types' => 'At least one type of attendance must be selected.',
        ], [
            'email' => 'email address',
        ]);

        $validator->stopOnFirstFailure()->validate();

        $all_family_members_ids = auth()->user()->allFamilyMembers()->pluck('id')->toArray();

        // Get all possible attendance types right now.
        foreach ((new AttendanceService(auth()->user()))->getCheckinAttendanceTypesForNow() as $attendance_type):
            // Go through each family member for this attendance type.
            foreach ($all_family_members_ids as $user_id):

                // If we've selected an attendance type,
                // AND this current type is in the selection,
                // AND this use is in this selection for this type
                // THEN add the attendance record.
                if (is_array(request('attendance_selections'))
                    && array_key_exists($attendance_type->id, request('attendance_selections'))
                    && in_array($user_id, request('attendance_selections')[$attendance_type->id])
                ) {
                    $user = User::where('family_id', auth()->user()->family_id)
                        ->find($user_id);

                    if ($user) {
                        (new CreateAttendanceRecordService())
                            ->forUser($user)
                            ->forAttendanceTypes([$attendance_type->id])
                            ->forNow()
                            ->create();
                    }
                } else {
                    $user = User::where('family_id', auth()->user()->family_id)
                        ->find($user_id);

                    if ($user) {
                        (new DeleteAttendanceRecordService())
                            ->forUser($user)
                            ->forAttendanceTypes([$attendance_type->id])
                            ->forDate(now()->setTimezone(auth()->user()->account->timezone))
                            ->delete();
                    }
                }
            endforeach;
        endforeach;

        return back()
            ->with('message.success', 'Attendance updated!');
    }
}
