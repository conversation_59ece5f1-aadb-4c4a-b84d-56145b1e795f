<?php

namespace App\App\Controllers;

use App\App\Requests\UpdateUserRequest;
use App\Base\Http\Controllers\Controller;
use App\Users\Services\UpdateUser;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AccountController extends Controller
{
    public function edit()
    {
        return view('app.account.edit')->withUser(Auth::user());
    }

    public function editExtended()
    {
        return view('app.account.edit-extended')->withUser(Auth::user());
    }

    public function editEmails()
    {
        return view('app.account.edit-emails')->withUser(Auth::user());
    }

    public function editAddresses()
    {
        return view('app.account.edit-addresses')->withUser(Auth::user());
    }

    public function editPhones()
    {
        return view('app.account.edit-phones')->withUser(Auth::user());
    }

    public function save(UpdateUserRequest $request)
    {
        $user = Auth::user();

        $validator = Validator::make(request()->all(), [
            'first_name'           => 'required|string|max:128',
            'preferred_first_name' => 'nullable|string|max:128',
            'last_name'            => 'nullable|string|max:128',
            'birthdate'            => 'nullable|string',
            'marital_status'       => 'nullable|string',
            'date_married'         => 'nullable|string',
            'maiden_name'          => 'nullable|string',
            'gender'               => 'nullable|string',
            'password'             => 'nullable|string|max:256',
            'blood_type'           => 'nullable|string',
            'school_attending'     => 'nullable|string',
            'user_grade_id'        => 'nullable|integer',
            'employer'             => 'nullable|string',
            'job_title'            => 'nullable|string',
            'job_keywords'         => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->with('message.failure', 'There was an error saving your account data.  Please check the input fields and try again.');
        }

        try {
            // @TODO: Make sure we're only ever passing fields allowed, the update service will accept all request fields.
            DB::transaction(function () use ($request, $user) {
                return (new UpdateUser($user))->update($request->only([
                    'first_name',
                    'preferred_first_name',
                    'last_name',
                    'birthdate',
                    'marital_status',
                    'date_married',
                    'maiden_name',
                    'gender',
                    'password',
                    'blood_type',
                    'school_attending',
                    'user_grade_id',
                    'employer',
                    'job_title',
                    'job_keywords',
                ]));
            });
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with('message.failure', 'There was an error saving your account data.  Please check the input fields and try again.');
        }

        return back()->with('message.success', 'Saved successfully.');
    }
}
