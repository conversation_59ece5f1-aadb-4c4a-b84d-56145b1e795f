<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Prayers\Prayer;
use App\Prayers\PrayerFolder;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Auth;

class PrayerListController extends Controller
{
    public function index()
    {
        Paginator::useTailwind();

        return view('app.prayers.index')->with([
            'folders' => PrayerFolder::visibleTo(auth()->user())
                ->IsNotHidden()
                ->orderBy('sort_id')
                ->get(),
            'prayers' => Prayer::visibleTo(auth()->user())
                ->when(request()->has('search'), function ($query) {
                    $query->where(function ($query) {
                        $query->where('title', 'like', '%' . request('search') . '%')
                            ->orWhere('details', 'like', '%' . request('search') . '%');
                    });
                })
                ->withCount('updates')
                ->isViewableToMembers()
                ->NoFolder()
                ->orderBy('last_updated_at', 'DESC')
                ->paginate(50),
        ]);
    }

    public function folder(PrayerFolder $folder)
    {
        Paginator::useTailwind();

        return view('app.prayers.folder')->with([
            'folder'  => $folder,
            'prayers' => Prayer::visibleTo(auth()->user())
                ->when(request()->has('search'), function ($query) {
                    $query->where(function ($query) {
                        $query->where('title', 'like', '%' . request('search') . '%')
                            ->orWhere('details', 'like', '%' . request('search') . '%');
                    });
                })
                ->withCount('updates')
                ->InFolder($folder)
                ->isNotExpired(auth()->user()->account?->timezone)
                ->isNotHidden()
                ->orderBy('last_updated_at', 'DESC')
                ->paginate(50),
        ]);
    }

    public function viewPrayer(Prayer $prayer)
    {
        $prayer = Prayer::visibleTo(auth()->user())
            ->isNotExpired()
            ->isNotHidden()
            ->find($prayer->id);

        if (!$prayer) {
            abort(404);
        }

        return view('app.prayers.view')->with([
            'prayer' => $prayer,
            'user'   => Auth::user(),
        ]);
    }

    public function request()
    {
        return view('app.prayers.request');
    }

    public function requestSubmit()
    {
        if (!request()->get('details')) {
            return back()->with('message.failure', 'A title and details are required.');
        }

        if (!auth()->user()->account->hasFeatureForMember('account.setting.prayers.allow_requests')) {
            return back()->with('message.failure', 'Prayer request submissions are not enabled for your account.');
        }

        $prayer                  = new Prayer();
        $prayer->account_id      = request()->user()->account_id;
        $prayer->created_by      = request()->user()->id;
        $prayer->last_updated_at = now();

        $prayer->title   = request()->input('title');
        $prayer->details = request()->input('details');

        if (auth()->user()->account->hasFeatureForMember('account.setting.prayers.auto_approve_requests')) {
            $prayer->is_active    = true;
            $prayer->is_approved  = true;
            $prayer->is_a_request = false;
        } else {
            $prayer->is_active    = true;
            $prayer->is_approved  = false;
            $prayer->is_a_request = true;
        }

        $prayer->save();

        if (auth()->user()->account->hasFeatureForMember('account.setting.prayers.auto_approve_requests')) {
            $message = 'Success! Your prayer request has added.';
        } else {
            $message = 'Success! Your prayer request has been sent. Please be patient while it is reviewed.';
        }

        return redirect(route('app.prayers.index'))
            ->with('message.success', $message);
    }
}
