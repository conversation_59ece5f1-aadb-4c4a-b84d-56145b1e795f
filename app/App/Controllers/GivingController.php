<?php

namespace App\App\Controllers;

use App\Accounts\FinanceBucket;
use App\Base\Http\Controllers\Controller;
use App\Finance\Transaction;
use App\Users\PaymentMethod;
use App\Users\PaymentSchedule;
use App\Users\Services\ChargePaymentMethod;
use App\Users\Services\CreatePaymentMethod;
use App\Users\Services\CreatePaymentSchedule;
use App\Users\Services\DeletePaymentMethod;
use App\Users\Services\UpdatePaymentMethod;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Money\Currencies\ISOCurrencies;
use Money\Parser\DecimalMoneyParser;
use Stripe\Customer;
use Stripe\Stripe;

class GivingController extends Controller
{
    public function index()
    {
        $payment_methods      = auth()->user()->paymentMethods;
        $contribution_history = auth()->user()->contributions()->with('bucket')->orderBy('created_at', 'DESC')->get();
        $payment_schedules    = auth()->user()->paymentSchedules;

        return view('app.giving.index')
            ->with('user', auth()->user())
            ->with('payment_methods', $payment_methods)
            ->with('contribution_history', $contribution_history)
            ->with('payment_schedules', $payment_schedules);
    }

    public function createPaymentSchedule()
    {
        return view('app.giving.create-payment-schedule')
            ->with(
                'account_finance_buckets',
                FinanceBucket::visibleTo(auth()->user())
                    ->notHidden()
                    ->isContribution()
                    ->get()
            );
    }

    public function submitCreatePaymentSchedule()
    {
        $validator = Validator::make(request()->all(), [
            'account_finance_bucket_id' => 'required|integer|exists:account_finance_buckets,id', // Consider adding a scope check if needed
            'user_payment_method_id'    => [
                'required',
                'integer',
                // Ensure the payment method exists and belongs to the authenticated user
                Rule::exists('user_payment_methods', 'id'),
            ],
            'amount'                    => 'required|numeric|gt:0', // Ensure amount is greater than 0
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput()
                ->with('message.failure', 'Oops! Please check your input and try again.');
        }

        $validated = $validator->validated();

        $finance_bucket = auth()->user()->account->financeBuckets->find($validated['account_finance_bucket_id']);
        $payment_method = auth()->user()->paymentMethods->find($validated['user_payment_method_id']);
        // Use validated amount directly if no further parsing is needed, or parse the validated string
        $amount = (new DecimalMoneyParser((new ISOCurrencies())))->parse($validated['amount'], 'USD')->getAmount();

        // Optional: Double-check ownership/visibility although validator rules handle existence/basic ownership
        if (!$finance_bucket || !$payment_method || $finance_bucket->account_id !== auth()->user()->account_id) {
            Log::warning('GivingController::submitCreatePaymentSchedule -- Authorization/existence check failed post-validation.', [
                'user_id'                   => auth()->id(),
                'account_finance_bucket_id' => $validated['account_finance_bucket_id'],
                'user_payment_method_id'    => $validated['user_payment_method_id'],
            ]);

            return back()
                ->with('message.failure', 'Oops! Invalid fund or payment method selected.');
        }

        if (auth()->user()->paymentSchedules->count() >= 3) {
            return back()
                ->with('message.failure', 'Oops! You can only have a maximum of <strong>three</strong> recurring payment schedules at a time.');
        }

        try {
            (new CreatePaymentSchedule())
                ->forUser(auth()->user())
                ->setFinanceBucket($finance_bucket)
                ->withPaymentMethod($payment_method)
                ->setAmountInCents($amount)
                ->setInterval('WEEKLY')
                ->setDayOfWeek(7)
                ->create();
        } catch (\Exception $e) {
            Log::error('GivingController::submitCreatePaymentSchedule -- Could not create recurring contribution.', [
                'message' => $e->getMessage(),
            ]);

            return back()
                ->with('message.failure', 'Oops! Something went wrong: ' . $e->getMessage());
        }

        return redirect(route('app.giving.index'))
            ->with('message.success', 'Success! Recurring contribution has been scheduled.');
    }

    public function addPaymentMethod()
    {
        $user = auth()->user();

        \Stripe\Stripe::setApiKey(config('services.stripe.connect.secret'));

        // If we are not in Stripe yet, create that customer.
        if (!$user->stripe_customer_id) {
            try {
                $customer = Customer::create([
                    'email'       => optional($user->getBestEmail())->email,
                    'name'        => $user->name,
                    'phone'       => optional($user->getBestPhone())->number,
                    'description' => 'User created through Lightpost.',
                ], [
                    'stripe_account' => auth()->user()->account->stripe_account_id,
                ]);

                $user->stripe_customer_id = $customer->id;
                $user->stripe_account_id  = auth()->user()->account->stripe_account_id;

                $user->save();
            } catch (\Exception $e) {
                Log::error('GivingController::addPaymentMethod -- Could not open the add payment method page.', [
                    'message' => $e->getMessage(),
                ]);

                return redirect(route('app.giving.index'))
                    ->with('message.failure', 'Oops! Something went wrong. ' . $e->getMessage());
            }
        }

        // Setup our intent to add a card or bank account.
        $intent = \Stripe\SetupIntent::create([
            'customer'                  => $user->stripe_customer_id,
            'automatic_payment_methods' => [
                'enabled' => true,
            ],
            'usage'                     => 'off_session',
        ], [
            'stripe_account' => auth()->user()->account->stripe_account_id,
        ]);

        return view('app.giving.add-payment-method')
            ->with('intent', $intent);
    }

    public function submitAddPaymentMethod()
    {
        $account = auth()->user()->account;
        $user    = auth()->user();

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        if (!auth()->user()->can('addPaymentMethods', Transaction::class)) {
            return back()->with('message.failure', 'Account is missing API key or permission to add payment methods.');
        }

        try {
            $source = null;
            // Create our source in Stripe.
            $source = Customer::createSource($user->stripe_customer_id, [
                'source' => request('stripeToken'),
            ], [
                'stripe_account' => $account->stripe_account_id,
            ]);

            if ($source) {
                (new CreatePaymentMethod())->forUser($user)
                    ->setStripePaymentMethodId($source->id)
                    ->setStripeObject($source)
                    ->setStripeType($source->object)
                    ->setType($source->object)
                    ->setLast4($source->last4)
                    ->shareWithSpouse(request()->get('share_with_spouse') ? true : false)
                    ->setExpireDate($source->exp_month, $source->exp_year)
                    ->create();
            }
        } catch (\Exception $e) {
            Log::error('GivingController::submitAddPaymentMethod -- Could not create payment method.', [
                'message'      => $e->getMessage(),
                'error_object' => $e,
            ]);

            // Attempt to delete source.
            try {
                Customer::deleteSource($user->stripe_customer_id, optional($source)->id, [], [
                    'stripe_account' => auth()->user()->account->stripe_account_id,
                ]);
            } catch (\Exception $e2) {
                Log::error('submitAddPaymentMethod -- Could not delete Stripe source after an error occurred.', [
                    'message' => $e2->getMessage(),
                ]);
            }

            return redirect(route('app.giving.index'))
                ->with('message.failure', 'Oops! Something went wrong. ' . $e->getMessage());
        }

        return redirect(route('app.giving.index'))
            ->with('message.success', 'Success! Payment information has been updated.');
    }

    public function updatePaymentMethod(PaymentMethod $payment_method)
    {
        try {
            $payment_method = (new UpdatePaymentMethod($payment_method))
                ->shareWithSpouse((bool)request()->get('share_with_spouse'))
                ->update();
        } catch (\Exception $e) {
            Log::error('GivingController::updatePaymentMethod -- Could not update payment method.', [
                'message' => $e->getMessage(),
            ]);

            return redirect(route('app.giving.index'))
                ->with('message.failure', 'Oops! Something went wrong. ' . $e->getMessage());
        }

        return redirect(route('app.giving.index'))
            ->with('message.success', 'Success! Payment method has been updated.');
    }

    public function submitDeletePaymentMethod($payment_method)
    {
        $user = auth()->user();

        if ($user->account_id !== $payment_method->account_id) {
            abort(404);
        }

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            (new DeletePaymentMethod($payment_method))->delete();
        } catch (\Exception $e) {
            Log::error('GivingController::submitDeletePaymentMethod -- Could not delete payment method.', [
                'message' => $e->getMessage(),
            ]);

            return redirect(route('app.giving.index'))
                ->with('message.failure', 'Oops! Something went wrong. ' . $e->getMessage());
        }

        return redirect(route('app.giving.index'))
            ->with('message.success', 'Payment method has been deleted.');
    }

    public function submitDeletePaymentSchedule(PaymentSchedule $payment_schedule)
    {
        $user = auth()->user();

        if (
            ($user->account_id !== $payment_schedule->account_id)
            ||
            ($user->id !== $payment_schedule->user_id)
        ) {
            abort(404);
        }

        try {
            $payment_schedule->delete();
        } catch (\Exception $e) {
            Log::error('GivingController::submitDeletePaymentSchedule -- Could not delete payment schedule.', [
                'message' => $e->getMessage(),
            ]);

            return back()
                ->with('message.failure', 'Oops! Something went wrong. ' . $e->getMessage());
        }

        return back()
            ->with('message.success', 'Recurring payment schedule has been deleted.');
    }

    public function verifyPaymentMethod(PaymentMethod $payment_method)
    {
        $account = auth()->user()->account;
        $user    = auth()->user();

        if ($user->account_id !== $payment_method->account_id) {
            abort(404);
        }

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            $bank_account = Customer::retrieveSource(
                $payment_method->user->stripe_customer_id,
                $payment_method->stripe_payment_method_id,
                [],
                [
                    'stripe_account' => $account->stripe_account_id,
                ]
            );

            $bank_account->verify(['amounts' => [request()->input('deposit_1'), request()->input('deposit_2')]]);

            $bank_account = Customer::retrieveSource(
                $payment_method->user->stripe_customer_id,
                $payment_method->stripe_payment_method_id,
                [],
                [
                    'stripe_account' => $account->stripe_account_id,
                ]
            );

            $payment_method->stripe_object = json_encode($bank_account);
            $payment_method->save();
        } catch (\Exception $e) {
            Log::error('GivingController::verifyPaymentMethod - Could not verify payment method.', [
                'payment_method_id' => $payment_method->id,
                'user_id'           => $user->id,
                'error'             => $e->getMessage(),
                'e'                 => $e,
            ]);

            return redirect(route('app.giving.index'))
                ->with('message.failure', 'Oops! Something went wrong. ' . $e->getMessage());
        }

        return redirect(route('app.giving.index'))
            ->with('message.success', 'Success!  Bank account verified.');
    }

    public function give($payment_method)
    {
        return view('app.giving.give')
            ->with('user', auth()->user())
            ->with(
                'account_finance_buckets',
                FinanceBucket::visibleTo(auth()->user())
                    ->notHidden()
                    ->isContribution()
                    ->orderBy('sort_id', 'asc')
                    ->get()
            )
            ->with('payment_method', $payment_method);
    }

    public function submitGive($payment_method)
    {
        $user = auth()->user();

        if ($user->account_id !== $payment_method->account_id) {
            abort(404);
        }

        $validator = Validator::make(request()->all(), [
            'amount'                    => 'required|numeric',
            'account_finance_bucket_id' => 'required|integer',
            'user_payment_method_id'    => 'required|integer',
        ]);

        if ($validator->fails()) {
            return back()
                ->with('message.failure', 'Oops! Something went wrong. Please check the amount you input and try again.')
                ->withInput();
        }

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            // Amount in cents.
            $amount         = (new DecimalMoneyParser((new ISOCurrencies())))->parse(request('amount'), 'USD')->getAmount();
            $bucket         = FinanceBucket::find(request('account_finance_bucket_id'));
            $payment_method = $user->paymentMethods->find(request('user_payment_method_id'));

            Log::info('Give::give() -- Attempt', [
                'user_id' => $user->id,
            ]);

            $payment = (new ChargePaymentMethod())
                ->setFinanceBucket($bucket)
                ->setPaymentMethod($payment_method)
                ->setAmountInCents($amount)
                ->charge();
        } catch (\Exception $e) {
            Log::error('Give::give() -- Could not charge payment method.', [
                'message' => $e->getMessage(),
                'error'   => $e,
            ]);

            return back()
                ->with('message.failure', 'Oops! Something went wrong. ' . $e->getMessage());
        }

        return redirect(route('app.giving.index'))
            ->with('message.success', 'Success!  Contribution successfully initiated.');
    }
}
