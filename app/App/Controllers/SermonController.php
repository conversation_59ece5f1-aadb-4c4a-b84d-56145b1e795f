<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Sermons\Sermon;
use App\Users\User;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Sabre\VObject\Component\VCard;

class SermonController extends Controller
{
    public function index()
    {
        Paginator::useTailwind();

        return view('app.sermons.index')->with([
            'sermons' => Sermon::visibleTo(auth()->user())
                ->when(request()->has('query'), function ($query) {
                    $query->where(function ($query) {
                        $query->where('title', 'like', '%' . request('query') . '%')
                            ->orWhere('speaker', 'like', '%' . request('query') . '%');
                    });
                })
                ->withCount('files')
                ->orderBy('date_sermon', 'DESC')
                ->orderBy('created_at', 'DESC')
                ->paginate(15),
        ]);
    }

    public function search()
    {
        Paginator::useTailwind();

        // We're searching ALL family members, not just heads of households.
        // So we get a result of any member that matches the search, then use that result below to pick the heads of
        // households that have members that match the search.
        $users = User::visibleTo(auth()->user())
            ->select([
                'id',
                'account_id',
                'preferred_first_name',
                'first_name',
                'last_name',
                'family_id',
                'family_role',
            ])
            ->whereHas('familyMembers.emails', function ($query) {
                $query->where('email', 'like', request('value') . '%');
            })
            ->when(preg_replace('/[^0-9]/', '', request('value')), function ($query) {
                $query->orWhereHas('familyMembers.phones', function ($query2) {
                    $query2->where('number', 'like', preg_replace('/[^0-9]/', '', request('value')) . '%');
                });
            })
            ->orWhereHas('familyMembers.addresses', function ($query) {
                $query->where('address1', 'like', request('value') . '%');
            })
            ->orWhere(function ($query) {
                $query->where('last_name', 'like', '%' . request('value') . '%');
                $query->orWhere('first_name', 'like', '%' . request('value') . '%');
                $query->orWhere('preferred_first_name', 'like', '%' . request('value') . '%');
            })
            ->membersOnly()
            ->isNotDeceased()
            ->get();
            
        
        return view('app.directory.index')
            ->with('users', User::visibleTo(auth()->user())
                ->whereIn('id', $users->pluck('family_id')->toArray())
                ->when(auth()->user()->account->hasFeature('account.setting.show_directory_by_user'), function ($query1) {
                    $query1->adultsOnly();
                })
                ->when(!auth()->user()->account->hasFeature('account.setting.show_directory_by_user'), function ($query1) {
                    $query1->HeadsOfFamily();
                })
                ->with([
                    'emails:id,user_id,email,family_id,is_family,is_primary',
                    'familyMembers:id,account_id,family_id,preferred_first_name,first_name,last_name,family_role,birthdate,date_deceased',
                ])
                ->orderBy('last_name', 'asc')
                ->membersOnly()
                ->isNotDeceased()
                ->paginate(21, [
                    'id',
                    'account_id',
                    'preferred_first_name',
                    'first_name',
                    'last_name',
                    'family_id',
                    'family_role',
                ])
            );
    }

    public function viewFamily(User $user)
    {
        $cached_key   = uniqid();
        $cached_token = uniqid('tokn', true);

        Cache::put($cached_key, $cached_token, now()->addMinutes(20));
        Cache::put($cached_token, $cached_key, now()->addMinutes(20));

        // Get our family_id if we're trying to look at a user that is not head of household.
        if ($user->id !== $user->family_id) {
            $user = User::visibleTo(auth()->user())
                ->where('id', $user->family_id)
                ->where('family_id', $user->family_id)
                ->first();
        }

        return view('app.directory.view-family')
            ->with('user', $user)
            ->with('cached_key', $cached_key)
            ->with('cached_token', $cached_token);
    }

    public function downloadVcard($user_id)
    {
        if (!request('key') || !request('token')) {
            abort(403);
        }

        if (!Cache::has(request('key')) || !Cache::has(request('token'))) {
            abort(403, 'Sorry, your token has expired. Please refresh the page in Lightpost and try again.');
        }

        $cached_key   = Cache::get(request('key'));
        $cached_token = Cache::get(request('token'));

        if ($cached_key !== request('token') || $cached_token !== request('key')) {
            abort(403);
        }

//        Cache::forget(request('key'));
//        Cache::forget(request('token'));

        $user = User::find($user_id);

        $vcard = new VCard([
            'FN'  => $user->name,
            'TEL' => optional($user->getBestPhone())->number,
            'N'   => [$user->display_first_name, $user->last_name, '', '', ''],
        ]);

        return response()
            ->streamDownload(function () use ($vcard) {
                echo $vcard->serialize();
            }, 'vcard-' . Str::slug($user->name) . '.vcf', ['Content-Type' => 'text/vcard; charset=utf-8']);
    }
}
