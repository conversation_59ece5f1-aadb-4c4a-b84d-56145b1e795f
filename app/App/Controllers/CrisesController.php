<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Crises\Checkin;
use App\Crises\CheckinReply;
use App\Crises\Crisis;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;

class CrisesController extends Controller
{
    public function view($crisis)
    {
        return view('app.crises.view')->with([
            'crisis' => $crisis,
        ]);
    }

    public function viewCheckin($crisis, $checkin)
    {
        return view('app.crises.checkin')->with([
            'crisis'  => $crisis,
            'checkin' => $checkin,
        ]);
    }

    public function userCheckin()
    {
        $crisis = Auth::user()->account->getActiveCrisis();

        return view('app.crises.user-checkin')->with([
            'crisis'  => $crisis,
            'checkin' => Auth::user()->getCheckinForCrisis($crisis->id),
        ]);
    }

    public function userCheckinSave()
    {
        $this->validate(request(), [
            'checkin_type' => 'required|string|max:60',
            'notes'        => 'nullable|string',
        ]);

        try {
            $crisis = Auth::user()->account->getActiveCrisis();

            $checkin = Checkin::firstOrNew([
                'account_id' => Auth::user()->account_id,
                'crisis_id'  => $crisis->id,
                'family_id'  => Auth::user()->family_id,
            ]);

            $checkin->user_id      = Auth::user()->id;
            $checkin->type         = request('checkin_type');
            $checkin->notes        = request('notes');
            $checkin->responded_at = Carbon::now();

            $checkin->save();
            // Notifications are sent (if needed) through the CheckinObserver.

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('app.home.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function checkinReplySave($crisis, $checkin)
    {
        $this->validate(request(), [
            'type'  => 'required|string|max:60',
            'notes' => 'nullable|string',
        ]);

        try {
            $reply = CheckinReply::create([
                'account_id'        => Auth::user()->account_id,
                'crisis_id'         => $crisis->id,
                'user_id'           => Auth::user()->id,
                'crisis_checkin_id' => $checkin->id,
                'type'              => request('type'),
                'notes'             => request('notes'),
            ]);

            $reply->save();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('app.crises.checkins.view', [$crisis, $checkin]))
            ->with('message.success', 'Thank you! Your response has been recorded.');
    }

    public function userPublicCheckin()
    {
        $concat_data = Crypt::decryptString(request('token'));
        $data        = explode('+', $concat_data);
        $user_id     = $data[0];
        $crisis_id   = $data[1];

        $user   = User::find($user_id);
        $crisis = Crisis::find($crisis_id);

        $current_crisis = $user->account->getActiveCrisis();

        if ($crisis->id !== $current_crisis?->id || !request('token')) {
            return view('app.crises.public-invalid-token');
        }

        if ($user->id . '+' . $crisis->id != $concat_data) {
            return view('app.crises.public-invalid-token');
        }

        return view('app.crises.public-user-checkin')->with([
            'crisis'  => $crisis,
            'token'   => request('token'),
            'checkin' => $user->getCheckinForCrisis($crisis->id),
        ]);
    }

    public function userPublicCheckinSave()
    {
        $this->validate(request(), [
            'checkin_type' => 'required|string|max:60',
            'notes'        => 'nullable|string',
        ]);

        $concat_data = Crypt::decryptString(request('token'));
        $data        = explode('+', $concat_data);
        $user_id     = $data[0];
        $crisis_id   = $data[1];

        $user   = User::find($user_id);
        $crisis = Crisis::find($crisis_id);

        $current_crisis = $user->account->getActiveCrisis();

        if ($crisis->id !== $current_crisis?->id || !request('token')) {
            return view('app.crises.public-invalid-token');
        }

        if ($user->id . '+' . $crisis->id != $concat_data) {
            return view('app.crises.public-invalid-token');
        }

        try {
            $crisis = $user->account->getActiveCrisis();

            $checkin = Checkin::firstOrNew([
                'account_id' => $user->account_id,
                'crisis_id'  => $crisis->id,
                'family_id'  => $user->family_id,
            ]);

            $checkin->user_id      = $user->id;
            $checkin->type         = request('checkin_type');
            $checkin->notes        = request('notes');
            $checkin->responded_at = Carbon::now();

            $checkin->save();
            // Notifications are sent (if needed) through the CheckinObserver.

        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()
            ->with('message.success', 'Saved successfully.');
    }
}
