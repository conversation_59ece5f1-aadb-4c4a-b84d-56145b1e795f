<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Phone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AccountPhoneController extends Controller
{
    public function create()
    {
        return view('app.account.phone.create')
            ->withUser(Auth::user());
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        $this->validate(request(), [
            'number'           => 'required|string|max:32',
            'is_primary'       => 'integer',
            'is_family'        => 'integer',
            'is_hidden'        => 'integer',
            'messages_opt_out' => 'integer',
            'type'             => 'required|in:' . implode(',', array_keys(Phone::$types)),
        ]);

        try {
            $phone = new Phone;

            $phone->fill(request()->only([
                'number',
                'is_primary',
                'is_family',
                'is_hidden',
                'messages_opt_out',
                'type',
            ]));

            $phone->number  = Phone::format($phone->number);
            $phone->user_id = request()->get('user_id') ?: $user->id;

            if (request()->input('is_family') == 1) {
                $phone->family_id = $user->family_id;
            }

            $phone->save();

        } catch (\Exception $e) {
            Log::error($e);
            if (request()->ajax()) {
                return response()->json($e->getMessage(), 422);
            } else {
                return back()->with('message.failure', $e->getMessage());
            }
        }

        session()->flash('message.success', 'Saved successfully.');

        if (request()->ajax()) {
            return response()->json('OK', 200);
        } else {
            return back()->with('message.success', 'Saved successfully.');
        }
    }

    public function edit(Phone $phone)
    {
        return view('app.account.phone.edit')
            ->withUser(Auth::user())
            ->withPhone($phone);
    }

    public function save(Phone $phone)
    {
        $user = Auth::user();

        $this->validate(request(), [
            'number'           => 'required|string|max:32',
            'is_primary'       => 'integer',
            'is_family'        => 'integer',
            'is_hidden'        => 'integer',
            'messages_opt_out' => 'integer',
            'type'             => 'required|in:' . implode(',', Phone::getTypeKeys()),
        ]);

        try {
            $phone->fill(request()->only([
                'number',
                'type',
                'is_primary',
                'is_family',
                'is_hidden',
                'messages_opt_out',
            ]));

            $phone->number  = Phone::format($phone->number);
            $phone->user_id = request()->get('user_id') ?: $user->id;

            $phone->is_primary       = request()->input('is_primary', 0);
            $phone->is_family        = request()->input('is_family', 0);
            $phone->is_hidden        = request()->input('is_hidden', 0);
            $phone->messages_opt_out = request()->input('messages_opt_out', 0);

            if (request()->input('is_family') == 1) {
                $phone->family_id = $user->family_id;
            }

            $phone->save();

        } catch (\Exception $e) {
            Log::error($e);
            if (request()->ajax()) {
                return response()->json($e->getMessage(), 422);
            } else {
                return back()->with('message.failure', $e->getMessage());
            }
        }

        session()->flash('message.success', 'Saved successfully.');

        if (request()->ajax()) {
            return response()->json('OK', 200);
        } else {
            return back()->with('message.success', 'Saved successfully.');
        }
    }

    public function delete(Phone $phone)
    {
        try {
            if (Auth::user()->id === $phone->user_id || Auth::user()->family_id === $phone->user->family_id) {
                $phone->delete();
            }
        } catch (\Exception $e) {
            Log::error($e);
            if (request()->ajax()) {
                return response()->json($e->getMessage(), 422);
            } else {
                return back()->with('message.failure', $e->getMessage());
            }
        }

        session()->flash('message.success', 'Deleted successfully.');

        if (request()->ajax()) {
            return response()->json('OK', 200);
        } else {
            return redirect()->route('app.account.phones')->with('message.success', 'Deleted successfully.');
        }
    }
}
