<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Calendars\Calendar;
use App\Calendars\Event;
use App\Calendars\EventOccurrence;
use App\Calendars\Services\CalendarFeed;
use Illuminate\Support\Facades\Auth;

class CalendarController extends Controller
{
    public function index()
    {
        $calendars = Calendar::visibleTo(Auth::user())
            ->notHidden()
            ->get();

        return view('app.calendars.index')
            ->with('all_calendars', $calendars);
    }

    // Calendar is determined by HashID, not primary key ID.
    public function view($calendar_ulid)
    {
        $calendar = Calendar::visibleToAccount(auth()->user())
            ->withUlid($calendar_ulid)
            ->first();

        if (!$calendar) {
            abort(404);
        }

        $events = (new CalendarFeed())
            ->forUser(auth()->user())
            ->forCalendar($calendar)
            ->getFullCalendarFeed();

        return response()->json($events);
    }

    public function viewEvent(Event $event)
    {
        $event = Event::visibleTo(Auth::user())->find($event->id);

        return view('app.calendars.view-event')
            ->with('event', $event);
    }

    public function viewEventOccurrence(EventOccurrence $eventOccurrence)
    {
        $eventOccurrence = EventOccurrence::visibleTo(Auth::user())->find($eventOccurrence->id);

        return view('app.calendars.view-event-occurrence')
            ->with('event', $eventOccurrence->event)
            ->with('eventOccurrence', $eventOccurrence);
    }
}
