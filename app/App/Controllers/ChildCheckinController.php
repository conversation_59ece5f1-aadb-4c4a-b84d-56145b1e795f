<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\ChildCheckins\ChildCheckin;

class ChildCheckinController extends Controller
{
    public function index()
    {
        return view('app.kiosk.child-checkin.index')
            ->with('kids', []);
    }

    public function checkin()
    {
        return view('app.kiosk.child-checkin.modals.checkin');
    }

    public function viewCheckin(ChildCheckin $child_checkin)
    {
        return view('app.kiosk.child-checkin.modals.view-checkin')
            ->with('child', $child_checkin->child)
            ->with('checkin', $child_checkin);
    }

    public function invalidBarcodeError()
    {
        return view('app.kiosk.child-checkin.modals.error-invalid-barcode');
    }
}
