<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Visitors\Status;
use App\Visitors\Visitor;
use Illuminate\Pagination\Paginator;

class VisitorController extends Controller
{
    public function index()
    {
        Paginator::useTailwind();

        $statuses = Status::visibleTo(auth()->user())
            ->withCount('visitors')
            ->orderBy('sort_id')
            ->get();

        $visitors = Visitor::visibleTo(auth()->user())
            ->isNotArchived()
            ->when(request()->get('status'), function ($query) {
                $query->where('visitor_status_id', request()->get('status'));
            })
            ->orderBy('last_contact_at', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('app.visitors.index')
            ->with('visitors', $visitors)
            ->with('statuses', $statuses)
            ->with('selected_status', request()->get('status'));
    }

    public function view(Visitor $visitor)
    {
        return view('app.visitors.view')
            ->with('visitor', $visitor);
    }

    public function create()
    {
        return view('app.visitors.create');
    }
}
