<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Email;
use App\Users\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class AccountEmailController extends Controller
{
    public function create()
    {
        return view('app.account.email.create')
            ->withUser(Auth::user());
    }

    public function store()
    {
        $user = Auth::user();

        $this->validate(request(), [
            'email'                 => 'required|email|max:128|unique:user_emails',
            'type'                  => 'required|in:' . implode(',', Email::getTypeKeys()),
            'is_primary'            => 'nullable|integer',
            'is_family'             => 'nullable|integer',
            'is_hidden'             => 'nullable|integer',
            'receives_group_emails' => 'nullable|integer',
        ]);

        // Make sure the user is not messing with the provided user_id.
        if (request()->get('user_id')) {
            $email_owner = User::find(request()->get('user_id'));

            if ($email_owner->account_id !== $user->account_id) {
                abort(403);
            }
        }

        try {
            $email = $user->emails()->create(request()->only([
                'email',
                'type',
                'is_primary',
                'is_family',
                'is_hidden',
                'receives_group_emails',
            ]));

            $email->user_id = request()->get('user_id') ?: $user->id;

            if (request()->input('is_family') == 1) {
                $email->family_id = $user->family_id;
            }

            $email->save();
        } catch (\Exception $e) {
            Log::error($e);
            if (request()->ajax()) {
                return response()->json($e->getMessage(), 422);
            } else {
                return back()->with('message.failure', $e->getMessage());
            }
        }

        session()->flash('message.success', 'Saved successfully.');

        if (request()->ajax()) {
            return response()->json('OK', 200);
        } else {
            return redirect()
                ->route('app.account.emails')
                ->with('message.success', 'Saved successfully.');
        }
    }

    public function edit(Email $email)
    {
        return view('app.account.email.edit')
            ->withUser(Auth::user())
            ->withEmail($email);
    }

    public function save(Email $email)
    {
        $user = Auth::user();

        $this->validate(request(), [
            'email'                 => [
                'required',
                'email',
                'max:128',
                Rule::unique('user_emails')->ignore($email->id),
            ],
            'type'                  => 'required|in:' . implode(',', Email::getTypeKeys()),
            'is_primary'            => 'nullable|integer',
            'is_family'             => 'nullable|integer',
            'is_hidden'             => 'nullable|integer',
            'receives_group_emails' => 'nullable|integer',
        ]);

        // Make sure the user is not messing with the provided user_id.
        if (request()->get('user_id')) {
            $email_owner = User::find(request()->get('user_id'));

            if ($email_owner->account_id !== $user->account_id) {
                abort(403);
            }
        }

        try {
            $email->fill(request()->only([
                'email',
                'type',
                'is_primary',
                'is_family',
                'is_hidden',
                'receives_group_emails',
            ]));

            $email->is_primary            = request()->input('is_primary', 0);
            $email->is_family             = request()->input('is_family', 0);
            $email->is_hidden             = request()->input('is_hidden', 0);
            $email->receives_group_emails = request()->input('receives_group_emails', 0);

            $email->user_id = request()->get('user_id') ?: $user->id;

            if (request()->input('is_family') == 1) {
                $email->family_id = $user->family_id;
            }

            $email->save();
        } catch (\Exception $e) {
            Log::error($e);
            if (request()->ajax()) {
                return response()->json($e->getMessage(), 422);
            } else {
                return back()->with('message.failure', $e->getMessage());
            }
        }

        session()->flash('message.success', 'Saved successfully.');

        if (request()->ajax()) {
            return response()->json('OK', 200);
        } else {
            return redirect()
                ->route('app.account.emails')
                ->with('message.success', 'Saved successfully.');
        }
    }

    public function delete(Email $email)
    {
        try {
            if (Auth::user()->id === $email->user_id || Auth::user()->family_id === $email->user->family_id) {
                $email->delete();
            }
        } catch (\Exception $e) {
            Log::error($e);
            if (request()->ajax()) {
                return response()->json($e->getMessage(), 422);
            } else {
                return redirect()->route('app.account.emails')->with('message.failure', $e->getMessage());
            }
        }

        session()->flash('message.success', 'Deleted successfully.');

        if (request()->ajax()) {
            return response()->json('OK', 200);
        } else {
            return redirect()->route('app.account.emails')->with('message.success', 'Deleted successfully.');
        }
    }
}
