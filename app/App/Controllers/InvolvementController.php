<?php

namespace App\App\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Subarea;
use App\Users\Services\UpdateInvolvementSelection;
use Illuminate\Support\Facades\Auth;

class InvolvementController extends Controller
{
    public function view()
    {
        return view('app.account.involvement')
            ->with('user', Auth::user())
            ->with('categories', Category::visibleTo(auth()->user())->applyRestrictions(auth()->user())->orderBy('sort_id', 'asc')->get())
            ->with('involvement_records', auth()->user()->allInvolvement());
    }

    public function saveSelection()
    {
        $user = auth()->user();

        $area_id    = request()->get('area_id');
        $subarea_id = request()->get('subarea_id');
        $toggle_on  = request()->get('new_value');

        $result = 1;

        try {
            if ($area_id) {
                $area = Area::visibleTo(auth()->user())->find($area_id);

                if ($toggle_on) {
                    (new UpdateInvolvementSelection($user))
                        ->forArea($area)
                        ->save();
                } else {
                    (new UpdateInvolvementSelection($user))
                        ->forArea($area)
                        ->delete();
                }
            } elseif ($subarea_id) {
                $subarea = Subarea::visibleTo(auth()->user())->find($subarea_id);

                if ($toggle_on) {
                    (new UpdateInvolvementSelection($user))
                        ->forSubarea($subarea)
                        ->save();
                } else {
                    (new UpdateInvolvementSelection($user))
                        ->forSubarea($subarea)
                        ->delete();
                }
            } else {
                response()->json('No data given.', 400);
            }
        } catch (\Exception $e) {
            response()->json($e->getMessage(), 400);
        }

        if (!$result) {
            return response()->json('Failed.', 400);
        }

        return response()->json('OK', 200);
    }
}
