<?php

namespace App\Attendance\Services;

use App\Attendance\Attendance;
use App\Users\User;

class ToggleAttendanceRecordService
{
    public $date             = null;
    public $user             = null;
    public $attendance_types = [];
    public $whole_family     = false;
    public $family_members   = null;
    public $exclude_children = false;

    public function toggle()
    {
        if (!$this->user) {
            throw new \Exception('An invalid user was provided to create an attendance record.');
        }
        if (!$this->date) {
            throw new \Exception('No date was provided to create an attendance record.');
        }
        if (empty($this->attendance_types)) {
            throw new \Exception('No attendance type was provided to create an attendance record.');
        }

        // Go through each attendance type we're given.
        foreach ($this->attendance_types as $type_id) {

            // See if we have a record matching these params.
            $attendance = Attendance::where([
                'user_id'                 => $this->user->id,
                'date_attendance'         => $this->date->format('Y-m-d'),
                'user_attendance_type_id' => $type_id,
            ])->first();


            // If we found something, delete it. Otherwise, create it.
            if ($attendance) {

                $attendance->delete();

                if ($this->whole_family) {
                    // If we're only updating specific family members.
                    if ($this->family_members) {
                        foreach ($this->user->familyMembers()->whereIn('id', $this->family_members)->get() as $familyMember) {
                            $attendance = Attendance::where([
                                'user_id'                 => $familyMember->id,
                                'date_attendance'         => $this->date->format('Y-m-d'),
                                'user_attendance_type_id' => $type_id,
                            ])->first();

                            if ($attendance) {
                                $attendance->delete();
                            }
                        }
                    } // Otherwise, update all family members.
                    else {
                        foreach ($this->user->familyMembers()->excludeChildren($this->exclude_children)->get() as $familyMember) {
                            $attendance = Attendance::where([
                                'user_id'                 => $familyMember->id,
                                'date_attendance'         => $this->date->format('Y-m-d'),
                                'user_attendance_type_id' => $type_id,
                            ])->first();

                            if ($attendance) {
                                $attendance->delete();
                            }
                        }
                    }
                }
            } else {
                Attendance::firstOrCreate([
                    'user_id'                 => $this->user->id,
                    'date_attendance'         => $this->date->format('Y-m-d'),
                    'user_attendance_type_id' => $type_id,
                ]);
                if ($this->whole_family) {
                    foreach ($this->user->familyMembers()->excludeChildren($this->exclude_children)->get() as $familyMember) {
                        Attendance::firstOrCreate([
                            'user_id'                 => $familyMember->id,
                            'date_attendance'         => $this->date->format('Y-m-d'),
                            'user_attendance_type_id' => $type_id,
                        ]);
                    }
                }
            }
        }
    }

    public function forUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function excludeChildren($exclude_children = true)
    {
        $this->exclude_children = $exclude_children;

        return $this;
    }

    public function forSpecificFamilyMembers($family_members = [])
    {
        $this->family_members = $family_members;

        return $this;
    }

    public function forNow()
    {
        $this->date = now()->setTimezone($this->user->account->timezone);

        return $this;
    }

    public function forDate($date_in_users_timezone)
    {
        $this->date = $date_in_users_timezone;

        return $this;
    }

    public function forAttendanceTypes(array $attendance_type_ids)
    {
        $this->attendance_types = $attendance_type_ids;

        return $this;
    }

    public function includeFamily($include_family = true)
    {
        $this->whole_family = $include_family;

        return $this;
    }
}
