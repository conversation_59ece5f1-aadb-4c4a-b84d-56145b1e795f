<?php

namespace App\Attendance\Services;

use App\Attendance\Attendance;
use App\Users\User;

class AttendanceService
{
    public $user = null;

    public function __construct(User $user = null)
    {
        if ($user) {
            $this->user = $user;
        }

        return $this;
    }

    public function forUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function familyHasCheckedInNow()
    {
        $all_family_members_ids = auth()->user()->allFamilyMembers()->pluck('id')->toArray();

        return Attendance::whereIn('user_id', $all_family_members_ids)
            ->where('date_attendance', now()->setTimezone($this->user->account->timezone)->format('Y-m-d'))
            ->whereHas('type', function ($query) {
                $query->isToday($this->user->account->timezone)
                    ->IsForNow($this->user->account->timezone);
            })
            ->exists();
    }

    public function getCheckinAttendanceTypesForNow()
    {
        return $this->user->account
            ->attendanceTypes()
            ->isMemberCheckin()
            ->isToday()
            ->IsForNow($this->user->account->timezone)
            ->get();
    }

    public function getFamilyAttendanceRecordsForNow()
    {
        $all_family_members_ids = auth()->user()->allFamilyMembers()->pluck('id')->toArray();

        return Attendance::whereIn('user_id', $all_family_members_ids)
            ->where('date_attendance', now()->setTimezone($this->user->account->timezone)->format('Y-m-d'))
            ->whereHas('type', function ($query) {
                $query->isToday($this->user->account->timezone)
                    ->IsForNow($this->user->account->timezone);
            })
            ->get();
    }

    public function hasMemberAttendanceCheckinNow()
    {
        return $this->user->account
            ->attendanceTypes()
            ->isMemberCheckin()
            ->isToday($this->user->account->timezone)
            ->IsForNow($this->user->account->timezone)
            ->exists();
    }
}
