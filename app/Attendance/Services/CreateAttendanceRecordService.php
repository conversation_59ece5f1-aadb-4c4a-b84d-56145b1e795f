<?php

namespace App\Attendance\Services;

use App\Attendance\Attendance;
use App\Users\User;

class CreateAttendanceRecordService
{
    public $date             = null;
    public $user             = null;
    public $attendance_types = [];
    public $whole_family     = false;
    public $exclude_children = false;
    public $comment          = null;

    public function create()
    {
        if (!$this->user) {
            throw new \Exception('An invalid user was provided to create an attendance record.');
        }
        if (!$this->date) {
            throw new \Exception('No date was provided to create an attendance record.');
        }
        if (empty($this->attendance_types)) {
            throw new \Exception('No attendance type was provided to create an attendance record.');
        }

        foreach ($this->attendance_types as $type_id) {
            Attendance::firstOrCreate([
                'user_id'                 => $this->user->id,
                'date_attendance'         => $this->date->format('Y-m-d'),
                'user_attendance_type_id' => $type_id,
            ], [
                'comment' => $this->comment,
            ]);

            if ($this->whole_family) {
                foreach ($this->user->familyMembers()->excludeChildren($this->exclude_children)->get() as $familyMember) {
                    Attendance::firstOrCreate([
                        'user_id'                 => $familyMember->id,
                        'date_attendance'         => $this->date->format('Y-m-d'),
                        'user_attendance_type_id' => $type_id,
                    ], [
                        'comment' => $this->comment,
                    ]);
                }
            }
        }
    }

    public function forUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function excludeChildren($exclude_children = true)
    {
        $this->exclude_children = $exclude_children;

        return $this;
    }

    public function forNow()
    {
        $this->date = now()->setTimezone($this->user->account->timezone);

        return $this;
    }

    public function forDate($date_in_users_timezone)
    {
        $this->date = $date_in_users_timezone;

        return $this;
    }

    public function forAttendanceTypes(array $attendance_type_ids)
    {
        $this->attendance_types = $attendance_type_ids;

        return $this;
    }

    public function includeFamily($include_family = true)
    {
        $this->whole_family = $include_family;

        return $this;
    }

    public function withComment($comment = null)
    {
        $this->comment = $comment;

        return $this;
    }
}
