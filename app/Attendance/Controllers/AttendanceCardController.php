<?php

namespace App\Attendance\Controllers;

use App\Attendance\AttendanceCard;
use App\Base\Http\Controllers\Controller;
use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;
use Illuminate\Support\Facades\Auth;

class AttendanceCardController extends Controller
{
    public function index()
    {
//        $data = 'HTTPS://PEW.CARDS/C/12399';
//
//        $opts = (new QROptions([
//            'version'      => 1,
//            'outputType'   => QRCode::OUTPUT_IMAGE_PNG,
//            'eccLevel'     => QRCode::ECC_L,
//            'addQuietzone' => false,
//            'dataMode'     => QRCode::DATA_ALPHANUM,
//        ]));
//        $qr   = (new QRCode($opts));
//
////quick and simple:
//        echo '<img src="' . $qr->render($data) . '" alt="QR Code" />';
//        exit;

        $cards = AttendanceCard::visibleTo(Auth::user())
            ->isNotSpam()
            ->paginate(50);

        return view('admin.attendance.cards.index')
            ->with([
                'cards' => $cards,
            ]);
    }

    public function view(AttendanceCard $card)
    {
        $card->markAsRead(auth()->user());

        return view('admin.attendance.cards.view')
            ->with([
                'card' => $card,
            ]);
    }

    public function visitorQRCode()
    {
        $v_data = route('public.attendance.cards.new', [auth()->user()->account->getHashId(), 'v']);
        $data   = route('public.attendance.cards.new', ['account_hash_id' => auth()->user()->account->getHashId()]);

        $options = new QROptions(
            [
                'version'      => 3,
                'eccLevel'     => QRCode::ECC_M,
                'outputType'   => QRCode::OUTPUT_IMAGE_PNG,
                'scale'        => 20,
                'addQuietzone' => false,
                'dataMode'     => QRCode::DATA_ALPHANUM,
            ]
        );

        $visitor_qr_code_image = (new QRcode($options))->render($v_data);
        $qr_code_image         = (new QRcode($options))->render($data);

        return view('admin.attendance.visitors.qrcode')
            ->with('visitor_qr_code_image', $visitor_qr_code_image)
            ->with('qr_code_image', $qr_code_image);
    }

    public function markAsUnread(AttendanceCard $card)
    {
        $card->unmarkAsRead();

        return redirect()
            ->route('admin.attendance.cards.index')
            ->with('message.success', 'Marked as unread.');
    }

    public function markAsSpam(AttendanceCard $card)
    {
        $card->markAsSpam(auth()->user());

        return back()->with('message.success', 'Marked as spam.');
    }

    public function unmarkAsSpam(AttendanceCard $card)
    {
        $card->unmarkAsSpam();

        return back()->with('message.success', 'Unmarked as spam.');
    }

    public function destroy(AttendanceCard $card)
    {
        $card->delete();

        return redirect()
            ->route('admin.attendance.cards.index')
            ->with('message.success', 'Card deleted.');
    }
}
