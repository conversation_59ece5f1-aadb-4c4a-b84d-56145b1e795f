<?php

namespace App\Attendance\Controllers;

use App\Attendance\AttendanceGeneralCount;
use App\Attendance\AttendanceType;
use App\Base\Http\Controllers\Controller;
use App\Exports\Attendance\GeneralAttendanceExport;
use Carbon\Carbon;
use Illuminate\Pagination\Paginator;
use Maatwebsite\Excel\Facades\Excel;

class AttendanceGeneralController extends Controller
{
    public function __construct()
    {
        parent::__construct();

        Paginator::useTailwind();
    }

    public function index()
    {
        $general_count_weeks = AttendanceGeneralCount::select('attendance_at')
            ->groupBy('attendance_at')
            ->orderBy('attendance_at', 'desc')
            ->visibleTo(auth()->user())
            ->paginate(30);

        return view('admin.attendance.general.index')
            ->with([
                'weeks' => $general_count_weeks,
                'types' => AttendanceType::visibleTo(auth()->user())->orderBy('sort_id', 'asc')->get(),
            ]);
    }

    public function create()
    {
        return view('admin.attendance.modals.general-create')
            ->with([
                'types' => AttendanceType::visibleTo(auth()->user())->get(),
                'today' => Carbon::now()->setTimezone(auth()->user()->account->timezone)->toDateString(),
            ]);
    }

    public function store()
    {
        $this->validate(request(), [
            'date'   => 'required|date',
            'counts' => 'required|array',
        ]);

        foreach (request()->get('counts') as $attendance_type_id => $count) {
            if ($count !== null) {
                AttendanceGeneralCount::updateOrCreate([
                    'account_id'              => auth()->user()->account_id,
                    'user_attendance_type_id' => $attendance_type_id,
                    'attendance_at'           => request()->get('date'),
                ], [
                    'created_by_user_id' => auth()->user()->id,
                    'count'              => $count,
                ]);
            }
        }

        return redirect(route('admin.attendance.general.index'))
            ->with('message.success', 'Attendance recorded successfully.');
    }

    public function destroy(AttendanceGeneralCount $attendance_general_count)
    {
        try {
            if ($attendance_general_count->user->account_id == auth()->user()->account_id) {
                $attendance_general_count->delete();
            }
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Attendance record removed.');
    }

    public function downloadGeneralReport()
    {
        $startDate = request()->get('start_date') ? Carbon::parse(request()->get('start_date')) : Carbon::now()->subMonth();
        $endDate   = request()->get('end_date') ? Carbon::parse(request()->get('end_date')) : Carbon::now();

        $attendance = AttendanceGeneralCount::select('attendance_at')
            ->visibleTo(auth()->user())
            ->groupBy('attendance_at')
            ->orderBy('attendance_at', 'desc')
            ->whereBetween('attendance_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->get();

        $types = AttendanceType::visibleTo(auth()->user())->orderBy('sort_id', 'asc')->get();

        return Excel::download(
            new GeneralAttendanceExport($attendance, $types),
            'general-attendance-' . $startDate->format('Y-m-d') . '-to-' . $endDate->format('Y-m-d') . '.csv'
        );
    }
}
