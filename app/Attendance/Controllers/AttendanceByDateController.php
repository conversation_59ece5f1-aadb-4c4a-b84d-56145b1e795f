<?php

namespace App\Attendance\Controllers;

use App\Attendance\Attendance;
use App\Attendance\AttendanceType;
use App\Base\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AttendanceByDateController extends Controller
{
    public function index()
    {
        return view('admin.attendance.attendance-by-dates.index')
            ->with([
                'dates' => Attendance::visibleTo(Auth::user())
                    ->select(DB::raw('DISTINCT ON (user_id) *'))
                    ->orderBy('date_attendance', 'desc')
                    ->paginate(15),
            ]);
    }

    public function show($date)
    {
        return view('admin.attendance.attendance-by-dates.show')
            ->with([
                'attendances'      => Attendance::visibleTo(Auth::user())->attendanceFromDate($date)->paginate(15),
                'date'             => Carbon::createFromFormat('Y-m-d', $date)->toFormattedDateString(),
                'attendance_types' => AttendanceType::visibleTo(Auth::user())->get(),
            ]);
    }
}
