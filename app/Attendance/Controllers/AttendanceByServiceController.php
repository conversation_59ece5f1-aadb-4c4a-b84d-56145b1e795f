<?php

namespace App\Attendance\Controllers;

use Carbon\Carbon;
use App\Users\Attendance;
use App\Base\Http\Controllers\Controller;

class AttendanceByServiceController extends Controller
{
    public function index() 
    {
        return view('admin.attendance.attendance-by-services.index')
            ->with([
                'services' => Attendance::attendanceTrackingWeeks()
                    ->orderBy('date_attendance', 'desc')
                    ->paginate(15),
            ]);
    }

    public function show($date)
    {
        return view('admin.attendance.attendance-by-services.show')
            ->with([
                'attendances' => Attendance::attendanceFromDate($date)->paginate(15),
                'date' => Carbon::createFromFormat('Y-m-d', $date)->toFormattedDateString(),
                'attendance_types' => Attendance::$types,
            ]);
    }
}
