<?php

namespace App\Attendance;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;

class AttendanceCardQuestion extends Model
{
    protected $table = 'attendance_card_questions';

    protected $casts = [
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
        'deleted_at'  => 'datetime',
        'for_visitor' => 'boolean',
        'for_member'  => 'boolean',
        'is_active'   => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'question',
        'type', // text|select|multiselect
        'option1',
        'option2',
        'option3',
        'option4',
        'option5',
        'option6',
        'option7',
        'option8',
        'for_visitor',
        'for_member',
        'is_active',
    ];

    public static $types = [
        'text',
        'select',
        'multiselect',
    ];

    public static $question_formats = [
        'first_time_visitor'                    => 'First time visiting?',
        'returning_visitor'                     => 'Returning visitor?',
        'new_to_area'                           => 'New to the area?',
        'learn_more_about_church'               => 'Would you like to learn more about the church?',
        'learn_more_about_becoming_a_christian' => 'Would you like to learn more about becoming a Christian?',
        'interested_in_bible_study'             => 'Interested in a Bible study?',
        'would_like_a_visit'                    => 'Would you like a visit?',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function type()
    {
        return $this->belongsTo(AttendanceType::class, 'user_attendance_type_id');
    }

    public function scopeForVisitor($query)
    {
        return $query->where('for_visitor', true);
    }

    public function scopeForMember($query)
    {
        return $query->where('for_member', true);
    }

}
