<?php

namespace App\Attendance;

use App\Accounts\Account;
use App\Attendance\Scopes\UserAttendanceTypeVisibleToScope;
use App\Base\Models\Model;
use App\BibleClasses\BibleClass;
use App\Sermons\Sermon;
use App\Users\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;

class AttendanceType extends Model
{
    use SoftDeletes;

    protected $table = 'user_attendance_types';

    protected $casts = [
        'created_at'            => 'datetime',
        'updated_at'            => 'datetime',
        'deleted_at'            => 'datetime',
        'days_of_week'          => 'array', // https://laravel.com/docs/10.x/eloquent-mutators#array-and-json-casting
        'is_am'                 => 'bool',
        'is_pm'                 => 'bool',
        'enable_member_checkin' => 'bool',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'name',
        'short_name',
        'sort_id',
        'days_of_week',
        'is_am',
        'is_pm',
        'enable_member_checkin',
    ];

    protected $am_start_time_hour = 0;
    protected $am_end_time_hour   = 14;
    protected $pm_start_time_hour = 14;
    protected $pm_end_time_hour   = 23;

    static public $days_of_week = [
        7 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    static public $days_of_week_short = [
        7 => 'Sun',
        1 => 'Mon',
        2 => 'Tues',
        3 => 'Wed',
        4 => 'Thur',
        5 => 'Fri',
        6 => 'Sat',
    ];

    protected static function booted()
    {
        static::addGlobalScope('sort', function (Builder $builder) {
            $builder->orderBy('sort_id', 'asc')
                ->orderBy('name', 'desc');
        });
    }

    public static function convertDayOfWeekNumberFromISOToNumeric($day_of_week)
    {
        if ($day_of_week == 7) {
            return 0;
        } else {
            return $day_of_week;
        }
    }

    public static function convertDayOfWeekNumberFromNumericToISO($day_of_week)
    {
        if ($day_of_week == 0) {
            return 7;
        } else {
            return $day_of_week;
        }
    }

    public function scopeVisibleTo($query, User $user)
    {
        return (new UserAttendanceTypeVisibleToScope())->getQuery($query, $user);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function attendance()
    {
        return $this->hasMany(Attendance::class);
    }

    public function bibleClasses()
    {
        return $this->hasMany(BibleClass::class, 'user_attendance_type_id');
    }

    public function Sermons()
    {
        return $this->hasMany(Sermon::class, 'user_attendance_type_id');
    }

    public function attendanceCards()
    {
        return $this->belongsToMany(AttendanceCard::class, 'attendance_card_to_attendance_type', 'user_attendance_type_id', 'attendance_card_id');
    }

    public function getShortName()
    {
        return $this->short_name ?: $this->name;
    }

    public function getCountForDate($date)
    {
        return Attendance::where('user_attendance_type_id', $this->id)
            ->where('date_attendance', $date->format('Y-m-d'))
            ->count();
    }

    public function getDaysOfWeekAsArray()
    {
        return array_values($this->days_of_week);
    }

    public function getGeneralCountForDate($date)
    {
        return optional(
            AttendanceGeneralCount::where('user_attendance_type_id', $this->id)
                ->where('attendance_at', $date->format('Y-m-d'))
                ->where('account_id', $this->account_id)
                ->first()
        )->count;
    }

    public function scopeIsMemberCheckin($query)
    {
        return $query->where('enable_member_checkin', true);
    }

    public function scopeIsToday($query, $timezone = 'America/Chicago')
    {
        return $query->whereJsonContains('days_of_week', now()->setTimezone($timezone)->format('N'));
    }

    // Timezone in the format of "America/Chicago" -- found in account()->timezone
    public function scopeIsForNow($query, $timezone = 'America/Chicago')
    {
        return $query->where(function ($query) use ($timezone) {
            $query->where(function ($query) use ($timezone) {
                $query->where(function ($query) {
                    $query->where('is_am', false)->orWhereNull('is_am'); // Value could be NULL, which is NOT FALSE .. we have to check for both.
                })->where(function ($query) {
                    $query->where('is_pm', false)->orWhereNull('is_pm');
                });
            })->orWhere(function ($query) use ($timezone) {
                $query->when(now()->setTimezone($timezone)->format('G') < $this->am_end_time_hour, function ($query) {
                    $query->where('is_am', true);
                })->when(now()->setTimezone($timezone)->format('G') >= $this->pm_start_time_hour, function ($query) {
                    $query->where('is_pm', true);
                });
            });
        });
    }

    /**
     * Convert our days of week (7=Sun, 1=Mon) to MySQL's DAYOFWEEK format (1=Sun, 2=Mon)
     *
     * @return array
     */
    public function getMySqlDaysOfWeek(): array
    {
        return array_map(function ($day) {
            return $day == 7 ? 1 : $day + 1;
        }, $this->days_of_week ?: []);
    }
}
