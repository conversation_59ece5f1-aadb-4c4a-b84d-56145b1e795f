<?php

namespace App\Attendance\Policies;

use App\Attendance\AttendanceCard;
use App\Users\User;

class AttendanceCardPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.attendance')) {
            return false;
        }
    }

    public function create(User $user)
    {
        return $user->hasPermission('attendance.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('attendance.manage');
    }

    public function edit(User $user)
    {
        return $user->hasPermission('attendance.manage');
    }

    public function manageTypes(User $user)
    {
        return $user->hasPermission('attendance.manage');
    }

    public function index(User $user)
    {
        return $user->hasPermission('attendance.index');
    }

    public function show(User $user)
    {
        return $user->hasPermission('attendance.manage');
    }

    public function deleteVisitorCard(User $user, AttendanceCard $card)
    {
        return $user->hasPermission('attendance.manage')
               && $user->account_id == $card->account_id;
    }
}
