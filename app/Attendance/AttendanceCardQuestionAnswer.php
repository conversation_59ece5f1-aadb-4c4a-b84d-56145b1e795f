<?php

namespace App\Attendance;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;

class AttendanceCardQuestionAnswer extends Model
{
    protected $table = 'attendance_card_question_answers';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'attendance_card_question_id',
        'answer',
        'option1',
        'option2',
        'option3',
        'option4',
        'option5',
        'option6',
        'option7',
        'option8',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function question()
    {
        return $this->belongsTo(AttendanceCardQuestion::class);
    }
}
