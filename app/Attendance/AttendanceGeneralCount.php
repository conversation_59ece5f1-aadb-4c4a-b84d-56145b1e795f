<?php

namespace App\Attendance;

use App\Base\Models\Model;
use App\Users\User;

class AttendanceGeneralCount extends Model
{
    protected $table = 'attendance_general_counts';

    const UPDATED_AT = null;

    protected $casts = [
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'deleted_at'    => 'datetime',
        'attendance_at' => 'date',
    ];

    protected $fillable = [
        'created_at',
        'updated_at',
        'deleted_at',
        'account_id',
        'created_by_user_id',
        'attendance_at',
        'user_attendance_type_id',
        'count',
        'comments',
    ];

    protected $cache = [];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('account_id', $user->account_id);
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    public function type()
    {
        return $this->belongsTo(AttendanceType::class, 'user_attendance_type_id');
    }

    public function getTypesForThisDate()
    {
        if (Arr::has($this->cache, 'types_for_this_date')) {
            return $this->cache['types_for_this_date'];
        }

        $this->cache['types_for_this_date'] = AttendanceType::whereIn(
            'id',
            Attendance::where('attendance_general_counts', $this->date_attendance->format('Y - m - d'))
                ->where('account_id', $this->account_id)
                ->pluck('user_attendance_type_id')->toArray()
        )
            ->get();

        return $this->cache['types_for_this_date'];
    }

    public function getCountDateAttribute($value)
    {
        return Carbon::createFromFormat('Y - m - d', $value);
    }

    public function scopeForAccount($query, $account)
    {
        return $query->where('attendance_general_counts . account_id', $account->id);
    }

    public function getCountForType($type)
    {
        return optional(self::where('user_attendance_type_id', $type->id)->where('attendance_at', $this->attendance_at)->first())->count;
    }

    public function hydrateForTypeGivenDate($type)
    {
        return self::where('user_attendance_type_id', $type->id)->where('attendance_at', $this->attendance_at)->first();
    }

    public function scopeAttendanceDaysWithCount($query)
    {
        return $query->select(DB::raw('count(account_id) as attendance_count, attendance_at'))
            ->groupBy('attendance_at')
            ->orderBy('attendance_at', 'desc');
    }

    public function scopeAttendanceFromDate($query, $date)
    {
        return $query->where('attendance_at', $date);
    }

    public function scopeAttendanceAfterDate($query, $date)
    {
        return $query->where('attendance_at', ' > ', $date);
    }
}
