<?php

namespace App\Attendance;

use App\Attendance\Scopes\UserAttendanceVisibleToScope;
use App\Base\Models\Model;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class Attendance extends Model
{
    protected $table = 'user_attendance';

    const UPDATED_AT = null;

    protected $casts = [
        'created_at'      => 'datetime',
        'date_attendance' => 'datetime:Y-m-d',
    ];

    protected $fillable = [
        'date_attendance',
        'user_attendance_type_id',
        'user_id',
    ];

    protected $cache = [];

    public function scopeVisibleTo($query, User $user)
    {
        return (new UserAttendanceVisibleToScope())->getQuery($query, $user);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function type()
    {
        return $this->belongsTo(AttendanceType::class, 'user_attendance_type_id')
            ->withTrashed();
    }

    public function getTypesForThisDate()
    {
        if (Arr::has($this->cache, 'types_for_this_date')) {
            return $this->cache['types_for_this_date'];
        }

        $this->cache['types_for_this_date'] = AttendanceType::whereIn('id',
            Attendance::where('date_attendance', $this->date_attendance->format('Y-m-d'))
                ->where('user_id', $this->user_id)
                ->pluck('user_attendance_type_id')->toArray())
            ->get();

        return $this->cache['types_for_this_date'];
    }

    public function scopeGetDatesAttendanceHappensAfter($query, $date)
    {
        return $query->select('date_attendance', 'user_attendance_type_id')
            ->distinct('date_attendance')
            ->where('date_attendance', '>', $date);
    }

    public function scopeGetAttendanceTypesAfter($query, $date)
    {
        return $query->select('user_attendance.user_attendance_type_id')
//            ->having('date_attendance', '>', $date)
            ->groupBy('user_attendance.user_attendance_type_id')
            ->join('user_attendance as u2', 'user_attendance.id', '=', 'u2.id');
    }

    public function scopeForUser($query, $user)
    {
        return $query->where('user_attendance.user_id', $user->id);
    }

    public function scopeAttendanceDaysWithCount($query)
    {
        return $query->select(DB::raw('count(user_id) as attendance_count, date_attendance'))
            ->groupBy('date_attendance')
            ->orderBy('date_attendance', 'desc');
    }

    public function scopeAttendanceFromDate($query, $date)
    {
        return $query->where('date_attendance', $date);
    }

    public function scopeAttendanceAfterDate($query, $date)
    {
        return $query->where('date_attendance', '>', $date);
    }
}
