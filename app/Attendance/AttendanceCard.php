<?php

namespace App\Attendance;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;
use Illuminate\Database\Eloquent\SoftDeletes;

class AttendanceCard extends Model
{
    use SoftDeletes;

    protected $table = 'attendance_cards';

    protected $casts = [
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
        'deleted_at'  => 'datetime',
        'archived_at' => 'datetime',
        'is_member'   => 'boolean',
        'is_visitor'  => 'boolean',
        'read_at'     => 'datetime',
        'is_spam'     => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'ip_address',
        'user_id',
        'family_id',
        'name',
        'first_name',
        'last_name',
        'spouse_name',
        'family_member_names',
        'address1',
        'address2',
        'city',
        'state',
        'zip',
        'country',
        'phone',
        'email',
        'guest_of',
        'comments',
        'is_member',
        'is_visitor',
        'read_at',
        'is_spam',
        'marked_read_user_id',
        'marked_archive_user_id',
        'marked_spam_user_id',
    ];

    public static $statuses = [
        'new'      => 'New',
        'viewed'   => 'Viewed',
        'followup' => 'Follow Up Needed',
        'flagged'  => 'Flagged',
    ];

    public static $status_colors = [
        'new'      => 'blue',
        'viewed'   => 'gray',
        'followup' => 'purple',
        'flagged'  => 'red',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function family()
    {
        return $this->belongsTo(User::class, 'id', 'family_id');
    }

    public function userComments()
    {
        return $this->hasMany(AttendanceCardComment::class, 'attendance_card_id');
    }

    public function answers()
    {
        return $this->hasMany(AttendanceCardQuestionAnswer::class);
    }

    public function attendanceTypes()
    {
        return $this->belongsToMany(AttendanceType::class, 'attendance_card_to_attendance_type', 'attendance_card_id', 'user_attendance_type_id');
    }

    public function markAsSpam(User|null $user = null)
    {
        $this->is_spam = now();

        if ($user) {
            $this->marked_spam_user_id = $user?->id;
        }

        $this->save();
    }

    public function markAsRead(User|null $user = null)
    {
        $this->read_at = now();

        if ($user) {
            $this->marked_read_user_id = $user?->id;
        }

        $this->save();
    }

    public function unmarkAsSpam()
    {
        $this->is_spam = null;

        $this->save();
    }

    public function unmarkAsRead()
    {
        $this->read_at = null;

        $this->save();
    }

    public function scopeIsNotArchived($query)
    {
        return $query->whereNull('archived_at');
    }

    public function scopeIsNotSpam($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('is_spam');
        });
    }

    public function getAddressString()
    {
        $return_string = null;

        $return_string .= @$this->address1 . ', ';

        if (isset($this->address2) && $this->address2 > '') {
            $return_string .= $this->address2 . ', ';
        }

        $return_string .= @$this->city . ', ' . @$this->state . ', ' . @$this->zip;

        return $return_string;
    }

}
