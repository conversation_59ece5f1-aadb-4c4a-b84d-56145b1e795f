<?php

namespace App\Calendars\Services;

use App\Calendars\Event;
use App\Calendars\EventOccurrence;
use DateTimeZone;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CreateEventOccurrences
{
    protected $attributes          = [];
    protected $group;
    protected $event;
    protected $required_attributes = [];

    public function create($attributes = [])
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        foreach ($this->required_attributes as $key) {
            if (!Arr::exists($this->attributes, $key)) {
                Log::error('CreateEventOccurrences:create -- Required field is missing.', [
                    'field' => $key,
                ]);

                throw new \Exception('The required field "' . $key . '" was not provided or is missing.');
            }
        }

        // One to one fields from the event to the occurrences.
        $this->attributes['title']               = $this->event->title;
        $this->attributes['overview']            = $this->event->overview;
        $this->attributes['description']         = $this->event->description;
        $this->attributes['location']            = $this->event->location;
        $this->attributes['enable_responses']    = $this->event->enable_responses;
        $this->attributes['timezone']            = $this->event->timezone;
        $this->attributes['duration']            = $this->event->duration;
        $this->attributes['is_all_day']          = $this->event->is_all_day;
        $this->attributes['local_start_at_date'] = $this->event->local_start_at_date;
        $this->attributes['local_end_at_date']   = $this->event->local_end_at_date;
        $this->attributes['local_start_at_time'] = $this->event->local_start_at_time;
        $this->attributes['local_end_at_time']   = $this->event->local_end_at_time;
        $this->attributes['created_by_user_id']  = $this->event->created_by_user_id;
        $this->attributes['owner_user_id']       = $this->event->owner_user_id;

        $this->createOccurrences();

        return $this->event;
    }

    public function fromEvent(Event $event)
    {
        $this->event = $event;

        $this->attributes = [
            'calendar_event_id' => $event->id,
            'account_id'        => $event->account_id,
            'calendar_id'       => $event->calendar_id,
            'user_group_id'     => $event->user_group_id,
        ];

        return $this;
    }

    private function createOccurrences()
    {
        // RECURRING / MANY OCCURRENCES
        if ($this->event->is_recurring && $this->event->rrule):
            // First we get our start time, which is in UTC -- turn it BACK into our ACCOUNT TIMEZONE before we create occurrences.
            // We MUST create occurrences in the original timezone, otherwise it will do things like make Tues@10pm be Mon@10pm

            // Get START TIME in TIMEZONE
            $original_start_at = clone $this->event->start_at;
            $original_start_at->setTimezone($this->event->account->timezone);
            $original_start_hour_in_timezone   = $original_start_at->format('G');
            $original_start_minute_in_timezone = $original_start_at->format('i');
            // Get END TIME in TIMEZONE
            $original_end_at = clone $this->event->end_at;
            $original_end_at->setTimezone($this->event->account->timezone);
            $original_end_hour_in_timezone   = $original_end_at->format('G');
            $original_end_minute_in_timezone = $original_end_at->format('i');

            // Create our RRULE
            // We MUST say what our account timezone is here when creating occurrences!
            $rule = new \Recurr\Rule($this->event->rrule, $original_start_at, $original_end_at, $this->event->account->timezone);

            $transformer = new \Recurr\Transformer\ArrayTransformer();

            // See last day of month fix:  https://github.com/simshaun/recurr#warnings
            // This will make a recurring event on the 31st translate to "last day of each month" rather than strictly "only the 31st".
            // We assume there are hardly any cases where someone wants to skip months without a 31st.
            $transformerConfig = new \Recurr\Transformer\ArrayTransformerConfig();
            $transformerConfig->enableLastDayOfMonthFix();
            $transformer->setConfig($transformerConfig);

            try {
                foreach ($transformer->transform($rule) as $recurrence) {
                    // This occurrence is in the ACCOUNT TIMEZONE, that we set above when we generated occurrences.
                    $start = $recurrence->getStart();
                    $end   = $recurrence->getEnd();

                    // We need to force set the time, so tha the times remain the same across DST... then turn it back to UTC.
                    // Now, to save our DateTime, we want to put it in the database as UTC!

                    // Start time for this occurrence in UTC
                    $start->setTimezone(new DateTimeZone($this->event->account->timezone))
                        ->setTime($original_start_hour_in_timezone, $original_start_minute_in_timezone);

                    $start->setTimezone(new DateTimeZone('UTC'));
                    // End time for this occurrence in UTC
                    $end->setTimezone(new DateTimeZone($this->event->account->timezone))
                        ->setTime($original_end_hour_in_timezone, $original_end_minute_in_timezone);
                    $end->setTimezone(new DateTimeZone('UTC'));

                    EventOccurrence::create(
                        array_merge(
                            $this->attributes,
                            [
                                'uuid'                => Str::uuid(),
                                'start_at'            => $start,
                                'end_at'              => $end,
                                'start_at_date'       => $start,
                                'end_at_date'         => $end,
                                'start_at_time'       => $start,
                                'end_at_time'         => $end,
                                'is_hidden'           => $this->event->is_hidden,
                                'is_private'          => $this->event->is_private,
                                'is_group_only'       => $this->event->is_group_only,
                                'is_public'           => $this->event->is_public,
                                'local_start_at_time' => $this->event->local_start_at_time,
                                'local_end_at_time'   => $this->event->local_end_at_time,
                                'local_start_at_date' => $this->event->local_start_at_date,
                                'local_end_at_date'   => $this->event->local_end_at_date,
                            ]
                        )
                    );
                }
            } catch (\Exception $e) {
                throw new \Exception($e->getMessage());
            }
        // ONE OCCURRENCE ONLY
        else:
            try {
                EventOccurrence::create(
                    array_merge(
                        $this->attributes,
                        [
                            'uuid'                => Str::uuid(),
                            'start_at'            => $this->event->start_at,
                            'end_at'              => $this->event->end_at,
                            'start_at_date'       => $this->event->start_at_date,
                            'end_at_date'         => $this->event->end_at_date,
                            'start_at_time'       => $this->event->start_at_time,
                            'end_at_time'         => $this->event->end_at_time,
                            'is_hidden'           => $this->event->is_hidden,
                            'is_private'          => $this->event->is_private,
                            'is_group_only'       => $this->event->is_group_only,
                            'is_public'           => $this->event->is_public,
                            'local_start_at_time' => $this->event->local_start_at_time,
                            'local_end_at_time'   => $this->event->local_end_at_time,
                            'local_start_at_date' => $this->event->local_start_at_date,
                            'local_end_at_date'   => $this->event->local_end_at_date,
                        ]
                    )
                );
            } catch (\Exception $e) {
                throw new \Exception($e->getMessage());
            }
        endif;

        return true;
    }
}
