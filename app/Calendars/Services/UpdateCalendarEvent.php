<?php

namespace App\Calendars\Services;

use App\Calendars\Calendar;
use App\Calendars\Event;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UpdateCalendarEvent
{
    protected $attributes          = [];
    protected $event;
    protected $calendar;
    protected $required_attributes = [
        'account_id',
        'calendar_id',
        'title',
        'url_title',
        'timezone',
        'start_at',
        'end_at',
    ];

    public function __construct(Event $event)
    {
        $this->event = $event;
    }

    public function update($attributes): Event
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (!Arr::exists($this->attributes, 'url_title')) {
            $this->attributes['url_title'] = Str::slug($this->attributes['title']);
        }
        if (!Arr::exists($this->attributes, 'is_all_day') || empty($this->attributes['is_all_day'])) {
            $this->attributes['is_all_day'] = false;
        }

        $this->attributes['uuid'] = Str::uuid();

        foreach ($this->required_attributes as $key) {
            if (!Arr::exists($this->attributes, $key)) {
                Log::error('UpdateCalendarEvent:update -- Required field is missing.', [
                    'field' => $key,
                ]);

                throw new \Exception('The required field "' . $key . '" was not provided or is missing.');
            }
        }

        // Dates -- set these manually to trigger our setters.
        $this->event->start_at      = $this->attributes['start_at']->setTimezone('UTC');
        $this->event->end_at        = $this->attributes['end_at']->setTimezone('UTC');
        $this->event->start_at_date = $this->attributes['start_at']->setTimezone('UTC');
        $this->event->end_at_date   = $this->attributes['end_at']->setTimezone('UTC');
        $this->event->start_at_time = $this->attributes['start_at']->setTimezone('UTC');
        $this->event->end_at_time   = $this->attributes['end_at']->setTimezone('UTC');
        $this->event->update($this->attributes);
//        $this->event->save();

        // Delete all existing occurrences.
        $this->deleteEventOccurrences();

        // Create new occurrences.
        $this->createEventOccurrences();

        return $this->event;
    }

    public function forCalendar($calendar)
    {
        if ($calendar instanceof Calendar) {
            $this->calendar = $calendar;
        } else {
            $this->calendar = Calendar::find($calendar);
        }

        if (!$this->calendar instanceof Calendar) {
            throw new \Exception('Calendar not found.');
        }

        $this->attributes = [
            'calendar_id'   => $this->calendar->id,
            'account_id'    => $this->calendar->account_id,
            'user_group_id' => $this->calendar->user_group_id,
            'timezone'      => $this->calendar->account->timezone,
        ];

        return $this;
    }

    private function createEventOccurrences()
    {
        return (new CreateCalendarEventOccurrences())->forEvent($this->event)->create();
    }

    private function deleteEventOccurrences()
    {
        $this->event->occurrences()->delete();

        return true;
    }
}
