<?php

namespace App\Calendars\Services;

use App\Calendars\EventOccurrence;
use App\Users\User;
use Illuminate\Support\Arr;

class UpdateEventOccurrence
{
    protected $occurrence;
    protected $attributes          = [];
    protected $required_attributes = [
        'title',
        'start_at',
        'end_at',
        'calendar_id',
    ];
    protected $limited_attributes  = [
        'start_at',
        'start_at_date',
        'start_at_time',
        'end_at',
        'end_at_date',
        'end_at_time',
        'is_all_day',
        'title',
        'location',
        'overview',
        'description',
        'internal_notes',
        'timezone',
        'local_start_at_date',
        'local_end_at_date',
        'local_start_at_time',
        'local_end_at_time',
        'calendar_id',
        'is_occurrence_modified',
        'updated_by_user_id',
        'owner_user_id',
        'enable_responses',
        'show_responses',
    ];

    public function __construct(EventOccurrence $occurrence)
    {
        $this->occurrence = $occurrence;
    }

    public function update($attributes = []): EventOccurrence
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        // If no calendar id is provided, use the one from the occurrence.
        if (!Arr::get($this->attributes, 'calendar_id')) {
            $this->attributes['calendar_id'] = $this->occurrence->calendar_id;
        }

        foreach ($this->required_attributes as $key) {
            if (
                Arr::exists($this->attributes, $key) && empty($this->attributes[$key]) ||
                (!Arr::exists($this->attributes, $key) && !$this->occurrence->$key)
            ) {
                throw new \Exception('The required field "' . $key . '" was not provided or is missing.');
            }
        }

        // Double check our timezone.
        if ((!Arr::exists($this->attributes, 'timezone') || empty($this->attributes['timezone']))
            && !$this->occurrence->timezone) {
            $this->attributes['timezone'] = $this->occurrence->account->timezone;
        }

        // If this is an all day event, set the start and end times to the beginning and end of the day.
        $this->adjustIfIsAllDay();

        // Mark this occurrence as modified from all other occurrences.
        $this->attributes['is_occurrence_modified'] = now();

        $this->occurrence->update(
            collect($this->attributes)
                ->only($this->limited_attributes)
                ->all()
        );

        // If this is the only occurrence for the event, update the event as well.
        if ($this->occurrence->event->occurrences()->count() == 1) {
            $event = $this->occurrence->event;

            $event->calendar_id = $this->occurrence->calendar_id;
            $event->start_at    = $this->occurrence->start_at;
            $event->end_at      = $this->occurrence->end_at;
            $event->is_all_day  = $this->occurrence->is_all_day;
            $event->title       = $this->occurrence->title;
            $event->location    = $this->occurrence->location;
            $event->description = $this->occurrence->description;

            $event->save();
        }

        return $this->occurrence;
    }

    public function adjustIfIsAllDay()
    {
        $timezone = Arr::get($this->attributes, 'timezone') ?: $this->occurrence->timezone;

        if (Arr::exists($this->attributes, 'is_all_day') && $this->attributes['is_all_day']) {
            $this->setStartAt($this->attributes['start_at']->setTimezone($timezone)->startOfDay());
            $this->setEndAt($this->attributes['end_at']->setTimezone($timezone)->endOfDay());
        }

        return $this;
    }

    /**
     * Set the start date and time.
     */
    public function setStartAt($value_in_local_timezone): self
    {
        // We get this in the user's timezone, but we want to store it in UTC.
        $this->attributes['local_start_at_time'] = $value_in_local_timezone->format('H:i:s');
        $this->attributes['local_start_at_date'] = $value_in_local_timezone->format('Y-m-d');

        $start_at_in_utc = (clone $value_in_local_timezone)->setTimezone('UTC');

        $this->attributes['start_at']      = $start_at_in_utc;
        $this->attributes['start_at_date'] = $start_at_in_utc;
        $this->attributes['start_at_time'] = $start_at_in_utc;

        return $this;
    }

    public function updatedByUser(User|int|null $user): self
    {
        if ($user instanceof User) {
            $user_id = $user->id;
        } elseif (is_numeric($user) && $user > 0) {
            $user_id = $user;
        } else {
            return $this;
        }

        $this->attributes['updated_by_user_id'] = $user_id;

        return $this;
    }

    /**
     * Set the end date and time.
     */
    public function setEndAt($value_in_local_timezone): self
    {
        $this->attributes['local_end_at_time'] = $value_in_local_timezone->format('H:i:s');
        $this->attributes['local_end_at_date'] = $value_in_local_timezone->format('Y-m-d');

        $end_at_in_utc = (clone $value_in_local_timezone)->setTimezone('UTC');

        $this->attributes['end_at']      = $end_at_in_utc;
        $this->attributes['end_at_date'] = $end_at_in_utc;
        $this->attributes['end_at_time'] = $end_at_in_utc;

        return $this;
    }

    public function setTitle($value): self
    {
        $this->attributes['title'] = $value;
        return $this;
    }

    public function setLocation($value): self
    {
        $this->attributes['location'] = $value;
        return $this;
    }

    public function setDescription($value): self
    {
        $this->attributes['description'] = $value;
        return $this;
    }

    public function setIsAllDay($value): self
    {
        $this->attributes['is_all_day'] = (bool)$value;
        return $this;
    }

    public function setTimezone($value): self
    {
        $this->timezone = $value;
        return $this;
    }

    public function setEnableResponses($value): self
    {
        if ($value) {
            $this->attributes['enable_responses'] = $value ? 1 : 0;
        } else {
            $this->attributes['enable_responses'] = 0;
        }

        return $this;
    }

    public function setShowResponses($value): self
    {
        if ($value) {
            $this->attributes['show_responses'] = $value ? 1 : 0;
        } else {
            $this->attributes['show_responses'] = 0;
        }

        return $this;
    }

}
