<?php

namespace App\Calendars\Services;

use App\Calendars\EventOccurrence;

class UpdateEventOccurrenceResponses
{
    protected $owner_user_id   = [];
    protected $remove_user_ids = [];
    protected $update_user_ids = [];
    protected $remove_names    = [];
    protected $update_names    = [];
    protected $occurrence;

    public function __construct(EventOccurrence $occurrence)
    {
        $this->occurrence = $occurrence;
    }

    public function update(): EventOccurrence
    {
        // Go through each response with user_ids and update it.
        foreach ($this->update_user_ids as $user_array) {
            // Find existing response that matches this user, account, and calendar.
            $existing = $this->occurrence->responses()->where(function ($query) use ($user_array) {
                $query->where('user_id', $user_array[0])
                    ->where('account_id', $this->occurrence->account_id)
                    ->where('calendar_id', $this->occurrence->calendar_id);
            })->first();

            // Delete existing response, only if it's NOT the same as this new response.
            if ($existing && !( // NOT the same as this new response.
                    ($existing->is_going && ($user_array[1] == 'is_going')) ||
                    ($existing->is_not_going && ($user_array[1] == 'is_not_going')) ||
                    ($existing->is_maybe && ($user_array[1] == 'is_maybe'))
                )) {
                $existing->delete();
            }

            // We'll never update because of the new check above.
            $this->occurrence->responses()->updateOrCreate([
                'user_id'     => $user_array[0],
                'account_id'  => $this->occurrence->account_id,
                'calendar_id' => $this->occurrence->calendar_id,
            ], [
                'owner_user_id' => $this->owner_user_id,
                'is_going'      => $user_array[1] == 'is_going' ? 1 : 0,
                'is_not_going'  => $user_array[1] == 'is_not_going' ? 1 : 0,
                'is_maybe'      => $user_array[1] == 'is_maybe' ? 1 : 0,
                'notes'         => $user_array[2],
            ]);
        }

        // Go through each response with names and update it.
        foreach ($this->update_names as $user_array) {
            $existing = $this->occurrence->responses()->where(function ($query) use ($user_array) {
                $query->where('name', $user_array[0])
                    ->where('account_id', $this->occurrence->account_id)
                    ->where('calendar_id', $this->occurrence->calendar_id);
            })->first();

            // Delete existing response, only if it's NOT the same as this new response.
            if ($existing && !( // NOT the same as this new response.
                    ($existing->is_going && ($user_array[1] == 'is_going')) ||
                    ($existing->is_not_going && ($user_array[1] == 'is_not_going')) ||
                    ($existing->is_maybe && ($user_array[1] == 'is_maybe'))
                )) {
                $existing->delete();
            }

            // We'll never update because of the new check above.
            $this->occurrence->responses()->updateOrCreate([
                'name'        => $user_array[0],
                'account_id'  => $this->occurrence->account_id,
                'calendar_id' => $this->occurrence->calendar_id,
            ], [
                'owner_user_id' => $this->owner_user_id,
                'is_going'      => $user_array[1] == 'is_going' ? 1 : 0,
                'is_not_going'  => $user_array[1] == 'is_not_going' ? 1 : 0,
                'is_maybe'      => $user_array[1] == 'is_maybe' ? 1 : 0,
                'notes'         => $user_array[2],
            ]);
        }

        // Remove responses by user_id.
        foreach ($this->remove_user_ids as $user_id) {
            $this->occurrence->responses()->where('user_id', $user_id)->delete();
        }
        // Remove responses by name.
        foreach ($this->remove_names as $name) {
            $this->occurrence->responses()->where('name', $name)->delete();
        }

        return $this->occurrence;
    }

    public function createdBy($user_id): self
    {
        $this->owner_user_id = $user_id;

        return $this;
    }

    public function updateByUserId($user_id, $response = null, $notes = null): self
    {
        $this->update_user_ids[] = [$user_id, $response, $notes];

        return $this;
    }

    public function updateByName($name, $response = null, $notes = null): self
    {
        $this->update_names[] = [$name, $response, $notes];

        return $this;
    }

    public function removeByUserId($user_id): self
    {
        $this->remove_user_ids[] = $user_id;

        return $this;
    }

    public function removeByName($name): self
    {
        $this->remove_names[] = $name;

        return $this;
    }
}
