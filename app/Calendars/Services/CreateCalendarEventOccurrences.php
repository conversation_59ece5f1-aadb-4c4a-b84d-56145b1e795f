<?php

namespace App\Calendars\Services;

use App\Calendars\Event;
use App\Calendars\EventOccurrence;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class CreateCalendarEventOccurrences
{
    protected $attributes          = [];
    protected $group;
    protected $event;
    protected $required_attributes = [];

    public function create($attributes = [])
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        foreach ($this->required_attributes as $key) {
            if (!Arr::exists($this->attributes, $key)) {
                Log::error('CreateCalendarEventOccurrences:create -- Required field is missing.', [
                    'field' => $key,
                ]);

                throw new \Exception('The required field "' . $key . '" was not provided or is missing.');
            }
        }

        $this->createOccurrences();

        return $this->event;
    }

    public function forEvent(Event $event)
    {
        $this->event = $event;

        $this->attributes = [
            'calendar_event_id'   => $event->id,
            'account_id'          => $event->account_id,
            'account_location_id' => $event->account->location ? $event->account->location->id : null,
            'calendar_id'         => $event->calendar_id,
            'user_group_id'       => $event->user_group_id,
        ];

        return $this;
    }

    private function createOccurrences()
    {
        if ($this->event->is_recurring) {
        } else {
            try {
                EventOccurrence::create(
                    array_merge(
                        $this->attributes,
                        [
                            'start_at'      => $this->event->start_at,
                            'end_at'        => $this->event->end_at,
                            'start_at_date' => $this->event->start_at_date,
                            'end_at_date'   => $this->event->end_at_date,
                            'start_at_time' => $this->event->start_at_time,
                            'end_at_time'   => $this->event->end_at_time,
                            'is_hidden'     => $this->event->is_hidden,
                            'is_private'    => $this->event->is_private,
                            'is_group_only' => $this->event->is_group_only,
                            'is_public'     => $this->event->is_public,
                        ]
                    )
                );
            } catch (\Exception $e) {
                throw new \Exception($e->getMessage());
            }
        }
    }
}
