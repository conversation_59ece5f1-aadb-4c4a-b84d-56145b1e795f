<?php

namespace App\Calendars\Services;

use App\Calendars\Calendar;
use App\Calendars\EventOccurrence;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Str;

class CalendarFeed
{
    protected $user      = null;
    protected $calendar  = null;
    protected $file_name = 'Lightpost-Calendar-Feed';

    protected static $ICAL_FORMAT         = 'Ymd\THis\Z';
    protected static $ICAL_ALL_DAY_FORMAT = 'Ymd';
    protected static $NEW_LINE            = "\r\n";

    public function getHeadersArray()
    {
        return [
            'Content-Type'        => 'text/calendar; charset=utf-8',
            'Content-Disposition' => 'attachment; filename=' . $this->file_name . '.ics',
            'X-Content-Source'    => 'Lightpost',
        ];
    }

    public function getFeedUrl()
    {
        if ($this->calendar) {
            $this->file_name = Str::kebab($this->calendar->name);

            return route('api.public.calendars.feed.calendar', [
                'calendar'  => $this->calendar->ulid,
                'token'     => $this->user->getPublicToken(),
                'feed_name' => str_replace('.ics', '', $this->file_name) . '.ics', // Only have one ".ics" in the file name
            ]);
        } else {
            return route('api.public.calendars.feed.user', [
                'token'     => $this->user->getPublicToken(),
                'feed_name' => str_replace('.ics', '', $this->file_name) . '.ics', // Only have one ".ics" in the file name
            ]);
        }
    }

    public function getiCalFeed()
    {
        $feed_string = 'BEGIN:VCALENDAR' . self::$NEW_LINE;
        $feed_string .= 'VERSION:2.0' . self::$NEW_LINE;
//        $feed_string .= 'METHOD:PUBLISH' . self::$NEW_LINE;
        $feed_string .= 'PRODID:Lightpost/1.0' . self::$NEW_LINE;
        $feed_string .= 'CALSCALE:GREGORIAN' . self::$NEW_LINE;
        $feed_string .= 'NAME:' . ($this->calendar ? $this->calendar->name : $this->file_name) . self::$NEW_LINE;

        if ($this->calendar) {
            $calendar_ids = [$this->calendar->id];
        } else {
            $calendar_ids = Calendar::visibleTo($this->user)
                ->notHidden()
                ->get()
                ->pluck('id');
        }

        $occurrences = EventOccurrence::visibleTo($this->user)
            ->whereIn('calendar_id', $calendar_ids)
            ->where('start_at', '>=', now()->subMonths(2))
            ->where('end_at', '<=', now()->addMonths(24))
            ->orderBy('start_at', 'ASC')
            ->select([
                'id',
                'created_at',
                'updated_at',
                'start_at',
                'end_at',
                'start_at_date',
                'end_at_date',
                'start_at_time',
                'end_at_time',
                'account_id',
                'calendar_id',
                'calendar_event_id',
                'user_group_id',
                'title',
                'location',
                'description',
                'is_all_day',
                'is_occurrence_modified',
                'overview',
                'is_hidden',
                'is_group_only',
            ])
            ->with([
                'event:id,uuid,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
            ])
            ->get();

        foreach ($occurrences as $occurrence):
            $feed_string .= 'BEGIN:VEVENT' . self::$NEW_LINE;
            $feed_string .= 'DTSTAMP:' . $occurrence->created_at->format(self::$ICAL_FORMAT) . self::$NEW_LINE;
            $feed_string .= 'LAST-MODIFIED:' . $occurrence->updated_at?->format(self::$ICAL_FORMAT) . self::$NEW_LINE;
            $feed_string .= 'UID:' . md5($occurrence->id) . self::$NEW_LINE;

            if ($occurrence->is_all_day) {
                $feed_string .= 'DTSTART;VALUE=DATE:' . $occurrence->start_at->format(self::$ICAL_ALL_DAY_FORMAT) . self::$NEW_LINE;
                $feed_string .= 'DTEND;VALUE=DATE:' . $occurrence->end_at->addDay()->format(self::$ICAL_ALL_DAY_FORMAT) . self::$NEW_LINE;
//                $feed_string .= 'DURATION:' . 'dur-day' . self::$NEW_LINE;
            } else {
                $feed_string .= 'DTSTART:' . $occurrence->start_at->format(self::$ICAL_FORMAT) . self::$NEW_LINE;
                $feed_string .= 'DTEND:' . $occurrence->end_at->format(self::$ICAL_FORMAT) . self::$NEW_LINE;
            }

            // ICAL format requires that a new line to be carried over needs to be a LITERAL "\n" -- so we convert that here.
            // See: https://stackoverflow.com/questions/666929/encoding-newlines-in-ical-files
            if ($occurrence->location) {
                $feed_string .= trim(chunk_split('LOCATION:' . str_replace("\r\n", '\n ', $occurrence->location), 70, "\r\n "), ' ');
            }
            if ($occurrence->title) {
                $feed_string .= trim(chunk_split('SUMMARY:' . str_replace("\r\n", '\n ', $occurrence->title), 70, "\r\n "), ' ');
            }
            if ($occurrence->description) {
                $feed_string .= trim(chunk_split('DESCRIPTION:' . str_replace(["\n", "\r", "\r\n"], '\n', $occurrence->description), 70, "\r\n "), ' ');
            }
            $feed_string .= 'END:VEVENT' . self::$NEW_LINE;

        endforeach;

        $feed_string .= 'END:VCALENDAR';

        return $feed_string;
    }

    public function getFullCalendarFeed($start_at = null, $end_at = null)
    {
        if (!$this->calendar) {
            throw new \Exception('No calendar selected.');
        }

        $events = [];

        $occurrences = $this->calendar
            ->eventOccurrences()
            ->when($start_at, function ($query) use ($start_at) {
                $query->where('calendar_event_occurrences.start_at', '>=', Carbon::parse($start_at));
            })
            ->when($end_at, function ($query) use ($end_at) {
                $query->where('calendar_event_occurrences.end_at', '<=', Carbon::parse($end_at));
            })
            ->with('event')
            ->get();

        foreach ($occurrences as $occurrence) {
            $events[] = [
                'id'            => $occurrence->id,
                'groupId'       => $occurrence->calendar_event_id,
                'allDay'        => $occurrence->is_all_day ? true : false,
                'start'         => $occurrence->start_at->format('c'),
                'end'           => $occurrence->is_all_day ? $occurrence->end_at->addDay(1)->format('c') : $occurrence->end_at->format('c'),
                'title'         => $occurrence->title,
                'url'           => null,
                'classNames'    => null,
                'extendedProps' => [
                    'isRecurring' => $occurrence->event->is_recurring,
                ],
                //                'editable'         => 'false',
                //                'startEditable'    => 'false',
                //                'durationEditable' => 'false',
                //                'resourceEditable' => 'false',
                //                'rendering'        => $occurrence->,
                //                'overlap'          => $occurrence->,
                //                'constraint'       => $occurrence->,
                //                'backgroundColor'  => $occurrence->,
                //                'borderColor'      => $occurrence->,
                //                'textColor'        => $occurrence->,
                //                'extendedProps'    => $occurrence->,
            ];
        }

        return $events;
    }

    public function forUser(User $user)
    {
        $this->user = $user;

        return $this;
    }

    public function forCalendar(Calendar $calendar)
    {
        $this->calendar = $calendar;

        return $this;
    }

    public function setFileName($file_name)
    {
        $this->file_name = $file_name;

        return $this;
    }
}
