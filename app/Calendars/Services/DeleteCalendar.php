<?php

namespace App\Calendars\Services;

use App\Calendars\Calendar;

class DeleteCalendar
{
    protected $calendar;

    public function __construct(Calendar $calendar)
    {
        $this->calendar = $calendar;
    }

    public function delete()
    {
        foreach ($this->calendar->events as $event) {
            $event->occurrences()->delete();
        }

        $this->calendar->events()->delete();

        $this->calendar->delete();

        return true;
    }
}
