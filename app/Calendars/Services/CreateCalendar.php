<?php

namespace App\Calendars\Services;

use App\Accounts\Account;
use App\Calendars\Calendar;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CreateCalendar
{
    protected $attributes          = [];
    protected $calendar;
    protected $required_attributes = [
        'account_id',
        'name',
        'url_name',
    ];
    protected $must_be_bool        = [
        'is_hidden'     => false,
        'is_private'    => false,
        'is_group_only' => false,
        'is_public'     => false,
        'auto_show'     => false,
    ];

    public function create($attributes)
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (!Arr::exists($this->attributes, 'url_name')) {
            $this->attributes['url_name'] = Str::slug($this->attributes['name']);
        }

        if (!Arr::exists($this->attributes, 'user_group_id')) {
            $this->attributes['is_group_only'] = true;
        }

        $this->attributes['uuid'] = Str::uuid();

        foreach ($this->required_attributes as $key) {
            if (!Arr::exists($this->attributes, $key)) {
                Log::error('CreateCalendar:create -- Required field is missing.', [
                    'field' => $key,
                ]);

                throw new \Exception('The required field "' . $key . '" was not provided or is missing.');
            }
        }

        foreach ($this->must_be_bool as $key => $value) {
            if (!Arr::exists($this->attributes, $key)) {
                $this->attributes[$key] = $value;
            }
        }

        $this->calendar = Calendar::create($this->attributes);

        $this->calendar->generateUlid();

        return $this->calendar;
    }

    public function forAccount(Account $account)
    {
        $this->attributes['account_id'] = $account->id;

        return $this;
    }

    public function forGroup($user_group_id)
    {
        if (!empty($user_group_id) && is_numeric($user_group_id)) {
            $this->attributes['user_group_id'] = $user_group_id;
        }

        return $this;
    }

    public function withUuid($uuid = null)
    {
        $this->attributes['uuid'] = $uuid ?: Str::uuid();

        return $this;
    }
}
