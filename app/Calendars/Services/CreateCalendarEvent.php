<?php

namespace App\Calendars\Services;

use App\Calendars\Calendar;
use App\Calendars\Event;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CreateCalendarEvent
{
    protected $attributes          = [];
    protected $event;
    protected $calendar;
    protected $required_attributes = [
        'account_id',
        'calendar_id',
        'title',
        'url_title',
        'timezone',
        'start_at',
        'end_at',
    ];

    public function create($attributes): Event
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (!Arr::exists($this->attributes, 'url_title')) {
            $this->attributes['url_title'] = Str::slug($this->attributes['title']);
        }
        if (!Arr::exists($this->attributes, 'is_all_day') || empty($this->attributes['is_all_day'])) {
            $this->attributes['is_all_day'] = false;
        }

        $this->attributes['uuid'] = Str::uuid();

        foreach ($this->required_attributes as $key) {
            if (!Arr::exists($this->attributes, $key)) {
                Log::error('CreateCalendarEvent:create -- Required field is missing.', [
                    'field' => $key,
                ]);

                throw new \Exception('The required field "' . $key . '" was not provided or is missing.');
            }
        }

        $this->event = Event::create($this->attributes);

        // Dates -- set these manually to trigger our setters.
//        $this->event->start_at      = Carbon::parse($this->attributes['start_at'])->setTimezone($this->calendar->account->timezone);
//        $this->event->end_at        = Carbon::parse($this->attributes['end_at'])->setTimezone($this->calendar->account->timezone);
//        $this->event->start_at_date = Carbon::parse($this->attributes['start_at'])->setTimezone($this->calendar->account->timezone);
//        $this->event->end_at_date   = Carbon::parse($this->attributes['end_at'])->setTimezone($this->calendar->account->timezone);
//        $this->event->start_at_time = Carbon::parse($this->attributes['start_at'])->setTimezone($this->calendar->account->timezone);
//        $this->event->end_at_time   = Carbon::parse($this->attributes['end_at'])->setTimezone($this->calendar->account->timezone);

        $this->createEventOccurrences();

        return $this->event;
    }

    public function forCalendar($calendar)
    {
        if ($calendar instanceof Calendar) {
            $this->calendar = $calendar;
        } else {
            $this->calendar = Calendar::find($calendar);
        }

        if (!$this->calendar instanceof Calendar) {
            throw new \Exception('Calendar not found.');
        }

        $this->attributes = [
            'calendar_id'   => $this->calendar->id,
            'account_id'    => $this->calendar->account_id,
            'user_group_id' => $this->calendar->user_group_id,
            'timezone'      => $this->calendar->account->timezone,
        ];

        // For now, if this is a group calendar, events are only for that group.
        if ($this->calendar->user_group_id > 0) {
            $this->attributes['is_group_only'] = true;
        }

        return $this;
    }

    private function createEventOccurrences()
    {
        return (new CreateCalendarEventOccurrences())->forEvent($this->event)->create();
    }
}
