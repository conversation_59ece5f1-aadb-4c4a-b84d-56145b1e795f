<?php

namespace App\Calendars\Policies;

use App\Calendars\Calendar;
use App\Users\User;

class CalendarPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.calendars')) {
            return false;
        }
    }

    public function manage(User $user, Calendar $calendar = null)
    {
        return $user->hasPermission('calendars.manage');
    }

    public function create(User $user)
    {
        return $user->hasPermission('calendars.manage');
    }

    public function memberIndex(User $user)
    {
        return $user->hasPermission('calendars.index') || $user->isMember();
    }

    public function index(User $user)
    {
        return $user->hasPermission('calendars.index');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('calendars.index');
    }

    public function view(User $user, Calendar $calendar)
    {
        return $user->hasPermission('calendars.index');
    }

    public function edit(User $user, Calendar $calendar)
    {
        return $user->hasPermission('calendars.manage');
    }

    public function save(User $user, Calendar $calendar)
    {
        return $user->hasPermission('calendars.manage');
    }

    public function delete(User $user, Calendar $calendar)
    {
        return $user->hasPermission('calendars.manage');
    }

    public function assign(User $user, Calendar $calendar)
    {
        return $user->hasPermission('calendars.manage');
    }

    public function unassign(User $user, Calendar $calendar)
    {
        return $user->hasPermission('calendars.manage');
    }
}
