<?php

namespace App\Calendars\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Calendars\Calendar;
use App\Calendars\Services\CalendarFeed;
use App\Calendars\Services\CreateCalendar;
use App\Calendars\Services\DeleteCalendar;
use App\Calendars\Services\UpdateCalendar;
use App\Users\Group;
use Illuminate\Support\Facades\Auth;
use Sabre\VObject\Reader;

class CalendarController extends Controller
{

    public function index()
    {
        return view('admin.calendars.index');
    }

    // Calendar is determined by HashID, not primary key ID.
    public function view($calendar_ulid)
    {
        $calendar = Calendar::visibleToAccount(auth()->user())
            ->withUlid($calendar_ulid)
            ->first();

        if (!$calendar) {
            abort(404);
        }

        $events = (new CalendarFeed())
            ->forUser(auth()->user())
            ->forCalendar($calendar)
            ->getFullCalendarFeed();

        return response()->json($events);
    }

    public function create()
    {
        return view('admin.calendars.modals.calendar-create')
            ->with('groups', Group::visibleTo(Auth::user())->get());
    }

    public function save()
    {
        try {
            (new CreateCalendar())
                ->forAccount(Auth::user()->account)
                ->forGroup(request('user_group_id'))
                ->create([
                    'name'             => request()->input('name'),
                    'background_color' => request()->input('background_color'),
                    'text_color'       => request()->input('text_color'),
                    'is_public'        => request()->input('is_public', false),
                    'is_hidden'        => request()->input('is_hidden', false),
                ]);
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.calendars.index')
                ->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.calendars.index')
            ->with('message.success', 'Calendar created successfully.');
    }

    public function edit(Calendar $calendar)
    {
        return view('admin.calendars.modals.calendar-edit')
            ->with('groups', Group::visibleTo(Auth::user())->get())
            ->with('calendar', $calendar);
    }

    public function update(Calendar $calendar)
    {
        try {
            (new UpdateCalendar($calendar))
                ->forGroup(request('user_group_id'))
                ->update([
                    'name'             => request()->input('name'),
                    'background_color' => request()->input('background_color'),
                    'text_color'       => request()->input('text_color'),
                    'is_public'        => request()->input('is_public'),
                    'is_hidden'        => request()->input('is_hidden'),
                ]);
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.calendars.index')
                ->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.calendars.index')
            ->with('message.success', 'Calendar updated successfully.');
    }

    public function destroy(Calendar $calendar)
    {
        try {
            (new DeleteCalendar($calendar))->delete();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.calendars.index')
                ->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.calendars.index')
            ->with('message.success', 'Calendar and all events deleted successfully.');
    }

    public function publicCalendarSnippet()
    {
        return view('admin.calendars.modals.public-calendar-snippet');
    }

    public function googleCalendarTest()
    {
        $test      = 'GOOGLE_CAL_ICS_LINK';
        $vcalendar = Reader::read(
            fopen($test, 'r')
        );

        dd($vcalendar);
    }
}
