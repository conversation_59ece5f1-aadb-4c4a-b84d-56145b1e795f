<?php

namespace App\Calendars\Requests;

use App\Base\Http\Request;

class CreateCalendarEventRequest extends Request
{
    public function rules()
    {
        $rules = [
            'title'         => 'required|string',
            'calendar_id'   => 'required|integer',
            'start_at_date' => 'required|date',
            'start_at_time' => 'string|max:24',
            'start_at_ampm' => 'string|max:24',
            'end_at_date'   => 'required|date',
            'end_at_time'   => 'string|max:3',
            'end_at_ampm'   => 'string|max:3',
            'description'   => 'nullable|string|max:2046',
            'location'      => 'nullable|string|max:256',
        ];

        return $rules;
    }
}
