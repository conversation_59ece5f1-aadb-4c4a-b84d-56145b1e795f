<?php

namespace App\Calendars\Requests;

use App\Base\Http\Request;

class UpdateEventRequest extends Request
{
    public function rules()
    {
        $rules = [
            'title'         => 'required|string',
            'calendar_id'   => 'sometimes|integer',
            'start_at_date' => 'sometimes|date',
            'start_at_time' => 'sometimes|string|max:24',
            'start_at_ampm' => 'sometimes|string|max:24',
            'end_at_date'   => 'sometimes|date',
            'end_at_time'   => 'sometimes|string|max:3',
            'end_at_ampm'   => 'sometimes|string|max:3',
            'description'   => 'nullable|string|max:2046',
            'overview'      => 'nullable|string|max:2046',
            'location'      => 'nullable|string|max:256',
        ];

        return $rules;
    }
}
