<?php

namespace App\Calendars;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventOccurrenceResponse extends Model
{
    use SoftDeletes;

    protected $table = 'calendar_event_occurrence_responses';

    protected $casts = [
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'deleted_at'   => 'datetime',
        'is_going'     => 'boolean',
        'is_not_going' => 'boolean',
        'is_maybe'     => 'boolean',
        'uuid'         => 'string',
    ];

    protected $fillable = [
        'account_id',
        'calendar_id',
        'calendar_event_id',
        'calendar_event_occurrence_id',
        'created_by_user_id',
        'owner_user_id',
        'user_id',
        'parent_id',
        'name',
        'email',
        'notes',
        'admin_notes',
        'is_going',
        'is_not_going',
        'is_maybe',
        'status',
        'uuid',
        'created_by_user_id',
        'updated_by_user_id',
    ];

    const STATUS_PENDING   = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_CANCELLED = 'cancelled';

    const RESPONSE_GOING     = 'going';
    const RESPONSE_NOT_GOING = 'not_going';
    const RESPONSE_MAYBE     = 'maybe';

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where(self::$table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function calendar()
    {
        return $this->belongsTo(Calendar::class);
    }

    public function event()
    {
        return $this->belongsTo(Event::class, 'calendar_event_id');
    }

    public function eventOccurrence()
    {
        return $this->belongsTo(EventOccurrence::class, 'calendar_event_occurrence_id');
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    public function scopeForUser($query, $user)
    {
        return $query->where('user_id', $user->id);
    }

    public function scopeForName($query, $name)
    {
        return $query->where('name', $name);
    }

    public function scopeIsGoing($query)
    {
        return $query->where('is_going', true);
    }

    public function scopeIsNotGoing($query)
    {
        return $query->where('is_not_going', true);
    }

    public function scopeIsMaybe($query)
    {
        return $query->where('is_maybe', true);
    }

    public function parent()
    {
        return $this->belongsTo(EventOccurrenceResponse::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(EventOccurrenceResponse::class, 'parent_id');
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', self::STATUS_CONFIRMED);
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    public function scopeResponseGoing($query)
    {
        return $query->where('is_going', true);
    }

    public function scopeResponseNotGoing($query)
    {
        return $query->where('is_not_going', true);
    }

    public function scopeResponseMaybe($query)
    {
        return $query->where('is_maybe', true);
    }

    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isConfirmed(): bool
    {
        return $this->status === self::STATUS_CONFIRMED;
    }

    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    public function hasParent(): bool
    {
        return !is_null($this->parent_id);
    }

    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    public function hasEmail(): bool
    {
        return !empty($this->email);
    }

    public function hasAdminNotes(): bool
    {
        return !empty($this->admin_notes);
    }

    public function setResponse(string $response)
    {
        $this->response = $response;

        $this->is_going     = $response === self::RESPONSE_YES;
        $this->is_not_going = $response === self::RESPONSE_NO;
        $this->is_maybe     = $response === self::RESPONSE_MAYBE;

        return $this;
    }

    public function setStatus(string $status)
    {
        if (!in_array($status, [self::STATUS_PENDING, self::STATUS_CONFIRMED, self::STATUS_CANCELLED])) {
            throw new \InvalidArgumentException('Invalid status');
        }

        $this->status = $status;
        return $this;
    }
}
