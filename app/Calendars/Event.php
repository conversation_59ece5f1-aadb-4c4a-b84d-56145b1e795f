<?php

namespace App\Calendars;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use App\Base\Models\Model;
use App\Calendars\Scopes\EventVisibleToScope;
use App\Users\Group;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Recurr\Transformer\TextTransformer;
use Spatie\CalendarLinks\Link;

class Event extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'calendar_events';

    protected $casts = [
        'created_at'                  => 'datetime',
        'updated_at'                  => 'datetime',
        'deleted_at'                  => 'datetime',
        'start_at'                    => 'datetime',
        'end_at'                      => 'datetime',
        'start_at_date'               => 'date',
        'end_at_date'                 => 'date',
        'is_all_day'                  => 'boolean',
        'is_recurring'                => 'boolean',
        'enable_responses'            => 'boolean',
        'enable_payments'             => 'boolean',
        'remove_event_after_end_date' => 'boolean',
        'local_start_at_date'         => 'date:Y-m-d',
        'local_end_at_date'           => 'date:Y-m-d',
        'local_start_at_time'         => 'string',
        'local_end_at_time'           => 'string',
    ];

    protected $fillable = [
        'uuid',
        'created_by_user_id',
        'account_id',
        'calendar_id',
        'user_group_id',
        'title',
        'url_title',
        'location',
        'overview',
        'description',
        'is_hidden',
        'is_private',
        'is_group_only',
        'is_group_only',
        'is_public',
        'timezone',
        'start_at',
        'end_at',
        'start_at_date',
        'end_at_date',
        'start_at_time',
        'end_at_time',
        'duration',
        'days_of_week',
        'is_all_day',
        'is_recurring',
        'enable_responses',
        'enable_payments',
        'remove_event_after_end_date',
        'rrule',
        'recur_frequency',
        'recur_end_at',
        'recur_interval',
        'recur_count',
        'recur_week_start_day',
        'recur_by_day',
        'recur_by_month',
        'recur_by_week_number',
        'recur_by_day_of_year',
        'recur_by_day_of_month',
        'recur_by_set_position',
        'recur_by_hour',
        'recur_by_minute',
        'recur_by_second',
        'local_start_at_date',
        'local_end_at_date',
        'local_start_at_time',
        'local_end_at_time',
        'updated_by_user_id',
        'owner_user_id',
    ];

    public static $days_of_week = [
        7 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    // See also \Recurr\Frequency::DAILY, etc
    public static $recur_frequencies = [
        'DAILY',
        'WEEKLY',
        'MONTHLY',
        'YEARLY',
    ];

    public static $recur_step_positions = [
        1  => 'First',
        2  => 'Second',
        3  => 'Third',
        4  => 'Fourth',
        -1 => 'Last',
    ];

    public static $recur_days = [
        'SU' => 'Sunday',
        'MO' => 'Monday',
        'TU' => 'Tuesday',
        'WE' => 'Wednesday',
        'TH' => 'Thursday',
        'FR' => 'Friday',
        'SA' => 'Saturday',
    ];

    public static $recur_by_day = [
        'SU'                   => 'Sunday',
        'MO'                   => 'Monday',
        'TU'                   => 'Tuesday',
        'WE'                   => 'Wednesday',
        'TH'                   => 'Thursday',
        'FR'                   => 'Friday',
        'SA'                   => 'Saturday',
        'SU,MO,TU,WE,TH,FR,SA' => 'Day',
        'MO,TU,WE,TH,FR'       => 'Weekday',
        'SU,SA'                => 'Weekend Day',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new EventVisibleToScope())->getQuery($query, $user);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function accountLocation()
    {
        return $this->belongsTo(AccountLocation::class);
    }

    public function calendar()
    {
        return $this->belongsTo(Calendar::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'user_group_id');
    }

    public function occurrences()
    {
        return $this->hasMany(EventOccurrence::class, 'calendar_event_id');
    }

    public function isRecurring()
    {
        return $this->is_recurring;
    }

    public function getHumanReadableRRule()
    {
        $rule = (new \Recurr\Rule)
            ->loadFromString($this->rrule)
            ->setTimezone($this->account->timezone)
            ->setStartDate($this->start_at->setTimeZone($this->account->timezone))
            ->setEndDate($this->end_at->setTimeZone($this->account->timezone));

        if ($rule) {
            $textTransformer = new TextTransformer();

            return $textTransformer->transform($rule);
        }
    }

    public function getCalendarLink($type = null)
    {
        $link = Link::create($this->title, $this->start_at, $this->end_at, $this->is_all_day)
            ->description($this->description ?: '')
            ->address($this->location ?: '');

        if ($type == 'google') {
            return $link->google();
        } elseif ($type == 'yahoo') {
            return $link->yahoo();
        } elseif ($type == 'outlookcom') {
            return $link->webOutlook();
        } elseif ($type == 'office') {
            return $link->webOffice();
        } elseif ($type == 'ics') {
            return $link->ics();
        } else {
            return $link->ics();
        }
    }

    public function setStartAtAttribute($value)
    {
        if ($value instanceof Carbon) {
            $this->attributes['start_at'] = $value->setTimezone('UTC');
        } elseif ($value instanceof \DateTime) {
            $this->attributes['start_at'] = $value->setTimezone(new \DateTimeZone('UTC'));
        }
    }

    public function setEndAtAttribute($value)
    {
        if ($value instanceof Carbon) {
            $this->attributes['end_at'] = $value->setTimezone('UTC');
        } elseif ($value instanceof \DateTime) {
            $this->attributes['end_at'] = $value->setTimezone(new \DateTimeZone('UTC'));
        }
    }

    public function setStartAtDateAttribute($value)
    {
        if ($value instanceof Carbon) {
            $this->attributes['start_at_date'] = $value->setTimezone('UTC');
        } elseif ($value instanceof \DateTime) {
            $this->attributes['start_at_date'] = $value->setTimezone(new \DateTimeZone('UTC'));
        }
    }

    public function setEndAtDateAttribute($value)
    {
        if ($value instanceof Carbon) {
            $this->attributes['end_at_date'] = $value->setTimezone('UTC');
        } elseif ($value instanceof \DateTime) {
            $this->attributes['end_at_date'] = $value->setTimezone(new \DateTimeZone('UTC'));
        }
    }

    public function setStartAtTimeAttribute($value)
    {
        if ($value instanceof Carbon) {
            $this->attributes['start_at_time'] = $value->setTimezone('UTC');
        } elseif ($value instanceof \DateTime) {
            $this->attributes['start_at_time'] = $value->setTimezone(new \DateTimeZone('UTC'));
        }
    }

    public function setEndAtTimeAttribute($value)
    {
        if ($value instanceof Carbon) {
            $this->attributes['end_at_time'] = $value->setTimezone('UTC');
        } elseif ($value instanceof \DateTime) {
            $this->attributes['end_at_time'] = $value->setTimezone(new \DateTimeZone('UTC'));
        }
    }
}
