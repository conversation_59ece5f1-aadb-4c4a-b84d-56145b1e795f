<?php

namespace App\Calendars;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use App\Base\Models\Model;
use App\Calendars\Scopes\CalendarVisibleToScope;
use App\Users\Group;
use App\Users\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Calendar extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'calendars';

    protected $casts = [
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'deleted_at'    => 'datetime',
        'is_hidden'     => 'boolean',
        'is_private'    => 'boolean',
        'is_group_only' => 'boolean',
        'is_public'     => 'boolean',
        'auto_show'     => 'boolean',
    ];

    protected $fillable = [
        'uuid',
        'account_id',
        'account_location_id',
        'user_group_id',
        'name',
        'url_name',
        'color',
        'background_color',
        'border_color',
        'text_color',
        'is_hidden',
        'is_private',
        'is_group_only',
        'is_public',
        'auto_show',
    ];

    // We MUST keep and use the ULID for legacy reasons - this is used for the public calendar URLs that people are subscribed to already!
    // This was originally HashIDs, but the library we were using was not maintained and would not work with Laravel 11.
    // We saved those HashIDs as ULIDs in the database, so we need to keep them.
    // We would otherwise use UUIDs.
    protected $guarded = [
        'ulid',
    ];

    static public $days_of_week = [
        7 => 'Sunday',
        1 => 'Monday',
        2 => 'Tuesday',
        3 => 'Wednesday',
        4 => 'Thursday',
        5 => 'Friday',
        6 => 'Saturday',
    ];

    static public $time_selections = [
        0  => '12 am',
        1  => '1 am',
        2  => '2 am',
        3  => '3 am',
        4  => '4 am',
        5  => '5 am',
        6  => '6 am',
        7  => '7 am',
        8  => '8 am',
        9  => '9 am',
        10 => '10 am',
        11 => '11 am',
        12 => '12 pm',
        13 => '1 pm',
        14 => '2 pm',
        15 => '3 pm',
        16 => '4 pm',
        17 => '5 pm',
        18 => '6 pm',
        19 => '7 pm',
        20 => '8 pm',
        21 => '9 pm',
        22 => '10 pm',
        23 => '11 pm',
    ];

    // This scope limits calendars based on account and user_group membership.
    public function scopeVisibleTo($query, User $user)
    {
        return (new CalendarVisibleToScope())->getQuery($query, $user);
    }

    // We have a "visible to Account" for Admin reasons.
    // Our normal visibleTo() scope will imit based on group members, etc.
    public function scopeVisibleToAccount($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('calendars.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccountByAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('calendars.account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function accountLocation()
    {
        return $this->belongsTo(AccountLocation::class);
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

    public function eventOccurrences()
    {
        return $this->hasManyThrough(EventOccurrence::class, Event::class, 'calendar_id', 'calendar_event_id');
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function scopeWithUlid($query, $ulid)
    {
        return $query->where('ulid', $ulid);
    }

    public function generateUlid()
    {
        if (!$this->ulid) {
            $this->ulid = Str::ulid()->toBase32();
            $this->save();
        }
    }

    public function scopeWithoutGroups($query)
    {
        return $query->whereNull('user_group_id');
    }

    public function scopeOnlyGroups($query)
    {
        return $query->whereNotNull('user_group_id');
    }

    public function scopeIsPublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeNotHidden($query)
    {
        return $query->where(function ($query2) {
            $query2->where('is_hidden', false)
                ->orWhereNull('is_hidden');
        });
    }

    public function getBackgroundColor()
    {
        return $this->background_color ?: null;
    }

    public function getFontColor()
    {
        return $this->text_color ?: null;
    }

    public function getBackgroundColorForCss()
    {
        return $this->getBackgroundColor() ? 'background-color: #' . $this->getBackgroundColor() . ';' : null;
    }

    public function getFontColorForCss()
    {
        return $this->getFontColor() ? 'color: #' . $this->getFontColor() . ';' : null;
    }

    static public $colors = [
        'rose'    => [
            '50'  => 'fff1f2',
            '100' => 'ffe4e6',
            '200' => 'fecdd3',
            '300' => 'fda4af',
            '400' => 'fb7185',
            '500' => 'f43f5e',
            '600' => 'e11d48',
            '700' => 'be123c',
            '800' => '9f1239',
            '900' => '881337',
        ],
        'pink'    => [
            '50'  => 'fdf2f8',
            '100' => 'fce7f3',
            '200' => 'fbcfe8',
            '300' => 'f9a8d4',
            '400' => 'f472b6',
            '500' => 'ec4899',
            '600' => 'db2777',
            '700' => 'be185d',
            '800' => '9d174d',
            '900' => '831843',
        ],
        'fuchsia' => [
            '50'  => 'fdf4ff',
            '100' => 'fae8ff',
            '200' => 'f5d0fe',
            '300' => 'f0abfc',
            '400' => 'e879f9',
            '500' => 'd946ef',
            '600' => 'c026d3',
            '700' => 'a21caf',
            '800' => '86198f',
            '900' => '701a75',
        ],
        'purple'  => [
            '50'  => 'faf5ff',
            '100' => 'f3e8ff',
            '200' => 'e9d5ff',
            '300' => 'd8b4fe',
            '400' => 'c084fc',
            '500' => 'a855f7',
            '600' => '9333ea',
            '700' => '7e22ce',
            '800' => '6b21a8',
            '900' => '581c87',
        ],
        'violet'  => [
            '50'  => 'f5f3ff',
            '100' => 'ede9fe',
            '200' => 'ddd6fe',
            '300' => 'c4b5fd',
            '400' => 'a78bfa',
            '500' => '8b5cf6',
            '600' => '7c3aed',
            '700' => '6d28d9',
            '800' => '5b21b6',
            '900' => '4c1d95',
        ],
        'indigo'  => [
            '50'  => 'eef2ff',
            '100' => 'e0e7ff',
            '200' => 'c7d2fe',
            '300' => 'a5b4fc',
            '400' => '818cf8',
            '500' => '6366f1',
            '600' => '4f46e5',
            '700' => '4338ca',
            '800' => '3730a3',
            '900' => '312e81',
        ],
        'blue'    => [
            '50'  => 'eff6ff',
            '100' => 'dbeafe',
            '200' => 'bfdbfe',
            '300' => '93c5fd',
            '400' => '60a5fa',
            '500' => '3b82f6',
            '600' => '2563eb',
            '700' => '1d4ed8',
            '800' => '1e40af',
            '900' => '1e3a8a',
        ],
        'sky'     => [
            '50'  => 'f0f9ff',
            '100' => 'e0f2fe',
            '200' => 'bae6fd',
            '300' => '7dd3fc',
            '400' => '38bdf8',
            '500' => '0ea5e9',
            '600' => '0284c7',
            '700' => '0369a1',
            '800' => '075985',
            '900' => '0c4a6e',
        ],
        'cyan'    => [
            '50'  => 'ecfeff',
            '100' => 'cffafe',
            '200' => 'a5f3fc',
            '300' => '67e8f9',
            '400' => '22d3ee',
            '500' => '06b6d4',
            '600' => '0891b2',
            '700' => '0e7490',
            '800' => '155e75',
            '900' => '164e63',
        ],
        'teal'    => [
            '50'  => 'f0fdfa',
            '100' => 'ccfbf1',
            '200' => '99f6e4',
            '300' => '5eead4',
            '400' => '2dd4bf',
            '500' => '14b8a6',
            '600' => '0d9488',
            '700' => '0f766e',
            '800' => '115e59',
            '900' => '134e4a',
        ],
        'emerald' => [
            '50'  => 'ecfdf5',
            '100' => 'd1fae5',
            '200' => 'a7f3d0',
            '300' => '6ee7b7',
            '400' => '34d399',
            '500' => '10b981',
            '600' => '059669',
            '700' => '047857',
            '800' => '065f46',
            '900' => '064e3b',
        ],
        'green'   => [
            '50'  => 'f0fdf4',
            '100' => 'dcfce7',
            '200' => 'bbf7d0',
            '300' => '86efac',
            '400' => '4ade80',
            '500' => '22c55e',
            '600' => '16a34a',
            '700' => '15803d',
            '800' => '166534',
            '900' => '14532d',
        ],
        'lime'    => [
            '50'  => 'f7fee7',
            '100' => 'ecfccb',
            '200' => 'd9f99d',
            '300' => 'bef264',
            '400' => 'a3e635',
            '500' => '84cc16',
            '600' => '65a30d',
            '700' => '4d7c0f',
            '800' => '3f6212',
            '900' => '365314',
        ],
        'yellow'  => [
            '50'  => 'fefce8',
            '100' => 'fef9c3',
            '200' => 'fef08a',
            '300' => 'fde047',
            '400' => 'facc15',
            '500' => 'eab308',
            '600' => 'ca8a04',
            '700' => 'a16207',
            '800' => '854d0e',
            '900' => '713f12',
        ],
        'amber'   => [
            '50'  => 'fffbeb',
            '100' => 'fef3c7',
            '200' => 'fde68a',
            '300' => 'fcd34d',
            '400' => 'fbbf24',
            '500' => 'f59e0b',
            '600' => 'd97706',
            '700' => 'b45309',
            '800' => '92400e',
            '900' => '78350f',
        ],
        'orange'  => [
            '50'  => 'fff7ed',
            '100' => 'ffedd5',
            '200' => 'fed7aa',
            '300' => 'fdba74',
            '400' => 'fb923c',
            '500' => 'f97316',
            '600' => 'ea580c',
            '700' => 'c2410c',
            '800' => '9a3412',
            '900' => '7c2d12',
        ],
        'red'     => [
            '50'  => 'fef2f2',
            '100' => 'fee2e2',
            '200' => 'fecaca',
            '300' => 'fca5a5',
            '400' => 'f87171',
            '500' => 'ef4444',
            '600' => 'dc2626',
            '700' => 'b91c1c',
            '800' => '991b1b',
            '900' => '7f1d1d',
        ],
        'gray'    => [
            '50'  => 'fafafa',
            '100' => 'f4f4f5',
            '200' => 'e4e4e7',
            '300' => 'd4d4d8',
            '400' => 'a1a1aa',
            '500' => '71717a',
            '600' => '52525b',
            '700' => '3f3f46',
            '800' => '27272a',
            '900' => '18181b',
        ],
    ];
}
