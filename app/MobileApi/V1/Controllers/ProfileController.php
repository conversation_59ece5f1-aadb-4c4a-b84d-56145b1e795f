<?php

namespace App\MobileApi\V1\Controllers;

use App\Accounts\AccountSetting;
use App\Accounts\Grade;
use App\Base\Http\Controllers\Controller;
use App\Users\Phone;
use App\Users\Services\UpdateUser;
use App\Users\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    public function index()
    {
        $user_data = User::select([
            'id',
            'account_id',
            'first_name',
            'last_name',
            'birthdate',
            'marital_status',
            'date_married',
            'gender',
            'blood_type',
            'school_attending',
            'user_grade_id',
            'employer',
            'job_title',
            'job_keywords',
        ])
            ->with([
                'emails:id,user_id,family_id,type,email,is_family,is_primary,is_hidden,receives_group_emails,allow_messages,sort_id',
                'addresses:id,user_id,family_id,type,label,address1,address2,address3,city,state,zip,country,is_family,map_thumbnail_url_512',
                'phones:id,user_id,family_id,type,number,is_family,is_primary,is_hidden',
                'grade:id,account_id,name,description',
            ])
            ->find(Auth::user()->id)
            ->toArray();

        $final_phones = [];
        foreach ($user_data['phones'] as $phone) {
            $phone['number_formatted'] = Phone::format($phone['number'], '-');
            $final_phones[]            = $phone;
        }

        $user_data['phones'] = $final_phones;

        $user_data['account']['grades'] = Grade::visibleTo(Auth::user())->select(['id', 'name', 'description', 'sort_id'])->orderBy('sort_id')->get()->toArray();

        return response()->json($user_data);
    }

    public function save()
    {
        $user = Auth::user();

        if (!Auth::user()->id) {
            return response()->json(null, 401);
        }

        $validator = Validator::make(request()->all(), [
            'first_name'       => 'required|string|max:128',
            'last_name'        => 'required|string|max:128',
            'birthdate'        => 'nullable|string',
            'marital_status'   => 'nullable|string',
            'date_married'     => 'nullable|string',
            'maiden_name'      => 'nullable|string',
            'gender'           => 'nullable|string',
            'password'         => 'nullable|string|max:256',
            'blood_type'       => 'nullable|string',
            'school_attending' => 'nullable|string',
            'user_grade_id'    => 'nullable|integer',
            'employer'         => 'nullable|string',
            'job_title'        => 'nullable|string',
            'job_keywords'     => 'nullable|string',
        ])->validate();

        if ($user) {
            try {
                // @TODO: Make sure we're only ever passing fields allowed, the update service will accept all request fields.
                DB::transaction(function () use ($user) {
                    return (new UpdateUser($user))->update(request()->only([
                        'first_name',
                        'last_name',
                        'birthdate',
                        'marital_status',
                        'date_married',
                        'maiden_name',
                        'gender',
                        'password',
                        'blood_type',
                        'school_attending',
                        'user_grade_id',
                        'employer',
                        'job_title',
                        'job_keywords',
                    ]));
                });
            } catch (\Exception $e) {
                Log::error($e);
                return response()->json([
                    'errors'  => 'save_failed',
                    'message' => $e->getMessage(),
                ], 400);
            }
        }

        return response()->json('OK', 200);
    }

    public function permissions()
    {
        if (!Auth::user()->id) {
            return response()->json(null, 401);
        }

        $user = Auth::user();

        try {
            return response()->json([
                'user_id'          => $user->id,
                'account_id'       => $user->account_id,
                'timezone'         => $user->account->timezone,
                'account'          => $user->account,
                'account_settings' => AccountSetting::with([
                    'value' => function ($query) use ($user) {
                        $query->where('account_id', $user->account_id);
                    },
                ])->get(),
                'permissions'      => $user->permissions(),
            ], 200);
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => $e->getMessage(),
            ], 400);
        }
    }
}
