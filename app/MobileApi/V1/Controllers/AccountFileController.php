<?php

namespace App\MobileApi\V1\Controllers;

use App\AccountFiles\AccountFile;
use App\Base\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class AccountFileController extends Controller
{
    public function index()
    {
        $files = AccountFile::visibleTo(Auth::user())->get();

        foreach ($files as $file) {
            $file->full_url = $file->getUrl();
        }

        return response()->json($files);
    }
}
