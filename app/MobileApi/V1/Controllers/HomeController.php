<?php

namespace App\MobileApi\V1\Controllers;

use App\Base\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        $data = [
            'birthdays' => [
                'today'    => (new \App\App\Services\Dashboard())->getBirthdaysToday(),
                'upcoming' => (new \App\Admin\Services\Dashboard())->getUpcomingBirthdays(),
            ],
            'sermons'   => [
                'latest' => (new \App\App\Services\Dashboard())->getLatestSermons(),
            ],
        ];

        return response()
            ->json($data);
    }
}
