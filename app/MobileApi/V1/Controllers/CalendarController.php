<?php

namespace App\MobileApi\V1\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Calendars\Calendar;
use App\Calendars\EventOccurrence;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class CalendarController extends Controller
{
    public function index()
    {
        return response()->json(
            Calendar::visibleTo(Auth::user())->where('is_hidden', false)->get()
        );
    }

    public function allEventOccurrences()
    {
        $calendars = Calendar::visibleTo(Auth::user())->where('is_hidden', false)->get();

        $events = EventOccurrence::visibleTo(Auth::user())
            ->whereIn('calendar_id', $calendars->pluck('id'))
            ->where('start_at', '>', Carbon::now()->setDay(1))
            ->where('end_at', '<', Carbon::now()->add('18 months'))
            ->orderBy('start_at', 'ASC')
            ->select(['id', 'start_at', 'end_at', 'start_at_date', 'end_at_date', 'start_at_time', 'end_at_time', 'account_id', 'calendar_id', 'calendar_event_id', 'user_group_id', 'is_hidden', 'is_group_only'])
            ->with([
                'event:id,uuid,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
            ])
            ->get();

        $final_events = [];

        foreach ($events as $event) {
            $final_events[$event->start_at->format('Ymd')]['date'] = $event->start_at->format('Y-m-d');

            // If we span multiple days
            if ($event->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d') != $event->end_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d')) {
                $final_events[$event->start_at->format('Ymd')]['events'][] = $event;
                $cloned                                                    = clone $event;
                while ($cloned->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d') < $event->end_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d')) {
                    $cloned->start_at                                           = $cloned->start_at->addDays(1);
                    $final_events[$cloned->start_at->format('Ymd')]['date']     = $cloned->start_at->format('Y-m-d');
                    $final_events[$cloned->start_at->format('Ymd')]['events'][] = $cloned;
                }
            } else {
                $final_events[$event->start_at->format('Ymd')]['events'][] = $event;
            }
        }

        $final_events = array_values($final_events);

        return response()->json($final_events);
    }

    public function viewEventOccurrence(EventOccurrence $eventOccurrence)
    {
        if (!$eventOccurrence->id) {
            return abort(404);
        }

        $eventOccurrence = EventOccurrence::visibleTo(Auth::user())
            ->select(['id', 'start_at', 'end_at', 'account_id', 'calendar_id', 'calendar_event_id'])
            ->with([
                'event:id,uuid,account_id,calendar_id,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
            ])
            ->find($eventOccurrence->id);

        return response()->json($eventOccurrence);
    }
}
