<?php

namespace App\MobileApi\V1\Controllers;

use App\Base\Http\Controllers\Controller;
use App\MobileApi\V1\Requests\CreateAddressRequest;
use App\Users\Address;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AccountAddressController extends Controller
{
    protected function store(CreateAddressRequest $request)
    {
        $user = Auth::user();

        try {
            $address = Address::create(request()->only([
                    'type',
                    'label',
                    'address1',
                    'address2',
                    'address3',
                    'city',
                    'state',
                    'zip',
                    'country',
                    'is_family',
                    'is_mailing',
                ]) + ['user_id' => $user->id]);

            $address->user_id = $user->id;
            $address->is_family ? $address->family_id = $user->id : null;

            $address->save();

        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json($user->addresses, 201);
    }

    public function save(Address $address)
    {
        if (Auth::user()->id !== $address->user_id) {
            return response()->json(null, 401);
        }

        $validator = Validator::make(request()->all(), [
            'type'                => 'required|in:' . implode(',', Address::getTypeKeys()),
            'label'               => 'nullable|string|max:64',
            'address1'            => 'required|string|max:128',
            'address2'            => 'nullable|string|max:64',
            'address3'            => 'nullable|string|max:64',
            'city'                => 'required|string|max:40',
            'state'               => 'nullable|string|max:32',
            'zip'                 => 'nullable|string|max:16',
            'country'             => 'required|string|max:4',
            'is_family'           => 'required|integer',
            'is_mailing'          => 'nullable|integer',
            'is_hidden'           => 'nullable|integer',
            'is_primary_for_user' => 'nullable|integer',
        ])->validate();

        try {
            $address->fill(request()->only([
                'type',
                'label',
                'address1',
                'address2',
                'address3',
                'city',
                'state',
                'zip',
                'country',
            ]));

            $address->is_family  = request()->input('is_family', 0);
            $address->is_mailing = request()->input('is_mailing', 0);

            $address->save();

        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json(Auth::user()->addresses, 200);
    }

    public function delete(Address $address)
    {
        if (Auth::user()->id !== $address->user_id) {
            return response()->json(null, 401);
        }

        try {
            if (Auth::user()->id == $address->user_id) {
                $address->delete();
            }
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json(Auth::user()->addresses, 200);
    }
}
