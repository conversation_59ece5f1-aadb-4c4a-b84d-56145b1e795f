<?php

namespace App\MobileApi\V1\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Email;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AccountEmailController extends Controller
{
    public function store()
    {
        $user = Auth::user();

        $validator = Validator::make(request()->all(), [
            'email'                 => 'required|email|max:128|unique:user_emails',
            'type'                  => 'required|in:' . implode(',', Email::getTypeKeys()),
            'is_primary'            => 'nullable|integer',
            'is_family'             => 'nullable|integer',
            'receives_group_emails' => 'nullable|integer',
        ])->validate();

        try {
            $email = $user->emails()->create(request()->only([
                'email',
                'type',
                'is_primary',
                'is_family',
                'receives_group_emails',
            ]));

            if (request()->input('is_family') == 1) {
                $email->family_id = $user->family_id;
            }

            $email->save();

        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json(Auth::user()->emails, 201);
    }

    public function save(Email $email)
    {
        $user = Auth::user();

        if (Auth::user()->id !== $email->user_id) {
            return response()->json(null, 401);
        }

        $validator = Validator::make(request()->all(), [
            'email'                 => [
                'required',
                'email',
                'max:128',
                Rule::unique('user_emails')->ignore($email->id),
            ],
            'type'                  => 'required|in:' . implode(',', Email::getTypeKeys()),
            'is_primary'            => 'nullable|integer',
            'is_family'             => 'nullable|integer',
            'receives_group_emails' => 'nullable|integer',
        ])->validate();

        try {
            $email->fill(request()->only([
                'email',
                'type',
                'is_primary',
                'is_family',
                'receives_group_emails',
            ]));

            $email->is_primary            = request()->input('is_primary', 0);
            $email->is_family             = request()->input('is_family', 0);
            $email->is_hidden             = request()->input('is_hidden', 0);
            $email->receives_group_emails = request()->input('receives_group_emails', 0);

            if (request()->input('is_family') == 1) {
                $email->family_id = $user->family_id;
            }

            $email->save();

        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json(Auth::user()->emails, 200);
    }

    public function delete(Email $email)
    {
        if (Auth::user()->id !== $email->user_id) {
            return response()->json(null, 401);
        }

        try {
            if (Auth::user()->id === $email->user_id) {
                $email->delete();
            }
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json(Auth::user()->emails, 200);
    }
}
