<?php

namespace App\MobileApi\V1\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Sermons\Sermon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;

class SermonController extends Controller
{
    public function index(Request $request)
    {
        $sermons = Sermon::visibleTo($request->user())
            ->isNotHidden()
            ->with('files:id,sermon_id,title,type,file_size,file_folder,file_name,file_extension,file_type,file_sha1')
            ->orderBy('date_sermon', 'desc')
            ->select([
                'id',
                'date_sermon',
                'language',
                'speaker',
                'title',
                'type',
                'description',
                'video_link',
                'podcast_link',
            ])
            ->when((request()->get('type') == 'title' && request()->get('query')), function ($query) {
                return $query->where('title', 'like', '%' . request()->get('query') . '%');
            })
            ->when(request()->get('title'), function ($query) {
                return $query->where('title', 'like', '%' . request()->get('title') . '%');
            })
            ->when((request()->get('type') == 'speaker' && request()->get('query')), function ($query) {
                return $query->where('speaker', 'like', '%' . request()->get('query') . '%');
            })
            ->when(request()->get('speaker'), function ($query) {
                return $query->where('speaker', 'like', '%' . request()->get('speaker') . '%');
            })
            ->when(request()->get('language'), function ($query) {
                return $query->where('language', request()->get('language'));
            })
            ->paginate(15);

        $sermons->map(function ($sermon) {
            $sermon->files->map(function ($file) {
                $file->link = $file->getCdnUrl();

                return $file;
            });

            return $sermon;
        });

        return response()
            ->json($sermons);
    }
}
