<?php

namespace App\MobileApi\V1\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Phone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AccountPhoneController extends Controller
{
    public function store(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make(request()->all(), [
            'number'     => 'required|string|max:32',
            'is_primary' => 'integer',
            'is_family'  => 'integer',
            'type'       => 'required|in:' . implode(',', array_keys(Phone::$types)),
        ])->validate();

        try {
            $phone = new Phone;

            $phone->fill(request()->only([
                'number',
                'is_primary',
                'is_family',
                'type',
            ]));

            $phone->number  = Phone::format($phone->number);
            $phone->user_id = $user->id;

            if (request()->input('is_family') == 1) {
                $phone->family_id = $user->family_id;
            }

            $phone->save();

        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json($user->phones, 201);
    }

    public function save(Phone $phone)
    {
        $user = Auth::user();

        if (Auth::user()->id !== $phone->user_id) {
            return response()->json(null, 401);
        }

        $validator = Validator::make(request()->all(), [
            'number'     => 'required|string|max:32',
            'is_primary' => 'integer',
            'is_family'  => 'integer',
            'type'       => 'required|in:' . implode(',', Phone::getTypeKeys()),
        ])->validate();

        try {
            $phone->fill(request()->only([
                'number',
                'type',
                'is_primary',
                'is_family',
            ]));

            $phone->number = Phone::format($phone->number);

            $phone->is_primary = request()->input('is_primary', 0);
            $phone->is_family  = request()->input('is_family', 0);

            if (request()->input('is_family') == 1) {
                $phone->family_id = $user->family_id;
            }

            $phone->save();

        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json($user->phones, 200);
    }

    public function delete(Phone $phone)
    {
        if (Auth::user()->id !== $phone->user_id) {
            return response()->json(null, 401);
        }

        try {
            if (Auth::user()->id === $phone->user_id) {
                $phone->delete();
            }
        } catch (\Exception $e) {
            Log::error($e);
            return response()->json([
                'errors'  => 'failed',
                'message' => 'There was an error saving your updates to the database.  Please try again.',
            ], 400);
        }

        return response()->json(Auth::user()->phones, 200);
    }
}
