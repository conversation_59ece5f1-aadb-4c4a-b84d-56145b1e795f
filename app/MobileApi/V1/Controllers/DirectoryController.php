<?php

namespace App\MobileApi\V1\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\User;
use Illuminate\Http\Request;

class DirectoryController extends Controller
{
    public function index(Request $request)
    {
        if (!$request->user()->account->getSetting('feature.website_integrations')) {
            abort(401);
        }

        $users = User::visibleTo($request->user())
            ->HeadsOfFamily()
            ->select(['id', 'account_id', 'first_name', 'last_name', 'family_id', 'family_role', 'birthdate'])
            ->with([
                'emails:id,user_id,email,family_id,is_family,is_primary',
                'familyMembers:id,account_id,family_id,first_name,last_name,family_role,birthdate',
            ])
            ->orderBy('last_name', 'asc')
            ->when(request()->input('member_name'), function ($query) {
                $query->where(function ($query2) {
                    $query2->where('last_name', 'like', request('member_name') . '%');
                    $query2->orWhere('first_name', 'like', request('member_name') . '%');
                });
            })
            ->where(function ($query) {
                $query->whereHas('roles', function ($query) {
                    $query->where('indicates_membership', '1');
                })->orWhereHas('groups', function ($query) {
                    $query->where('indicates_membership', '1');
                });
            })
            ->paginate(2000, ['id', 'account_id', 'first_name', 'last_name', 'family_id', 'family_role', 'birthdate'], '_page');

        return response()->json($users);
    }

    public function viewFamily($user_id)
    {
        if (!request()->user()->account->getSetting('feature.website_integrations')) {
            abort(401);
        }

        $user = User::visibleTo(request()->user())->find($user_id);

        if (!$user) {
            abort(403);
        }

        $data['user'] = [
            'id'              => $user->id,
            'first_name'      => $user->first_name,
            'last_name'       => $user->last_name,
            'is_baptized'     => $user->is_baptized,
            'birthdate'       => $user->birthdate ? $user->birthdate->format('Y-m-d') : null,
            'date_baptism'    => $user->date_baptism ? $user->date_baptism->format('Y-m-d') : null,
            'date_membership' => $user->date_membership ? $user->date_membership->format('Y-m-d') : null,
            'date_married'    => $user->date_married ? $user->date_married->format('Y-m-d') : null,
        ];

        $data['user']['spouse'] = [];
        if ($user->spouse) {
            $data['user']['spouse'] = [
                'id'           => $user->spouse->id,
                'first_name'   => $user->spouse->first_name,
                'last_name'    => $user->spouse->last_name,
                'is_baptized'  => $user->is_baptized,
                'birthdate'    => $user->birthdate ? $user->birthdate->format('Y-m-d') : null,
                'date_baptism' => $user->date_baptism ? $user->date_baptism->format('Y-m-d') : null,
            ];
        }

        $data['addresses'] = [];
        $address           = $user->getFamilyAddress();
        if ($address && ($address->family_id || $address->user->date_deceased === null)) {
            $data['addresses'][] = [
                'id'             => $address->id,
                'family_id'      => $address->family_id,
                'address_string' => $address->getAddressString(),
                'address_html'   => $address->getFullAddressHTML(),
                'label'          => $address->label,
                'type'           => $address->type,
                'address1'       => $address->address1,
                'address2'       => $address->address2,
                'address3'       => $address->address3,
                'city'           => $address->city,
                'state'          => $address->state,
                'zip'            => $address->zip,
                'country'        => $address->country,
                'is_mailing'     => $address->is_mailing,
                'maps_image'     => $address->getMapThumbnailUrl(), // $address->getStaticGoogleMapsImage(160, 135, 'class="float-right ml-2 border border-secondary"'),
                'maps_link'      => 'http://maps.google.com/?daddr=' . urlencode(str_replace("<br>", ",", \App\Users\Address::format($address))),
            ];
        }

        $data['phones'] = [];
        foreach ($user->family()->with([
            'phones' => function ($query) {
                $query->where('is_hidden', false);
            },
        ])->get()->pluck('phones')->flatten() as $phone) {
            if ($phone->family_id || $phone->user->date_deceased === null) {
                $data['phones'][] = [
                    'id'               => $phone->id,
                    'family_id'        => $phone->family_id,
                    'number_formatted' => $phone->formattedNumber(),
                    'number'           => $phone->number,
                    'type'             => $phone->type,
                    'first_name'       => $phone->user->first_name,
                ];
            }
        }

        $data['emails'] = [];
        foreach ($user->family()->with([
            'emails' => function ($query) {
                $query->where('is_hidden', false);
            },
        ])->get()->pluck('emails')->flatten() as $email) {
            if ($email->family_id || $email->user->date_deceased === null) {
                $data['emails'][] = [

                    'id'         => $email->id,
                    'family_id'  => $email->family_id,
                    'type'       => $email->type,
                    'email'      => $email->email,
                    'first_name' => $email->user->first_name,
                    'type'       => $email->type,
                ];
            }
        }

        $data['family_members'] = [];
        foreach ($user->family()->isNotDeceased()->get() as $member) {
            $data['family_members'][] = [
                'id'           => $member->id,
                'first_name'   => $member->first_name,
                'family_role'  => $member->family_role != 'other' ? $member->family_role : null,
                'birthdate'    => $member->birthdate ? $member->birthdate->format('F d') : null,
                'is_baptized'  => $member->is_baptized,
                'date_baptism' => $user->date_baptism ? $user->date_baptism->format('Y-m-d') : null,
            ];
        }

        $data['family_photo'] = [];
        if ($photo = $user->familyPrimaryPhoto()) {
            $data['family_photo'] = [
                'id'                   => $photo->id,
                'full_size_url'        => $photo->getCdnUrl(),
                'thumbnail_url'        => $photo->getCdnUrl(512),
                'thumbnail_retina_url' => $photo->getCdnUrl(1024),
            ];
        }

        return response()->json($data);
    }
}
