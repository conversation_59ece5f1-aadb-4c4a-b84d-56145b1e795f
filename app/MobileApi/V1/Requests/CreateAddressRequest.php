<?php

namespace App\MobileApi\V1\Requests;

use App\Base\Http\Request;
use App\Users\Address;

class CreateAddressRequest extends Request
{
    public function rules()
    {
        return [
            'type'                => 'required|in:' . implode(',', Address::getTypeKeys()),
            'label'               => 'nullable|string|max:64',
            'address1'            => 'required|string|max:128',
            'address2'            => 'nullable|string|max:64',
            'address3'            => 'nullable|string|max:64',
            'city'                => 'required|string|max:40',
            'state'               => 'nullable|string|max:32',
            'zip'                 => 'nullable|string|max:16',
            'country'             => 'required|string|max:4',
            'is_family'           => 'required|integer',
            'is_mailing'          => 'nullable|integer',
            'is_hidden'           => 'nullable|integer',
            'is_primary_for_user' => 'nullable|integer',
        ];
    }
}
