<?php

namespace App\MobileApi\Auth\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Mail\App\MobileResetPassword;
use App\Users\Email;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ForgotPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    public function sendResetLinkEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Please enter a valid email.',
            ], 404);
        }

        $email = trim(request('email'));

        if (!Email::where('email', $email)->exists()) {
//            Log::warning('MobileAPI::Password reset attempt, email not found for: ' . $email);
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Email was not found.',
            ], 404);
        }

        Log::info('MobileAPI::ForgotPasswordController::sendResetLinkEmail -- Attempting to email password reset email for email: ' . $email);

        $token = uniqid('', true);
        $pin   = rand(100000, 999999);

        try {
            DB::table('password_resets')->insert([
                'email'      => $email,
                'token'      => $token,
                'pin'        => $pin,
                'created_at' => date('Y-m-d H:i:s'),
            ]);

            Mail::to($email)->queue(new MobileResetPassword($email, $token, $pin));
        } catch (\Exception $e) {
            Log::error($e);
        }

        return response()->json('Email sent.', 200);
    }
}
