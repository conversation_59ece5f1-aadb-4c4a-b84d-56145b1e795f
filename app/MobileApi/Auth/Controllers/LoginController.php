<?php

namespace App\MobileApi\Auth\Controllers;

use App\Accounts\AccountSetting;
use App\Base\Http\Controllers\Controller;
use App\MobileApi\Auth\Requests\LoginUserRequest;
use App\Users\Activity;
use App\Users\Device;
use App\Users\Email;
use App\Users\Phone;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LoginController extends Controller
{
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function healthCheck()
    {
        return response()->json('OK', 200);
    }

    public function login(LoginUserRequest $request)
    {
        Log::info('Login attempt.', [
            'user_name' => $request->get('user_name'),
        ]);

        if ($this->attemptLogin($request)) {
            Log::info('Login with MobileApi success.', [
                'path'      => request()->path(),
                'user_id'   => auth()->user()->id,
                'user_name' => $request->get('user_name'),
            ]);

            Activity::create([
                'account_id' => auth()->user()->account_id,
                'user_id'    => auth()->user()->id,
                'site'       => Activity::getSiteFromString(request()->root()),
                'method'     => strtoupper(request()->method()),
                'path'       => request()->path(),
            ]);

            $user             = auth()->user();
            $user->last_login = Carbon::now();
            $user->save();

            $user->load('account:id,name,address1,address2,address3,city,state,postal_code,country_code,phone_work,phone_fax,timezone,email');

//            $grades = Grade::visibleTo($user)->select(['id', 'name', 'description', 'sort_id'])->orderBy('sort_id')->get();

            $access_token = $request->bearerToken();
            if (!$access_token || $access_token == 'null') {
                $access_token = $user->createToken('LightpostRN')->accessToken;
            }

            return response()->json([
                'user_id'          => $user->id,
                'account_id'       => $user->account_id,
                'token'            => $access_token,
                'timezone'         => $user->account->timezone,
                'account'          => $user->account,
//                'account_grades'   => $grades,
                'account_settings' => AccountSetting::with([
                    'value' => function ($query) use ($user) {
                        $query->where('account_id', $user->account_id);
                    },
                ])->get(),
                'permissions'      => $user->permissions(),
            ], 200);
        }

        $errors = [
            'errors'  => 'invalid_credentials',
            'message' => trans('auth.failed'),
        ];

        Log::info('MobileApi login failed.', [
            'invalid_credentials' => request('user_name'),
            'user_name'           => $request->get('user_name'),
        ]);
        Log::info($errors);

        return response()->json($errors, 422);
    }

    protected function attemptLogin(Request $request)
    {
        $user = User::where('user_name', $request->get('user_name'))
            ->whereNotNull('user_name')
            ->where('user_name', '<>', '')
            ->first();

        if ($user && $user->account->is_active && $user->isMember()) {
            return auth()->guard()->attempt(
                $request->only('user_name', 'password'),
                $request->has('remember')
            );
        }

        $user_email = Email::where('email', 'like', $request->get('user_name'))
            ->whereNotNull('email')
            ->where('email', '<>', '')
            ->first();

        if ($user_email && $user_email->user->account->is_active && $user_email->user->isMember()) {
            return auth()->attempt(['id' => $user_email->user_id, 'password' => $request->get('password')]);
        }

        $user_phone = Phone::where('number', 'like', preg_replace('/[^0-9]/', '', $request->get('user_name')))
            ->whereNotNull('number')
            ->where('number', '<>', '')
            ->first();

        if ($user_phone && $user_phone->user->account->is_active && $user_phone->user->isMember()) {
            return auth()->attempt([
                'id'       => $user_phone->user_id,
                'password' => $request->get('password'),
            ]);
        }

        return false;
    }

    protected function username()
    {
        return 'user_name';
    }

    public function logout(Request $request)
    {
        Log::info('MobileAPI:Logout request. ' . request()->path(), [
            'user_id' => optional(auth()->user())->id,
            'name'    => optional(auth()->user())->first_name . ' ' . optional(auth()->user())->last_name,
        ]);

        Activity::create([
            'account_id' => auth()->user()->account_id,
            'user_id'    => auth()->user()->id,
            'site'       => Activity::getSiteFromString(request()->root()),
            'method'     => strtoupper(request()->method()),
            'path'       => request()->path(),
        ]);

        auth()->user()->token()->revoke();

        if ($request->has('device_token')) {
            $device = Device::where('device_token', 'LIKE', $request->get('device_token'))->first();
            if ($device) {
                $device->delete();
            }
        }

        Log::info('Logout request from React Native being processed.');

        return response()->json('OK', 200);
    }

    public function removeDeviceToken()
    {
        Log::info('MobileAPI:removeDeviceToken request. ' . request()->path(), [
            'user_id'      => optional(auth()->user())->id,
            'device_token' => request('device_token'),
        ]);

        Log::notice('MobileAPI::Login::removeDeviceToken - Route hit.', [
            'device_token' => request('device_token'),
        ]);

        if (!request('device_token')) {
            return abort(422, 'Invalid.');
        }

        $token = Device::where('device_token', 'LIKE', request('device_token'))->first();

        $token?->delete();

        return response()->json('OK', 200);
    }
}
