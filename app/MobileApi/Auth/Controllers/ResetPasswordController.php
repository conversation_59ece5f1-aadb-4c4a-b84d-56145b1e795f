<?php

namespace App\MobileApi\Auth\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Email;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    public function reset(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token'    => 'required',
            'password' => 'required|confirmed',
        ])->validate();

        $email = trim(optional(DB::table('password_resets')->where('token', request('token'))->first())->email);

        if (!$email) {
            Log::error("MobileAPI::Could not find token for password reset: " . request('token'));
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Sorry, the reset token provided is invalid. It could be expired! Please try again, or reach out to your church office for support.',
            ], 404);
        }

        Log::info('MobileAPI::ResetPasswordController::reset -- Attempting to change password for email: ' . $email);

        $emailObject = Email::where('email', $email)->first();

        if (!optional($emailObject)->user) {
//            Log::error("MobileAPI::Could not find user for password reset for email: " . $email);
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Sorry, we could not find a valid user account for the email provided. 
                Please reach out to your church office for support.',
            ], 404);
        }

        if (!$emailObject?->user->isMember()) {
            Log::warning("MobileAPI::Attempt to reset non-member password.", [
                'email' => $email,
            ]);
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Sorry, only emails for members can reset their password. Please reach out to your church office for support.',
            ], 404);
        }

        $user = $emailObject->user;

        $user->password = request('password');

        $user->save();

        DB::table('password_resets')->where('token', request('token'))->delete();

        event(new PasswordReset($user));

        Auth::login($user);

        return response()->json('Password reset successful.', 200);
    }

    public function checkPin(Request $request)
    {
        $exists = DB::table('password_resets')
            ->where('email', 'LIKE', trim(request('email')))
            ->where('pin', request('pin'))
            ->first();

        if (!$exists) {
//            Log::warning("MobileAPI::Could not find PIN for checkPin: " . request('email') . ' - ' . request('pin'));
            return response()->json([
                'errors'  => 'pin_not_found',
                'email'   => request('email'),
                'pin'     => request('pin'),
                'message' => 'Oops! This code is invalid or expired. Please try again.',
            ], 404);
        }

        Log::info('MobileAPI::ResetPasswordController::checkPin -- Attempting to check PIN for email: ' . request('email'));

        return response()->json('Valid.', 200);
    }

    public function resetWithPin(Request $request)
    {
//        $validator = Validator::make($request->all(), [
//            'pin'      => 'required',
//            'email'    => 'required',
//            'password' => 'required|confirmed',
//        ])->validate();
//
        // Currently errors with "call to fails() on array" -- no idea why.
//        if ($validator->fails()) {
//            return response()->json([
//                'errors'  => 'email_not_found',
//                'message' => 'Sorry, the reset code provided is invalid. It could be expired! Please try again, or reach out to your church office for support.',
//            ], 404);
//        }

        $email = trim(
            optional(
                DB::table('password_resets')
                    ->where('email', 'LIKE', trim(request('email')))
                    ->where('pin', request('pin'))
                    ->first()
            )->email
        );

        if (!$email) {
//            Log::warning("MobileAPI::Could not find PIN for password reset: " . request('email'));
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Sorry, the reset code provided is invalid. It could be expired! Please try again, or reach out to your church office for support.',
            ], 404);
        }

        Log::info('MobileAPI::ResetPasswordController::reset -- Attempting to change password with PIN for email: ' . $email);

        $emailObject = Email::where('email', $email)->first();

        if (!optional($emailObject)->user) {
            Log::error("MobileAPI::Could not find user for password reset with PIN for email: " . $email);
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Sorry, we could not find a valid user account for the email provided. 
                Please reach out to your church office for support.',
            ], 404);
        }

        if (!$emailObject?->user->isMember()) {
            Log::warning("MobileAPI::Attempt to reset non-member password.", [
                'email' => $email,
            ]);
            return response()->json([
                'errors'  => 'email_not_found',
                'message' => 'Sorry, only emails for member accounts can reset their password. Please reach out to your church office for support.',
            ], 404);
        }

        $user = $emailObject->user;

        $user->password = request('password');

        $user->save();

        DB::table('password_resets')
            ->where('email', 'LIKE', trim(request('email')))
            ->where('pin', request('pin'))
            ->delete();

        Log::info('MobileAPI::ResetPasswordController::reset -- User password reset successful: ' . $email);

        return response()->json('Password reset successful.', 200);
    }
}
