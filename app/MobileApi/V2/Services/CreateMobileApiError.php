<?php

namespace App\MobileApi\V2\Services;

class CreateMobileApiError
{
    protected $message       = null;
    protected $type          = 'error';
    protected $error_code    = 0;
    protected $response_code = 400;
    protected $fields        = [];
    protected $is_error      = true;

    public function withMessage($message)
    {
        $this->message = $message;

        return $this;
    }

    public function withType($type)
    {
        $this->type = $type;

        return $this;
    }

    public function withErrorCode($error_code)
    {
        $this->error_code = $error_code;

        return $this;
    }

    public function withResponseCode($response_code)
    {
        $this->response_code = $response_code;

        return $this;
    }

    public function withFields($fields)
    {
        $this->fields = $fields;

        return $this;
    }

    public function isError($is_error)
    {
        $this->is_error = $is_error;

        return $this;
    }

    public function create()
    {
        return [
            'error'   => $this->is_error,
            'message' => $this->message,
            'type'    => $this->type,
            'code'    => $this->error_code,
            'fields'  => $this->fields,
        ];
    }

    public function response()
    {
        return response()->json($this->create(), $this->response_code);
    }
}
