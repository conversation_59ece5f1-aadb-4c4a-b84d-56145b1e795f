<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Crises\Checkin;
use App\Crises\CheckinReply;
use App\Crises\Crisis;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class CrisesController extends Controller
{
    public function active()
    {
        return response()->json([
            'checkin_types'       => Checkin::$types,
            'checkin_reply_types' => CheckinReply::$types,
            'family_checkin'      => Auth::user()->getCheckinForCrisis(optional(Auth::user()->account->getActiveCrisis())->id),
            'crisis'              => Crisis::visibleTo(Auth::user())->isActive()
                ->with([
                    'checkins',
                    'checkins.replies',
                    'checkins.replies.user:id,account_id,first_name,last_name,family_id',
                    'checkins.user:id,family_id,first_name,last_name',
                    'checkins.family:id,family_id,first_name,last_name',
                ])
                ->first(),
        ]);
    }

    public function userCheckinSave()
    {
        $crisis = Auth::user()->account->getActiveCrisis();
        $user   = Auth::user();

        $validator = Validator::make(request()->all(), [
            'type'  => 'required|string|max:60',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails() || !$crisis) {
            return abort(422);
        }

        try {
            $checkin = Checkin::firstOrNew([
                'account_id' => $user->account_id,
                'crisis_id'  => $crisis->id,
                'family_id'  => $user->family_id,
            ]);

            $checkin->user_id      = $user->id;
            $checkin->type         = request('type');
            $checkin->notes        = request('notes');
            $checkin->responded_at = Carbon::now();

            $checkin->save();

            // Notifications are sent (if needed) through the CheckinObserver.

        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }

//        PostCreated::dispatch($checkin);

        return response()->json([
            'data' => $checkin,
        ]);
    }

    public function checkinReplySave($checkin)
    {
        $crisis = Auth::user()->account->getActiveCrisis();
        $user   = Auth::user();

        $validator = Validator::make(request()->all(), [
            'type'  => 'required|string|max:60',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails() || !$crisis) {
            return abort(422);
        }

        try {
            $reply = CheckinReply::create([
                'account_id'        => $user->account_id,
                'crisis_id'         => $crisis->id,
                'user_id'           => $user->id,
                'crisis_checkin_id' => $checkin->id,
                'type'              => request('type'),
                'notes'             => request('notes'),
            ]);

            $reply->save();
        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }

        return response()->json([
            'data' => $checkin,
        ]);
    }
}
