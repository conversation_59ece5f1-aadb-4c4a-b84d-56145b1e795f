<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\User;
use App\WorshipAssignments\Group;
use App\WorshipAssignments\Period;
use App\WorshipAssignments\Pick;
use App\WorshipAssignments\Services\DeclineAssignment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

class WorshipAssignmentController extends Controller
{
    public function index(Request $request)
    {
        return response()
            ->json([

            ]);
    }

    public function current(Request $request)
    {
        return response()
            ->json([
                'current_period' => Period::visibleTo(auth()->user())
                    ->select([
                        'id',
                        'account_id',
                        'wa_group_id',
                        'name',
                        'start_at',
                        'end_at',
                        'sent_notifications',
                    ])
                    ->getActivePeriod()
                    ->isPublished()
                    ->first(),
                'picks'          => Pick::visibleTo(auth()->user())
                    ->select([
                        'id',
                        'account_id',
                        'wa_group_id',
                        'wa_period_id',
                        'wa_position_id',
                        'notes',
                        'user_id',
                        'start_at',
                        'end_at',
                        'span_whole_period',
                        'day_of_week',
                        'sent_email',
                        'sent_sms',
                        'is_confirmed',
                        'has_replied',
                    ])
                    ->forUser(auth()->user())
                    ->isActiveOrFuture()
                    ->isPublished()
                    ->orderBy('start_at')
                    ->with('position:id,account_id,wa_group_id,name,notes,day_of_week,span_whole_period,restrict_user_selections_by_involvement_selections,number_of_users,involvement_category_id,involvement_area_id,involvement_subarea_id,sort_id,is_temporary')
                    ->get(),
            ]);
    }

    public function userConfirm(Pick $pick)
    {
        if (auth()->user()->id != $pick->user_id) {
            abort(404);
        }

        $pick->has_replied  = Carbon::now();
        $pick->is_confirmed = Carbon::now();
        $pick->save();

        return response(['pick' => $pick], 200);
    }

    public function userDecline(Pick $pick)
    {
        if (auth()->user()->id != $pick->user_id) {
            abort(404);
        }

        (new DeclineAssignment($pick))->decline();

        return response(['pick' => $pick], 200);
    }

    public function adminPeriods()
    {
        if (!auth()->user()->can('index', Group::class)) {
            abort(404);
        }

        $periods = Period::visibleTo(auth()->user())
            ->getActiveOrFuturePeriod()
            ->with([
                'positions',
                'picks'      => function ($query) {
                    $query->orderBy('start_at', 'asc');
                },
                'picks.position:id,wa_group_id,name,notes,day_of_week,span_whole_period,restrict_user_selections_by_involvement_selections,notes,is_blocking,is_temporary,sort_id',
                'group',
                'picks.user' => function ($query) {
                    $query->select([
                        'id',
                        'family_id',
                        DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                        'last_name',
                    ]);
                },
            ])
            ->get();

        return response(['periods' => $periods], 200);
    }

    public function adminGetQualifiedUsersForPick(Pick $pick)
    {
        if (!$pick || !auth()->user()->can('index', Group::class)) {
            abort(404);
        }

        $pick = Pick::visibleTo(auth()->user())->find($pick->id);

        if (!$pick) {
            abort(404);
        }

        $picks = $pick->position->getQualifiedUsers(auth()->user())->select([
            'id',
            'account_id',
            DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'), // 'first_name',
            'last_name',
            'family_id',
            'family_role',
            'birthdate',
        ])->get();

        return response($picks, 200);
    }

    public function adminUpdatePick(Pick $pick)
    {
        if (!$pick) {
            abort(404);
        }

        if (!auth()->user()->can('manageAssignments', Group::class)) {
            abort(404);
        }

        if (request()->get('response') == 'confirmed') {
            $pick->has_replied  = Carbon::now();
            $pick->is_confirmed = Carbon::now();
            $pick->save();
        } elseif (request()->get('response') == 'declined') {
            $pick->has_replied  = Carbon::now();
            $pick->is_confirmed = null;
            $pick->save();
        } elseif (request()->get('response') == 'no_response') {
            $pick->has_replied  = null;
            $pick->is_confirmed = null;
            $pick->save();
        }

        return response(['pick' => $pick], 200);
    }

    public function adminChangePickUser(Pick $pick, User $user)
    {
        $user = User::visibleTo(auth()->user())->find($user->id);

        if (!$user || !auth()->user()->can('manageAssignments', Group::class)) {
            abort(404);
        }

        $pick->user_id      = $user->id;
        $pick->has_replied  = null;
        $pick->sent_email   = null;
        $pick->is_confirmed = null;
        $pick->save();

        return response(['pick' => $pick], 200);
    }

    public function adminResendNotifications(Pick $pick)
    {
    }
}
