<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Base\Http\Responses\ApiSuccessResponse;
use App\Groups\Comment;
use App\Groups\File;
use App\Groups\Post;
use App\Groups\Reaction;
use App\Groups\Services\CreateComment;
use App\Groups\Services\CreateGroupFile;
use App\Groups\Services\CreatePost;
use App\Groups\Services\RecordPostReaction;
use App\Groups\Services\SendGroupSMS;
use App\Users\Group;
use App\Users\Photo;
use App\Users\Services\UpdateGroup;
use App\Users\Services\UpdateUser;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class GroupController extends Controller
{
    public function index()
    {
        return new ApiSuccessResponse(
            Group::viewableToUser(auth()->user())
                ->select([
                    'id',
                    'account_id',
                    'created_at',
                    'name',
                    'url_name',
                    'allow_individual_to_toggle',
                    'indicates_membership',
                    'indicates_visitor',
                    'is_hidden',
                    'is_suspended',
                    'allow_all_members_to_send',
                    'allow_lightpost_users_to_email',
                    'enable_posts',
                    'allow_members_to_post',
                    'allow_members_to_comment',
                    'allow_members_to_view_membership',
                    'uuid',
                    'is_default_visitor_group',
                    'is_default_member_group',
                ])
                ->with([
                    'usersWithoutSettings:id,family_id,first_name,last_name',
                    'users:id,first_name,last_name',
                    'senders:id,account_id,first_name,last_name',
                    'messageHandlers:id,user_group_id,message_type_id,name,address,reply_to,is_active,cost',
                    'messageHandlers.messageType:id,name,code,is_active,incurs_sending_fee',
                ])
                ->withCount('users')
                ->withUnreadNotificationCountForUser(auth()->user()->id)
                ->orderBy('name', 'ASC')
                ->get(),
        );
    }

    public function getGroupsNotifications()
    {
        return new ApiSuccessResponse(
            Group::viewableToUser(auth()->user())
                ->select([
                    'id',
                    'name',
                    'url_name',
                ])
                ->withUnreadNotificationCountForUser(auth()->user()->id)
                ->get(),
        );
    }

    public function view(Group $group)
    {
        $group_found = Group::visibleTo(auth()->user())
            ->where('id', $group->id)
            ->hasPostsEnabled()
            ->with([
                'usersWithoutSettings'        => function ($query) {
                    $query->select([
                        'users.id',
                        'account_id',
                        'family_id',
                        'first_name',
                        'last_name',
                        'birthdate',
                        'exclude_from_reports',
                        'status',
                        'is_active',
                    ])->orderBy('last_name', 'asc')
                        ->orderBy('first_name', 'asc');
                },
                'senders:id,account_id,first_name,last_name',
                'messageHandlers:id,user_group_id,message_type_id,name,address,reply_to,is_active,cost',
                'messageHandlers.messageType:id,name,code,is_active,incurs_sending_fee',
                'usersWithoutSettings.avatar' => function ($query2) {
                    $query2->select([
                        'user_id',
                        DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                        DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                    ]);
                },
            ])
//            ->with('users:id,account_id,family_id,first_name,last_name,birthdate,exclude_from_reports,status,is_active')
            ->withUnreadNotificationCountForUser(auth()->user()->id)
            ->withCount(['posts', 'users'])
            ->first();

        return response()->json([
            'data' => $group_found,
        ]);
    }

    public function posts(Group $group)
    {
        $group_found = Group::visibleTo(auth()->user())
            ->where('id', $group->id)
            ->hasPostsEnabled()
            ->withCount('posts')
            ->first();

        if (!$group_found) {
            return response()->json(['data' => []]);
        }

        $posts = $group_found
            ->posts()
            ->select([
                'id',
                'account_id',
                'user_group_id',
                //                'user_group_post_id',
                'creator_id',
                'created_at',
                'deleted_at',
                'published_at',
                'last_active_at',
                'last_edited_at',
                'title',
                'url_title',
                'content',
                'link',
                'allow_comments',
                'is_shared',
                'is_pinned',
                'is_hidden',
                'is_flagged',
                'is_poll',
                'is_link',
                'is_rsvp',
            ])
            ->reorder() // This will clear any existing order clauses
            ->Ordered()
            ->withCount([
                'comments',
                'reactions',
                'likes',
                'loves',
                'cares',
                'prays',
                'laughs',
                'files',
                'documents',
                'images',
            ])
            ->withUnreadNotificationCountForUser(auth()->user()->id)
            ->with([
                //                'comments:id,user_group_id,user_group_post_id,user_group_post_comment_id,creator_id,created_at,content,is_pinned,is_hidden,is_flagged',
                'creator'        => function ($query) {
                    $query->select([
                        'id',
                        'account_id',
                        DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                        'last_name',
                        'family_id',
                    ]);
                },
                'creator.avatar' => function ($query2) {
                    $query2->select([
                        'user_id',
                        DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                        DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                    ]);
                },
                'files'          => function ($query) {
                    $query->select([
                        'account_id',
                        'user_group_id',
                        'user_group_post_id',
                        'user_group_post_comment_id',
                        'user_id',
                        'storage_service',
                        'file_size',
                        //                        'data_separator',
                        'file_folder',
                        //                        'file_id',
                        'file_name_original',
                        'file_name',
                        'file_extension',
                        'file_type',
                        //                        'file_sha1',
                        'remote_gif_url',
                        'width',
                        'height',
                        'has_1024',
                        'has_512',
                        'has_256',
                        'is_remote_gif',
                        'is_image',
                        'is_video',
                        'is_document',
                        'force_download',
                        'is_flagged',
                        'views',
                        'downloads',
                        'sort_id',
                        DB::raw((new File())->getMySQLConcatString() . ' AS `url`'),
                        DB::raw((new File())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                        DB::raw((new File())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                    ]);
                },
            ])
            ->paginate(15);

        return response()->json($posts);
    }

    public function post(Group $group, Post $post)
    {
        $post = Post::visibleTo(auth()->user())
            ->where('id', $post->id)
            ->select([
                'id',
                'account_id',
                'user_group_id',
                //                'user_group_post_id',
                'creator_id',
                'created_at',
                'deleted_at',
                'published_at',
                'last_active_at',
                'last_edited_at',
                'title',
                'url_title',
                'content',
                'link',
                'allow_comments',
                'is_shared',
                'is_pinned',
                'is_hidden',
                'is_flagged',
                'is_poll',
                'is_link',
                'is_rsvp',
            ])
            ->withCount([
                'comments',
                'reactions',
                'likes',
                'loves',
                'cares',
                'prays',
                'laughs',
                'files',
                'documents',
                'images',
            ])
            ->withUnreadNotificationCountForUser(auth()->user()->id)
            ->with([
                'reactions:user_id,user_group_post_id,user_group_post_comment_id,is_like,is_love,is_care,is_pray,is_laugh',
                'reactions.user'          => function ($query) {
                    $query->select([
                        'id',
                        'account_id',
                        DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                        'last_name',
                    ]);
                },
                'comments.reactions:user_id,user_group_post_id,user_group_post_comment_id,is_like,is_love,is_care,is_pray,is_laugh',
                'comments'                => function ($query) {
                    $query->select([
                        'id',
                        'user_group_id',
                        'user_group_post_id',
                        'user_group_post_comment_id',
                        'creator_id',
                        'created_at',
                        'content',
                        'is_pinned',
                        'is_hidden',
                        'is_flagged',
                    ])
                        ->withCount([
                            'reactions',
                            'likes',
                            'loves',
                            'cares',
                            'prays',
                            'laughs',
                        ]);
                },
                'comments.creator'        => function ($query) {
                    $query->select([
                        'id',
                        'account_id',
                        DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                        'last_name',
                        'family_id',
                    ]);
                },
                'comments.creator.avatar' => function ($query2) {
                    $query2->select([
                        'user_id',
                        DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                        DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                    ]);
                },
                'creator'                 => function ($query) {
                    $query->select([
                        'id',
                        'account_id',
                        DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                        'last_name',
                        'family_id',
                    ]);
                },
                'creator.avatar'          => function ($query2) {
                    $query2->select([
                        'user_id',
                        DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                        DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                    ]);
                },
                'files'                   => function ($query) {
                    $query->select([
                        'account_id',
                        'user_group_id',
                        'user_group_post_id',
                        'user_group_post_comment_id',
                        'user_id',
                        'storage_service',
                        'file_size',
                        //                        'data_separator',
                        'file_folder',
                        //                        'file_id',
                        'file_name_original',
                        'file_name',
                        'file_extension',
                        'file_type',
                        //                        'file_sha1',
                        'remote_gif_url',
                        'width',
                        'height',
                        'has_1024',
                        'has_512',
                        'has_256',
                        'is_remote_gif',
                        'is_image',
                        'is_video',
                        'is_document',
                        'force_download',
                        'is_flagged',
                        'views',
                        'downloads',
                        'sort_id',
                        DB::raw((new File())->getMySQLConcatString() . ' AS `url`'),
                        DB::raw((new File())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                        DB::raw((new File())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                    ]);
                },
            ])
            ->first();

        // For documents, get a presigned URL that will expire.
        // Documents are not public read.
        $replace_files_array = [];
        $found_docs          = 0;
        foreach ($post->files as $file) {
            if ($file->is_document) {
                $file->url = $file->getTemporaryUrl(120);
                $found_docs++;
            }

            $replace_files_array[] = $file;
        }

        // Only replace the files array if we found documents. Micro-optimization for the win.
        if ($found_docs > 0) {
            $post->files = $replace_files_array;
        }

        if (!$post) {
            abort(404);
        }

        // Indicate if we can edit this post.
        $post->can_edit = auth()->user()->can('edit', $post);

        return response()->json([
            'data' => $post,
        ]);
    }

    /**
     * request()->get('images') payload example:
     * [{
     * "base64": "",
     * "fileName": "2945F606-4936-4D21-9B72-5EF41C0076CB.jpg",
     * "fileSize": 1894871,
     * "height": 1062,
     * "type": "image/jpg",
     * "uri": "file:///Users/<USER>/tmp/2945F606-4936-4D21-9B72-5EF41C0076CB.jpg",
     * "width": 1600
     * }]
     *
     * "documents" payload example: https://docs.expo.dev/versions/latest/sdk/document-picker/#documentpickerasset
     * {"base64","size","name","mimeType","uri"}
     *
     */
    public function submitPost(Group $group)
    {
        if (!auth()->user()->can('post', $group)) {
            return abort(403);
        }

        $validator = Validator::make(request()->all(), [
            'content' => 'required',
        ]);

        if ($validator->fails() || !$group) {
            Log::error('Validation error by user ' . auth()->user()->id . ' trying to post a Group Post.');
            return abort(422);
        }

        try {
            $new_post = (new CreatePost())
                ->createdBy(auth()->user())
                ->forGroup($group)
                ->create(
                    request()->only([
                        'title',
                        'url_title',
                        'content',
                        'allow_comments',
                    ])
                );
        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }

        /**
         * Android Example Item:
         * array (
         * 'width' => 296,
         * 'rotation' => NULL,
         * 'height' => 170,
         * 'exif' =>
         * array (
         *   'ImageWidth' => 0,
         *   'Orientation' => 0,
         *   'ImageLength' => 0,
         *   'LightSource' => 0,
         * ),
         * 'duration' => NULL,
         * 'type' => 'image',
         * 'base64' => ....
         * 'uri' => 'file:///data/user/0/host.exp.exponent/cache/ExperienceData/%2540tinybitfarm%252Flightpost/ImagePicker/e325ae8b-21b8-4ccf-a06b-84ae1309a7f6.png',
         * 'assetId' => NULL,
         * ),
         */
        if (request()->has('images') && is_array(request()->get('images'))) {
            foreach (request()->get('images') as $image) {
                (new CreateGroupFile())
                    ->forAccount(auth()->user()->account)
                    ->byUser(auth()->user())
                    ->forPost($new_post)
                    ->setFileName(Arr::get($image, 'fileName'))
                    ->setFileSize(Arr::get($image, 'fileSize'))
                    ->setFileType(Arr::get($image, 'fileType'))
                    ->setAttributes([
                        'width'  => Arr::get($image, 'width'),
                        'height' => Arr::get($image, 'height'),
                    ])
                    ->create(Arr::get($image, 'base64'), true);
            }
        }

        if (request()->has('documents') && is_array(request()->get('documents'))) {
            foreach (request()->get('documents') as $document) {
                (new CreateGroupFile())
                    ->forAccount(auth()->user()->account)
                    ->byUser(auth()->user())
                    ->forPost($new_post)
                    ->setFileName(Arr::get($document, 'name'))
                    ->setFileSize(Arr::get($document, 'size'))
                    ->setFileType(Arr::get($document, 'mimeType'))
                    ->create(Arr::get($document, 'base64'), true);
            }
        }

        return response()->json([
            'data' => $new_post,
        ]);
    }

    public function submitEditPost(Group $group, Post $post)
    {
        if (!auth()->user()->can('edit', $post)) {
            return abort(403);
        }

        $validator = Validator::make(request()->all(), [
            'content' => 'required',
        ]);

        if ($validator->fails()) {
            return abort(422);
        }

        try {
            $post->content = request()->get('content');

            $post->save();
        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }

        return response()->json([
            'data' => $post,
        ]);
    }

    public function submitComment(Group $group, Post $post)
    {
        if (!auth()->user()->can('comment', $post)) {
            return abort(403);
        }

        $validator = Validator::make(request()->all(), [
            'comment' => 'required',
        ]);

        if ($validator->fails()) {
            return abort(422);
        }

        try {
            $new_comment = (new CreateComment())
                ->forPost($post)
                ->createdBy(auth()->user())
                ->withComment(request()->get('comment'))
                ->create();
        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }

        return response()->json([
            'comment' => [
                'name'       => auth()->user()->name,
                'created_at' => $new_comment->created_at,
                'content'    => nl2br($new_comment->content),
            ],
        ]);
    }

    public function getGroupSettings(Group $group)
    {
        $settings = auth()->user()->groups->where('id', $group->id)->first()?->settings;

        if (!$settings) {
            return response(null, 404);
        }

        return response()->json([
            'data' => [
                'receive_group_emails'                                => $settings->receive_group_emails,
                'receive_group_sms'                                   => $settings->receive_group_sms,
                'receive_group_voice'                                 => $settings->receive_group_voice,
                'receive_group_post_email_summaries'                  => $settings->receive_group_post_email_summaries,
                'receive_group_post_mobile_notifications'             => $settings->receive_group_post_mobile_notifications,
                'receive_all_group_post_comment_mobile_notifications' => $settings->receive_all_group_post_comment_mobile_notifications,
                'receive_group_own_post_comment_mobile_notifications' => $settings->receive_group_own_post_comment_mobile_notifications,
                'is_favorite'                                         => $settings->is_favorite ? 1 : 0, // Non of the other values return null on 0, but this one does??
                'is_admin'                                            => $settings->is_admin ? 1 : 0, // Non of the other values return null on 0, but this one does??
            ],
        ]);
    }

    public function submitGroupSettings(Group $group)
    {
        // Temp logging to debug settings not saving for specific users.
        Log::info('GroupController::submitGroupSettings', [
            'request' => request()->all(),
            'user_id' => auth()->user()->id,
        ]);

        $settings = auth()->user()->groups->where('id', $group->id)->first()?->settings;

        if (!$settings) {
            // Temp logging to debug settings not saving for specific users.
            Log::info('GroupController::submitGroupSettings -- $settings not found.', [
                'request'  => request()->all(),
                'settings' => $settings,
                'user_id'  => auth()->user()->id,
            ]);

            return response(null, 404);
        }

        try {
            (new UpdateUser(auth()->user()))
                ->updateExistingGroup($group->id, [
                    'receive_group_emails'                                => request('receive_group_emails', $settings->receive_group_emails) ? 1 : 0,
                    'receive_group_sms'                                   => request('receive_group_sms', $settings->receive_group_sms) ? 1 : 0,
                    'receive_group_voice'                                 => request('receive_group_voice', $settings->receive_group_voice) ? 1 : 0,
                    'receive_group_post_email_summaries'                  => request('receive_group_post_email_summaries', $settings->receive_group_post_email_summaries) ? 1 : 0,
                    'receive_group_post_mobile_notifications'             => request('receive_group_post_mobile_notifications', $settings->receive_group_post_mobile_notifications) ? 1 : 0,
                    'receive_all_group_post_comment_mobile_notifications' => request('receive_all_group_post_comment_mobile_notifications', $settings->receive_all_group_post_comment_mobile_notifications) ? 1 : 0,
                    'receive_group_own_post_comment_mobile_notifications' => request('receive_group_own_post_comment_mobile_notifications', $settings->receive_group_own_post_comment_mobile_notifications) ? 1 : 0,
                    'is_favorite'                                         => request('is_favorite', $settings->is_favorite) ? 1 : 0,
                ])
                ->update([]);
        } catch (\Exception $e) {
            Log::error('GroupController::submitGroupSettings', [
                'request' => request()->all(),
                'user_id' => auth()->user()->id,
                'error'   => $e->getMessage(),
                'e'       => $e,
            ]);

            abort(400, $e->getMessage());
        }

        return response()->json([
            'data' => [
                'receive_group_emails'                                => request('receive_group_emails', $settings->receive_group_emails) ? 1 : 0,
                'receive_group_sms'                                   => request('receive_group_sms', $settings->receive_group_sms) ? 1 : 0,
                'receive_group_voice'                                 => request('receive_group_voice', $settings->receive_group_voice) ? 1 : 0,
                'receive_group_post_email_summaries'                  => request('receive_group_post_email_summaries', $settings->receive_group_post_email_summaries) ? 1 : 0,
                'receive_group_post_mobile_notifications'             => request('receive_group_post_mobile_notifications', $settings->receive_group_post_mobile_notifications) ? 1 : 0,
                'receive_all_group_post_comment_mobile_notifications' => request('receive_all_group_post_comment_mobile_notifications', $settings->receive_all_group_post_comment_mobile_notifications) ? 1 : 0,
                'receive_group_own_post_comment_mobile_notifications' => request('receive_group_own_post_comment_mobile_notifications', $settings->receive_group_own_post_comment_mobile_notifications) ? 1 : 0,
                'is_favorite'                                         => request('is_favorite', $settings->is_favorite) ? 1 : 0,
                'is_admin'                                            => $settings->is_admin ? 1 : 0,
            ],
        ]);
    }

    public function submitSendSMS(Group $group)
    {
        try {
            // This will auto-select a handler if none is given.
            (new SendGroupSMS())->withMessage(request('message'))
                ->fromUser(auth()->user())
                ->forGroup($group)
                ->send();
        } catch (\Exception $e) {
            Log::error($e);

            abort(406);
        }

        return response()->json('OK');
    }

    public function submitTogglePinPost(Group $group, Post $post, $pinned = 1)
    {
        $this->authorize('edit', $post);

        if ($pinned) {
            $post->is_pinned = true;
            $post->save();
        } else {
            $post->is_pinned = false;
            $post->save();
        }

        return response()->json('OK');
    }

    public function submitPostReaction(Group $group, Post $post, $type = 'like')
    {
        (new RecordPostReaction())
            ->forPost($post)
            ->forUser(auth()->user())
            ->withType($type)
            ->record();

        return response()->json('OK');
    }

    public function submitCommentReaction(Group $group, Post $post, Comment $comment, $type = 'like')
    {
        // Delete any previous reaction
        if ($existing_reaction = Reaction::where('user_id', auth()->user()->id)->where('user_group_post_comment_id', $comment->id)->first()) {
            $existing_reaction->delete();
        }

        // Only add a new reaction if we're not simply deleting an existing one.
        if ($type !== 'delete') {
            // If our type is an invalid type, default to a like.  Prevents SQL injection issues.
            if (!in_array('is_' . $type, Reaction::$types)) {
                $type = 'like';
            }

            Reaction::create([
                'user_id'                    => auth()->user()->id,
                'user_group_post_id'         => $post->id,
                'user_group_post_comment_id' => $comment->id,
                'is_' . $type                => 1,
            ]);
        }

        return response()->json('OK');
    }

    public function addUser(Group $group)
    {
        $validator = Validator::make(request()->all(), [
            'user_id' => 'required',
        ]);

        if ($validator->fails()) {
            return abort(422);
        }

        $user        = User::visibleTo(auth()->user())->find(request('user_id'));
        $found_group = Group::viewableToUser(auth()->user())->first();

        if (!$user || !$found_group) {
            return abort(422);
        }

        try {
            (new UpdateUser($user))
                ->addGroupsById([$group->id])
                ->update();

            (new UpdateGroup($group))->touch();
        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }

        return response()->json('OK');
    }

    public function removeUser(Group $group)
    {
        $validator = Validator::make(request()->all(), [
            'user_id' => 'required',
        ]);

        if ($validator->fails()) {
            return abort(422);
        }

        $user        = User::visibleTo(auth()->user())->find(request('user_id'));
        $found_group = Group::viewableToUser(auth()->user())->first();

        if (!$user || !$found_group) {
            return abort(422);
        }

        try {
            (new UpdateUser($user))
                ->removeGroupsById([$group->id])
                ->update();

            (new UpdateGroup($group))->touch();
        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }

        return response()->json('OK');
    }

    public function deletePost(Group $group, Post $post)
    {
        // Authorized at the Route
        // $this->authorize('delete', $post);

        try {
            $post->delete();
        } catch (\Exception $e) {
            Log::error($e);

            abort(403);
        }

        return response()->json('OK');
    }

    public function deleteComment(Group $group, Post $post, Comment $comment)
    {
        // Authorized at the Route
        // $this->authorize('delete', $comment);

        try {
            $comment->delete();
        } catch (\Exception $e) {
            Log::error($e);

            abort(403);
        }

        return response()->json('OK');
    }
}
