<?php

namespace App\MobileApi\V2\Controllers;

use App\Accounts\FinanceBucket;
use App\Base\Http\Controllers\Controller;
use App\Finance\Transaction;
use App\MobileApi\V2\Services\CreateMobileApiError;
use App\Users\PaymentSchedule;
use App\Users\Services\ChargePaymentMethod;
use App\Users\Services\CreatePaymentMethod;
use App\Users\Services\CreatePaymentSchedule;
use App\Users\Services\DeletePaymentMethod;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Money\Currencies\ISOCurrencies;
use Money\Parser\DecimalMoneyParser;
use Stripe\Customer;
use Stripe\Stripe;

class GivingController extends Controller
{
    public function paymentMethods()
    {
        return response()->json(
            auth()->user()->paymentMethods()->select([
                'id',
                'user_id',
                'family_id',
                'created_at',
                //            'stripe_payment_method_id',
                //            'stripe_type',
                'stripe_object',
                'type',
                'label',
                'expire_month',
                'expire_year',
                'last4',
                'is_family',
                'is_shared',
            ])
                ->orderBy('created_at', 'DESC')
                ->get()
        );
    }

    public function paymentSchedules()
    {
        return response()->json(
            auth()->user()->paymentSchedules()->select([
                'id',
                'account_id',
                'created_at',
                'last_run_at',
                'user_id',
                'user_payment_method_id',
                'account_finance_bucket_id',
                'amount',
                'recur_start_at',
                'recur_end_at',
                'recur_frequency',
                'recur_day_of_week',
                'recur_day_of_month',
            ])
                ->with([
                    'paymentMethod:id,user_id,family_id,type,stripe_object,label,expire_month,expire_year,last4,is_family,is_shared',
                    'financeBucket:id,name,url_name,account_code,is_contribution_bucket,is_reimbursement_bucket,is_expense,is_income,is_hidden,sort_id',
                ])
                ->orderBy('created_at', 'DESC')
                ->get()
        );
    }

    public function contributionHistory()
    {
        return response()->json(
            auth()->user()->contributions()->select([
                'id',
                'user_id',
                'created_at',
                'user_payment_method_id',
                'account_contribution_bucket_id',
                'account_finance_bucket_id',
                'provider',
                'provider_payment_id',
                'stripe_charge_id',
                'stripe_charge_object',
                'stripe_status',
                'stripe_balance_transaction_id',
                'cleared_at',
                'captured_at',
                'disputed_at',
                'deposited_at',
                'applied_at',
                'type',
                'title',
                'description',
                'amount',
                'amount_fee',
                'amount_refunded',
                'is_hidden',
                'is_contribution',
                'is_payment',
                'is_reimbursement',
                'status',
            ])
                ->with('paymentMethod:id,user_id,family_id,created_at,type,label,expire_month,expire_year,last4,is_family,is_shared')
                ->orderBy('created_at', 'DESC')
                ->get()
        );
    }

    public function submitCreatePaymentSchedule()
    {
        $validator = Validator::make(request()->all(), [
            'amount'                    => 'required|numeric|max:9999.99', // In DOLLARS
            'account_finance_bucket_id' => 'required|integer',
            'user_payment_method_id'    => 'required|integer',
        ]);

        if ($validator->fails()) {
            return (new CreateMobileApiError())
                ->withMessage('Some form fields failed validation.')
                ->withFields($validator->errors()->toArray())
                ->withResponseCode(422)
                ->response();
        }

        // Convert to cents.
        $amount         = (new DecimalMoneyParser((new ISOCurrencies())))->parse(request('amount'), 'USD')->getAmount();
        $finance_bucket = auth()->user()->account->financeBuckets->find(request('account_finance_bucket_id'));
        $payment_method = auth()->user()->paymentMethods->find(request('user_payment_method_id'));

        if (auth()->user()->paymentSchedules->count() >= 3) {
            return (new CreateMobileApiError())
                ->withMessage('Oops! You can only have a maximum of <strong>three</strong> recurring payment schedules at a time.')
                ->response();
        }

        try {
            (new CreatePaymentSchedule())
                ->forUser(auth()->user())
                ->setFinanceBucket($finance_bucket)
                ->withPaymentMethod($payment_method)
                ->setAmountInCents($amount)
                ->setInterval('WEEKLY')
                ->setDayOfWeek(7)
                ->create();
        } catch (\Exception $e) {
            Log::error($e);

            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        return response()->json(['Success! Recurring contribution has been scheduled.'], 200);
    }

    public function submitDeletePaymentSchedule(PaymentSchedule $payment_schedule)
    {
        if (!$payment_schedule) {
            return (new CreateMobileApiError())
                ->withMessage('Missing a payment schedule.')
                ->withResponseCode(422)
                ->response();
        }

        // Just double check this payment schedule is ours.
        $payment_schedule = auth()->user()->paymentSchedules->find($payment_schedule->id);

        try {
            $payment_schedule->delete();
        } catch (\Exception $e) {
            Log::error($e);

            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        return response()->json(['Success! Recurring contribution has been deleted.'], 200);
    }

    // Get Payment Intent for Stripe
    public function addPaymentMethod()
    {
        $user = auth()->user();

        if (!auth()->user()->id) {
            return response()->json(null, 401);
        }

        if (!auth()->user()->can('addPaymentMethods', Transaction::class)) {
            return (new CreateMobileApiError())
                ->withResponseCode(401)
                ->withMessage('Account is missing API key or permission to add payment methods.')
                ->response();
        }

        \Stripe\Stripe::setApiKey(config('services.stripe.connect.secret'));

        // If we are not in Stripe yet, create that customer.
        if (!$user->stripe_customer_id) {
            try {
                $customer = \Stripe\Customer::create([
                    'email'       => optional($user->getBestEmail())->email,
                    'name'        => $user->name,
                    'phone'       => optional($user->getBestPhone())->number,
                    'description' => 'User created through Lightpost.',
                ], [
                    'stripe_account' => auth()->user()->account->stripe_account_id,
                ]);

                $user->stripe_customer_id = $customer->id;
                $user->stripe_account_id  = auth()->user()->account->stripe_account_id;

                $user->save();
            } catch (\Exception $e) {
                Log::error($e);
                return (new CreateMobileApiError())
                    ->withMessage($e->getMessage())
                    ->response();
            }
        }

        // Setup our intent to add a card or bank account.
        try {
            $intent = \Stripe\SetupIntent::create([
                'customer'                  => $user->stripe_customer_id,
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
                'usage'                     => 'off_session',
            ], [
                'stripe_account' => auth()->user()->account->stripe_account_id,
            ]);
        } catch (\Exception $e) {
            Log::error($e);
            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        return response()->json([
            'intent' => $intent,
        ]);
    }

    public function submitAddPaymentMethod()
    {
        $account = auth()->user()->account;
        $user    = auth()->user();

        if (!auth()->user()->id) {
            return response()->json(null, 401);
        }

        if (!auth()->user()->can('addPaymentMethods', Transaction::class)) {
            return (new CreateMobileApiError())
                ->withResponseCode(401)
                ->withMessage('Account is missing API key or permission to add payment methods.')
                ->response();
        }

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            if (!$user->stripe_customer_id) {
                try {
                    $customer = \Stripe\Customer::create([
                        'email'       => optional($user->getBestEmail())->email,
                        'name'        => $user->name,
                        'phone'       => optional($user->getBestPhone())->number,
                        'description' => 'User created through Lightpost.',
                    ], [
                        'stripe_account' => $account->stripe_account_id,
                    ]);

                    $user->stripe_customer_id = $customer->id;
                    $user->stripe_account_id  = $account->stripe_account_id;

                    $user->save();
                } catch (\Exception $e) {
                    Log::error($e);
                    return (new CreateMobileApiError())
                        ->withMessage($e->getMessage())
                        ->response();
                }
            }

            $source = null;
            // Create our source in Stripe.
            $source = Customer::createSource($user->stripe_customer_id, [
                'source' => request('token') ?: request('stripe_token'),
            ], [
                'stripe_account' => $account->stripe_account_id,
            ]);

            if ($source) {
                (new CreatePaymentMethod())->forUser($user)
                    ->setStripePaymentMethodId($source->id)
                    ->setStripeObject($source)
                    ->setStripeType($source->object)
                    ->setType($source->object)
                    ->setLast4($source->last4)
                    ->setExpireDate($source->exp_month, $source->exp_year)
                    ->create();
            }
        } catch (\Exception $e) {
            Log::error($e);

            // Attempt to delete source.
            try {
                Customer::deleteSource($user->stripe_customer_id, optional($source)->id, [], [
                    'stripe_account' => auth()->user()->account->stripe_account_id,
                ]);
            } catch (\Exception $e2) {
                Log::error('MobileAPI:submitAddPaymentMethod -- Could not delete Stripe source after an error occurred. ' . $e2->getMessage());
            }

            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        return response()->json(['Success!'], 200);
    }

    public function submitDeletePaymentMethod($payment_method)
    {
        $user = auth()->user();

        if ($user->account_id !== $payment_method->account_id) {
            abort(404);
        }

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            (new DeletePaymentMethod($payment_method))->delete();
        } catch (\Exception $e) {
            Log::error($e);

            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        return response()->json(['Deleted.'], 200);
    }

    public function verifyPaymentMethod($payment_method)
    {
        $account = auth()->user()->account;
        $user    = auth()->user();

        if ($user->account_id !== $payment_method->account_id) {
            abort(403);
        }

        // Make our deposit amounts integers, if they are not already.
        $deposit_1 = request()->input('deposit_1') >= 1 ? request()->input('deposit_1') : request()->input('deposit_1') * 100;
        $deposit_2 = request()->input('deposit_2') >= 1 ? request()->input('deposit_2') : request()->input('deposit_2') * 100;

        Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            $bank_account = \Stripe\Customer::retrieveSource(
                $payment_method->user->stripe_customer_id,
                $payment_method->stripe_payment_method_id,
                [],
                [
                    'stripe_account' => $account->stripe_account_id,
                ]
            );

            $bank_account->verify([
                'amounts' => [
                    $deposit_1,
                    $deposit_2,
                ],
            ]);

            $bank_account = \Stripe\Customer::retrieveSource(
                $payment_method->user->stripe_customer_id,
                $payment_method->stripe_payment_method_id,
                [],
                [
                    'stripe_account' => $account->stripe_account_id,
                ]
            );

            $payment_method->stripe_object = json_encode($bank_account);
            $payment_method->save();
        } catch (\Exception $e) {
            Log::error('GivingController::verifyPaymentMethod - Could not verify payment method.', [
                'payment_method_id' => $payment_method->id,
                'user_id'           => $user->id,
                'error'             => $e->getMessage(),
                'e'                 => $e,
            ]);

            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        Log::info('MobileApi:VerifyBankAccount -- success for payment method: ' . $payment_method->id);

        return response()->json(['Success! Bank account verified.'], 200);
    }

    // account_contribution_bucket_id === legacy naming for account_finance_bucket_id.
    public function submitGive($payment_method)
    {
        $user      = auth()->user();
        $cache_key = $user->account_id . ':' . $user->id . ':submittedContributionRecently';

        if ($user->account_id !== $payment_method->account_id) {
            abort(404);
        }

        $validator = Validator::make(request()->all(), [
            'amount'                         => 'required|numeric',
            'account_contribution_bucket_id' => 'sometimes|required|integer',
            'account_finance_bucket_id'      => 'sometimes|required|integer',
            //            'user_payment_method_id'         => 'required|integer',
        ]);

        if ($validator->fails()) {
            return (new CreateMobileApiError())
                ->withMessage('Some form fields failed validation.')
                ->withFields($validator->errors()->toArray())
                ->withResponseCode(422)
                ->response();
        }

        // If we've tried in the last 15 seconds, don't try again.
        if (Cache::has($cache_key)) {
            return (new CreateMobileApiError())
                ->withMessage('Please wait at least 15 seconds between contributions.')
                ->withResponseCode(404)
                ->response();
        }

        // Put our timer in place to make sure we don't submit a contribution twice in a row.
        Cache::put($cache_key, true, 15);

        try {
            // Convert amount to cents.
            $amount = (new DecimalMoneyParser((new ISOCurrencies())))->parse(request('amount'), 'USD')->getAmount();
            $bucket = FinanceBucket::find(request('account_contribution_bucket_id', request('account_finance_bucket_id')));

            Log::info('MobileAPI:Give::give() -- Attempt', [
                'user_id' => $user->id,
            ]);

            $payment = (new ChargePaymentMethod())
                ->setFinanceBucket($bucket)
                ->setPaymentMethod($payment_method)
                ->setAmountInCents($amount)
                ->charge();
        } catch (\Exception $e) {
            Log::error($e);
            Log::warning('MobileAPI:Give::give() -- Possible payment without a recorded contribution record! User ' . $user->id);

            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        Log::info('MobileApi:SubmitGive -- Success', [
            'user_id'         => $user->id,
            'user_payment_id' => $payment->id,
        ]);

        return response()->json('Success! You have successfully completed a contribution.', 200);
    }
}
