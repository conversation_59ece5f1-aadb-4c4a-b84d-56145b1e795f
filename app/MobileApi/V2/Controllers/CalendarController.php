<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Base\Http\Responses\ApiErrorResponse;
use App\Base\Http\Responses\ApiSuccessResponse;
use App\Calendars\Calendar;
use App\Calendars\EventOccurrence;
use App\Calendars\Services\CalendarFeed;
use App\Calendars\Services\UpdateEventOccurrenceResponses;
use App\Users\Photo;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CalendarController extends Controller
{
    public function index()
    {
        return response()->json(
            Calendar::visibleTo(Auth::user())
                ->notHidden()
                ->get()
        );
    }

    public function settings()
    {
        return response()->json([
            'feeds' => [
                'all_events' => (new CalendarFeed())->forUser(auth()->user())->getFeedUrl(),
                //                'calendars'          => [],
            ],
        ]);
    }

    public function allEventOccurrences()
    {
        $calendars = Calendar::visibleTo(Auth::user())
            ->notHidden()
            ->get();

        $events = EventOccurrence::visibleTo(Auth::user())
            ->whereIn('calendar_id', $calendars->pluck('id'))
            ->where('start_at', '>', Carbon::now()->setTimezone(Auth::user()->account->timezone)->setDay(1)->setTime(0, 0, 0))
            ->where('end_at', '<', Carbon::now()->add('18 months'))
            ->orderBy('start_at', 'ASC')
            ->select([
                'id',
                'uuid',
                'title',
                'location',
                'overview',
                'description',
                'is_all_day',
                'start_at',
                'end_at',
                'start_at_date',
                'end_at_date',
                'start_at_time',
                'end_at_time',
                'account_id',
                'calendar_id',
                'calendar_event_id',
                'user_group_id',
                'is_hidden',
                'is_group_only',
                'enable_responses',
                'show_responses',
                'enable_payments',
                'owner_user_id',
            ])
            ->with([
                'event:id,uuid,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
            ])
            ->get();

        $final_events = [];

        foreach ($events as $event) {
            $cur_start_at = $event->start_at->setTimezone(Auth::user()->account->timezone);
            $cur_end_at   = $event->end_at->setTimezone(Auth::user()->account->timezone);

            $final_events[$cur_start_at->format('Ymd')]['date'] = $cur_start_at->format('Y-m-d');

            // If we span multiple days
            if ($cur_start_at->format('Y-m-d') != $cur_end_at->format('Y-m-d')) {
                $final_events[$cur_start_at->format('Ymd')]['events'][] = $event;

                $cloned = clone $event;

                while ($cloned->start_at->format('Y-m-d') < $cur_end_at->format('Y-m-d')) {
                    $cloned->start_at                                           = $cloned->start_at->addDays(1);
                    $final_events[$cloned->start_at->format('Ymd')]['date']     = $cloned->start_at->format('Y-m-d');
                    $final_events[$cloned->start_at->format('Ymd')]['events'][] = $cloned;
                }
            } else {
                $final_events[$cur_start_at->format('Ymd')]['events'][] = $event;
            }
        }

        $final_events = array_values($final_events);

        return response()->json($final_events);
    }

    public function currentWeekEventOccurrences()
    {
        $calendars = Calendar::visibleTo(Auth::user())->notHidden()->get();

        $events = EventOccurrence::visibleTo(Auth::user())
            ->whereIn('calendar_id', $calendars->pluck('id'))
            ->where('start_at', '>', Carbon::now()->startOfWeek())
            ->where('end_at', '<', Carbon::now()->endOfWeek())
            ->orderBy('start_at', 'ASC')
            ->select([
                'id',
                'start_at',
                'end_at',
                'start_at_date',
                'end_at_date',
                'start_at_time',
                'end_at_time',
                'account_id',
                'calendar_id',
                'calendar_event_id',
                'user_group_id',
                'is_hidden',
                'is_group_only',
                'enable_responses',
                'enable_payments',
            ])
            ->with([
                'event:id,uuid,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
            ])
            ->get();

        $final_events = [];

        foreach ($events as $event) {
            $final_events[$event->start_at->format('Ymd')]['date'] = $event->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d');

            // If we span multiple days
            if ($event->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d') != $event->end_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d')) {
                $final_events[$event->start_at->format('Ymd')]['events'][] = $event;
                $cloned                                                    = clone $event;
                while ($cloned->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d') < $event->end_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d')) {
                    $cloned->start_at                                           = $cloned->start_at->addDays(1);
                    $final_events[$cloned->start_at->format('Ymd')]['date']     = $cloned->start_at->format('Y-m-d');
                    $final_events[$cloned->start_at->format('Ymd')]['events'][] = $cloned;
                }
            } else {
                $final_events[$event->start_at->format('Ymd')]['events'][] = $event;
            }
        }

        $final_events = array_values($final_events);

        return response()->json($final_events);
    }

    public function currentMonthEventOccurrences()
    {
        $calendars = Calendar::visibleTo(Auth::user())->notHidden()->get();

        $events = EventOccurrence::visibleTo(Auth::user())
            ->whereIn('calendar_id', $calendars->pluck('id'))
            ->where('start_at', '>', Carbon::now()->startOfMonth())
            ->where('end_at', '<', Carbon::now()->endOfMonth())
            ->orderBy('start_at', 'ASC')
            ->select([
                'id',
                'start_at',
                'end_at',
                'start_at_date',
                'end_at_date',
                'start_at_time',
                'end_at_time',
                'account_id',
                'calendar_id',
                'calendar_event_id',
                'user_group_id',
                'is_hidden',
                'is_group_only',
                'enable_responses',
                'enable_payments',
            ])
            ->with([
                'event:id,uuid,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
            ])
            ->get();

        $final_events = [];

        foreach ($events as $event) {
            $final_events[$event->start_at->format('Ymd')]['date'] = $event->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d');

            // If we span multiple days
            if ($event->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d') != $event->end_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d')) {
                $final_events[$event->start_at->format('Ymd')]['events'][] = $event;
                $cloned                                                    = clone $event;
                while ($cloned->start_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d') < $event->end_at->setTimezone(Auth::user()->account->timezone)->format('Y-m-d')) {
                    $cloned->start_at                                           = $cloned->start_at->addDays(1);
                    $final_events[$cloned->start_at->format('Ymd')]['date']     = $cloned->start_at->format('Y-m-d');
                    $final_events[$cloned->start_at->format('Ymd')]['events'][] = $cloned;
                }
            } else {
                $final_events[$event->start_at->format('Ymd')]['events'][] = $event;
            }
        }

        $final_events = array_values($final_events);

        return response()->json($final_events);
    }

    public function viewEventOccurrence(EventOccurrence $eventOccurrence)
    {
        if (!$eventOccurrence->id) {
            return abort(404);
        }

        $eventOccurrence = EventOccurrence::visibleTo(Auth::user())
            ->select([
                'id',
                'uuid',
                'title',
                'location',
                'overview',
                'description',
                'is_all_day',
                'start_at',
                'end_at',
                'start_at_date',
                'end_at_date',
                'start_at_time',
                'end_at_time',
                'account_id',
                'calendar_id',
                'calendar_event_id',
                'user_group_id',
                'is_hidden',
                'is_group_only',
                'enable_responses',
                'show_responses',
                'enable_payments',
                'owner_user_id',
            ])
            ->with([
                'event:id,uuid,account_id,calendar_id,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
                'responses:id,calendar_event_occurrence_id,owner_user_id,user_id,name,notes,is_going,is_not_going,is_maybe',
                'responses.user'        => function ($query) {
                    $query->select([
                        'id',
                        User::getFirstNameRawSql(),
                        'last_name',
                    ]);
                },
                'responses.user.avatar' => function ($query2) {
                    $query2->select([
                        'user_id',
                        DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                    ]);
                },
            ])
            ->find($eventOccurrence->id);

        return response()->json($eventOccurrence);
    }

    public function updateEventOccurrenceResponse(EventOccurrence $eventOccurrence)
    {
        if (!$eventOccurrence->id) {
            return abort(404);
        }

        try {
            if (request()->get('user_id')) {
                (new UpdateEventOccurrenceResponses($eventOccurrence))
                    ->createdBy(auth()->user()->id)
                    ->updateByUserId(request()->get('user_id'), request()->get('response', null), request()->get('notes', null))
                    ->update();
            } elseif (request()->get('name')) {
                (new UpdateEventOccurrenceResponses($eventOccurrence))
                    ->createdBy(auth()->user()->id)
                    ->updateByName(request()->get('name'), request()->get('response', null), request()->get('notes', null))
                    ->update();
            } else {
                throw new \Exception('No user_id or name provided.');
            }
        } catch (\Exception $e) {
            return new ApiErrorResponse('Oops!  We could not save the response.', $e);
        }

        return new ApiSuccessResponse('OK');
    }

    public function deleteEventOccurrenceResponse(EventOccurrence $eventOccurrence)
    {
        if (!$eventOccurrence->id) {
            return abort(404);
        }

        try {
            if (request()->get('user_id')) {
                (new UpdateEventOccurrenceResponses($eventOccurrence))
                    ->createdBy(auth()->user()->id)
                    ->removeByUserId(request()->get('user_id'))
                    ->update();
            } elseif (request()->get('name')) {
                (new UpdateEventOccurrenceResponses($eventOccurrence))
                    ->createdBy(auth()->user()->id)
                    ->removeByName(request()->get('name'))
                    ->update();
            } else {
                throw new \Exception('No user_id or name provided.');
            }
        } catch (\Exception $e) {
            return new ApiErrorResponse('Oops!  We could not save the response.', $e);
        }

        return new ApiSuccessResponse('OK');
    }
}
