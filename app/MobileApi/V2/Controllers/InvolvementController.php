<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Subarea;
use App\MobileApi\V2\Services\CreateMobileApiError;
use App\Users\Services\UpdateInvolvementSelection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvolvementController extends Controller
{
    public function view()
    {
        $categories = Category::visibleTo(auth()->user())
            ->applyRestrictions(auth()->user())
            ->with([
                'areas'          => function ($query3) {
                    $query3->select(['id', 'involvement_category_id', 'name', 'description', 'sort_id'])
                        ->applyRestrictions(auth()->user());
                },
                'areas.subareas' => function ($query4) {
                    $query4->select(['id', 'involvement_area_id', 'name', 'description', 'sort_id'])
                        ->applyRestrictions(auth()->user());
                },
            ])
            ->select(['id', 'account_id', 'account_location_id', 'name', 'description', 'sort_id'])
            ->orderBy('sort_id', 'asc')
            ->get();

        $involvement_records = auth()->user()->allInvolvement();

        foreach ($categories as $category) {
            foreach ($category->areas as $area) {
                $area->is_selected = $involvement_records->contains(function ($value, $key) use ($area) {
                    return $value->involvement_area_id == $area->id && !$value->involvement_subarea_id;
                });
                foreach ($area->subareas as $subarea) {
                    $subarea->is_selected = $involvement_records->contains('involvement_subarea_id', $subarea->id);
                }
            }
        }

        return response()
            ->json([
                'categories'          => $categories,
                'involvement_records' => $involvement_records,
            ]);
    }

    public function save()
    {
        $user = auth()->user();

        if (!request()->has('id')) {
            return abort(400, 'No ID provided.');
        }

        try {
            // AREA ONLY
            if (request()->has('type') && request()->get('type') == 'area') {
                $area = Area::find(request()->get('id'));

                if (!$area || $area->category->account_id != $user->account_id) {
                    return abort(400, 'Area does not belong to user.');
                }

                if ($user->involvementAreas()
                    ->where('involvement_area_id', request()->get('id'))
                    ->whereNull('involvement_subarea_id')
                    ->first()) {
                    // Only remove this if we're saying to remove it.
                    if (request()->get('value') == false) {
                        Log::info('InvolvementController::detach() for user.', [
                            'user_id' => $user->id,
                            'area_id' => request()->get('id'),
                        ]);

                        (new UpdateInvolvementSelection($user))
                            ->forArea($area)
                            ->delete();
                    }
                } else {
                    // Only add it if we're saying to set it.
                    if (request()->get('value') == true) {
                        Log::info('InvolvementController::attach() for user.', [
                            'user_id' => $user->id,
                            'area_id' => request()->get('id'),
                        ]);
                        (new UpdateInvolvementSelection($user))
                            ->forArea($area)
                            ->save();
                    }
                }
            } // SUBAREA ONLY
            elseif (request()->has('type') && request()->get('type') == 'subarea') {
                $subarea = Subarea::find(request()->get('id'));

                if (!$subarea || $subarea->area->category->account_id != $user->account_id) {
                    return abort(400, 'Subarea does not belong to user.');
                }

                if ($user->involvementSubareas()->where('involvement_subarea_id', request()->get('id'))->first()) {
                    // Only remove this if we're saying to remove it.
                    if (request()->get('value') == false) {
                        Log::info('InvolvementController::detach() for user.', [
                            'user_id' => $user->id,
                            'area_id' => request()->get('id'),
                        ]);

                        (new UpdateInvolvementSelection($user))
                            ->forSubarea($subarea)
                            ->delete();
                    }
                } else {
                    // Only add it if we're saying to set it.
                    if (request()->get('value') == true) {
                        Log::info('InvolvementController::attach() for user.', [
                            'user_id' => $user->id,
                            'area_id' => request()->get('id'),
                        ]);
                        (new UpdateInvolvementSelection($user))
                            ->forSubarea($subarea)
                            ->save();
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error($e);
            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }

        return response()->json('OK', 200);
    }

    // This method is used by people with Volunteers access, so we should show all categories, etc without restrictions.
    public function all()
    {
        $category = Category::visibleTo(auth()->user())
            ->isVisible()
            ->select(['id', 'account_id', 'name', 'description', 'sort_id'])
            ->with([
                'areas'                => function ($query3) {
                    $query3->isVisible()
                        ->select(['id', 'involvement_category_id', 'name', 'description', 'sort_id']);
                },
                'areas.users'          => function ($query1) {
                    $query1->membersOnly()
                        ->VisibleVolunteersOnly()
                        ->select([
                            'users.id',
                            DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                            'last_name',
                            'family_id',
                        ]);
                },
                'areas.subareas'       => function ($query4) {
                    $query4->isVisible()
                        ->select(['id', 'involvement_area_id', 'name', 'description', 'sort_id']);
                },
                'areas.subareas.users' => function ($query2) {
                    $query2->membersOnly()
                        ->VisibleVolunteersOnly()
                        ->select([
                            'users.id',
                            DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                            'last_name',
                            'family_id',
                        ]);
                },
            ])
            ->orderBy('sort_id', 'asc')
            ->get();

        return response()->json($category);
    }

    public function categories()
    {
        return response()->json(
            Category::visibleTo(auth()->user())
                ->applyRestrictions(auth()->user())
                ->select(['id', 'account_id', 'name', 'description', 'sort_id'])
                ->orderBy('sort_id', 'asc')
                ->get()
        );
    }

    public function category(Category $category)
    {
        $category = Category::visibleTo(auth()->user())
            ->applyRestrictions(auth()->user())
            ->isVisible()
            ->select(['id', 'account_id', 'name', 'description', 'sort_id'])
            ->with([
                'users' => function ($query1) {
                    $query1->membersOnly()->select([
                        'users.id',
                        DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                        'last_name',
                    ]);
                },
            ])
            ->find($category->id);

        return response()->json([
            'category' => $category,
        ]);
    }

    public function areas(Category $category)
    {
        $category = Category::visibleTo(auth()->user())
            ->isVisible()
            ->find($category->id);

        if (!$category) {
            return abort(400);
        }

        return response()->json(
            $category
                ->load('areas:id,involvement_category_id,name,description,sort_id')
                ->areas()
                ->isVisible()
                ->applyRestrictions(auth()->user())
                ->get()
        );
    }

    public function area(Category $category, Area $area)
    {
        $category = Category::visibleTo(auth()->user())->find($category->id);

        if (!$category) {
            return abort(400);
        }

        $area = Area::select(['id', 'name', 'description', 'sort_id'])
            ->with([
                'users' => function ($query1) {
                    $query1->membersOnly()
                        ->visibleVolunteersOnly()
                        ->select([
                            'users.id',
                            DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                            'last_name',
                        ]);
                },
            ])
            ->find($area->id);

        return response()->json([
            'area' => $area,
        ]);
    }

    public function subareas(Category $category, Area $area)
    {
        $category = Category::visibleTo(auth()->user())->find($category->id);

        if (!$category) {
            return abort(400);
        }

        return response()->json(
            $area
                ->load('subareas:id,involvement_area_id,name,description,sort_id')
                ->subareas()
                ->IsVisible()
                ->get()
        );
    }

    public function subarea(Category $category, Area $area, Subarea $subarea)
    {
        $category = Category::visibleTo(auth()->user())->find($category->id);

        if (!$category) {
            return abort(400);
        }

        $subarea = Subarea::select(['id', 'name', 'description', 'sort_id'])
            ->with([
                'users' => function ($query1) {
                    $query1->membersOnly()
                        ->visibleVolunteersOnly()
                        ->select([
                            'users.id',
                            DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'),
                            'last_name',
                        ]);
                },
            ])
            ->isVisible()
            ->find($subarea->id);

        return response()->json([
            'area' => $subarea,
        ]);
    }

    public function volunteers()
    {
        $categories = Category::visibleTo(auth()->user())
            ->orderBy('sort_id', 'asc')
            ->get();

        return response()->json('OK', 200);
    }
}
