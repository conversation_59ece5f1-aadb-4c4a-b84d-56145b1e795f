<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Prayers\Prayer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PrayerController extends Controller
{
    public function index(Request $request)
    {
        $prayers = Prayer::visibleTo($request->user())
            ->isViewableToMembers()
            ->with([
                'updates' => function ($query) {
                    return $query
                        ->select(['id', 'prayer_id', 'created_at', 'updated_at', 'last_updated_at', 'created_by', 'title', 'details', 'is_hidden'])
                        ->orderBy('created_at', 'desc');
                },
                'folder'  => function ($query) {
                    return $query->select(['id', 'name', 'is_hidden', 'sort_id']);
                },
                'users'   => function ($query) {
                    return $query->select(['users.id', DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'), 'last_name', 'family_id']);
                },
            ])
            ->orderBy('last_updated_at', 'desc')
            ->select([
                'id',
                'account_id',
                'prayer_folder_id',
                'created_at',
                'last_updated_at',
                'expires_at',
                'created_by',
                'title',
                'details',
                'is_for_member',
                'is_active',
                'is_a_request',
                'is_approved',
                'is_hidden',
            ])
            ->paginate(500);

        return response()
            ->json($prayers);
    }

    public function create(Request $request)
    {
        if (!$request->get('title') || !$request->get('details')) {
            abort(400, 'A title and details are required.');
        }

        if (!auth()->user()->account->hasFeatureForMember('account.setting.prayers.allow_requests')) {
            abort(403, 'Prayer request submissions are not enabled for your account.');
        }

        $prayer                  = new Prayer();
        $prayer->account_id      = $request->user()->account_id;
        $prayer->created_by      = $request->user()->id;
        $prayer->last_updated_at = now();

        $prayer->title   = $request->input('title');
        $prayer->details = $request->input('details');

        if (auth()->user()->account->hasFeatureForMember('account.setting.prayers.auto_approve_requests')) {
            $prayer->is_active    = true;
            $prayer->is_approved  = true;
            $prayer->is_a_request = false;
        } else {
            $prayer->is_active    = true;
            $prayer->is_approved  = false;
            $prayer->is_a_request = true;
        }

        $prayer->save();

        return response()
            ->json($prayer);
    }
}
