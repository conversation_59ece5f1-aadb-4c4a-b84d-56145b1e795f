<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Sermons\File;
use App\Sermons\Sermon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

class SermonController extends Controller
{
    public function index(Request $request)
    {
        $sermons = Sermon::visibleTo($request->user())
            ->isNotHidden()
            ->with('files:id,sermon_id,title,type,file_size,file_folder,file_name,file_extension,file_type,file_sha1')
            ->orderBy('date_sermon', 'desc')
            ->select([
                'id',
                'date_sermon',
                'language',
                'speaker',
                'speaker_user_id',
                'title',
                'type',
                'summary',
                'description',
                'video_link',
                'podcast_link',
                'is_public',
                'is_hidden',
                'youtube_id',
                'youtube_data',
                'youtube_link',
            ])
            ->when((request()->has('search') && request()->get('search') > ''), function ($query) {
                $query->where(function ($query) {
                    return $query->where('title', 'like', '%' . request()->get('search') . '%')
                        ->orWhere('speaker', 'like', '%' . request()->get('search') . '%');
                });
            })
            ->when((request()->get('type') == 'title' && request()->get('query')), function ($query) {
                return $query->where('title', 'like', '%' . request()->get('query') . '%');
            })
            ->when(request()->get('title'), function ($query) {
                return $query->where('title', 'like', '%' . request()->get('title') . '%');
            })
            ->when((request()->get('type') == 'speaker' && request()->get('query')), function ($query) {
                return $query->where('speaker', 'like', '%' . request()->get('query') . '%');
            })
            ->when(request()->get('speaker'), function ($query) {
                return $query->where('speaker', 'like', '%' . request()->get('speaker') . '%');
            })
            ->when(request()->get('language'), function ($query) {
                return $query->where('language', request()->get('language'));
            })
            ->with([
                'files' => function ($query) {
                    $query->select([
                        'id',
                        'created_at',
                        'updated_at',
                        'sermon_id',
                        'title',
                        'url_title',
                        'type',
                        'file_size',
                        'file_folder',
                        'file_name',
                        'file_extension',
                        'file_sha1',
                        DB::raw((new File())->getFullUrlMySQLConcatString() . ' AS `full_url`'),
                    ]);
                },
            ])
            ->paginate(20);

        return response()
            ->json($sermons);
    }
}
