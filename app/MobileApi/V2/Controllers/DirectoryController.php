<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\MobileApi\V2\Services\CreateMobileApiError;
use App\Users\Photo;
use App\Users\Services\SubmitFamilyPhotoForReview;
use App\Users\User;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DirectoryController extends Controller
{
    public function index(Request $request)
    {
        if (!$request->user()->account->getSetting('feature.directory')) {
            abort(403);
        }

        $users = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.directory.index', 900, function () {
            return User::visibleTo(request()->user())
                ->HeadsOfFamily()
                ->membersOnly()
                ->isNotDeceased()
                ->select(['id', 'account_id', User::getFirstNameRawSql(), 'last_name', 'family_id', 'family_role', 'birthdate'])
                ->with([
                    'emails:id,user_id,email,family_id,is_family,is_primary',
                    'avatar'        => function ($query) {
                        $query->select([
                            'user_id',
                            DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                            DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                        ]);
                    },
                    'familyMembers' => function ($query) {
                        $query->select(['id', 'account_id', 'family_id', User::getFirstNameRawSql(), 'last_name', 'family_role', 'birthdate', 'date_deceased']);
                    },
                    'familyMembers.emails:id,user_id,email',
                    'familyMembers.phones:id,user_id,number',
                    'familyMembers.addresses:id,user_id,address1',
                ])
                ->orderBy('last_name', 'asc')
                ->orderBy('first_name', 'asc')
                ->when(request()->input('member_name'), function ($query) {
                    $query->where(function ($query2) {
                        $query2->where('last_name', 'like', request('member_name') . '%');
                        $query2->orWhere('first_name', 'like', request('member_name') . '%');
                    });
                })
                ->paginate(2000, ['id', 'account_id', 'preferred_first_name', 'first_name', 'last_name', 'family_id', 'family_role', 'birthdate'], '_page');
        });

        return response()->json($users);
    }

    // Note: Optional flag of `include_children`
    public function indexByUser(Request $request)
    {
        if (!$request->user()->account->getSetting('feature.directory')) {
            abort(403);
        }

        $users = User::visibleTo($request->user())
            ->membersOnly()
            ->when(!request('include_children'), function ($query) {
                return $query->adultsOnly();
            })
            ->isNotDeceased()
            ->select(['id', 'account_id', User::getFirstNameRawSql(), 'last_name', 'family_id', 'family_role', 'birthdate'])
            ->with([
                'emails:id,user_id,email,family_id,is_family,is_primary',
                'avatar' => function ($query) {
                    $query->select([
                        'user_id',
                        DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                        DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                    ]);
                },
                'phones:id,user_id,number',
                'addresses:id,user_id,address1',
            ])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->when(request()->input('member_name'), function ($query) {
                $query->where(function ($query2) {
                    $query2->where('last_name', 'like', request('member_name') . '%');
                    $query2->orWhere('first_name', 'like', request('member_name') . '%');
                });
            })
            ->paginate(2000, ['id', 'account_id', 'first_name', 'last_name', 'family_id', 'family_role', 'birthdate'], '_page');

        return response()->json($users);
    }

    public function photoDirectory(Request $request)
    {
        if (!$request->user()->account->getSetting('feature.directory')) {
            abort(403);
        }

        $cache_key = '.' . $request->user()->account_id . '.cache.mobile-api.v2.directory.photo';

        if (Cache::has($cache_key)) {
            return Cache::get($cache_key);
        }

        $users = User::visibleTo($request->user())
            ->HeadsOfFamily()
            ->membersOnly()
            ->isNotDeceased()
            ->select(['id', 'account_id', User::getFirstNameRawSql(), 'last_name', 'family_id', 'family_role', 'birthdate'])
//            ->with([
//                'emails:id,user_id,email,family_id,is_family,is_primary',
//                'familyMembers:id,account_id,family_id,first_name,last_name,family_role,birthdate',
//            ])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->paginate(2000, ['id', 'account_id', 'first_name', 'last_name', 'family_id', 'family_role', 'birthdate'], '_page');

        foreach ($users as $user) {
            $family_photo = [];
            if ($photo = $user->familyPrimaryPhoto(true)) {
                $user->family_photo = [
                    'id'                   => $photo->id,
                    'full_size_url'        => $photo->getCdnUrl(),
                    'thumbnail_url'        => $photo->getCdnUrl(512),
                    'thumbnail_retina_url' => $photo->getCdnUrl(1024),
                ];
            }
        }

        Cache::put($cache_key, $users->toArray(), 900);

        return $users->toArray();
    }

    public function viewFamily($user_id)
    {
        if (!request()->user()->account->getSetting('feature.directory')) {
            abort(403);
        }

        $user = User::visibleTo(request()->user())
            ->find($user_id);

        if (!$user) {
            abort(403);
        }

        $data['user'] = [
            'id'              => $user->id,
            'first_name'      => $user->display_first_name,
            'last_name'       => $user->last_name,
            'is_baptized'     => $user->is_baptized,
            'birthdate'       => $user->birthdate ? $user->birthdate->format('Y-m-d') : null,
            'date_baptism'    => $user->date_baptism ? $user->date_baptism->format('Y-m-d') : null,
            'date_membership' => $user->date_membership ? $user->date_membership->format('Y-m-d') : null,
            'date_married'    => $user->date_married ? $user->date_married->format('Y-m-d') : null,
        ];

        if ($user->avatar) {
            $data['user']['avatar'] = [
                'small_thumbnail_url' => optional($user->avatar)->getCdnUrl(256),
                'thumbnail_url'       => optional($user->avatar)->getCdnUrl(512),
            ];
        }

        $data['user']['spouse'] = [];
        if ($user->spouse) {
            $data['user']['spouse'] = [
                'id'           => $user->spouse->id,
                'first_name'   => $user->spouse->display_first_name,
                'last_name'    => $user->spouse->last_name,
                'is_baptized'  => $user->is_baptized,
                'birthdate'    => $user->spouse->birthdate ? $user->spouse->birthdate->format('Y-m-d') : null,
                'date_baptism' => $user->spouse->date_baptism ? $user->spouse->date_baptism->format('Y-m-d') : null,
            ];

            if ($user->spouse->avatar) {
                $data['user']['spouse']['avatar'] = [
                    'small_thumbnail_url' => optional($user->spouse->avatar)->getCdnUrl(256),
                    'thumbnail_url'       => optional($user->spouse->avatar)->getCdnUrl(512),
                ];
            }
        }

        $data['addresses'] = [];
        foreach ($user->getAllAddressesInFamily() as $address) {
            $data['addresses'][] = [
                'id'             => $address->id,
                'family_id'      => $address->family_id,
                'user'           => [
                    'first_name' => $address->user->display_first_name,
                    'last_name'  => $address->user->last_name,
                ],
                'address_string' => $address->getAddressString(),
                'address_html'   => $address->getFullAddressHTML(),
                'label'          => $address->label,
                'type'           => $address->type,
                'address1'       => $address->address1,
                'address2'       => $address->address2,
                'address3'       => $address->address3,
                'city'           => $address->city,
                'state'          => $address->state,
                'zip'            => $address->zip,
                'country'        => $address->country,
                'is_mailing'     => $address->is_mailing,
                'maps_image'     => $address->getMapThumbnailUrl(), // $address->getStaticGoogleMapsImage(160, 135, 'class="float-right ml-2 border border-secondary"'),
                'maps_link'      => 'http://maps.google.com/?daddr=' . urlencode(str_replace("<br>", ",", \App\Users\Address::format($address))),
            ];
        }

        $data['phones'] = [];
        foreach (
            $user->family()->with([
                'phones' => function ($query) {
                    $query->where('is_hidden', false);
                },
            ])->get()->pluck('phones')->flatten() as $phone
        ) {
            if ($phone->family_id || $phone->user->date_deceased === null) {
                $data['phones'][] = [
                    'id'               => $phone->id,
                    'family_id'        => $phone->family_id,
                    'number_formatted' => $phone->formattedNumber(),
                    'number'           => $phone->number,
                    'type'             => $phone->type,
                    'first_name'       => $phone->user->display_first_name,
                ];
            }
        }

        $data['emails'] = [];
        foreach (
            $user->family()->with([
                'emails' => function ($query) {
                    $query->where('is_hidden', false);
                },
            ])->get()->pluck('emails')->flatten() as $email
        ) {
            if ($email->family_id || $email->user->date_deceased === null) {
                $data['emails'][] = [

                    'id'         => $email->id,
                    'family_id'  => $email->family_id,
                    'type'       => $email->type,
                    'email'      => $email->email,
                    'first_name' => $email->user->display_first_name,
                    'type'       => $email->type,
                ];
            }
        }

        $data['family_members'] = [];
        foreach ($user->family()->isNotDeceased()->get() as $member) {
            // Create a temp array so we can decide to have an avatar or not.
            $temp_member_array = null;
            $temp_member_array = [
                'id'           => $member->id,
                'first_name'   => $member->display_first_name,
                'last_name'    => $member->last_name,
                'family_role'  => $member->family_role != 'other' ? $member->family_role : null,
                'birthdate'    => $member->birthdate ? $member->birthdate->format('F d') : null,
                'is_baptized'  => $member->is_baptized,
                'date_baptism' => $member->date_baptism ? $member->date_baptism->format('Y-m-d') : null,
            ];

            if ($member->avatar) {
                $temp_member_array['avatar'] = [
                    'small_thumbnail_url' => optional($member->avatar)->getCdnUrl(256),
                    'thumbnail_url'       => optional($member->avatar)->getCdnUrl(512),
                ];
            }

            $data['family_members'][] = $temp_member_array;
        }

        $data['family_photo'] = [];
        if ($photo = $user->familyPrimaryPhoto(true)) {
            $data['family_photo'] = [
                'id'                   => $photo->id,
                'full_size_url'        => $photo->getCdnUrl(),
                'thumbnail_url'        => $photo->getCdnUrl(512),
                'thumbnail_retina_url' => $photo->getCdnUrl(1024),
            ];
        }

        return response()->json($data);
    }

    public function submitNewFamilyPhoto($user_id)
    {
        if (!auth()->user()->id
            || !auth()->user()->can('manageFamilyPhotos', Photo::class)) {
            return response()->json(null, 401);
        }

        $user = User::visibleTo(request()->user())
            ->find($user_id);

        if (auth()->user()->account_id != $user->account_id) {
            return response()->json(null, 401);
        }

        $validator = Validator::make(request()->all(), [
            'images' => 'required',
        ]);

        if ($validator->fails()) {
            return (new CreateMobileApiError())
                ->withMessage('Some form fields failed validation.')
                ->withFields($validator->errors()->toArray())
                ->withResponseCode(422)
                ->response();
        }

        try {
            $head_of_family = $user->getHeadOfFamily();
            $image          = request()->get('images')[0];

            if (!$head_of_family) {
                Log::error('submitNewFamilyPhoto: Could not find head of family for user.', [
                    'user_id' => $user->id,
                ]);
                throw new \Exception('Oops! We could not find a head of family for your account.  Please reach out to an admin.');
            }

            (new SubmitFamilyPhotoForReview())
                ->forUser($head_of_family)
                ->originalFileName(Arr::get($image, 'fileName'))
                ->withMobileImage(Arr::get($image, 'base64'))
                ->needsApproval(false)
                ->createdBy(auth()->user())
                ->create();
        } catch (\Exception $e) {
            Log::error($e);

            return (new CreateMobileApiError())
                ->withMessage('Oops! There was an error uploading the photo. An error report has been sent to Lightpost.')
                ->withResponseCode(422)
                ->response();
        }

        return response()->json('OK', 200);
    }
}
