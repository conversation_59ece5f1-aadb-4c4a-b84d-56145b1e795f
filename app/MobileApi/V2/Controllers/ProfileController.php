<?php

namespace App\MobileApi\V2\Controllers;

use App\Accounts\AccountSetting;
use App\Accounts\Grade;
use App\Base\Http\Controllers\Controller;
use App\Crises\Crisis;
use App\MobileApi\V2\Services\CreateMobileApiError;
use App\Users\Device;
use App\Users\Group;
use App\Users\Phone;
use App\Users\Photo;
use App\Users\Services\SubmitFamilyPhotoForReview;
use App\Users\Services\UpdateUser;
use App\Users\User;
use App\WorshipAssignments\Pick;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ProfileController extends Controller
{
    public function index()
    {
        $user_data = User::select([
            'id',
            'account_id',
            'family_id',
            'preferred_first_name',
            'first_name',
            'last_name',
            'prefix_name',
            'suffix_name',
            'birthdate',
            'marital_status',
            'date_married',
            'gender',
            'blood_type',
            'school_attending',
            'user_grade_id',
            'employer',
            'job_title',
            'job_keywords',
        ])
            ->with([
                'emails:id,user_id,family_id,type,email,is_family,is_primary,is_hidden,receives_group_emails,allow_messages,sort_id',
                'addresses:id,user_id,family_id,type,label,address1,address2,address3,city,state,zip,country,is_family,map_thumbnail_url_512',
                'phones:id,user_id,family_id,type,number,is_family,is_primary,is_hidden',
                'grade:id,account_id,name,description',
                'avatar'        => function ($query) {
                    $query->select([
                        'user_id',
                        DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                        DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                    ]);
                },
                'familyMembers' => function ($query) {
                    $query->select([
                        'id',
                        'account_id',
                        'family_id',
                        'first_name',
                        'last_name',
                        'birthdate',
                        'marital_status',
                        'date_married',
                        'gender',
                        'blood_type',
                        'school_attending',
                        'user_grade_id',
                        'employer',
                        'job_title',
                        'job_keywords',
                    ]);
                },
            ])
            ->find(auth()->user()->id)
            ->toArray();

        $final_phones = [];
        foreach ($user_data['phones'] as $phone) {
            $phone['number_formatted'] = Phone::format($phone['number'], '-');
            $final_phones[]            = $phone;
        }

        $user_data['family_photo'] = [];
        if ($photo = auth()->user()->familyPrimaryPhoto(include_user_primary_only: true)) {
            $user_data['family_photo'] = [
                'id'                   => $photo->id,
                'created_at'           => $photo->created_at,
                'file_name'            => $photo->file_name,
                'file_extension'       => $photo->file_extension,
                'file_folder'          => $photo->file_folder,
                'width'                => $photo->width,
                'height'               => $photo->height,
                'full_size_url'        => $photo->getCdnUrl(),
                'thumbnail_url'        => $photo->getCdnUrl(512),
                'thumbnail_retina_url' => $photo->getCdnUrl(1024),
            ];
        }
        if ($pending_photo = auth()->user()->familyPrimaryPhoto(include_user_primary_only: true, only_approved: false)) {
            $user_data['unapproved_family_photo'] = [
                'id'                   => $pending_photo->id,
                'created_at'           => $pending_photo->created_at,
                'file_name'            => $pending_photo->file_name,
                'file_extension'       => $pending_photo->file_extension,
                'file_folder'          => $pending_photo->file_folder,
                'width'                => $pending_photo->width,
                'height'               => $pending_photo->height,
                'full_size_url'        => $pending_photo->getCdnUrl(),
                'thumbnail_url'        => $pending_photo->getCdnUrl(512),
                'thumbnail_retina_url' => $pending_photo->getCdnUrl(1024),
            ];
        }

        $user_data['phones'] = $final_phones;

        $user_data['account']['grades']            = Grade::visibleTo(auth()->user())->select(['id', 'name', 'description', 'sort_id'])->orderBy('sort_id')->get()->toArray();
        $user_data['account']['has_active_crisis'] = Crisis::visibleTo(auth()->user())->isActive()->exists();

        $user_data['timezone'] = optional(auth()->user()->account)->timezone;

        $user_data['has_had_worship_assignments'] = Pick::where('user_id', auth()->user()->id)->limit(1)->exists();

        return response()->json($user_data);
    }

    public function userDevices()
    {
        if (!auth()->user()?->id) {
            return response()->json(null, 401);
        }

        return response()->json(auth()->user()->devices);
    }

    public function save()
    {
        $user = auth()->user();

        if (!auth()->user()->id) {
            return response()->json(null, 401);
        }

        $validator = Validator::make(request()->all(), [
            'preferred_first_name' => 'nullable|string|max:128',
            'prefix_name'          => 'nullable|string|max:128',
            'suffix_name'          => 'nullable|string|max:128',
            'first_name'           => 'required|string|max:128',
            'last_name'            => 'required|string|max:128',
            'birthdate'            => 'nullable|string',
            'marital_status'       => 'nullable|string',
            'date_married'         => 'nullable|string',
            'maiden_name'          => 'nullable|string',
            'gender'               => 'nullable|string',
            'password'             => 'nullable|string|max:256',
            'blood_type'           => 'nullable|string',
            'school_attending'     => 'nullable|string',
            'user_grade_id'        => 'nullable|integer',
            'employer'             => 'nullable|string',
            'job_title'            => 'nullable|string',
            'job_keywords'         => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return (new CreateMobileApiError())
                ->withMessage('Some form fields failed validation.')
                ->withFields($validator->errors()->toArray())
                ->withResponseCode(422)
                ->response();
        }

        if ($user) {
            try {
                // @TODO: Make sure we're only ever passing fields allowed, the update service will accept all request fields.
                DB::transaction(function () use ($user) {
                    return (new UpdateUser($user))->update(
                        request()->only([
                            'preferred_first_name',
                            'prefix_name',
                            'suffix_name',
                            'first_name',
                            'last_name',
                            'birthdate',
                            'marital_status',
                            'date_married',
                            'maiden_name',
                            'gender',
                            'password',
                            'blood_type',
                            'school_attending',
                            'user_grade_id',
                            'employer',
                            'job_title',
                            'job_keywords',
                        ])
                    );
                });
            } catch (\Exception $e) {
                Log::error($e);
                return (new CreateMobileApiError())
                    ->withMessage($e->getMessage())
                    ->response();
            }
        }

        return response()->json('OK', 200);
    }

    public function permissions()
    {
        if (!auth()->user()->id) {
            return response()->json(null, 401);
        }

        $user = auth()->user();

        try {
            $permissions = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.users.' . $user->id . '.permissions', 60, function () use ($user) {
                return [
                    'user_id'                      => $user->id,
                    'family_id'                    => $user->family_id,
                    'account_id'                   => $user->account_id,
                    'timezone'                     => $user->account->timezone,
                    'account'                      => $user->account()->select([
                        'id',
                        'name',
                        'short_name',
                        'url_name',
                        'church_website',
                        'group_email_prefix',
                        'static_short_name',
                        'podcasts_prefix',
                        'domain',
                        'subdomain',
                        'cname_domain',
                        'cname_subdomain',
                        'address1',
                        'address2',
                        'address3',
                        'city',
                        'state',
                        'postal_code',
                        'country_code',
                        'phone_work',
                        'phone_fax',
                        'phone_other',
                        'timezone',
                        'email',
                        'status',
                        'is_active',
                        'is_suspended',
                        'stripe_customer_id',
                        'stripe_account_id',
                        'stripe_granted_scope',
                        'stripe_account_publishable_key',
                        'mailing_address1',
                        'mailing_address2',
                        'mailing_address3',
                        'mailing_city',
                        'mailing_state',
                        'mailing_postal_code',
                        'mailing_country_code',
                        'congregation_contact_email',
                        'public_contact_email',
                    ])->first(),
                    'system_settings'              => [
                        'pusher_api_key'     => config('broadcasting.connections.pusher.key'),
                        'pusher_host'        => config('broadcasting.connections.pusher.options.host'),
                        'pusher_cluster'     => config('broadcasting.connections.pusher.options.cluster'),
                        'pusher_auth_url'    => config('broadcasting.connections.pusher.options.auth_url'),
                        'pusher_secure_port' => config('broadcasting.connections.pusher.options.port'),
                    ],
                    'account_settings'             => AccountSetting::select([
                        'id',
                        'key',
                        'name',
                        //                    'description',
                        'type',
                        //                    'allow_account_to_view',
                        //                    'allow_account_to_enable',
                        //                    'allow_account_to_select_permissions',
                        'default',
                        'values',
                    ])
                        ->with([
                            'value' => function ($query) use ($user) {
                                $query->where('account_id', $user->account_id)
                                    ->select([
                                        'id',
                                        'account_id',
                                        'account_setting_id',
                                        'value',
                                        'enable_for_admin',
                                        'enable_for_member',
                                    ]);
                            },
                        ])->get(),
                    'permissions'                  => $user->permissions(),
                    'has_had_worship_assignments'  => Pick::where('user_id', auth()->user()->id)->exists(),
                    'has_active_crisis'            => Crisis::visibleTo(auth()->user())->isActive()->exists(),
                    'has_group_with_posts_enabled' => auth()->user()->can('seeGroupPosts', Group::class),
                ];
            });

            return response()->json($permissions, 200);
        } catch (\Exception $e) {
            Log::error($e);
            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }
    }

    public function sendDeviceToken()
    {
        if (!auth()->user()->id) {
            return response()->json(null, 401);
        }

        $user = auth()->user();

        $validator = Validator::make(request()->all(), [
            'device_token'           => 'required|string|max:300',
            'device_name'            => 'nullable|string',
            'device'                 => 'nullable|string',
            'device_version'         => 'nullable',
            'type'                   => 'nullable|string',
            'receives_notifications' => 'nullable|bool',
        ]);

        if ($validator->fails()) {
            return (new CreateMobileApiError())
                ->withMessage('Some form fields failed validation.')
                ->withFields($validator->errors()->toArray())
                ->withResponseCode(422)
                ->response();
        }

        Log::info('sendDeviceToken: Device token sent and passed validation.', [
            'device_token' => request()->input('device_token'),
            'user_id'      => $user->id,
        ]);

        try {
            $device = Device::updateOrCreate([
                'account_id'   => $user->account_id,
                'user_id'      => $user->id,
                'device_token' => request()->input('device_token'),
            ], [
                'name'                   => request()->input('device_name'),
                'device'                 => request()->input('device'),
                'device_version'         => request()->input('device_version'),
                'receives_notifications' => request()->has('receives_notifications') ? request()->input('receives_notifications') : true,
                'last_refreshed_at'      => Carbon::now(),
            ]);

            // If we're given an Expo token, delete any other tokens for this user that are not Expo tokens.
            if (Str::contains(request()->input('device_token'), 'ExponentPushToken', true)) {
                $user->devices()
                    ->where('receives_notifications', 1)
                    ->where('device_token', 'not like', 'ExponentPushToken%')
                    ->delete();
            }

            return response()->json([
                'device' => $device,
            ], 200);
        } catch (\Exception $e) {
            Log::error($e);
            return (new CreateMobileApiError())
                ->withMessage($e->getMessage())
                ->response();
        }
    }

    public function submitNewFamilyPhoto()
    {
        $user = auth()->user();

        if (!auth()->user()->id) {
            return response()->json(null, 401);
        }

        $validator = Validator::make(request()->all(), [
            'images' => 'required',
        ]);

        if ($validator->fails()) {
            return (new CreateMobileApiError())
                ->withMessage('Some form fields failed validation.')
                ->withFields($validator->errors()->toArray())
                ->withResponseCode(422)
                ->response();
        }

        try {
            $head_of_family = $user->getHeadOfFamily();
            $image          = request()->get('images')[0];

            if (!$head_of_family) {
                Log::error('submitNewFamilyPhoto: Could not find head of family for user.', [
                    'user_id' => $user->id,
                ]);
                throw new \Exception('Oops! We could not find a head of family for your account.  Please reach out to an admin.');
            }

            (new SubmitFamilyPhotoForReview())
                ->forUser($head_of_family)
                ->originalFileName(Arr::get($image, 'fileName'))
                ->withMobileImage(Arr::get($image, 'base64'))
                ->createdBy(auth()->user())
                ->create();
        } catch (\Exception $e) {
            Log::error($e);

            return (new CreateMobileApiError())
                ->withMessage('Oops! There was an error uploading the photo. An error report has been sent to Lightpost.')
                ->withResponseCode(422)
                ->response();
        }

        return response()->json('OK', 200);
    }
}
