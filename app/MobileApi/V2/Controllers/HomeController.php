<?php

namespace App\MobileApi\V2\Controllers;

use App\Accounts\VerseOfTheDay;
use App\App\Services\Dashboard;
use App\Base\Http\Controllers\Controller;
use App\Calendars\Calendar;
use App\Calendars\EventOccurrence;
use App\Prayers\Prayer;
use App\Prayers\PrayerUpdate;
use App\Users\Photo;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    public function index(Request $request)
    {
        /** CACHE **/
        $votd = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.dashboard.votd', 900, function () {
            return (new VerseOfTheDay())->getVerseForMobile(null, auth()->user())?->toArray();
        });

        $notable_days = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.dashboard.notable_days', 600, function () {
            [$key, $upcoming_birthdays] = Arr::divide((new Dashboard())->getBirthdaysThisMonth()->toArray());
            [$key, $upcoming_baptism_birthdays] = Arr::divide((new Dashboard())->getBaptismBirthdaysThisMonth()->toArray());
            $birthdays_today         = (new Dashboard())->getBirthdaysToday();
            $baptism_birthdays_today = (new Dashboard())->getBaptismBirthdaysToday();
            $anniversaries_today     = (new Dashboard())->getAnniversariesToday();
            $anniversaries_upcoming  = (new Dashboard())->getAnniversariesThisMonth();

            return [
                'birthdays_today'            => $birthdays_today->toArray(),
                'upcoming_birthdays'         => $upcoming_birthdays,
                'baptism_birthdays_today'    => $baptism_birthdays_today->toArray(),
                'upcoming_baptism_birthdays' => $upcoming_baptism_birthdays,
                'anniversaries_today'        => $anniversaries_today->toArray(),
                'anniversaries_upcoming'     => $anniversaries_upcoming->toArray(),
            ];
        });

        $prayer_list = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.dashboard.prayer_list', 300, function () {
            return [
                'total_count'            => Prayer::visibleTo(request()->user())
                    ->IsNotARequest()
                    ->isNotHidden()
                    ->isNotExpired(auth()->user()->account?->timezone)
                    ->count(),
                'recently_updated_count' => PrayerUpdate::visibleTo(request()->user())
                    ->PrayerIsViewableToMembers()
                    ->isNotHidden()
                    ->isNotExpired(auth()->user()->account?->timezone)
                    ->where('created_at', '>', Carbon::now()->subWeeks(2))
                    ->count(),
                'last_updated'           => Prayer::visibleTo(request()->user())
                    ->isNotHidden()
                    ->isNotExpired(auth()->user()->account?->timezone)
                    ->orderBy('updated_at', 'desc')
                    ->first()?->updated_at,
            ];
        });

        $calendar = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.dashboard.calendar', 300, function () {
            return [
                'events_count_this_month' => EventOccurrence::visibleTo(auth()->user())
                    ->whereIn('calendar_id', Calendar::visibleTo(auth()->user())->notHidden()->get()->pluck('id'))
                    ->where('start_at', '>', now()->setTimezone(auth()->user()->account?->timezone)->startOfMonth()->setTimezone('UTC'))
                    ->where('end_at', '<', now()->setTimezone(auth()->user()->account?->timezone)->endOfMonth()->setTimezone('UTC'))
                    ->orderBy('start_at', 'ASC')
                    ->select(['id', 'start_at', 'end_at', 'start_at_date', 'end_at_date', 'start_at_time', 'end_at_time', 'account_id', 'calendar_id', 'calendar_event_id', 'user_group_id', 'is_hidden', 'is_group_only'])
                    ->with([
                        'event:id,uuid,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                        'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
                    ])
                    ->count(),
                'events_count_this_week'  => EventOccurrence::visibleTo(auth()->user())
                    ->whereIn('calendar_id', Calendar::visibleTo(auth()->user())->notHidden()->get()->pluck('id'))
                    ->where('start_at', '>', now()->setTimezone(auth()->user()->account?->timezone)->startOfWeek()->setTimezone('UTC'))
                    ->where('end_at', '<', now()->setTimezone(auth()->user()->account?->timezone)->endOfWeek()->setTimezone('UTC'))
                    ->orderBy('start_at', 'ASC')
                    ->select(['id', 'start_at', 'end_at', 'start_at_date', 'end_at_date', 'start_at_time', 'end_at_time', 'account_id', 'calendar_id', 'calendar_event_id', 'user_group_id', 'is_hidden', 'is_group_only'])
                    ->with([
                        'event:id,uuid,user_group_id,title,url_title,location,overview,description,is_hidden,is_group_only,timezone,start_at,end_at,is_all_day,enable_responses,enable_payments,remove_event_after_end_date',
                        'calendar:id,uuid,user_group_id,name,url_name,color,background_color,border_color,text_color,is_hidden,is_group_only,auto_show',
                    ])
                    ->count(),
            ];
        });

        $leadership = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.dashboard.leadership', 300, function () {
            return [
                'ministers' => auth()->user()->account->churchOffices()->where('name', 'Minister')->first()?->users()->select(['users.id', 'family_id', DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'), 'preferred_first_name', 'last_name', 'church_office', 'church_office_description'])
                    ->with([
                        'avatar' => function ($query) {
                            $query->select([
                                'user_id',
                                DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                                DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                            ]);
                        },
                    ])
                    ->orderBy('last_name', 'asc')
                    ->get(),
                'elders'    => auth()->user()->account->churchOffices()->where('name', 'Elder')->first()?->users()->select(['users.id', 'family_id', DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'), 'preferred_first_name', 'last_name', 'church_office', 'church_office_description'])
                    ->with([
                        'avatar' => function ($query) {
                            $query->select([
                                'user_id',
                                DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                                DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                            ]);
                        },
                    ])
                    ->orderBy('last_name', 'asc')
                    ->get(),
                'deacons'   => auth()->user()->account->churchOffices()->where('name', 'Deacon')->first()?->users()->select(['users.id', 'family_id', DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'), 'preferred_first_name', 'last_name', 'church_office', 'church_office_description'])
                    ->with([
                        'avatar' => function ($query) {
                            $query->select([
                                'user_id',
                                DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                                DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                            ]);
                        },
                    ])
                    ->orderBy('last_name', 'asc')
                    ->get(),
            ];
        });

        $church_offices = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.dashboard.church_offices', 600, function () {
            return auth()->user()->account->churchOffices()
                ->ShownAsLeadership()
                ->with('users', function ($query) {
                    $query->select(['users.id', 'family_id', DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'), 'preferred_first_name', 'last_name'])
                        ->with([
                            'avatar' => function ($query) {
                                $query->select([
                                    'user_id',
                                    DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                                    DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                                ]);
                            },
                        ]);
                })
                ->get();
        });

        if (!auth()->user()->isChild() && auth()->user()->account->hasFeatureForMember('account.setting.attendance.enable_member_checkin')) {
            $checkin_types             = Cache::remember('.' . auth()->user()->account_id . '.cache.mobile-api.v2.dashboard.checkin_types', 300, function () {
                return (new \App\Attendance\Services\AttendanceService())->forUser(auth()->user())->getCheckinAttendanceTypesForNow()->toArray();
            });
            $family_attendance_records = (new \App\Attendance\Services\AttendanceService())->forUser(auth()->user())->getFamilyAttendanceRecordsForNow()->toArray();
        }

        $data = [
            'birthdays'         => [
                'today'    => $notable_days['birthdays_today'],
                'upcoming' => $notable_days['upcoming_birthdays'],
            ],
            'baptism_birthdays' => [
                'today'    => $notable_days['baptism_birthdays_today'],
                'upcoming' => $notable_days['upcoming_baptism_birthdays'],
            ],
            'anniversaries'     => [
                'today'    => $notable_days['anniversaries_today'],
                'upcoming' => $notable_days['anniversaries_upcoming'],
            ],
            'sermons'           => [
                'latest' => [], // DEPRECATED, REMOVE IN Mobile API v3
            ],
            'verse_of_the_day'  => $votd,
            'attendance'        => [
                'checkin' => [
                    'types'          => $checkin_types ?? [],
                    'family_records' => $family_attendance_records ?? [],
                ],
            ],
            'prayer_list'       => $prayer_list,
            'calendar'          => $calendar,
            'leadership'        => $leadership,
            'church_offices'    => $church_offices,
        ];

        return response()
            ->json($data);
    }

    public function birthdays(Request $request)
    {
        $all_birthdays = (new \App\App\Services\Dashboard())->getAllBirthdays();

        $data = [
            'all_birthdays' => $all_birthdays,
        ];

        return response()->json($data);
    }

    public function baptismBirthdays(Request $request)
    {
        $all_baptism_birthdays = (new \App\App\Services\Dashboard())->getAllBaptismBirthdays();

        $data = [
            'all' => $all_baptism_birthdays,
        ];

        return response()->json($data);
    }

    public function anniversaries(Request $request)
    {
        return response()->json([
            'all' => (new \App\App\Services\Dashboard())->getAllAnniversaries(),
        ]);
    }

    public function lightpost(Request $request)
    {
        return response()->json([
            'services' => [
                'stripe' => [
                    'connect' => [
                        'publishable_key' => config('services.stripe.connect.publishable_key'),
                    ],
                ],
            ],
        ]);
    }
}
