<?php

namespace App\MobileApi\V2\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Base\Http\Responses\ApiErrorResponse;
use App\Base\Http\Responses\ApiSuccessResponse;
use App\Programs\Program;
use App\Programs\ProgramGroup;
use App\Programs\ProgramUser;
use App\Programs\Services\SubmitUserCheckin;
use App\Users\Phone;

class ProgramController extends Controller
{
    public function index()
    {
        return new ApiSuccessResponse(
            Program::visibleTo(auth()->user())
                ->isActive()
                ->select([
                    'id',
                    'name',
                    'url_name',
                    'description',
                    'is_active',
                    'enable_public_registration',
                ])
                ->withCount([
                    'registeredUsers',
                    'groups',
                ])
                ->orderBy('created_at', 'desc')
                ->get(),
        );
    }

    public function program(Program $program)
    {
        return new ApiSuccessResponse(
            Program::visibleTo(auth()->user())
                ->where('id', $program->id)
                ->isActive()
                ->select([
                    'id',
                    'name',
                    'url_name',
                    'description',
                    'is_active',
                    'enable_public_registration',
                ])
                ->withCount([
                    'registeredUsers',
                    'groups',
                    'currentCheckins',
                ])
                ->orderBy('created_at', 'desc')
                ->first(),
        );
    }

    public function groups(Program $program)
    {
        return new ApiSuccessResponse(
            ProgramGroup::visibleTo(auth()->user())
                ->where('program_id', $program->id)
                ->with([
                    'users:id,first_name,last_name',
                ])
                ->select([
                    'id',
                    'program_id',
                    'name',
                    'url_name',
                    'description',
                    'bg_color',
                    'text_color',
                    'tw_bg_color',
                    'tw_text_color',
                    'enable_checkins',
                    'is_program_group',
                    'is_registration_group',
                ])
                ->withCount([
                    'users',
                    'currentCheckins',
                ])
                ->orderBy('sort_id', 'asc')
                ->orderBy('name', 'desc')
                ->get(),
        );
    }

    public function group(Program $program, ProgramGroup $programGroup)
    {
        $group = ProgramGroup::visibleTo(auth()->user())
            ->where('id', $programGroup->id)
            ->with([
                'registeredUsers'             => function ($query) {
                    $query->select([
                        'program_users.id',
                        'first_name',
                        'last_name',
                        'program_id',
                        'allergies',
                        'special_needs',
                    ])
                        ->orderBy('last_name', 'asc')
                        ->orderBy('first_name', 'asc');
                },
                'registeredUsers.lastCheckin' => function ($query) {
                    $query->select([
                        'id',
                        'checkout_at',
                        'checkin_at',
                        'program_user_checkins.program_user_id',
                        'created_by_user_id',
                    ]);
                },
                'users.lastCheckin.createdByUser:id,first_name,last_name',
            ])
            ->withCount([
                'currentCheckins',
            ])
            ->select([
                'id',
                'program_id',
                'name',
                'url_name',
                'description',
                'bg_color',
                'text_color',
                'tw_bg_color',
                'tw_text_color',
                'enable_checkins',
                'is_program_group',
                'is_registration_group',
            ])
            ->first();

        if (!$group) {
            return new ApiErrorResponse(null, null, 404);
        }

        return new ApiSuccessResponse($group);
    }

    public function user(Program $program, ProgramUser $programUser)
    {
        $user = ProgramUser::visibleTo(auth()->user())
            ->where('id', $programUser->id)
            ->with([
                'checkins.createdByUser:id,first_name,last_name',
                'groups:id,name,bg_color,text_color,tw_bg_color,tw_text_color,enable_checkins,is_program_group,is_registration_group',
                'checkins'              => function ($query) {
                    $query->select([
                        'id',
                        'checkin_at',
                        'checkout_at',
                        'program_group_id',
                        'program_user_id',
                        'checkin_by_user_id',
                        'checkout_by_user_id',
                        'created_by_user_id',
                    ])
                        ->limit(10);
                },
                'registration.contacts' => function ($query) {
                    $query->select([
                        'program_users.id',
                        'first_name',
                        'last_name',
                        'mobile_phone',
                        'home_phone',
                        'email',
                    ])
                        ->withPivot([
                            'is_contact',
                            'is_primary_contact',
                            'is_emergency_contact',
                            'can_pickup',
                            'can_not_pickup',
                            'no_contact_allowed',
                            'contact_notes',
                            'admin_notes',
                            'metadata',
                        ]);
                },
            ])
            ->select([
                'id',
                'family_role',
                'title',
                'prefix_name',
                'suffix_name',
                'preferred_first_name',
                'first_name',
                'middle_name',
                'last_name',
                'gender',
                'marital_status',
                'user_name',
                'notes',
                'validation_code',
                'school_grade',
                'school_attending',
                'blood_type',
                'allergies',
                'special_needs',
                'public_token',
                'mobile_phone',
                'home_phone',
                'email',
            ])
            ->first();

        // Send phone numbers over pre-formatted.
        if ($user->registration?->contacts) {
            foreach ($user->registration->contacts as $contact) {
                $contact->mobile_phone_formatted = Phone::format($contact->mobile_phone, '-');
            }
        }

        return new ApiSuccessResponse($user);
    }

    public function submitUserCheckin(Program $program, ProgramUser $programUser)
    {
        return new ApiSuccessResponse(
            (new SubmitUserCheckin())
                ->createdByUser(auth()->user())
                ->forProgramGroup(request('program_group_id'))
                ->forProgramUser($programUser)
                ->checkAt(request('check_at', null))
                ->submit()
        );
    }
}
