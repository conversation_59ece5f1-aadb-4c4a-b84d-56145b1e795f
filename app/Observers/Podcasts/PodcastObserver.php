<?php

namespace App\Observers\Podcasts;

use App\App\Services\SendTransactionalEmail;
use App\Podcasts\Podcast;

class PodcastObserver
{
    /**
     * Handle the Podcast "created" event.
     */
    public function created(Podcast $podcast): void
    {
        $account_name = $podcast->account->name;

        dispatch(function () use ($podcast, $account_name) {
            (new SendTransactionalEmail())
                ->to(config('app.contact_email'))
                ->type('podcast_created')
                ->mergeData([
                    'podcast_name' => $podcast->title,
                    'church_name'  => $account_name,
                ])
                ->send();
        });
    }

    /**
     * Handle the Podcast "updated" event.
     */
    public function updated(Podcast $podcast): void
    {
        //
    }

    /**
     * Handle the Podcast "deleted" event.
     */
    public function deleted(Podcast $podcast): void
    {
        //
    }

    /**
     * Handle the Podcast "restored" event.
     */
    public function restored(Podcast $podcast): void
    {
        //
    }

    /**
     * Handle the Podcast "force deleted" event.
     */
    public function forceDeleted(Podcast $podcast): void
    {
        //
    }
}
