<?php

namespace App\Observers\Groups;

use App\Events\Groups\PostCreatedBroadcast;
use App\Groups\Post;

class PostObserver
{
    /**
     * Handle the Post "created" event.
     *
     * @param \App\Groups\Post $post
     *
     * @return void
     */
    public function created(Post $post)
    {
        PostCreatedBroadcast::dispatch($post);

        if (config('app.env') != 'production') {
            return;
        }
    }

    /**
     * Handle the Post "updated" event.
     *
     * @param \App\Groups\Post $post
     *
     * @return void
     */
    public function updated(Post $post)
    {
    }

    /**
     * Handle the Post "deleted" event.
     *
     * @param \App\Groups\Post $post
     *
     * @return void
     */
    public function deleted(Post $post)
    {
        //
    }

    /**
     * Handle the Post "restored" event.
     *
     * @param \App\Groups\Post $post
     *
     * @return void
     */
    public function restored(Post $post)
    {
        //
    }

    /**
     * Handle the Post "force deleted" event.
     *
     * @param \App\Groups\Post $post
     *
     * @return void
     */
    public function forceDeleted(Post $post)
    {
        //
    }
}
