<?php

namespace App\Observers\Groups;

use App\Events\Groups\CommentCreatedBroadcast;
use App\Groups\Comment;

class CommentObserver
{
    /**
     * Handle the Post "created" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function created(Comment $comment)
    {
        CommentCreatedBroadcast::dispatch($comment);

        if (config('app.env') != 'production') {
            return;
        }
    }

    /**
     * Handle the Comment "updated" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function updated(Comment $comment)
    {
        //
    }

    /**
     * Handle the Comment "deleted" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function deleted(Comment $comment)
    {
        //
    }

    /**
     * Handle the Comment "restored" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function restored(Comment $comment)
    {
        //
    }

    /**
     * Handle the Comment "force deleted" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function forceDeleted(Comment $comment)
    {
        //
    }
}
