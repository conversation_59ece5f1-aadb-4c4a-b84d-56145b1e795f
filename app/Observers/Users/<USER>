<?php

namespace App\Observers\Users;

use App\Users\Address;
use Illuminate\Support\Str;

class AddressObserver
{
    /**
     * Handle the Address "created" event.
     *
     * @param \App\Users\Address $address
     *
     * @return void
     */
    public function created(Address $address)
    {
        //
    }

    /**
     * Handle the Address "updated" event.
     *
     * @param \App\Users\Address $address
     *
     * @return void
     */
    public function updated(Address $address)
    {
        if (!Str::contains(request()->url(), 'https://admin')) {

        }
    }

    /**
     * Handle the Address "deleted" event.
     *
     * @param \App\Users\Address $address
     *
     * @return void
     */
    public function deleted(Address $address)
    {
        //
    }

    /**
     * Handle the Address "restored" event.
     *
     * @param \App\Users\Address $address
     *
     * @return void
     */
    public function restored(Address $address)
    {
        //
    }

    /**
     * Handle the Address "force deleted" event.
     *
     * @param \App\Users\Address $address
     *
     * @return void
     */
    public function forceDeleted(Address $address)
    {
        //
    }
}
