<?php

namespace App\Observers\Users;

use App\Users\Phone;

class PhoneObserver
{
    /**
     * Handle the Phone "created" event.
     *
     * @param \App\Users\Phone $phone
     *
     * @return void
     */
    public function created(Phone $phone)
    {
        //
    }

    /**
     * Handle the Phone "updated" event.
     *
     * @param \App\Users\Phone $phone
     *
     * @return void
     */
    public function updated(Phone $phone)
    {
        //
    }

    /**
     * Handle the Phone "deleted" event.
     *
     * @param \App\Users\Phone $phone
     *
     * @return void
     */
    public function deleted(Phone $phone)
    {
        //
    }

    /**
     * Handle the Phone "restored" event.
     *
     * @param \App\Users\Phone $phone
     *
     * @return void
     */
    public function restored(Phone $phone)
    {
        //
    }

    /**
     * Handle the Phone "force deleted" event.
     *
     * @param \App\Users\Phone $phone
     *
     * @return void
     */
    public function forceDeleted(Phone $phone)
    {
        //
    }
}
