<?php

namespace App\Observers\Calendars;

use App\Calendars\Calendar;

class CalendarObserver
{
    /**
     * Handle the Calendar "created" event.
     *
     * @param \App\Calendars\Calendar $calendar
     *
     * @return void
     */
    public function created(Calendar $calendar)
    {
        $calendar->generateUlid();
    }

    /**
     * Handle the Calendar "updated" event.
     *
     * @param \App\Calendars\Calendar $calendar
     *
     * @return void
     */
    public function updated(Calendar $calendar)
    {
    }

    /**
     * Handle the Calendar "deleted" event.
     *
     * @param \App\Calendars\Calendar $calendar
     *
     * @return void
     */
    public function deleted(Calendar $calendar)
    {
        //
    }

    /**
     * Handle the Calendar "restored" event.
     *
     * @param \App\Calendars\Calendar $calendar
     *
     * @return void
     */
    public function restored(Calendar $calendar)
    {
        //
    }

    /**
     * Handle the Calendar "force deleted" event.
     *
     * @param \App\Calendars\Calendar $calendar
     *
     * @return void
     */
    public function forceDeleted(Calendar $calendar)
    {
        //
    }
}
