<?php

namespace App\Observers\Programs;

use App\Events\Programs\Users\ProgramUserCheckinBroadcast;
use App\Programs\ProgramUserCheckin;

class ProgramUserCheckinObserver
{
    /**
     * Handle the Post "created" event.
     *
     * @param \App\Programs\ProgramUserCheckin $checkin
     *
     * @return void
     */
    public function created(ProgramUserCheckin $checkin)
    {
        if (config('app.env') == 'local') {
            return;
        }

        ProgramUserCheckinBroadcast::dispatch($checkin);
    }

    /**
     * Handle the Comment "updated" event.
     *
     * @param \App\Programs\ProgramUserCheckin $checkin
     *
     * @return void
     */
    public function updated(ProgramUserCheckin $checkin)
    {
        if (config('app.env') == 'local') {
            return;
        }

        ProgramUserCheckinBroadcast::dispatch($checkin);
    }

    /**
     * Handle the Comment "deleted" event.
     *
     * @param \App\Programs\ProgramUserCheckin $checkin
     *
     * @return void
     */
    public function deleted(ProgramUserCheckin $checkin)
    {
        //
    }

    /**
     * Handle the Comment "restored" event.
     *
     * @param \App\Programs\ProgramUserCheckin $checkin
     *
     * @return void
     */
    public function restored(ProgramUserCheckin $checkin)
    {
        //
    }

    /**
     * Handle the Comment "force deleted" event.
     *
     * @param \App\Programs\ProgramUserCheckin $checkin
     *
     * @return void
     */
    public function forceDeleted(ProgramUserCheckin $checkin)
    {
        //
    }
}
