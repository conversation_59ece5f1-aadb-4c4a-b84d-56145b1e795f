<?php

namespace App\Observers\Programs;

use App\Events\Programs\Users\ProgramUserCheckinBroadcast;
use App\Groups\Comment;
use App\Programs\ProgramUserCheckin;

class ProgramUserCheckinObserver
{
    /**
     * Handle the Post "created" event.
     *
     * @param \App\Programs\ProgramUserCheckin $checkin
     *
     * @return void
     */
    public function created(ProgramUserCheckin $checkin)
    {
        ProgramUserCheckinBroadcast::dispatch($checkin);

        if (config('app.env') != 'production') {
            // return;
        }
    }

    /**
     * Handle the Comment "updated" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function updated(Comment $comment)
    {
        //
    }

    /**
     * Handle the Comment "deleted" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function deleted(Comment $comment)
    {
        //
    }

    /**
     * Handle the Comment "restored" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function restored(Comment $comment)
    {
        //
    }

    /**
     * Handle the Comment "force deleted" event.
     *
     * @param \App\Groups\Comment $comment
     *
     * @return void
     */
    public function forceDeleted(Comment $comment)
    {
        //
    }
}
