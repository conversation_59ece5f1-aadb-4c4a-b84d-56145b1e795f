<?php

namespace App\Observers\Visitors;

use App\Jobs\Visitors\ProcessNewHistoryEvent;
use App\Visitors\History;

class HistoryObserver
{
    /**
     * Handle the History "created" event.
     *
     * @param \App\Visitors\History $history
     *
     * @return void
     */
    public function created(History $history)
    {
        ProcessNewHistoryEvent::dispatch($history);
    }

    /**
     * Handle the History "updated" event.
     *
     * @param \App\Visitors\History $history
     *
     * @return void
     */
    public function updated(History $history)
    {
        //
    }

    /**
     * Handle the History "deleted" event.
     *
     * @param \App\Visitors\History $history
     *
     * @return void
     */
    public function deleted(History $history)
    {
        //
    }

    /**
     * Handle the History "restored" event.
     *
     * @param \App\Visitors\History $history
     *
     * @return void
     */
    public function restored(History $history)
    {
        //
    }

    /**
     * Handle the History "force deleted" event.
     *
     * @param \App\Visitors\History $history
     *
     * @return void
     */
    public function forceDeleted(History $history)
    {
        //
    }
}
