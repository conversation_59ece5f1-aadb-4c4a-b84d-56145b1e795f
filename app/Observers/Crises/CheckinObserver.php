<?php

namespace App\Observers\Crises;

use App\Crises\Checkin;
use App\Jobs\Crises\ProcessHelpRequestCheckin;

class CheckinObserver
{
    /**
     * Handle the Checkin "created" event.
     *
     * @param \App\Crises\Checkin $checkin
     *
     * @return void
     */
    public function created(Checkin $checkin)
    {
        //
    }

    /**
     * Handle the Checkin "updated" event.
     *
     * @param \App\Crises\Checkin $checkin
     *
     * @return void
     */
    public function updated(Checkin $checkin)
    {
        //
    }

    public function saving(Checkin $checkin)
    {
        //
    }

    public function saved(Checkin $checkin)
    {
        // Help Notifications
        if ($checkin->isHelpRequest() || $checkin->isUrgentHelpRequest()) {
            ProcessHelpRequestCheckin::dispatch($checkin);
        }
    }

    /**
     * Handle the Checkin "deleted" event.
     *
     * @param \App\Crises\Checkin $checkin
     *
     * @return void
     */
    public function deleted(Checkin $checkin)
    {
        //
    }

    /**
     * Handle the Checkin "restored" event.
     *
     * @param \App\Crises\Checkin $checkin
     *
     * @return void
     */
    public function restored(Checkin $checkin)
    {
        //
    }

    /**
     * Handle the Checkin "force deleted" event.
     *
     * @param \App\Crises\Checkin $checkin
     *
     * @return void
     */
    public function forceDeleted(Checkin $checkin)
    {
        //
    }
}
