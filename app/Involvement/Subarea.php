<?php

namespace App\Involvement;

use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Subarea extends Model
{
    protected $table = 'involvement_subareas';

    protected $casts = [
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'men_only'      => 'boolean',
        'women_only'    => 'boolean',
        'baptized_only' => 'boolean',
    ];

    protected $fillable = [
        'involvement_area_id',
        'name',
        'description',
        'men_only',
        'women_only',
        'baptized_only',
        'approved_to_teach_only',
        'completed_background_check_only',
        'sort_id',
        'is_hidden',
        'auto_approve_for_assignments',
        'auto_show_in_volunteer_list',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->whereHas('area', function ($query1) use ($user) {
            $query1->whereHas('category', function ($query2) use ($user) {
                $query2->visibleTo($user);
            });
        });
    }

    public function area()
    {
        return $this->belongsTo(Area::class, 'involvement_area_id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'involvement_category_id');
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'involvement_to_user', 'involvement_subarea_id', 'user_id')
            ->as('settings')
            ->withPivot(Category::$settings_pivot_fields);
    }

    public function scopeIsVisible($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeApplyRestrictions($query, User $user)
    {
        $query->isVisible();

        // Baptized Users ?
        $query->where(function ($query) use ($user) {
            $query->where('baptized_only', false)
                ->orWhere(function ($query) use ($user) {
                    $query->where('baptized_only', true)
                        ->whereRaw("'1' = ?", [$user->is_baptized ? '1' : '0']);
                });
        });

        // Teachers ?
        $query->where(function ($query) use ($user) {
            $query->where('approved_to_teach_only', false)
                ->orWhere(function ($query) use ($user) {
                    $query->where('approved_to_teach_only', true)
                        ->whereRaw("'1' = ?", [$user->can_teach ? '1' : '0']);
                });
        });

        // Background check ?
        $query->where(function ($query) use ($user) {
            $query->where('completed_background_check_only', false)
                ->orWhere(function ($query) use ($user) {
                    $query->where('completed_background_check_only', true)
                        ->whereRaw("'1' = ?", [$user->date_background_check ? '1' : '0']);
                });
        });

        if ($user->gender == 'male') {
            $query->where('men_only', true);
        } elseif ($user->gender == 'female') {
            $query->where('women_only', true);
        }
    }

    // DUPLICATED ABOVE
    public function scopeForBaptized($query, User $user)
    {
        $query->where(function ($query) use ($user) {
            $query->where('baptized_only', false)
                ->orWhere(function ($query) use ($user) {
                    $query->where('baptized_only', true)
                        ->whereRaw("'1' = ?", [$user->is_baptized ? '1' : '0']);
                });
        });
    }

    // DUPLICATED ABOVE
    public function scopeForGender($query, User $user)
    {
        if ($user->gender == 'male') {
            $query->where('men_only', true);
        } elseif ($user->gender == 'female') {
            $query->where('women_only', true);
        }
    }

    public function isMenAndWomen()
    {
        return $this->men_only && $this->women_only;
    }

    public function isMenOnly()
    {
        return $this->men_only && !$this->women_only;
    }

    public function isWomenOnly()
    {
        return !$this->men_only && $this->women_only;
    }

    public function isBaptizedOnly()
    {
        return $this->baptized_only;
    }

    public function updateSelectionsBasedOnRestrictions()
    {
        $this->updateGenderedSelections();
        $this->updateBaptizedSelections();
        $this->updateTeachersOnlySelections();
        $this->updateBackgroundCheckOnlySelections();
    }

    public function updateBaptizedSelections()
    {
        if ($this->baptized_only) {
            $this->removeAllBaptizedSelections();
        }
    }

    public function updateGenderedSelections()
    {
        // Remove selections that now don't belong!
        if ($this->isMenOnly()) {
            // Remove all women attachments.
            $this->removeAllGenderedSelections('female');
        }
        if ($this->isWomenOnly()) {
            // Remove all men attachments.
            $this->removeAllGenderedSelections('male');
        }
    }

    public function updateTeachersOnlySelections()
    {
        if ($this->approved_to_teach_only) {
            $this->removeAllNonTeacherSelections();
        }
    }

    public function updateBackgroundCheckOnlySelections()
    {
        if ($this->completed_background_check_only) {
            $this->removeAllNotBackgroundCheckedSelections();
        }
    }

    public function removeAllGenderedSelections($gender)
    {
        if ($gender !== 'male' && $gender !== 'female') {
            return false;
        }

        // Subarea users for this gender.
        foreach ($this->users()->where('gender', $gender)->get() as $user) {
            DB::table('involvement_to_user')
                ->where('user_id', $user->id)
                ->where('involvement_subarea_id', $this->id)
                ->limit(1)
                ->delete();
        }

        return true;
    }

    // Remove all users who are NOT baptized.
    public function removeAllBaptizedSelections()
    {
        if (!$this->baptized_only) {
            return false;
        }

        // Subarea users for is_baptized.
        foreach ($this->users()->whereNull('is_baptized')->get() as $user) {
            DB::table('involvement_to_user')
                ->where('user_id', $user->id)
                ->where('involvement_subarea_id', $this->id)
                ->limit(1)
                ->delete();
        }

        return true;
    }

    // Remove all users who are NOT authorized to teach.
    public function removeAllNonTeacherSelections()
    {
        if (!$this->approved_to_teach_only) {
            return false;
        }

        // Subarea users for this.
        foreach ($this->users()->whereNull('can_teach')->get() as $user) {
            DB::table('involvement_to_user')
                ->where('user_id', $user->id)
                ->where('involvement_subarea_id', $this->id)
                ->limit(1)
                ->delete();
        }

        return true;
    }

    // Remove all users who are NOT completed a background check.
    public function removeAllNotBackgroundCheckedSelections()
    {
        if (!$this->completed_background_check_only) {
            return false;
        }

        // Subarea users for this.
        foreach ($this->users()->whereNull('date_background_check')->get() as $user) {
            DB::table('involvement_to_user')
                ->where('user_id', $user->id)
                ->where('involvement_subarea_id', $this->id)
                ->limit(1)
                ->delete();
        }

        return true;
    }
}
