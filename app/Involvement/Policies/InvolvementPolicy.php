<?php

namespace App\Involvement\Policies;

use App\Involvement\Category;
use App\Users\User;

class InvolvementPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.involvement')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('involvement.index');
    }

    public function manageVolunteers(User $user)
    {
        return $user->hasPermission('involvement.manage');
    }

    public function search(User $user)
    {
        return $user->hasPermission('involvement.index');
    }

    public function create(User $user)
    {
        return $user->hasPermission('involvement.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('involvement.manage');
    }

    public function edit(User $user, Category $category)
    {
        return $user->hasPermission('involvement.manage');
    }

    public function save(User $user, Category $category)
    {
        return $user->hasPermission('involvement.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('involvement.index');
    }

    public function delete(User $user)
    {
        return $user->hasPermission('involvement.manage');
    }

    public function selections(User $user)
    {
        return $user->hasPermission('involvement.selections')
               || $user->hasPermission('app.involvement.selections');
    }
}