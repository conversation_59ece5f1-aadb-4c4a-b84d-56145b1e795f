<?php

namespace App\Involvement\Services;

use App\Involvement\Category;

class DeleteCategory
{
    protected $attributes = [];
    protected $category;

    public function __construct(Category $category)
    {
        $this->category = $category;
    }

    public function delete()
    {
        # Areas
        foreach ($this->category->areas as $area) {
            # Subareas
            foreach ($area->subareas as $subarea) {
                # Delete Subarea
                (new DeleteSubarea($subarea))->delete();
            }

            # Delete Area
            (new DeleteArea($area))->delete();
        }

        $this->category->users()->detach();

        $this->category->delete();

        return true;
    }
}
