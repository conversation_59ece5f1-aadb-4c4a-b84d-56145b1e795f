<?php

namespace App\Involvement\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Services\DeleteArea;

class AreaController extends Controller
{
    public function create(Category $category)
    {
        return view('admin.involvement.areas.create')
            ->with('category', $category);
    }

    public function store(Category $category)
    {
        try {
            $area = Area::create([
                'involvement_category_id'         => $category->id,
                'name'                            => request('name'),
                'men_only'                        => request('men_only', 0),
                'women_only'                      => request('women_only', 0),
                'baptized_only'                   => request('baptized_only', 0),
                'approved_to_teach_only'          => request('approved_to_teach_only', 0),
                'completed_background_check_only' => request('completed_background_check_only', 0),
            ]);
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.involvement.categories.view', $category))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Category $category, Area $area)
    {
        return view('admin.involvement.areas.edit')
            ->with('category', $category)
            ->with('area', $area);
    }

    public function save(Category $category, Area $area)
    {
        try {
            $area->name                            = request()->get('name');
            $area->men_only                        = request()->get('men_only', 0);
            $area->women_only                      = request()->get('women_only', 0);
            $area->baptized_only                   = request()->get('baptized_only', 0);
            $area->approved_to_teach_only          = request()->get('approved_to_teach_only', 0);
            $area->completed_background_check_only = request()->get('completed_background_check_only', 0);

            $area->save();

            // Remove selections that now don't belong!
            $area->updateSelectionsBasedOnRestrictions();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.involvement.categories.view', $category))
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Category $category, Area $area)
    {
        return view('admin.involvement.areas.delete')
            ->with('category', $category)
            ->with('area', $area);
    }

    public function destroy(Category $category, Area $area)
    {
        try {
            (new DeleteArea($area))->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Delete successful.');
    }
}
