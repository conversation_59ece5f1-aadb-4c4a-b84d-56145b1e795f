<?php

namespace App\Involvement\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Services\DeleteSubarea;
use App\Involvement\Subarea;

class SubareaController extends Controller
{
    public function create(Category $category, Area $area)
    {
        return view('admin.involvement.subareas.create')
            ->with('category', $category)
            ->with('area', $area);
    }

    public function store(Category $category, Area $area)
    {
        try {
            $subarea = Subarea::create([
                'involvement_area_id'             => $area->id,
                'name'                            => request('name'),
                'men_only'                        => request('men_only', 0),
                'women_only'                      => request('women_only', 0),
                'baptized_only'                   => request('baptized_only', 0),
                'approved_to_teach_only'          => request('approved_to_teach_only', 0),
                'completed_background_check_only' => request('completed_background_check_only', 0),
            ]);
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.involvement.categories.view', $category))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Category $category, Area $area, Subarea $subarea)
    {
        return view('admin.involvement.subareas.edit')
            ->with('category', $category)
            ->with('area', $area)
            ->with('subarea', $subarea);
    }

    public function save(Category $category, Area $area, Subarea $subarea)
    {
        try {
            $subarea->name                            = request()->get('name');
            $subarea->men_only                        = request()->get('men_only', 0);
            $subarea->women_only                      = request()->get('women_only', 0);
            $subarea->baptized_only                   = request()->get('baptized_only', 0);
            $subarea->approved_to_teach_only          = request()->get('approved_to_teach_only', 0);
            $subarea->completed_background_check_only = request()->get('completed_background_check_only', 0);

            $subarea->save();

            // Remove selections that now don't belong!
            $subarea->updateSelectionsBasedOnRestrictions();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(redirect(route('admin.involvement.categories.view', $category)))
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Category $category, Area $area, Subarea $subarea)
    {
        return view('admin.involvement.subareas.delete')
            ->with('category', $category)
            ->with('area', $area)
            ->with('subarea', $subarea);
    }

    public function destroy(Category $category, Area $area, Subarea $subarea)
    {
        try {
            (new DeleteSubarea($subarea))->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return back()->with('message.success', 'Delete successful.');
    }
}
