<?php

namespace App\Involvement\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Involvement\Area;
use App\Involvement\Category;

class InvolvementController extends Controller
{
    public function index()
    {
        $first_cat = Category::visibleTo(auth()->user())
            ->orderBy('sort_id', 'asc')
            ->first();

        if ($first_cat) {
            // Keep flash data because we're about to redirect somewhere.
            session()->reflash();

            return redirect(route('admin.involvement.categories.view', $first_cat));
        }

        return view('admin.involvement.index')
            ->with([
                'selected_category' => null,
            ]);

        return view('admin.involvement.index')->with([
            'categories' => Category::visibleTo(auth()->user())->get(),
        ]);
    }

    public function viewCategory(Category $category)
    {
        return view('admin.involvement.index')
            ->with([
                'selected_category' => $category,
            ]);
    }

    public function categoryUpdateSortId(Category $category)
    {
        $new_sort_id = request()->get('new_sort_id');

        // Start from our old sort ID
        $categories = Category::visibleTo(auth()->user())
            ->orderBy('sort_id', 'asc')
            ->get();

        $i = 0;
        foreach ($categories as $current_category) {
            if ($current_category->id == $category->id) {
                $category->sort_id    = $new_sort_id;
                $category->timestamps = false; // Don't update our `updated_at` field
                $category->save();
            } else {
                // Skip over the new_sort_id
                if ($i == $new_sort_id) {
                    $i++;
                }
                $current_category->sort_id    = $i;
                $current_category->timestamps = false; // Don't update our `updated_at` field
                $current_category->save();
                $i++;
            }
        }

        return response(200);
    }

    public function areaUpdateSortId(Area $area)
    {
        $new_sort_id = request()->get('new_sort_id');

        // Start from our old sort ID
        $areas = Area::visibleTo(auth()->user())
            ->where('involvement_category_id', $area->involvement_category_id)
            ->orderBy('sort_id', 'asc')
            ->get();

        $i = 0;
        foreach ($areas as $current_area) {
            if ($current_area->id == $area->id) {
                $area->sort_id    = $new_sort_id;
                $area->timestamps = false; // Don't update our `updated_at` field
                $area->save();
            } else {
                // Skip over the new_sort_id
                if ($i == $new_sort_id) {
                    $i++;
                }
                $current_area->sort_id    = $i;
                $current_area->timestamps = false; // Don't update our `updated_at` field
                $current_area->save();
                $i++;
            }
        }

        return response(200);
    }
}
