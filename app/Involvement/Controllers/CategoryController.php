<?php

namespace App\Involvement\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Services\DeleteCategory;
use App\Involvement\Subarea;
use Illuminate\Support\Facades\Auth;

class CategoryController extends Controller
{
    public function create()
    {
        return view('admin.involvement.categories.create');
    }

    public function store()
    {
        try {
            $category = Category::create([
                'account_id'                      => Auth::user()->account->id,
                'account_location_id'             => Auth::user()->account->location ? Auth::user()->account->location->id : null,
                'name'                            => request('name'),
                'description'                     => request('description'),
                'men_only'                        => request('men_only'),
                'women_only'                      => request('women_only', 0),
                'baptized_only'                   => request('baptized_only', 0),
                'approved_to_teach_only'          => request('approved_to_teach_only', 0),
                'completed_background_check_only' => request('completed_background_check_only', 0),
                'sort_id'                         => request('sort_id', 0),
            ]);
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.involvement.index'))
            ->with('message.success', 'Saved successfully.');
    }

    public function edit(Category $category)
    {
        return view('admin.involvement.categories.edit')
            ->withCategory($category);
    }

    public function save(Category $category)
    {
        try {
            $category->name                            = request()->get('name');
            $category->description                     = request()->get('description');
            $category->men_only                        = request()->get('men_only');
            $category->women_only                      = request()->get('women_only', 0);
            $category->baptized_only                   = request()->get('baptized_only', 0);
            $category->approved_to_teach_only          = request()->get('approved_to_teach_only', 0);
            $category->completed_background_check_only = request()->get('completed_background_check_only', 0);
            $category->sort_id                         = request()->get('sort_id', 0);

            $category->save();

            // Remove selections that now don't belong!
            $category->updateSelectionsBasedOnRestrictions();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.involvement.index')
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Category $category)
    {
        return view('admin.involvement.categories.delete')
            ->with('category', $category);
    }

    public function destroy(Category $category)
    {
        try {
            (new DeleteCategory($category))->delete();
        } catch (\Exception $e) {
            return redirect(route('admin.involvement.index'))->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.involvement.index'))->with('message.success', 'Delete successful.');
    }

    public function getAreas(Category $category)
    {
        $areas = Area::where('involvement_category_id', $category->id)->get();

        return response()->json($areas->toArray());
    }

    public function getSubareas(Category $category, Area $area)
    {
        $subareas = Subarea::where('involvement_area_id', $area->id)->get();

        return response()->json($subareas->toArray());
    }
}
