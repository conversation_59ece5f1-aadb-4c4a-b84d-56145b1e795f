<?php

namespace App\Finance\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ExpenseIndexExport implements FromView
{
    public $expenses;
    public $expenses_query;

    public function __construct($expenses_query)
    {
        $this->expenses_query = $expenses_query;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->expenses_query->get();
    }

    public function view(): View
    {
        return view('admin.finance.exports.expense-index-table', [
            'is_export' => true,
            'expenses'  => $this->collection(),
        ]);
    }
}
