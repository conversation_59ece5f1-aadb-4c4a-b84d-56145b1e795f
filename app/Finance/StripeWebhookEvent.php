<?php

namespace App\Finance;

use App\Users\Payment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StripeWebhookEvent extends Model
{
    use SoftDeletes;

    protected $table = 'stripe_webhook_events';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'is_failure' => 'boolean',
        'is_warning' => 'boolean',
        'is_success' => 'boolean',
        'event'      => 'json',
    ];

    protected $fillable = [
        'stripe_event_id',
        'stripe_charge_id',
        'event_type',
        'event',
        'is_failure',
        'is_warning',
        'is_success',
    ];

    public function payment()
    {
        return $this->belongsTo(Payment::class, 'stripe_charge_id', 'stripe_charge_id');
    }
}
