<?php

namespace App\Finance;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContributionReportTransaction extends Model
{
    use SoftDeletes;

    protected $table = 'user_contribution_report_transactions';

    protected $casts = [
        'account_id'                                => 'integer',
        'user_contribution_report_id'               => 'integer',
        'user_id'                                   => 'integer',
        'finance_transaction_id'                    => 'integer',
        'amount'                                    => 'integer',
        'posted_at'                                 => 'datetime',
        'modified_at'                               => 'datetime',
        'modified_from_user_contribution_report_id' => 'integer',
        'notes'                                     => 'text',
    ];

    protected $fillable = [
        'account_id',
        'user_contribution_report_id',
        'user_id',
        'finance_transaction_id',
        'amount',
        'posted_at',
        'modified_at',
        'modified_from_user_contribution_report_id',
        'notes',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function contributionReport()
    {
        return $this->belongsTo(ContributionReport::class, 'user_contribution_report_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'finance_transaction_id');
    }

    // Convert cents to dollars
    public function formatAmount($amount = null)
    {
        if (!$amount) {
            $amount = $this->amount;
        }

        return \Brick\Money\Money::ofMinor($amount, $this->currency ?: 'USD')->getAmount();
    }
}
