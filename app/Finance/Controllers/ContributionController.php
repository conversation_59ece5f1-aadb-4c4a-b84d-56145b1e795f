<?php

namespace App\Finance\Controllers;

use App\Accounts\Payout;
use App\Base\Http\Controllers\Controller;
use App\Exports\Finance\ContributionReportExport;
use App\Exports\Finance\PayoutReportExport;
use App\Users\Payment;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use TCPDF;

class ContributionController extends Controller
{
    public function index()
    {
        return view('admin.finance.contributions.index');
    }

    public function createContributions()
    {
        return view('admin.finance.contributions.create');
    }

    public function downloadContributionReport()
    {
        // Get our date from the request or default to today.
        $from_date = request('from_date') ?: date('Y-m-d');
        $to_date   = request('to_date') ?: date('Y-m-d');

        $contributions = Payment::visibleTo(Auth::user())
            ->ContributionsOnly()
            ->with('bucket')
            ->where('created_at', '>=', $from_date . ' 00:00:00')
            ->where('created_at', '<=', $to_date . ' 23:59:59')
            ->orderBy('created_at', 'DESC');

        // Export as Excel file
        if (request()->get('export') == 'xlsx') {
            return Excel::download(new ContributionReportExport($contributions), 'Contribution Report.xlsx');
        }

        $contributions = $contributions->get();

        $view = view('admin.finance.giving.pdfs.contribution-report')
            ->with('contributions', $contributions)
            ->with('from_date', $from_date)
            ->with('to_date', $to_date);

        $pdf = new TCPDF;

        @$pdf->SetSubject("Contribution Report - ' . $from_date . ' - ' . $to_date . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Contribution Report.pdf", 'I');
        exit;
    }

    public function downloadPayoutReport(Payout $payout)
    {
        // Export as Excel file
        if (request()->get('export') == 'xlsx') {
            return Excel::download(new PayoutReportExport($payout), 'Payout Report - ' . $payout->deposited_at->format('Y-m-d') . '.xlsx');
        }

        $view = view('admin.finance.giving.pdfs.payout-report')
            ->with('payout', $payout)
            ->with('payments', $payout->userPayments);

        $pdf = new TCPDF;

        @$pdf->SetSubject("Payout Report - ' . $payout->deposited_at->format('Y-m-d') . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Payout Report.pdf", 'I');
        exit;
    }
}

/*
access_token: "sk_test_RJq5uYYLdFkp9nVAxQXxoIYo00OqEkoDeF"
livemode: false
refresh_token: "rt_H0pET6iDWQq63DLbghkhOGdwPv2gEm3ghQj311evNKw5El2C"
token_type: "bearer"
stripe_publishable_key: "pk_test_c3CNaV7MYpBT2TlVkJJpr2yP00AS3gRfwj"
stripe_user_id: "Yod5WD7tiQcS7FTsGPdEj0ljdADQ7UfO"
scope: "read_write"
*/











