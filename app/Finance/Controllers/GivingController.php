<?php

namespace App\Finance\Controllers;

use App\Accounts\Payout;
use App\Base\Http\Controllers\Controller;
use App\Exports\Finance\ContributionReportExport;
use App\Exports\Finance\PayoutReportExport;
use App\Users\Payment;
use Carbon\Carbon;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Stripe\Exception\OAuth\OAuthErrorException;
use TCPDF;

class GivingController extends Controller
{
    public function __construct()
    {
        Paginator::useTailwind();
    }

    public function index()
    {
        if (auth()->user()->account->stripe_account_id) {
            \Stripe\Stripe::setApiKey(config('services.stripe.connect.secret'));

            $stripe_account = \Stripe\Account::retrieve(auth()->user()->account->stripe_account_id);
        } else {
            $stripe_account = null;
        }

        return view('admin.finance.giving.index')
            ->with('stripe_account', $stripe_account);
    }

    public function contributionReport()
    {
        $contributions_count = Payment::visibleTo(auth()->user())
            ->with('bucket')
            ->ContributionsOnly()
            ->where('created_at', '>', Carbon::now()->sub('7 days'))
            ->orderBy('created_at', 'DESC')
            ->count();

        return view('admin.finance.giving.contributions')
            ->with('contributions_count', $contributions_count);
    }

    public function payoutReport()
    {
        return view('admin.finance.giving.payouts')
            ->with('payouts', Payout::visibleTo(auth()->user())->orderBy('deposited_at', 'desc')->paginate(30));
    }

    public function settings()
    {
        return view('admin.finance.giving.settings');
    }

    public function saveStripePublishableKey()
    {
        // Make sure the key is a valid Stripe Publishable Key and not doubling up on the "pk_live_" prefix.
        if (!Str::startsWith(request()->get('stripe_account_publishable_key'), 'pk_live_') || Str::startsWith(request()->get('stripe_account_publishable_key'), 'pk_live_pk_live')) {
            return redirect()
                ->route('admin.finance.giving.index')
                ->with('message.failure', 'Invalid Stripe Publishable Key. Please try again. The key will start with "pk_live_".');
        }

        $account = auth()->user()->account;

        $account->stripe_account_publishable_key = request()->get('stripe_account_publishable_key');
        $account->save();

        return redirect()
            ->route('admin.finance.giving.index')
            ->with('message.success', 'Stripe Publishable Key saved successfully!');
    }

    public function stripeConnectRedirect()
    {
        $auth_code = request()->get('code');

        $error             = request()->get('error');
        $error_description = request()->get('error_description');

        if ($error) {
            Log::error('GivingController::stripeConnectRedirect -- Account #' . auth()->user()->account_id . ' -- Stripe returned an error after attempting to Connect with Stripe: ' . $error_description);

            redirect()->route('admin.finance.giving.index')
                ->with('message.failure', $error_description);
        }

        \Stripe\Stripe::setApiKey(config('services.stripe.connect.secret'));

        try {
            $response = \Stripe\OAuth::token([
                'grant_type' => 'authorization_code',
                'code'       => $auth_code,
            ]);

            if ($response->error) {
                Log::error('GivingController::stripeConnectRedirect -- Account #' . auth()->user()->account_id . ' -- Stripe redirected successfully, but then failed getting Client information: ' . $response->error . ': ' . $response->error_description);

                redirect()->route('admin.finance.giving.index')
                    ->with('message.failure', $error_description);
            }

            // Access the connected account id in the response
            $connected_account_id = $response->stripe_user_id;

            $account = auth()->user()->account;

            $account->stripe_account_id = $connected_account_id;
            $account->save();

            return redirect()
                ->route('admin.finance.giving.index')
                ->with('message.success', 'Stripe account successfully connected!');
        } catch (OAuthErrorException $e) {
            Log::error('GivingController::stripeConnectRedirect -- Account #' . auth()->user()->account_id . ' -- Stripe redirected successfully, but then failed getting Client information: ' . $e->getMessage());

            redirect()->route('admin.finance.giving.index')
                ->with('message.failure', $error_description);
        } catch (\Exception $e) {
            Log::error('GivingController::stripeConnectRedirect -- Account #' . auth()->user()->account_id . ' -- Stripe redirected successfully, but then failed getting Client information: ' . $e->getMessage());

            redirect()->route('admin.finance.giving.index')
                ->with('message.failure', $error_description);
        }

        return redirect()->route('admin.finance.giving.index')
            ->with('message.failure', 'An unknown error occurred.  Please try again.');
    }

    public function downloadContributionReport()
    {
        // Get our date from the request or default to today.
        $from_date = request('from_date') ?: date('Y-m-d');
        $to_date   = request('to_date') ?: date('Y-m-d');

        $contributions = Payment::visibleTo(auth()->user())
            ->ContributionsOnly()
            ->with('bucket')
            ->where('created_at', '>=', $from_date . ' 00:00:00')
            ->where('created_at', '<=', $to_date . ' 23:59:59')
            ->orderBy('created_at', 'DESC');

        // Export as Excel file
        if (request()->get('export') == 'xlsx') {
            return Excel::download(new ContributionReportExport($contributions), 'Contribution Report.xlsx');
        }

        $contributions = $contributions->get();

        $view = view('admin.finance.giving.pdfs.contribution-report')
            ->with('contributions', $contributions)
            ->with('from_date', $from_date)
            ->with('to_date', $to_date);

        $pdf = new TCPDF;

        @$pdf->SetSubject("Contribution Report - ' . $from_date . ' - ' . $to_date . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Contribution Report.pdf", 'I');
        exit;
    }

    public function downloadPayoutReport(Payout $payout)
    {
        // Export as Excel file
        if (request()->get('export') == 'xlsx') {
            return Excel::download(new PayoutReportExport($payout), 'Payout Report - ' . $payout->deposited_at->format('Y-m-d') . '.xlsx');
        }

        $view = view('admin.finance.giving.pdfs.payout-report')
            ->with('payout', $payout)
            ->with('payments', $payout->userPayments);

        $pdf = new TCPDF;

        @$pdf->SetSubject("Payout Report - ' . $payout->deposited_at->format('Y-m-d') . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Payout Report.pdf", 'I');
        exit;
    }
}

/*
access_token: "sk_test_RJq5uYYLdFkp9nVAxQXxoIYo00OqEkoDeF"
livemode: false
refresh_token: "rt_H0pET6iDWQq63DLbghkhOGdwPv2gEm3ghQj311evNKw5El2C"
token_type: "bearer"
stripe_publishable_key: "pk_test_c3CNaV7MYpBT2TlVkJJpr2yP00AS3gRfwj"
stripe_user_id: "Yod5WD7tiQcS7FTsGPdEj0ljdADQ7UfO"
scope: "read_write"
*/