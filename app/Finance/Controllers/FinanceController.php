<?php

namespace App\Finance\Controllers;

use App\Accounts\FinanceBucket;
use App\Accounts\Payout;
use App\Base\Http\Controllers\Controller;
use App\Finance\Budget;
use App\Finance\Transaction;
use App\Users\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class FinanceController extends Controller
{
    public function index()
    {
        if (request()->has('month')) {
            $month = request()->get('month');
        } else {
            $month = now()->format('m');
        }
        if (request()->has('year')) {
            $year = request()->get('year');
        } else {
            $year = now()->format('Y');
        }

        $report_date            = Carbon::createFromDate($year, $month, 1);
        $last_month_report_date = clone $report_date;
        $last_month_report_date->subMonth();

        $contributions_count = Payment::visibleTo(Auth::user())
            ->with('bucket')
            ->ContributionsOnly()
            ->whereMonth('created_at', $month)
            ->whereYear('created_at', $year)
            ->orderBy('created_at', 'DESC')
            ->count();

        $total_income                  = Transaction::visibleTo(auth()->user())
            ->isIncome()
            ->forMonth($month)
            ->forYear($year)
            ->sum('amount');
        $total_income_count            = Transaction::visibleTo(auth()->user())
            ->isIncome()
            ->forMonth($month)
            ->forYear($year)
            ->count();
        $last_month_total_income       = Transaction::visibleTo(auth()->user())
            ->isIncome()
            ->forMonth($last_month_report_date->format('n'))
            ->forYear($last_month_report_date->format('Y'))
            ->sum('amount');
        $last_month_total_income_count = Transaction::visibleTo(auth()->user())
            ->isIncome()
            ->forMonth($last_month_report_date->format('n'))
            ->forYear($last_month_report_date->format('Y'))
            ->count();

        $total_expense                  = Transaction::visibleTo(auth()->user())
            ->isExpense()->isNotIncome()
            ->forMonth($month)
            ->forYear($year)
            ->sum('amount');
        $total_expense_count            = Transaction::visibleTo(auth()->user())
            ->isExpense()->isNotIncome()
            ->forMonth($month)
            ->forYear($year)
            ->count();
        $last_month_total_expense       = Transaction::visibleTo(auth()->user())
            ->isExpense()->isNotIncome()
            ->forMonth($last_month_report_date->format('n'))
            ->forYear($last_month_report_date->format('Y'))
            ->sum('amount');
        $last_month_total_expense_count = Transaction::visibleTo(auth()->user())
            ->isExpense()->isNotIncome()
            ->forMonth($last_month_report_date->format('n'))
            ->forYear($last_month_report_date->format('Y'))
            ->count();

        return view('admin.finance.index')
            ->with('report_date', $report_date)
            ->with('contributions_count', $contributions_count)
            ->with('payouts', Payout::visibleTo(Auth::user())->orderBy('deposited_at', 'desc')->paginate(30))
            ->with('buckets', FinanceBucket::visibleTo(auth()->user())->get())
            ->with('budgets', Budget::visibleTo(auth()->user())->get())
            ->with('total_income', $total_income)
            ->with('last_month_total_income', $last_month_total_income)
            ->with('total_expense', $total_expense)
            ->with('last_month_total_expense', $last_month_total_expense)
            ->with('total_income_count', $total_income_count)
            ->with('last_month_total_income_count', $last_month_total_income_count)
            ->with('total_expense_count', $total_expense_count)
            ->with('last_month_total_expense_count', $last_month_total_expense_count)
            ->with(
                'income_budgets',
                FinanceBucket::visibleTo(auth()->user())
                    ->isIncome()
                    ->whereHas('budgets', function ($query) {
                        $query->ForYear(now()->format('Y'));
                    })
                    ->orderBy('name')
                    ->get()
            )
            ->with(
                'expense_budgets',
                FinanceBucket::visibleTo(auth()->user())
                    ->isExpense()
                    ->whereHas('budgets', function ($query) {
                        $query->ForYear(now()->format('Y'));
                    })
                    ->orderBy('name')
                    ->get()
            );
    }

}
