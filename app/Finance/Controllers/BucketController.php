<?php

namespace App\Finance\Controllers;

use App\Accounts\FinanceBucket;
use App\Accounts\Payout;
use App\Base\Http\Controllers\Controller;
use App\Exports\Finance\ContributionReportExport;
use App\Exports\Finance\PayoutReportExport;
use App\Finance\Budget;
use App\Finance\Services\CreateBudget;
use Maatwebsite\Excel\Facades\Excel;
use TCPDF;

class BucketController extends Controller
{
    public function index()
    {
        return view('admin.finance.buckets.index');
    }

    public function create()
    {
        return view('admin.finance.buckets.create');
    }

    public function view(FinanceBucket $bucket)
    {
        return view('admin.finance.buckets.view')
            ->with('bucket', $bucket);
    }

    public function budgets(FinanceBucket $bucket)
    {
        return view('admin.finance.buckets.view-budgets')
            ->with('bucket', $bucket);
    }

    public function createBudgetModal(FinanceBucket $bucket)
    {
        return view('admin.finance.buckets.modals.budget-create')
            ->with('bucket', $bucket);
    }

    public function createBudget(FinanceBucket $bucket)
    {
        try {
            (new CreateBudget())
                ->forBucket($bucket)
                ->setYearlyBudget(request('yearly_budget') * 100) // In Cents
                ->forYear(request('year'))
                ->setName(request('name'))
                ->setDescription(request('description'))
                ->create();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.finance.buckets.budgets', $bucket)
                ->with('message.failure', 'There was an issue creating a new budget!  Please try again.');
        }

        return redirect()
            ->route('admin.finance.buckets.budgets', $bucket)
            ->with('message.success', 'New budget created.');
    }

    public function editBudgetModal(FinanceBucket $bucket, Budget $budget)
    {
        return view('admin.finance.buckets.modals.budget-edit')
            ->with('bucket', $bucket)
            ->with('budget', $budget);
    }

    public function editBudget(FinanceBucket $bucket, Budget $budget)
    {
        try {
            $budget->yearly_budget = request('yearly_budget') * 100; // In Cents
            $budget->description   = request('description');

            $budget->save();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.finance.buckets.budgets', $bucket)
                ->with('message.failure', 'There was an issue editing the budget!  Please try again.');
        }

        return redirect()
            ->route('admin.finance.buckets.budgets', $bucket)
            ->with('message.success', 'Budget changes saved.');
    }

    public function deleteBudget(FinanceBucket $bucket, Budget $budget)
    {
        try {
            $budget->delete();
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.finance.buckets.budgets', $bucket)
                ->with('message.failure', 'There was an issue editing the budget!  Please try again.');
        }

        return redirect()
            ->route('admin.finance.buckets.budgets', $bucket)
            ->with('message.success', 'Budget deleted.');
    }

    public function edit(FinanceBucket $bucket)
    {
        return view('admin.finance.buckets.edit')
            ->with('bucket', $bucket);
    }

    public function editSave(FinanceBucket $bucket)
    {
        try {
            $bucket->name         = request('name');
            $bucket->description  = request('description');
            $bucket->account_code = request('account_code');

            if ($bucket->is_income) {
                $bucket->is_contribution_bucket = request('is_contribution') ? true : false;
            }
            if ($bucket->is_expense) {
                $bucket->is_reimbursement_bucket = request('is_reimbursement') ? true : false;
            }

            $bucket->save();
        } catch (\Exception $e) {
            return back()
                ->with('message.failure', 'There was an issue editing the bucket!  Please try again. ' . $e->getMessage());
        }

        return back()
            ->with('message.success', 'Bucket changes saved.');
    }

    public function downloadContributionReport()
    {
        // Get our date from the request or default to today.
        $from_date = request('from_date') ?: date('Y-m-d');
        $to_date   = request('to_date') ?: date('Y-m-d');

        $buckets = FinanceBucket::visibleTo(auth()->user())->get();

        // Export as Excel file
        if (request()->get('export') == 'xlsx') {
            return Excel::download(new ContributionReportExport($buckets), 'Contribution Report.xlsx');
        }

        $buckets = $buckets->get();

        $view = view('admin.finance.giving.pdfs.contribution-report')
            ->with('buckets', $buckets)
            ->with('from_date', $from_date)
            ->with('to_date', $to_date);

        $pdf = new TCPDF;

        @$pdf->SetSubject("Contribution Report - ' . $from_date . ' - ' . $to_date . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Contribution Report.pdf", 'I');
        exit;
    }

    public function downloadPayoutReport(Payout $payout)
    {
        // Export as Excel file
        if (request()->get('export') == 'xlsx') {
            return Excel::download(new PayoutReportExport($payout), 'Payout Report - ' . $payout->deposited_at->format('Y-m-d') . '.xlsx');
        }

        $view = view('admin.finance.giving.pdfs.payout-report')
            ->with('payout', $payout)
            ->with('payments', $payout->userPayments);

        $pdf = new TCPDF;

        @$pdf->SetSubject("Payout Report - ' . $payout->deposited_at->format('Y-m-d') . '.pdf");
        @$pdf->SetKeywords('');
        @$pdf->setPrintHeader(false);
        @$pdf->SetTopMargin(5);
        @$pdf->SetLeftMargin(10);
        @$pdf->SetRightMargin(10);
        @$pdf->SetFont('helvetica', '', 11);
        @$pdf->SetAutoPageBreak(true, 1.5);

        // add a page
        @$pdf->AddPage('P', 'LETTER');

        @$pdf->writeHTML($view->render(), true, false, false, false, '');

        //Close and output PDF document
        @$pdf->Output("Payout Report.pdf", 'I');
        exit;
    }
}
