<?php

namespace App\Finance\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Finance\ContributionReport;
use App\Users\User;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ContributionReportController extends Controller
{
    public function __construct()
    {
        Paginator::useTailwind();
    }

    public function index()
    {
        return view('admin.finance.contribution-reports.index');
    }

    public function preview()
    {
        $year = request()->get('year');

        $query = User::whereHas('transactions', function ($query) use ($year) {
            $query->whereYear('posted_at', $year)
                ->where('is_contribution', true);
        })
            ->withSum([
                'transactions' => function ($query) use ($year) {
                    $query->whereYear('posted_at', $year)
                        ->where('is_contribution', true);
                },
            ], 'amount')
            ->withExists([
                'contributionReports' => function ($query) use ($year) {
                    $query->where('year', $year);
                },
            ])
            ->visibleTo(auth()->user())
            ->withCount([
                'transactions' => function ($query) use ($year) {
                    $query->whereYear('posted_at', $year)
                        ->where('is_contribution', true);
                },
            ])
            ->distinct()
            ->orderBy('last_name', 'ASC');

        // Get the total before pagination
        $total = $query->get()->sum('transactions_sum_amount');

        // Get paginated results
        $users_contributed = $query->paginate(15);

        return view('admin.finance.contribution-reports.preview')
            ->with('year', $year)
            ->with('users_contributed', $users_contributed)
            ->with('total_contributions', $total);
    }

    public function previewUser($user_id)
    {
        $user = User::visibleTo(auth()->user())
            ->find($user_id);

        $transactions = $user->transactions()->whereYear('posted_at', request()->get('year'))->where('is_contribution', true)->get();

        return view('admin.finance.contribution-reports.preview-user')
            ->with('user', $user)
            ->with('transactions', $transactions);
    }

    public function create()
    {
        $year = request()->get('year');

        $query = User::whereHas('transactions', function ($query) use ($year) {
            $query->whereYear('posted_at', $year)
                ->where('is_contribution', true);
        })
            ->withSum([
                'transactions' => function ($query) use ($year) {
                    $query->whereYear('posted_at', $year)
                        ->where('is_contribution', true);
                },
            ], 'amount')
            ->withExists([
                'contributionReports' => function ($query) use ($year) {
                    $query->where('year', $year);
                },
            ])
            ->visibleTo(auth()->user())
            ->withCount([
                'transactions' => function ($query) use ($year) {
                    $query->whereYear('posted_at', $year)
                        ->where('is_contribution', true);
                },
            ])
            ->distinct()
            ->orderBy('last_name', 'ASC');

        // Get the total before pagination
        $total = $query->get()->sum('transactions_sum_amount');

        // Get paginated results
        $users_contributed = $query->get();

        return view('admin.finance.contribution-reports.create')
            ->with('year', $year)
            ->with('users_contributed', $users_contributed)
            ->with('total_contributions', $total);
    }

    public function createSubmit()
    {
        $year              = request()->get('year');
        $statement_message = request()->get('statement_message');
        $notes             = request()->get('notes');

        $users_query = User::whereHas('transactions', function ($query) use ($year) {
            $query->whereYear('posted_at', $year)
                ->where('is_contribution', true);
        })
            ->withSum([
                'transactions' => function ($query) use ($year) {
                    $query->whereYear('posted_at', $year)
                        ->where('is_contribution', true);
                },
            ], 'amount')
            ->withExists([
                'contributionReports' => function ($query) use ($year) {
                    $query->where('year', $year);
                },
            ])
            ->visibleTo(auth()->user())
            ->withCount([
                'transactions' => function ($query) use ($year) {
                    $query->whereYear('posted_at', $year)
                        ->where('is_contribution', true);
                },
            ])
            ->distinct()
            ->orderBy('last_name', 'ASC');

        try {
            DB::beginTransaction();

            foreach ($users_query->get() as $user) {
                $contribution_report = ContributionReport::create([
                    'uuid'               => Str::uuid(),
                    'account_id'         => auth()->user()->account_id,
                    'created_by_user_id' => auth()->user()->id,
                    'posted_by_user_id'  => auth()->user()->id,
                    'posted_at'          => now(),
                    'user_id'            => $user->id,
                    'year'               => $year,
                    'amount_total'       => $user->transactions_sum_amount,
                    'statement_message'  => $statement_message,
                    'notes'              => $notes,
                ]);

                $transactions = $user->transactions()->whereYear('posted_at', $year)->where('is_contribution', true)->get();

                foreach ($transactions as $transaction) {
                    $contribution_report->transactions()->create([
                        'account_id'                  => auth()->user()->account_id,
                        'user_contribution_report_id' => $contribution_report->id,
                        'user_id'                     => $user->id,
                        'finance_transaction_id'      => $transaction->id,
                        'amount'                      => $transaction->amount,
                        'posted_at'                   => now(),
                    ]);
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            return back()->with('message.failure', 'An error occurred while creating the contribution reports. ' . $e->getMessage());
        }

        return redirect()->route('admin.finance.contributions.reports.yearly.history.view', ['year' => $year])
            ->with('success', 'User Contribution Reports for ' . $year . ' have been created successfully.');
    }

    public function history()
    {
        $years = ContributionReport::where('account_id', auth()->user()->account_id)
            ->distinct('year')
            ->pluck('year');

        return view('admin.finance.contribution-reports.history')
            ->with('years', $years);
    }

    public function historyView($year)
    {
        $contribution_reports = ContributionReport::where('account_id', auth()->user()->account_id)
            ->withCount('transactions')
            ->where('year', $year)
            ->get();

        $total_contributions = $contribution_reports->sum('amount_total');
        $total_transactions  = $contribution_reports->sum('transactions_count');
        $users_contributed   = $contribution_reports->count();

        return view('admin.finance.contribution-reports.history-view')
            ->with('year', $year)
            ->with('contribution_reports', $contribution_reports)
            ->with('total_transactions', $total_transactions)
            ->with('total_contributions', $total_contributions)
            ->with('users_contributed', $users_contributed);
    }

    public function downloadUserContributionReport($year, $contribution_report_uuid)
    {
        $contribution_report = ContributionReport::visibleTo(auth()->user())
            ->with('transactions')
            ->where('year', $year)
            ->where('uuid', $contribution_report_uuid)
            ->first();

        $contribution_report->downloadPdf();
        exit;
    }
}
