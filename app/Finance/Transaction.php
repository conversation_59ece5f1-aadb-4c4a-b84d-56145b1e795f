<?php

namespace App\Finance;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Base\Models\Traits\AuditTrait;
use App\Users\Payment;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Money\Currencies\ISOCurrencies;
use Money\Parser\DecimalMoneyParser;

class Transaction extends Model
{
    use SoftDeletes, AuditTrait;

    protected $table = 'finance_transactions';

    private static $hash_salt = 'LP-account-number-encode:';

    protected $casts = [
        'account_id'                => 'integer',
        'user_id'                   => 'integer',
        'finance_vendor_id'         => 'integer',
        'user_payment_id'           => 'integer',
        'account_finance_bucket_id' => 'integer',
        'created_at'                => 'datetime',
        'updated_at'                => 'datetime',
        'deleted_at'                => 'datetime',
        'posted_at'                 => 'datetime',
        'captured_at'               => 'datetime',
        'cleared_at'                => 'datetime',
        'disputed_at'               => 'datetime',
        'deposited_at'              => 'datetime',
        'amount_original'           => 'integer',
        'amount'                    => 'integer',
        'amount_fee'                => 'integer',
        'amount_deposited'          => 'integer',
        'amount_refunded'           => 'integer',
        'is_income'                 => 'boolean',
        'is_expense'                => 'boolean',
        'is_contribution'           => 'boolean',
        'is_payment'                => 'boolean',
        'is_reimbursement'          => 'boolean',
        'is_hidden'                 => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'created_by',
        'user_id',
        'finance_vendor_id',
        'user_payment_id',
        'account_finance_bucket_id',
        'title',
        'notes',
        'tags',
        'check_number',
        'check_account_number',
        'check_account_number_last4',
        'check_routing_number',
        'posted_at',
        'captured_at',
        'cleared_at',
        'disputed_at',
        'deposited_at',
        'source',
        'amount_original',
        'amount',
        'amount_fee',
        'amount_deposited',
        'amount_refunded',
        'currency',
        'is_income',
        'is_expense',
        'is_contribution',
        'is_payment',
        'is_reimbursement',
        'is_hidden',
    ];

    public static $sources = [
        'check'        => 'Check',
        'card'         => 'Card',
        'bank_account' => 'Bank Account',
        'cash'         => 'Cash',
        'user_payment' => 'User Payment',
        'other'        => 'Other',
    ];

    public static $public_sources = [
        'check'        => 'Check',
        'card'         => 'Card',
        'bank_account' => 'Bank Account',
        'cash'         => 'Cash',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class, 'finance_vendor_id');
    }

    public function bucket()
    {
        return $this->belongsTo(FinanceBucket::class, 'account_finance_bucket_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class, 'user_payment_id');
    }

    public function history()
    {
        return $this->hasMany(TransactionHistory::class, 'finance_transaction_id');
    }

    public static function percentDifference($amount, $compare_amount)
    {
        if ($compare_amount == 0 && $amount > 0) {
            return null;
        } elseif ($compare_amount == 0) {
            return 0;
        } else {
            return number_format(($amount - $compare_amount) / $compare_amount * 100, 0);
        }
    }

    // We use this to save the check_account_number field.
    // We add some unique data to the account number and save the SHA1 hash to look up later when checks are scanned in the future.
    public static function hashEncode($string = null): string|null
    {
        if (!$string) {
            return null;
        }

        return hash('sha1', self::$hash_salt . $string);
    }

    // Convert cents to dollars
    public function formatAmount($amount = null)
    {
        if (!$amount) {
            $amount = $this->amount;
        }

        return \Brick\Money\Money::ofMinor($amount, $this->currency ?: 'USD')->getAmount();
    }

    public static function dollarsToCents($amount_in_dollars)
    {
        return (new DecimalMoneyParser((new ISOCurrencies())))->parse($amount_in_dollars, 'USD')->getAmount();
    }

    public static function centsToDollars($amount_in_cents)
    {
        return \Brick\Money\Money::ofMinor($amount_in_cents, 'USD')->getAmount();
    }

    public function scopeThisMonth($query, $timezone = 'America/Chicago')
    {
        return $query->where(function ($query) use ($timezone) {
            $query->where('created_at', '>=', now()->setTimezone($timezone)->startOfMonth())
                ->where('created_at', '<=', now()->setTimezone($timezone)->endOfMonth());
        });
    }

    public function scopeForMonth($query, $month_int)
    {
        return $query->where(function ($query) use ($month_int) {
            $query->whereMonth('created_at', $month_int);
        });
    }

    public function scopeForYear($query, $year_int)
    {
        return $query->where(function ($query) use ($year_int) {
            $query->whereYear('created_at', $year_int);
        });
    }

    public function scopeLastMonth($query, $timezone = 'America/Chicago', Carbon $end_date = null)
    {
        return $query->where(function ($query) use ($timezone, $end_date) {
            $query->where('created_at', '>=', now()->setTimezone($timezone)->subMonth(1)->startOfMonth())
                ->when($end_date, function ($query) use ($timezone, $end_date) {
                    $query->where('created_at', '<=', $end_date->setTimezone($timezone));
                }, function ($query) use ($timezone) {
                    $query->where('created_at', '<=', now()->setTimezone($timezone)->subMonth(1)->endOfMonth());
                });
        });
    }

    public function scopeThisWeek($query, $timezone = 'America/Chicago')
    {
        return $query->where(function ($query) use ($timezone) {
            $query->where('created_at', '>=', now()->setTimezone($timezone)->startOfWeek())
                ->where('created_at', '<=', now()->setTimezone($timezone)->endOfWeek());
        });
    }

    public function scopeLastXDays($query, $days = 3, $timezone = 'America/Chicago')
    {
        return $query->where(function ($query) use ($timezone, $days) {
            $query->where('created_at', '>=', now()->setTimezone($timezone)->setTimezone('UTC')->subDays($days))
                ->where('created_at', '<=', now()->setTimezone($timezone)->setTimezone('UTC')->endOfDay());
        });
    }

    public function scopeLastWeek($query)
    {
        return $query->where(function ($query) {
            $query->where('created_at', '>=', now()->subWeek(1)->startOfWeek())
                ->where('created_at', '<=', now()->subWeek(1)->endOfWeek());
        });
    }

    public function scopeIsNotIncome($query)
    {
        return $query->where('is_income', false);
    }

    public function scopeIsIncome($query)
    {
        return $query->where('is_income', true);
    }

    public function scopeIsNotExpense($query)
    {
        return $query->where('is_expense', false);
    }

    public function scopeIsExpense($query)
    {
        return $query->where('is_expense', 1);
    }

    public function scopeIsNotContribution($query)
    {
        return $query->where('is_contribution', false);
    }

    public function scopeIsContribution($query)
    {
        return $query->where('is_contribution', true);
    }

    public function scopeIsNotPayment($query)
    {
        return $query->where('is_payment', false);
    }

    public function scopeIsPayment($query)
    {
        return $query->where('is_payment', true);
    }

    public function scopeIsNotReimbursement($query)
    {
        return $query->where('is_reimbursement', false);
    }

    public function scopeIsReimbursement($query)
    {
        return $query->where('is_reimbursement', true);
    }

    public function scopeIsNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeIsHidden($query)
    {
        return $query->where('is_hidden', true);
    }
}
