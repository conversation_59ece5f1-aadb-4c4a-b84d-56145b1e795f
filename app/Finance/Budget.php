<?php

namespace App\Finance;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Budget extends Model
{
    use SoftDeletes;

    protected $table = 'finance_bucket_budgets';

    protected $casts = [
        'created_at'                => 'datetime',
        'updated_at'                => 'datetime',
        'deleted_at'                => 'datetime',
        'start_at'                  => 'datetime',
        'end_at'                    => 'datetime',
        'year'                      => 'integer',
        'account_id'                => 'integer',
        'account_finance_bucket_id' => 'integer',
        'yearly_budget'             => 'integer',
        'monthly_budget'            => 'integer',
        'weekly_budget'             => 'integer',
        'requested_yearly_budget'   => 'integer',
        'requested_monthly_budget'  => 'integer',
    ];

    protected $fillable = [
        'account_id',
        'account_finance_bucket_id',
        'start_at',
        'end_at',
        'year',
        'name',
        'description',
        'yearly_budget',
        'monthly_budget',
        'weekly_budget',
        'requested_yearly_budget',
        'requested_monthly_budget',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function bucket()
    {
        return $this->belongsTo(FinanceBucket::class, 'account_finance_bucket_id');
    }

    public function transactions()
    {
        return $this->hasManyThrough(Transaction::class, FinanceBucket::class, 'id', 'account_finance_bucket_id', 'account_finance_bucket_id')
            ->whereYear('posted_at', $this->year);
    }

    public function scopeForYear($query, $year)
    {
        return $query->where('year', $year);
    }

    public function getAmountUsed($month = null)
    {
        $amount = $this->transactions()
            ->when($month, function ($query) use ($month) {
                $query->whereMonth('posted_at', $month);
            })
            ->sum('amount');

        return \Brick\Money\Money::ofMinor($amount, $this->currency ?: 'USD')->getAmount()->toFloat();
    }

    public function getPercentUsed()
    {
        $amount = $this->getAmountUsed();
        $budget = $this->yearly_budget;

        if (!$budget) {
            return 0;
        }

        return number_format(($amount / $budget) * 100 * 100); // Extra *100 because this is cents.
    }

    // Convert cents to dollars
    public function formatAmount($amount = null)
    {
        if (!$amount) {
            $amount = $this->yearly_budget;
        }

        if (!$amount) {
            return 0;
        }

        return \Brick\Money\Money::ofMinor($amount, $this->currency ?: 'USD')->getAmount()->toFloat();
    }
}
