<?php

namespace App\Finance;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReimbursementRequestFile extends Model
{
    use SoftDeletes;

    protected $table = 'finance_reimbursement_request_files';

    protected $casts = [
        'account_id'             => 'integer',
        'user_id'                => 'integer',
        'created_by_user_id'     => 'integer',
        'finance_transaction_id' => 'integer',
        'sort_id'                => 'integer',
        'created_at'             => 'datetime',
        'updated_at'             => 'datetime',
        'deleted_at'             => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'created_by_user_id',
        'finance_reimbursement_request_id',
        'title',
        'description',
        'type',
        'storage_service',
        'file_original_name',
        'file_size',
        'data_separator',
        'file_folder',
        'file_id',
        'file_name',
        'file_extension',
        'file_type',
        'file_sha1',
        'sort_id',
        'width',
        'height',
        'has_original',
        'has_1024',
        'has_512',
        'has_256',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function reimbursementRequest()
    {
        return $this->belongsTo(ReimbursementRequest::class, 'finance_reimbursement_request_id');
    }
}
