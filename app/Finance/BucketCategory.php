<?php

namespace App\Finance;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BucketCategory extends Model
{
    use SoftDeletes;

    protected $table = 'finance_bucket_categories';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'account_id' => 'integer',
    ];

    protected $fillable = [
        'account_id',
        'name',
        'description',
        'account_code',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function buckets()
    {
        return $this->hasMany(FinanceBucket::class, 'account_finance_bucket_id');
    }
}
