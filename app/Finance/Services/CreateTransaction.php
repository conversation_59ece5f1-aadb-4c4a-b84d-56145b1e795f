<?php

namespace App\Finance\Services;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Finance\Transaction;
use App\Users\Payment;
use App\Users\User;
use Illuminate\Support\Str;

class CreateTransaction
{
    protected $attributes = [];

    public function create($attributes = []): Transaction
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        return Transaction::create($this->attributes);
    }

    public function createdBy(User $user)
    {
        $this->attributes['created_by'] = $user->id;
        $this->attributes['account_id'] = $user->account->id;

        return $this;
    }

    public function forUser(User|null $user)
    {
        if ($user) {
            $this->attributes['user_id']    = $user->id;
            $this->attributes['account_id'] = $user->account->id;
        }

        return $this;
    }

    public function forAccount(Account $account)
    {
        $this->attributes['account_id'] = $account->id;

        return $this;
    }

    public function fromUserPayment(Payment $payment)
    {
        $this->attributes['user_payment_id'] = $payment->id;

        return $this;
    }

    public function setSource($source)
    {
        $this->attributes['source'] = $source;

        return $this;
    }

    public function setFinanceBucket(FinanceBucket $finance_bucket)
    {
        $this->attributes['account_finance_bucket_id'] = $finance_bucket->id;

        return $this;
    }

    public function setCheckInformation($check_number, $check_account_number, $check_routing_number)
    {
        $this->attributes['check_number']               = $check_number;
        $this->attributes['check_account_number']       = Transaction::hashEncode($check_account_number);
        $this->attributes['check_account_number_last4'] = Str::substr($check_account_number, -4, 4);
        $this->attributes['check_routing_number']       = $check_routing_number;

        return $this;
    }

    /**
     * @param $amount_in_cents INTEGER Amount as $CENTS
     *
     * @return $this CreateTransaction::class
     */
    public function setAmount($amount_in_cents)
    {
        $this->attributes['amount'] = $amount_in_cents;

        return $this;
    }

    /**
     * @param $amount_in_cents INTEGER Amount as $CENTS
     *
     * @return $this CreateTransaction::class
     */
    public function setAmountFee($amount_in_cents)
    {
        $this->attributes['amount_fee'] = $amount_in_cents;

        return $this;
    }

    public function setPostedAt($posted_at)
    {
        $this->attributes['posted_at'] = $posted_at;

        return $this;
    }

    public function isContribution()
    {
        $this->attributes['is_contribution'] = true;

        // Contributions are income too!
        return $this->isIncome();
    }

    public function isExpense()
    {
        $this->attributes['is_expense'] = true;

        return $this;
    }

    public function isPayment()
    {
        $this->attributes['is_payment'] = true;

        return $this;
    }

    public function isIncome()
    {
        $this->attributes['is_income'] = true;

        return $this;
    }

    public function isReimbursement()
    {
        $this->attributes['is_reimbursement'] = true;

        // Reimbursements are an expense too!
        return $this->isExpense();
    }

    public function setTitle($title)
    {
        $this->attributes['title'] = $title;

        return $this;
    }

    public function setNotes($notes)
    {
        $this->attributes['notes'] = $notes;

        return $this;
    }
}
