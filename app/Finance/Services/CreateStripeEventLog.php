<?php

namespace App\Finance\Services;

use App\Finance\StripeWebhookEvent;

class CreateStripeEventLog
{
    protected $event            = [];
    protected $stripe_charge_id = null;
    protected $stripe_event_id  = null;
    protected $event_type       = null;

    public function create(): StripeWebhookEvent
    {
        return StripeWebhookEvent::create([
            'event'            => $this->event,
            'event_type'       => $this->event_type,
            'stripe_event_id'  => $this->stripe_event_id,
            'stripe_charge_id' => $this->stripe_charge_id,
        ]);
    }

    public function setEvent($event)
    {
        $this->event            = $event;
        $this->event_type       = $event->type;
        $this->stripe_event_id  = $event->id;
        $this->stripe_charge_id = $event->data?->object?->id;

        return $this;
    }
}
