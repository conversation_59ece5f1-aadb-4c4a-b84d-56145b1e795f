<?php

namespace App\Finance\Services;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Users\User;
use Illuminate\Support\Str;

class CreateBucket
{
    protected $attributes = [];

    public function create($attributes = []): FinanceBucket
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        return FinanceBucket::create($this->attributes);
    }

    public function createdBy(User $user)
    {
        $this->attributes['created_by'] = $user->id;
        $this->attributes['account_id'] = $user->account->id;

        return $this;
    }

    public function forUser(User|null $user)
    {
        if ($user) {
            $this->attributes['user_id']    = $user->id;
            $this->attributes['account_id'] = $user->account->id;
        }

        return $this;
    }

    public function forAccount(Account $account)
    {
        $this->attributes['account_id'] = $account->id;

        return $this;
    }

    public function setName($name)
    {
        $this->attributes['name']     = $name;
        $this->attributes['url_name'] = Str::slug($name);

        return $this;
    }

    public function setAccountCode($account_code)
    {
        $this->attributes['account_code'] = $account_code;

        return $this;
    }

    public function setDescription($description)
    {
        $this->attributes['description'] = $description;

        return $this;
    }

    public function setIsContribution($is_contribution = true)
    {
        $this->attributes['is_contribution_bucket'] = $is_contribution;
        $this->attributes['is_contribution']        = $is_contribution;

        return $this;
    }

    public function setIsReimbursement($is_reimbursement = true)
    {
        $this->attributes['is_reimbursement_bucket'] = $is_reimbursement;
        $this->attributes['is_reimbursement']        = $is_reimbursement;

        return $this;
    }

    public function setIsIncome($is_income = true)
    {
        $this->attributes['is_income'] = $is_income;

        return $this;
    }

    public function setIsExpense($is_expense = true)
    {
        $this->attributes['is_expense'] = $is_expense;

        return $this;
    }
}
