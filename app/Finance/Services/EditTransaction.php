<?php

namespace App\Finance\Services;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Finance\Budget;
use App\Finance\Transaction;
use App\Users\Payment;
use App\Users\User;
use Illuminate\Support\Str;
use Money\Currencies\ISOCurrencies;
use Money\Parser\DecimalMoneyParser;

class EditTransaction
{
    protected $attributes  = [];
    protected $transaction = null;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    public function save($attributes = []): Transaction
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        $this->transaction->fill($this->attributes);

        $this->transaction->save();

        return $this->transaction;
    }

    public function createdBy(User $user)
    {
        $this->transaction->created_by = $user->id;

        return $this;
    }

    public function forUser(User|null $user)
    {
        if ($user) {
            $this->transaction->user_id    = $user->id;
            $this->transaction->account_id = $user->account->id;
        }

        return $this;
    }

    public function forAccount(Account $account)
    {
        $this->transaction->account_id = $account->id;

        return $this;
    }

    public function fromUserPayment(Payment $payment)
    {
        $this->transaction->user_payment_id = $payment->id;

        return $this;
    }

    public function setCheckNumber($check_number = null)
    {
        if ($check_number && $check_number != $this->transaction->check_number) {
            $this->transaction->check_number = $check_number;
        }

        return $this;
    }

    public function setSource($source)
    {
        $this->transaction->source = $source;

        return $this;
    }

    public function setFinanceBucket(FinanceBucket $finance_bucket)
    {
        $this->transaction->account_finance_bucket_id = $finance_bucket->id;

        return $this;
    }

    public function setBudget(Budget $budget)
    {
        $this->transaction->finance_bucket_budget_id = $budget->id;

        return $this;
    }

    public function setCheckInformation($check_number, $check_account_number, $check_routing_number)
    {
        $this->transaction->check_number               = $check_number;
        $this->transaction->check_account_number       = Transaction::hashEncode($check_account_number);
        $this->transaction->check_account_number_last4 = Str::substr($check_account_number, -4, 4);
        $this->transaction->check_routing_number       = $check_routing_number;

        return $this->setSource('check');
    }

    /**
     * @param $amount_in_dollars FLOAT Amount as $DOLLARS.CENTS -- will be converted to cents.
     *
     * @return $this CreateTransaction::class
     */
    public function setAmount($amount_in_dollars)
    {
        $this->transaction->amount = (new DecimalMoneyParser((new ISOCurrencies())))->parse($amount_in_dollars, 'USD')->getAmount();

        return $this;
    }

    public function setPostedAt($posted_at)
    {
        $this->transaction->posted_at = $posted_at;

        return $this;
    }

    public function isContribution()
    {
        $this->transaction->is_contribution = true;

        // Contributions are income too!
        return $this->isIncome();
    }

    public function isExpense()
    {
        $this->transaction->is_expense = true;

        return $this;
    }

    public function isPayment()
    {
        $this->transaction->is_payment = true;

        return $this;
    }

    public function isIncome()
    {
        $this->transaction->is_income = true;

        return $this;
    }

    public function isReimbursement()
    {
        $this->transaction->is_reimbursement = true;

        // Reimbursements are an expense too!
        return $this->isExpense();
    }

    public function setTitle($title)
    {
        $this->transaction->title = $title;

        return $this;
    }

    public function setNotes($notes)
    {
        $this->transaction->notes = $notes;

        return $this;
    }
}
