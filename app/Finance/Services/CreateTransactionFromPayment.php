<?php

namespace App\Finance\Services;

use App\Finance\Transaction;
use App\Users\Payment;
use Illuminate\Support\Facades\Log;

class CreateTransactionFromPayment
{
    protected $attributes   = [];
    protected $user_payment = null;

    public function create($attributes = []): Transaction|false
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        if (Transaction::where('user_payment_id', $this->user_payment->id)->exists()) {
            Log::warning('CreateTransactionFromPayment - Duplicate transaction.', [
                'message'      => 'Trying to create duplicate finance_transaction from a user_payment.',
                'user_payment' => $this->user_payment->id,
            ]);

            return false;
        }

        $transaction = (new CreateTransaction())
            ->setTitle('Contribution')
            ->isContribution()
            ->setSource('user_payment')
            ->fromUserPayment($this->user_payment)
            ->setPostedAt($this->user_payment->created_at)
            ->createdBy($this->user_payment->user)
            ->forAccount($this->user_payment->account)
            ->forUser($this->user_payment->user)
            ->setAmount($this->user_payment->amount)
            ->setAmountFee($this->user_payment->amount_fee)
            ->setFinanceBucket($this->user_payment->bucket)
            ->create();

        return $transaction;
    }

    public function fromUserPayment(Payment $payment)
    {
        $this->user_payment = $payment;

        return $this;
    }
}
