<?php

namespace App\Finance\Policies;

use App\Finance\Transaction;
use App\Users\User;

class FinancePolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.finance') && !$user->account->getSetting('feature.online_giving')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('finance.manage');
    }

    public function search(User $user)
    {
        return true;
    }

    public function addPaymentMethods(User $user)
    {
        return $user->account?->hasFeatureForMember('feature.online_giving')
               && !empty($user->account?->stripe_account_id);
    }

    public function create(User $user)
    {
        return $user->hasPermission('finance.manage');
    }

    public function manageFinancialManagement(User $user)
    {
        return (
                   $user->hasPermission('finance.manage') || $user->hasPermission('finance.index')
               )
               && (
                   $user->account?->hasFeature('feature.finance') || $user->account?->hasFeature('feature.online_giving')
               );
    }

    public function indexBuckets(User $user)
    {
        return ($user->hasPermission('finance.manage') || $user->hasPermission('finance.index'))
               && (
                   $user->account?->hasFeature('feature.online_giving')
                   ||
                   $user->account?->hasFeature('feature.finance')
               );
    }

    public function manageBuckets(User $user)
    {
        return $user->hasPermission('finance.manage')
               && (
                   $user->account?->hasFeature('feature.online_giving')
                   ||
                   $user->account?->hasFeature('feature.finance')
               );
    }

    public function indexBudgets(User $user)
    {
        return ($user->hasPermission('finance.manage') || $user->hasPermission('finance.index'))
               && $user->account?->hasFeature('feature.finance');
    }

    public function manageBudgets(User $user)
    {
        return $user->hasPermission('finance.manage')
               && $user->account?->hasFeature('feature.finance');
    }

    public function createContributions(User $user)
    {
        return $user->hasPermission('finance.manage');
    }

    public function createExpenses(User $user)
    {
        return $user->hasPermission('finance.manage');
    }

    public function createTransactions(User $user)
    {
        return $user->hasPermission('finance.manage');
    }

    public function editTransactions(User $user, Transaction $transaction)
    {
        return $user->hasPermission('finance.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('finance.manage');
    }

    public function edit(User $user, Transaction $transaction)
    {
        return $user->hasPermission('finance.manage');
    }

    public function save(User $user, Transaction $transaction)
    {
        return $user->hasPermission('finance.manage');
    }

    public function delete(User $user)
    {
        return $user->hasPermission('finance.manage');
    }
}