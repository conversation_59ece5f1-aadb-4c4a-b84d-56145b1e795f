<?php

namespace App\Finance;

use App\Accounts\Account;
use App\Accounts\FinanceBucket;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReimbursementRequestItem extends Model
{
    use SoftDeletes;

    protected $table = 'finance_reimbursement_request_items';

    protected $casts = [
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
        'deleted_at'  => 'datetime',
        'spent_at'    => 'datetime',
        'is_approved' => 'boolean',
        'is_declined' => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'finance_reimbursement_request_id',
        'account_finance_bucket_id',
        'finance_transaction_id',
        'spent_at',
        'title',
        'notes',
        'amount',
        'currency',
        'is_approved',
        'is_declined',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function reimbursementRequest()
    {
        return $this->belongsTo(ReimbursementRequest::class, 'finance_reimbursement_request_id');
    }

    public function bucket()
    {
        return $this->belongsTo(FinanceBucket::class, 'account_finance_bucket_id');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'finance_transaction_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopeIsNotApproved($query)
    {
        return $query->where('is_approved', false);
    }

    public function scopeIsApproved($query)
    {
        return $query->where('is_approved', true);
    }

    public function scopeIsNotDeclined($query)
    {
        return $query->where('is_declined', false);
    }

    public function scopeIsDeclined($query)
    {
        return $query->where('is_declined', true);
    }
}
