<?php

namespace App\Finance;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReimbursementRequest extends Model
{
    use SoftDeletes;

    protected $table = 'finance_reimbursement_requests';

    protected $casts = [
        'account_id'            => 'integer',
        'user_id'               => 'integer',
        'created_at'            => 'datetime',
        'updated_at'            => 'datetime',
        'deleted_at'            => 'datetime',
        'is_approved'           => 'boolean',
        'is_partially_approved' => 'boolean',
        'is_declined'           => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'reimburse_by',
        'name',
        'description',
        'admin_notes',
        'check_number',
        'is_approved',
        'is_partially_approved',
        'is_declined',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account $account)
    {
        return $query->where(function ($query) use ($account) {
            $query->where('account_id', $account->id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function items()
    {
        return $this->hasMany(ReimbursementRequestItem::class, 'finance_reimbursement_request_id');
    }
}
