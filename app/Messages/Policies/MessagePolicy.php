<?php

namespace App\Messages\Policies;

use App\Messages\Message;
use App\Users\User;

class MessagePolicy
{
    public function before($user, $ability)
    {
        if ($user->isSuper()) {
            return true;
        }

        if (!$user->account->getSetting('feature.messages')) {
            return false;
        }
    }

    public function create(User $user)
    {
        return $user->hasPermission('messages.manage');
    }

    public function index(User $user)
    {
        return $user->hasPermission('messages.index');
    }

    public function view(User $user, Message $message)
    {
        return $user->account_id == $message->account_id
            || $user->hasPermission('messages.index');
    }

    public function update(User $user, Message $message)
    {
        return $user->account_id == $message->account_id
            || $user->hasPermission('messages.manage');
    }
}
