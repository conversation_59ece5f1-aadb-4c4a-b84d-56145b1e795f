<?php

namespace App\Messages\Policies;

use App\Messages\MessageAttachment;
use App\Users\User;

class MessageAttachmentPolicy
{
    public function before($user, $ability)
    {
//        if ($user->isSuper()) {
//            return true;
//        }
    }

    public function create(User $user)
    {
        return $user->isSuper();
    }

    public function index(User $user)
    {
        return $user->isSuper();
    }

    public function view(User $user, MessageAttachment $message_attachment)
    {
        return $user->isSuper()
            || ($user->account_id == $message_attachment->account_id);
    }

    public function update(User $user, MessageAttachment $message_attachment)
    {
        return $user->isSuper()
            || ($user->account_id == $message_attachment->account_id);
    }
}
