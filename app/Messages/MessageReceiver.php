<?php

namespace App\Messages;

use App\Accounts\Account;
use App\Users\Group;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MessageReceiver extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'account_id',
        'user_group_id',
        'message_type_id',
        'message_provider_id',
        'created_at',
        'updated_at',
        'last_received_at',
        'name',
        'receiver',
        'reply_to',
        'status',
        'is_active',
    ];

    protected $casts = [
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
        'deleted_at'       => 'datetime',
        'last_received_at' => 'datetime',
    ];

    public static $statuses = [
        'active'         => 'Active',
        'pending_review' => 'In Review',
        'inactive'       => 'Inactive',
    ];

    public static $status_colors = [
        'active'         => 'label-success',
        'pending_review' => 'label-warning',
        'inactive'       => 'label-default',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function messageType()
    {
        return $this->belongsTo(MessageType::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function scopeIsActive($query)
    {
        return $query->where('is_active', 1)
            ->where('status', 'active');
    }

    public function scopeOfType($query, $type)
    {
        return $query->where('message_type_id', array_search($type, MessageType::$types));
    }

    public function getPresentStatusAttribute()
    {
        return \Illuminate\Support\Arr::get(self::$statuses, $this->status);
    }

    public function getPresentStatusColorAttribute()
    {
        return \Illuminate\Support\Arr::get(self::$status_colors, $this->status);
    }
}
