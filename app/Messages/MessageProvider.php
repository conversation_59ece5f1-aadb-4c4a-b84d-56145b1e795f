<?php

namespace App\Messages;

use App\Accounts\Account;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class MessageProvider extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'account_id',
        'message_type_id',
        'created_at',
        'updated_at',
        'name',
        'short_name',
        'provider_code',
        'api_key',
        'api_secret',
        'description',
        'is_active',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function messageType()
    {
        return $this->belongsTo(MessageSender::class);
    }

    public function scopeIsActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeIsGlobalDefault($query)
    {
        return $query->where('is_global_default', true);
    }

    public function getDefaultForUserAndType($user, $message_type)
    {
        // First look for a provider specific to this account.
        $provider = MessageProvider::where('account_id', optional($user)->account_id)
            ->where('message_type_id', optional($message_type)->id)
            ->isActive()
            ->first();

        if ($provider) {
            return $provider;
        }

        // If no account specific provider for this type, look for a global one.
        $provider = MessageProvider::where('message_type_id', optional($message_type)->id)
            ->isGlobalDefault()
            ->isActive()
            ->first();

        return $provider;
    }
}
