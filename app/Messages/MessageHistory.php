<?php

namespace App\Messages;

use App\Accounts\Account;
use App\Messages\Scopes\MessageHistoryVisibleToScope;
use App\Users\Email;
use App\Users\Phone;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;

class MessageHistory extends Model
{
    protected $table = 'message_history';

    const UPDATED_AT = null;

    protected $casts = [
        'created_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'user_email_id',
        'user_phone_id',
        'message_id',
        'message_status_id',
        'provider_message_id',
        'provider_batch_id',
        'failed_reason',
        'charge',
        'cost',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new MessageHistoryVisibleToScope())->getQuery($query, $user);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function userEmail()
    {
        return $this->belongsTo(Email::class);
    }

    public function userPhone()
    {
        return $this->belongsTo(Phone::class);
    }

    public function message()
    {
        return $this->belongsTo(Message::class);
    }

    public function messageStatus()
    {
        return $this->belongsTo(MessageStatus::class);
    }

    public function sentTo()
    {
        if ($this->user_email_id && $this->userEmail) {
            return $this->userEmail?->email;
        } elseif ($this->user_phone_id) {
            return $this->userPhone?->number;
        }

        return null;
    }

    public function getBadgeColor()
    {
        if ($this->indicates_error) {
            return 'badge-danger';
        } else {
            return 'badge-success';
        }
    }

    public function getPresentStatusAttribute()
    {
        return $this->messageStatus->present_status;
    }

    public function getPresentStatusColorAttribute()
    {
        return $this->messageStatus->present_status_color;
    }

    // This will look for a status existing with this name for this account, or create one if not found.
    // The $override_message_type_id is useful for "one time messages" that do not belong to a `message`, but we need a type_id.
    public function setStatusCreateOnMissing($status_name, $indicates_error = false, $override_message_type_id = null)
    {
        $status = MessageStatus::where('account_id', $this->account_id)
            ->where('message_type_id', $override_message_type_id ?: $this->message?->message_type_id)
            ->where('status', $status_name)
            ->first();

        if (!$status) {
            $status = MessageStatus::create([
                'account_id'      => $this->account_id,
                'status'          => $status_name,
                'indicates_error' => $indicates_error,
                'message_type_id' => $override_message_type_id ?: $this->message?->message_type_id,
            ]);
        }

        $this->message_status_id = $status->id;
        $this->save();

        return true;
    }
}
