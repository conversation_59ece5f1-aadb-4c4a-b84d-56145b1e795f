<?php

namespace App\Messages\Services;

use App\Messages\MessageHandler;
use App\Messages\MessageProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Plivo\Exceptions\PlivoRestException;
use Plivo\RestClient;

class CreateMessageHandler
{
    private $message_handler;
    private $message_provider;
    private $group;
    private $message_type;
    private $user;
    private $address  = null;
    private $reply_to = null;
    private $cost     = 0;

    public function create(): MessageHandler
    {
        if (!$this->group || !$this->message_type) {
            throw new \Exception('Missing group or message type to create a MessageHandler.');
        }

        # Get our provider for this message type and account
        $this->message_provider = (new MessageProvider())->getDefaultForUserAndType($this->user, $this->message_type);

        if (!$this->message_provider) {
            throw new \Exception('Unable to find a MessageProvider for the given User and MessageType.');
        }

        // Generate Handler details based on the message type.
        if ($this->message_type->code == 'email') {
            Log::info('Generating an email address for a new MessageHandler to enable Email messaging.');

            $this->createMessageEmailHandlerAddress();
        } elseif ($this->message_type->code == 'sms') {
            Log::info('Getting an address and assigning it to a new handler to enable SMS messaging.');

            $this->assignSmsAddress();
        } elseif ($this->message_type->code == 'voice') {
            Log::info('Assigning a number to a new MessageHandler to enable Voice messaging.');

            $this->createMessageVoiceHandlerAddress();
        } else {
            Log::error('CreateMessageHandler::create() -- Attempting to create handler with no valid message type code.', [
                'code' => $this->message_type->code,
            ]);
        }

        try {
            Log::info('CreateMessageHandler::create() -- Attempting to create the MessageHandler in the database.');

            $this->message_handler = MessageHandler::create([
                'account_id'          => $this->group->account_id,
                'user_group_id'       => $this->group->id,
                'message_type_id'     => $this->message_type->id,
                'message_provider_id' => $this->message_provider->id,
                'address'             => $this->address,
                'reply_to'            => $this->reply_to,
                'is_active'           => true,
                'cost'                => $this->cost,
            ]);
        } catch (\Exception $e) {
            // If we fail, log it
            // We need to delete the phone number eventually.
            Log::error('CreateMessageHandler::create() -- Create phone/email, but failed to create the actual Handler.', [
                'message' => $e->getMessage(),
                'object'  => $this,
            ]);
        }

        if (!$this->message_handler) {
            throw new \Exception('Unable to create a MessageHandler for message type.', [
                'name' => $this->message_type?->name,
            ]);
        }

        return $this->message_handler;
    }

    public function forUser($user)
    {
        $this->user = $user;

        return $this;
    }

    public function forGroup($group)
    {
        $this->group = $group;

        return $this;
    }

    public function withType($message_type)
    {
        $this->message_type = $message_type;

        return $this;
    }

    public function usingAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Creates an email address like:
     *  <EMAIL> -- if an account->short_name exists
     *  <EMAIL> -- else
     * @return string
     */
    private function createEmailAddress()
    {
        $generated_address = ($this->group->account->group_email_prefix ?: $this->group->account->short_name)
            . '-' . Str::slug($this->group?->name, '-')
            . '@' . config('app.domains.email_to');

        if (!MessageHandler::where('address', 'like', $generated_address)->exists()) {
            return $generated_address;
        }

        return ($this->group->account->group_email_prefix ?: $this->group->account->short_name)
            . '-' . Str::slug($this->group?->name, '-')
            . '-' . substr(uniqid(true), -5)
            . '@' . config('app.domains.email_to');
    }

    private function createMessageEmailHandlerAddress()
    {
        if ($this->message_type->code == 'email') {
            $this->address  = $this->createEmailAddress();
            $this->reply_to = $this->group->account->email;
        }
    }

    private function createMessageVoiceHandlerAddress()
    {
        // We only use a single Twilio address to send voice messages.
        if ($this->message_type->code == 'voice') {
            $this->address  = $this->message_provider->default_address;
            $this->reply_to = null;
        }
    }

    private function assignSmsAddress()
    {
        if ($this->message_provider->provider_code == 'plivo') {
            Log::info('CreateMessageHandler:assignSmsAddress() -- Message provider code is Plivo.');

            $this->cost = $this->group->account->plan->price_per_sms;

            // Nov 2023 - We only use one Pliov number for all SMS messages.
            // $number = $this->purchaseFromPlivo();

            // We use one toll-free number for all SMS messages. Nov 2023.
            $this->address = $this->message_provider->default_address;

            // This was when we used the Plivo PowerPack
            // $this->address = $this->message_provider->default_address;

            return true;
        }

        Log::info('CreateMessageHandler:assignSmsAddress() -- No available matching provider code found.', [
            'code' => $this->message_provider->provider_code,
        ]);

        return false;
    }

    // Plivo Only for now
    private function purchasePhoneNumber()
    {
        if ($this->message_provider->provider_code == 'plivo') {
            Log::info('CreateMessageHandler:purchasePhoneNumber() -- Message provider code is Plivo -- following route.');

            return $this->purchaseFromPlivo();
        }

        Log::info('CreateMessageHandler:purchasePhoneNumber() -- No available matching provider code found.', [
            'code' => $this->message_provider->provider_code,
        ]);
    }

    private function purchaseFromPlivo()
    {
        $plivo = new RestClient($this->message_provider->api_key, $this->message_provider->api_secret);

        try {
            Log::info('CreateMessageHandler:purchasePhoneNumber() -- Attempting to get number list from Plivo.');

            // Get a list of available phone number from Plivo.
            $response = $plivo->getPhoneNumbers()->getList('US', [
                'type'     => 'tollfree',
                'services' => 'voice,sms',
            ]);

            if ($response->error) {
                throw new \Exception($response->error);
            }

            // Get the first number available.
            // $response->get()[0];

            // Get the number itself.
            $selected_number = $response->get()[0];
            /*
               {
                  "number": "***********",
                  "prefix": "415",
                  "city": "SAN FRANCISCO",
                  "country": "UNITED STATES",
                  "region": "United States",
                  "rate_center": "SNFC CNTRL",
                  "lata": 722,
                  "type": "fixed",
                  "sub_type": "local",
                  "setup_rate": "0.00000",
                  "monthly_rental_rate": "0.80000",
                  "sms_enabled": true,
                  "sms_rate": "0.00800",
                  "voice_enabled": true,
                  "voice_rate": "0.00500",
                  "restriction": null,
                  "restriction_text": null,
                  "resource_uri": "/v1/Account/MAXXXXXXXXXXXXXXXXXX/PhoneNumber/***********/",
                },
             */

            Log::info('CreateMessageHandler:purchasePhoneNumber() -- Searched for a number and found.', [
                'number' => $selected_number->number,
            ]);

            $this->cost = $selected_number->monthlyRentalRate * 100; // Store in CENTS

            // Purchase the phone number.
            $buy_response = $plivo->getPhoneNumbers()->buy($selected_number->number);

            /* BUY RESPONSE
            {
                "api_id": "aa52882c-1c88-11e4-bd8a-12313f016a39",
                "message": "created",
                "numbers": [
                    {
                        "number": "***********",
                        "status": "Success"
                    }
                ],
                "status": "fulfilled"
            }
            */

            if ($buy_response->getStatusCode() !== 201) {
                Log::error('CreateMessageHandler::purchasePhoneNumber() -- Failed to purchase selected phone number. Plivo said: ' . $response?->error);
//                Log::error($buy_response);

                throw new \Exception('Failed purchasing selected phone number.');
            }

            // Add an Alias for this number, so we can reference it easily in Plivo.
            try {
                $plivo->numbers->update(
                    $selected_number->number,
                    ['alias' => $this->group->account->name . ' :: ' . $this->group->name]
                );
            } catch (PlivoRestException $ex) {
                Log::error('CreateMessageHandler::purchasePhoneNumber::createAliasInPlivo -- Failed to update a purchased phone number with an alias. ' / $ex->getMessage());
            }

            // Set the address with the new number.
            $this->address = $selected_number->number;

        } catch (\Exception $e) {
            Log::error($e);
            throw new \Exception('Could not secure a phone number from the service provider. Possibly purchased a number! Service provider said: ' . $e->getMessage());
        }

        return true;

//        PhoneNumberBuyResponse
//        #number: "***********"
//        #numberStatus: "Success"
//        #status: "fulfilled"
//        +_message: "created"
//        +apiId: "1feeb5b4-b4ce-11e9-82ac-0242ac110003"
//        +statusCode: 201

//        "country" => "UNITED STATES"
//        "lata" => 722
//        "number" => "1628244XXXX"
//        "type" => "fixed"
//        "monthlyRentalRate" => "0.80000"
//        "prefix" => "628"
//        "rateCenter" => "BELVEDERE"
//        "region" => "California"
//        "resourceUri" => "/v1/Account/MANGEYMJI4YWM3ODJHZW/PhoneNumber/***********/"
//        "restriction" => null
//        "restrictionText" => null
//        "setupRate" => "0.00000"
//        "smsEnabled" => true
//        "smsRate" => "0.00000"
//        "voiceEnabled" => true
//        "voiceRate" => "0.00850"
    }
}
