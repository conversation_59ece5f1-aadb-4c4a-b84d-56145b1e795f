<?php

namespace App\Messages\Services;

use App\Messages\Message;

class CreateMessage
{
    protected $attributes   = [];
    protected $message;
    protected $group;
    protected $message_handler;
    protected $message_type;
    protected $message_provider;
    protected $from_user;
    protected $to_address   = null;
    protected $from_address = null;
    protected $content      = null;
    protected $html_content = null;
    protected $subject      = null;
    protected $status       = 'new';

    public function create($provider_transaction_id = null): Message
    {
        if (!$this->group || !$this->message_type) {
            throw new \Exception('Missing group or message type to create a Message.');
        }

        $this->message = Message::create([
            'account_id'              => $this->account->id,
            'created_by_user_id'      => optional($this->from_user)->id,
            'message_sender_id'       => optional($this->from_user)->id,
            'message_handler_id'      => $this->message_handler->id,
            'message_type_id'         => $this->message_type->id,
            'message_provider_id'     => $this->message_provider->id,
            'provider_transaction_id' => $provider_transaction_id,
            'user_group_id'           => $this->group->id,
//            'sent_at'                 => $this->sent_at,
//            'original_sent_at'        => $this->original_sent_at,
            'from_address'            => $this->from_address,
            'to_address'              => $this->to_address,
            'subject'                 => $this->subject,
            'content'                 => $this->content,
            'html_content'            => $this->html_content,
            'status'                  => $this->status, // Defaults to 'new'
        ]);


        return $this->message;
    }

    public function forAccount($account)
    {
        $this->account = $account;

        return $this;
    }

    public function forGroup($group)
    {
        $this->group = $group;

        return $this;
    }

    public function withHandler($message_handler)
    {
        $this->message_handler = $message_handler;

        return $this;
    }

    public function withProvider($message_provider)
    {
        $this->message_provider = $message_provider;

        return $this;
    }

    public function setSubject($subject)
    {
        $this->subject = $subject;

        return $this;
    }

    public function setContent($content)
    {
        $this->content = $content;

        return $this;
    }

    public function setHtmlContent($content)
    {
        $this->html_content = $content;

        return $this;
    }

    public function fromUser($user)
    {
        $this->from_user = $user;

        return $this;
    }

    public function withType($message_type)
    {
        $this->message_type = $message_type;

        return $this;
    }

    public function fromAddress($address)
    {
        $this->from_address = $address;

        return $this;
    }

    public function toAddress($address)
    {
        $this->to_address = $address;

        return $this;
    }

    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }
}
