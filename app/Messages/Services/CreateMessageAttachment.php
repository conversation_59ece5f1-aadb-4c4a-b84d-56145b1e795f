<?php

namespace App\Messages\Services;

use App\Messages\Message;
use App\Messages\MessageAttachment;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CreateMessageAttachment
{
    protected $message;
    protected $file;

    public function forMessage(Message $message)
    {
        $this->message = $message;

        return $this;
    }

    /**
     * @param UploadedFile $uploaded_file From request()->file() on the frontend
     * @param null         $inline_content_id
     *
     * @return MessageAttachment
     * @throws \Exception
     */
    public function create(UploadedFile $uploaded_file, $inline_content_id = null): MessageAttachment
    {
        if ($uploaded_file->isValid()) {

            // $folder    = Config::get('app.user_image_file_path');
            $extension = '.' . $uploaded_file->getClientOriginalExtension();

            $new_file_name                   = $this->message->account_id . '--' . Str::random(4) . '--' . $uploaded_file->getClientOriginalName();
            $new_file_name_without_extension = str_replace($extension, '', $new_file_name);

            // Save our file for delivery later.
            if (!$uploaded_file->storeAs($this->message->account_id, $new_file_name, 'message-files')) {
                throw new \Exception('Could not write file to cloud server.');
            }

            $this->file = new MessageAttachment();

            $this->file->fill([
                'message_id'         => $this->message->id,
                'storage_service'    => 'local',
                'data_separator'     => '--',
                'file_original_name' => $uploaded_file->getClientOriginalName(),
                'file_size'          => $uploaded_file->getSize(),
                'file_folder'        => $this->message->account_id,
                'file_id'            => null,
                'file_name'          => $new_file_name_without_extension,
                'file_extension'     => $uploaded_file->getClientOriginalExtension(),
                'file_type'          => $uploaded_file->getClientMimeType(),
                'file_sha1'          => sha1(file_get_contents($uploaded_file->getRealPath())),
                'inline_content_id'  => $inline_content_id,
            ]);

            $this->file->save();

        } else {
            throw new \Exception('File size was zero.');
        }

        return $this->file;
    }

    /**
     * Allows creating a MessageAttachment from a string of the file.
     *
     * @param      $original_file_name
     * @param      $file_content
     * @param      $content_type
     * @param null $content_id
     *
     * @return MessageAttachment
     */
    public function createFromString($original_file_name, $file_content, $content_type, $content_id = null): MessageAttachment
    {
        $extension = Arr::last(explode('.', $original_file_name));
        // $folder    = Config::get('app.user_image_file_path');

        $new_file_name                   = $this->message->account_id . '--' . Str::random(4) . '--' . $original_file_name;
        $new_file_name_without_extension = str_replace('.' . $extension, '', $new_file_name);

        // Save our file for delivery later.
        if (!Storage::disk('message-files')->put($this->message->account_id . '/' . $new_file_name, $file_content)) {
            Log::error('Could not store message attachment to the temporary folder for message ID ' . $this->message->id);
        }

        $this->file = new MessageAttachment();

        $this->file->fill([
            'message_id'         => $this->message->id,
            'storage_service'    => 'local',
            'data_separator'     => '--',
            'file_original_name' => $original_file_name,
            'file_size'          => Storage::disk('message-files')->size($this->message->account_id . '/' . $new_file_name),
            'file_folder'        => $this->message->account_id,
            'file_id'            => null,
            'file_name'          => $new_file_name_without_extension,
            'file_extension'     => $extension,
            'file_type'          => $content_type,
            'file_sha1'          => sha1($file_content),
            'inline_content_id'  => $content_id,
        ]);

        $this->file->save();

        return $this->file;
    }
}
