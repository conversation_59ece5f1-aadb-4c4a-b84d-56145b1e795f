<?php

namespace App\Messages;

use App\Accounts\Account;
use App\Users\Group;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;

class Message extends Model
{
    protected $fillable = [
        'account_id',
        'account_location_id',
        'created_by_user_id',
        'message_sender_id',
        'message_handler_id',
        'message_type_id',
        'message_provider_id',
        'provider_transaction_id',
        'user_group_id',
        'sent_at',
        'original_sent_at',
        'from_address',
        'to_address',
        'subject',
        'content',
        'html_content',
        'messages_sent',
        'status',
    ];

    protected $casts = [
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
        'sent_at'          => 'datetime',
        'original_sent_at' => 'datetime',
    ];

    public static $statuses = [
        'new'             => 'New',
        'on_hold'         => 'On Hold',
        'sending'         => 'In Progress',
        'too_delayed'     => 'Too Delayed',
        'cancelled'       => 'Cancelled',
        'error'           => 'Error',
        'failed'          => 'Failed',
        'invalid_sender'  => 'Invalid Sender',
        'invalid_handler' => 'Invalid Handler',
        'complete'        => 'Complete',
    ];

    public static $status_colors = [
        'new'            => 'label-primary',
        'on_hold'        => 'label-warning',
        'sending'        => 'label-info',
        'too_delayed'    => 'label-error',
        'cancelled'      => 'label-error',
        'failed'         => 'label-error',
        'invalid_sender' => 'label-error',
        'error'          => 'label-error',
        'complete'       => 'label-success',
    ];

    public function attachments()
    {
        return $this->hasMany(MessageAttachment::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function handler()
    {
        return $this->belongsTo(MessageHandler::class, 'message_handler_id');
    }

    public function provider()
    {
        return $this->belongsTo(MessageProvider::class, 'message_provider_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'message_sender_id');
    }

    public function type()
    {
        return $this->belongsTo(MessageType::class, 'message_type_id');
    }

    public function group()
    {
        return $this->belongsTo(Group::class, 'user_group_id');
    }

    public function history()
    {
        return $this->hasMany(MessageHistory::class);
    }

    public function canSend()
    {
        return $this->status == 'new' || $this->status == 'sending';
    }
}
