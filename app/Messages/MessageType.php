<?php

namespace App\Messages;

use App\Users\Group;
use Illuminate\Database\Eloquent\Model;

class MessageType extends Model
{
    protected $fillable = [
        'name',
        'code',
        'description',
        'is_active',
        'incurs_sending_fee',
        'account_plan_sending_fee_field',
        'incurs_monthly_fee',
        'account_plan_monthly_fee_field',
        'sending_fee_description',
        'monthly_fee_description',
    ];

    public static $types = [
        'email'        => 'Email',
        'sms'          => 'SMS',
        'mobile'       => 'Mobile App',
        'voice'        => 'Voice',
        'notification' => 'Notification',
    ];

    // The ID of the type in the database.
    public static $types_to_ids = [
        1 => 'email',
        2 => 'sms',
        3 => 'mobile',
        4 => 'voice',
        5 => 'notification',
    ];

    public function groups()
    {
        return $this->belongsToMany(Group::class, 'user_group_to_message_type', 'user_group_id', 'message_type_id');
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function messageReceiver()
    {
        return $this->hasMany(MessageReceiver::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }
}
