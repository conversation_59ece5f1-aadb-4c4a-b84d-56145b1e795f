<?php

namespace App\ChildCheckins\Policies;

use App\Users\User;

class ChildCheckinPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.child_checkin')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('groups.index');
    }

    public function create(User $user)
    {
        return $user->hasPermission('groups.manage');
    }

    public function store(User $user)
    {
        return true;
    }

    public function edit(User $user)
    {
        return $user->hasPermission('groups.manage');
    }

    public function save(User $user)
    {
        return $user->hasPermission('groups.manage');
    }

    public function view(User $user)
    {
        return $user->hasPermission('groups.index');
    }

    public function delete(User $user)
    {
        return $user->hasPermission('groups.manage');
    }

    public function post(User $user)
    {
        return $user->hasPermission('app.groups.post');
    }

    public function kiosk(User $user)
    {
        return $user->hasPermission('child-checkin.enable_kiosk');
    }
}