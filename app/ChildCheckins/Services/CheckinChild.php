<?php

namespace App\ChildCheckins\Services;

use App\ChildCheckins\ChildCheckin;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class CheckinChild
{
    protected $attributes         = [];
    protected $checkin_by_user_id = null;
    protected $created_by_user_id = null;
    protected $notes              = null;
    protected $child;

    public function checkin(): ChildCheckin
    {
        $checkin = new ChildCheckin();

        $checkin->account_id         = $this->child->account_id;
        $checkin->child_user_id      = $this->child->id;
        $checkin->family_id          = $this->child->family_id;
        $checkin->checkin_by_user_id = $this->checkin_by_user_id;
        $checkin->created_by_user_id = $this->created_by_user_id;
        $checkin->checkin_at         = now();
        $checkin->uuid               = Str::uuid();
//        $checkin->notes              = $this->notes;

        $checkin->save();

        (new CreateChildActivity())->forCheckin($checkin)
            ->type('checkin')
            ->withNotes($this->notes)
            ->createdByUser($this->checkin_by_user_id)
            ->create();

        return $checkin;
    }

    public function withBarcode($string)
    {
        $parsed_string = User::parseChildCheckinBarcodeString($string);

        if (!$parsed_string) {
            return $this;
        }

        return $this->forChild(Arr::get($parsed_string, 'child_user_id'));
    }

    public function forChild($child)
    {
        if ($child instanceof User) {
            $this->child = $child;
        } else {
            $this->child = User::find($child);
        }

        if (!$this->child instanceof User) {
            throw new \Exception('User for child not found.');
        }

        return $this;
    }

    public function withNotes($notes)
    {
        $this->notes = $notes;

        return $this;
    }

    public function checkinByUser(User $user)
    {
        if ($user instanceof User) {
            $this->checkin_by_user_id = $user->id;
        } elseif ($user > 0) {
            $this->checkin_by_user_id = $user;
        }

        return $this;
    }

    public function createdByUser($user = null)
    {
        if ($user instanceof User) {
            $this->created_by_user_id = $user->id;
        } elseif ($user > 0) {
            $this->created_by_user_id = $user;
        }

        return $this;
    }
}
