<?php

namespace App\ChildCheckins\Services;

use App\ChildCheckins\Activity;
use App\Users\User;

class CreateChildActivity
{
    protected $created_by_user_id = null;
    protected $notes              = null;
    protected $type               = 'checkin';
    protected $checkin;

    public function create(): Activity
    {
        return Activity::create([
            'account_id'         => $this->checkin->account_id,
            'type'               => $this->type,
            'notes'              => $this->notes,
            'child_checkin_id'   => $this->checkin->id,
            'child_user_id'      => $this->checkin->child_user_id,
            'created_by_user_id' => $this->created_by_user_id,
        ]);
    }

    public function forCheckin($checkin)
    {
        $this->checkin = $checkin;

        return $this;
    }

    public function withNotes($notes)
    {
        $this->notes = $notes;

        return $this;
    }

    public function type($type)
    {
        $this->type = $type;

        return $this;
    }

    public function createdByUser($user = null)
    {
        if ($user instanceof User) {
            $this->created_by_user_id = $user->id;
        } elseif ($user > 0) {
            $this->created_by_user_id = $user;
        }

        return $this;
    }
}
