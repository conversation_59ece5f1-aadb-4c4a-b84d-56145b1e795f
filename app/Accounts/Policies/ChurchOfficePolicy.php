<?php

namespace App\Accounts\Policies;

use App\Accounts\ChurchOffice;
use App\Users\User;

class ChurchOfficePolicy
{
    public function index(User $user)
    {
        return $user->hasPermission('account.index');
    }

    public function manage(User $user)
    {
        return $user->hasPermission('account.manage');
    }

    public function create(User $user)
    {
        return $user->hasPermission('account.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('account.manage');
    }

    public function edit(User $user, ChurchOffice $church_office)
    {
        return $user->hasPermission('account.manage');
    }

    public function save(User $user, ChurchOffice $church_office)
    {
        return $user->hasPermission('account.manage');
    }

    public function delete(User $user, ChurchOffice $church_office)
    {
        return $user->hasPermission('account.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('account.index');
    }
}