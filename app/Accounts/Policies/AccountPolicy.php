<?php

namespace App\Accounts\Policies;

use App\Accounts\Account;
use App\Users\User;

class AccountPolicy
{
    public function index(User $user)
    {
        return $user->hasPermission('account.index');
    }

    public function manage(User $user)
    {
        return $user->hasPermission('account.manage');
    }

    public function create(User $user)
    {
        return $user->hasPermission('account.manage');
    }

    public function store(User $user)
    {
        return $user->hasPermission('account.manage');
    }

    public function edit(User $user, Account $account)
    {
        return $user->hasPermission('account.manage');
    }

    public function save(User $user, Account $account)
    {
        return $user->hasPermission('account.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('account.index');
    }
}