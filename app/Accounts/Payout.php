<?php

namespace App\Accounts;

use App\Base\Models\Model;
use App\Users\Contribution;
use App\Users\Payment;
use App\Users\User;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payout extends Model
{
    use SoftDeletes;

    protected $table = 'account_payouts';

    protected $casts = [
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'deleted_at'   => 'datetime',
        'posted_at'    => 'datetime',
        'cleared_at'   => 'datetime',
        'deposited_at' => 'datetime',
    ];

    protected $fillable = [
        'created_at',
        'posted_at',
        'cleared_at',
        'deposited_at',
        'account_id',
        'account_location_id',
        'provider',
        'provider_payout_id',
        'name',
        'description',
        'is_hidden',
        'sort_id',
        'amount',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('account_payouts.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function userPayments()
    {
        return $this->hasMany(Payment::class, 'account_payout_id', 'id')
            ->where('account_id', $this->account_id);
    }
}
