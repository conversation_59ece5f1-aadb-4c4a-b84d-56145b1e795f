<?php

namespace App\Accounts;

use App\Base\Models\Model;
use App\Messages\MessageType;
use App\Users\User;

class AccountNotification extends Model
{
    const UPDATED_AT = null;

    protected $fillable = [
        'account_id',
        'message_type_id',
        'source',
        'user_id',
        'destination',
        'charge',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function messageType()
    {
        return $this->belongsTo(MessageType::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function quickCreate($account_id, $message_type_id = null, $source = null, $user_id = null, $destination = null, $charge = null)
    {
        return self::create([
            'account_id'      => $account_id,
            'message_type_id' => $message_type_id,
            'source'          => $source,
            'user_id'         => $user_id,
            'destination'     => $destination,
            'charge'          => $charge,
        ]);
    }
}