<?php

namespace App\Accounts;

use App\Base\Models\Model;

class AccountPlan extends Model
{
    protected $fillable = [
        'type',
        'name',
        'url_name',
        'domain',

        'max_emails_per_send',
        'max_emails_per_month',
        'max_sms_per_send',
        'max_sms_per_month',
        'max_voice_per_send',
        'max_voice_per_month',

        'max_users',
        'max_admins',
        'max_senders',
        'max_storage',

        'price_per_month',
        'price_per_year',

        'is_public',
        'is_active',
        'is_recommended',
        'allow_signups',

        'free_emails_per_month',
        'free_sms_per_month',
        'free_voice_per_month',
        'free_storage_per_month',
        'free_podcast_downloads_per_month',

        'price_per_email',
        'price_per_sms',
        'price_per_voice',
        'price_per_notification',
        'price_per_10000_podcast_downloads',
        'price_per_50000_podcast_downloads',
        'price_per_100_users',

        'monthly_price_per_email_group',
        'monthly_price_per_sms_group',
        'monthly_price_per_voice_group',
        'monthly_price_per_notification_group',
        'monthly_price_per_gb_storage',

        'monthly_price_online_giving',
        'monthly_price_podcasts',

        'included_users',
        'monthly_price_base',
        'monthly_price_attendance',
        'monthly_price_assignments',
        'monthly_price_visitor_tracking',
        'monthly_price_child_checkin',
        'monthly_price_financial_management',
        'monthly_price_sms_enabled',

        'monthly_price_website',
        'monthly_price_domain_management',
    ];

    public function account()
    {
        return $this->hasMany(Account::class);
    }
}