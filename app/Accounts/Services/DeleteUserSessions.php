<?php

namespace App\Accounts\Services;

use App\Users\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;

class DeleteUserSessions
{
    private $account;
    private $for_web            = false;
    private $for_mobile         = false;
    private $for_specific_users = []; // An array of User IDs that this should be run for.

    public function delete()
    {
        if (!$this->account) {
            throw new \Exception('No account set.');
        }

        // If we have said NOT for specific users, then we need to get all users for this account.
        if ($this->for_specific_users === false) {
            exit; // For now we do not allow deleting ALL account user sessions.
            $this->for_specific_users = User::where('account_id', $this->account->id)->get()->pluck('id');
        }

        if ($this->for_web) {
            $this->deleteWebLogins();
        }

        if ($this->for_mobile) {
            $this->deleteMobileLogins();
        }

        return true;
    }

    private function deleteMobileLogins()
    {
        // MOBILE LOGINS
        DB::table('oauth_access_tokens')
            ->whereIn('user_id', $this->for_specific_users)
            ->delete();

        return true;
    }

    private function deleteWebLogins()
    {
        // Delete web sessions in the database.
        DB::table('sessions')->whereIn('user_id', $this->for_specific_users)->delete();

        // WEB LOGINS
        $keys = Redis::keys('lightpost:*');
//        $data = Redis::command('keys', ['lightpost:*']);

        // Array of Redis keys that we want to delete.
        $keys_to_delete = [];

        foreach ($keys as $redis_key) {
            // Get this specific key, returns as an array with one item, with the data in the first index.
            $redis_record = Redis::mget($redis_key);

            try {
                // Unserialize the data, then unserialize it again to get the data as an array.
                $data = unserialize($redis_record[0]);
                if (is_string($data)) {
                    $data = unserialize($data);
                }

                foreach ($data as $key => $line_item) {
                    // If we're on the line with our login_token => userId, then we want to get the userId.
                    if (is_string($key) && Str::startsWith($key, 'login_') && is_int($line_item)) {

                        if (in_array($line_item, $this->for_specific_users)) {
                            // If the userId is in our list of users to delete, then we want to delete this key.
                            $keys_to_delete[] = $redis_key;
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error($e, ['redis_key' => $redis_key, 'redis_record' => $redis_record]);
            }
        }

        // Delete all identified keys in one command.
        if (count($keys_to_delete) > 0) {
            Redis::del($keys_to_delete);
        }
    }

    public function forAccount($account)
    {
        $this->account = $account;

        return $this;
    }

    public function forUsers($for_specific_users = [])
    {
        $this->for_specific_users = $for_specific_users;

        return $this;
    }

    public function forAllAccountUsers()
    {
        $this->for_specific_users = false;

        return $this;
    }

    public function forWeb($for_web = true)
    {
        $this->for_web = $for_web;

        return $this;
    }

    public function forMobile($for_mobile = true)
    {
        $this->for_mobile = $for_mobile;

        return $this;
    }

    public function forAllUsers()
    {
    }
}