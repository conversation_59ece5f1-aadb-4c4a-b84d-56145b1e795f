<?php

namespace App\Accounts\Services;

use App\Accounts\InvoiceItem;
use Brick\Math\RoundingMode;
use Brick\Money\Money;

class CreateInvoiceItem
{
    private $invoice;
    private $title       = '';
    private $description = '';
    private $quantity    = 1;
    private $type        = '';
    private $is_taxable  = true;
    private $amount      = 0;

    private $amount_subtotal = null;

    public function __construct($invoice)
    {
        $this->invoice = $invoice;

        return $this;
    }

    public function create(): InvoiceItem
    {
        $item = new InvoiceItem();

        $item->account_id         = $this->invoice->account_id;
        $item->account_invoice_id = $this->invoice->id;

        $item->title       = $this->title;
        $item->description = $this->description;
        $item->quantity    = $this->quantity;
        $item->type        = $this->type;
        $item->is_taxable  = $this->is_taxable;
        $item->amount      = $this->amount;
        // If we gave a subtotal amount to override this, use that instead.
        $item->amount_subtotal = $this->amount_subtotal ?: Money::ofMinor($item->amount, 'USD', null, RoundingMode::DOWN)->multipliedBy($this->quantity, RoundingMode::DOWN)->getMinorAmount()->toInt();
        $item->amount_total    = $item->amount_subtotal;

        $item->save();

        return $item;
    }

    public function setTitle($value)
    {
        $this->title = $value;

        return $this;
    }

    public function setDescription($value)
    {
        $this->description = $value;

        return $this;
    }

    public function setQuantity($value)
    {
        $this->quantity = $value;

        return $this;
    }

    public function setType($value)
    {
        $this->type = $value;

        return $this;
    }

    public function isTaxable($value = true)
    {
        $this->is_taxable = $value;

        return $this;
    }

    // In CENTS
    public function setAmount($value)
    {
        $this->amount = $value;

        return $this;
    }

    // In CENTS
    public function setAmountSubtotal($value)
    {
        $this->amount_subtotal = $value;

        return $this;
    }
}