<?php

namespace App\Accounts\Services;

use App\Accounts\Invoice;
use App\App\Services\SendTransactionalEmail;

class ChargeInvoice
{
    private $invoice;

    public const EXCEPTION_CODES = [
        'account_inactive'          => 401,
        'account_billing_suspended' => 402,
        'invoice_too_old'           => 402,
        'no_payment_method'         => 404,
        'amount_exceeds_history'    => 405,
        'amount_too_low'            => 406,
        'amount_exceeds_max_limit'  => 407,
        'recent_payment_limit'      => 408,
        'unusable_payment_method'   => 409,
        'payment_received'          => 500,
    ];

    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;

        return $this;
    }

    public function charge()
    {
        $this->checkAccountDetails();
        $this->checkInvoiceDetails();
        $this->checkPrimaryPaymentMethod();
        $this->checkPastInvoices();
        $this->checkRecentPayments();
        $this->checkMaxLimit();
        $this->checkMinLimit();

        // If we've made it this far, do the charge.
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));

        // Retrieve the full payment method object from Stripe
        $paymentMethod = $stripe->paymentMethods->retrieve($this->invoice->account->stripe_payment_method_id);
        // Start a payment intent.
        $result = $stripe->paymentIntents->create([
            'customer'             => $this->invoice->account->stripe_customer_id,
            'amount'               => $this->invoice->amount_total,
            'currency'             => 'usd',
            'payment_method'       => $this->invoice->account->stripe_payment_method_id,
            'payment_method_types' => [$paymentMethod->type],
            'confirm'              => true,
            'description'          => $this->invoice->posted_at ? $this->invoice->posted_at->format('F Y') : now()->format('F Y'),
        ]);

        // Capture the payment.
        // In testing, it looks like the above call does this automatically.
        // $result = $stripe->paymentIntents->capture($result->id, []);

        // Save this info on the invoice.
        $this->invoice->paid_at              = now();
        $this->invoice->payment_reference_id = $result->id;
        $this->invoice->save();

        // Update our account's last payment date.
        $account                  = $this->invoice->account;
        $account->last_payment_at = now();
        $account->save();

        // Send a receipt if we have an email to send it to.
        if ($account->payment_receipt_email) {
            // Allow for the possibility that we have many comma separate emails
            $email_addresses = explode(',', $account->payment_receipt_email);

            foreach ($email_addresses as $email_address) {
                $this->sendPaymentReceipt($email_address);
            }
        }

        return true;
    }

    private function sendPaymentReceipt($email_address = null)
    {
        dispatch(function () use ($email_address) {
            (new SendTransactionalEmail())
                ->to($email_address ? trim($email_address) : $this->invoice->account->payment_receipt_email)
                ->withAttachment(
                    config('app.name') . '_Receipt-' . now()->format('F Y') . '.pdf',
                    'application/pdf',
                    base64_encode(
                        (new AccountInvoice())
                            ->forInvoice($this->invoice)
                            ->createPDF('Invoice ' . $this->invoice->created_at->format('M Y') . '.pdf', 'S')
                    )
                )
                ->type('account_payment_receipt')
                ->mergeData([
                    'account_name' => $this->invoice->account->name,
                    'month_name'   => now()->format('F Y'),
                ])
                ->send();
        });
    }

    // Make sure the account is active and billing is active.
    private function checkAccountDetails()
    {
        if (!$this->invoice->account->billing_active) {
            throw new \Exception('Account billing is not active.', self::EXCEPTION_CODES['account_billing_suspended']);
        }

        if ($this->invoice->account->last_payment_at && $this->invoice->account->last_payment_at->diffInDays(now()) < 2) {
            throw new \Exception('Account indicates a recent payment already.', self::EXCEPTION_CODES['payment_received']);
        }
    }

    // Make sure the invoice is recent and not already paid.
    private function checkInvoiceDetails()
    {
        if ($this->invoice->posted_at->diffInDays(now()) > 69) { // 2 months + 7 days
            throw new \Exception('Invoice is too old.', self::EXCEPTION_CODES['invoice_too_old']);
        }

        if ($this->invoice->paid_at) {
            throw new \Exception('Invoice already paid.', self::EXCEPTION_CODES['payment_received']);
        }
    }

    // Make sure the account has a primary payment method and it's a card, not ACH.
    private function checkPrimaryPaymentMethod()
    {
        if (!$this->invoice->account->stripe_payment_method_id) {
            throw new \Exception('No primary payment method set.', self::EXCEPTION_CODES['no_payment_method']);
        }
    }

    // Make sure the new invoice amount is not too much more than the last invoice.
    private function checkPastInvoices()
    {
        $all_invoices_count = $this->invoice->account->invoices()
            ->orderBy('created_at', 'desc')
            ->where('id', '<>', $this->invoice->id)
            ->count();

        $last_invoice = $this->invoice->account->invoices()
            ->orderBy('created_at', 'desc')
            ->where('id', '<>', $this->invoice->id)
            ->first();

        if (!$last_invoice) {
            return true;
        }

        // We only care if there are more than 2 invoices because accounts getting started will have smaller historical invoices as they grow.
        if ($all_invoices_count > 2 && $this->invoice->amount_total > ($last_invoice->amount_total * 2)) {
            throw new \Exception('New invoice amount is more than 200% of last invoice.', self::EXCEPTION_CODES['amount_exceeds_history']);
        }
    }

    // Make sure the last payment on the last invoice was not too recent.
    private function checkRecentPayments()
    {
        $last_paid_invoice = $this->invoice->account->invoices()
            ->whereNotNull('paid_at')
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$last_paid_invoice) {
            return true;
        }

        // If the last payment was made in the last 2 days, throw an error.
        if ($last_paid_invoice->paid_at->diffInDays(now()) < 2) {
            throw new \Exception('Payments too close together.', self::EXCEPTION_CODES['recent_payment_limit']);
        }
    }

    // Make sure the new invoice amount is not more than a max limit.
    private function checkMaxLimit()
    {
        // $325.00
        if ($this->invoice->amount_total > 32500) {
            throw new \Exception('New invoice amount is more than max limit.', self::EXCEPTION_CODES['amount_exceeds_max_limit']);
        }
    }

    // Make sure the new invoice amount is not less than a min limit.
    private function checkMinLimit()
    {
        // $15.00
        if ($this->invoice->amount_total < 900) {
            throw new \Exception('New invoice amount is less than minimum limit.', self::EXCEPTION_CODES['amount_too_low']);
        }
    }
}