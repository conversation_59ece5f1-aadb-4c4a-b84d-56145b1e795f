<?php

namespace App\Accounts;

use App\Base\Models\Model;

class AccountLocation extends Model
{
    protected $fillable = [
        'account_id',
        'name',
        'short_name',
        'url_name',
        'address1',
        'address2',
        'address3',
        'city',
        'state',
        'postal_code',
        'country_code',
        'phone_work',
        'phone_fax',
        'phone_other',
        'timezone',
        'email',
        'status',
        'is_active',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function settings()
    {
        return $this->hasMany(AccountSetting::class, 'account_location_id');
    }
}