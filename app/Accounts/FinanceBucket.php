<?php

namespace App\Accounts;

use App\Base\Models\Model;
use App\Finance\BucketCategory;
use App\Finance\Budget;
use App\Finance\ReimbursementRequestItem;
use App\Finance\Transaction;
use App\Finance\TransactionSplit;
use App\Users\Payment;
use App\Users\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;

class FinanceBucket extends Model
{
    use SoftDeletes;

    protected $table = 'account_finance_buckets';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'finance_bucket_category_id',
        'name',
        'url_name',
        'description',
        'account_code',
        'is_contribution_bucket',
        'is_reimbursement_bucket',
        'is_expense',
        'is_income',
        'is_hidden',
        'sort_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function category()
    {
        return $this->belongsTo(BucketCategory::class, 'finance_bucket_category_id');
    }

    public function budgets()
    {
        return $this->hasMany(Budget::class, 'account_finance_bucket_id');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'id', 'finance_transaction_id');
    }

    public function transactionSplits()
    {
        return $this->hasMany(TransactionSplit::class, 'id', 'finance_transaction_split_id');
    }

    public function reimbursementRequestItems()
    {
        return $this->hasMany(ReimbursementRequestItem::class, 'id', 'finance_reimbursement_request_item_id');
    }

    public function currentBudget()
    {
        return $this->hasOne(Budget::class, 'account_finance_bucket_id')
            ->where('year', now()->format('Y'));
    }

    public function budgetForYear($year)
    {
        return $this->hasOne(Budget::class, 'account_finance_bucket_id')
            ->where('year', $year);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class)
            ->where('account_id', $this->account_id);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'user_to_finance_bucket', 'user_group_id', 'user_id')
            ->as('settings')
            ->withPivot([
                'can_view_details',
            ]);
    }

    protected function isContribution(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $attributes['is_contribution_bucket']
        );
    }

    protected function isReimbursement(): Attribute
    {
        return Attribute::make(
            get: fn($value, $attributes) => $attributes['is_reimbursement_bucket']
        );
    }

    public function scopeIsContribution($query)
    {
        return $query->where('is_contribution_bucket', true);
    }

    public function scopeIsNotContribution($query)
    {
        return $query->where('is_contribution_bucket', false);
    }

    public function scopeIsReimbursement($query)
    {
        return $query->where('is_reimbursement_bucket', true);
    }

    public function scopeIsNotReimbursement($query)
    {
        return $query->where('is_reimbursement_bucket', false);
    }

    public function scopeIsExpense($query)
    {
        return $query->where('is_expense', true);
    }

    public function scopeIsNotExpense($query)
    {
        return $query->where('is_expense', false);
    }

    public function scopeIsIncome($query)
    {
        return $query->where('is_income', true);
    }

    public function scopeIsNotIncome($query)
    {
        return $query->where('is_income', false);
    }

    public function scopeNotHidden($query)
    {
        return $query->where('is_hidden', false);
    }
}
