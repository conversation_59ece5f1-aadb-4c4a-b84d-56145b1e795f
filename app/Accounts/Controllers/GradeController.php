<?php

namespace App\Accounts\Controllers;

use App\Accounts\Grade;
use App\Base\Http\Controllers\Controller;
use App\Users\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GradeController extends Controller
{
    public function index()
    {
        return view('admin.accounts.grades.index')
            ->with(
                'grades',
                Grade::visibleTo(Auth::user())
                    ->withCount('users')
                    ->orderBy('sort_id', 'asc')
                    ->get()
            )
            ->with(
                'first_grade',
                Grade::visibleTo(Auth::user())
                    ->withCount('users')
                    ->orderBy('sort_id', 'asc')
                    ->first()
            )
            ->with(
                'last_grade',
                Grade::visibleTo(Auth::user())
                    ->withCount('users')
                    ->orderBy('sort_id', 'desc')
                    ->first()
            );
    }

    public function show(Grade $grade)
    {
        return view('admin.accounts.grades.show')
            ->with('grade', $grade);
    }

    public function create()
    {
        return view('admin.accounts.grades.create');
    }

    public function store()
    {
        try {
            DB::transaction(function () {
                $grade = (new Grade())->fill([
                    'account_id'  => Auth::user()->account_id,
                    'name'        => request()->get('name'),
                    'sort_id'     => request()->get('sort_id'),
                    'description' => request()->get('description'),
                ]);

                $grade->save();
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.accounts.grades.index'))->with('message.success', 'Saved successfully.');
    }

    public function edit(Grade $grade)
    {
        return view('admin.accounts.grades.edit')
            ->with('grade', $grade);
    }

    public function save(Grade $grade)
    {
        $grade = Grade::visibleTo(Auth::user())->find($grade->id);

        try {
            DB::transaction(function () use ($grade) {
                $grade->name        = request()->get('name');
                $grade->description = request()->get('description');
                $grade->sort_id     = request()->get('sort_id');

                $grade->save();
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.accounts.grades.index'))->with('message.success', 'Saved successfully.');
    }

    public function destroy(Grade $grade)
    {
        $grade = Grade::visibleTo(Auth::user())->where('id', $grade->id)->first();

        try {
            DB::transaction(function () use ($grade) {
                // Remove this grade from all users with it
                foreach (User::where('user_grade_id', $grade->id)->get() as $user) {
                    $user->user_grade_id = null;
                    $user->save();
                }

                // Delete the grade
                $grade->delete();
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.accounts.grades.index'))->with('message.success', 'Saved successfully.');
    }

    public function promote()
    {
        $this->validate(request(), [
            'grades'           => 'required|array',
            'promotion_action' => 'required|string',
        ]);

        // Check if we have grades with no sort_id.  This will cause issues with our promotion/demotion.
        $null_sort_grades = Grade::visibleTo(Auth::user())
            ->whereNull('sort_id')
            ->get();

        // If we found some, Push the user back.
        if ($null_sort_grades->count() > 0) {
            return back()->with('message.failure', 'Some grades have no sort_id.  Please assign a sort_id to all grades before promoting/demoting.');
        }

        // Get the list of all the grades we selected to promote/demote.
        $grades = Grade::visibleTo(Auth::user())
            ->whereIn('id', request('grades'))
            ->with('users')
            ->orderBy('sort_id', request('promotion_action') == 'promote' ? 'desc' : 'asc')
            ->get();

        // Get the first grade in our list -- these are the ones dropping off, if we ever hit the first grade in the list.
        $first_grade = Grade::visibleTo(Auth::user())
            ->orderBy('sort_id', request('promotion_action') == 'promote' ? 'desc' : 'asc')
            ->first();

        $users_removed = 0;
        $users_moved   = 0;

        $grades = $grades->values()->all();

        while ($grade = current($grades)) {
            $key        = key($grades);
            $next_grade = next($grades);

            // When we're out of grades and the grade we're on is the "last" grade of all our grades, remove our last group from the grade.
            if ($key === 0 && $grade->id == $first_grade->id) {
                $users_removed = count($grade->users);

                foreach ($grade->users as $user) {
                    $user->user_grade_id = null;
                    $user->save();
                }
            } else {
                $users_moved   += count($grade->users);
                $promote_grade = Grade::visibleTo(Auth::user())
                    ->orderBy('sort_id', request('promotion_action') == 'promote' ? 'asc' : 'desc')
                    ->where('sort_id', request('promotion_action') == 'promote' ? '>' : '<', $grade->sort_id)
                    ->first();

                foreach ($grade->users as $user) {
                    $user->user_grade_id = $promote_grade->id;
                    $user->save();
                }
            }
        }

        return back()->with(
            'message.success',
            'Grade ' . request('promotion_action') . ' completed successfully.
        ' . $users_moved . ' users moved.
        ' . $users_removed . ' users dropped from grade: ' . $first_grade->name . '.'
        );
    }

}
