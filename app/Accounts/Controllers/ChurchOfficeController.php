<?php

namespace App\Accounts\Controllers;

use App\Accounts\ChurchOffice;
use App\Base\Http\Controllers\Controller;
use Illuminate\Support\Str;

class ChurchOfficeController extends Controller
{
    public function index()
    {
        return view('admin.accounts.settings.church-offices.church-offices');
    }

    public function users(ChurchOffice $church_office)
    {
        return view('admin.accounts.settings.church-offices.church-office-users')
            ->with('church_office', $church_office)
            ->with('users', $church_office->users);
    }

    public function create()
    {
        return view('admin.accounts.settings.modals.create-church-office');
    }

    public function edit(ChurchOffice $church_office)
    {
        return view('admin.accounts.settings.modals.edit-church-office')
            ->with('church_office', $church_office);
    }

    public function createSubmit()
    {
        $office = new ChurchOffice();

        $office->account_id         = auth()->user()->account_id;
        $office->name               = request()->get('name');
        $office->plural_name        = request()->get('plural_name') ?: Str::plural(request()->get('name'));
        $office->short_name         = Str::kebab(Str::lower(request()->get('name')));
        $office->url_name           = Str::kebab(Str::lower(request()->get('name')));
        $office->description        = request()->get('description');
        $office->show_in_leadership = request()->get('show_in_leadership') ? 1 : 0;
        $office->is_public          = request()->get('is_public') ? 1 : 0;
        $office->sort_id            = ChurchOffice::visibleTo(auth()->user())->orderBy('sort_id', 'desc')->first()?->sort_id + 1;

        $office->save();

        $office->generateUlid();

        return redirect(route('admin.accounts.settings.church-offices.index'))
            ->with('message.success', 'Success!  New office created.');
    }

    public function editSubmit(ChurchOffice $church_office)
    {
        $church_office->name               = request()->get('name');
        $church_office->plural_name        = request()->get('plural_name');
        $church_office->short_name         = Str::kebab(Str::lower(request()->get('name')));
        $church_office->url_name           = Str::kebab(Str::lower(request()->get('name')));
        $church_office->description        = request()->get('description');
        $church_office->is_public          = request()->get('is_public') ? 1 : 0;
        $church_office->show_in_leadership = request()->get('show_in_leadership') ? 1 : 0;

        $church_office->save();

        return redirect(route('admin.accounts.settings.church-offices.index'))
            ->with('message.success', 'Success!  Updated.');
    }

    public function deleteSubmit(ChurchOffice $church_office)
    {
        foreach ($church_office->users as $user) {
            $user->churchOffices()->detach($church_office->id);
        }

        $church_office->delete();

        return redirect(route('admin.accounts.settings.church-offices.index'))
            ->with('message.success', 'Office deleted successfully.');
    }
}
