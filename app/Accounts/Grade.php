<?php

namespace App\Accounts;

use App\Accounts\Scopes\GradeVisibleToScope;
use App\Base\Models\Model;
use App\Users\User;
use Illuminate\Database\Eloquent\SoftDeletes;

class Grade extends Model
{
    use SoftDeletes;

    protected $table = 'user_grades';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'account_location_id',
        'name',
        'description',
        'sort_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return (new GradeVisibleToScope())->getQuery($query, $user);
    }

    public function users()
    {
        return $this->hasMany(User::class, 'user_grade_id');
    }

    public function safeUserCount(User $user)
    {
        return $this->users()->visibleTo($user)->count();
    }
}
