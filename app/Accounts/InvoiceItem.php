<?php

namespace App\Accounts;

use App\Base\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceItem extends Model
{
    use SoftDeletes;

    protected $table = 'account_invoice_items';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'account_invoice_id',
        'title',
        'description',
        'type',
        'quantity',
        'amount',
        'amount_subtotal',
        'amount_tax',
        'amount_total',
        'is_taxable',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'account_invoice_id');
    }

    public function scopeIsPending($query)
    {
        return $query->whereNull('account_invoice_id');
    }
}
