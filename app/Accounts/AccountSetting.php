<?php

namespace App\Accounts;

use Illuminate\Database\Eloquent\Model;

class AccountSetting extends Model
{
    protected $table = 'account_settings';

    const UPDATED_AT = null;
    const CREATED_AT = null;

    protected $fillable = [
        'key',
        'name',
        'description',
        'type',
        'allow_account_to_view',
        'allow_account_to_enable',
        'allow_account_to_select_permissions',
        'default',
        'values',
    ];

    public static $types = [
        'bool'    => 'Boolean',
        'integer' => 'Integer',
        'string'  => 'String',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class)->whereNull('account_location_id');
    }

    public function location()
    {
        return $this->belongsTo(AccountLocation::class);
    }

    public function valueForAccountId($account_id): ?AccountSettingValue
    {
        return $this->hasOne(AccountSettingValue::class)
            ->where('account_id', $account_id)
            ->first();
    }

    public function value()
    {
        return $this->hasOne(AccountSettingValue::class);
    }

    public function isBool()
    {
        return $this->type == 'bool';
    }

    public function isInteger()
    {
        return $this->type == 'integer';
    }

    public function isString()
    {
        return $this->type == 'string';
    }
}
