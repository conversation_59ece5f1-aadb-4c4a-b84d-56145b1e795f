<?php

namespace App\Accounts\Traits;

use App\Accounts\AccountSetting;
use App\Accounts\AccountSettingValue;

trait AccountSettingTrait
{
    public $settings_cache       = null;
    public $setting_values_cache = null;

    public function getSetting($key, $default = null)
    {
        return $this->getFromCache($key, $default);
    }

    public function hasSetting($key, $default = null)
    {
        return $this->getFromCache($key, $default) ? true : false;
    }

    public function setSetting($key, $value)
    {
        if ($this->settingKeyExistsForAccount($key)) {
            $setting        = $this->setting_values_cache->firstWhere('account_setting_id', optional($this->settings_cache->firstWhere('key', $key))->id);
            $setting->value = $value;
            $setting->save();
        } else {
            AccountSettingValue::create([
                'account_id'         => $this->id,
                'account_setting_id' => optional($this->settings_cache->firstWhere('key', $key))->id,
                'value'              => $value,
            ]);
        }
    }

    public function settingKeyExistsForAccount($key)
    {
        $this->loadSettings();

        return $this->setting_values_cache->where('account_setting_id', optional($this->settings_cache->firstWhere('key', $key))->id)->count() > 0 ? true : false;
    }

    private function getFromCache($key, $default = null)
    {
        $this->loadSettings();

        return optional($this->setting_values_cache->firstWhere('account_setting_id', optional($this->settings_cache->firstWhere('key', $key))->id))->value ?: $default;
    }

    private function loadSettings()
    {
        if (!$this->settings_cache) {
            // Get all possible account settings
            $this->settings_cache = AccountSetting::get();
        }
        if (!$this->setting_values_cache) {
            $this->setting_values_cache = AccountSettingValue::where('account_id', $this->id)->get();
        }
    }
}