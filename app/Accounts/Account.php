<?php

namespace App\Accounts;

use App\Accounts\Traits\AccountSettingTrait;
use App\Attendance\AttendanceType;
use App\BibleClasses\BibleClassGroup;
use App\Calendars\Calendar;
use App\Crises\Crisis;
use App\Crises\CrisisSetting;
use App\Finance\Transaction;
use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Subarea;
use App\Prayers\PrayerSetting;
use App\Users\Group;
use App\Users\User;
use Hashids\Hashids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class Account extends Model
{
    use AccountSettingTrait, HasFactory;

    protected $table = 'accounts';

    protected $casts = [
        'created_at'           => 'datetime',
        'updated_at'           => 'datetime',
        'last_payment_at'      => 'datetime',
        'last_invoice_at'      => 'datetime',
        'next_invoice_at'      => 'datetime',
        'can_send_emails'      => 'boolean',
        'can_send_sms'         => 'boolean',
        'can_send_voice_calls' => 'boolean',
    ];

    protected $fillable = [
        'account_plan_id',
        'last_payment_at',
        'last_invoice_at',
        'next_invoice_at',
        'billing_frequency',
        'name',
        'short_name',
        'url_name',
        'church_website',
        'group_email_prefix',
        'static_short_name',
        'podcasts_prefix',
        'domain',
        'subdomain',
        'cname_domain',
        'cname_subdomain',
        'use_ssl',
        'file_storage_service',
        's3_base_url',
        's3_region',
        's3_bucket',
        's3_folder',
        'require_verified_emails',
        'require_verified_sms',
        'stripe_customer_id',
        'stripe_payment_method_id',
        'address1',
        'address2',
        'address3',
        'city',
        'state',
        'postal_code',
        'country_code',
        'phone_work',
        'phone_fax',
        'phone_other',
        'ein',
        'ein_name',
        'timezone',
        'email',
        'status',
        'is_active',
        'billing_active',
        'is_suspended',
        'is_late_on_payment',
        'is_tax_exempt',
        'stripe_customer_id',
        'stripe_account_id',
        'stripe_granted_scope',
        'stripe_account_publishable_key',
        'use_v2_billing',
        'api_token',
        'payment_receipt_email',
        'mailing_address1',
        'mailing_address2',
        'mailing_address3',
        'mailing_city',
        'mailing_state',
        'mailing_postal_code',
        'mailing_country_code',
        'congregation_contact_email',
        'public_contact_email',
        'billing_contact_email',
        'can_send_emails',
        'can_send_sms',
        'can_send_voice_calls',
        'canceled_at',
        'canceled_reason',
    ];

    protected $guarded = [
        'ulid',
        'hash_id',
    ];

    protected $_settings_cache = null;

    public function generateUlid()
    {
        if (!$this->ulid) {
            $this->ulid = Str::ulid()->toBase32();
            $this->save();
        }
    }

    public function getUlid()
    {
        if (!$this->ulid) {
            $this->generateUlid();
        }

        return $this->ulid;
    }

    public function generateHashId()
    {
        if (!$this->hash_id) {
            $hashids       = new Hashids();
            $this->hash_id = $hashids->encode($this->id);
            $this->save();
        }
    }

    public function getHashId()
    {
        if (!$this->hash_id) {
            $this->generateHashId();
        }

        return $this->hash_id;
    }

    public function getAuthIdentifierName()
    {
        return 'api_token';
    }

    public function getAuthIdentifier()
    {
        return $this->api_token;
    }

    public function attendanceTypes()
    {
        return $this->hasMany(AttendanceType::class)
            ->orderBy('sort_id', 'asc');
    }

    public function churchOffices()
    {
        return $this->hasMany(ChurchOffice::class)
            ->orderBy('sort_id', 'asc');
    }

    public function contributionBuckets()
    {
        return $this->hasMany(FinanceBucket::class)
            ->where('is_contribution_bucket', true)
            ->orderBy('sort_id', 'asc');
    }

    public function reimbursementBuckets()
    {
        return $this->hasMany(FinanceBucket::class)
            ->where('is_reimbursement_bucket', true);
    }

    public function financeBuckets()
    {
        return $this->hasMany(FinanceBucket::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function involvementCategories()
    {
        return $this->hasMany(Category::class);
    }

    public function involvementAreas()
    {
        return $this->hasManyThrough(Area::class, Category::class, 'id', 'involvement_category_id');
    }

    public function involvementSubareas()
    {
        return $this->hasMany(Subarea::class);
    }

    public function calendars()
    {
        return $this->hasMany(Calendar::class);
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function groups()
    {
        return $this->hasMany(Group::class);
    }

    public function plan()
    {
        return $this->belongsTo(AccountPlan::class, 'account_plan_id');
    }

    public function prayerSettings()
    {
        return $this->hasOne(PrayerSetting::class)->withDefault([
            'account_id' => $this->id,
        ]);
    }

    public function crises()
    {
        return $this->hasMany(Crisis::class);
    }

    public function getActiveCrisis()
    {
        return $this->crises()->where('is_active', true)->orderBy('created_at')->first();
    }

    public function crisisSettings()
    {
        return $this->hasOne(CrisisSetting::class)->withDefault([
            'account_id' => $this->id,
        ]);
    }

    public function locations()
    {
        return $this->hasMany(AccountLocation::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function settings()
    {
        return AccountSetting::all();
    }

    public function settingValues()
    {
        return $this->hasMany(AccountSettingValue::class)->whereNull('account_location_id');
    }

    public function getSettingValue($key)
    {
        if (!$this->_settings_cache) {
            $this->_settings_cache = $this->settingValues()
                ->with('setting')
                ->get();
        }

        $setting = $this->_settings_cache->where('value', true)->where('setting.key', $key)->first();

        // If we found no setting, don't crash, just return null.
        if (!$setting) {
            return null;
        }

        // If this value type is bool, force a return of true/false (not a number)
        if ($setting->setting->type == 'bool') {
            return boolval($setting->value);
        }

        // If this value is an integer, force an int to be returned (not a string value from the database)
        if ($setting->setting->type == 'integer') {
            return (int)$setting->value;
        }

        // Else, just return what the value is.
        return $setting->value;
    }

    public function hasFeature($key)
    {
        if (!$this->_settings_cache) {
            $this->_settings_cache = $this->settingValues()
                ->with('setting')
                ->get();
        }

        return boolval($this->_settings_cache->where('value', true)->where('setting.key', $key)->count());
//        return $this->settingValues()->whereHas('setting', function ($query) use ($key) {
//            $query->where('key', $key);
//        })
//            ->where('value', true)
//            ->exists();
    }

    public function hasFeatureForMember($key)
    {
        if (!$this->_settings_cache) {
            $this->_settings_cache = $this->settingValues()
                ->with('setting')
                ->get();
        }

        return boolval(
            $this->_settings_cache->where('value', true)
                ->where('setting.key', $key)
                ->where('enable_for_member', true)
                ->count()
        );
    }

    public function hasGroupWithPostsEnabled()
    {
        return Group::viewableToUser(auth()->user())->exists();
    }

    public function notifications()
    {
        return $this->hasMany(AccountNotification::class);
    }

    public function getReplyToEmail()
    {
        return $this->email;
    }

    public function hasActiveCrisis()
    {
        return $this->crises()->where('is_active', true)->exists();
    }

    public function currentBibleClassGroup()
    {
        $group = BibleClassGroup::where('account_id', $this->id)
            ->isCurrentlyActive()
            ->orderBy('created_at', 'DESC')
            ->first();

        // If we don't have a "currently active" group, use the next group that has sign-ups active.
        if (!$group) {
            return $this->activeSignupBibleClassGroup();
        }

        return $group;
    }

    public function activeSignupBibleClassGroup()
    {
        return BibleClassGroup::where('account_id', $this->id)
            ->signupIsActive()
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    public function getDefaultVisitorGroup()
    {
        $group = $this->groups()
            ->indicatesVisitorGroup()
            ->where('is_default_visitor_group', true)
            ->first();

        if (!$group) {
            $group = $this->groups()
                ->indicatesVisitorGroup()
                ->orderBy('created_at')
                ->first();
        }

        return $group;
    }

    public function getDefaultMemberGroup()
    {
        $group = $this->groups()
            ->indicatesMembership()
            ->where('is_default_member_group', true)
            ->first();

        if (!$group) {
            $group = $this->groups()
                ->indicatesMembership()
                ->orderBy('sort_id')
                ->first();
        }

        return $group;
    }

    // Gives us the overage count so we can bill for them (i.e. 200 users = 2 unit counts)
    public function getUserOverageUnitCount($unit_amount = 100)
    {
        $number_of_members    = $this->users()->membersOnly()->count();
        $base_allowed_members = $this->plan->included_users;

        // If we have 50-150 members, let's bill for more.
        // After 150 members, we give some grace before billing for more. (Currently 25 members over)
        // Added 2025-03-27: We only give this grace until 2025-04-01 - we updated the pricing to start at 100 members for the base plan (100% increase!) - so the line is a hard cut-off now.
        if ($number_of_members > 150 && $this->created_at < Carbon::create(2025, 4)) {
            $base_allowed_members += 25;
        }

        return ceil(($number_of_members - $base_allowed_members) / $unit_amount);
    }

    public function getAddressString($html_format = false)
    {
        $return_string = null;

        $return_string .= @$this->address1 . ', ';

        if (isset($this->address2) && $this->address2 > '') {
            $return_string .= $this->address2 . ', ';
        }

        $return_string .= @$this->city . ', ' . @$this->state . ', ' . @$this->postal_code;

        if ($html_format) {
            $return_string = urlencode($return_string);
        }

        return $return_string;
    }
}
