<?php

namespace App\Accounts;

use App\Base\Models\Model;
use App\Users\User;
use Brick\Math\RoundingMode;
use Brick\Money\Money;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use SoftDeletes;

    protected $table = 'account_invoices';

    protected $TAX = .0825;

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'posted_at'  => 'datetime',
        'due_at'     => 'datetime',
        'sent_at'    => 'datetime',
        'paid_at'    => 'datetime',
    ];

    protected $fillable = [
        'posted_at',
        'due_at',
        'sent_at',
        'paid_at',
        'amount_credits',
        'amount_charges',
        'amount_subtotal',
        'amount_tax',
        'amount_discount',
        'amount_total',
        'payment_reference_id',
        'description',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('account_invoices.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function items()
    {
        return $this->hasMany(InvoiceItem::class, 'account_invoice_id');
    }

    public function formatAmount($raw_amount)
    {
        return Money::ofMinor($raw_amount, 'USD')->getAmount();
    }

    public function addPendingItems()
    {
        $pending_items = InvoiceItem::where('account_id', $this->account_id)
            ->isPending()
            ->get();

        foreach ($pending_items as $item) {
            $item->account_invoice_id = $this->id;

            $item->save();
        }

        return true;
    }

    public function calculateAndSaveTotals()
    {
        $amount_credits  = Money::ofMinor(0, 'USD');
        $amount_charges  = Money::ofMinor(0, 'USD');
        $amount_subtotal = Money::ofMinor(0, 'USD');
        $amount_tax      = Money::ofMinor(0, 'USD');
        $amount_discount = Money::ofMinor(0, 'USD');
        $amount_total    = Money::ofMinor(0, 'USD');

        foreach ($this->items as $item) {
            // Apply tax, if our account is not tax exempt.
            if ($this->account->is_tax_exempt) {
                $item_tax = Money::ofMinor(0, 'USD');
            } else {
                $item_tax = Money::ofMinor($item->amount_total, 'USD')->multipliedBy($this->TAX, RoundingMode::DOWN);
            }

            // Get our subtotal after applying tax.
            $item_subtotal = Money::ofMinor($item->amount_total, 'USD');

            if ($item->amount_total < 0) {
                $amount_credits = $amount_credits->plus($item_subtotal, RoundingMode::DOWN);
            }
            if ($item->amount_total > 0) {
                $amount_charges = $amount_charges->plus($item_subtotal, RoundingMode::DOWN);
            }

            // We calculate tax manually for now.
            // $amount_tax += $item->amount_tax;
            $amount_tax = $amount_tax->plus($item_tax, RoundingMode::DOWN);

            $amount_subtotal = $amount_subtotal->plus($item_subtotal, RoundingMode::DOWN);

            $amount_total = $amount_total->plus($item_subtotal, RoundingMode::DOWN)->plus($item_tax, RoundingMode::DOWN);
        }

        $this->amount_credits = $amount_credits->getMinorAmount()->toInt();
        $this->amount_charges = $amount_charges->getMinorAmount()->toInt();
        $this->amount_tax     = $amount_tax->getMinorAmount()->toInt();
        $this->amount_total   = $amount_total->getMinorAmount()->toInt();

        $this->save();

        return true;
    }
}
