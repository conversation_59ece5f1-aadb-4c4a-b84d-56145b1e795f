<?php

namespace App\Reports\Services;

use App\Users\Address;
use App\Users\Phone;
use TCPDF;

class PrintablePhotoDirectory
{
    private $users;
    private $pdf;
    private $asterisk_unbaptized = false;
    private $asterisk_baptized   = false;

    const FAMILIES_PER_SECTION = 4;

    const DEFAULT_FONT_SIZE = 10;

//    const DEFAULT_ROW_POSITION = 12;
//    const DEFAULT_COL_POSITION = 14;
//    const DEFAULT_ROW_HEIGHT   = 64;
//    const DEFAULT_COL_WIDTH    = 64;
//    const CELL_WIDTH           = 60;
//    const CELL_HEIGHT          = 60;
    const DEFAULT_ROW_POSITION = 8;
    const DEFAULT_COL_POSITION = 5;
    const DEFAULT_ROW_HEIGHT   = 50;
    const DEFAULT_COL_WIDTH    = 44;
    const CELL_WIDTH           = 44;
    const CELL_HEIGHT          = 50;
    const CELL_BORDER          = 0;

    const BLANK_IMAGE = '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';

    public function withUsers($users)
    {
        $this->users = $users;

        return $this;
    }

    public function createPDF($filename)
    {
        $this->pdf = new TCPDF('P');
//        $this->pdf = new TCPDF('P', 'mm', [140, 216], true, 'UTF-8', false);

        $this->pdf->SetMargins(12, 10, 12, false);
//        $this->pdf->setPageOrientation('P', false, 0);

        $sections = $this->createSections();

        $this->pdf->SetCreator('Lightpost');
        $this->pdf->SetSubject('Member Photo Directory');
        $this->pdf->SetKeywords('');
        $this->pdf->setPrintHeader(false);
        $this->pdf->setPrintFooter(false);
//        $this->pdf->SetTopMargin(5);
        $this->pdf->SetFont('helvetica', '', self::DEFAULT_FONT_SIZE);
        $this->pdf->SetAutoPageBreak(false);

        foreach ($sections as $section) {
            $this->pdf->AddPage('P');

            $this->renderSection($section);
        }

        // close and output PDF document
        $this->pdf->Output($filename, 'D');
    }

    private function createSections()
    {
        $sections = [];

        $section = 1;
        $i       = 1;

        foreach ($this->users as $family) {
            $sections[$section][] = $family;

            // calculate the next section after this one is full
            if ($i % self::FAMILIES_PER_SECTION == 0) {
                $section++;
            }

            $i++;
        }

        return $sections;
    }

    private function get_http_response_code($url)
    {
        $headers = get_headers($url);
        return substr($headers[0], 9, 3);
    }

    private function renderSection($contents)
    {
        $cell_content = null;

        $cell_content .= '<table width="100%" cellpadding="10">';
        foreach ($contents as $family) {
            $second       = null;
            $second_phone = null;
            $head_phone   = null;
            $address      = null;
            $head_email   = null;
            $spouse_email = null;

            $family_phone = $family->getFamilyPhone();
            $head         = $family;
            $head_phone   = $head->getBestPhone();
            $head_email   = $head->getBestEmail();
            $spouse       = $head->spouse;
            $kids         = $head->kids(include_dependents: true)->get();

            $kids_list = null;
            if ($kids) {
                foreach ($kids as $kid) {
                    $asterisk  = $this->getAsterisk($kid);
                    $kids_list .= $kid->display_first_name . $asterisk . ', ';
                }

                $kids_list = trim($kids_list, ', ');
            }

            $spouse_phone = null;

            if ($spouse) {
                $spouse_phone = $spouse->getBestPhone(false);
                $spouse_email = $spouse->getBestEmail();
            }

            $address = $head->getFamilyAddress();
            $mailing = $head->getMailingAddress();
            if ($mailing && $address && $address->id === $mailing->id) {
                $mailing = null;
            }

            $cell_content .= '<tr><td style="padding: 8px; text-align:center;" width="275px">';

            if ($family->familyPrimaryPhoto() && $this->get_http_response_code($family->familyPrimaryPhoto()?->getCdnUrl(512)) == "200") {
                $cell_content .= '<img src="@' . base64_encode(@file_get_contents($family->familyPrimaryPhoto()?->getCdnUrl(512))) . '" height="175px" />';
            } else {
                $cell_content .= '<img src="@' . self::BLANK_IMAGE . '" width="175px" height="175px"/>';
            }

            $cell_content .= '</td><td padding="12">';

            $cell_content .= '<span style="font-size: 138%; font-weight: bold;">' . $family->last_name . '</span>, 
' . '<span style="font-size: 125%"><strong>' . $head->display_first_name . $this->getAsterisk($head) . '</strong>' . ($spouse ? ' and <strong>' . $spouse->display_first_name . $this->getAsterisk($spouse) . '</strong>' : '') . '</span><br>
' . ($kids_list ? '<span style="font-size: 112%; font-weight: bold;"><em>' . $kids_list . '</span></em>' . '<br>' : '') . '
' . ($address ? Address::format($address) . '<br>' : '') . '
' . ($mailing ? '<span style="font-size: 80%; font-weight: bold;">Mailing:</span><br>' . Address::format($mailing) . '<br>' : '') . '
' . ($family_phone ? 'Home: ' . Phone::format($family_phone->number, '-') . '<br>' : '') . '
' . ($head_phone ? $head->display_first_name . ': ' . Phone::format($head_phone->number, '-') . '<br>' : '') . '
' . (($spouse && $spouse_phone) ? $spouse->display_first_name . ': ' . Phone::format($spouse_phone->number, '-') . '<br>' : '') . '
' . ($head_email ? $head->display_first_name . ': ' . $head_email?->email . '<br>' : '') . '
' . (($spouse && $spouse_email) ? $spouse->display_first_name . ': ' . $spouse_email?->email : '');

            $cell_content .= '</td></tr>';
        }
        $cell_content .= '</table>';

        $this->pdf->writeHTML($cell_content);
    }

    private function getAsterisk($user)
    {
        return ((!$user->is_baptized && $this->asterisk_unbaptized) || ($user->is_baptized && $this->asterisk_baptized)) ? '*' : null;
    }

    public function indicateBaptismNotChecked($value = false)
    {
        $this->asterisk_unbaptized = $value;

        return $this;
    }

    public function indicateBaptismIsChecked($value = false)
    {
        $this->asterisk_baptized = $value;

        return $this;
    }
}