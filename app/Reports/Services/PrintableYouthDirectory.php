<?php

namespace App\Reports\Services;


use App\Users\Address;
use App\Users\Phone;
use TCPDF;

class PrintableYouthDirectory
{
    private $users;
    private $pdf;
    private $asterisk_unbaptized = false;
    private $asterisk_baptized   = false;

    const FAMILIES_PER_SECTION = 18;

    const DEFAULT_FONT_SIZE = 9;

//    const DEFAULT_ROW_POSITION = 12;
//    const DEFAULT_COL_POSITION = 14;
//    const DEFAULT_ROW_HEIGHT   = 64;
//    const DEFAULT_COL_WIDTH    = 64;
//    const CELL_WIDTH           = 60;
//    const CELL_HEIGHT          = 60;
    const DEFAULT_ROW_POSITION = 8;
    const DEFAULT_COL_POSITION = 3;
    const DEFAULT_ROW_HEIGHT   = 33;
    const DEFAULT_COL_WIDTH    = 45;
    const CELL_WIDTH           = 45;
    const CELL_HEIGHT          = 33;
    const CELL_BORDER          = 0;

    public function withUsers($users)
    {
        $this->users = $users;

        return $this;
    }

    public function createPDF($filename)
    {
//        $this->pdf = new TCPDF('P', PDF_UNIT, 'LETTER', true, 'UTF-8', false);
        $this->pdf = new TCPDF('P', 'mm', [140, 216], true, 'UTF-8', false);

        $this->pdf->SetMargins(0, 0, -1, false);
//        $this->pdf->setPageOrientation('P', false, 0);

        $sections = $this->createSections();

        $this->pdf->SetCreator('Lightpost');
        $this->pdf->SetSubject('Member Directory');
        $this->pdf->SetKeywords('');
        $this->pdf->setPrintHeader(false);
        $this->pdf->setPrintFooter(false);
//        $this->pdf->SetTopMargin(5);
        $this->pdf->SetFont('helvetica', '', self::DEFAULT_FONT_SIZE);
        $this->pdf->SetAutoPageBreak(false);

        foreach ($sections as $section) {
            $this->pdf->AddPage('P');

            $this->renderSection($section);
        }

        // close and output PDF document
        $this->pdf->Output($filename, 'D');
    }

    private function createSections()
    {
        $sections = [];

        // figure out how many sections we have
        $num_sections = ceil(count($this->users) / self::FAMILIES_PER_SECTION);

        $section = 1;
        $i       = 1;

        foreach ($this->users as $family) {
            $sections[$section][] = $family;

            // calculate the next section after this one is full
            if ($i % self::FAMILIES_PER_SECTION == 0) {
                $section++;
            }

            $i++;
        }

        return $sections;
    }

    private function renderSection($contents)
    {
        $x_space = 0;
        $y_space = 0;

        foreach ($contents as $family) {
            $second       = null;
            $second_phone = null;
            $head_phone   = null;
            $address      = null;
            $head_email   = null;
            $second_email = null;

            $head       = $family;
            $head_phone = $head->getBestPhone();

            $address = $head->getFamilyAddress();
//            $mailing = $head->getMailingAddress();
//            if ($address->id === $mailing->id) {
//                $mailing = null;
//            }

            $cell_content = '<span style="font-size: 120%; font-weight: bold;">' . $family->last_name . '</span><br>
<span style="font-size: 108%; font-weight: medium;">' . $head->display_first_name . $this->getAsterisk($head) . '</span><br>
' . ($head_phone ? '<strong>' . Phone::format($head_phone->number, '-') . '</strong><br>' : '') . '
' . ($address ? Address::format($address) . '<br>' : '') . '
' . ($head_phone ? $head_phone->type . ': ' . Phone::format($head_phone->number, '-') . '<br>' : '');

            // writeHTMLCell($w, $h, $x, $y, $html='', $border=0, $ln=0, $fill=false, $reseth=true, $align='', $autopadding=true)
            $this->pdf->writeHTMLCell(self::CELL_WIDTH, self::CELL_HEIGHT, self::DEFAULT_COL_POSITION + ($x_space * self::DEFAULT_COL_WIDTH), self::DEFAULT_ROW_POSITION + ($y_space * self::DEFAULT_ROW_HEIGHT), $cell_content, self::CELL_BORDER);

            $x_space++;

            if ($x_space % 3 == 0) {
                $y_space++;
                $x_space = 0;
            }
        }
    }

    private function getAsterisk($user)
    {
        return ((!$user->is_baptized && $this->asterisk_unbaptized) || ($user->is_baptized && $this->asterisk_baptized)) ? '*' : null;
    }

    public function indicateBaptismIsChecked($value = false)
    {
        $this->asterisk_baptized = $value;

        return $this;
    }

    public function indicateBaptismNotChecked($value = false)
    {
        $this->asterisk_unbaptized = $value;

        return $this;
    }
}