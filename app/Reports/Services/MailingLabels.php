<?php

namespace App\Reports\Services;


use App\Users\Address;
use TCPDF;

class MailingLabels
{
    private $users;
    private $show_family_name_only = false;
    private $offset                = 0;

    const DEFAULT_ROW_POSITION = 14.3;
    const DEFAULT_COL_POSITION = 5.8;
    const DEFAULT_ROW_HEIGHT   = 25.6;
    const DEFAULT_COL_WIDTH    = 70.8;
    const CELL_WIDTH           = 70.8;
    const CELL_HEIGHT          = 25.5;

    public function withUsers($users)
    {
        $this->users = $users;

        return $this;
    }

    public function setOffset($offset)
    {
        $this->offset = $offset;

        return $this;
    }

    public function showFamilyNameOnly($value)
    {
        $this->show_family_name_only = $value;

        return $this;
    }

    public function createPDF($filename)
    {
        for ($i = 0; $i < $this->offset; $i++) {
            $this->users->prepend(null);
        }

        $pdf = new TCPDF('P', PDF_UNIT, 'LETTER', true, 'UTF-8', false);

        $pdf->SetMargins(0, 0, -1, true);
        $pdf->setPageOrientation('P', false, 0);

        $pdf->SetCreator('Lightpost');
        $pdf->SetSubject('Mailing Labels');
        $pdf->SetKeywords('');
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        $pdf->SetFont('helvetica', '', 10.5);

        foreach ($this->users->chunk(30) as $current_users) {
            $pdf->AddPage('P');

            $current_user_count = 1;
            $col_position       = self::DEFAULT_COL_POSITION;
            $row_position       = self::DEFAULT_ROW_POSITION;

            foreach ($current_users as $user) {
                $cell = null;

                if (!$user) {
                    $cell = '';
                } else {
                    $address = $user->getMailingAddress();

                    if ($this->show_family_name_only) {
                        $cell = '<span style="font-weight: bold;">' . $user->last_name . ' Family</span>';
                    } else {
                        $cell = '<span style="font-weight: bold;">' . $user->display_first_name . ' ' . $user->last_name . '</span>';
                    }

                    $cell .= '<br>' . ($address ? Address::format($address) : null);
                }
                $pdf->writeHTMLCell(self::CELL_WIDTH, self::CELL_HEIGHT, $col_position, $row_position, $cell, 0, 0, false, true, 'L');

                $row_position += SELF::DEFAULT_ROW_HEIGHT;

                if ($current_user_count % 10 == 0) {
                    $col_position += SELF::DEFAULT_COL_WIDTH;
                    $row_position = self::DEFAULT_ROW_POSITION;
                }

                $current_user_count++;
            }
        }

        // close and output PDF document
        $pdf->Output($filename, 'D');
    }
}