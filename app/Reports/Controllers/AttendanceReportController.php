<?php

namespace App\Reports\Controllers;

use App\Attendance\Attendance;
use App\Attendance\AttendanceType;
use App\Base\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class AttendanceReportController extends Controller
{
    public function index()
    {
        $attendance_dates = Attendance::visibleTo(Auth::user())
            ->select('date_attendance')
            ->where('date_attendance', '>', Carbon::now()->subMonths(5))
            ->orderBy('date_attendance', 'asc')
            ->distinct()
            ->get()->pluck('date_attendance');

        $attendance_types = Attendance::visibleTo(Auth::user())
            ->select('user_attendance_type_id')
            ->where('date_attendance', '>', Carbon::now()->subMonths(5))
            ->distinct()
            ->get()->pluck('user_attendance_type_id');

        $chart_data = [];

        // Header
        $header[] = 'Date';
        foreach ($attendance_types as $type_id) {
            $header[] = AttendanceType::find($type_id)->name;
        }
        $chart_data[] = $header;

        foreach ($attendance_dates as $date) {
            $row   = [];
            $row[] = $date->format('M-d');

            foreach ($attendance_types as $type_id) {
                $count = Attendance::visibleTo(Auth::user())
                    ->where('date_attendance', $date->format('Y-m-d'))
                    ->where('user_attendance_type_id', $type_id)
                    ->count();

                $row[] = $count ?: null;
            }

            $chart_data[] = $row;
        }

        $chartsd_data       = [];
        $google2_chart_data = [];

        foreach ($attendance_types as $type) {
            foreach ($attendance_dates as $date) {
                $count = Attendance::visibleTo(Auth::user())
                    ->where('date_attendance', $date->format('Y-m-d'))
                    ->where('user_attendance_type_id', $type)
                    ->count();

                if ($count) {

                    $row = $count ?: null;

                    $chartsd_data[$type][]       = $row;
                    $google2_chart_data[$type][] = [$date->format('M-d'), $row];
                }
            }
        }

        return view('admin.reports.attendance.index')
            ->with('chart_data', $chart_data)
            ->with('chartsd_data', $chartsd_data)
            ->with('google2_chart_data', $google2_chart_data);
    }
}
