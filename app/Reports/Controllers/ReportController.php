<?php

namespace App\Reports\Controllers;

use App\Base\Http\Controllers\Controller;

class ReportController extends Controller
{
    public function index()
    {
        return view('admin.reports.index');
    }

    public function missingPersons()
    {
        return view('admin.reports.missing-persons-report');
    }

    public function attendance()
    {
        return view('admin.reports.attendance-report');
    }

    public function birthday()
    {
        return view('admin.reports.birthday-report')
            ->with('type', 'birthdays');
    }

    public function anniversary()
    {
        return view('admin.reports.birthday-report')
            ->with('type', 'anniversaries');
    }

    public function baptismBirthday()
    {
        return view('admin.reports.birthday-report')
            ->with('type', 'baptism_birthdays');
    }

    public function mailingLabels()
    {
        return view('admin.reports.mailing-labels');
    }

    public function directory()
    {
        return view('admin.reports.printable-directory');
    }

    public function youthDirectory()
    {
        return view('admin.reports.printable-youth-directory');
    }

    public function photoDirectory()
    {
        return view('admin.reports.printable-photo-directory');
    }
}
