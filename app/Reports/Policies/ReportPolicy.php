<?php

namespace App\Reports\Policies;

use App\Users\User;

class ReportPolicy
{
    public function before($user, $ability)
    {
        if ($user->isSuper()) {
            return true;
        }

        if (!$user->account->getSetting('feature.reports')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('reports.index');
    }

    public function view(User $user)
    {
        return $user->hasPermission('reports.index');
    }
}