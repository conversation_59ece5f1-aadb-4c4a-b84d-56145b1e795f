<?php

namespace App\Announcements\Services;

use App\AccountFiles\AccountFile;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class UpdateAnnouncementFile
{
    protected $account       = null;
    protected $account_file  = null;
    protected $uploaded_file = null;
    protected $file_title    = null;

    public function __construct(AccountFile $account_file)
    {
        $this->account_file = $account_file;
    }

    public function update($attributes): AccountFile
    {
        $this->account_file->fill($attributes);

        if ($this->uploaded_file !== null) {
            $this->saveFile();
        }

        $this->account_file->save();

        return $this->account_file;
    }

    /**
     * @param UploadedFile $uploaded_file From request()->file() on the frontend
     * @param              $file_title
     *
     * @return UpdateAnnouncementFile
     * @throws \Exception
     */
    public function withFile(UploadedFile $uploaded_file, $file_title = null)
    {
        if ($uploaded_file->isValid()) {

            $this->uploaded_file = $uploaded_file;
            $this->file_title    = $file_title;

        } else {
            throw new \Exception('File was invalid.');
        }

        return $this;
    }

    private function saveFile()
    {
        // First delete our existing file
        if ($this->account_file->file_name) {
            if ($this->account_file->deleteFile()) {
                // $folder    = Config::get('app.user_image_file_path');
                $extension = '.' . $this->uploaded_file->getClientOriginalExtension();

                $new_file_name                   = $this->account_file->account_id . '--' . Str::random(6) . '--' . $this->uploaded_file->getClientOriginalName();
                $new_file_name_without_extension = str_replace($extension, '', $new_file_name);

                // Original -- saved as a JPG
                if (!$this->uploaded_file->storeAs($this->account_file->account_id, $new_file_name, 'account-files')) {
                    throw new \Exception('Could not write file to cloud server. The database entry has been saved though.');
                }

                $this->account_file->fill([
                    'title'              => $this->file_title ?? $this->account_file->title,
                    'url_title'          => \Illuminate\Support\Str::slug($this->file_title ?? $this->account_file->title, '-'),
                    'storage_service'    => 'do-spaces',
                    'data_separator'     => '--',
                    'file_original_name' => $this->uploaded_file->getClientOriginalName(),
                    'file_size'          => $this->uploaded_file->getSize(),
                    'file_folder'        => $this->account_file->account_id,
                    'file_id'            => null,
                    'file_name'          => $new_file_name_without_extension,
                    'file_extension'     => $this->uploaded_file->getClientOriginalExtension(),
                    'file_type'          => $this->uploaded_file->getClientMimeType(),
                    'file_sha1'          => sha1(file_get_contents($this->uploaded_file->getRealPath())),
                ]);

                $this->account_file->save();
            }
        }
    }
}
