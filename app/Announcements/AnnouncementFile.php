<?php

namespace App\Announcements;

use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class AnnouncementFile extends Model
{
    use SoftDeletes;

    protected $table = 'announcement_files';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'announcement_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'title',
        'url_title',
        'type',
        'storage_service',
        'file_original_name',
        'file_size',
        'data_separator',
        'file_folder',
        'file_id',
        'file_name',
        'file_extension',
        'file_type',
        'file_sha1',
        'sort_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function getVisibleFilesQuery($user)
    {
        return $this->visibleTo($user)
            ->notExpired()
            ->orderBy('sort_id');
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($query2) {
            $query2->whereNull('expires_at')
                ->orWhere('expires_at', '>=', now());
        });
    }

    public function getUrl($size = null)
    {
        if (config('digital_ocean.account_files.cdn_url')) {
            return config('digital_ocean.account_files.cdn_url') . '/' . $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
        } else {
            return 'https://' . config('digital_ocean.account_files.bucket') . '.' . config('digital_ocean.sermon_files.region') . '.digitaloceanspaces.com/' . $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension;
        }
    }

    public function getTempUrl($minutes = 10)
    {
        return Storage::disk('account-files')
            ->temporaryUrl(
                $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension,
                now()->addMinutes($minutes)
            );
    }

    public function deleteFile()
    {
        return Storage::disk('account-files')->delete($this->file_folder . '/' . $this->file_name . '.' . $this->file_extension);
    }
}
