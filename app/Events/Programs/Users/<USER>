<?php

namespace App\Events\Programs\Users;

use App\Programs\ProgramUserCheckin;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProgramUserCheckinBroadcast implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $program_user_checkin;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(ProgramUserCheckin $program_user_checkin)
    {
        $this->program_user_checkin = $program_user_checkin;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel($this->program_user_checkin->account_id . '.programs.' . $this->program_user_checkin->program_id . '.checkins');
    }

    public function broadcastAs()
    {
        return 'user.checkin';
    }

    public function broadcastWith()
    {
        $program_user_checkin = $this->program_user_checkin->load([
            'createdByUser:id,first_name,last_name',
        ]);

        return [
            'id'                 => $program_user_checkin->id,
            'program_user_id'    => $program_user_checkin->program_user_id,
            'checkin_at'         => $program_user_checkin->checkin_at,
            'checkout_at'        => $program_user_checkin->checkout_at ?: null,
            //            'checkin_by_user_id'  => $program_user_checkin->checkin_by_user_id,
            //            'checkout_by_user_id' => $program_user_checkin->checkout_by_user_id,
            'created_by_user_id' => $program_user_checkin->created_by_user_id,
            'created_by_user'    => optional($program_user_checkin->createdByUser)->only(['id', 'first_name', 'last_name']),
        ];
    }
}
