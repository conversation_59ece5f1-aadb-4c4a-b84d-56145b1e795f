<?php

namespace App\Events\Groups;

use App\Users\Group;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GroupRead implements ShouldQueue
{
    use Dispatchable, SerializesModels;

    public $group;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Group $group)
    {
        $this->group = $group;
    }

    public function broadcastAs()
    {
        return 'group.read';
    }

    public function broadcastWith()
    {
        return [
            'user_group_id'      => $this->post->user_group_id,
            'user_group_post_id' => $this->post->user_group_post_id,
            'created_at'         => $this->post->created_at,
            'name'               => optional($this->post->creator)->name,
        ];
    }
}
