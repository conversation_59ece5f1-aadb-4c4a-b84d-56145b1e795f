<?php

namespace App\Events\Groups;

use App\Groups\Comment;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CommentCreatedBroadcast implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $comment;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Comment $comment)
    {
        $this->comment = $comment;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel($this->comment->account_id . '.groups.' . $this->comment->user_group_id . '.posts.comments');
    }

    public function broadcastAs()
    {
        return 'comment.created';
    }

    public function broadcastWith()
    {
        return [
            'user_group_post_id' => $this->comment->user_group_post_id,
            'created_at'         => $this->comment->created_at,
            'content'            => $this->comment->content,
            'name'               => optional($this->comment->creator)->name,
            'comment_count'      => $this->comment->post->comments()->count(),
        ];
    }
}
