<?php

namespace App\Api\V1\Controllers\Calendars;

use App\Base\Http\Controllers\Controller;
use App\Calendars\Calendar;
use App\Calendars\Services\CalendarFeed;
use App\Users\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CalendarController extends Controller
{
    public function publicUserFeed($token, $feed_name)
    {
        try {
            $token_user = User::decodePublicToken($token);
        } catch (\Exception $e) {
            abort(404);
        }

        if (!$token_user) {
            abort(404);
        }

        $service = (new CalendarFeed())->forUser($token_user);

        return response($service->getiCalFeed())
            ->withHeaders($service->getHeadersArray());
    }

    public function publicCalendarFeed($calendar, $token, $feed_name)
    {
        try {
            $token_user = User::decodePublicToken($token);
        } catch (\Exception $e) {
            Log::error($e);
            abort(404);
        }

        if (!$token_user) {
            abort(404);
        }

        try {
            $calendar = Calendar::visibleTo($token_user)->withUlid($calendar)->firstOrFail();
        } catch (\Exception $e) {
            Log::error($e);
            abort(404);
        }

        $service = (new CalendarFeed())
            ->forUser($token_user)
            ->forCalendar($calendar)
            ->setFileName(Str::kebab($calendar->name) . '.ics');

        return response($service->getiCalFeed())
            ->withHeaders($service->getHeadersArray());
    }
}
