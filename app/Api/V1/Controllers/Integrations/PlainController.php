<?php

namespace App\Api\V1\Controllers\Integrations;

use App\Base\Http\Controllers\Controller;
use App\Users\Email;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Log;

class PlainController extends Controller
{
    public function customerDetails(Request $request)
    {
        if (!$request->hasHeader('token') || (string)$request->header('token') != config('integrations.plain.integration_token')) {
            Log::error('Unauthorized request to Plain customer card endpoint.', [
                'request' => $request->all(),
            ]);

            return abort(401);
        }

        $user_email = Email::where('email', 'like', strtolower($request->input('customer.email')))
            ->whereNull('deleted_at')
            ->first();

        if (!$user_email) {
            Log::notice('Plain customer card endpoint: user not found.', ['request' => $request->all()]);
            return abort(404);
        }

        return response()->json([
            'cards' => [
                [
                    'key'               => 'customer-details',
                    'timeToLiveSeconds' => null,
                    'components'        => [
                        ['componentSpacer' => ['spacerSize' => 'S']],

                        // Account name
                        [
                            'componentRow' => [
                                'rowMainContent'  => [
                                    [
                                        'componentText' => [
                                            'text'      => 'Account name',
                                            'textColor' => 'MUTED',
                                        ],
                                    ],
                                ],
                                'rowAsideContent' => [
                                    [
                                        'componentText' => [
                                            'text' => $user_email->user->account->name,
                                        ],
                                    ],
                                ],
                            ],
                        ],

                        ['componentSpacer' => ['spacerSize' => 'S']],

                        // Registered at
                        [
                            'componentRow' => [
                                'rowMainContent'  => [
                                    [
                                        'componentText' => [
                                            'text'      => 'Registered at',
                                            'textColor' => 'MUTED',
                                        ],
                                    ],
                                ],
                                'rowAsideContent' => [
                                    [
                                        'componentText' => [
                                            'text' => $user_email->user->created_at->format('n/j/Y, g:i A'),
                                        ],
                                    ],
                                ],
                            ],
                        ],

                        ['componentSpacer' => ['spacerSize' => 'M']],

                        // Account created at
                        [
                            'componentRow' => [
                                'rowMainContent'  => [
                                    [
                                        'componentText' => [
                                            'text'      => 'Account created',
                                            'textColor' => 'MUTED',
                                        ],
                                    ],
                                ],
                                'rowAsideContent' => [
                                    [
                                        'componentText' => [
                                            'text' => $user_email->user->account->created_at->format('n/j/Y, g:i A'),
                                        ],
                                    ],
                                ],
                            ],
                        ],

                        ['componentSpacer' => ['spacerSize' => 'M']],

                        // Is Admin
                        [
                            'componentRow' => [
                                'rowMainContent'  => [
                                    [
                                        'componentText' => [
                                            'text'      => 'Admin privileges',
                                            'textColor' => 'MUTED',
                                        ],
                                    ],
                                ],
                                'rowAsideContent' => [
                                    [
                                        'componentText' => [
                                            'text' => $user_email->user->roles()->where('name', 'LIKE', '%admin%')->exists() ? 'Yes 🔐' : 'No',
                                        ],
                                    ],
                                ],
                            ],
                        ],

                        ['componentSpacer' => ['spacerSize' => 'M']],

                        // Is Member
                        [
                            'componentRow' => [
                                'rowMainContent'  => [
                                    [
                                        'componentText' => [
                                            'text'      => 'Membership',
                                            'textColor' => 'MUTED',
                                        ],
                                    ],
                                ],
                                'rowAsideContent' => [
                                    [
                                        'componentText' => [
                                            'text' => $user_email->user->isMember() ? 'Active ✅' : 'Inactive ❌',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ]);
    }
}
