<?php

namespace App\Api\V1\Controllers\Integrations;

use App\Base\Http\Controllers\Controller;
use App\Users\Email;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Log;

class GrooveController extends Controller
{
    public function profile(Request $request)
    {
        if (!$request->has('api_token') || $request->input('api_token') !== config('integrations.groove.profile_token')) {
            Log::error('Unauthorized request to Groove profile endpoint', ['request' => $request->all()]);
            return abort(401);
        }

        $user_email = Email::where('email', 'like', strtolower($request->input('email')))->first();

        // Try Church Directory if we find nothing in Lightpost.
        // This is because Groove does not allow different endpoints for different channels.
        if (!$user_email) {
            $response = Http::get('https://' . config('services.church_directory.domains.api') . '/integrations/groove/profile', [
                'api_token' => config('integrations.groove.profile_token'),
                'email'     => $request->input('email'),
            ]);

            if ($response->status() === 200) {
                return $response->json();
            }
        }

        if (!$user_email) {
            Log::notice('Groove profile endpoint: user not found.', ['request' => $request->all()]);
            return abort(404);
        }

        return response()->json([
            'account_id'               => $user_email->user->account->id,
            'account_active'           => $user_email->user->account->is_active,
            'account_name'             => $user_email->user->account->name,
            'account_created_at'       => $user_email->user->account->created_at->format('Y-m-d'),
            'account_created_at_human' => $user_email->user->account->created_at->diffForHumans(now(), true),
            'user_created_at'          => $user_email->user->created_at->format('Y-m-d'),
            'user_created_at_human'    => $user_email->user->created_at->diffForHumans(now(), true),
            'user_id'                  => $user_email->user->id,
            'user_name'                => $user_email->user->name,
            'user_first_name'          => $user_email->user->first_name,
            'user_last_name'           => $user_email->user->last_name,
            'is_member'                => $user_email->user->isMember(),
            'is_admin'                 => $user_email->user->roles()->where('name', 'LIKE', '%admin%')->exists(),
        ]);
    }
}
