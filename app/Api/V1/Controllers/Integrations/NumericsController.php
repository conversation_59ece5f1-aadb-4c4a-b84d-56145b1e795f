<?php

namespace App\Api\V1\Controllers\Integrations;

use App\Base\Http\Controllers\Controller;
use App\Messages\MessageHistory;
use App\Users\NotificationTarget;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Input;

class NumericsController extends Controller
{
    public function __construct(Request $request)
    {
        if (!$request->header('Authorization') || empty($request->header('Authorization')) || $request->header('Authorization') !== 'Bearer ' . config('integrations.numerics.api_token')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
    }

    public function accountCount(Request $request)
    {
        $result = DB::query()
            ->selectRaw('(SELECT count(*) from accounts where is_active = 1 and is_suspended = 0) as current_active_accounts, (select count(*) from accounts where is_active = 1 and is_suspended = 0 and created_at < NOW() - INTERVAL 1 MONTH) as last_month_count')
            ->get();

        return response()->json([
            'postfix' => 'Active Accounts',
            'data'    => [
                ['value' => $result[0]->current_active_accounts],
                ['value' => $result[0]->last_month_count],
            ],
        ]);
    }

    public function membersCount(Request $request)
    {
        $result = DB::select(
            'SELECT (select COUNT(*) from `users` where (exists (select * from `user_roles` inner join `user_to_role` on `user_roles`.`id` = `user_to_role`.`user_role_id` where `users`.`id` = `user_to_role`.`user_id` and `indicates_membership` = 1 and `user_roles`.`deleted_at` is null) or exists (select * from `user_groups` inner join `user_to_group` on `user_groups`.`id` = `user_to_group`.`user_group_id` where `users`.`id` = `user_to_group`.`user_id` and `indicates_membership` = 1 and `user_groups`.`deleted_at` is null)) and `users`.`deleted_at` is null) as today_count,
(select COUNT(*) from `users` where (exists (select * from `user_roles` inner join `user_to_role` on `user_roles`.`id` = `user_to_role`.`user_role_id` where `users`.`id` = `user_to_role`.`user_id` and `indicates_membership` = 1 and `user_roles`.`deleted_at` is null) or exists (select * from `user_groups` inner join `user_to_group` on `user_groups`.`id` = `user_to_group`.`user_group_id` where `users`.`id` = `user_to_group`.`user_id` and `indicates_membership` = 1 and `user_groups`.`deleted_at` is null)) and `users`.`deleted_at` is null AND created_at < NOW() - INTERVAL 1 MONTH) as last_month_count'
        );

        return response()->json([
            'postfix' => 'Members',
            'data'    => [
                ['value' => $result[0]->today_count],
                ['value' => $result[0]->last_month_count],
            ],
        ]);
    }

    public function visitorsCount(Request $request)
    {
        $result = DB::select(
            'SELECT (select COUNT(*) from `users` where (exists (select * from `user_groups` inner join `user_to_group` on `user_groups`.`id` = `user_to_group`.`user_group_id` where `users`.`id` = `user_to_group`.`user_id` and `indicates_visitor` = 1 and `user_groups`.`deleted_at` is null)) and `users`.`deleted_at` is null) as today_count,
(select COUNT(*) from `users` where (exists (select * from `user_groups` inner join `user_to_group` on `user_groups`.`id` = `user_to_group`.`user_group_id` where `users`.`id` = `user_to_group`.`user_id` and `indicates_visitor` = 1 and `user_groups`.`deleted_at` is null)) and `users`.`deleted_at` is null AND created_at < NOW() - INTERVAL 1 MONTH) as last_month_count'
        );

        return response()->json([
            'postfix' => 'Visitors',
            'data'    => [
                ['value' => $result[0]->today_count],
                ['value' => $result[0]->last_month_count],
            ],
        ]);
    }

    public function usersCount(Request $request)
    {
        $result = DB::select(
            'SELECT (select count(*) from users) as today_count, (select count(*) from users WHERE created_at < NOW() - INTERVAL 1 MONTH) as last_month_count'
        );

        return response()->json([
            'postfix' => 'Users',
            'data'    => [
                ['value' => $result[0]->today_count],
                ['value' => $result[0]->last_month_count],
            ],
        ]);
    }

    public function notificationsCount(Request $request)
    {
        $result = DB::select(
            "SELECT (select count(*) from user_notifications WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW(), '+00:00', '-06:00'))
 AND YEAR(created_at) = YEAR(NOW())) as today_count,
 (select count(*) from user_notifications WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW() - INTERVAL 1 DAY, '+00:00', '-06:00'))
 AND YEAR(created_at) = YEAR(NOW() - INTERVAL 1 DAY)) as yesterday_count"
        );

        return response()->json([
            'postfix' => 'Notifications',
            'data'    => [
                ['value' => $result[0]->today_count],
                ['value' => $result[0]->yesterday_count],
            ],
        ]);
    }

    public function groupPostsCount(Request $request)
    {
        $result = DB::select(
            "SELECT (select count(*) from user_group_posts WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW(), '+00:00', '-06:00'))
 AND YEAR(created_at) = YEAR(NOW())) as today_count,
 (select count(*) from user_group_posts WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW() - INTERVAL 1 DAY, '+00:00', '-06:00'))
 AND YEAR(created_at) = YEAR(NOW() - INTERVAL 1 DAY)) as yesterday_count"
        );

        return response()->json([
            'postfix' => 'Group Posts',
            'data'    => [
                ['value' => $result[0]->today_count],
                ['value' => $result[0]->yesterday_count],
            ],
        ]);
    }

    public function groupPostCommentsCount(Request $request)
    {
        $result = DB::select(
            "SELECT (select count(*) from user_group_post_comments WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW(), '+00:00', '-06:00'))
 AND YEAR(created_at) = YEAR(NOW())) as today_count,
(select count(*) as yesterday from user_group_post_comments WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW() - INTERVAL 1 DAY, '+00:00', '-06:00'))
 AND YEAR(created_at) = YEAR(NOW() - INTERVAL 1 DAY)) as yesterday_count"
        );

        return response()->json([
            'postfix' => 'Group Post Comments',
            'data'    => [
                ['value' => $result[0]->today_count],
                ['value' => $result[0]->yesterday_count],
            ],
        ]);
    }

    public function messageTypesUsage(Request $request)
    {
        $email_count = MessageHistory::where('created_at', '>', now()->subDays(14))
            ->whereHas('message', function ($query) {
                $query->where('message_type_id', 1);
            })
            ->selectRaw('count(*) as count')
            ->first();
        $sms_count   = MessageHistory::where('created_at', '>', now()->subDays(14))
            ->whereHas('message', function ($query) {
                $query->where('message_type_id', 2);
            })
            ->selectRaw('count(*) as count')
            ->first();
        $voice_count = MessageHistory::where('created_at', '>', now()->subDays(14))
            ->whereHas('message', function ($query) {
                $query->where('message_type_id', 4);
            })
            ->selectRaw('count(*) as count')
            ->first();

        $mobile_notifications_count = NotificationTarget::where('created_at', '>', now()->subDays(14))->selectRaw('count(*) as count')->first();
//        $post_count    = Post::where('created_at', '>', now()->subDays(14))->selectRaw('count(*) as count')->first();
//        $comment_count = Comment::where('created_at', '>', now()->subDays(14))->selectRaw('count(*) as count')->first();

        return response()->json([
            'postfix' => 'Message Types Usage',
            'data'    => [
                ['name' => 'E-mail', 'value' => $email_count->count],
                ['name' => 'SMS', 'value' => $sms_count->count],
                ['name' => 'Voice Calls', 'value' => $voice_count->count],
                ['name' => 'Mobile Notifications', 'value' => $mobile_notifications_count->count],
            ],
        ]);
    }

    public function dailyUsageGroupPostComments()
    {
        $result = DB::select(
            'SELECT sub1.sdate, COUNT(user_group_post_comments.id) as count
FROM
(
    SELECT DATE_FORMAT(DATE_SUB(NOW(), INTERVAL units.i + tens.i * 10 + hundreds.i * 100 DAY), "%Y/%m/%d") as sdate 
    FROM (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)units
    CROSS JOIN (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)tens
    CROSS JOIN (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)hundreds
    WHERE DATE_SUB(NOW(), INTERVAL units.i + tens.i * 10 + hundreds.i * 100 DAY) BETWEEN DATE_SUB(NOW(), INTERVAL 1 MONTH) AND NOW()
) sub1
LEFT OUTER JOIN user_group_post_comments
ON (sub1.sdate = DATE(user_group_post_comments.created_at) AND user_group_post_comments.created_at >= DATE_SUB(NOW(), INTERVAL 5 WEEK))
GROUP BY sub1.sdate
ORDER BY sub1.sdate'
        );

        $result_array = [];
        foreach ($result as $row) {
            $result_array[] = [
                'date'  => $row->sdate,
                'value' => $row->count,
            ];
        }

        // Only show for the current month
        if (request()->get('limit') == 'current_month') {
            $limit = now()->format('d') <= 31 ? now()->format('d') : 30;
        } else {
            $limit = 30;
        }

        return response()->json([
            'postfix' => 'Group Post Comments',
            'data'    => array_reverse(array_slice(array_reverse($result_array), 0, $limit)),
        ]);
    }

    public function dailyUsageGroupPosts()
    {
        $result = DB::select(
            'SELECT sub1.sdate, COUNT(user_group_posts.id) as count
FROM
(
    SELECT DATE_FORMAT(DATE_SUB(NOW(), INTERVAL units.i + tens.i * 10 + hundreds.i * 100 DAY), "%Y/%m/%d") as sdate 
    FROM (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)units
    CROSS JOIN (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)tens
    CROSS JOIN (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)hundreds
    WHERE DATE_SUB(NOW(), INTERVAL units.i + tens.i * 10 + hundreds.i * 100 DAY) BETWEEN DATE_SUB(NOW(), INTERVAL 1 MONTH) AND NOW()
) sub1
LEFT OUTER JOIN user_group_posts
ON sub1.sdate = DATE_FORMAT(user_group_posts.created_at, "%Y/%m/%d")
GROUP BY sub1.sdate
ORDER BY sub1.sdate'
        );

        $result_array = [];
        foreach ($result as $row) {
            $result_array[] = [
                'date'  => $row->sdate,
                'value' => $row->count,
            ];
        }

        return response()->json([
            'postfix' => 'Group Posts',
            'data'    => $result_array,
        ]);
    }

    public function dailyUsageMessages()
    {
        $result = DB::select(
            'SELECT sub1.sdate, COUNT(message_history.id) as count
FROM
(
    SELECT DATE_FORMAT(DATE_SUB(NOW(), INTERVAL units.i + tens.i * 10 + hundreds.i * 100 DAY), "%Y/%m/%d") as sdate 
    FROM (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)units
    CROSS JOIN (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)tens
    CROSS JOIN (SELECT 0 i UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9)hundreds
    WHERE DATE_SUB(NOW(), INTERVAL units.i + tens.i * 10 + hundreds.i * 100 DAY) BETWEEN DATE_SUB(NOW(), INTERVAL 2 WEEK) AND NOW()
) sub1
LEFT OUTER JOIN message_history
ON (sub1.sdate = DATE(message_history.created_at) AND message_history.created_at > DATE_SUB(NOW(), INTERVAL 3 WEEK))
GROUP BY sub1.sdate
ORDER BY sub1.sdate'
        );

        $result_array = [];
        foreach ($result as $row) {
            $result_array[] = [
                'date'  => $row->sdate,
                'value' => $row->count,
            ];
        }

        return response()->json([
            'postfix' => 'Messages',
            'data'    => $result_array,
        ]);
    }

    public function dailyUsageUsersAdded()
    {
        $result = DB::select(
            "SELECT DATE_FORMAT(created_at, '%Y/%m/%d') as created_at_date, count(*) as count FROM users WHERE created_at BETWEEN NOW() - INTERVAL 30 DAY AND NOW() GROUP BY created_at_date ORDER BY created_at_date ASC"
        );

        $result_array = [];
        foreach ($result as $row) {
            $result_array[] = [
                'date'  => $row->created_at_date,
                'value' => $row->count,
            ];
        }

        return response()->json([
            'postfix' => 'Users Added',
            'data'    => $result_array,
        ]);
    }

    public function todayMessagesCount(Request $request)
    {
        $result = DB::select(
            "SELECT (select count(*) from message_history WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW(), '+00:00', '-06:00')) AND YEAR(created_at) = YEAR(NOW())) as today_count"
        );

        return response()->json([
            'postfix' => 'Messages Today',
            'data'    => ['value' => $result[0]->today_count],
        ]);
    }

    public function todayNotificationsCount(Request $request)
    {
        $result = DB::select(
            "SELECT (select count(*) from user_notifications WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW(), '+00:00', '-06:00')) AND YEAR(created_at) = YEAR(NOW())) as today_count"
        );

        return response()->json([
            'postfix' => 'Notifications Today',
            'data'    => ['value' => $result[0]->today_count],
        ]);
    }

    public function todayGroupPostsCount(Request $request)
    {
        $result = DB::select(
            "SELECT (select count(*) from user_group_posts WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW(), '+00:00', '-06:00')) AND YEAR(created_at) = YEAR(NOW())) as today_count"
        );

        return response()->json([
            'postfix' => 'Group Posts Today',
            'data'    => ['value' => $result[0]->today_count],
        ]);
    }

    public function todayGroupPostCommentsCount(Request $request)
    {
        $result = DB::select(
            "SELECT (select count(*) from user_group_post_comments WHERE DAYOFYEAR(created_at) = DAYOFYEAR(CONVERT_TZ(NOW(), '+00:00', '-06:00')) AND YEAR(created_at) = YEAR(NOW())) as today_count"
        );

        return response()->json([
            'postfix' => 'Group Post Comments Today',
            'data'    => ['value' => $result[0]->today_count],
        ]);
    }

    public function financeBilledThisMonth()
    {
        $result = DB::select(
            "SELECT (SELECT SUM(ROUND(amount_total / 100, 0)) FROM account_invoices WHERE YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())) as this_month, 
 (SELECT SUM(ROUND(amount_total / 100, 0)) as last_month FROM account_invoices WHERE YEAR(created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND MONTH(created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))) as last_month"
        );

        return response()->json([
            'postfix' => 'Rev',
            'data'    => [
                ['value' => $result[0]->this_month],
                ['value' => $result[0]->last_month],
            ],
        ]);
    }

    public function financeOutstandingThisMonth()
    {
        $result = DB::select(
            "SELECT (SELECT SUM(ROUND(amount_total / 100, 0)) FROM account_invoices WHERE  paid_at IS NULL AND YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())) as this_month"
        );

        return response()->json([
            'postfix' => 'Outstanding',
            'data'    => ['value' => (float)number_format($result[0]->this_month, 2)],
        ]);
    }
}
