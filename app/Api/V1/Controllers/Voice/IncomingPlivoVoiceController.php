<?php

namespace App\Api\V1\Controllers\Voice;

use App\Base\Http\Controllers\Controller;
use App\Jobs\ProcessSms;
use App\Jobs\SendSms;
use App\Messages\Message;
use App\Messages\MessageHandler;
use App\Messages\MessageHistory;
use App\Messages\Services\CreateMessage;
use App\Users\GroupSender;
use App\Users\Phone;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Plivo\XML\Response;

class IncomingPlivoVoiceController extends Controller
{
    /**
     * https://developers.messagebird.com/api/sms-messaging#receive-and-handle-inbound-sms
     *
     * 1.  Pull key information out.
     * 2.  Find our message Handler.
     * 3.  Find our message Sender.
     * 4.  Check that accounts match.
     * 5.  Check that we have not already received this message.
     * 6.
     * 7.  Create our Message in the database.
     * 8.  Update the last received from <PERSON><PERSON> field.
     * 9.  Extract attachments and save them.
     * 10. Dispatch the message to the queue.
     * 11. Return a success message.
     *
     * Example:  GET http://your-own.url/script?mid=*********&shortcode=1008&keyword=MESSAGEBIRD&originator=***********&operator=20401&message=This+is+an+incoming+message&receive_datetime=**************
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response|void
     * @throws \Exception
     */
    public function playTextToSpeech()
    {
        Log::info(print_r(request()->getContent(), true));

        # Generate a Speak XML with the details of the text to play on the call.
        $r = new Response();

        # Add Speak XML Tag with English text
        $body1   = 'Congratulations! You have successfully made a call with Pleevo.';
        $params1 = array(
            'language' => "en-US", # Language used to read out the text.
            'voice'    => "MAN" # The tone to be used for reading out the text.
        );
        $r->addSpeak($body1, $params1);

        # Add Speak XML Tag with English text
        $body2   = 'Félicitations! Vous avez effectué avec succès un appel avec Pleevo.';
        $params2 = array(
            'language' => "fr-FR", # Language used to read out the text.
            'voice'    => "WOMAN" # The tone to be used for reading out the text.
        );
        $r->addSpeak($body2, $params2);

        # Add Speak XML Tag with English text
        $body3   = '¡Enhorabuena! Usted ha realizado con éxito una llamada con Pleevo.';
        $params3 = array(
            'language' => "es-US", # Language used to read out the text.
            'voice'    => "MAN" # The tone to be used for reading out the text.
        );
        $r->addSpeak($body3, $params3);

        Header('Content-type: text/xml');

        echo($r->toXML());

        exit;

        // Pull key information out.
        $_to_phone                  = request()->get('To');
        $_from_phone                = request()->get('From');
        $_from_phone_without_prefix = ltrim($_from_phone, '1');
        $_body_text                 = request()->get('Text');
        $_message_id                = request()->get('MessageUUID');

        /**
         * DEPRECATED
         *
         * We no longer allow SMS messaging via sending to a text number.
         * We're now using Plivo's Powerpack feature to do text load balancing.
         *
         */

        Log::error('Attempt to send a text message to ' . $_to_phone . ' from ' . $_from_phone);
        Log::info('Message was: ' . $_body_text);

        return response('OK', 200);


        // Find our message Handler.
        $handler = MessageHandler::where('address', $_to_phone)
            ->where('message_type_id', 2)
            ->isActive()
            ->with('account')
            ->first();

        if (!$handler) {
            Log::error('No valid or active handler found for: ' . $_to_phone);
            Log::error(print_r(request()->getContent(), true));
            return abort(428);
        }

        Log::info('IncomingPlivoController:FoundHandler: ' . $handler->id);

        return response('OK', 200);


        // STOP HERE.

        // Find our message Sender.
        // If any group member is allowed to send messages, just get the User and check if they're from this group.
        if (boolval($handler->group->allow_all_members_to_send)) {
            Log::info('IncomingPlivoController -- Searching for a sender from the whole group. "allow_all_members_to_send"');
            $sender = User::where('account_id', $handler->account_id)
                ->whereHas('phones', function ($query) use ($_from_phone_without_prefix) {
                    $query->where('user_phones.number', 'like', '%' . $_from_phone_without_prefix);
                })->whereHas('groups', function ($query) use ($handler) {
                    $query->where('user_to_group.user_group_id', $handler->user_group_id);
                })->first();
        } else {
            Log::info('IncomingPlivoController -- Searching for a sender from the authorized senders list.');
            $sender = GroupSender::where('account_id', $handler->account_id)
                ->where('user_group_id', $handler->user_group_id)
                ->whereHas('user.phones', function ($query) use ($_from_phone_without_prefix) {
                    $query->where('user_phones.number', 'like', '%' . $_from_phone_without_prefix);
                })->first();

            if ($sender) {
                $sender = $sender->user;
            }
        }

        if (!$sender) {
//            if ($this->checkIfReplyMessage($handler)) {
//                // Return a success message.
//                return response('OK', 200);
//            }

            Log::error('No valid sender found for: ' . $_from_phone . ' -- searched: ' . $_from_phone_without_prefix);
            return response('OK', 200); // Stop repeated attempts.
        }

        Log::info('IncomingPlivoController:FoundSender: UserID: ' . $sender->id);

        // Check that accounts match.
        if ($handler->account_id !== $sender->account_id) {
            Log::error('Handler account does not match sender!');
            return abort(403);
        }

        // Check that we have not already received this message.
        if ($this->checkIfMessageExists($_message_id)) {
            Log::error('Message already exists in database! Receiving duplicate webhook requests. ' . $_message_id);

            return abort(208);
        }

        // Create our Message in the database.
        try {
            $newMessage = (new CreateMessage())
                ->fromUser($sender)
                ->forAccount($handler->account)
                ->forGroup($handler->group)
                ->withHandler($handler)
                ->withProvider($handler->messageProvider)
                ->withType($handler->messageType)
                ->fromAddress($_from_phone)
                ->toAddress($_to_phone)
                ->setContent($_body_text)
                ->create($_message_id);
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return abort(401);
        }

        Log::info('IncomingPlivoController:CreatedMessage: message_id: ' . $newMessage->id);

        // Update our last received from Handler date.
        $handler->last_received_at = Carbon::now();
        $handler->save();

        // Dispatch the message to the queue.
        try {
            $newMessage->status = 'processing';
            $newMessage->save();
            ProcessSms::dispatch($newMessage)->onQueue('sms');

            Log::info('IncomingPlivoController:DispatchedMessageToProcess: message_id:' . $newMessage->id);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());

            return abort(401);
        }

        // Return a success message.
        return response('OK', 200);
    }

    public function checkIfMessageExists($message_id)
    {
        return Message::where('provider_transaction_id', $message_id)->exists();
    }

    // Check if this is a reply from one of the group users, and route it back to the last person to send a message.
    public function checkIfReplyMessage($handler)
    {
        // Pull key information out.
        $_from_phone = request()->get('From');

        // First find the FROM phone in our database.
        $phone = Phone::where('number', 'like', $_from_phone)->first();

        $group_users_list = DB::table('user_to_group')->where('user_group_id', $handler->user_group_id)->get();

        $unauth_sender = User::where('account_id', $handler->account_id)
            ->whereIn('id', $group_users_list->pluck('user_id')->toArray())
            ->whereHas('user.phones', function ($query) use ($_from_phone) {
                $query->where('user_phones.number', $_from_phone);
            })->first();

        if (!$unauth_sender) {
            return false;
        }

        // We found the user from the group, now send the message to the last group user... add this MessageHistory to the original Message.
        $last_message = Message::where('message_handler_id', $handler->id)
            ->orderBy('created_at', 'desc')
            ->first();

        $charge = optional($handler->account->plan)->price_per_sms;

        // Create a new MessageHistory item and send the SMS.
        $mh_result = MessageHistory::create([
            'account_id'          => $handler->account_id,
            'user_id'             => optional($unauth_sender)->id,
            'user_phone_id'       => optional($phone)->id,
            'message_id'          => $this->message->id,
            'message_status_id'   => null,
            'provider_batch_id'   => null,
            'provider_message_id' => null,
            'charge'              => $charge,
        ]);

        $mh_result->setStatusCreateOnMissing('sent'); // This method saves the message_history.

        if ($mh_result) {
            SendSms::dispatch($mh_result);
        }

        return false;
    }
}
