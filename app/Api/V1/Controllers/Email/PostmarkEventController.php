<?php

namespace App\Api\V1\Controllers\Email;

use App\Base\Http\Controllers\Controller;
use App\Messages\MessageHistory;
use App\Users\Email;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PostmarkEventController extends Controller
{
    // https://postmarkapp.com/developer/webhooks/bounce-webhook
    public function handleBounce(Request $request)
    {
        $message_id            = $request->input('MessageID');
        $type                  = $request->input('Type');
        $name                  = $request->input('Name');
        $message_stream        = $request->input('MessageStream');
        $description           = $request->input('Description');
        $details               = $request->input('Details');
        $lp_message_history_id = $request->input('Metadata.LpMessageHistoryId');

        Log::info('PostmarkEventHandler:TypeReceived', [
            'type' => $type,
        ]);
        Log::info('PostmarkEventHandler:MessageId', [
            'message_id' => $message_id,
        ]);

        // Get our message
        $message_history = MessageHistory::where('created_at', '>=', Carbon::now()->subDays(10))
            ->where('id', $lp_message_history_id)
            ->where('provider_message_id', $message_id)
            ->first();

        // If we failed a message_history lookup.
        if (!$message_history) {
            Log::error('PostmarkEventHandler:CouldNotFindMessageHistory:TransmissionId', [
                'message_id'         => $message_id,
                'message_history_id' => $lp_message_history_id,
            ]);

            return response('OK', 200);
        }

        Log::info('PostmarkEventHandler:MessageHistoryFound', [
            'message_history' => $message_history,
        ]);

        $message_history->failed_reason = Str::substr($description, 0, 255);
        $message_history->setStatusCreateOnMissing($type, true); // This method saves the message_history.

        return response('OK', 200);
    }

    // https://postmarkapp.com/developer/webhooks/delivery-webhook
    public function handleDelivery(Request $request)
    {
        $message_id            = $request->input('MessageID');
        $record_type           = $request->input('RecordType');
        $message_stream        = $request->input('MessageStream');
        $details               = $request->input('Details');
        $delivered_at          = $request->input('DeliveredAt');
        $lp_message_history_id = $request->input('Metadata.LpMessageHistoryId');

        Log::info('PostmarkEventHandler:TypeReceived: Delivered');
        Log::info('PostmarkEventHandler:MessageId', [
            'message_id' => $message_id,
        ]);

        // Get our message
        $message_history = MessageHistory::where('created_at', '>=', Carbon::now()->subDays(10))
            ->where('id', $lp_message_history_id)
            ->where('provider_message_id', $message_id)
            ->first();

        // If we failed a message_history lookup.
        if (!$message_history) {
            Log::error('PostmarkEventHandler:CouldNotFindMessageHistory:TransmissionId', [
                'message_id'         => $message_id,
                'message_history_id' => $lp_message_history_id,
            ]);

            return response('OK', 200);
        }

        Log::info('PostmarkEventHandler:MessageHistoryFound', [
            'message_history_id' => optional($message_history)->id,
        ]);

        $message_history->setStatusCreateOnMissing('delivered'); // This method saves the message_history.

        return response('OK', 200);
    }

    // https://postmarkapp.com/developer/webhooks/spam-complaint-webhook
    public function handleSpamComplaint(Request $request)
    {
        $message_id   = $request->input('MessageID');
        $to_email     = $request->input('Email');
        $subject      = $request->input('Subject');
        $details      = $request->input('Details');
        $can_activate = $request->input('CanActivate');

        Log::info('PostmarkEventHandler:TypeReceived: Spam Complaint');
        Log::info('PostmarkEventHandler:MessageId', [
            'message_id' => $message_id,
        ]);

        // Find our email address in Lightpost
        $email = Email::where('email', 'like', $to_email)->first();

        // If we failed an email lookup.
        if (!$email) {
            Log::error('PostmarkEventHandler:handleSpamComplaint:CouldNotFindUserEmail:Email: ' . $to_email . ' | ' . $message_id);

            return response('OK', 200);
        }

        $email->reported_spam_complaint     = now();
        $email->spam_complaint_subject_line = $subject;

        $email->save();

        Log::info('PostmarkEventHandler:handleSpamComplaint', [
            'email' => $email,
        ]);

        return response('OK', 200);
    }

    // https://postmarkapp.com/developer/webhooks/subscription-change-webhook
    public function handleSubscriptionChange(Request $request)
    {
        $message_id            = $request->input('MessageID');
        $email                 = $request->input('Recipient');
        $record_type           = $request->input('RecordType');
        $suppress_sending      = $request->input('SuppressSending');
        $reason                = $request->input('SuppressionReason');
        $lp_message_history_id = $request->input('Metadata.LpMessageHistoryId');

        Log::info('PostmarkEventHandler:TypeReceived: SubscriptionChange');
        Log::info('PostmarkEventHandler:Email: ' . $email);

        // Get our message
        $message_history = MessageHistory::where('created_at', '>=', Carbon::now()->subDays(60))
            ->where('id', $lp_message_history_id)
            ->where('provider_message_id', $message_id)
            ->first();

        // Get our email.
        $user_email = Email::where('email', 'like', $email)->first();

        // If we failed a message_history lookup.
        if (!$user_email) {
            Log::error('PostmarkEventHandler:CouldNotFindUserEmail', [
                'email'      => $email,
                'message_id' => $message_id,
            ]);

            return response('OK', 200);
        }

        Log::info('PostmarkEventHandler:EmailFound', [
            'email_id' => optional($user_email)->id,
        ]);

        if ($suppress_sending) {
            $user_email->receives_group_emails = 0; // Block group emails
            // $user_email->allow_messages        = 0; // Block ALL emails
            $user_email->save();

            $message_history->setStatusCreateOnMissing('unsubscribe', true); // This method saves the message_history.

            Log::info('PostmarkEventHandler:SuppressEmail', [
                'email'    => $email,
                'email_id' => optional($user_email)->id,
            ]);
        } else {
            $user_email->receives_group_emails = 1; // Leave group emails as they were
            $user_email->allow_messages        = 1; // Unblock ALL emails
            $user_email->save();

            Log::info('PostmarkEventHandler:UnsuppressEmail', [
                'email'    => $email,
                'email_id' => optional($user_email)->id,
            ]);
        }

        return response('OK', 200);
    }
}
