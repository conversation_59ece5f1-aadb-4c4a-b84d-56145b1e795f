<?php

namespace App\Api\V1\Controllers\Email;

use App\Base\Http\Controllers\Controller;
use App\Messages\MessageHandler;
use App\Messages\Services\CreateMessage;
use App\Users\GroupSender;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class IncomingSparkpostController extends Controller
{
    /**
     * https://developers.sparkpost.com/api/relay-webhooks/
     *
     * @param Request $request
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response|void
     * @throws \Exception
     */
    public function inbound(Request $request)
    {
        $messages = $request->input();

        if (!$messages) {
            return abort(399);
        }

        if ($this->checkIfSparkpostValidationCall($messages)) {
            return response('OK', 200);
        }

        // Log all our messages for now.
        Log::info(print_r($messages, true));

        foreach ($messages as $message):

            // If this is a relay event, skip it for now.
            if (Arr::has($message, 'msys.relay_event')) {
                $this->handleRelayEvent($message);

                continue;
            }

            $message = Arr::get($message, 'msys.relay_message');

            $_from_email     = Arr::get($message, 'friendly_from');
            $_to_emails      = Arr::get($message, 'content.to');
            $_subject        = Arr::get($message, 'content.subject');
            $_email_html     = Arr::get($message, 'content.html');
            $_email_text     = Arr::get($message, 'content.text');
            $message_headers = Arr::get($message, 'content.headers');

            // Find our recipients, log our message.
            foreach ($_to_emails as $to_email) {
                $handler = null;
                $sender  = null;


                $handler = MessageHandler::where('address', $to_email)->with('account')->first();

                if (!$handler) {
                    Log::error('No valid handler found for: ' . $to_email);

                    return abort(428);
                }

                $sender = GroupSender::where('account_id', $handler->account->id)
                    ->whereHas('user.emails', function ($query) use ($_from_email) {
                        $query->where('user_emails.email', $_from_email);
                    })->first();

                if (!$sender) {
                    Log::error('No valid sender found for: ' . $_from_email);

                    return abort(428);
                }

                if ($handler->account_id !== $sender->account_id) {
                    Log::error('Handler account does not match sender!');

                    return abort(403);
                }

                if (false) {
                    Log::error('Message already exists in database! Receiving duplicate webhook requests.');

                    return abort(208);
                }

                try {
                    $newMessage = (new CreateMessage())
                        ->fromUser($sender->user)
                        ->forAccount($handler->account)
                        ->forGroup($handler->group)
                        ->withHandler($handler)
                        ->withProvider($handler->messageProvider)
                        ->withType($handler->messageType)
                        ->fromAddress($_from_email)
                        ->toAddress($to_email)
                        ->setContent($_email_text)
                        ->setHtmlContent($_email_html)
                        ->setSubject($_subject)
                        ->create();
                } catch (\Exception $e) {
                    Log::error($e->getMessage());

                    return abort(401);
                }
            }

        endforeach;

        return response('OK', 200);

        $attachments = [];

        // Get any file attachments.
        if (request()->get('attachment-count')) {
            for ($i = 1; $i <= request()->get('attachment-count'); $i++) {
                $attachment_id = 'attachment-' . $i;
                $file          = request()->file($attachment_id);

                // @TODO: Use S3 for this stuff.
                $file->storeAs(env('AMAZON_S3_MESSAGE_ATTACHMENT_FOLDER') . '/' . $sender->account_id,
                    $file->getClientOriginalName());

                $attachments[] = env('AMAZON_S3_MESSAGE_ATTACHMENT_FOLDER')
                    . '/'
                    . $sender->account_id
                    . '/'
                    . $file->getClientOriginalName();
            }
        }
    }

    public function handleRelayEvent($message)
    {
        return true;
    }

    public function event(Request $request)
    {
        $messages = $request->input();

        Log::info($messages);

        if ($this->checkIfSparkpostValidationCall($messages)) {
            return response('OK', 200);
        }

        exit;
    }

    // If Sparkpost is calling to confirm that our endpoint is valid, return true.
    public function checkIfSparkpostValidationCall($messages)
    {
        if (is_array($messages) &&
            !empty($messages) &&
            (Arr::get($messages[0], 'msys.relay_message.content.text') == 'Hi there SparkPostians.' || Arr::get($messages[0], 'msys.message_event.msg_from') == '<EMAIL>')) {
            return true;
        }

        return false;
    }
}
