<?php

namespace App\Api\V1\Controllers\Email;

use App\Base\Http\Controllers\Controller;
use App\Jobs\ProcessMessage;
use App\Mail\App\BadEmailMessage;
use App\Messages\Message;
use App\Messages\MessageHandler;
use App\Messages\Services\CreateMessage;
use App\Messages\Services\CreateMessageAttachment;
use App\Users\Email;
use App\Users\GroupSender;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class IncomingPostmarkController extends Controller
{
    /**
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response|void
     * @throws \Exception
     */
    public function inbound()
    {
        Log::info('IncomingPostmarkController:ReceivedIncomingEmail');

        // If postmark endpoint test.
        if (request()->input('FromFull.Email') == '<EMAIL>') {
            return response('OK', 200);
        }

        // Get the array of all emails this message was sent to.
        $_to_email_array  = request()->input('ToFull');
        $_cc_email_array  = request()->input('CcFull');
        $_bcc_email_array = request()->input('BccFull');

        // We just ignore CC fields for now.
        // BCC fields will include the "original recipient" email address, so we need to merge them.
        $_all_to_email_array = array_merge($_to_email_array, $_bcc_email_array);

        $handled_emails_array = [];
        $has_valid_handler    = false;

        // First figure out if we have NO valid handlers for any email in the TO or CC section
        foreach ($_all_to_email_array as $current_to_email_array) {
            // See if we have a message handler.
            $handler = MessageHandler::where('address', strtolower($current_to_email_array['Email']))
                ->isActive()
                ->with('account')
                ->first();

            // If we found a handler, stop looking.
            if ($handler) {
                $has_valid_handler      = true;
                $handled_emails_array[] = $current_to_email_array;
            }
        }

        // If there are multiple emails in the TO or CC section, send an error back to the user.
        // This will otherwise result in sending multiple emails to the same group when the next inbound email comes in for the next email address.
        if (count($handled_emails_array) > 1) {
            $this->sendTooManyEmailsErrorBackToUser(request()->input('FromFull.Email'));
        } // If we found at least one good handler, now process this message.
        elseif ($has_valid_handler) {
            foreach ($handled_emails_array as $current_to_email_array) {
                // See if we have a message handler.
                $handler = MessageHandler::where('address', strtolower($current_to_email_array['Email']))
                    ->isActive()
                    ->with('account')
                    ->first();

                // If we found a handler, process this message with this handler.
                if ($handler) {
                    Log::info('IncomingPostmarkController:FoundHandler: ' . $handler->id);

                    $this->processFromAddress($handler);
                }
            }
        } // If we found no valid handler, email the user back with such.
        else {
            $this->sendNoValidHandlerErrorBackToUser(strtolower(request()->input('FromFull.Email')));
        }

        // Return a success message.
        return response('OK', 200);
    }

    public function processFromAddress($handler)
    {
        $_to_email = $handler->address;

        // Pull key information out.
        $_from_email          = strtolower(request()->input('FromFull.Email'));
        $_from_name           = request()->input('FromName');
        $_from_reply_to_email = request()->input('ReplyTo');
        $_subject             = request()->input('Subject');
        $_body_html           = request()->input('HtmlBody');
        $_body_text           = request()->input('TextBody');
        $_message_id          = request()->input('MessageID');
        $_original_sent_at    = Carbon::parse(request()->input('Date'));
        $message_headers      = request()->input('Headers');
        $_attachments         = request()->input('Attachments');

        // First find our email in the database, to make sure it exists somewhere.
        $email = Email::where('email', 'like', $_from_email)->first();

        if (!$email) {
            Log::info('No valid email found for: ' . $_from_email);

            // Record the message for reference
            $newMessage = (new CreateMessage())
                ->forAccount($handler->account)
                ->forGroup($handler->group)
                ->withHandler($handler)
                ->withProvider($handler->messageProvider)
                ->withType($handler->messageType)
                ->fromAddress($_from_email)
                ->toAddress($_to_email)
                ->setContent($_body_text)
                ->setHtmlContent($_body_html)
                ->setSubject($_subject)
                ->setStatus('invalid_sender')
                ->create($_message_id);

            // Send an error back to this person.
            $this->sendErrorBackToUser($_from_email);

            // Return a success, so Postmark stops trying.
            return response('OK', 200);
        }

        // Get a list of possible users this could be.
        // This will only be one user, unless the $_from_email is a family email, then it could be a list of family members.
        $users_with_email = User::where('account_id', $handler->account_id)
            ->where(function ($query) use ($email) {
                $query->where('id', $email->user_id);
                if ($email->family_id > 0) {
                    $query->orWhere('family_id', $email->family_id);
                }
            })->get();

        // Find our message Sender.
        // If any group member is allowed to send messages, just get the User and check if they're from this group.
        if (boolval($handler->group?->allow_all_members_to_send)) {
            Log::info('IncomingPostmarkController -- Searching for a sender from the whole group. "allow_all_members_to_send"');

            $sender = User::where('account_id', $handler->account_id)
                ->whereIn('id', $users_with_email->pluck('id'))
                ->whereHas('groups', function ($query) use ($handler) {
                    $query->where('user_to_group.user_group_id', $handler->user_group_id);
                })->first();
        } // If any Lightpost user in the same account is allowed to send messages, just get the User.
        elseif (boolval($handler->group?->allow_lightpost_users_to_email)) {
            Log::info('IncomingPostmarkController -- Searching for a sender from all Lightpost. "allow_lightpost_users_to_email"');

            $sender = User::where('account_id', $handler->account_id)
                ->whereIn('id', $users_with_email->pluck('id'))
                ->first();
        } else {
            if (boolval($handler->group?->allow_internet_to_email)) {
                Log::info('IncomingPostmarkController -- Searching for a sender from the authorized senders list for a group that allows message from the internet. "allow_internet_to_email"');
            } else {
                Log::info('IncomingPostmarkController -- Searching for a sender from the authorized senders list.');
            }

            $sender = GroupSender::where('account_id', $handler->account_id)
                ->where('user_group_id', $handler->user_group_id)
                ->whereIn('user_id', $users_with_email->pluck('id'))
                ->first();

            if ($sender) {
                $sender = $sender->user;
            }
        }

        if (!$sender && !boolval($handler->group?->allow_internet_to_email)) {
            Log::error('No valid sender found for: ' . request()->get('sender'));

            // Record the message for reference
            $newMessage = (new CreateMessage())
                ->forAccount($handler->account)
                ->forGroup($handler->group)
                ->withHandler($handler)
                ->withProvider($handler->messageProvider)
                ->withType($handler->messageType)
                ->fromAddress($_from_email)
                ->toAddress($_to_email)
                ->setContent($_body_text)
                ->setHtmlContent($_body_html)
                ->setSubject($_subject)
                ->setStatus('invalid_sender')
                ->create($_message_id);

            // Send an error back to this person.
            $this->sendErrorBackToUser($_from_email);

            // Return a success, so Postmark stops trying.
            return response('OK', 200);
        }

        Log::info('IncomingPostmarkController:FoundSender: ' . optional($sender)->id);

        // Check that accounts match.
        if (!boolval($handler->group->allow_internet_to_email) && $handler->account_id !== $sender->account_id) {
            Log::error('Handler account does not match sender!');
            return abort(403);
        }

        // Check that we have not already received this Message for this Group.
        if ($this->checkIfMessageExists($_message_id, $handler->user_group_id)) {
            Log::error('IncomingPostmarkController::processFromAddress - Duplicate webhook requests. Message already exists in database!', [
                'message_id' => request()->get('Message-Id'),
            ]);

            return abort(208);
        }

        // Create our Message in the database.
        try {
            $newMessage = (new CreateMessage())
                ->fromUser($sender)
                ->forAccount($handler->account)
                ->forGroup($handler->group)
                ->withHandler($handler)
                ->withProvider($handler->messageProvider)
                ->withType($handler->messageType)
                ->fromAddress($_from_email)
                ->toAddress($_to_email)
                ->setContent($_body_text)
                ->setHtmlContent($_body_html)
                ->setSubject($_subject)
                ->create($_message_id);
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return abort(401);
        }

        // Update our last received from Handler date.
        $handler->last_received_at = Carbon::now();
        $handler->save();

        // Extract attachments and save them.
        if ($_attachments) {
            Log::info('IncomingPostmarkController:FoundAttachments: ' . count($_attachments));

            $i = 0;
            foreach ($_attachments as $attachment) {
                Log::info('IncomingPostmarkController:SavingAttachment: ' . $i);

                (new CreateMessageAttachment())->forMessage($newMessage)->createFromString(
                    Arr::get($attachment, 'Name'),
                    base64_decode(Arr::get($attachment, 'Content')),
                    Arr::get($attachment, 'ContentType'),
                    Arr::get($attachment, 'ContentID')
                );
                $i++;
            }
        }

        // Dispatch the message to the queue.
        try {
            $newMessage->status = 'processing';
            $newMessage->save();
            ProcessMessage::dispatch($newMessage)->onQueue('emails');

            Log::info('IncomingPostmarkController:DispatchedMessageToProcess: message_id:' . $newMessage->id);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());

            return abort(401);
        }
    }

    public function checkIfMessageExists($message_id, $user_group_id)
    {
        return Message::where('provider_transaction_id', $message_id)
            ->where('user_group_id', $user_group_id)
            ->exists();
    }

    protected function sendErrorBackToUser($from_email, $to_email = null, $type = 'invalid_sender')
    {
        if (!$from_email) {
            Log::warning('IncomingPostmarkController:sendErrorBackToUser: No email provided to send error back to for group email.', [
                'from_email'              => $from_email,
                'postmark_transaction_id' => request()->input('MessageID'),
            ]);
            return false;
        }

        $message = '## Oops! Please Retry!

It looks like you tried to send an email to a group ' . ($to_email ? '(' . $to_email . ')' : null) . ', but this *failed* because the email address you are sending from is not an **Authorized Sender**.

This could also happen if the group email you sent this to was put on the CC or BCC field.  Lightpost cannot see what emails are on a BCC list!

This is not a problem though!  Just follow these instructions to fix this:

1.  Go to the Groups section in Admin:  https://admin.lightpost.app/groups
2.  Click on the **name** of the group to view details.
3.  Click on the **Authorized Senders tab**.
4.  Click the “Add Sender” button on the **top right**.

Add yourself there and you’re good to start sending emails to that group!

**Be sure the email you are sending from is on your Lightpost account.**

If you need **any** help, I am here for you!  Just reply to this email, or give me a call at 833-548-7678.

Thanks!

-Drew Johnston';

        Log::warning('IncomingPostmarkController:sendErrorBackToUser', [
            'from_email'              => $from_email,
            'to_email'                => $to_email,
            'postmark_transaction_id' => request()->input('MessageID'),
            'email'                   => request()->all(),
        ]);

        Mail::to($from_email)->queue(new BadEmailMessage($message));
    }

    protected function sendNoValidHandlerErrorBackToUser($from_email, $type = 'no_valid_handler')
    {
        if (!$from_email) {
            Log::warning('IncomingPostmarkController:sendNoValidHandlerErrorBackToUser: No email provided to send error back to for group email.');
            return false;
        }

        $message = '## Oops! Please Retry!

It looks like you tried to send an email to a Lightpost group, but we could not find a valid group with the email used.

This is not a problem though!  Just follow these instructions to fix this:

1.  Go to the Groups section in Admin:  https://admin.lightpost.app/groups
2.  Click on the **name** of the group to view details.
3.  Check at the top right of the screen for the email that should be used.

If no email is seen, edit the Group Settings to make sure email messaging is enabled.

If you need **any** help, I am here for you!  Just reply to this email, or give me a call at 833-548-7678.

Thanks!

-Drew Johnston';

        Log::warning('IncomingPostmarkController:sendNoValidHandlerErrorBackToUser', [
            'from_email'              => $from_email,
            'postmark_transaction_id' => request()->input('MessageID'),
            'email'                   => request()->all(),
        ]);

        Mail::to($from_email)->queue(new BadEmailMessage($message));
    }

    protected function sendTooManyEmailsErrorBackToUser($from_email, $type = 'too_many_emails')
    {
        if (!$from_email) {
            Log::warning('IncomingPostmarkController:sendTooManyEmailsErrorBackToUser: More than one group email at once.');
            return false;
        }

        $message = '## Oops! Please Retry!

It looks like you tried to send an email to **multiple Lightpost groups** at once.

**Your email was not sent to any groups.** (Unless you used the BCC field)

Because of the nature of multiple recipients in emails, Lightpost can not determine exactly how many times we should send this email.

**Please try again**, with one group email at a time.

If you need **any** help, I am here for you!  Just reply to this email.

Thanks!

-Drew Johnston';

        Log::warning('IncomingPostmarkController:sendTooManyEmailsErrorBackToUser', [
            'from_email'              => $from_email,
            'postmark_transaction_id' => request()->input('MessageID'),
            'email'                   => request()->all(),
        ]);

        Mail::to($from_email)->queue(new BadEmailMessage($message));
    }
}
