<?php

namespace App\Api\V1\Controllers\Email;

use App\Base\Http\Controllers\Controller;
use App\Jobs\ProcessMessage;
use App\Mail\App\BadEmailMessage;
use App\Messages\Message;
use App\Messages\MessageHandler;
use App\Messages\Services\CreateMessage;
use App\Messages\Services\CreateMessageAttachment;
use App\Users\Email;
use App\Users\GroupSender;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class IncomingMailgunController extends Controller
{
    /**
     * https://developers.sparkpost.com/api/relay-webhooks/
     *
     * 1.  Pull key information out.
     * 2.  Find our message Handler.
     * 3.  Find our message Sender.
     * 4.  Check that accounts match.
     * 5.  Check that we have not already received this message.
     * 6.  Verify the message is from Mailgun.
     * 7.  Create our Message in the database.
     * 8.  Update the last received from <PERSON><PERSON> field.
     * 9.  Extract attachments and save them.
     * 10. Dispatch the message to the queue.
     * 11. Return a success message.
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response|void
     * @throws \Exception
     */
    public function inbound()
    {
        Log::info('IncomingMailgunController:ReceivedIncomingEmail');

        // Pull key information out.
        $_to_email   = request()->get('recipient');
        $_from_email = request()->get('sender');
        $_subject    = request()->get('Subject');
        $_body_html  = request()->get('body-html');
        $_body_text  = request()->get('body-plain');
        $_message_id = request()->get('Message-Id');

        // Find our message Handler.
        $handler = MessageHandler::where('address', $_to_email)
            ->isActive()
            ->with('account')
            ->first();

        if (!$handler) {
            Log::error('No valid or active handler found for: ' . request()->get('recipient'));

            // Send an error back to this person.
            $this->sendErrorBackToUser($_from_email);

            // Return a success, so Mailgun stops trying.
            return response('OK', 200);
        }

        Log::info('IncomingMailgunController:FoundHandler: ' . $handler->id);

        // First find our email in the database, to make sure it exists somewhere.
        $email = Email::where('email', 'like', $_from_email)->first();

        if (!$email) {
            Log::error('No valid email found for: ' . request()->get('sender'));

            // Record the message for reference
            $newMessage = (new CreateMessage())
                ->forAccount($handler->account)
                ->forGroup($handler->group)
                ->withHandler($handler)
                ->withProvider($handler->messageProvider)
                ->withType($handler->messageType)
                ->fromAddress($_from_email)
                ->toAddress($_to_email)
                ->setContent($_body_text)
                ->setHtmlContent($_body_html)
                ->setSubject($_subject)
                ->setStatus('invalid_sender')
                ->create($_message_id);

            // Send an error back to this person.
            $this->sendErrorBackToUser($_from_email);

            // Return a success, so Mailgun stops trying.
            return response('OK', 200);
        }

        // Get a list of possible users this could be.
        // This will only be one user, unless the $_from_email is a family email, then it could be a list of family members.
        $users_with_email = User::where('account_id', $handler->account_id)
            ->where(function ($query) use ($email) {
                $query->where('id', $email->user_id);
                if ($email->family_id > 0) {
                    $query->orWhere('family_id', $email->family_id);
                }
            })->get();

        // Find our message Sender.
        // If any group member is allowed to send messages, just get the User and check if they're from this group.
        if (boolval($handler->group->allow_all_members_to_send)) {
            Log::info('IncomingMailgunController -- Searching for a sender from the whole group. "allow_all_members_to_send"');

            $sender = User::where('account_id', $handler->account_id)
                ->whereIn('id', $users_with_email->pluck('id'))
                ->whereHas('groups', function ($query) use ($handler) {
                    $query->where('user_to_group.user_group_id', $handler->user_group_id);
                })->first();
        } else {
            if (boolval($handler->group->allow_internet_to_email)) {
                Log::info('IncomingMailgunController -- Searching for a sender from the authorized senders list for a group that allows message from the internet. "allow_internet_to_email"');
            } else {
                Log::info('IncomingMailgunController -- Searching for a sender from the authorized senders list.');
            }

            $sender = GroupSender::where('account_id', $handler->account_id)
                ->where('user_group_id', $handler->user_group_id)
                ->whereIn('user_id', $users_with_email->pluck('id'))
                ->first();

            if ($sender) {
                $sender = $sender->user;
            }
        }

        if (!$sender && !boolval($handler->group->allow_internet_to_email)) {
            Log::error('No valid sender found for: ' . request()->get('sender'));

            // Record the message for reference
            $newMessage = (new CreateMessage())
                ->forAccount($handler->account)
                ->forGroup($handler->group)
                ->withHandler($handler)
                ->withProvider($handler->messageProvider)
                ->withType($handler->messageType)
                ->fromAddress($_from_email)
                ->toAddress($_to_email)
                ->setContent($_body_text)
                ->setHtmlContent($_body_html)
                ->setSubject($_subject)
                ->setStatus('invalid_sender')
                ->create($_message_id);

            // Send an error back to this person.
            $this->sendErrorBackToUser($_from_email);

            // Return a success, so Mailgun stops trying.
            return response('OK', 200);
        }

        Log::info('IncomingMailgunController:FoundSender: ' . optional($sender)->id);

        // Check that accounts match.
        if (!boolval($handler->group->allow_internet_to_email) && $handler->account_id !== $sender->account_id) {
            Log::error('Handler account does not match sender!');
            return abort(403);
        }

        // Check that we have not already received this Message for this Group.
        if ($this->checkIfMessageExists(request()->get('Message-Id'), $handler->user_group_id)) {
            Log::error('Message already exists in database! Receiving duplicate webhook requests. ' . request()->get('Message-Id'));

            return abort(208);
        }

        // Verify the message is from Mailgun.
        if (!$this->verifyMailgunPost(
            config('mail.inbound.mailgun.api_key', null),
            request()->get('token'),
            request()->get('timestamp'),
            request()->get('signature'))
        ) {
            Log::error('Could not verify message from Mailgun is a valid message.');

            return abort(400);
        }

        // Create our Message in the database.
        try {
            $newMessage = (new CreateMessage())
                ->fromUser($sender)
                ->forAccount($handler->account)
                ->forGroup($handler->group)
                ->withHandler($handler)
                ->withProvider($handler->messageProvider)
                ->withType($handler->messageType)
                ->fromAddress($_from_email)
                ->toAddress($_to_email)
                ->setContent($_body_text)
                ->setHtmlContent($_body_html)
                ->setSubject($_subject)
                ->create($_message_id);
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return abort(401);
        }

        Log::info('IncomingMailgunController:CreatedMessage: message_id: ' . $newMessage->id);

        // Update our last received from Handler date.
        $handler->last_received_at = Carbon::now();
        $handler->save();

        // Extract attachments and save them.
        if (request()->get('attachment-count') > 0) {
            Log::info('IncomingMailgunController:FoundAttachments: ' . request()->get('attachment-count'));

            for ($i = 1; $i <= request()->get('attachment-count'); $i++) {
                Log::info('IncomingMailgunController:SavingAttachment: ' . $i);

                $content_id    = null;
                $attachment_id = 'attachment-' . $i;
                $file          = request()->file($attachment_id);

                // Sample of content-id-map (content_id is the KEY, not the VALUE):
                //   {"<<EMAIL>.>": "attachment-1"}
                if (is_array(json_decode(request()->get('content-id-map'), true))) {
                    $content_id = array_search($attachment_id, json_decode(request()->get('content-id-map'), true)) ?: null;
                    $content_id = trim($content_id, '<>');
                }

                (new CreateMessageAttachment())->forMessage($newMessage)->create($file, $content_id);
            }
        }

        // Dispatch the message to the queue.
        try {
            $newMessage->status = 'processing';
            $newMessage->save();
            ProcessMessage::dispatch($newMessage)->onQueue('emails');

            Log::info('IncomingMailgunController:DispatchedMessageToProcess: message_id:' . $newMessage->id);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());

            return abort(401);
        }

        // Return a success message.
        return response('OK', 200);
    }

    protected function sendErrorBackToUser($email, $type = 'invalid_sender')
    {
        if (!$email) {
            Log::info('IncomingMailgunController:sendErrorBackToUser: No email provided to send error back to.');
            return false;
        }

        $message = '## Oops! Please Retry!

It looks like you tried to send an email to a group, but this *failed* because the email address you are sending from is not an **Authorized Sender**.

This is not a problem though!  Just follow these instructions to fix this:

1.  Go to the Groups section in Admin:  https://admin.lightpost.app/groups
2.  Click on the **name** of the group to view details.
3.  Click on the **Authorized Senders tab**.
4.  Click the “Add Sender” button on the **top right**.

Add yourself there and you’re good to start sending emails to that group!

If you need **any** help, I am there for you!  Just reply to this email, or give me a call at 833-548-7678.

Thanks!

-Drew Johnston';

        Log::info('IncomingMailgunController:sendErrorBackToUser: ' . $email);

        Mail::to($email)->queue(new BadEmailMessage($message));
    }

    public function checkIfMessageExists($message_id, $user_group_id)
    {
        return Message::where('provider_transaction_id', $message_id)
            ->where('user_group_id', $user_group_id)
            ->exists();
    }

    /**
     * Verifies that our Mailgun API call is authentic.
     *
     * @param $apiKey
     * @param $token
     * @param $timestamp
     * @param $signature
     *
     * @return bool
     */
    private function verifyMailgunPost($apiKey, $token, $timestamp, $signature)
    {
        //check if the timestamp is fresh
        if (time() - $timestamp > 15) {
            return false;
        }

        //returns true if signature is valid
        return hash_hmac('sha256', $timestamp . $token, $apiKey) === $signature;
    }
}

/*
 * SAMPLE MAILGUN Route -> forward
 */

/**
 * Array
 * (
 *       [recipient] => <EMAIL>
 *       [sender] => <EMAIL>
 *       [subject] => ANOTHER test!
 *       [from] => Drew Johnston <<EMAIL>>
 *       [X-Mailgun-Spf] => Neutral
 *       [X-Mailgun-Sscore] => 0.0
 *       [X-Mailgun-Sflag] => No
 *       [X-Mailgun-Incoming] => Yes
 *       [X-Envelope-From] => <<EMAIL>>
 *       [Received] => by ************* with HTTP; Tue, 27 Dec 2016 07:36:45 -0800 (PST)
 *       [Dkim-Signature] => v=1; a=rsa-sha256; **SNIP**
 *       [X-Google-Dkim-Signature] => v=1; a=rsa-sha256; **SNIP**
 *       [X-Gm-Message-State] => AIkVDXIwS+sv1fMIZjMeiyW4yvuY0UnHyp6llNGWzq9dNAYfV3m+MbzcD/sURh3maxpmnt39fNq5e4/VBWZB7Q==
 *       [X-Received] => by *********** with SMTP id q71mr30843392qka.292.1482853045803; Tue, 27 Dec 2016 07:37:25 -0800 (PST)
 *       [Mime-Version] => 1.0
 *       [From] => Drew Johnston <<EMAIL>>
 *       [Date] => Tue, 27 Dec 2016 09:36:45 -0600
 *       [Message-Id] => <<EMAIL>>
 *       [Subject] => ANOTHER test!
 *       [To] => <EMAIL>
 *       [Content-Type] => multipart/alternative; boundary="001a114ac30e0cb8780544a5a1f7"
 *       [message-headers] => [["X-Mailgun-Spf", "Neutral"], ["X-Mailgun-Sscore", "0.0"], ["X-Mailgun-Sflag", "No"], ["X-Mailgun-Incoming", "Yes"], ["X-Envelope-From", "<<EMAIL>>"], ["Received", "from mail-qk0-f170.google.com (mail-qk0-f170.google.com [**************]) by mxa.mailgun.org with ESMTP id 58628ab6.7f1e22a1b3b0-in07; Tue, 27 Dec 2016 15:37:26 -0000 (UTC)"], ["Received", "by mail-qk0-f170.google.com with SMTP id t184so222631829qkd.0        for <<EMAIL>>; Tue, 27 Dec 2016 07:37:26 -0800 (PST)"], ["Dkim-Signature", "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=johnston.us; s=googlemail;        h=mime-version:from:date:message-id:subject:to;        bh=uz9mmzIpZUJC6dFu62iBJnoj7De2vgphPJSxUQhbiVA=;        b=L+4/Awa02bfecjZrGuI3/NE1EEBNK+E/jHBomAfcSK3JNUoPIQt4Lye4WsEpUup52V         HdwXPv7xxbyFTQMipwJBxj/KnJAjVtuOlxjxXhXfJGbLtdqO61bQVbIlqZZ9FX8U2hLq         SUO+wvl9JZ5q+Ig5IsyRkWUdJhG/ApM0dAlRE="], ["X-Google-Dkim-Signature", "v=1; a=rsa-sha256; c=relaxed/relaxed;        d=1e100.net; s=20161025;        h=x-gm-message-state:mime-version:from:date:message-id:subject:to;        bh=uz9mmzIpZUJC6dFu62iBJnoj7De2vgphPJSxUQhbiVA=;        b=BPqZdB28XOzlNr8lNQZvmX+hg0P/1KxgsOpXIoPop7OIkdz/DCgMju06rPvi1Bat6M         0bOKPQlMJQc8U61rhTf1UujrWLCgFXDropBWDZiCw2Ql2c6uCHNxIWwoXrwGauQOrkUk         0zkO0jFNmOTgP7lRkkiErrWufE8lf0KNdMoeDeXt3xqD8Hcnz9s9+d+6xlQoOLjgwvTM         FiOaLW9hFo/+0poQQ4LyS6nZQZtcen5hQbeUhdMYkkskekKEkNeM7/BcY5v/nOcxZryG         2xzR6QH4eSKh/kMWTmhKf+QqRCUjC2PGsgWpsTN56HaUSZAVXsSl6uiUP+zHGwknNVrq         hVdA=="], ["X-Gm-Message-State", "AIkVDXIwS+sv1fMIZjMeiyW4yvuY0UnHyp6llNGWzq9dNAYfV3m+MbzcD/sURh3maxpmnt39fNq5e4/VBWZB7Q=="], ["X-Received", "by *********** with SMTP id q71mr30843392qka.292.1482853045803; Tue, 27 Dec 2016 07:37:25 -0800 (PST)"], ["Mime-Version", "1.0"], ["Received", "by ************* with HTTP; Tue, 27 Dec 2016 07:36:45 -0800 (PST)"], ["From", "Drew Johnston <<EMAIL>>"], ["Date", "Tue, 27 Dec 2016 09:36:45 -0600"], ["Message-Id", "<<EMAIL>>"], ["Subject", "ANOTHER test!"], ["To", "<EMAIL>"], ["Content-Type", "multipart/alternative; boundary=\"001a114ac30e0cb8780544a5a1f7\""]]
 *       [timestamp] => 1482853047
 *       [token] => f7c67737b50eb1d1d143299679dbd45100b00bc73b4a189ab7
 *       [signature] => 57afad7eda5f08c4083043e23eba5bfbb1d1682cf51a80e1c86f317ff941d0b5
 *       [body-plain] => —
 *       Drew Johnston
 *      ʕ•ᴥ•ʔ*
 *
 *       [body-html] => <div dir="ltr"><br clear="all"><div><div class="gmail_signature" data-smartmail="gmail_signature"><div dir="ltr"><div><div dir="ltr">—<div>Drew Johnston<div><b style="color:rgb(0,0,0);font-family:Helvetica"><span style="font-size:19px;font-family:Arial;color:rgb(217,217,217);font-weight:normal;vertical-align:baseline;white-space:pre-wrap">ʕ•ᴥ•ʔ</span></b><br></div></div></div></div></div></div></div>
 *       </div>
 *
 *       [stripped-html] => <div dir="ltr"><br clear="all"><div><div class="gmail_signature" data-smartmail="gmail_signature"><div dir="ltr"><div><div dir="ltr">—<div>Drew Johnston<div><b style="color:rgb(0,0,0);font-family:Helvetica"><span style="font-size:19px;font-family:Arial;color:rgb(217,217,217);font-weight:normal;vertical-align:baseline;white-space:pre-wrap">ʕ•ᴥ•ʔ</span></b><br></div></div></div></div></div></div></div>
 *       </div>
 *
 *       [stripped-text] => —
 *       [stripped-signature] => Drew Johnston
 *      ʕ•ᴥ•ʔ*
 * )
 */
