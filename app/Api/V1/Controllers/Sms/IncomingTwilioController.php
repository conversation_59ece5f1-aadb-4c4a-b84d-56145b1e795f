<?php

namespace App\Api\V1\Controllers\Sms;

use App\Base\Http\Controllers\Controller;
use App\Mail\Api\Messages\InboundSmsReceived;
use App\Messages\MessageHistory;
use App\Users\Phone;
use App\Users\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class IncomingTwilioController extends Controller
{
    /**
     * Handle inbound text messages from Twilio.
     *
     * 1.  Pull key information out.
     * 2.  Look up the sender.
     * 3.  Check if this is an opt-out request.
     * 4.  If not, look up and sent an email notification to the message sender or account email, alerting them of an inbound text message.
     *
     * Twilio Docs:  https://www.twilio.com/docs/messaging/guides/webhook-request
     * Twilio Status Descriptions:  https://www.twilio.com/docs/sms/api/message-resource#message-status-values
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response|void
     * @throws \Exception
     */
    public function inbound()
    {
        Log::info('IncomingTwilioController:ReceivedIncomingSms');

//        Log::info(print_r(request()->getContent(), true));

        // Pull key information out.
        $_to_phone                  = request()->get('To');
        $_from_phone                = request()->get('From');
        $_from_phone_without_prefix = ltrim($_from_phone, '1');
        $_body_text                 = request()->get('Body');
        $_message_id                = request()->get('MessageSid');

        Log::info('Attempt to send a text message to ' . $_to_phone . ' from ' . $_from_phone);
        Log::info('Message ID: ' . $_message_id);

        $sender = $this->getUserFromPhone($_from_phone);

        if ($sender) {
            // If this message is an opt-out response.
            if ($this->checkIfOptOut($_body_text)) {
                $this->optUserOut($sender, $_from_phone);
            } // If this message is an opt-IN response.
            elseif ($this->checkIfOptIn($_body_text)) {
                $this->optUserIn($sender, $_from_phone);
            } else {
                Log::info('IncomingTwilioController:ReceivedIncomingSms - Found sender user ID: ' . $sender->id);
                $last_sms_message_to_user = $this->getLastMessageSentToUser($sender);

                if ($last_sms_message_to_user) {
                    $this->sendNotificationOfInboundSms($sender, $_from_phone, $_body_text, $last_sms_message_to_user);
                } else {
                    Log::info('IncomingTwilioController:ReceivedIncomingSms - No valid last message found that was sent to the user.');
                }
            }
        } else {
            Log::error('IncomingTwilioController:ReceivedIncomingSms - No valid sender found for number ' . $_from_phone . '.');
        }

        return response('OK', 200);
    }

    // Status webhook for info on when a message in delivered.
    public function callback()
    {
        // Pull key information out.
        $_status       = request()->get('MessageStatus'); // alt/deprecated: "SmsStatus"
        $_total_amount = request()->get('TotalAmount');
        $_error_code   = request()->get('ErrorCode');
        $_message_id   = request()->get('MessageSid');

        $message_history = MessageHistory::where('provider_message_id', $_message_id)
            ->whereNotNull('provider_message_id')
            ->first();

        if (!$message_history) {
            Log::error('IncomingTwilioController:ReceivedSmsCallback - No valid message history found for: ' . $_message_id);

            return response('ERROR', 404);
        }

        Log::info('IncomingTwilioController:ReceivedSmsCallback - Success: ' . $_message_id);

        $message_history->cost = $_total_amount;

        if ($_status == 'undelivered' || $_status == 'failed') {
            $message_history->failed_reason = $_error_code;
            $message_history->setStatusCreateOnMissing($_status, true);
        } else {
            $message_history->setStatusCreateOnMissing($_status, false, $message_history->message?->message_type_id ?: 2);
        }

        return response('OK', 200);
    }

    /**
     * Check if the message is an opt-out request.
     *
     * @param $message
     *
     * @return bool
     */
    public function checkIfOptOut($message)
    {
        // If there is an "opt-out indicator", return true. -- Get first 4 characters, lowercase it, trim it, search it.
        if (Str::contains(Str::lower(Str::substr(trim($message), 0, 4)), ['stop', 'quit', 'spam', 'paus', 'end'])) {
            return true;
        }

        // Or if we find clear phrases of unwanted messages.
        if (Str::contains(trim(Str::lower($message)), ['wrong number', 'please stop'])) {
            return true;
        }

        return false;
    }

    /**
     * Check if the message is an opt-IN request.
     *
     * @param $message
     *
     * @return bool
     */
    public function checkIfOptIn($message)
    {
        // If there is an "opt-in indicator", return true. -- Get first 6 characters, lowercase it, trim it, search it.
        if (Str::contains(Str::lower(Str::substr(trim($message), 0, 6)), ['unstop', 'start', 'resub'])) {
            return true;
        }

        return false;
    }

    /**
     * Edit a phone number to opt-out of messages.
     *
     * @param $user
     * @param $phone_number
     *
     * @return bool
     */
    public function optUserOut($user, $phone_number)
    {
        $_from_phone_without_prefix = ltrim($phone_number, '1');

        // In case we have duplicate entries of this phone number.
        $phones = Phone::where('number', 'like', '%' . $_from_phone_without_prefix)->get();

        if ($phones) {
            try {
                foreach ($phones as $phone) {
                    $phone->messages_opt_out = true;
                    $phone->save();

                    Log::info('IncomingTwilioController:ReceivedIncomingSms - User opt-out request received and saved.  User ID ' . $user->id . ' with number ' . $phone_number . ' -- Phone ID ' . $phone->id);
                }

                return true;
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        } else {
            Log::error('IncomingTwilioController:ReceivedIncomingSms - User opt-out request received, but could not find number: User ID ' . $user->id . ' and phone number ' . $phone_number);
        }

        return false;
    }

    /**
     * Edit a phone number to opt-IN to messages.
     *
     * @param $user
     * @param $phone_number
     *
     * @return bool
     */
    public function optUserIn($user, $phone_number)
    {
        $_from_phone_without_prefix = ltrim($phone_number, '1');

        // In case we have duplicate entries of this phone number.
        $phones = Phone::where('number', 'like', '%' . $_from_phone_without_prefix)->get();

        if ($phones) {
            try {
                foreach ($phones as $phone) {
                    $phone->messages_opt_out = false;
                    $phone->save();

                    Log::info('IncomingTwilioController:ReceivedIncomingSms - User opt-IN request received and saved.  User ID ' . $user->id . ' with number ' . $phone_number . ' -- Phone ID ' . $phone->id);
                }

                return true;
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        } else {
            Log::error('IncomingTwilioController:ReceivedIncomingSms - User opt-IN request received, but could not find number: User ID ' . $user->id . ' and phone number ' . $phone_number);
        }

        return false;
    }

    /**
     * Return a user object given a phone number.
     *
     * @param $from_phone
     *
     * @return mixed
     */
    public function getUserFromPhone($from_phone)
    {
        $_from_phone_without_prefix = ltrim($from_phone, '1');

        return User::whereHas('phones', function ($query) use ($_from_phone_without_prefix) {
            $query->where('user_phones.number', 'like', '%' . $_from_phone_without_prefix);
        })->first();
    }

    /**
     * Get the last text message sent to a user.
     *
     * @param $user
     *
     * @return bool
     */
    public function getLastMessageSentToUser($user)
    {
        if (!$user) {
            return false;
        }

        $message_history = MessageHistory::where('user_id', $user->id)
            ->with('message')
            ->orderBy('created_at', 'desc')
            ->limit(30)
            ->get();

        if (!$message_history) {
            Log::info('IncomingTwilioController:ReceivedIncomingSms - No message history found for user ' . $user->id);
            return null;
        }

        foreach ($message_history as $mh) {
            if ($mh->message->message_type_id == 2) { // Is SMS
                Log::info('IncomingTwilioController:ReceivedIncomingSms - Found message of type SMS! ID: ' . $mh->message->id);
                return $mh->message;
            }
        }

        Log::info('IncomingTwilioController:ReceivedIncomingSms - No valid message found of type SMS in message_history. Searched through ' . $message_history->count() . ' message_history records.');

        return true;
    }

    /**
     * Get the email (string) that a reply to a message should go to.
     *
     * If no sender is found for a message, it will go to the Account email.
     *
     * @param $message
     *
     * @return string
     */
    public function getReplyToUserEmailFromMessage($message)
    {
        // Send this to the person who sent the last message.
        if ($message->message_sender_id) {
            return optional($message->sender->getBestEmail())->email;
        } else { // Otherwise, send it to our account owner.
            return $message->account->email;
        }
    }

    /**
     * Send an email notification of an inbound text message.
     *
     * @param $sender
     * @param $from_phone
     * @param $text
     * @param $last_message
     *
     * @return bool
     */
    public function sendNotificationOfInboundSms($sender, $from_phone, $text, $last_message)
    {
        if (!$last_message) {
            Log::info('IncomingTwilioController:sendNotificationOfInboundSms: No known last message to send notification back to.');
            return false;
        }

        $reply_to_user_email = $this->getReplyToUserEmailFromMessage($last_message);

        Log::info('IncomingTwilioController:sendNotificationOfInboundSms: Sent to ' . $reply_to_user_email);

        Mail::to($reply_to_user_email)->queue(new InboundSmsReceived($sender, $from_phone, $text));

        // Send a copy to myself for now.
        Mail::to('<EMAIL>')->queue(new InboundSmsReceived($sender, $from_phone, $text));

        return true;
    }
}
