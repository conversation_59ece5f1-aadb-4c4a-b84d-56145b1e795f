<?php

namespace App\Api\V1\Controllers\Sms;

use App\Base\Http\Controllers\Controller;
use App\Mail\Api\Messages\InboundSmsReceived;
use App\Messages\MessageHistory;
use App\Users\Phone;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class IncomingPlivoController extends Controller
{
    public $plivo_error_codes = [
        '10'   => [
            'code'        => '10',
            'reason'      => 'Invalid Message',
            'description' => 'Message rejected by downstream carriers after basic validation. Contact Plivo support if frequent.',
        ],
        '20'   => [
            'code'        => '20',
            'reason'      => 'Network Error',
            'description' => 'Temporary carrier network issues. Retry when network is stable.',
        ],
        '30'   => [
            'code'        => '30',
            'reason'      => 'Spam Detected',
            'description' => 'Message blocked by carrier spam filters or too many messages sent using long code numbers. Consider using short codes for bulk messages in US/Canada.',
        ],
        '40'   => [
            'code'        => '40',
            'reason'      => 'Invalid Source Number',
            'description' => 'Source number is incorrectly formatted, not SMS-enabled, or not assigned to your Plivo account.',
        ],
        '50'   => [
            'code'        => '50',
            'reason'      => 'Invalid Destination Number',
            'description' => 'Destination number is incorrect, not SMS-enabled, or is a PSTN landline.',
        ],
        '60'   => [
            'code'        => '60',
            'reason'      => 'Loop Detected',
            'description' => 'Carrier detected endless message loop between source and destination numbers.',
        ],
        '70'   => [
            'code'        => '70',
            'reason'      => 'Destination Permanently Unavailable',
            'description' => 'Destination number is inactive with no indication of future availability.',
        ],
        '80'   => [
            'code'        => '80',
            'reason'      => 'Destination Temporarily Unavailable',
            'description' => 'Destination number unreachable (e.g., phone off or out of coverage).',
        ],
        '90'   => [
            'code'        => '90',
            'reason'      => 'No Route Available',
            'description' => 'No carrier route available for message delivery.',
        ],
        '100'  => [
            'code'        => '100',
            'reason'      => 'Prohibited by Carrier',
            'description' => 'Network doesn\'t support the message being sent.',
        ],
        '110'  => [
            'code'        => '110',
            'reason'      => 'Message Too Long',
            'description' => 'Message exceeds 1,600 characters (GSM) or 737 characters (UCS-2).',
        ],
        '120'  => [
            'code'        => '120',
            'reason'      => 'MMS Message Payload Too Large',
            'description' => 'Total MMS payload (text plus media) exceeds 5MB limit.',
        ],
        '130'  => [
            'code'        => '130',
            'reason'      => 'Unsupported Message Media',
            'description' => 'One or more MMS media attachments are of unsupported type.',
        ],
        '140'  => [
            'code'        => '140',
            'reason'      => 'Message Media Processing Failed',
            'description' => 'Media attachments could not be processed due to unreachable URL or incorrect format.',
        ],
        '150'  => [
            'code'        => '150',
            'reason'      => 'Recipient Registered for India DND',
            'description' => 'User is registered under Do Not Disturb registry in India.',
        ],
        '160'  => [
            'code'        => '160',
            'reason'      => 'DLT Registration Issue',
            'description' => 'Message rejected due to DLT registration issues. Use only registered headers and templates.',
        ],
        '200'  => [
            'code'        => '200',
            'reason'      => 'Source Number Blocked by STOP from Destination Number',
            'description' => 'Destination has opted out of your campaign.',
        ],
        '300'  => [
            'code'        => '300',
            'reason'      => 'Failed to Dispatch Message',
            'description' => 'Internal failure while passing message to carriers. Can retry later.',
        ],
        '310'  => [
            'code'        => '310',
            'reason'      => 'WhatsApp phone number registration error',
            'description' => 'Phone number not registered or deregistration failed for WhatsApp.',
        ],
        '320'  => [
            'code'        => '320',
            'reason'      => 'WhatsApp Phone Number Verification Error',
            'description' => 'Phone number needs verification for WhatsApp.',
        ],
        '330'  => [
            'code'        => '330',
            'reason'      => 'Unsupported WhatsApp message type',
            'description' => 'Message type not supported by Plivo for WhatsApp.',
        ],
        '340'  => [
            'code'        => '340',
            'reason'      => 'Template status error',
            'description' => 'WhatsApp template issues (approval, name, locale, or length).',
        ],
        '350'  => [
            'code'        => '350',
            'reason'      => 'Template parameter errors',
            'description' => 'Parameter values don\'t match template or are incorrectly formatted.',
        ],
        '360'  => [
            'code'        => '360',
            'reason'      => 'WhatsApp Business Account Disabled',
            'description' => 'WABA account locked or temporarily disabled.',
        ],
        '370'  => [
            'code'        => '370',
            'reason'      => 'WhatsApp Throttling Errors',
            'description' => 'Rate limits reached for WABA account.',
        ],
        '380'  => [
            'code'        => '380',
            'reason'      => 'Re-engagement message',
            'description' => 'More than 24 hours since last recipient reply. Use template message.',
        ],
        '420'  => [
            'code'        => '420',
            'reason'      => 'Message Expired',
            'description' => 'Message remained in processing queue for over three hours.',
        ],
        '450'  => [
            'code'        => '450',
            'reason'      => 'Destination Country Disabled',
            'description' => 'Messages to this country disabled for your account.',
        ],
        '451'  => [
            'code'        => '451',
            'reason'      => 'Unusual Activity',
            'description' => 'Messages exceeded hourly threshold.',
        ],
        '900'  => [
            'code'        => '900',
            'reason'      => 'Insufficient Credit',
            'description' => 'Account lacks required credits to send message.',
        ],
        '910'  => [
            'code'        => '910',
            'reason'      => 'Account Disabled',
            'description' => 'Parent Plivo account was disabled when message was processed.',
        ],
        '950'  => [
            'code'        => '950',
            'reason'      => 'Daily Messaging Limit Reached for Your 10DLC Brand',
            'description' => 'T-Mobile daily message limit reached for brand.',
        ],
        '960'  => [
            'code'        => '960',
            'reason'      => 'Limit Reached or Messages Caught by Spam Filter on Unverified or Pending Verification Toll-Free Number',
            'description' => 'Exceeded limits for unverified toll-free numbers or messages caught by filters.',
        ],
        '1000' => [
            'code'        => '1000',
            'reason'      => 'Unknown Error',
            'description' => 'Message failed for unknown reasons.',
        ],
        '2xxx' => [
            'code'        => '2xxx',
            'reason'      => 'HTTP Error Received From Message URL For Incoming SMS',
            'description' => 'HTTP error response from message_url for undelivered incoming SMS.',
        ],
    ];

    /**
     * Handle inbound text messages from Plivo.
     *
     * 1.  Pull key information out.
     * 2.  Look up the sender.
     * 3.  Check if this is an opt-out request.
     * 4.  If not, look up and sent an email notification to the message sender or account email, alerting them of an inbound text message.
     *
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response|void
     * @throws \Exception
     */
    public function inbound()
    {
        Log::info('IncomingPlivoController:ReceivedIncomingSms');

//        Log::info(print_r(request()->getContent(), true));

        // Pull key information out.
        $_to_phone                  = request()->get('To');
        $_from_phone                = request()->get('From');
        $_from_phone_without_prefix = ltrim($_from_phone, '1');
        $_body_text                 = request()->get('Text');
        $_message_id                = request()->get('MessageUUID');

        Log::info('Attempt to send a text message to ' . $_to_phone . ' from ' . $_from_phone);
        Log::info('Message was: ' . $_body_text);

        $sender = $this->getUserFromPhone($_from_phone);

        if ($sender) {
            // If this message is an opt-out response.
            if ($this->checkIfOptOut($_body_text)) {
                $this->optUserOut($sender, $_from_phone);
            } // If this message is an opt-IN response.
            elseif ($this->checkIfOptIn($_body_text)) {
                $this->optUserIn($sender, $_from_phone);
            } else {
                Log::info('IncomingPlivoController:ReceivedIncomingSms - Found sender user ID: ' . $sender->id);
                $last_sms_message_to_user = $this->getLastMessageSentToUser($sender);

                if ($last_sms_message_to_user) {
                    $this->sendNotificationOfInboundSms($sender, $_from_phone, $_body_text, $last_sms_message_to_user);
                } else {
                    Log::info('IncomingPlivoController:ReceivedIncomingSms - No valid last message found that was sent to the user.');
                }
            }
        } else {
            Log::error('IncomingPlivoController:ReceivedIncomingSms - No valid sender found for number ' . $_from_phone . '.');
        }

        return response('OK', 200);
    }

    public function callback()
    {
        // Pull key information out.
        $_status       = request()->get('Status');
        $_units        = request()->get('Units');
        $_total_amount = request()->get('TotalAmount');
        $_error_code   = request()->get('ErrorCode');
        $_message_id   = request()->get('MessageUUID');

        $message_history = MessageHistory::where('provider_message_id', $_message_id)
            ->whereNotNull('provider_message_id')
            ->first();

        if (!$message_history) {
            Log::error('IncomingPlivoController:ReceivedSmsCallback - No valid message history found for: ' . $_message_id);

            return response('ERROR', 404);
        }

        Log::info('IncomingPlivoController:ReceivedSmsCallback - Success: ' . $_message_id);

        $message_history->cost = $_total_amount;

        if ($_status == 'undelivered' || $_status == 'failed') {
            $error_code_array = array_key_exists((string)$_error_code, $this->plivo_error_codes) ? $this->plivo_error_codes[$_error_code] : null;
            $reason           = Arr::get($error_code_array, 'reason') . ' - ' . Arr::get($error_code_array, 'description');

            if ($reason) {
                $message_history->failed_reason = $reason;
            } else {
                $message_history->failed_reason = $_error_code;
            }

            $message_history->setStatusCreateOnMissing($_status, true, $message_history->message?->message_type_id ?: 2);
        } else {
            $message_history->setStatusCreateOnMissing($_status, false, $message_history->message?->message_type_id ?: 2);
        }

        return response('OK', 200);
    }

    /**
     * Check if the message is an opt-out request.
     *
     * @param $message
     *
     * @return bool
     */
    public function checkIfOptOut($message)
    {
        // If there is an "opt-out indicator", return true. -- Get first 4 characters, lowercase it, trim it, search it.
        if (Str::contains(Str::lower(Str::substr(trim($message), 0, 4)), ['stop', 'quit', 'spam'])) {
            return true;
        }

        // Or if we find clear phrases of unwanted messages.
        if (Str::contains(trim(Str::lower($message)), ['wrong number', 'please stop', 'remove me', 'want these messages'])) {
            return true;
        }

        return false;
    }

    /**
     * Check if the message is an opt-IN request.
     *
     * @param $message
     *
     * @return bool
     */
    public function checkIfOptIn($message)
    {
        // If there is an "opt-in indicator", return true. -- Get first 6 characters, lowercase it, trim it, search it.
        if (Str::contains(Str::lower(Str::substr(trim($message), 0, 6)), ['unstop', 'start', 'resub'])) {
            return true;
        }

        return false;
    }

    /**
     * Edit a phone number to opt-out of messages.
     *
     * @param $user
     * @param $phone_number
     *
     * @return bool
     */
    public function optUserOut($user, $phone_number)
    {
        $_from_phone_without_prefix = ltrim($phone_number, '1');

        // In case we have duplicate entries of this phone number.
        $phones = Phone::where('number', 'like', '%' . $_from_phone_without_prefix)->get();

        if ($phones) {
            try {
                foreach ($phones as $phone) {
                    $phone->messages_opt_out = true;
                    $phone->save();

                    Log::info('IncomingPlivoController:ReceivedIncomingSms - User opt-out request received and saved.  User ID ' . $user->id . ' with number ' . $phone_number . ' -- Phone ID ' . $phone->id);
                }

                return true;
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        } else {
            Log::error('IncomingPlivoController:ReceivedIncomingSms - User opt-out request received, but could not find number: User ID ' . $user->id . ' and phone number ' . $phone_number);
        }

        return false;
    }

    /**
     * Edit a phone number to opt-IN to messages.
     *
     * @param $user
     * @param $phone_number
     *
     * @return bool
     */
    public function optUserIn($user, $phone_number)
    {
        $_from_phone_without_prefix = ltrim($phone_number, '1');

        // In case we have duplicate entries of this phone number.
        $phones = Phone::where('number', 'like', '%' . $_from_phone_without_prefix)->get();

        if ($phones) {
            try {
                foreach ($phones as $phone) {
                    $phone->messages_opt_out = false;
                    $phone->save();

                    Log::info('IncomingPlivoController:ReceivedIncomingSms - User opt-IN request received and saved.  User ID ' . $user->id . ' with number ' . $phone_number . ' -- Phone ID ' . $phone->id);
                }

                return true;
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        } else {
            Log::error('IncomingPlivoController:ReceivedIncomingSms - User opt-IN request received, but could not find number: User ID ' . $user->id . ' and phone number ' . $phone_number);
        }

        return false;
    }

    /**
     * Return a user object given a phone number.
     *
     * @param $from_phone
     *
     * @return mixed
     */
    public function getUserFromPhone($from_phone)
    {
        $_from_phone_without_prefix = ltrim($from_phone, '1');

        return User::whereHas('phones', function ($query) use ($_from_phone_without_prefix) {
            $query->where('user_phones.number', 'like', '%' . $_from_phone_without_prefix);
        })->first();
    }

    /**
     * Get the last text message sent to a user.
     *
     * @param $user
     *
     * @return bool
     */
    public function getLastMessageSentToUser($user)
    {
        if (!$user) {
            return false;
        }

        $message_history = MessageHistory::where('user_id', $user->id)
            ->with('message')
            ->orderBy('created_at', 'desc')
            ->limit(30)
            ->get();

        if (!$message_history) {
            Log::info('IncomingPlivoController:ReceivedIncomingSms - No message history found for user ' . $user->id);
            return null;
        }

        foreach ($message_history as $mh) {
            if ($mh->message->message_type_id == 2) { // Is SMS
                Log::info('IncomingPlivoController:ReceivedIncomingSms - Found message of type SMS! ID: ' . $mh->message->id);
                return $mh->message;
            }
        }

        Log::info('IncomingPlivoController:ReceivedIncomingSms - No valid message found of type SMS in message_history. Searched through ' . $message_history->count() . ' message_history records.');

        return true;
    }

    /**
     * Get the email (string) that a reply to a message should go to.
     *
     * If no sender is found for a message, it will go to the Account email.
     *
     * @param $message
     *
     * @return string
     */
    public function getReplyToUserEmailFromMessage($message)
    {
        // Send this to the person who sent the last message.
        if ($message->message_sender_id) {
            return optional($message->sender->getBestEmail())->email;
        } else { // Otherwise, send it to our account owner.
            return $message->account->email;
        }
    }

    /**
     * Send an email notification of an inbound text message.
     *
     * @param $sender
     * @param $from_phone
     * @param $text
     * @param $last_message
     *
     * @return bool
     */
    public function sendNotificationOfInboundSms($sender, $from_phone, $text, $last_message)
    {
        if (!$last_message) {
            Log::info('IncomingPlivoController:sendNotificationOfInboundSms: No known last message to send notification back to.');
            return false;
        }

//        $reply_to_user_email = $this->getReplyToUserEmailFromMessage($last_message);

//        Log::info('IncomingPlivoController:sendNotificationOfInboundSms: Sent to ' . $reply_to_user_email);

//        Mail::to($reply_to_user_email)->queue(new InboundSmsReceived($sender, $from_phone, $text));

        // Send a copy to myself for now.
        Mail::to('<EMAIL>')->queue(new InboundSmsReceived($sender, $from_phone, $text));

        return true;
    }
}
