<?php

namespace App\Api\V1\Controllers\Accounts;

use App\Base\Http\Controllers\Controller;
use App\Base\Models\WebhookEvent;

class PaddleWebhookController extends Controller
{
    public function event()
    {
        $payload = request()->getContent();

        try {
//        (new Verifier())->verify(
//            request(),
//            new Secret(config('services.paddle.webhook_secret_key'))
//        );
        } catch (\Exception $e) {
            http_response_code(400);
            exit();
        }

        // Just log the event for now.
        WebhookEvent::create([
            'service' => 'paddle',
            'payload' => request()->getContent(),
        ]);

        return response('OK');
    }

}
