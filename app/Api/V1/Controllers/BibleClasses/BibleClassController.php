<?php

namespace App\Api\V1\Controllers\BibleClasses;

use App\Base\Http\Controllers\Controller;
use App\BibleClasses\BibleClassGroup;
use App\Mail\App\BibleClassRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class BibleClassController extends Controller
{
    public function indexGroups(Request $request)
    {
        if (!request()->user()->getSetting('feature.website_integrations')) {
            abort(401);
        }

        $quarters = BibleClassGroup::visibleToAccount($request->user())
            ->signupIsActive()
            ->with([
                'classes' => function ($query) {
                    $query->select([
                        'id',
                        'bible_class_group_id',
                        'user_attendance_type_id',
                        'created_at',
                        'title',
                        'description',
                        'short_description',
                        'location_name',
                        'day_of_week',
                        'is_new',
                        'enable_signup',
                        'is_featured',
                        'is_hidden',
                    ])
                        ->where('is_hidden', 0);
                },
                'classes.attendanceType:id,name,short_name,sort_id',
                'classes.teachers:first_name,last_name',
            ])
            ->select([
                'id',
                'created_at',
                'name',
                'description',
                'weeks',
                'start_date',
                'end_date',
                'is_signup_active',
            ])
            ->paginate(5);

        $quarters = $quarters->map(function ($group) {
            $group->classes = $group->classes->map(function ($class) {
                unset($class->user_attendance_type_id);

                return $class;
            });

            return $group;
        });

        return response()->json($quarters);
    }

    public function register(Request $request)
    {
        if (!request()->user()->getSetting('feature.website_integrations')) {
            abort(401);
        }

        if (!request()->input('classes')) {
            abort(401);
        }

        Log::info('BibleClass::Register -- ' . request()->input('name') . ' -- ' . request()->input('email'));

        if (request('url') > '') {
            Log::error('Probable bot submission for Bible class registration.', [
                'location' => 'BibleClassController:register',
                'email'    => request()->input('email'),
                'name'     => request()->input('name'),
                'classes'  => request()->input('classes'),
            ]);

            return abort(400);
        }

        Mail::to($request->user()->email)
            ->queue(new BibleClassRegistration(request()->input('name'), request()->input('email'), request()->input('classes')));
    }
}
