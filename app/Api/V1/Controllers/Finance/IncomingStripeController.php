<?php

namespace App\Api\V1\Controllers\Finance;

use App\Base\Http\Controllers\Controller;
use App\Finance\Services\CreateStripeEventLog;
use App\Users\Payment;
use Illuminate\Support\Facades\Log;

class IncomingStripeController extends Controller
{
    public function events()
    {
        // This is your Stripe CLI webhook secret for testing your endpoint locally.
        $endpoint_secret = config('services.stripe.connect.webhook_signing_secret');

        $payload    = request()->getContent();
        $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
        $event      = null;

        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $sig_header,
                $endpoint_secret
            );
        } catch (\UnexpectedValueException $e) {
            // Invalid payload
            Log::error('IncomingStripeController::invalidPayload', [
                'exception' => $e,
            ]);

            http_response_code(400);
            exit();
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            // Invalid signature
            Log::error('IncomingStripeController::invalidSignature', [
                'exception' => $e,
            ]);

            http_response_code(400);
            exit();
        }

        // Handle the event
        switch ($event->type) {
            case 'charge.captured':
                $this->handleChargeCapturedEvent($event);
                break;
            case 'charge.pending':
                $this->handleChargePendingEvent($event);
                break;
            case 'charge.succeeded':
                $this->handleChargeSucceededEvent($event);
                break;
            case 'charge.failed':
                $this->handleChargeFailedEvent($event);
                break;
            case 'charge.dispute.created':
                $this->handleChargeDisputeCreatedEvent($event);
                break;
            case 'charge.refunded':
                $this->handleChargeRefundedEvent($event);
                break;
            case 'charge.refund.updated':
                $this->handleChargeRefundUpdatedEvent($event);
                break;
            case 'payment_intent.created':
                $this->handlePaymentIntentCreated($event);
                break;
            case 'payment_intent.payment_failed':
                $this->handlePaymentIntentFailed($event);
                break;
            case 'payment_intent.requires_action':
                $this->handlePaymentIntentRequiresAction($event);
                break;
            case 'payment_intent.partially_funded':
                $this->handlePaymentIntentPartiallyFunded($event);
                break;
            case 'payment_intent.succeeded':
                $this->handlePaymentIntentSucceeded($event);
                break;
            case 'payout.updated':
                $this->handlePayoutUpdated($event);
                break;
            case 'payout.paid':
            case 'payout.created':
            case 'payout.reconciliation_completed':
            case 'balance.available':
                break;
            default:
                Log::error('IncomingStripeController::Events -- Received unregistered event type.', [
                    'type'  => $event->type,
                    'event' => $event,
                ]);
        }

        // Log this event to our database.
        try {
            (new CreateStripeEventLog())
                ->setEvent($event)
                ->create();
        } catch (\Exception $e) {
            Log::error('IncomingStripeController::handleChargeFailedEvent -- Could not create a Stripe event log.', [
                'exception' => $e,
            ]);
        }

        return response(null);
    }

    public function handleChargeFailedEvent($event)
    {
        // Get the ID of the failed charge from the event object.
        $charge_id = $event->data->object->id;

        // Find our charge.
        $user_payment = Payment::where('stripe_charge_id', $charge_id)->first();

        // If we did not find a charge, log an error and return.
        if (!$user_payment) {
            Log::error('IncomingStripeController::handleChargeFailedEvent -- Could not find a charge with the ID.', [
                'event'             => $event,
                'event_data_object' => $event?->data?->object,
                'charge_id'         => $charge_id,
            ]);
            return response(null);
        }

        // If we found a charge, update it.
        $user_payment->update([
            'status'           => $event->data->object->status,
            'stripe_status'    => $event->data->object->status,
            'is_failed'        => true,
            'is_success'       => false,
            'is_pending'       => false,
            'original_amount'  => $user_payment->amount,
            'amount'           => $event->data->object->amount_captured ?: 0,
            'amount_deposited' => $user_payment->amount_fee * -1,
        ]);
    }

    public function handleChargePendingEvent($event)
    {
        // Get the ID of the failed charge from the event object.
        $charge_id = $event->data->object->id;

        // Find our charge.
        $user_payment = Payment::where('stripe_charge_id', 'LIKE', $charge_id)->first();

        // If we did not find a charge, log an error and return.
        if (!$user_payment) {
            Log::error('IncomingStripeController::handleChargePendingEvent -- Could not find a charge with the ID.', [
                'event'             => $event,
                'event_data_object' => $event?->data?->object,
                'charge_id'         => $charge_id,
            ]);
            return response(null);
        }

        // If we found a charge, update it.
        $user_payment->update([
            'status'               => $event->data->object->status,
            'stripe_status'        => $event->data->object->status,
            'is_failed'            => false,
            'is_success'           => false,
            'is_pending'           => true,
            'original_amount'      => $user_payment->amount,
            'amount'               => $event->data->object->amount_captured ?: 0,
            'amount_deposited'     => $user_payment->amount_fee * -1,
            'stripe_charge_object' => json_encode($event->data->object),
        ]);
    }

    public function handleChargeSucceededEvent($event)
    {
        // Get the ID of the failed charge from the event object.
        $charge_id = $event->data->object->id;

        // Find our charge.
        $user_payment = Payment::where('stripe_charge_id', 'LIKE', $charge_id)->first();

        // If we did not find a charge, log an error and return.
        if (!$user_payment) {
            Log::error('IncomingStripeController::handleChargeSucceededEvent -- Could not find a charge with the ID.', [
                'event'             => $event,
                'event_data_object' => $event?->data?->object,
                'charge_id'         => $charge_id,
            ]);
            return response(null);
        }

        // If we found a charge, update it.
        $user_payment->update([
            'status'               => $event->data->object->status,
            'stripe_status'        => $event->data->object->status,
            'is_failed'            => false,
            'is_success'           => true,
            'is_pending'           => false,
            'original_amount'      => $user_payment->amount,
            'amount'               => $event->data->object->amount_captured ?: 0,
            'amount_deposited'     => $user_payment->amount_fee * -1,
            'stripe_charge_object' => json_encode($event->data->object),
        ]);
    }

    public function handleChargeRefundedEvent($event)
    {
        Log::error('IncomingStripeController::handleChargeRefundedEvent', [
            'event' => $event,
        ]);
    }

    public function handleChargeRefundUpdatedEvent($event)
    {
        Log::error('IncomingStripeController::handleChargeRefundUpdatedEvent', [
            'event' => $event,
        ]);
    }

    public function handleChargeCapturedEvent($event)
    {
    }

    public function handleChargeDisputeCreatedEvent($event)
    {
        Log::error('IncomingStripeController::handleChargeDisputeCreatedEvent', [
            'event' => $event,
        ]);
    }

    public function handlePaymentIntentCreated($event)
    {
    }

    public function handlePaymentIntentFailed($event)
    {
        Log::error('IncomingStripeController::handlePaymentIntentFailed', [
            'event' => $event,
        ]);
    }

    public function handlePaymentIntentSucceeded($event)
    {
    }

    public function handlePaymentIntentRequiresAction($event)
    {
        Log::error('IncomingStripeController::handlePaymentIntentRequiresAction', [
            'event' => $event,
        ]);
    }

    public function handlePaymentIntentPartiallyFunded($event)
    {
        Log::error('IncomingStripeController::handlePaymentIntentPartiallyFunded', [
            'event' => $event,
        ]);
    }

    public function handlePayoutUpdated($event)
    {
        Log::error('IncomingStripeController::handlePayoutUpdated', [
            'event' => $event,
        ]);
    }

}
