<?php

namespace App\Api\V1;

use App\Base\Http\Controllers\Controller;
use App\Models\Message;
use App\Models\MessageReceiver;
use App\Models\MessageSender;
use App\Scopes\AccountScope;

class IncomingBandwidthController extends Controller
{
    public function inboundMessage()
    {
        $cred = new \Catapult\Credentials(env('BANDWIDTH_USER_ID'), env('BANDWIDTH_TOKEN'), env('BANDWIDTH_SECRET'));

        $client = new \Catapult\Client($cred);

        try {
//            $message = (new \Catapult\Message())->send(
//                [
//                    "from"        => new \Catapult\PhoneNumber('1832XXXXXXX'),
//                    "to"          => new \Catapult\PhoneNumber('1281XXXXXXX'),
//                    "text"        => new \Catapult\TextMessage('Test message from Bandwidth! #1'),
//                    'callbackUrl' => 'https://api.memo.church/sms/incoming/bandwidth',
//                ]
//            );
            echo 'Message sent';
        } catch (\CatapultApiException $e) {
            echo var_dump($e);
        }
    }

    public function inboundVoice()
    {
        echo 'true';
    }

    public function getMessageReceiver($recipient)
    {
        return (new MessageReceiver())->withoutGlobalScope(new AccountScope())
            ->receiver(trim($recipient))
            ->ofType('email')
            ->isActive()
            ->first();
    }

    public function getMessageSender($sender, $account_id)
    {
        return MessageSender::withoutGlobalScope(new AccountScope())
            ->where('sender', trim($sender))
            // Only look in the account where we found the receiver.
            ->where('account_id', $account_id)
            ->first();
    }

    public function checkIfMessageExists($message_id)
    {
        return Message::withoutGlobalScope(new AccountScope())
            ->where('provider_transaction_id', str_replace(['<', '>'], '', $message_id))
            ->exists();
    }
}


/**
 * SAMPLE INCOMING SMS HOOK
 *
 * array (
 * 'messageId' => 'm-cll7ypyrlrddgmraubjz46y',
 * 'from' => '+1832XXXXXXX',
 * 'eventType' => 'sms',
 * 'text' => 'Test message from Bandwidth! #1',
 * 'time' => '2017-01-17T23:35:03Z',
 * 'to' => '+1281XXXXXXX',
 * 'state' => 'sent',
 * 'messageUri' => 'https://api.catapult.inetwork.com/v1/users/u-jovupgaii5ouesqhdcbjj5a/messages/m-cll7ypyrlrddgmraubjz46y',
 * 'direction' => 'out',
 * )
 */