<?php

namespace App\Public\Controllers;

use App\Accounts\Account;
use App\Base\Http\Controllers\Controller;
use App\Calendars\Calendar;
use App\Sermons\Sermon;
use Illuminate\Pagination\Paginator;

class PublicSermonController extends Controller
{
    public function home()
    {
        return redirect()->away('https://' . config('app.domains.frontend'));
    }

    public function jsCode($account_ulid)
    {
        $account = Account::where('ulid', $account_ulid)->firstOrFail();

        if (!$account || !$account->is_active) {
            abort(404);
        }

        $all_calendars = Calendar::visibleToAccountByAccount($account)
            ->isPublic()
            ->get();

        $view_string = view('public.sermons.js_code', [
            'account' => $account,
        ])->render();

        // We keep the <script> tags in the view so we can see code formatting in our IDE.  We remove it before serving it.
        $view_string = str_replace('<script type="text/javascript">', '', $view_string);
        $view_string = str_replace('</script>', '', $view_string);

        return response($view_string)
            ->header('Content-Type', 'application/javascript; charset=utf-8')
            ->header('Cross-Origin-Resource-Policy', 'cross-origin')
            ->header('Timing-Allow-Origin', '*')
            ->header('Access-Control-Allow-Origin', '*');
    }

    public function htmlCode($account_ulid)
    {
        $account = Account::where('ulid', $account_ulid)->firstOrFail();

        if (!$account || !$account->is_active) {
            abort(404);
        }

        Paginator::useTailwind();

        $sermons = Sermon::VisibleToAccount($account)
            ->when(request()->has('search_term'), function ($query) {
                $query->where('title', 'like', '%' . request()->get('search_term') . '%')
                    ->orWhere('speaker', 'like', '%' . request()->get('search_term') . '%');
            })
            ->isPublic()
            ->whereHas('files')
            ->orderBy('date_sermon', 'desc')
            ->paginate(30)
            ->setPath('');

        $view_string = view('public.sermons.html_code', [
            'account' => $account,
            'sermons' => $sermons,
        ])->render();

        return response($view_string)
            ->header('Content-Type', 'application/javascript; charset=utf-8')
            ->header('Cross-Origin-Resource-Policy', 'cross-origin')
            ->header('Timing-Allow-Origin', '*')
            ->header('Access-Control-Allow-Origin', '*');
    }
}
