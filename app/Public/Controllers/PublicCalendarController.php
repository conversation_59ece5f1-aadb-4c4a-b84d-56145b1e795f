<?php

namespace App\Public\Controllers;

use App\Accounts\Account;
use App\Base\Http\Controllers\Controller;
use App\Calendars\Calendar;
use App\Calendars\Services\CalendarFeed;

class PublicCalendarController extends Controller
{
    public function home()
    {
        return redirect()->away('https://' . config('app.domains.frontend'));
    }

    public function js_code($account_ulid)
    {
        $account = Account::where('ulid', $account_ulid)->firstOrFail();

        if (!$account || !$account->is_active) {
            abort(404);
        }

        $all_calendars = Calendar::visibleToAccountByAccount($account)
            ->isPublic()
            ->get();

        $view_string = view('public.calendars.js_code', [
            'all_calendars' => $all_calendars,
        ])->render();

        // We keep the <script> tags in the view so we can see code formatting in our IDE.  We remove it before serving it.
        $view_string = str_replace('<script type="text/javascript">', '', $view_string);
        $view_string = str_replace('</script>', '', $view_string);

        return response($view_string)
            ->header('Content-Type', 'application/javascript; charset=utf-8')
            ->header('Cross-Origin-Resource-Policy', 'cross-origin')
            ->header('Timing-Allow-Origin', '*')
            ->header('Access-Control-Allow-Origin', '*');
    }

    public function full_calendar_feed($calendar_ulid, $calendar_uuid = null)
    {
        $calendar = Calendar::withUlid($calendar_ulid)
            ->when($calendar_uuid, function ($query, $calendar_uuid) {
                return $query->where('uuid', $calendar_uuid);
            })
            ->isPublic()
            ->firstOrFail();

        if (!$calendar || !$calendar->account->is_active) {
            abort(404);
        }

        $events = (new CalendarFeed())
            ->forCalendar($calendar)
            ->getFullCalendarFeed();

        return response()
            ->json($events)
            ->header('Content-Type', 'application/javascript; charset=utf-8')
            ->header('Cross-Origin-Resource-Policy', 'cross-origin')
            ->header('Timing-Allow-Origin', '*')
            ->header('Access-Control-Allow-Origin', '*');
    }
}
