<?php

namespace App\Public\Controllers\Website;

use App\AccountFiles\Services\AccountFileGetter;
use App\Accounts\Account;
use App\App\Services\SendTransactionalEmail;
use App\Base\Http\Controllers\Controller;
use App\BibleClasses\BibleClass;
use App\Sermons\Sermon;
use App\Users\Photo;
use App\Website\Services\GetWebsiteSettingValue;
use App\Website\Services\WebsiteSettingService;
use App\Website\WebsitePage;
use App\Website\WebsiteSettingValue;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * This is the BaseController for all Admin controllers.  All Admin Controllers extend this class
 *
 * @package App\Http\Controllers\Admin
 */
class PublicWebsiteController extends Controller
{
    protected $template = 'template1';

    public function __construct()
    {
        Paginator::useTailwind();
    }

    public function debugHeaders()
    {
        return response()->json(request()->headers->all());
    }

    public function index()
    {
        $carousel = WebsiteSettingValue::where('account_id', request()->get('account_id'))
            ->whereHas('setting', function ($query) {
                $query->where('key', 'home.carousel');
            })
            ->first()
            ?->value;

        $page = (new WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.home');

        $featured_sermon = Sermon::where('account_id', request()->get('account_id'))
            ->where('is_public', true)
            ->find(8665);

        if ($page) {
            $page_content = $page['page']->html;
        } else {
            $page_content = null;
        }

        return view('website.' . $this->template . '.home.index')
            ->with('carousel', json_decode($carousel, true))
            ->with('page_content', $page_content ?: null)
            ->with('featured_sermon', $featured_sermon);
    }

    public function info()
    {
        return view('website.' . $this->template . '.info');
    }

    public function about()
    {
        $page = (new WebsiteSettingService(request()->get('account_id')))->getSpecialLink('special_links.about');

        if (!$page || !$page['page']) {
            abort(404);
        }
        return view('website.' . $this->template . '.page.index')
            ->with('page', $page['page']);
    }

    public function calendar()
    {
        return view('website.' . $this->template . '.calendar.index');
    }

    public function media()
    {
        $sermons = Sermon::where('account_id', request()->get('account_id'))
            ->isNotHidden()
            ->with('files:id,sermon_id,title,type,file_size,file_folder,file_name,file_extension,file_type,file_sha1')
            ->orderBy('date_sermon', 'desc')
            ->select([
                'id',
                'date_sermon',
                'language',
                'speaker',
                'title',
                'type',
                'description',
                'video_link',
                'podcast_link',
                'youtube_id',
                'youtube_link',
            ])
            ->when((request()->get('type') == 'title' && request()->get('search')), function ($query) {
                return $query->where('title', 'like', '%' . request()->get('search') . '%');
            })
            ->when(request()->get('title'), function ($query) {
                return $query->where('title', 'like', '%' . request()->get('title') . '%');
            })
            ->when((request()->get('type') == 'speaker' && request()->get('search')), function ($query) {
                return $query->where('speaker', 'like', '%' . request()->get('search') . '%');
            })
            ->when(request()->get('speaker'), function ($query) {
                return $query->where('speaker', 'like', '%' . request()->get('speaker') . '%');
            })
            ->when(request()->get('language'), function ($query) {
                return $query->where('language', request()->get('language'));
            })
            ->paginate(25);

        $sermons->map(function ($sermon) {
            $sermon->files->map(function ($file) {
                $file->link = $file->getCdnUrl();

                return $file;
            });

            return $sermon;
        });

        return view('website.' . $this->template . '.media.index')
            ->with('sermons', $sermons);
    }

    public function mediaSermon($sermon_title)
    {
        $sermon = Sermon::where('account_id', request()->get('account_id'))
            ->where('id', $sermon_title) // TODO: Change this to url_title when we add that field later
            ->with('files:id,sermon_id,title,type,file_size,file_folder,file_name,file_extension,file_type,file_sha1')
            ->first();

        if (!$sermon || $sermon->account_id != request()->get('account_id')) {
            abort(404);
        }

        if ($sermon->is_hidden || !$sermon->is_public) {
            abort(404);
        }

        return view('website.' . $this->template . '.media.sermon')
            ->with('sermon', $sermon);
    }

    public function page($page)
    {
        $page = WebsitePage::where('account_id', request()->get('account_id'))
            ->where('url_title', $page)
            ->first();

        return view('website.' . $this->template . '.page.index')
            ->with('page', $page);
    }

    public function leadership()
    {
        $leadership_enabled        = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
            ->get('leadership.enabled');
        $leadership_photos_enabled = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
            ->get('leadership.show_photos');

        if (!$leadership_enabled) {
            abort(404);
        }

        $leadership = Account::where('id', request()->get('account_id'))->first()
            ->churchOffices()
            ->ShownAsLeadership()
            ->isPublic()
            ->with('users', function ($query) {
                $query->select(['users.id', 'family_id', DB::raw('COALESCE(`preferred_first_name`, `first_name`) as `first_name`'), 'preferred_first_name', 'last_name'])
                    ->with([
                        'avatar' => function ($query) {
                            $query->select([
                                'user_id',
                                DB::raw((new Photo())->getMySQLConcatString(256) . ' AS `small_thumbnail_url`'),
                                DB::raw((new Photo())->getMySQLConcatString(512) . ' AS `thumbnail_url`'),
                            ]);
                        },
                    ]);
            })
            ->get();

        return view('website.' . $this->template . '.leadership.index')
            ->with('leadership_photos_enabled', $leadership_photos_enabled)
            ->with('leadership_enabled', $leadership_enabled)
            ->with('leadership', $leadership);
    }

    public function files()
    {
        $files = (new AccountFileGetter())
            ->forAccount(request()->get('account_id'))
            ->PublicOnly()
            ->get();

        return view('website.' . $this->template . '.files.index')
            ->with('files', $files->get());
    }

    public function privacyPolicy()
    {
        echo 'coming soon';
    }

    public function termsOfService()
    {
        echo 'coming soon';
    }

    public function contact()
    {
        $contact_enabled = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
            ->get('contact.enabled');

        if (!$contact_enabled) {
            abort(404);
        }

        return view('website.' . $this->template . '.contact.index')
            ->with('contact_enabled', $contact_enabled);
    }

    public function contactSubmit()
    {
        if (!$this->verifyTurnstileToken(request()->get('cf-turnstile-response'), $this->getClientIp())) {
            Log::info('Website::contactSubmit -- Turnstile failed.  Dropping the request.', [
                'ip'      => $this->getClientIp(),
                'name'    => request()->get('name'),
                'email'   => request()->get('email'),
                'phone'   => request()->get('phone'),
                'message' => request()->get('comment'),
            ]);
            return back()->with('message.failure', 'Oops! Something went wrong. Please try again.');
        } # Hidden field should not be filled in.
        elseif (!empty(request()->get('first_name')) || !empty(request()->get('address'))) {
            Log::info('Website::contactSubmit -- Nonempty first_name.  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } elseif (empty(request()->get('name')) || empty(request()->get('comment'))) {
            Log::info('Website::contactSubmit -- Empty name or comment.  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If there's a URL in the comment, this is junk.
        elseif (Str::contains(request()->get('comment'), ['http://', 'https://'])
                || Str::contains(request()->get('name'), ['http://', 'https://'])
                || Str::contains(request()->get('church'), ['http://', 'https://'])) {
            Log::info('Website::contactSubmit -- Attempt with a URL in the comments section.  Dropping the sign-up request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If the first name === last name, this is junk
        elseif (request()->get('name') == request()->get('last_name')) {
            Log::info('Website::contactSubmit -- Attempt with a first and last name the same.  Dropping the sign-up request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If the first or last name ends with "uhGM", this is junk
        elseif (Str::endsWith(request()->get('name'), 'uhGM') || Str::endsWith(request()->get('last_name'), 'uhGM')) {
            Log::info('Website::contactSubmit -- Attempt with known junk name (uhGM).  Dropping the sign-up request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If the address field is filled in, it's spam (address input is hidden)
        elseif (Str::length(request()->get('address')) > 0 || Str::length(request()->get('first_name')) > 0) {
            Log::info('Website::contactSubmit -- Attempt with an address filled in from hidden field.  Dropping the sign-up request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } else {
            $to_email = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
                ->get('contact.to_email');

            $account = Account::where('id', request()->get('account_id'))->first();

            $name    = request()->get('name');
            $email   = request()->get('email');
            $phone   = request()->get('phone');
            $message = request()->get('comment');

            dispatch(function () use ($to_email, $name, $email, $phone, $message, $account) {
                (new SendTransactionalEmail())
                    ->to($to_email)
                    ->type('website.forms.contact')
                    ->mergeData([
                        'name'         => $name ?: '',
                        'email'        => $email ?: '',
                        'phone'        => $phone ?: '',
                        'message'      => $message ?: '[empty]',
                        'ip_address'   => $this->getClientIp(),
                        'account_name' => $account->name,
                    ])
                    ->send();
            });
        }

        return redirect(url()->previous() . '?r=s');
    }

    public function prayerRequest()
    {
        $prayer_request_enabled = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
            ->get('prayer_requests.enabled');

        if (!$prayer_request_enabled) {
            abort(404);
        }

        return view('website.' . $this->template . '.prayer-request.index');
    }

    public function prayerRequestSubmit()
    {
        Log::info('Website::prayerRequestSubmit -- ' . request()->get('name') . ' -- ' . request()->get('email'));

        if (!$this->verifyTurnstileToken(request()->get('cf-turnstile-response'), $this->getClientIp())) {
            Log::info('Website::contactSubmit -- Turnstile failed.  Dropping the request.', [
                'ip'      => $this->getClientIp(),
                'name'    => request()->get('name'),
                'email'   => request()->get('email'),
                'phone'   => request()->get('phone'),
                'message' => request()->get('comment'),
            ]);
            return back()->with('message.failure', 'Oops! Something went wrong. Please try again.');
        } # Hidden field should not be filled in.
        elseif (!empty(request()->get('first_name')) || !empty(request()->get('address'))) {
            Log::info('Website::prayerRequestSubmit -- Nonempty first_name.  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } elseif (empty(request()->get('name')) || empty(request()->get('comment'))) {
            Log::info('Website::prayerRequestSubmit -- Empty name or comment.  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If there's a URL in the comment, this is junk.
        elseif (Str::contains(request()->get('comment'), ['http://', 'https://'])
                || Str::contains(request()->get('name'), ['http://', 'https://'])
                || Str::contains(request()->get('church'), ['http://', 'https://'])) {
            Log::info('Website::prayerRequestSubmit -- Attempt with a URL in the comments section.  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If the first name === last name, this is junk
        elseif (request()->get('name') == request()->get('last_name')) {
            Log::info('Website::prayerRequestSubmit -- Attempt with a first and last name the same.  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If the first or last name ends with "uhGM", this is junk
        elseif (Str::endsWith(request()->get('name'), 'uhGM') || Str::endsWith(request()->get('last_name'), 'uhGM')) {
            Log::info('Website::prayerRequestSubmit -- Attempt with known junk name (uhGM).  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } # If the address field is filled in, it's spam (address input is hidden)
        elseif (Str::length(request()->get('address')) > 0 || Str::length(request()->get('first_name')) > 0) {
            Log::info('Website::prayerRequestSubmit -- Attempt with an address filled in from hidden field.  Dropping the request.', [
                'ip'         => $this->getClientIp(),
                'first_name' => request()->get('first_name'),
                'name'       => request()->get('name'),
                'email'      => request()->get('email'),
                'phone'      => request()->get('phone'),
                'message'    => request()->get('comment'),
            ]);
        } else {
            $to_email = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
                ->get('prayer_requests.to_email');

            $account = Account::where('id', request()->get('account_id'))->first();

            $name    = request()->get('name');
            $email   = request()->get('email');
            $phone   = request()->get('phone');
            $message = request()->get('comment');

            dispatch(function () use ($to_email, $name, $email, $phone, $message, $account) {
                (new SendTransactionalEmail())
                    ->to($to_email)
                    ->type('website.forms.prayer_request')
                    ->mergeData([
                        'name'         => $name ?: '',
                        'email'        => $email ?: '',
                        'phone'        => $phone ?: '',
                        'message'      => $message ?: '[empty]',
                        'ip_address'   => $this->getClientIp(),
                        'account_name' => $account->name,
                    ])
                    ->send();
            });
        }

        return redirect(url()->previous() . '?r=s');
    }

    public function bibleClassRegistration()
    {
        $class_registration = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
            ->get('bible_class_registration.enabled');

        if (!$class_registration) {
            abort(404);
        }

        $account = Account::where('id', request()->get('account_id'))->first();

        $bible_class_group = $account->activeSignupBibleClassGroup();
        $attendance_types  = $bible_class_group->classes()
            ->with('attendanceType')
            ->get()
            ->pluck('attendanceType')
            ->unique('id');

        return view('website.' . $this->template . '.bible-class.registration')
            ->with('class_registration', $class_registration)
            ->with('bible_class_group', $bible_class_group)
            ->with('attendance_types', $attendance_types);
    }

    public function bibleClassRegistrationSubmit()
    {
//        Log::info('Website::bibleClassRegistrationSubmit -- ' . request()->get('lightpost_name') . ' -- ' . request()->get('lightpost_email'));

        if (!$this->verifyTurnstileToken(request()->get('cf-turnstile-response'), $this->getClientIp())) {
            Log::info('Website::contactSubmit -- Turnstile failed.  Dropping the request.', [
                'ip'    => $this->getClientIp(),
                'name'  => request()->get('name'),
                'email' => request()->get('email'),
            ]);
            return back()->with('message.failure', 'Oops! Something went wrong. Please try again.');
        } # Hidden field should not be filled in.
        elseif (!empty(request()->get('first_name')) || !empty(request()->get('address'))) {
            Log::info('Website::bibleClassRegistrationSubmit -- Nonempty first_name.  Dropping the request.', [
                'ip'              => $this->getClientIp(),
                'first_name'      => request()->get('first_name'),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } # Expected hidden field
        elseif (request()->get('sub_19493') != 'sub_11492') {
            Log::info('Website::bibleClassRegistrationSubmit -- Missing hidden field sub_19493.  Dropping the request.', [
                'ip'              => $this->getClientIp(),
                'first_name'      => request()->get('first_name'),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } elseif (empty(request()->get('selected_classes_ids'))) {
            Log::info('Website::bibleClassRegistrationSubmit -- Empty selected classes.  Dropping the request.', [
                'ip'              => $this->getClientIp(),
                'first_name'      => request()->get('first_name'),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } # If there's a URL in the comment, this is junk.
        elseif (Str::contains(request()->get('comment'), ['http://', 'https://'])
                || Str::contains(request()->get('lightpost_name'), ['http://', 'https://'])
                || Str::contains(request()->get('first_name'), ['http://', 'https://'])) {
            Log::info('Website::bibleClassRegistrationSubmit -- Attempt with a URL in a field.  Dropping the registration request.', [
                'ip'              => $this->getClientIp(),
                'first_name'      => request()->get('first_name'),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } # If the first name === last name, this is junk
        elseif (request()->get('lightpost_name') == request()->get('last_name')) {
            Log::info('Website::bibleClassRegistrationSubmit -- Attempt with a first and last name the same.  Dropping the registration request.', [
                'ip'              => $this->getClientIp(),
                'first_name'      => request()->get('first_name'),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } # If the first or last name ends with "uhGM", this is junk
        elseif (Str::endsWith(request()->get('lightpost_name'), 'uhGM') || Str::endsWith(request()->get('last_name'), 'uhGM')) {
            Log::info('Website::bibleClassRegistrationSubmit -- Attempt with known junk name (uhGM).  Dropping the registration request.', [
                'ip'              => $this->getClientIp(),
                'first_name'      => request()->get('first_name'),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } # If the address field is filled in, it's spam (address input is hidden)
        elseif (Str::length(request()->get('address')) > 0 || Str::length(request()->get('first_name')) > 0) {
            Log::info('Website::bibleClassRegistrationSubmit -- Attempt with an address filled in from hidden field.  Dropping the registration request.', [
                'ip'              => $this->getClientIp(),
                'first_name'      => request()->get('first_name'),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } elseif (preg_match_all('/[A-Z]/', request()->get('lightpost_name')) > 4) {
            Log::info('Website::bibleClassRegistrationSubmit -- Attempt with too many capital letters in lightpost_name.  Dropping the registration request.', [
                'ip'              => $this->getClientIp(),
                'lightpost_name'  => request()->get('lightpost_name'),
                'lightpost_email' => request()->get('lightpost_email'),
            ]);
        } else {
            $to_email = (new GetWebsiteSettingValue())->forAccount(request()->get('account_id'))
                ->get('bible_class_registration.to_email');

            $account = Account::where('id', request()->get('account_id'))->first();

            $classes = [];

            foreach (request()->get('selected_classes_ids') as $attendance_type_id => $class_ids):
                foreach ($class_ids as $class_id):
                    if (is_numeric($class_id)) {
                        $bible_class = BibleClass::where('account_id', request()->get('account_id'))->find($class_id);
                        $classes[]   = [
                            'name' => $bible_class->title,
                        ];
                    }
                endforeach;
            endforeach;

            $lightpost_name  = request()->get('lightpost_name');
            $lightpost_email = request()->get('lightpost_email');

            dispatch(function () use ($to_email, $classes, $lightpost_name, $lightpost_email, $account) {
                (new SendTransactionalEmail())
                    ->to($to_email)
                    ->type('website.forms.bible_class_registration')
                    ->mergeData([
                        'date'         => now()->format('l, F j, Y \@ g:ia'),
                        'name'         => $lightpost_name ?: '[not provided]',
                        'email'        => $lightpost_email ?: '[not provided]',
                        'classes'      => $classes,
                        'ip_address'   => $this->getClientIp(),
                        'account_name' => $account->name,
                    ])
                    ->send();
            });
        }

        return redirect(url()->previous() . '?r=s');
    }

    // Add this method to your controller
    private function getClientIp()
    {
        $ip = request()->header('X-Real-IP');
        if ($ip) {
            // This is from our Caddy configuration with Lightpost websites.
            return $ip;
        }

        // Fallback to other common headers
        return request()->header('X-Forwarded-For')
               ?? request()->server('REMOTE_ADDR')
                  ?? $this->getClientIp();
    }

    private function verifyTurnstileToken($token, $ip)
    {
        $secretKey = config('services.cloudflare.secret_key');

        $response = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
            'secret'   => $secretKey,
            'response' => $token,
            'remoteip' => $ip,
        ]);

        return $response->json() ?? false;
    }
}
