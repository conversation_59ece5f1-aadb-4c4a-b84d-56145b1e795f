<?php

namespace App\Public\Controllers\Website;

use App\Base\Http\Controllers\Controller;
use App\Website\WebsitePage;
use Illuminate\Pagination\Paginator;

/**
 * This is the BaseController for all Admin controllers.  All Admin Controllers extend this class
 *
 * @package App\Http\Controllers\Admin
 */
class PublicWebsiteRedirectController extends Controller
{
    // This is the last route and "catch all" for the website.  If no other route is matched, this is the final route.
    public function handleFinalRoute()
    {
        $page = WebsitePage::where('account_id', request()->get('account_id'))
            ->isRedirect()
            ->where('redirect_entry_path', trim(request()->path(), '/'))
            ->first();

        if ($page) {
            return redirect($page->getRedirectUrl(), 301);
        } else {
            abort(404);
        }
    }
}
