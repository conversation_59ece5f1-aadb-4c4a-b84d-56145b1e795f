<?php

namespace App\Livewire\App\Visitors;

use App\Visitors\History;
use App\Visitors\Services\ArchiveVisitor;
use App\Visitors\Services\CreateHistory;
use App\Visitors\Visitor;
use Livewire\Component;

class ViewVisitor extends Component
{
    public Visitor $visitor;
    public         $edit_mode                = false;
    public         $background_info          = false;
    public         $first_contact_at         = null;
    public         $last_contact_at          = null;
    public         $new_comment_comment      = null;
    public         $new_comment_date         = null;
    public         $new_comment_history_type = null;

    public $edit_history_item_id      = null;
    public $history_edit_notes        = null;
    public $history_edit_date         = null;
    public $history_edit_history_type = null;

    protected $listeners = [
        'refreshViewVisitor' => '$refresh',
    ];

    public function mount()
    {
        // Set the initial value for our dropdown.
        $this->new_comment_history_type = array_key_first(History::$history_types);

        $this->background_info  = $this->visitor->background_info;
        $this->first_contact_at = $this->visitor->first_contact_at?->setTimezone(auth()->user()->account->timezone)->format('Y-m-d');
        $this->last_contact_at  = $this->visitor->last_contact_at?->setTimezone(auth()->user()->account->timezone)->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.app.visitors.view-visitor');
    }

    public function changeStatus($new_status_id)
    {
        $this->visitor->visitor_status_id = $new_status_id;

        $this->visitor->save();

        $this->dispatch('refreshViewVisitor');
    }

    public function changeSubscription()
    {
        if ($this->visitor->userIsSubscribed(auth()->user())) {
            $this->visitor->subscribers()->detach(auth()->user()->id);
        } else {
            $this->visitor->subscribers()->attach(auth()->user()->id);
        }

        $this->dispatch('refreshViewVisitor');
    }

    public function enableEditMode()
    {
        $this->edit_mode = true;
    }

    public function editSubmit()
    {
        $this->visitor->background_info  = $this->background_info;
        $this->visitor->first_contact_at = $this->first_contact_at;
        $this->visitor->last_contact_at  = $this->last_contact_at;
        $this->visitor->save();

        $this->edit_mode = false;
        $this->dispatch('refreshViewVisitor');
    }

    public function editHistory($history_id)
    {
        // If we're in some other mode, turn it off.
        $this->edit_mode = false;

        $this->edit_history_item_id = $history_id;

        $history = History::visibleTo(auth()->user())
            ->where('created_by_user_id', auth()->user()->id)
            ->find($history_id);

        $this->history_edit_notes        = $history->notes;
        $this->history_edit_history_type = $history->type;
    }

    public function editHistorySubmit($history_id)
    {
        $history = History::visibleTo(auth()->user())
            ->where('created_by_user_id', auth()->user()->id)
            ->find($history_id);

        $history->notes = $this->history_edit_notes;
        $history->type  = $this->history_edit_history_type;

        if ($this->history_edit_date) {
            $history->created_at = $this->history_edit_date;
        }

        $history->save();

        $this->edit_history_item_id      = null;
        $this->history_edit_notes        = null;
        $this->history_edit_history_type = null;

        $this->dispatch('refreshViewVisitor');
    }

    public function cancelHistoryEdit()
    {
        $this->edit_history_item_id      = null;
        $this->history_edit_notes        = null;
        $this->history_edit_history_type = null;

        $this->dispatch('refreshViewVisitor');
    }

    public function submitNote()
    {
        (new CreateHistory())
            ->forVisitor($this->visitor)
            ->createdAt($this->new_comment_date)
            ->withNotes($this->new_comment_comment)
            ->withType($this->new_comment_history_type)
            ->createdBy(auth()->user())
            ->create();

        $this->new_comment_comment      = null;
        $this->new_comment_history_type = array_key_first(History::$history_types);

        // Emails sent via HistoryObserver

        $this->dispatch('refreshViewVisitor');
    }

    public function archiveVisitorRecord()
    {
        (new ArchiveVisitor($this->visitor))
            ->byUser(auth()->user())
            ->archive();

        return redirect()->route('app.visitors.index')
            ->with('message.success', 'Visitor archived successfully.');
    }
}
