<?php

namespace App\Livewire\App\Visitors;

use App\Users\User;
use Livewire\Component;

class CreateVisitor extends Component
{
    public $search_individual = null;
    public $search_family     = null;

    protected $listeners = [
        'refreshCreateVisitor' => '$refresh',
        'selectExistingUser',
    ];

    public function render()
    {
        $existing_individual_users = [];
        $existing_family_users     = [];

        if ($this->search_individual) {
            $existing_individual_users = User::visibleTo(auth()->user())
                ->select([
                    'id',
                    'account_id',
                    'first_name',
                    'last_name',
                    'family_id',
                    'family_role',
                ])
                ->visitorsOnly()
                ->isNotDeceased()
                ->when($this->search_individual, function ($query) {
                    $query->SearchNameByString($this->search_individual);
                })
                ->limit(8)
                ->get();
        }

        if ($this->search_family) {
            $existing_family_users = User::visibleTo(auth()->user())
                ->select([
                    'id',
                    'account_id',
                    'first_name',
                    'last_name',
                    'family_id',
                    'family_role',
                ])
                ->visitorsOnly()
                ->headsofFamily()
                ->isNotDeceased()
                ->when($this->search_family, function ($query) {
                    $query->SearchNameByString($this->search_family);
                })
                ->limit(8)
                ->get();
        }

        return view('livewire.app.visitors.create-visitor')
            ->with('existing_individual_users', $existing_individual_users)
            ->with('existing_family_users', $existing_family_users);
    }

    public function selectExistingUser($selected_existing_user)
    {
        $visitor = (new \App\Visitors\Services\CreateVisitor())
            ->createdBy(auth()->user())
            ->forUserId($selected_existing_user)
            ->create();

        return redirect()->to(route('app.visitors.view', $visitor));
    }

    public function selectExistingFamily($selected_existing_family)
    {
        $visitor = (new \App\Visitors\Services\CreateVisitor())
            ->createdBy(auth()->user())
            ->forFamilyId($selected_existing_family)
            ->create();

        return redirect()->to(route('app.visitors.view', $visitor));
    }
}
