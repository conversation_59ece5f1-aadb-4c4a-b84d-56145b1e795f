<?php

namespace App\Livewire\App\Groups\Posts;

use App\Groups\Services\CreateGroupFile;
use App\Groups\Services\CreatePost as CreatePostService;
use App\Users\Group;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithFileUploads;

class CreatePost extends Component
{
    use AuthorizesRequests, WithFileUploads;

    public Group $group;
    public array $files          = [];
    public array $uploaded_files = [];
    public       $content;

    protected function getListeners()
    {
        return [];
    }

    public function render()
    {
        if (auth()->user()->can('post', $this->group)) {
            return view('livewire.app.groups.posts.create-post');
        } else {
            return '<div x-cloak></div>';
        }
    }

    public function updatedFiles()
    {
        $this->validate([
            'files.*' => 'file|max:20480', // 20MB max per file
        ]);

        foreach ($this->files as $file) {
            // We keep our own array of uploaded files so we can clean them up later.
            // Leave the file in the temp directory for now.
            $this->uploaded_files[] = [
                'name'          => $file->getClientOriginalName(),
                'size'          => $file->getSize(),
                'mime_type'     => $file->getMimeType(),
                'extension'     => $file->extension() ?: $file->guessExtension(),
                'temp_path'     => $file->getRealPath(), // Store the ACTUAL temporary path
                // Optional: Store the temporary filename Livewire used, if needed for cleanup later
                'temp_filename' => $file->getFilename(),
            ];
        }

        $this->reset('files');
    }

    public function clearFiles()
    {
        // Use the stored real path to unlink the temporary files
        foreach ($this->uploaded_files as $file) {
            $path_to_unlink = Arr::get($file, 'temp_path');
            if ($path_to_unlink && file_exists($path_to_unlink)) {
                unlink($path_to_unlink);
            }
        }

        $this->reset('uploaded_files');
        $this->reset('files');
    }

    public function submit()
    {
        $this->validate([
            'content' => 'required',
        ]);

        $new_post = (new CreatePostService())
            ->createdBy(auth()->user())
            ->forGroup($this->group)
            ->create(['content' => $this->content]);

        foreach ($this->uploaded_files as $file) {
            $temp_path = Arr::get($file, 'temp_path'); // This is now the real temp path

            try {
                if (!$temp_path || !file_exists($temp_path)) {
                    Log::error("Temporary file not found or path missing.", ['file_info' => $file]);
                    continue; // Skip this file if path is invalid or file doesn't exist
                }

                (new CreateGroupFile())
                    ->forAccount(auth()->user()->account)
                    ->byUser(auth()->user())
                    ->forPost($new_post)
                    ->setFileName(Arr::get($file, 'name'))
                    ->setFileSize(Arr::get($file, 'size'))
                    ->setFileType(Arr::get($file, 'mime_type'))
                    ->create(file_get_contents($temp_path)); // Use the real temp path

                // Clean up the temp file after successful processing
                if (file_exists($temp_path)) {
                    unlink($temp_path);
                }
            } catch (\Exception $e) {
                Log::error("Failed to process uploaded file", ['exception' => $e, 'file_info' => $file]);
                // Optionally try to clean up even if processing failed
                if ($temp_path && file_exists($temp_path)) {
                    unlink($temp_path);
                }
            }
        }

        $this->reset('uploaded_files');
        $this->reset('content');

        // Optional: Redirect or emit event
        return redirect()->route('app.groups.view', $this->group);
    }
}
