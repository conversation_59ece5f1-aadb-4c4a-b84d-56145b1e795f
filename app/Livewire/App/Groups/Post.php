<?php

namespace App\Livewire\App\Groups;

use App\Groups\Comment;
use App\Groups\Services\CreateComment;
use App\Groups\Services\RecordCommentReaction;
use App\Groups\Services\RecordPostReaction;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class Post extends Component
{
    use AuthorizesRequests;

    public \App\Groups\Post $post;
    public                  $newCommentContent;
    public                  $delete_mode       = false;
    public                  $edit_mode         = false;
    public                  $delete_comment_id = null;

    protected function getListeners()
    {
        return [
            'refreshPost.' . $this->post->id => '$refresh',
        ];
    }

    public function render()
    {
        // If we're about to render without counts (when updating the Post), get those counts.
        if ($this->post->reactions_count === null) {
            $this->post = \App\Groups\Post::with(['creator', 'reactions', 'comments', 'comments.creator'])
                ->withReactionCounts()
                ->withCount(['comments'])
                ->find($this->post->id);
        }

        return view('livewire.app.groups.post');
    }

    public function pinPost()
    {
        $this->authorize('edit', $this->post);

        $this->post->is_pinned = true;
        $this->post->save();

        return redirect(request()->header('Referer'));
    }

    public function unpinPost()
    {
        $this->authorize('edit', $this->post);

        $this->post->is_pinned = false;
        $this->post->save();

        return redirect(request()->header('Referer'));
    }

    public function likePost($type = 'like')
    {
        $this->authorize('like', $this->post);

        (new RecordPostReaction())
            ->forPost($this->post)
            ->forUser(auth()->user())
            ->withType($type)
            ->record();
    }

    public function unlikePost()
    {
        $this->authorize('like', $this->post);

        (new RecordPostReaction())
            ->forPost($this->post)
            ->forUser(auth()->user())
            ->withType('delete')
            ->record();
    }

    public function likeComment($comment_id, $type = 'like')
    {
        $comment = Comment::find($comment_id);

        $this->authorize('like', $comment);

        (new RecordCommentReaction())
            ->forComment($comment)
            ->forUser(auth()->user())
            ->withType($type)
            ->record();
    }

    public function unlikeComment($comment_id)
    {
        $comment = Comment::find($comment_id);

        $this->authorize('like', $comment);

        (new RecordCommentReaction())
            ->forComment($comment)
            ->forUser(auth()->user())
            ->withType('delete')
            ->record();
    }

    public function newComment()
    {
        if (!$this->post->allow_comments || !$this->post->group->allow_members_to_comment) {
            abort(400);
        }

        $this->authorize('comment', $this->post);

        $validatedData = $this->validate([
            'newCommentContent' => 'required',
        ]);

        try {
            $new_comment = (new CreateComment())
                ->forPost($this->post)
                ->createdBy(auth()->user())
                ->withComment(Arr::get($validatedData, 'newCommentContent'))
                ->create();
        } catch (\Exception $e) {
            Log::error($e);

            abort(400, $e->getMessage());
        }
    }

    public function deletePostMode($mode = 'off')
    {
        if ($mode == 'on') {
            $this->delete_mode = true;
        } else {
            $this->delete_mode = false;
        }
    }

    public function deletePost()
    {
        $this->authorize('delete', $this->post);

        $this->post->delete();

        return redirect(request()->header('Referer'))
            ->with('message.success', 'Post deleted.');
    }

    public function deleteCommentMode($comment_id = null)
    {
        if ($comment_id) {
            $this->delete_comment_id = $comment_id;
        } else {
            $this->delete_comment_id = null;
        }
    }

    public function deleteComment()
    {
        $comment = Comment::visibleTo(auth()->user())->find($this->delete_comment_id);

        if ($comment) {
            $this->authorize('delete', $comment);

            $comment->delete();
        }

        $this->dispatch('refreshPost.' . $this->post->id);
    }
}
