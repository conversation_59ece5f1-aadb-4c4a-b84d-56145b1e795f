<?php

namespace App\Livewire\App\Calendars;

use App\Calendars\Services\UpdateEventOccurrenceResponses;
use Livewire\Component;

class ViewEventOccurrence extends Component
{
    public $event;
    public $eventOccurrence;

    public function render()
    {
        return view('livewire.app.calendars.view-event-occurrence');
    }

    public function addResponse($user_id, $response)
    {
        (new UpdateEventOccurrenceResponses($this->eventOccurrence))
            ->createdBy(auth()->user()->id)
            ->updateByUserId($user_id, $response)
            ->update();

    }

    public function removeResponse($user_id, $response)
    {
        (new UpdateEventOccurrenceResponses($this->eventOccurrence))
            ->createdBy(auth()->user()->id)
            ->removeByUserId($user_id, $response)
            ->update();

    }
}
