<?php

namespace App\Livewire\App\Kiosk\ChildCheckin;

use App\ChildCheckins\ChildCheckin;
use App\ChildCheckins\Services\CheckinChild;
use App\ChildCheckins\Services\CheckoutChild;
use App\Users\User;
use Livewire\Component;

class Checkins extends Component
{
    public $invalid_barcode = false;

    protected $listeners = [
        'refreshCheckins'     => '$refresh',
        'checkinViaQr'        => 'checkinViaQr',
        'checkoutViaLivewire' => 'checkout',
        'checkinViaLivewire'  => 'checkin',
    ];

    public function render()
    {
        return view('livewire.app.kiosk.child-checkin.checkins', [
            'checkins'         => ChildCheckin::visibleTo(auth()->user())->isCheckedIn()->orderBy('created_at', 'desc')->get(),
            'recent_checkouts' => ChildCheckin::visibleTo(auth()->user())->recentlyCheckedOut()->orderBy('created_at', 'DESC')->get(),
        ]);
    }

    // QR Code Format:  See info at User->createChildCheckinBarcodeString()
    public function checkinViaQr($qr_string)
    {
        $this->invalid_barcode = false;

        if (!ChildCheckin::validateBarcode($qr_string)) {
            $this->invalid_barcode = true;
        } else {
            $checkin = ChildCheckin::findByBarcode($qr_string);

            if (!$checkin) {
                (new CheckinChild())
                    ->withBarcode($qr_string)
                    ->checkin();
            } else {
                $this->checkAuth($checkin->account_id);

                (new CheckoutChild())
                    ->withBarcode($qr_string)
                    ->checkout($checkin);
            }
        }

        $this->dispatch('checkins-refreshed');
    }

    public function checkin($child_user_id)
    {
        $child = User::visibleTo(auth()->user())->find($child_user_id);

        // Make sure we're not already checked in.
        $checkin = ChildCheckin::findByChildUserId($child_user_id, auth()->user()->account_id);

        if (!$checkin) {
            $this->checkAuth($child->account_id);

            (new CheckinChild())
                ->forChild($child)
                ->checkin();

            $this->dispatch('checkins-refreshed');
        }
    }

    public function checkout(ChildCheckin $child_checkin, $checkout_by_user_id = null)
    {
        $this->checkAuth($child_checkin->account_id);

        (new CheckoutChild())
            ->checkoutByUser($checkout_by_user_id)
            ->checkout($child_checkin);

        $this->dispatch('checkins-refreshed');
    }

    private function checkAuth($account_id)
    {
        if ($account_id != auth()->user()->account_id) {
            abort(403);
        }
    }
}
