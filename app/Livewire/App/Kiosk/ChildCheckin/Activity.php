<?php

namespace App\Livewire\App\Kiosk\ChildCheckin;

use Livewire\Component;

class Activity extends Component
{
    protected $listeners = ['refreshActivity' => '$refresh'];

    public function render()
    {
        return view('livewire.app.kiosk.child-checkin.activity')
            ->with('activity', \App\ChildCheckins\Activity::visibleTo(auth()->user())->recentOnly()->orderBy('created_at', 'DESC')->get());
    }
}
