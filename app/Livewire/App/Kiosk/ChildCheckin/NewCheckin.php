<?php

namespace App\Livewire\App\Kiosk\ChildCheckin;

use App\ChildCheckins\Services\CheckinChild;
use App\Users\Phone;
use App\Users\Services\CreateUser;
use App\Users\User;
use Illuminate\Support\Arr;
use Livewire\Component;

class New<PERSON>heckin extends Component
{
    public $search_string               = null;
    public $parent_search_first_name    = null;
    public $parent_search_last_name     = null;
    public $selected_existing_parent    = null;
    public $parent_search_mobile_number = null;
    public $child_first_name            = null;
    public $child_last_name             = null;
    public $child_allergies             = null;
    public $child_special_needs         = null;

    protected $listeners = [
        'refreshCheckins' => '$refresh',
        'selectExistingParent',
        'checkinNewVisitor',
    ];

    public function render()
    {
        $children = [];
        $parents  = [];

        if ($this->search_string && strlen($this->search_string) > 1) {
            // In case we're searching "Drew Johnston"... include both names in the search.
            $strings = explode(' ', $this->search_string);

            $string1 = Arr::get($strings, 0, null);
            $string2 = Arr::get($strings, 1, null);

            $children = User::visibleTo(auth()->user())
                ->select([
                    'id',
                    'account_id',
                    'first_name',
                    'last_name',
                    'family_id',
                ])
                ->childrenOnly()
                ->isNotDeceased()
                ->when((!empty($string1) && empty($string2)), function ($query) use ($string1) {
                    $query->where(function ($query2) use ($string1) {
                        $query2->where('last_name', 'like', $string1 . '%')
                            ->orWhere('first_name', 'like', $string1 . '%');
                    });
                })
                ->when((!empty($string1) && !empty($string2)), function ($query) use ($string1) {
                    $query->where('first_name', 'like', $string1 . '%');
                })
                ->when((!empty($string2) && !empty($string1)), function ($query) use ($string2) {
                    $query->where('last_name', 'like', $string2 . '%');
                })
                ->limit(6)
                ->get();
        }
        if ($this->parent_search_first_name && strlen($this->parent_search_first_name) > 1) {
            $parents = User::visibleTo(auth()->user())
                ->select([
                    'id',
                    'account_id',
                    'first_name',
                    'last_name',
                    'family_id',
                ])
                ->adultsOnly()
                ->isNotDeceased()
                ->when((!empty($this->parent_search_first_name) && empty($string2)), function ($query) {
                    $query->where(function ($query2) {
                        $query2->where('last_name', 'like', $this->parent_search_first_name . '%')
                            ->orWhere('first_name', 'like', $this->parent_search_first_name . '%');
                    });
                })
                ->when((!empty($this->parent_search_first_name) && !empty($this->parent_search_last_name)), function ($query) {
                    $query->where('first_name', 'like', $this->parent_search_first_name . '%');
                })
                ->when((!empty($this->parent_search_last_name) && !empty($this->parent_search_first_name)), function ($query) {
                    $query->where('last_name', 'like', $this->parent_search_last_name . '%');
                })
                ->limit(3)
                ->get();
        }

        return view('livewire.app.kiosk.child-checkin.new-checkin')
            ->with('children', $children)
            ->with('existing_parent_selected', User::visibleTo(auth()->user())->find($this->selected_existing_parent))
            ->with('parents', $parents);
    }

    public function checkinNewVisitor()
    {
        $parent = null;

        if ($this->selected_existing_parent > 0) {
            $parent = User::visibleTo(auth()->user())->find($this->selected_existing_parent);
        }

        if (!$parent) {
            // Create our user.
            $parent = (new CreateUser())
                ->forAccount(auth()->user()->account)
                ->create([
                    'family_role' => 'head',
                    'first_name'  => $this->parent_search_first_name,
                    'last_name'   => $this->parent_search_last_name,
                ]);

            // Assign them to default visitor group/role
            $group = auth()->user()->account->getDefaultVisitorGroup();
            if ($group) {
                $parent->groups()->sync($group->id);
            }
            // Needed ?
//            $role = auth()->user()->account->getDefaultVisitorRole();
//            if ($role) {
//                $parent->roles()->sync($role->id);
//            }

            $phone = new Phone();

            $phone->user_id = $parent->id;
            $phone->number  = Phone::format($this->parent_search_mobile_number);

            $phone->is_primary = 1;
            $phone->type       = 'mobile';

            $phone->save();
        }

        // If we still have no parent, abort.
        if (!$parent) {
            abort(404);
        }

        // Create our new child.
        $child = (new CreateUser())
            ->forAccount(auth()->user()->account)
            ->create([
                'family_role'   => 'child',
                'first_name'    => $this->child_first_name,
                'last_name'     => $this->child_last_name,
                'allergies'     => $this->child_allergies,
                'special_needs' => $this->child_special_needs,
                'family_id'     => $parent->family_id,
            ]);
        $group = auth()->user()->account->getDefaultVisitorGroup();
        if ($group) {
            $child->groups()->sync($group->id);
        }

        // Reset everything for next time.
        $this->parent_search_first_name    = null;
        $this->parent_search_last_name     = null;
        $this->selected_existing_parent    = null;
        $this->parent_search_mobile_number = null;
        $this->child_first_name            = null;
        $this->child_last_name             = null;
        $this->child_allergies             = null;
        $this->child_special_needs         = null;

        // Checkin our child.
        (new CheckinChild())
            ->forChild($child)
            ->checkin();

        // All done, let the frontend know.
        $this->dispatch('close-new-checkin-modal');
        $this->dispatch('checkins-refreshed');
    }

    public function updateSearch($search_string)
    {
        $this->search_string = $search_string;
    }

    public function updateParentSearch($parent_search_first_name, $parent_search_last_name)
    {
        $this->parent_search_first_name = $parent_search_first_name;
        $this->parent_search_last_name  = $parent_search_last_name;
    }

    public function selectExistingParent($selected_existing_parent)
    {
        $this->selected_existing_parent = $selected_existing_parent;
    }
}
