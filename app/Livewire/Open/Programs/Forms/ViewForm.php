<?php

namespace App\Livewire\Open\Programs\Forms;

use App\Accounts\Account;
use App\Programs\Form;
use App\Programs\FormFieldResponse;
use App\Programs\ProgramGroup;
use App\Programs\Services\CreateProgramUser;
use App\Programs\Services\UpdateProgramUser;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class ViewForm extends Component
{

    public $form;
    public $program;
    public $step                   = 1;
    public $registrants            = [];
    public $contacts               = [];
    public $currentRegistrantIndex = 0;
    public $currentContactIndex    = 0;
    public $has_error              = null;
    public $userFields             = [];
    public $contactFields          = [];
    public $registration_limit_per_registration;
    public $submit_success         = false;

    public function mount(Form $form)
    {
        $this->form                                = $form;
        $this->program                             = $form->program;
        $this->registration_limit_per_registration = $form->registration_limit_per_registration ?? 10; // Default to 10 if not set
        $required_registrant_fields                = $form->required_user_fields ?? [];
        $required_contact_fields                   = $form->required_contact_fields ?? [];

        $this->userFields    = array_unique(array_merge($required_registrant_fields, $form->optional_user_fields ?? []));
        $this->contactFields = array_unique(array_merge($required_contact_fields, $form->optional_contact_fields ?? []));

        $this->addRegistrant();
    }

    public function render()
    {
        if ($this->step === 1) {
            return view('livewire.open.programs.forms.step1');
        } elseif ($this->step === 2) {
            return view('livewire.open.programs.forms.step2');
        } elseif ($this->step === 3) {
            return view('livewire.open.programs.forms.step3');
        } elseif ($this->step === 4) {
            return view('livewire.open.programs.forms.step4');
        }
    }

    public function addRegistrant()
    {
        if (count($this->registrants) < ($this->registration_limit_per_registration > 0 ? $this->registration_limit_per_registration : 1000)) {
            $userFieldsData = [];
            foreach ($this->userFields as $field) {
                $userFieldsData[$field] = '';
            }

            $uniqueId = uniqid('registrant_'); // Generate a unique ID for each registrant

            $this->registrants[$uniqueId] = [
                'id'          => $uniqueId,
                'user_fields' => $userFieldsData,
                'fields'      => $this->form->registrantFields->mapWithKeys(function ($field, $index) {
                    return [
                        $field->id => [
                            'field_id'       => $field->id,
                            'response'       => null,
                            'multi_response' => [],
                        ],
                    ];
                })->toArray(),
            ];
            $this->currentRegistrantIndex = $uniqueId;
        }
    }

    public function removeRegistrant($uniqueId)
    {
        if (isset($this->registrants[$uniqueId])) {
            unset($this->registrants[$uniqueId]);

            if (empty($this->registrants)) {
                $this->addRegistrant();
            } else {
                // Adjust currentRegistrantIndex to the last registrant or another valid key
                $keys = array_keys($this->registrants);
                if (in_array($this->currentRegistrantIndex, $keys)) {
                    // Keep current index if it still exists
                } elseif ($uniqueId === $this->currentRegistrantIndex) {
                    // If removing the current registrant, switch to the last one
                    $this->currentRegistrantIndex = end($keys);
                }
            }
        }

        $this->dispatch('$refresh');
    }

    public function addContact()
    {
        $contactFieldsData = [];
        foreach ($this->contactFields as $field) {
            $contactFieldsData[$field] = '';
        }

        $uniqueId = uniqid('contact_'); // Generate a unique ID for each registrant

        $this->contacts[$uniqueId] = [
            'id'          => $uniqueId,
            'user_fields' => $contactFieldsData,
            'fields'      => $this->form->contactFields->mapWithKeys(function ($field, $index) {
                return [
                    $field->id => [
                        'field_id'       => $field->id,
                        'response'       => null,
                        'multi_response' => [],
                    ],
                ];
            })->toArray(),
        ];
    }

    public function removeContact($uniqueId)
    {
        unset($this->contacts[$uniqueId]);

        $this->dispatch('$refresh');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();

        if ($this->step === 1 && $this->form->require_contact_information) {
            if (empty($this->contacts)) {
                $this->addContact();
            }

            $this->step = 2;
        } else {
            $this->step = 3;
        }
    }

    public function previousStep()
    {
        if ($this->step === 2 || ($this->step === 3 && !$this->form->require_contact_information)) {
            $this->step = 1;

            // Revalidate our first step since we're back on that page.
            // This will also reset any errors that were specific to the second step.
            $this->validateCurrentStep();
        } elseif ($this->step === 3) {
            $this->step = 2;
        }
    }

    protected function validationAttributes()
    {
        $attributes = [];
        foreach ($this->registrants as $key => $registrant) {
            foreach ($this->userFields as $field) {
                $attributes["registrants.{$key}.user_fields.{$field}"] = str_replace('_', ' ', $field);
            }
            foreach ($this->form->fields as $formField) {
                $attributes["registrants.{$key}.fields.{$formField->id}.response"] = $formField->label ?? "field {$formField->id}";
            }
        }

        return $attributes;
    }

    protected function validateCurrentStep()
    {
        $rulesToValidate = [];
        $messages        = [];

        if ($this->step === 1) {
            // It's often cleaner to build the messages array alongside the rules.
            // We'll iterate through registrants to ensure messages are specific if needed,
            // though for now, generic messages might suffice if attribute names are clear.

            foreach ($this->registrants as $registrantKey => $registrantData) {
                // Required user fields
                foreach ($this->form->required_user_fields ?? [] as $field) {
                    $ruleKey                         = "registrants.{$registrantKey}.user_fields.{$field}";
                    $rulesToValidate[$ruleKey]       = 'required|string|max:255';
                    $messages["{$ruleKey}.required"] = "The " . str_replace('_', ' ', $field) . " field is required for registrant.";
                    if ($field === 'email') {
                        $rulesToValidate[$ruleKey]    .= '|email';
                        $messages["{$ruleKey}.email"] = "The " . str_replace('_', ' ', $field) . " must be a valid email address for registrant.";
                    }
                    if ($field === 'birthdate') {
                        $rulesToValidate[$ruleKey]   .= '|date';
                        $messages["{$ruleKey}.date"] = "The " . str_replace('_', ' ', $field) . " must be a valid date for registrant.";
                    }
                }

                // Form fields
                foreach ($this->form->registrantFields as $formField) {
                    if ($formField->is_required) {
                        $fieldLabel = $formField->label ?? ""; // Use field label if available
                        if ($formField->is_text_answer) {
                            $ruleKey                         = "registrants.{$registrantKey}.fields.{$formField->id}.response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required|string|max:1000' : 'nullable|string|max:1000';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for registrant.";
                        } elseif ($formField->is_select_program_group) {
                            $ruleKey                         = "registrants.{$registrantKey}.fields.{$formField->id}.response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required' : 'nullable';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for registrant.";
                        } elseif (($formField->is_question_answer || $formField->is_true_false || $formField->is_yes_no) && !$formField->allow_multiple_answers) {
                            $ruleKey                         = "registrants.{$registrantKey}.fields.{$formField->id}.response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required' : 'nullable';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for registrant.";
                        } elseif ($formField->is_question_answer && $formField->allow_multiple_answers) {
                            $ruleKey                         = "registrants.{$registrantKey}.fields.{$formField->id}.multi_response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required|array|min:1' : 'nullable|array';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for registrant.";
                            $messages["{$ruleKey}.min"]      = "Please select at least one option for {$fieldLabel} for registrant.";
                        }
                    }
                }
            }
//            Log::info('RULES TO VALIDATE', $rulesToValidate);
        } elseif ($this->step === 2) {
            foreach ($this->contacts as $contactKey => $contactData) {
                // Required user fields
                foreach ($this->form->required_contact_fields ?? [] as $field) {
                    $ruleKey                         = "contacts.{$contactKey}.user_fields.{$field}";
                    $rulesToValidate[$ruleKey]       = 'required|string|max:255';
                    $messages["{$ruleKey}.required"] = "The " . str_replace('_', ' ', $field) . " field is required for contact.";
                    if ($field === 'email') {
                        $rulesToValidate[$ruleKey]    .= '|email';
                        $messages["{$ruleKey}.email"] = "The " . str_replace('_', ' ', $field) . " must be a valid email address for contact.";
                    }
                    if ($field === 'birthdate') {
                        $rulesToValidate[$ruleKey]   .= '|date';
                        $messages["{$ruleKey}.date"] = "The " . str_replace('_', ' ', $field) . " must be a valid date for contact.";
                    }
                }

                // Form fields
                foreach ($this->form->contactFields as $formField) {
                    if ($formField->is_required) {
                        $fieldLabel = $formField->label ?? ""; // Use field label if available
                        if ($formField->is_text_answer) {
                            $ruleKey                         = "contacts.{$contactKey}.fields.{$formField->id}.response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required|string|max:1000' : 'nullable|string|max:1000';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for contact.";
                        } elseif ($formField->is_select_program_group) {
                            $ruleKey                         = "contacts.{$contactKey}.fields.{$formField->id}.response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required' : 'nullable';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for contact.";
                        } elseif (($formField->is_question_answer || $formField->is_true_false || $formField->is_yes_no) && !$formField->allow_multiple_answers) {
                            $ruleKey                         = "contacts.{$contactKey}.fields.{$formField->id}.response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required' : 'nullable';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for contact.";
                        } elseif ($formField->is_question_answer && $formField->allow_multiple_answers) {
                            $ruleKey                         = "contacts.{$contactKey}.fields.{$formField->id}.multi_response";
                            $rulesToValidate[$ruleKey]       = $formField->is_required ? 'required|array|min:1' : 'nullable|array';
                            $messages["{$ruleKey}.required"] = "This {$fieldLabel} field is required for contact.";
                            $messages["{$ruleKey}.min"]      = "Please select at least one option for {$fieldLabel} for registrant.";
                        }
                    }
                }
            }
        }

        if (!empty($rulesToValidate)) {
            $this->validate($rulesToValidate, $messages, $this->validationAttributes());
        }
    }

    public function submit()
    {
        $this->has_error = null;

        DB::beginTransaction();

        $created_registered_users = [];

        try {
            // Go through and create each registrant
            foreach ($this->registrants as $registrantData) {
                // Create our registrant
                $userService = new CreateProgramUser();
                $programUser = $userService
                    ->forAccount(Account::find($this->form->account_id))
                    ->forProgram($this->program)
                    ->withRegistrationForm($this->form)
                    ->isRegistrant()
                    ->create($registrantData['user_fields']);

                // CUSTOM REGISTRANT FIELDS
                foreach ($registrantData['fields'] as $fieldResponse) {
                    $field         = $this->form->registrantFields->firstWhere('id', $fieldResponse['field_id']);
                    $responseValue = null;

                    if ($field) {
                        if (($field->is_text_answer || $field->is_question_answer || $field->is_true_false || $field->is_yes_no)
                            && !$field->allow_multiple_answers) {
                            $responseValue = $fieldResponse['response'];
                        }

                        $multiResponseValue = null;
                        if ($field->allow_multiple_answers) {
                            $multiResponseValue = $fieldResponse['multi_response'];
                        }

                        // Handle program group selection - we'll attach them to the selected group and we'll store the name of the group as the response
                        if ($field->is_select_program_group) {
                            $responseValue = null;
                            $responseValue = $fieldResponse['response'];

                            $group = ProgramGroup::where('program_id', $this->program->id)->find($responseValue);

                            if ($group) {
                                $programUser->groups()->attach($group->id, [
                                    'account_id' => $this->form->account_id,
                                ]);
                                $responseValue = $group->name;
                            } else {
                                $responseValue = 'Invalid group selected.' . $responseValue;
                            }
                        }

                        FormFieldResponse::create([
                            'account_id'                         => $this->form->account_id,
                            'program_id'                         => $this->program->id,
                            'program_registration_form_field_id' => $field->id,
                            'program_registration_form_id'       => $this->form->id,
                            'program_registration_id'            => $programUser->registration->id,
                            'program_user_id'                    => $programUser->id,
                            'response'                           => $responseValue,
                            'multi_response'                     => $multiResponseValue,
                        ]);
                    }
                }

                $created_registered_users[] = $programUser;
            }

            // Create contacts - attach these contacts to each registrant
            foreach ($this->contacts as $contactData) {
                $userService = new CreateProgramUser();
                $contact     = $userService
                    ->forAccount(Account::find($this->form->account_id))
                    ->forProgram($this->program)
                    ->isContact()
                    ->create($contactData['user_fields']);

                foreach ($created_registered_users as $registered_user) {
                    $updateService = new UpdateProgramUser($contact);

                    $updateService->attachRegistration($registered_user->registration, [
                        'account_id'           => $this->form->account_id,
                        'program_id'           => $this->program->id,
                        'is_emergency_contact' => !empty($contactData['is_emergency_contact']) ? now() : null,
                        'is_primary_contact'   => !empty($contactData['is_primary_contact']) ? now() : null,
                        'can_pickup'           => !empty($contactData['can_pickup']) ? now() : null,
                        'created_at'           => now(),
                        'updated_at'           => now(),
                    ])
                        ->update();
                }

                // CUSTOM CONTACT FIELDS
                foreach ($contactData['fields'] as $fieldResponse) {
                    $field         = $this->form->contactFields->firstWhere('id', $fieldResponse['field_id']);
                    $responseValue = null;

                    if ($field) {
                        if (($field->is_text_answer || $field->is_question_answer || $field->is_true_false || $field->is_yes_no)
                            && !$field->allow_multiple_answers) {
                            $responseValue = $fieldResponse['response'];
                        }

                        $multiResponseValue = null;
                        if ($field->allow_multiple_answers) {
                            $multiResponseValue = $fieldResponse['multi_response'];
                        }

                        // Handle program group selection - we'll attach them to the selected group and we'll store the name of the group as the response
                        if ($field->is_select_program_group) {
                            $responseValue = null;
                            $responseValue = $fieldResponse['response'];

                            $group = ProgramGroup::where('program_id', $this->program->id)->find($responseValue);

                            if ($group) {
                                $contact->groups()->attach($group->id, [
                                    'account_id' => $this->form->account_id,
                                ]);
                                $responseValue = $group->name;
                            } else {
                                $responseValue = 'Invalid group selected.' . $responseValue;
                            }
                        }

                        FormFieldResponse::create([
                            'account_id'                         => $this->form->account_id,
                            'program_id'                         => $this->program->id,
                            'program_registration_form_field_id' => $field->id,
                            'program_registration_form_id'       => $this->form->id,
                            //                            'program_registration_id'            => $contact->registration->id,
                            'program_user_id'                    => $contact->id,
                            'response'                           => $responseValue,
                            'multi_response'                     => $multiResponseValue,
                        ]);
                    }
                }
            }

            DB::commit();

            $this->submit_success = true;
        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            $this->has_error = 'Please check the form for errors.';
            \Log::error('Registration validation failed!  Please check the form for errors.', $e->errors());
        } catch (\Exception $e) {
            DB::rollBack();
            $this->has_error = 'An error occurred while processing your registration to the database.  Please try again!  We have been notified of the error.';
            \Log::error('Registration failed: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
        }

        if (!$this->has_error) {
            $this->step = 4;

            $this->dispatch('$refresh');
        }
    }
}
