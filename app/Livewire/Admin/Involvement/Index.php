<?php

namespace App\Livewire\Admin\Involvement;

use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Services\DeleteArea;
use App\Involvement\Services\DeleteSubarea;
use App\Involvement\Subarea;
use Livewire\Component;

class Index extends Component
{
    public               $categories;
    public               $selected_category = null;
    public Category|null $edit_category     = null;
    public Area|null     $edit_area         = null;
    public Subarea|null  $edit_subarea      = null;

    protected $listeners = ['refreshPage' => '$refresh'];

    protected $rules = [
        'edit_category.name'                            => 'sometimes',
        'edit_category.description'                     => 'sometimes',
        'edit_category.men_only'                        => 'sometimes',
        'edit_category.women_only'                      => 'sometimes',
        'edit_category.baptized_only'                   => 'sometimes',
        'edit_category.approved_to_teach_only'          => 'sometimes',
        'edit_category.completed_background_check_only' => 'sometimes',
        'edit_category.is_hidden'                       => 'sometimes',
        'edit_area.name'                                => 'sometimes',
        'edit_area.description'                         => 'sometimes',
        'edit_area.men_only'                            => 'sometimes',
        'edit_area.women_only'                          => 'sometimes',
        'edit_area.baptized_only'                       => 'sometimes',
        'edit_area.approved_to_teach_only'              => 'sometimes',
        'edit_area.completed_background_check_only'     => 'sometimes',
        'edit_area.is_hidden'                           => 'sometimes',
        'edit_area.auto_approve_for_assignments'        => 'sometimes',
        'edit_area.auto_show_in_volunteer_list'         => 'sometimes',
        'edit_subarea.name'                             => 'sometimes',
        'edit_subarea.description'                      => 'sometimes',
        'edit_subarea.men_only'                         => 'sometimes',
        'edit_subarea.women_only'                       => 'sometimes',
        'edit_subarea.baptized_only'                    => 'sometimes',
        'edit_subarea.approved_to_teach_only'           => 'sometimes',
        'edit_subarea.completed_background_check_only'  => 'sometimes',
        'edit_subarea.is_hidden'                        => 'sometimes',
        'edit_subarea.auto_approve_for_assignments'     => 'sometimes',
        'edit_subarea.auto_show_in_volunteer_list'      => 'sometimes',
    ];

    public function mount()
    {
        $this->categories = Category::visibleTo(auth()->user())
            ->orderBy('sort_id', 'asc')
            ->with(['areas', 'areas.subareas'])
            ->get();
    }

    public function render()
    {
        if (!$this->selected_category) {
            $this->selected_category = $this->categories->first();
        }

        return view('livewire.admin.involvement.index');
    }

    public function selectCategory($category_id)
    {
        $this->selected_category = Category::visibleTo(auth()->user())
            ->where('id', $category_id)
            ->orderBy('sort_id', 'asc')
            ->with(['areas', 'areas.subareas'])
            ->first();

        $this->dispatch('refreshPage');
    }

    public function editCategory($category_id)
    {
        $this->edit_area     = null;
        $this->edit_subarea  = null;
        $this->edit_category = Category::visibleTo(auth()->user())
            ->where('id', $category_id)
            ->orderBy('sort_id', 'asc')
            ->first();

        $this->dispatch('refreshPage');
    }

    public function saveEditCategory()
    {
        $this->validate();

        $this->edit_category->save();

        $this->edit_category = null;

        $this->dispatch('refreshPage');
    }

    public function cancelEditCategory()
    {
        $this->edit_category = null;
    }

    public function editArea($area_id)
    {
        $this->edit_category = null;
        $this->edit_subarea  = null;
        $this->edit_area     = Area::visibleTo(auth()->user())
            ->where('id', $area_id)
            ->orderBy('sort_id', 'asc')
            ->first();

        $this->dispatch('refreshPage');
    }

    public function saveEditArea()
    {
        $this->validate();

        $this->edit_area->save();

        $this->edit_area = null;

        $this->dispatch('refreshPage');
    }

    public function deleteArea()
    {
        (new DeleteArea($this->edit_area))->delete();

        $this->edit_area = null;

        $this->dispatch('refreshPage');
    }

    public function cancelEditArea()
    {
        $this->edit_area = null;
    }

    public function editSubarea($area_id)
    {
        $this->edit_category = null;
        $this->edit_area     = null;
        $this->edit_subarea  = Subarea::where('id', $area_id)
            ->visibleTo(auth()->user())
            ->orderBy('sort_id', 'asc')
            ->first();

        $this->dispatch('refreshPage');
    }

    public function saveEditSubarea()
    {
        $this->validate();

        $this->edit_subarea->save();

        $this->edit_subarea = null;

        $this->dispatch('refreshPage');
    }

    public function deleteSubarea()
    {
        (new DeleteSubarea($this->edit_subarea))->delete();

        $this->edit_subarea = null;

        $this->dispatch('refreshPage');
    }

    public function cancelEditSubarea()
    {
        $this->edit_subarea = null;
    }
}
