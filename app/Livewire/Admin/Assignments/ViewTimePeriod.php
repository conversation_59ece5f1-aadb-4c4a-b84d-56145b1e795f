<?php

namespace App\Livewire\Admin\Assignments;

use App\WorshipAssignments\Period;
use Livewire\Component;

class ViewTimePeriod extends Component
{
    public Period $period;
    public        $filter       = null;
    public        $filter_value = null;

    public function render()
    {
        if ($this->filter == 'day') {
            $picks = $this->period
                ->picks()
                ->where('span_whole_period', true)
                ->where('day_of_week', $this->period->convertDayOfWeekToISO8601($this->filter_value))
                ->get();
        } elseif ($this->filter == 'date') {
            $picks = $this->period
                ->picks()
                ->where('span_whole_period', false)
                ->where('start_at', $this->filter_value)
                ->get();
        } else {
            $picks = $this->period->picks()->orderBy('start_at')->get();
        }

        return view('livewire.admin.assignments.view-time-period')
            ->with('picks', $picks);
    }

    public function setFilter($type, $value)
    {
        $this->filter       = $type;
        $this->filter_value = $value;
    }
}
