<?php

namespace App\Livewire\Admin\Assignments;

use App\WorshipAssignments\Pick;
use App\WorshipAssignments\Services\SendPickNotification;
use App\WorshipAssignments\Services\SendPickReminder;
use Livewire\Component;

class ViewTimePeriodAssignment extends Component
{
    public Pick $pick;
    public      $notes           = null;
    public      $edit_user_mode  = false;
    public      $edit_notes_mode = false;

    protected $listeners = [
        'refresh'                          => '$refresh',
        // This is a listener for the SelectUser component.
        'user-selected-for-pick-{pick.id}' => 'updatedSelectedUser',
    ];

    public function render()
    {
        return view('livewire.admin.assignments.view-time-period-assignment');
    }

    public function changeResponse($new_response = 'no_response')
    {
        if ($new_response == 'confirmed') {
            $this->pick->is_confirmed = now();
            $this->pick->save();
        } elseif ($new_response == 'declined') {
            $this->pick->is_confirmed = null;
            $this->pick->has_replied  = now();
            $this->pick->save();
        } else {
            $this->pick->has_replied  = null;
            $this->pick->sent_email   = null;
            $this->pick->is_confirmed = null;
            $this->pick->save();
        }
    }

    // This is a listener for the SelectUser component.
    public function updatedSelectedUser($user_id)
    {
        if ($this->pick->user_id != $user_id) {
            $this->setUser($user_id);
        }
    }

    public function enterEditMode()
    {
        $this->edit_user_mode = true;
    }

    public function exitEditMode()
    {
        $this->edit_user_mode = false;
    }

    public function enterEditNotesMode()
    {
        $this->notes           = $this->pick->notes;
        $this->edit_notes_mode = true;
    }

    public function exitEditNotesMode()
    {
        $this->edit_notes_mode = false;
    }

    public function saveNotes()
    {
        $this->pick->notes = $this->notes;

        $this->pick->save();

        $this->edit_notes_mode = false;

        $this->dispatch('refresh');
    }

    public function setUser($user_id = null)
    {
        if (!$user_id) {
            $this->pick->user_id      = null;
            $this->pick->has_replied  = null;
            $this->pick->sent_email   = null;
            $this->pick->is_confirmed = null;
        } elseif ($this->pick->user_id && $this->pick->user_id != $user_id) {
            $this->pick->has_replied  = null;
            $this->pick->sent_email   = null;
            $this->pick->is_confirmed = null;
            $this->pick->user_id      = $user_id;
        } else {
            $this->pick->user_id = $user_id;
        }

        $this->pick->save();

        $this->edit_user_mode = false;

        $this->dispatch('refresh');
    }

    public function removeUser()
    {
        $this->pick->user_id      = null;
        $this->pick->has_replied  = null;
        $this->pick->sent_email   = null;
        $this->pick->is_confirmed = null;
        $this->pick->save();
    }

    public function sendNotification()
    {
        if ($this->pick->hasSentNotification()) {
            (new SendPickReminder($this->pick))->send();
        } else {
            (new SendPickNotification($this->pick))
                ->viaEmail()
                ->viaMobileNotification()
                ->send();
        }
    }
}
