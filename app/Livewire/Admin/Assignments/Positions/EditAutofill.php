<?php

namespace App\Livewire\Admin\Assignments\Positions;

use App\Involvement\Area;
use App\Involvement\Category;
use App\Involvement\Subarea;
use Livewire\Component;

class EditAutofill extends Component
{
    public $position             = null;
    public $categories           = [];
    public $selected_category_id = null;
    public $areas                = [];
    public $selected_area_id     = null;
    public $subareas             = [];
    public $selected_subarea_id  = null;

    public function mount()
    {
        // Default to showing all categories in the first dropdown.
        $this->categories = Category::visibleTo(auth()->user())->get();

        // If we have a position, let's set all our dropdowns to proper selections.
        if ($this->position) {
            if ($this->position->involvementCategory) {
                $this->selected_category_id = $this->position->involvementCategory->id;
                $this->areas                = $this->position->involvementCategory->areas;
            }
            if ($this->position->involvementArea) {
                $this->selected_area_id = $this->position->involvementArea->id;
                $this->subareas         = $this->position->involvementArea->subareas;
            }
            if ($this->position->involvementSubarea) {
                $this->selected_subarea_id = $this->position->involvementSubarea->id;
            }
        }
    }

    public function render()
    {
        return view('livewire.admin.assignments.positions.edit-autofill');
    }

    public function setCategory()
    {
        if ($this->selected_category_id) {
            $this->areas = Area::visibleTo(auth()->user())
                ->where('involvement_category_id', $this->selected_category_id)
                ->get();
        }

        $this->subareas            = [];
        $this->selected_area_id    = null;
        $this->selected_subarea_id = null;
    }

    public function setArea()
    {
        if ($this->selected_area_id) {
            $this->subareas = Subarea::visibleTo(auth()->user())
                ->where('involvement_area_id', $this->selected_area_id)
                ->get();
        }

        $this->selected_subarea_id = null;
    }
}
