<?php

namespace App\Livewire\Admin\Rooms;

use App\Rooms\Room;
use Livewire\Component;

class EditRoom extends Component
{
    public Room $room;

    public $name;
    public $short_name;
    public $type;
    public $location;
    public $capacity;
    public $description;
    public $instructions;
    public $cancellation_policy;
    public $features = [];
    public $minimum_booking_time_in_minutes;
    public $maximum_booking_time_in_minutes;
    public $pre_reservation_buffer_in_minutes;
    public $post_reservation_buffer_in_minutes;
    public $is_hidden;
    public $is_bookable;
    public $is_available;
    public $is_maintenance;

    protected $rules = [
        'name'                               => 'required|string|max:200',
        'short_name'                         => 'nullable|string|max:100',
        'type'                               => 'nullable|string|max:100',
        'location'                           => 'nullable|string|max:250',
        'capacity'                           => 'nullable|integer|min:0',
        'description'                        => 'nullable|string',
        'instructions'                       => 'nullable|string',
        'cancellation_policy'                => 'nullable|string',
        'features'                           => 'nullable|array',
        //        'features.*' => ['nullable', Rule::in([
        //            Room::FEATURE_PROJECTOR,
        //            Room::FEATURE_WHITEBOARD,
        //            Room::FEATURE_WIFI
        //        ])],
        'minimum_booking_time_in_minutes'    => 'nullable|integer|min:0',
        'maximum_booking_time_in_minutes'    => 'nullable|integer|min:0',
        'pre_reservation_buffer_in_minutes'  => 'nullable|integer|min:0',
        'post_reservation_buffer_in_minutes' => 'nullable|integer|min:0',
        'is_hidden'                          => 'boolean',
        'is_bookable'                        => 'boolean',
        'is_available'                       => 'boolean',
        'is_maintenance'                     => 'boolean',
    ];

    public function mount(Room $room)
    {
        $this->room                               = $room;
        $this->name                               = $room->name;
        $this->short_name                         = $room->short_name;
        $this->type                               = $room->type;
        $this->location                           = $room->location;
        $this->capacity                           = $room->capacity;
        $this->description                        = $room->description;
        $this->instructions                       = $room->instructions;
        $this->cancellation_policy                = $room->cancellation_policy;
        $this->features                           = $room->features ?? [];
        $this->minimum_booking_time_in_minutes    = $room->minimum_booking_time_in_minutes;
        $this->maximum_booking_time_in_minutes    = $room->maximum_booking_time_in_minutes;
        $this->pre_reservation_buffer_in_minutes  = $room->pre_reservation_buffer_in_minutes;
        $this->post_reservation_buffer_in_minutes = $room->post_reservation_buffer_in_minutes;
        $this->is_hidden                          = $room->is_hidden;
        $this->is_bookable                        = $room->is_bookable;
        $this->is_available                       = $room->is_available;
        $this->is_maintenance                     = $room->is_maintenance;
    }

    public function save()
    {
        $validated = $this->validate();

        $this->room->update($validated);

        session()->flash('message', 'Room updated successfully.');
    }

    public function render()
    {
        return view('livewire.admin.rooms.edit-room');
    }
}
