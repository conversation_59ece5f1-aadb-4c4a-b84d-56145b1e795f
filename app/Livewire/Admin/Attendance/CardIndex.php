<?php

namespace App\Livewire\Admin\Attendance;

use App\Attendance\AttendanceCard;
use App\Exports\Attendance\VisitorCardIndexExport;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class CardIndex extends Component
{
    use WithPagination;

    public $query_term       = null;
    public $filter_show_spam = null;
    public $filter_start_at  = null;
    public $filter_end_at    = null;

    protected $queryString = [
        'page'             => ['except' => 1, 'as' => 'p'],
        'query_term'       => ['except' => '', 'as' => 'q'],
        'filter_show_spam' => ['except' => '', 'as' => 'is_spam'],
        'filter_start_at'  => ['except' => '', 'as' => 'start_at'],
        'filter_end_at'    => ['except' => '', 'as' => 'end_at'],
    ];

    protected $listeners = [
        'refreshView'       => '$refresh',
        'initExcelDownload' => 'initExcelDownload',
        'toggleFilter'      => 'toggleFilter',
        'queryChanged'      => 'queryChanged',
    ];

    public function queryChanged()
    {
        $this->resetPage();
    }

    public function render()
    {
        $this->dispatch('contentChanged');

        $filter_count = 0;

        $this->query_term ? $filter_count++ : null;
        $this->filter_show_spam ? $filter_count++ : null;
        $this->filter_start_at ? $filter_count++ : null;
        $this->filter_end_at ? $filter_count++ : null;

        $cards = $this->getCards()
            ->paginate(30);

        return view('livewire.admin.attendance.card-index')
            ->with('cards', $cards)
            ->with('filter_count', $filter_count);
    }

    public function initExcelDownload()
    {
        // Export as Excel file
        return Excel::download(new VisitorCardIndexExport($this->getCards()), 'visitor-cards-export.xlsx');
    }

    protected function getCards()
    {
        return AttendanceCard::visibleTo(auth()->user())
            ->when($this->query_term, function ($query) {
                $query->where('name', 'like', $this->query_term . '%');
            })
            ->when(!$this->filter_show_spam, function ($query) {
                $query->isNotSpam();
            })
            ->when($this->filter_start_at, function ($query) {
                $query->where('created_at', '>=', Carbon::createFromFormat('Y-m-d', $this->filter_start_at)->setTimezone(auth()->user()->account->timezone)->setTime(0, 0));
            })
            ->when($this->filter_end_at, function ($query) {
                $query->where('created_at', '<=', Carbon::createFromFormat('Y-m-d', $this->filter_end_at)->setTimezone(auth()->user()->account->timezone)->setTime(23, 59, 59));
            })
            ->orderBy('created_at', 'desc');
    }
}
