<?php

namespace App\Livewire\Admin\Attendance\Reports;

use App\Attendance\AttendanceType;
use App\Attendance\Attendance;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class UserAttendanceReport extends Component
{
    use WithPagination;

    public $attendance = [];
    public $attendance_type;
    public $attendance_type_id;
    public $start_date;
    public $end_date;

    protected $listeners = [
        'refreshComponent' => '$refresh',
    ];

    public function mount()
    {
        $attendanceType = AttendanceType::visibleTo(auth()->user())->orderBy('sort_id', 'asc')->first();

        $this->attendance_type    = $attendanceType;
        $this->attendance_type_id = $attendanceType->id;
        $this->start_date        = now()->subMonths(3)->format('Y-m-d');
        $this->end_date          = now()->format('Y-m-d');
        $this->loadAttendance();
    }

    public function updatedAttendanceTypeId($value)
    {
        $this->attendance_type = AttendanceType::visibleTo(auth()->user())->find($value);
        $this->loadAttendance();
    }

    public function updatedStartDate()
    {
        $this->loadAttendance();
    }

    public function updatedEndDate()
    {
        $this->loadAttendance();
    }

    public function loadAttendance()
    {
        $startDate = Carbon::parse($this->start_date);
        $endDate   = Carbon::parse($this->end_date);

        // Use the new method from AttendanceType
        $daysOfWeek = $this->attendance_type->getMySqlDaysOfWeek();

        // Create a dates CTE with day of week filter
        $dates = DB::table(
            DB::raw(
                "(
                    WITH RECURSIVE dates(date) AS (
                        SELECT DATE('{$startDate->format('Y-m-d')}')
                        UNION ALL
                        SELECT DATE_ADD(date, INTERVAL 1 DAY)
                        FROM dates
                        WHERE date < DATE('{$endDate->format('Y-m-d')}')
                    )
                    SELECT date 
                    FROM dates 
                    WHERE DAYOFWEEK(date) IN (" . implode(',', $daysOfWeek) . ")
                ) as dates"
            )
        );

        $this->attendance = $dates
            ->leftJoin('user_attendance', function ($join) {
                $join->on(DB::raw('DATE(user_attendance.date_attendance)'), '=', 'dates.date')
                    ->where('user_attendance.user_attendance_type_id', $this->attendance_type_id);
            })
            ->join('users', function($join) {
                $join->on('users.id', '=', 'user_attendance.user_id')
                    ->where('users.account_id', auth()->user()->account_id);
            })
            ->select('dates.date as attendance_at', DB::raw('COUNT(user_attendance.id) as total_count'))
            ->groupBy('dates.date')
            ->orderBy('dates.date', 'asc')
            ->get()
            ->toArray();

        $this->dispatch('refreshUserAttendanceChart', $this->attendance);
    }

    public function updateType(AttendanceType $attendanceType)
    {
        $this->attendance_type    = $attendanceType;
        $this->attendance_type_id = $attendanceType->id;
        $this->loadAttendance();
    }

    public function render()
    {
        return view('livewire.admin.attendance.reports.user-attendance-report');
    }
} 