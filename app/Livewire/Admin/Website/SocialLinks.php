<?php

namespace App\Livewire\Admin\Website;

use App\Website\Services\GetWebsiteSettingValue;
use App\Website\Services\SaveOrCreateWebsiteSettingValue;
use Livewire\Component;

class SocialLinks extends Component
{
    public $facebook = '';
    public $youtube  = '';
    public $vimeo    = '';
    public $twitter  = '';
    public $notification = null;

    public function mount()
    {
        $this->loadSocialLinks();
    }

    public function loadSocialLinks()
    {
        $this->facebook = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.facebook');

        $this->youtube = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.youtube');

        $this->vimeo = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.vimeo');

        $this->twitter = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.twitter');
    }

    public function notify($message, $type = 'success')
    {
        $this->notification = [
            'type' => $type,
            'message' => $message
        ];
    }

    public function dismissNotification()
    {
        $this->notification = null;
    }

    public function save()
    {
        try {
            $this->validate([
                'facebook' => 'nullable|url',
                'youtube'  => 'nullable|url',
                'vimeo'    => 'nullable|url',
                'twitter'  => 'nullable|url',
            ]);

            $saveService = new SaveOrCreateWebsiteSettingValue();

            $saveService->forAccount(auth()->user()->account)
                ->forSetting('links.facebook')
                ->save($this->facebook);

            $saveService->forAccount(auth()->user()->account)
                ->forSetting('links.youtube')
                ->save($this->youtube);

            $saveService->forAccount(auth()->user()->account)
                ->forSetting('links.vimeo')
                ->save($this->vimeo);

            $saveService->forAccount(auth()->user()->account)
                ->forSetting('links.twitter')
                ->save($this->twitter);

            $this->notify('Social links updated successfully');

        } catch (\Exception $e) {
            $this->notify('Failed to save settings: ' . $e->getMessage(), 'error');
        }
    }

    public function render()
    {
        return view('livewire.admin.website.social-links');
    }
} 