<?php

namespace App\Livewire\Admin\Website;

use App\Website\Services\SaveOrCreateWebsiteSettingValue;
use App\Website\WebsitePage;
use App\Website\WebsiteSettingValue;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class SidebarLinks extends Component
{
    public $sidebarLinks = [];
    public $newItem      = [
        'title'           => '',
        'link'            => '',
        'link_type'       => 'page',
        'website_page_id' => null,
    ];
    public $editingIndex = null;
    public $showForm     = false;
    public $websitePages = [];

    public function mount()
    {
        $this->loadSidebarLinks();
        $this->loadWebsitePages();
    }

    public function loadSidebarLinks()
    {
        $settingValue = WebsiteSettingValue::where('account_id', auth()->user()->account_id)
            ->whereHas('setting', function ($query) {
                $query->where('key', 'quick_links');
            })
            ->first();

        $links = $settingValue ? json_decode($settingValue->value, true) : [];

        // Ensure all items have the required structure
        $this->sidebarLinks = collect($links)->map(function ($item) {
            return [
                'title'           => $item['title'] ?? '',
                'link'            => $item['link'] ?? '',
                'link_type'       => $item['link_type'] ?? 'custom',
                'website_page_id' => $item['website_page_id'] ?? null,
            ];
        })->toArray();
    }

    public function loadWebsitePages()
    {
        $this->websitePages = WebsitePage::where('account_id', auth()->user()->account_id)
            ->whereNotNull('is_public')
            ->orderBy('title')
            ->get();
    }

    public function addItem()
    {
        try {
            $this->validate([
                'newItem.title'           => 'required',
                'newItem.link_type'       => 'required|in:custom,page',
                'newItem.link'            => $this->newItem['link_type'] === 'custom' ? 'required|url' : '',
                'newItem.website_page_id' => [
                    $this->newItem['link_type'] === 'page' ? 'required' : 'nullable',
                    'exists:website_pages,id',
                    function ($attribute, $value, $fail) {
                        if ($value && !WebsitePage::where('id', $value)
                                ->where('account_id', auth()->user()->account_id)
                                ->exists()
                        ) {
                            $fail('The selected page is invalid.');
                        }
                    },
                ],
            ]);

            // If it's a page type, clear the custom link
            if ($this->newItem['link_type'] === 'page') {
                $this->newItem['link'] = '';
            } else {
                $this->newItem['website_page_id'] = null;
            }

            $this->sidebarLinks[] = $this->newItem;
            $this->resetNewItem();
            $this->showForm = false;
            $this->saveLinks();

            $this->dispatch('notify', ['message' => 'Link added successfully']);
        } catch (\Exception $e) {
            Log::error('Error adding sidebar link: ' . $e->getMessage(), [
                'newItem'      => $this->newItem,
                'sidebarLinks' => $this->sidebarLinks,
            ]);
            $this->addError('addItem', 'Failed to add link. Please try again. ' . $e->getMessage());
        }
    }

    public function editItem($index)
    {
        $this->editingIndex = $index;
        $this->newItem      = $this->sidebarLinks[$index];
        $this->showForm     = true;
    }

    public function updateItem()
    {
        $this->validate([
            'newItem.title' => 'required',
            'newItem.link'  => 'required|url',
        ]);

        $this->sidebarLinks[$this->editingIndex] = $this->newItem;
        $this->resetNewItem();
        $this->showForm = false;
        $this->saveLinks();
    }

    public function deleteItem($index)
    {
        unset($this->sidebarLinks[$index]);
        $this->sidebarLinks = array_values($this->sidebarLinks);
        $this->saveLinks();
    }

    public function reorder($orderedIds)
    {
        $newOrder = collect($orderedIds)->map(function ($id) {
            return $this->sidebarLinks[$id];
        })->toArray();

        $this->sidebarLinks = $newOrder;
        $this->saveLinks();
    }

    public function cancelEdit()
    {
        $this->resetNewItem();
        $this->showForm = false;
    }

    private function resetNewItem()
    {
        $this->newItem      = [
            'title'           => '',
            'link'            => '',
            'link_type'       => 'custom',
            'website_page_id' => null,
        ];
        $this->editingIndex = null;
    }

    private function saveLinks()
    {
        (new SaveOrCreateWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->forSetting('quick_links')
            ->save(json_encode($this->sidebarLinks));
    }

    public function render()
    {
        return view('livewire.admin.website.sidebar-links');
    }
} 