<?php

namespace App\Livewire\Admin\Website\Pages;

use App\Website\WebsitePage;
use Illuminate\Validation\Rule;
use Livewire\Component;

class EditPage extends Component
{
    public $page;
    public $title = '';
    public $slug = '';
    public $status = 'draft';
    public $slugError = '';

    public function mount(WebsitePage $page)
    {
        $this->page = $page;
        $this->title = $page->title;
        $this->slug = $page->url_title;
        $this->status = $page->getStatus();
    }

    public function updatedSlug($value)
    {
        $this->slugError = '';
        if (empty($value)) {
            $this->slugError = 'URL cannot be empty.';
            return;
        }
        if (!preg_match('/^[a-z0-9-]+$/', $value)) {
            $this->slugError = 'URL can only contain lowercase letters, numbers, and hyphens.';
            return;
        }
        if (WebsitePage::where('url_title', $value)->where('account_id', auth()->user()->account_id)->where('id', '!=', $this->page->id)->exists()) {
            $this->slugError = 'This URL is already taken.';
        }
    }

    public function save()
    {
        $this->validate([
            'title' => 'required',
            'slug' => [
                'required',
                Rule::unique('website_pages', 'url_title')->where('account_id', auth()->user()->account_id)->ignore($this->page->id),
            ],
            'status' => 'required|in:draft,public,archived',
        ]);

        $this->page->update([
            'title' => $this->title,
            'url_title' => $this->slug,
            'is_draft' => $this->status == 'draft' ? now() : null,
            'is_public' => $this->status == 'public' ? now() : null,
            'is_archived' => $this->status == 'archived' ? now() : null,
        ]);

        session()->flash('message.success', 'Page details updated successfully.');
    }

    public function render()
    {
        return view('livewire.admin.website.pages.edit-page');
    }
}