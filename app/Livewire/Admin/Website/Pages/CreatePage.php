<?php

namespace App\Livewire\Admin\Website\Pages;

use App\Website\WebsitePage;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Livewire\Component;
use Livewire\WithFileUploads;

class CreatePage extends Component
{
    use WithFileUploads;

    public $title        = '';
    public $slug         = '';
    public $content      = '';
    public $json_content = '';
    public $tempImage;
    public $status       = 'draft';
    public $slugError    = '';

    public function mount()
    {
        $this->content = '';
    }

    public function updatedTitle($value)
    {
        // Auto-generate slug when title changes
        $this->slug = Str::slug($value);
        // Validate the auto-generated slug
        $this->updatedSlug($this->slug);
    }

    public function updatedSlug($value)
    {
        // Clear error first
        $this->slugError = '';

        // Don't validate empty values
        if (empty($value)) {
            $this->slugError = 'URL cannot be empty.';
            return;
        }

        // Validate format
        if (!preg_match('/^[a-z0-9-]+$/', $value)) {
            $this->slugError = 'URL can only contain lowercase letters, numbers, and hyphens.';
            return;
        }

        // Check uniqueness
        $validator = Validator::make(
            ['slug' => $value],
            [
                'slug' => [
                    'required',
                    Rule::unique('website_pages', 'url_title')->where('account_id', auth()->user()->account_id),
                ],
            ]
        );

        if ($validator->fails()) {
            $this->slugError = 'This URL is already taken.';
        }
    }

    public function updatedTempImage()
    {
        $this->validate([
            'tempImage' => 'image|max:2048',
        ]);

        $path = $this->tempImage->store('public/images');
        $url  = Storage::url($path);

        return $url;
    }

    public function save()
    {
        // Revalidate slug before saving
        $this->updatedSlug($this->slug);

        if ($this->slugError) {
            return;
        }

        $this->validate([
            'title'   => 'required',
            'slug'    => [
                'required',
                Rule::unique('website_pages', 'url_title')->where('account_id', auth()->user()->account_id),
            ],
            'content' => 'required',
            'status'  => 'required|in:draft,public,archived',
        ]);

        // Create the page
        $page = WebsitePage::create([
            'title'         => $this->title,
            'url_title'     => $this->slug,
            'trix'          => $this->json_content,
            'html'          => $this->content,
            'account_id'    => auth()->user()->account_id,
            'created_by_id' => auth()->id(),
            'updated_by_id' => auth()->id(),
            'is_public'     => $this->status == 'public' ? now() : null,
            'is_draft'      => $this->status == 'draft' ? now() : null,
            'is_archived'   => $this->status == 'archived' ? now() : null,
        ]);

        // Optional: Redirect to edit page
        return redirect()->route('admin.website.pages.index')
            ->with('message.success', 'Page created successfully.');
    }

    public function render()
    {
        return view('livewire.admin.website.pages.create-page');
    }
} 