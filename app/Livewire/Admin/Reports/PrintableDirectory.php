<?php

namespace App\Livewire\Admin\Reports;

use App\Accounts\Grade;
use App\Reports\Services\PrintableDirectory as PrintableDirectoryService;
use App\Users\Group;
use App\Users\User;
use Illuminate\Support\Arr;
use Livewire\Component;
use Livewire\WithPagination;

class PrintableDirectory extends Component
{
    use WithPagination;

    public $query_term            = null;
    public $filter_groups         = [];
    public $filter_exclude_groups = [];
    public $columns               = [];
    public $family_roles          = [];
    public $marital_statuses      = [];
    public $filter_grades         = [];
    public $options               = [];
    public $asterisk_unbaptized   = false;
    public $asterisk_baptized     = false;

    protected $queryString = [
        'page'                  => ['except' => 1, 'as' => 'p'],
        'query_term'            => ['except' => '', 'as' => 'q'],
        'filter_groups'         => ['except' => '', 'as' => 'f_g'],
        'filter_exclude_groups' => ['except' => '', 'as' => 'f_eg'],
        'columns'               => ['except' => [], 'as' => 'cols'],
        'family_roles'          => ['except' => [], 'as' => 'f_r'],
        'marital_statuses'      => ['except' => [], 'as' => 'm_s'],
        'filter_grades'         => ['except' => '', 'as' => 'f_gra'],
        'options'               => ['except' => [], 'as' => 'f_o'],
    ];

    protected $listeners = [
        'refreshView'              => '$refresh',
        'initExcelDownload'        => 'initExcelDownload',
        'addGroupFilter'           => 'addGroupFilter',
        'removeGroupFilter'        => 'removeGroupFilter',
        'addGroupExcludeFilter'    => 'addGroupExcludeFilter',
        'removeGroupExcludeFilter' => 'removeGroupExcludeFilter',
        'toggleFilter'             => 'toggleFilter',
        'queryChanged'             => 'queryChanged',
        'addGrade'                 => 'addGrade',
        'removeGrade'              => 'removeGrade',
    ];

    public function mount()
    {
        $this->columns = ['adults_only'];

        $default_member_group = auth()->user()->account->getDefaultMemberGroup();
        if ($default_member_group) {
            $this->filter_groups = [$default_member_group->id];
        }
    }

    public function queryChanged()
    {
        $this->resetPage();
    }

    public function render()
    {
        $groups = Group::visibleTo(auth()->user())->get();
        $grades = Grade::visibleTo(auth()->user())->get();

        $this->dispatch('contentChanged');

        $filter_groups_js = [];
        foreach ($groups as $group) {
            $filter_groups_js[] = [
                'value'    => $group->id,
                'label'    => $group->name,
                'selected' => in_array($group->id, $this->filter_groups) ? 'true' : 'false',
            ];
        }
        $filter_exclude_groups_js = [];
        foreach ($groups as $group) {
            $filter_exclude_groups_js[] = [
                'value'    => $group->id,
                'label'    => $group->name,
                'selected' => in_array($group->id, $this->filter_exclude_groups) ? 'true' : 'false',
            ];
        }
        $filter_grades_js = [];
        foreach ($grades as $grade) {
            $filter_grades_js[] = [
                'value'    => $grade->id,
                'label'    => $grade->name,
                'selected' => in_array($grade->id, $this->filter_grades) ? 'true' : 'false',
            ];
        }

        $filter_count = 0;
        if (count($this->filter_groups) > 0) {
            $filter_count++;
        }
        if (count($this->filter_exclude_groups) > 0) {
            $filter_count++;
        }
        if (count($this->filter_grades) > 0) {
            $filter_count++;
        }
        if (count($this->columns) > 0) {
            $filter_count++;
        }
        if (count($this->options) > 0) {
            $filter_count++;
        }
        if (count($this->family_roles) > 0) {
            $filter_count++;
        }
        if (count($this->marital_statuses) > 0) {
            $filter_count++;
        }

        return view('livewire.admin.reports.printable-directory')
            ->with('groups', $groups)
            ->with('grades', $grades)
            ->with('filter_count', $filter_count)
            ->with('filter_groups_js', $filter_groups_js)
            ->with('filter_exclude_groups_js', $filter_exclude_groups_js)
            ->with('filter_grades_js', $filter_grades_js)
            ->with('users', $this->getUsers()->get());
    }

    public function download($type = 'csv')
    {
        return response()->streamDownload(function () {
            (new PrintableDirectoryService())
                ->withUsers($this->getUsers()->get())
                ->indicateBaptismNotChecked($this->asterisk_unbaptized)
                ->indicateBaptismIsChecked($this->asterisk_baptized)
                ->createPDF('Member Paper Directory.pdf');
        }, 'Member Paper Directory.pdf');
    }

    public function addGroupFilter($group_id)
    {
        $this->filter_groups = array_unique(array_merge($this->filter_groups, [$group_id]));

        $this->dispatch('refreshView');
    }

    public function removeGroupFilter($remove_group_id)
    {
        if (($key = array_search($remove_group_id, $this->filter_groups)) !== false) {
            unset($this->filter_groups[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function addGroupExcludeFilter($group_id)
    {
        $this->filter_exclude_groups = array_unique(array_merge($this->filter_exclude_groups, [$group_id]));

        $this->dispatch('refreshView');
    }

    public function removeGroupExcludeFilter($remove_group_id)
    {
        if (($key = array_search($remove_group_id, $this->filter_exclude_groups)) !== false) {
            unset($this->filter_exclude_groups[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function addGrade($grade_id)
    {
        $this->filter_grades = array_unique(array_merge($this->filter_grades, [$grade_id]));

        $this->dispatch('refreshView');
    }

    public function removeGrade($grade_id)
    {
        if (($key = array_search($grade_id, $this->filter_grades)) !== false) {
            unset($this->filter_grades[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function setAgeStart($age)
    {
        $this->filter_grades = array_unique(array_merge($this->filter_grades, [$grade_id]));

        $this->dispatch('refreshView');
    }

    public function setAgeEnd($age)
    {
        if (($key = array_search($grade_id, $this->filter_grades)) !== false) {
            unset($this->filter_grades[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function setBirthdateStart($date)
    {
        $this->filter_birthdate_start = $date;

        $this->dispatch('refreshView');
    }

    public function setBirthdateEnd($date)
    {
        $this->filter_birthdate_end = $date;

        $this->dispatch('refreshView');
    }

    public function clearOption($option_name)
    {
        unset($this->options[$option_name]);

        $this->dispatch('refreshView');
    }

    protected function getUsers()
    {
        return User::visibleTo(auth()->user())
            ->when($this->query_term, function ($mainQuery) {
                return $mainQuery->BroadSearchByString($this->query_term);
            })
            ->when($this->options, function ($mainQuery) {
                $mainQuery->when((Arr::exists($this->options, 'birthdate_start') && !empty($this->options['birthdate_start'])), function ($query) {
                    $query->where('birthdate', '>=', $this->options['birthdate_start']);
                });
                $mainQuery->when((Arr::exists($this->options, 'birthdate_end') && !empty($this->options['birthdate_end'])), function ($query) {
                    $query->where('birthdate', '<=', $this->options['birthdate_end']);
                });
                $mainQuery->when((Arr::exists($this->options, 'membership_start') && !empty($this->options['membership_start'])), function ($query) {
                    $query->where('date_membership', '>=', $this->options['membership_start']);
                });
                $mainQuery->when((Arr::exists($this->options, 'membership_end') && !empty($this->options['membership_end'])), function ($query) {
                    $query->where('date_membership', '<=', $this->options['membership_end']);
                });
                $mainQuery->when((Arr::exists($this->options, 'baptism_start') && !empty($this->options['baptism_start'])), function ($query) {
                    $query->where('date_baptism', '>=', $this->options['baptism_start']);
                });
                $mainQuery->when((Arr::exists($this->options, 'baptism_end') && !empty($this->options['baptism_end'])), function ($query) {
                    $query->where('date_baptism', '<=', $this->options['baptism_end']);
                });
            })
            ->when($this->columns, function ($mainQuery) {
                $mainQuery->when(in_array('is_baptized', $this->columns), function ($query) {
                    $query->whereNotNull('is_baptized');
                });
                $mainQuery->when(in_array('is_not_baptized', $this->columns), function ($query) {
                    $query->whereNull('is_baptized');
                });
                $mainQuery->when(in_array('date_background_check', $this->columns), function ($query) {
                    $query->whereNotNull('date_background_check');
                });
                $mainQuery->when(in_array('can_teach', $this->columns), function ($query) {
                    $query->whereNotNull('can_teach');
                });
                $mainQuery->when(in_array('can_lead', $this->columns), function ($query) {
                    $query->whereNotNull('can_lead');
                });
                $mainQuery->when(in_array('head_of_household_only', $this->columns), function ($query) {
                    $query->headsOfFamily();
                });
                $mainQuery->when(in_array('children_only', $this->columns), function ($query) {
                    $query->childrenOnly();
                });
                $mainQuery->when(in_array('adults_only', $this->columns), function ($query) {
                    $query->adultsOnly();
                });
                $mainQuery->when(in_array('men_only', $this->columns), function ($query) {
                    $query->menOnly();
                });
                $mainQuery->when(in_array('women_only', $this->columns), function ($query) {
                    $query->womenOnly();
                });
            })
            ->when($this->family_roles, function ($query) {
                $query->where(function ($subQuery) {
                    foreach (User::$family_roles as $key => $value) {
                        if (in_array($key, $this->family_roles)) {
                            $subQuery->orWhere('users.family_role', $key);
                        }
                    }
                });
            })
            ->when($this->marital_statuses, function ($query) {
                $query->where(function ($subQuery) {
                    foreach (User::$marital_statuses as $key => $value) {
                        if (in_array($key, $this->marital_statuses)) {
                            $subQuery->orWhere('users.marital_status', $key);
                        }
                    }
                });
            })
            ->whereHas('groups', function ($query) {
                $query->whereIn('user_groups.id', $this->filter_groups ?: []);
            })
            ->when($this->filter_exclude_groups, function ($query) {
                $query->whereDoesntHave('groups', function ($query) {
                    $query->whereIn('user_groups.id', $this->filter_exclude_groups ?: []);
                });
            })
            ->when($this->filter_grades, function ($query) {
                $query->whereIn('user_grade_id', $this->filter_grades ?: []);
            })
            ->withFamilyLastName()
            ->isNotDeceased()
            ->with(['emails'])
            ->includeInReports()
            ->headsOfFamily()
            ->orderByRaw('last_name asc, first_name asc');
    }
}
