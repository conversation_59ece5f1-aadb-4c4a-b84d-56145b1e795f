<?php

namespace App\Livewire\Admin\Sermons;

use App\Accounts\Grade;
use App\Exports\Users\UserIndexExport;
use App\Sermons\Sermon;
use App\Users\Group;
use App\Users\User;
use Illuminate\Support\Arr;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class SermonIndex extends Component
{
    use WithPagination;

    public $query_term = null;

    protected $queryString = [
        'page'       => ['except' => 1, 'as' => 'p'],
        'query_term' => ['except' => '', 'as' => 'q'],
    ];

    protected $listeners = [
        'refreshView'       => '$refresh',
        'initExcelDownload' => 'initExcelDownload',
        'queryChanged'      => 'queryChanged',
    ];

    public function mount()
    {
        $this->columns = [];
    }

    public function queryChanged()
    {
        $this->resetPage();
    }

    public function render()
    {
        $sermons = $this->getSermons();

        $this->dispatch('contentChanged');

        return view('livewire.admin.sermons.sermon-index')
            ->with('sermons', $sermons->paginate(25));
    }

    public function initExcelDownload()
    {
        $users = $this->getUsers();

        // Export as Excel file
        return Excel::download(new UserIndexExport($users), 'user-list-export.xlsx');
    }


    protected function getSermons()
    {
        return Sermon::visibleTo(auth()->user())
            ->when($this->query_term, function ($mainQuery) {
                return $mainQuery->SearchTitleByString($this->query_term);
            })
            ->withCount('files')
            ->orderBy('id', 'DESC');
    }
}
