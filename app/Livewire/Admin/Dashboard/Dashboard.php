<?php

namespace App\Livewire\Admin\Dashboard;

use App\Users\Email;
use App\Users\Photo;
use Livewire\Component;

class Dashboard extends Component
{
    public $spam_activity           = [];
    public $labels                  = [];
    public $user_activity           = [];
    public $pending_photo_approvals = [];

    protected $listeners = [
        'refreshPage' => '$refresh',
    ];

    public function loadUserActivity()
    {
        $this->user_activity = (new \App\Admin\Services\Dashboard())->getRecentActiveUsers();
    }

    public function loadSpamActivity()
    {
        $this->spam_activity = Email::visibleTo(auth()->user())
            ->markedSpam()
            ->get();
    }

    public function loadPendingPhotoApprovals()
    {
        $this->pending_photo_approvals = Photo::visibleTo(auth()->user())->needsApproval()->get();
    }

    public function render()
    {
        return view('livewire.admin.dashboard.dashboard');
    }
}
