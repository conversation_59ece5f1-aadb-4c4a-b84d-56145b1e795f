<?php

namespace App\Livewire\Admin\Dashboard;

use App\Users\Email;
use Livewire\Component;

class SpamComplaints extends Component
{
    public $spam_activity = [];

    protected $listeners = [
        'refreshPage' => '$refresh',
    ];

    public function render()
    {
        $spam_complaints = Email::visibleTo(auth()->user())
            ->markedSpam()
            ->get();

        return view('livewire.admin.dashboard.spam-complaints')
            ->with(
                'spam_complaints',
                $spam_complaints
            );
    }
}
