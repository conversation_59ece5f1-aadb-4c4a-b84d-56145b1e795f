<?php

namespace App\Livewire\Admin\Dashboard;

use App\Users\Photo;
use Livewire\Component;

class PendingPhotoApprovals extends Component
{
    public $spam_activity = [];

    protected $listeners = [
        'refreshPage' => '$refresh',
    ];

    public function render()
    {
        $pending_photo_approvals = Photo::visibleTo(auth()->user())->needsApproval()->get();

        return view('livewire.admin.dashboard.pending-photo-approvals')
            ->with(
                'pending_photo_approvals',
                $pending_photo_approvals
            );
    }
}
