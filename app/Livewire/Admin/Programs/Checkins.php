<?php

namespace App\Livewire\Admin\Programs;

use App\Programs\ProgramUser;
use App\Programs\ProgramUserCheckin;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithPagination;

class Checkins extends Component
{
    use WithPagination;

    public $program;
    public $query_term = null;
    public $groups_filter = [];
    public $show_checked_in_only = false;

    protected $queryString = [
        'page' => ['except' => 1, 'as' => 'p'],
        'query_term' => ['except' => '', 'as' => 'q'],
        'groups_filter' => ['except' => [], 'as' => 'groups'],
        'show_checked_in_only' => ['except' => false, 'as' => 'checked_in'],
    ];

    protected $listeners = [
        'refreshCheckins' => '$refresh',
        'userCheckedIn' => 'handleUserCheckedIn',
        'userCheckedOut' => 'handleUserCheckedOut',
    ];

    public function mount($program)
    {
        $this->program = $program;
    }

    public function render()
    {
        $users = $this->getUsers();
        
        // Get groups for filtering
        $groups = $this->program->groups()
            ->withCount(['currentCheckins', 'users'])
            ->orderBy('sort_id')
            ->orderBy('name')
            ->get();

        // Calculate total checked in count
        $totalCheckedIn = $this->program->currentCheckins()->count();

        return view('livewire.admin.programs.checkins')
            ->with('users', $users)
            ->with('groups', $groups)
            ->with('totalCheckedIn', $totalCheckedIn);
    }

    public function toggleCheckin($userId)
    {
        $programUser = ProgramUser::find($userId);
        
        if (!$programUser || $programUser->program_id !== $this->program->id) {
            return;
        }

        $currentCheckin = $programUser->currentCheckin;

        if ($currentCheckin) {
            // Check out
            $currentCheckin->checkout_at = now();
            $currentCheckin->checkout_by_user_id = auth()->id();
            $currentCheckin->save();
            
            $this->dispatch('userCheckedOut', userId: $userId);
        } else {
            // Check in
            $checkin = ProgramUserCheckin::create([
                'account_id' => $programUser->account_id,
                'program_id' => $programUser->program_id,
                'program_user_id' => $programUser->id,
                'program_group_id' => $programUser->groups()->first()?->id,
                'checkin_at' => now(),
                'checkin_by_user_id' => auth()->id(),
                'created_by_user_id' => auth()->id(),
                'uuid' => Str::uuid(),
            ]);
            
            $this->dispatch('userCheckedIn', userId: $userId);
        }

        // Refresh the component
        $this->dispatch('refreshCheckins');
    }

    public function handleUserCheckedIn($userId)
    {
        // This will be called when Pusher broadcasts a check-in event
        $this->dispatch('refreshCheckins');
    }

    public function handleUserCheckedOut($userId)
    {
        // This will be called when Pusher broadcasts a check-out event
        $this->dispatch('refreshCheckins');
    }

    protected function getUsers()
    {
        $strings = explode(' ', $this->query_term);
        $string1 = Arr::get($strings, 0, null);
        $string2 = Arr::get($strings, 1, null);

        $query = $this->program->users()
            ->with(['currentCheckin', 'groups', 'registration'])
            ->when($this->query_term, function ($mainQuery) use ($string1, $string2) {
                return $mainQuery->where(function ($mainQuery) use ($string1, $string2) {
                    $mainQuery->where(function ($query) use ($string1, $string2) {
                        $query->when((!empty($string1) && empty($string2)), function ($query2) {
                            $query2->where('last_name', 'like', '%' . $this->query_term . '%')
                                ->orWhere('first_name', 'like', '%' . $this->query_term . '%')
                                ->orWhere('preferred_first_name', 'like', '%' . $this->query_term . '%');
                        })
                        ->when((!empty($string1) && !empty($string2)), function ($query) use ($string1) {
                            $query->where('first_name', 'like', '%' . $string1 . '%');
                            $query->orWhere('preferred_first_name', 'like', '%' . $string1 . '%');
                        })
                        ->when((!empty($string2) && !empty($string1)), function ($query) use ($string2) {
                            $query->where('last_name', 'like', '%' . $string2 . '%');
                        });
                    })
                    ->orWhere('email', 'like', $this->query_term . '%')
                    ->when(preg_replace('/[^0-9]/', '', $this->query_term), function ($query) {
                        $query->orWhere('mobile_phone', 'like', preg_replace('/[^0-9]/', '', $this->query_term) . '%');
                    });
                });
            })
            ->when(!empty($this->groups_filter), function ($query) {
                $query->whereHas('groups', function ($query) {
                    $query->whereIn('program_groups.id', $this->groups_filter);
                });
            })
            ->when($this->show_checked_in_only, function ($query) {
                $query->whereHas('currentCheckin');
            })
            ->isRegistrant(); // Only show registered users

        // Group by program groups
        if (empty($this->groups_filter)) {
            // If no filter, get all users and we'll group them in the view
            return $query->orderBy('last_name', 'asc')
                ->orderBy('first_name', 'asc')
                ->paginate(200);
        } else {
            // If filtering by groups, just return the filtered users
            return $query->orderBy('last_name', 'asc')
                ->orderBy('first_name', 'asc')
                ->paginate(200);
        }
    }
}