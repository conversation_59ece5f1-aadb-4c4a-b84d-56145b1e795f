<?php

namespace App\Livewire\Admin\Programs;

use App\Programs\ProgramUser;
use Illuminate\Support\Arr;
use Livewire\Component;
use Livewire\WithPagination;

class ViewRegistrations extends Component
{
    use WithPagination;

    public $program;
    public $query_term    = null;
    public $columns       = [];
    public $groups_filter = [];
    public $family_roles  = [];

    // members, admins or senders
    public $group_user_mode     = 'members';
    public $add_users           = false;
    public $copy_users_view     = false;
    public $group_to_copy_id    = null;
    public $add_user_query_term = null;
    public $user_added          = null;

    protected $queryString = [
        'page'          => ['except' => 1, 'as' => 'p'],
        'query_term'    => ['except' => '', 'as' => 'q'],
        'columns'       => ['except' => [], 'as' => 'cols'],
        'groups_filter' => ['except' => [], 'as' => 'groups'],
    ];

    protected $listeners = [
        'refreshView'  => '$refresh',
        'toggleFilter' => 'toggleFilter',
    ];

    public function mount($program)
    {
        $this->columns = [];
    }

    public function render()
    {
        $users = $this->getUsers($this->program->users());

        $filter_count = 0;
        if (count($this->columns) > 0) {
            $filter_count++;
        }
        if (count($this->family_roles) > 0) {
            $filter_count++;
        }
        if (count($this->groups_filter) > 0) {
            foreach ($this->groups_filter as $gf) {
                if ($gf > 0) {
                    $filter_count++;
                    break;
                }
            }
        }

        return view('livewire.admin.programs.view-users')
            ->with('filter_count', $filter_count)
            ->with('users', $users->paginate(15));
    }

    protected function getUsers($user_query)
    {
        // In case we're searching "Drew Johnston"... include both names in the search.
        $strings = explode(' ', $this->query_term);

        $string1 = Arr::get($strings, 0, null);
        $string2 = Arr::get($strings, 1, null);

        return $user_query
            ->when($this->query_term, function ($mainQuery) use ($string1, $string2) {
                // We MUST wrap all of this in a "where", otherwise the "user.account_id = Y" will
                // fall inside a () block that will start including other account users!
                return $mainQuery->where(function ($mainQuery) use ($string1, $string2) {
                    $mainQuery->where(function ($query) use ($string1, $string2) {
                        $query->when((!empty($string1) && empty($string2)), function ($query2) {
                            $query2->where('last_name', 'like', '%' . $this->query_term . '%')
                                ->orWhere('first_name', 'like', '%' . $this->query_term . '%')
                                ->orWhere('preferred_first_name', 'like', '%' . $this->query_term . '%');
                        })
                            ->when((!empty($string1) && !empty($string2)), function ($query) use ($string1) {
                                $query->where('first_name', 'like', '%' . $string1 . '%');
                                $query->orWhere('preferred_first_name', 'like', '%' . $string1 . '%');
                            })
                            ->when((!empty($string2) && !empty($string1)), function ($query) use ($string2) {
                                $query->where('last_name', 'like', '%' . $string2 . '%');
                            });
                    })
                        ->orWhere('email', 'like', $this->query_term . '%')
                        ->when(preg_replace('/[^0-9]/', '', $this->query_term), function ($query) {
                            $query->orWhere('mobile_phone', 'like', preg_replace('/[^0-9]/', '', $this->query_term) . '%');
                        });
                });
            })
            ->when(count($this->columns) > 0 || $this->groups_filter, function ($mainQuery) {
                $mainQuery->when(in_array('is_registered', $this->columns), function ($query) {
                    $query->whereHas('registrationGroups');
                });
                $mainQuery->when(in_array('is_contact', $this->columns), function ($query) {
                    $query->doesntHave('registrationGroups');
                });
                $mainQuery->when(!empty($this->groups_filter), function ($query) {
                    $query->whereHas('groups', function ($query) {
                        $query->whereIn('program_groups.id', $this->groups_filter);
                    });
                });
            })
            ->when($this->family_roles, function ($mainQuery) {
                $mainQuery->where(function ($subQuery) {
                    foreach (ProgramUser::$family_roles as $key => $value) {
                        if (in_array($key, $this->family_roles)) {
                            $subQuery->orWhere('family_role', $key);
                        }
                    }
                });
            })
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->orderBy('preferred_first_name', 'asc');
    }
}
