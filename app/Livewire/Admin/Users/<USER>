<?php

namespace App\Livewire\Admin\Users;

use App\Users\Services\UpdateUser;
use App\Users\User;
use Livewire\Component;

class UserGroupsAndRoles extends Component
{
    public $user;

    protected $listeners = [
        'refreshView' => '$refresh',
    ];

    public function render()
    {
        return view('livewire.admin.users.user-groups-and-roles');
    }

    public function addGroup($group_id)
    {
        $update_service = (new UpdateUser($this->user));

        if (auth()->user()->can('manageGroups', User::class)) {
            $update_service->addGroupsById([$group_id]);
        }

        $update_service->update();

        $this->dispatch('refreshView');
    }

    public function removeGroup($group_id)
    {
        $update_service = (new UpdateUser($this->user));

        if (auth()->user()->can('manageGroups', User::class)) {
            $update_service->removeGroupsById([$group_id]);
        }

        $update_service->update();

        $this->dispatch('refreshView');
    }

    public function addRole($role_id)
    {
        $update_service = (new UpdateUser($this->user));

        if (auth()->user()->can('manageRoles', User::class)) {
            $update_service->addRolesById([$role_id]);
        }

        $update_service->update();

        $this->dispatch('refreshView');
    }

    public function removeRole($role_id)
    {
        $update_service = (new UpdateUser($this->user));

        if (auth()->user()->can('manageRoles', User::class)) {
            $update_service->removeRolesById([$role_id]);
        }

        $update_service->update();

        $this->dispatch('refreshView');
    }
}
