<?php

namespace App\Livewire\Admin\Users;

use App\Involvement\Category;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class UserInvolvement extends Component
{
    public $user;
    public $involvement_records = [];
    public $categories          = [];

    protected $listeners = [
        'refreshView' => '$refresh',
    ];

    public function mount()
    {
        $this->involvement_records = $this->user->allInvolvement();
        $this->categories          = Category::visibleTo(auth()->user())->with(['areas', 'areas.subareas'])->get();
    }

    public function render()
    {
        return view('livewire.admin.users.user-involvement');
    }

    public function toggleSelection($category_id, $area_id, $subarea_id = null)
    {
        if ($subarea_id) {
            if (DB::table('involvement_to_user')
                ->where('user_id', $this->user->id)
//                ->where('involvement_category_id', $area->involvement_category_id)
//                ->where('involvement_area_id', $area_id)
                ->where('involvement_subarea_id', $subarea_id)
                ->limit(1)
                ->exists()) {
                $result = DB::table('involvement_to_user')
                    ->where('user_id', $this->user->id)
                    ->where('involvement_subarea_id', $subarea_id)
                    ->limit(1)
                    ->delete();
            } else {
                $this->user->involvementCategories()->attach($category_id, [
                    'created_at'             => now(),
                    'involvement_area_id'    => $area_id,
                    'involvement_subarea_id' => $subarea_id,
                ]);
            }
        } elseif ($area_id) {
            if (DB::table('involvement_to_user')
                ->where('user_id', $this->user->id)
//                ->where('involvement_category_id', $area->involvement_category_id)
                ->where('involvement_area_id', $area_id)
                ->whereNull('involvement_subarea_id')
                ->limit(1)
                ->exists()) {
                $result = DB::table('involvement_to_user')
                    ->where('user_id', $this->user->id)
                    ->where('involvement_area_id', $area_id)
                    ->whereNull('involvement_subarea_id')
                    ->limit(1)
                    ->delete();
            } else {
                $this->user->involvementCategories()->attach($category_id, [
                    'created_at'             => now(),
                    'involvement_area_id'    => $area_id,
                    'involvement_subarea_id' => null,
                ]);
            }
        }

        $this->dispatch('refreshView');
    }

    public function removeAllSelections()
    {
        $this->user->involvementCategories()->detach();

        return redirect()->route('admin.users.involvement.index', $this->user);
    }
}
