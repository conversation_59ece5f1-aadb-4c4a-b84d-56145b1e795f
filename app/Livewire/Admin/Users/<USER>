<?php

namespace App\Livewire\Admin\Users;

use App\Accounts\Grade;
use App\Exports\Users\UserIndexExport;
use App\Users\Group;
use App\Users\Role;
use App\Users\User;
use Illuminate\Support\Arr;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Sqids\Sqids;

class UserIndex extends Component
{
    use WithPagination;

    public $page                     = null;
    public $query_term               = null;
    public $filter_groups            = [];
    public $filter_exclude_groups    = [];
    public $filter_roles             = [];
    public $filter_grades            = [];
    public $columns                  = [];
    public $family_roles             = [];
    public $marital_statuses         = [];
    public $options                  = [];
    public $selected_users           = null;
    public $show_selected_users_only = false;

    protected $queryString = [
        'page'                     => ['except' => 1, 'as' => 'p'],
        'query_term'               => ['except' => '', 'as' => 'q'],
        'filter_groups'            => ['except' => '', 'as' => 'f_g'],
        'filter_exclude_groups'    => ['except' => '', 'as' => 'f_eg'],
        'filter_roles'             => ['except' => '', 'as' => 'f_r'],
        'filter_grades'            => ['except' => '', 'as' => 'f_gra'],
        'columns'                  => ['except' => [], 'as' => 'cols'],
        'family_roles'             => ['except' => [], 'as' => 'f_r'],
        'marital_statuses'         => ['except' => [], 'as' => 'm_s'],
        'options'                  => ['except' => [], 'as' => 'f_o'],
        'selected_users'           => ['except' => [], 'as' => 's_u'],
        'show_selected_users_only' => ['except' => false, 'as' => 'ssuo'],
    ];

    protected $listeners = [
        'refreshView'              => '$refresh',
        'initExcelDownload'        => 'initExcelDownload',
        'addGroupFilter'           => 'addGroupFilter',
        'removeGroupFilter'        => 'removeGroupFilter',
        'addGroupExcludeFilter'    => 'addGroupExcludeFilter',
        'addRoleFilter'            => 'addRoleFilter',
        'removeRoleFilter'         => 'removeRoleFilter',
        'removeGroupExcludeFilter' => 'removeGroupExcludeFilter',
        'addGrade'                 => 'addGrade',
        'removeGrade'              => 'removeGrade',
        'toggleFilter'             => 'toggleFilter',
        'queryChanged'             => 'queryChanged',
    ];

    public function mount()
    {
        $this->columns = [];
    }

    public function updated($field)
    {
        if (str_starts_with($field, 'filter_')
            || $field == 'query_term'
            || $field == 'selected_users'
            || $field == 'show_selected_users_only'
            || $field == 'no_email'
            || $field == 'has_email'
            || $field == 'no_password' || $field == 'has_password'
            || str_starts_with($field, 'columns')
            || str_starts_with($field, 'family_roles')
            || str_starts_with($field, 'marital_statuses')
            || str_starts_with($field, 'options')
        ) {
            $this->resetPage();
        }
    }

    public function queryChanged()
    {
        $this->resetPage();
    }

    public function render()
    {
        $users  = $this->getUsers();
        $groups = Group::visibleTo(auth()->user())->get();
        $roles  = Role::visibleTo(auth()->user())->get();
        $grades = Grade::visibleTo(auth()->user())->get();

        $this->dispatch('contentChanged');

        $filter_groups_js = [];
        foreach ($groups as $group) {
            $filter_groups_js[] = [
                'value'    => $group->id,
                'label'    => $group->name,
                'selected' => (is_array($this->filter_groups) && in_array($group->id, $this->filter_groups)) ? 'true' : 'false',
            ];
        }
        $filter_exclude_groups_js = [];
        foreach ($groups as $group) {
            $filter_exclude_groups_js[] = [
                'value'    => $group->id,
                'label'    => $group->name,
                'selected' => (is_array($this->filter_exclude_groups) && in_array($group->id, $this->filter_exclude_groups)) ? 'true' : 'false',
            ];
        }
        $filter_roles_js = [];
        foreach ($roles as $role) {
            $filter_roles_js[] = [
                'value'    => $role->id,
                'label'    => $role->name,
                'selected' => (is_array($this->filter_roles) && in_array($role->id, $this->filter_roles)) ? 'true' : 'false',
            ];
        }
        $filter_grades_js = [];
        foreach ($grades as $grade) {
            $filter_grades_js[] = [
                'value'    => $grade->id,
                'label'    => $grade->name,
                'selected' => (is_array($this->filter_grades) && in_array($grade->id, $this->filter_grades)) ? 'true' : 'false',
            ];
        }

        $filter_count = 0;
        if (is_array($this->filter_groups) && count($this->filter_groups) > 0) {
            $filter_count++;
        }
        if (is_array($this->filter_exclude_groups) && count($this->filter_exclude_groups) > 0) {
            $filter_count++;
        }
        if (is_array($this->filter_grades) && count($this->filter_grades) > 0) {
            $filter_count++;
        }
        if (is_array($this->columns) && count($this->columns) > 0) {
            $filter_count++;
        }
        if (is_array($this->options) && count($this->options) > 0) {
            $filter_count++;
        }
        if (is_array($this->family_roles) && count($this->family_roles) > 0) {
            $filter_count++;
        }
        if (is_array($this->marital_statuses) && count($this->marital_statuses) > 0) {
            $filter_count++;
        }

        return view('livewire.admin.users.user-index')
            ->with('selected_users_array', $this->getSelectedUsersArray())
            ->with('groups', $groups)
            ->with('roles', $roles)
            ->with('grades', $grades)
            ->with('filter_count', $filter_count)
            ->with('filter_groups_js', $filter_groups_js)
            ->with('filter_exclude_groups_js', $filter_exclude_groups_js)
            ->with('filter_roles_js', $filter_roles_js)
            ->with('filter_grades_js', $filter_grades_js)
            ->with('users', $users->paginate(25));
    }

    public function initExcelDownload()
    {
        $users = $this->getUsers();

        // Export as Excel file
        return Excel::download(new UserIndexExport($users), 'user-list-export.xlsx');
    }

    public function addGroupFilter($group_id)
    {
        $this->filter_groups = array_unique(array_merge($this->filter_groups, [$group_id]));

        $this->dispatch('refreshView');
    }

    public function removeGroupFilter($remove_group_id)
    {
        if (($key = array_search($remove_group_id, $this->filter_groups)) !== false) {
            unset($this->filter_groups[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function addRoleFilter($role_id)
    {
        $this->filter_roles = array_unique(array_merge($this->filter_roles, [$role_id]));

        $this->dispatch('refreshView');
    }

    public function removeRoleFilter($role_id)
    {
        if (($key = array_search($role_id, $this->filter_roles)) !== false) {
            unset($this->filter_roles[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function addSelectedUser($user_id)
    {
        $sqids = new Sqids();

        $this->selected_users = $sqids->encode(
            array_unique(array_merge($this->getSelectedUsersArray(), [$user_id]))
        );
    }

    public function removeSelectedUser($user_id)
    {
        $sqids = new Sqids();

        $arr = $this->getSelectedUsersArray();

//        if (($key = array_search($user_id, $arr)) !== false) {
//            unset($arr[$key]);
//        }

        foreach (array_keys($arr, (int)$user_id, true) as $key) {
            unset($arr[$key]);
        }

        // We do array value's here because apparently encode uses an index to pull values by key, even if that key doesn't exist, since we just unset one.
        $this->selected_users = $sqids->encode(array_values($arr));
    }

    public function clearSelectedUsers()
    {
        $this->selected_users           = null;
        $this->show_selected_users_only = false;
    }

    public function toggleShowSelectedUsersOnly()
    {
        $this->show_selected_users_only = !$this->show_selected_users_only;
        $this->resetPage();
    }

    public function getSelectedUsersArray()
    {
        $sqids = new Sqids();
        return $this->selected_users ? $sqids->decode($this->selected_users) : [];
    }

    public function addGroupExcludeFilter($group_id)
    {
        $this->filter_exclude_groups = array_unique(array_merge($this->filter_exclude_groups, [$group_id]));

        $this->dispatch('refreshView');
    }

    public function removeGroupExcludeFilter($remove_group_id)
    {
        if (($key = array_search($remove_group_id, $this->filter_exclude_groups)) !== false) {
            unset($this->filter_exclude_groups[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function addGrade($grade_id)
    {
        $this->filter_grades = array_unique(array_merge($this->filter_grades, [$grade_id]));

        $this->dispatch('refreshView');
    }

    public function removeGrade($grade_id)
    {
        if (($key = array_search($grade_id, $this->filter_grades)) !== false) {
            unset($this->filter_grades[$key]);
        }

        $this->dispatch('refreshView');
    }

    public function setBirthdateStart($date)
    {
        $this->filter_birthdate_start = $date;

        $this->dispatch('refreshView');
    }

    public function setBirthdateEnd($date)
    {
        $this->filter_birthdate_end = $date;

        $this->dispatch('refreshView');
    }

    public function clearOption($option_name)
    {
        unset($this->options[$option_name]);

        $this->dispatch('refreshView');
    }

    protected function getUsers()
    {
        return User::visibleTo(auth()->user())
            ->when($this->query_term, function ($mainQuery) {
                return $mainQuery->BroadSearchByString($this->query_term);
            })
            ->when($this->options, function ($mainQuery) {
                $mainQuery->when((Arr::exists($this->options, 'birthdate_start') && !empty($this->options['birthdate_start'])), function ($query) {
                    $query->where('birthdate', '>=', $this->options['birthdate_start']);
                });
                $mainQuery->when((Arr::exists($this->options, 'birthdate_end') && !empty($this->options['birthdate_end'])), function ($query) {
                    $query->where('birthdate', '<=', $this->options['birthdate_end']);
                });
                $mainQuery->when((Arr::exists($this->options, 'membership_start') && !empty($this->options['membership_start'])), function ($query) {
                    $query->where('date_membership', '>=', $this->options['membership_start']);
                });
                $mainQuery->when((Arr::exists($this->options, 'membership_end') && !empty($this->options['membership_end'])), function ($query) {
                    $query->where('date_membership', '<=', $this->options['membership_end']);
                });
                $mainQuery->when((Arr::exists($this->options, 'baptism_start') && !empty($this->options['baptism_start'])), function ($query) {
                    $query->where('date_baptism', '>=', $this->options['baptism_start']);
                });
                $mainQuery->when((Arr::exists($this->options, 'baptism_end') && !empty($this->options['baptism_end'])), function ($query) {
                    $query->where('date_baptism', '<=', $this->options['baptism_end']);
                });
            })
            ->when($this->columns, function ($mainQuery) {
                $mainQuery->when(in_array('is_baptized', $this->columns), function ($query) {
                    $query->whereNotNull('is_baptized');
                });
                $mainQuery->when(in_array('is_not_baptized', $this->columns), function ($query) {
                    $query->whereNull('is_baptized');
                });
                $mainQuery->when(in_array('date_background_check', $this->columns), function ($query) {
                    $query->whereNotNull('date_background_check');
                });
                $mainQuery->when(in_array('can_teach', $this->columns), function ($query) {
                    $query->whereNotNull('can_teach');
                });
                $mainQuery->when(in_array('can_lead', $this->columns), function ($query) {
                    $query->whereNotNull('can_lead');
                });
                $mainQuery->when(in_array('head_of_household_only', $this->columns), function ($query) {
                    $query->headsOfFamily();
                });
                $mainQuery->when(in_array('children_only', $this->columns), function ($query) {
                    $query->childrenOnly();
                });
                $mainQuery->when(in_array('adults_only', $this->columns), function ($query) {
                    $query->adultsOnly();
                });
                $mainQuery->when(in_array('men_only', $this->columns), function ($query) {
                    $query->menOnly();
                });
                $mainQuery->when(in_array('women_only', $this->columns), function ($query) {
                    $query->womenOnly();
                });
                $mainQuery->when(in_array('no_email', $this->columns), function ($query) {
                    $query->whereDoesntHave('emails');
                });
                $mainQuery->when(in_array('has_email', $this->columns), function ($query) {
                    $query->whereHas('emails');
                });
                $mainQuery->when(in_array('no_password', $this->columns), function ($query) {
                    $query->whereNull('password');
                });
                $mainQuery->when(in_array('has_password', $this->columns), function ($query) {
                    $query->whereNotNull('password');
                });
            })
            ->when($this->family_roles, function ($mainQuery) {
                $mainQuery->where(function ($subQuery) {
                    foreach (User::$family_roles as $key => $value) {
                        if (in_array($key, $this->family_roles)) {
                            $subQuery->orWhere('users.family_role', $key);
                        }
                    }
                });
            })
            ->when($this->marital_statuses, function ($mainQuery) {
                $mainQuery->where(function ($subQuery) {
                    foreach (User::$marital_statuses as $key => $value) {
                        if (in_array($key, $this->marital_statuses)) {
                            $subQuery->orWhere('users.marital_status', $key);
                        }
                    }
                });
            })
            ->when($this->filter_groups, function ($mainQuery) {
                $mainQuery->whereHas('groups', function ($query) {
                    $query->whereIn('user_groups.id', $this->filter_groups ?: []);
                });
            })
            ->when($this->filter_exclude_groups, function ($mainQuery) {
                $mainQuery->whereDoesntHave('groups', function ($query) {
                    $query->whereIn('user_groups.id', $this->filter_exclude_groups ?: []);
                });
            })
            ->when($this->filter_roles, function ($mainQuery) {
                $mainQuery->whereHas('roles', function ($query) {
                    $query->whereIn('user_roles.id', $this->filter_roles ?: []);
                });
            })
            ->when($this->filter_grades, function ($mainQuery) {
                $mainQuery->whereIn('user_grade_id', $this->filter_grades ?: []);
            })
            ->withFamilyLastName()
            ->with(['emails'])
            ->when($this->show_selected_users_only, function ($mainQuery) {
                return $mainQuery->whereIn('users.id', $this->getSelectedUsersArray());
            })
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->orderBy('preferred_first_name', 'asc');
    }
}
