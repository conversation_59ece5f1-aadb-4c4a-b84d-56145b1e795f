<?php

namespace App\Livewire\Admin\Calendars\Events;

use App\Base\DTOs\DateRange;
use App\Calendars\Calendar;
use App\Calendars\EventOccurrence;
use App\Calendars\Services\DeleteCalendarEvent;
use App\Calendars\Services\DeleteCalendarEventOccurrence;
use App\Calendars\Services\UpdateEventOccurrence;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class OccurrenceEdit extends Component
{
    public EventOccurrence $occurrence;

    // DATES AND TIMES IN USER TIMEZONE.
    public $event_start_at;
    public $event_end_at;
    public $calendars             = [];
    public $occurrence_attributes = [];
    public $event_start_at_date;
    public $event_start_at_hour;
    public $event_start_at_minute;
    public $event_end_at_date;
    public $event_end_at_hour;
    public $event_end_at_minute;
    public $event_account_id;
    public $event_calendar_id;
    public $event_is_all_day;
    public $event_title;
    public $event_overview;
    public $event_description;
    public $event_location;
    public $event_internal_notes;
    public $event_enable_comments;
    public $event_responses_require_name;
    public $event_responses_require_email;
    public $event_responses_require_phone;
    public $event_updated_by_user_id;
    public $event_owner_user_id;

    public $dates_error   = null;
    public $dates_warning = null;
    public $recur_error   = null;
    public $general_error = null;

    public function mount(EventOccurrence $occurrence)
    {
        $this->occurrence = $occurrence;

        $this->occurrence_attributes = $occurrence->attributesToArray();

        $this->event_start_at        = $occurrence->start_at->setTimezone(auth()->user()->account->timezone);
        $this->event_start_at_date   = $occurrence->start_at->setTimezone(auth()->user()->account->timezone)->format('Y-m-d');
        $this->event_start_at_hour   = $occurrence->start_at->setTimezone(auth()->user()->account->timezone)->format('H');
        $this->event_start_at_minute = $occurrence->start_at->setTimezone(auth()->user()->account->timezone)->format('i');

        $this->event_end_at        = $occurrence->end_at->setTimezone(auth()->user()->account->timezone);
        $this->event_end_at_date   = $occurrence->end_at->setTimezone(auth()->user()->account->timezone)->format('Y-m-d');
        $this->event_end_at_hour   = $occurrence->end_at->setTimezone(auth()->user()->account->timezone)->format('H');
        $this->event_end_at_minute = $occurrence->end_at->setTimezone(auth()->user()->account->timezone)->format('i');

        $this->event_is_all_day = $occurrence->is_all_day ?: $occurrence->event->is_all_day;

        $this->event_title       = $occurrence->title;
        $this->event_location    = $occurrence->location;
        $this->event_description = $occurrence->description;

        $this->event_calendar_id = $occurrence->calendar_id;

        $this->calendars = Calendar::visibleToAccount(auth()->user())->get();
    }

    public function render()
    {
        return view('livewire.admin.calendars.events.occurrences.occurrence-edit');
    }

    public function rendered()
    {
        // Reset errors
        $this->dates_error   = null;
        $this->dates_warning = null;
        $this->recur_error   = null;
    }

    public function saveChanges()
    {
        $this->general_error = null;

        if ($this->event_start_at->diffInDays($this->event_end_at) > 56) {
            $this->general_error = 'Sorry! Currently, events cannot last more than 8 weeks.  Did you mean to create a recurring event instead of one really long event?';
            return;
        }

        try {
            (new UpdateEventOccurrence($this->occurrence))
                ->setStartAt(value_in_local_timezone: $this->event_start_at)
                ->setEndAt(value_in_local_timezone: $this->event_end_at)
                ->setIsAllDay($this->event_is_all_day)
                ->updatedByUser(auth()->user())
                ->update([
                    'title'       => $this->event_title,
                    'description' => $this->event_description,
                    'calendar_id' => $this->event_calendar_id,
                    'location'    => $this->event_location,
                ]);
        } catch (\Exception $e) {
            $this->general_error = 'Oops! There was an error trying to create your event.  Please reach out to support.  The details of this issue has been logged for researching the issue.';

            Log::error('OccurrenceEdit::saveChanges - Error creating new event.', [
                'account_id' => auth()->user()->account_id,
                'e'          => $e,
                'message'    => $e->getMessage(),
            ]);

            return;
        }

        return redirect()->to(route('admin.calendars.index'))
            ->with('message.success', 'Event occurrence saved successfully.');
    }

    public function setDateTime()
    {
        // Use the date string property instead of the Carbon object
        $new_start_at_date = Carbon::createFromFormat(
            'Y-m-d G:i:s',
            $this->event_start_at_date . ' ' . $this->event_start_at_hour . ':' . $this->event_start_at_minute . ':00',
            auth()->user()->account->timezone
        );
        $new_end_at_date   = Carbon::createFromFormat(
            'Y-m-d G:i:s',
            $this->event_end_at_date . ' ' . $this->event_end_at_hour . ':' . $this->event_end_at_minute . ':00',
            auth()->user()->account->timezone
        );

        try {
            $date_range = (new DateRange())
                ->setStartDate($new_start_at_date)
                ->setEndDate($new_end_at_date)
                ->validate();
        } catch (ValidationException $e) {
            $this->dates_warning = $e->getMessage();
            return;
        }

        $this->event_end_at        = $date_range->end_at;
        $this->event_end_at_date   = $date_range->end_at->format('Y-m-d');
        $this->event_end_at_hour   = $date_range->end_at->format('H');
        $this->event_end_at_minute = $date_range->end_at->format('i');

        $this->event_start_at        = $date_range->start_at;
        $this->event_start_at_date   = $date_range->start_at->format('Y-m-d');
        $this->event_start_at_hour   = $date_range->start_at->format('H');
        $this->event_start_at_minute = $date_range->start_at->format('i');

        if ($date_range->hasWarnings()) {
            $this->dates_warning = $date_range?->getWarnings();
        }
    }

    public function deleteOccurrence()
    {
        try {
            if ($this->occurrence->event->occurrences()->count() > 1) {
                // We want to redirect to the next occurrence if possible after deleting this occurrence.
                $redirect_occurrence = $this->occurrence->nextOccurrence();

                // If we don't have a next occurrence, we'll redirect to the previous occurrence.
                if (!$redirect_occurrence) {
                    $redirect_occurrence = $this->occurrence->previousOccurrence();
                }

                (new DeleteCalendarEventOccurrence($this->occurrence))->delete();

                // If we have a next occurrence, we'll redirect to it.
                if ($redirect_occurrence) {
                    return redirect()->to(route('admin.calendars.event.occurrence.view', $redirect_occurrence))
                        ->with('message.success', 'Event occurrence deleted.');
                } // If we don't have a next occurrence, we'll redirect back to the calendar index.
                else {
                    return redirect()->to(route('admin.calendars.index'))
                        ->with('message.success', 'Event deleted. (Last occurrence deleted)');
                }
            } else {
                (new DeleteCalendarEvent($this->occurrence->event))->delete();

                return redirect()->to(route('admin.calendars.index'))
                    ->with('message.success', 'Event deleted.');
            }
        } catch (\Exception $e) {
            Log::error('OccurrenceEdit::deleteOccurrence - Error deleting event.', [
                'account_id'          => auth()->user()->account_id,
                'user_id'             => auth()->user()->id,
                'event_id'            => $this->occurrence->event->id,
                'event_occurrence_id' => $this->occurrence->id,
                'e'                   => $e,
                'message'             => $e->getMessage(),
            ]);

            return back()->with('message.failure', 'Event could not be deleted.');
        }
    }
}
