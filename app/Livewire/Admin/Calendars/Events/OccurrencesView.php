<?php

namespace App\Livewire\Admin\Calendars\Events;

use App\Calendars\EventOccurrence;
use Livewire\Component;

class OccurrencesView extends Component
{
    public EventOccurrence $occurrence;

    public $dates_error   = null;
    public $dates_warning = null;
    public $recur_error   = null;
    public $general_error = null;

    public function mount(EventOccurrence $occurrence)
    {
        $this->occurrence = $occurrence;
    }

    public function render()
    {
        return view('livewire.admin.calendars.events.occurrences.occurrences-view');
    }

    public function rendered()
    {
        // Reset errors
        $this->dates_error   = null;
        $this->dates_warning = null;
        $this->recur_error   = null;
    }
}
