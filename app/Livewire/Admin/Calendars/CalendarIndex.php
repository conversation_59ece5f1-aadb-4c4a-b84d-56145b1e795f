<?php

namespace App\Livewire\Admin\Calendars;

use App\Calendars\Calendar;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class CalendarIndex extends Component
{
    public $calendars;
    public $group_calendars;
    public $all_calendars;
    public $all_calendars_json = [];

    public function mount()
    {
        $this->calendars       = Calendar::visibleToAccount(auth()->user())->withoutGroups()->get();
        $this->group_calendars = Calendar::visibleToAccount(auth()->user())->onlyGroups()->get();
        $this->all_calendars   = Calendar::visibleToAccount(auth()->user())->get();

        foreach ($this->all_calendars as $calendar) {
            $this->all_calendars_json[] = [
                'id'        => $calendar->ulid,
                'url'       => '/calendars/' . $calendar->ulid,
                'className' => 'cal_id_' . $calendar->ulid . ($calendar->user_group_id ? ' group_cal' : null),
                'method'    => 'GET',
                'color'     => '#' . $calendar->getBackgroundColor(),
                'textColor' => '#' . $calendar->getFontColor(),
            ];
        }

        $this->all_calendars_json = json_encode($this->all_calendars_json);
    }

    public function render()
    {
        return view('livewire.admin.calendars.calendar-index');
    }
} 