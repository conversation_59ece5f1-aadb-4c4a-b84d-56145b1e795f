<?php

namespace App\Livewire\Admin\Calendars;

use App\Calendars\Calendar;
use Livewire\Component;

class CreateEvent extends Component
{
    protected $queryString = [
        'selected_calendar_id'            => ['as' => 'cal'],
        'event_is_all_day'                => ['as' => 'all_day'],
        'event_title'                     => ['as' => 'title'],
        'event_overview'                  => ['as' => 'overview'],
        'event_description'               => ['as' => 'details'],
        'event_location'                  => ['as' => 'location'],
        'event_start_at'                  => ['as' => 'start'],
        'event_start_at_date'             => ['as' => 'start_date'],
        'event_start_at_hour'             => ['as' => 'start_hour'],
        'event_start_at_minute'           => ['as' => 'start_minute'],
        'event_end_at'                    => ['as' => 'end'],
        'event_end_at_date'               => ['as' => 'end_date'],
        'event_end_at_hour'               => ['as' => 'end_hour'],
        'event_end_at_minute'             => ['as' => 'end_minute'],
        'event_recur_end_at'              => ['as' => 'r_end'],
        'event_recur_end_at_date'         => ['as' => 'r_end_date'],
        'event_is_recurring'              => ['as' => 'recur'],
        'event_recurring_frequency'       => ['as' => 'recur_freq'],
        'event_recurring_interval'        => ['as' => 'recur_int'],
        'event_recurring_days'            => ['as' => 'recur_days'],
        'event_recurring_monthly_type'    => ['as' => 'recur_monthly_type'],
        'event_recurring_step_position'   => ['as' => 'recur_step_pos'],
        'event_recurring_by_day'          => ['as' => 'recur_by_day'],
        'event_recurring_by_month'        => ['as' => 'recur_by_month'],
        'event_recurring_by_day_of_month' => ['as' => 'recur_by_day_of_month'],
    ];

    public $selected_calendar_id            = null;
    public $event_is_all_day                = null;
    public $event_title                     = null;
    public $event_description               = null;
    public $event_overview                  = null;
    public $event_location                  = null;
    public $event_start_at                  = null;
    public $event_start_at_date             = null;
    public $event_start_at_hour             = null;
    public $event_start_at_minute           = 0;
    public $event_end_at                    = null;
    public $event_end_at_date               = null;
    public $event_end_at_hour               = null;
    public $event_end_at_minute             = 0;
    public $event_recur_end_at              = null;
    public $event_recur_end_at_date         = null;
    public $event_recur_end_at_hour         = null;
    public $event_recur_end_at_minute       = null;
    public $event_is_recurring              = null;
    public $event_recurring_frequency       = null;
    public $event_recurring_interval        = 1;
    public $event_recurring_days            = ['SU'];
    public $event_recurring_monthly_type    = 'day_of_month';
    public $event_recurring_step_position   = null;
    public $event_recurring_by_month        = null;
    public $event_recurring_by_day          = 'SU';
    public $event_recurring_by_day_of_month = null;

    // Keep track if we've set the dates manually.
    // This comes into effect when we change our start_at from 01/30 to 01/01.
    //  If we've not changed our end_at, we'll sync it to track our start_at so that we don't accidentally create an event from 01/01-01/30.
    public $user_set_start_at = false;
    public $user_set_end_at   = false;

    public $dates_error   = null;
    public $dates_warning = null;
    public $recur_error   = null;
    public $general_error = null;

    public $rrule_human_readable = null;

    public function render()
    {
        $calendars = Calendar::visibleToAccount(auth()->user())->get();

        // Reset errors
        $this->dates_error   = null;
        $this->dates_warning = null;
        $this->recur_error   = null;

        // If we have no calendar defaulted, default to the first one.
        if (!$this->selected_calendar_id && $calendars) {
            $this->selected_calendar_id = $calendars?->first()?->id;
        }

        $this->refreshDates();

        if ($this->event_is_recurring) {
            if (!$this->event_recurring_frequency) {
                $this->event_recurring_frequency = 'DAILY';
            }

            // If we have an end date set, set the DateTime version of it too.
            if ($this->event_recur_end_at_date) {
                $this->event_recur_end_at = now()->setDateFrom($this->event_recur_end_at_date)
                    ->setHour(12)
                    ->setMinute(0)
                    ->setSecond(0);
            }

            // If we don't have an end date, create one a week from now.
            if (!$this->event_recur_end_at) {
                $this->event_recur_end_at      = now()->addWeek();
                $this->event_recur_end_at_date = $this->event_recur_end_at->format('Y-m-d');

                // If this date is AFTER our start_date, set it to a week after our start_date.
                if ($this->event_start_at > $this->event_recur_end_at) {
                    $this->event_recur_end_at      = (clone $this->event_start_at)->addWeek();
                    $this->event_recur_end_at_date = $this->event_recur_end_at->format('Y-m-d');
                }
            }
            // If we have an end date, and it's more than 18 months out, reset to only 18 months away.
            if ($this->event_recur_end_at > now()->addMonths(18) && $this->event_recurring_frequency != 'YEARLY') {
                $this->event_recur_end_at      = now()->addMonths(18);
                $this->event_recur_end_at_date = $this->event_recur_end_at->format('Y-m-d');
            }

            // If we selected MONTHLY, and have no day_of_month selected.
            if ($this->event_recurring_frequency == 'MONTHLY'
                && $this->event_recurring_monthly_type == 'day_of_month'
                && !$this->event_recurring_by_day_of_month) {
                $this->event_recurring_by_day_of_month = 1;
            } // If we selected by step position, but have no details selected.
            elseif ($this->event_recurring_frequency == 'MONTHLY'
                    && $this->event_recurring_monthly_type == 'step_position'
                    && (!$this->event_recurring_step_position || empty($this->event_recurring_by_day))) {
                $this->event_recurring_step_position = 1;
                $this->event_recurring_days          = ['SU'];
            }
        } else {
            $this->event_recurring_frequency = null;
        }

        $create_event = (new \App\Calendars\Services\CreateEvent())
            ->forCalendar($this->selected_calendar_id)
            ->createdByUser(auth()->user())
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt(now()->setDateTimeFrom((clone $this->event_start_at)->setTimezone('UTC')))
            ->setEndAt(now()->setDateTimeFrom((clone $this->event_end_at)->setTimezone('UTC')))
            ->setTitle($this->event_title)
            ->setOverview($this->event_overview)
            ->setDescription($this->event_description)
            ->setIsAllDay($this->event_is_all_day);

        if ($this->event_is_recurring) {
            // We set everything and let the CreateEvent service figure it out.
            $create_event->setRecurFrequency($this->event_recurring_frequency)
                ->setRecurInterval($this->event_recurring_interval)
                // We do NOT set our timezone here, as the "end_at" is just a day.
                ->setRecurEndAt(now()->setDateFrom($this->event_recur_end_at)->setHour(12)->setMinute(00))
                ->setRecurByDay($this->event_recurring_days);

            if ($this->event_recurring_monthly_type == 'step_position') {
                $create_event
                    ->setRecurByMonth($this->event_recurring_by_month)
                    ->setRecurByDay($this->event_recurring_days)
                    ->setRecurSetPosition($this->event_recurring_step_position);
            } elseif ($this->event_recurring_monthly_type == 'day_of_month') {
                $create_event
                    ->setRecurByMonth($this->event_recurring_by_month)
                    ->setRecurByMonthDay($this->event_recurring_by_day_of_month)
                    ->setRecurSetPosition($this->event_recurring_step_position);
            }
        }

        $create_event->createRRule();

        $rrule_occurrences = null;
        if ($this->event_is_recurring) {
            $this->rrule_human_readable = $create_event->rrule_human_readable;
            $rrule_occurrences          = $create_event->rrule_occurrences;
        }

        return view('livewire.admin.calendars.create-event')
            ->with('calendars', $calendars)
            ->with('temp_event', $create_event)
            ->with('rrule_occurrences', $rrule_occurrences)
            ->with('selected_calendar', Calendar::find($this->selected_calendar_id));
    }

    public function refreshDates()
    {
        // TIMEZONE -- We DO set a timezone here, so the times the user see's is the current time.
        // If we have no start date, set it to today.
        if (!$this->event_start_at_date) {
            $this->event_start_at_date = now()->setTimezone(auth()->user()->account->timezone)->format('Y-m-d');
            $this->event_end_at_date   = now()->setTimezone(auth()->user()->account->timezone)->format('Y-m-d');
        }
        // If we have no start hour, set it to now, and end_at to an hour from now.
        if (!$this->event_start_at_hour) {
            $this->event_start_at_hour = now()->setTimezone(auth()->user()->account->timezone)->format('G');
            $this->event_end_at_hour   = now()->setTimezone(auth()->user()->account->timezone)->addHour()->format('G');
        }

        if (!$this->user_set_end_at) {
            $this->event_end_at_date = $this->event_start_at_date;
        }

        if ($this->event_is_all_day) {
            $this->event_start_at_hour   = 12;
            $this->event_start_at_minute = 0;
            $this->event_end_at_hour     = 12;
            $this->event_end_at_minute   = 0;
        }

        // Set our start_at and end_at as DateTime objects.
        // We set USER TIMEZONE here -- we'll change that before we create the event.
        $this->event_start_at = now()
            ->setTimezone(auth()->user()->account->timezone)
            ->setDateFrom($this->event_start_at_date)
            ->setHour((int)$this->event_start_at_hour)
            ->setMinute((int)$this->event_start_at_minute)
            ->setSecond(0);
        $this->event_end_at   = now()
            ->setTimezone(auth()->user()->account->timezone)
            ->setDateFrom($this->event_end_at_date)
            ->setHour((int)$this->event_end_at_hour)
            ->setMinute((int)$this->event_end_at_minute)
            ->setSecond(0);

        // If end date is less than start date, fix it.
        if ($this->event_start_at >= $this->event_end_at) {
            $this->event_end_at = now()
                ->setDateFrom($this->event_start_at_date)
                ->setHour((int)$this->event_start_at_hour)
                ->setMinute((int)$this->event_start_at_minute)
                ->setSecond(0)
                ->addHour();

            $this->event_end_at_date = $this->event_end_at->format('Y-m-d');
            $this->event_end_at_hour = $this->event_end_at->format('G');

            $this->dates_warning = 'End date must be after start date. We moved the date forward for you.';
        }

        // If our event is more than 42 days, we can't do that.
        if ($this->event_start_at->diffInDays($this->event_end_at) > 42) {
//            $this->event_end_at = now()
//                ->setDateFrom($this->event_start_at_date)
//                ->setHour((int)$this->event_start_at_hour)
//                ->setMinute((int)$this->event_start_at_minute)
//                ->setSecond(0)
//                ->addHour();

//            $this->event_end_at_date = $this->event_end_at->format('Y-m-d');
//            $this->event_end_at_hour = $this->event_end_at->format('G');

            // We just set an error and let the user fix the error.  Auto-correcting the date makes this warning go away immediately.
            $this->dates_warning = 'Sorry! Currently, events cannot last more than 6 weeks.';
        }
    }

    public function createEvent()
    {
        $this->general_error = null;

        if ($this->event_start_at->diffInDays($this->event_end_at) > 42) {
            $this->general_error = 'Sorry! Currently, events cannot last more than 6 weeks.  Did you mean to create a recurring event instead of one really long event?';
            return;
        }

        $create_event = (new \App\Calendars\Services\CreateEvent())
            ->forCalendar($this->selected_calendar_id)
            ->createdByUser(auth()->user())
            // We set our Timezone here, before we hand it to the CreateEvent service.
            // Change to UTC WITH the clone, and not after setDateTimeFrom, otherwise it keeps the date/time but shifts the timezone -- we want to shift the timezone AND date/time
            ->setStartAt(now()->setDateTimeFrom((clone $this->event_start_at)->setTimezone('UTC')))
            ->setEndAt(now()->setDateTimeFrom((clone $this->event_end_at)->setTimezone('UTC')))
            ->setTitle($this->event_title)
            ->setLocation($this->event_location)
            ->setOverview($this->event_overview)
            ->setDescription($this->event_description)
            ->setIsAllDay($this->event_is_all_day);

        if ($this->event_is_recurring) {
            // We set everything and let the CreateEvent service figure it out.
            $create_event->setRecurFrequency($this->event_recurring_frequency)
                ->setRecurInterval($this->event_recurring_interval)
                // We do NOT set our timezone here, as the "end_at" is just a day.
                ->setRecurEndAt(now()->setDateFrom($this->event_recur_end_at)->setHour(12)->setMinute(00))
                ->setRecurByDay($this->event_recurring_days);

            if ($this->event_recurring_monthly_type == 'step_position') {
                $create_event
                    ->setRecurByMonth($this->event_recurring_by_month)
                    ->setRecurByDay($this->event_recurring_days)
                    ->setRecurSetPosition($this->event_recurring_step_position);
            } elseif ($this->event_recurring_monthly_type == 'day_of_month') {
                $create_event
                    ->setRecurByMonth($this->event_recurring_by_month)
                    ->setRecurByMonthDay($this->event_recurring_by_day_of_month)
                    ->setRecurSetPosition($this->event_recurring_step_position);
            }
        }

        try {
            $event = $create_event->create();

            return redirect()->to(route('admin.calendars.index'));
        } catch (\Exception $e) {
            $this->general_error = $e->getMessage();
        }
    }

    public function changeFrequency()
    {
        if ($this->event_recurring_frequency == 'YEARLY') {
            $this->event_recurring_step_position   = null;
            $this->event_recurring_days            = [];
            $this->event_recurring_by_day_of_month = null;
            $this->event_recurring_by_day          = null;
        }
        if ($this->event_recurring_frequency == 'MONTHLY') {
            $this->event_recurring_step_position   = null;
            $this->event_recurring_days            = ['SU'];
            $this->event_recurring_by_day_of_month = 1;
            $this->event_recurring_by_day          = 'SU';
        }
        if ($this->event_recurring_frequency == 'WEEKLY') {
            $this->event_recurring_step_position   = null;
            $this->event_recurring_days            = [];
            $this->event_recurring_by_day          = null;
            $this->event_recurring_by_day_of_month = null;
        }
    }

    public function setRecurringType($type)
    {
        if ($type == 'monthly_by_step_position') {
            $this->event_recurring_step_position = 1;
            $this->event_recurring_days          = ['SU'];

            $this->event_recurring_by_day_of_month = null;
        } elseif ($type == 'monthly_by_day_of_month') {
            $this->event_recurring_step_position = null;
            $this->event_recurring_days          = [];

            $this->event_recurring_by_day_of_month = 1;
        }
    }

    public function setRecurringDaysByCSV()
    {
        $this->event_recurring_days = explode(',', $this->event_recurring_by_day);
    }

    public function setRecurringDay($day_as_string)
    {
        if (in_array($day_as_string, $this->event_recurring_days)) {
            // Unset if it's already set.
            $this->event_recurring_days = array_diff($this->event_recurring_days, [$day_as_string]);
        } else {
            $this->event_recurring_days[] = $day_as_string;
        }

        $this->event_recurring_by_day = implode(',', $this->event_recurring_days);
    }

    public function setStartAt()
    {
        $this->user_set_start_at = true;
    }

    public function setEndAt()
    {
        $this->user_set_end_at = true;
    }
}
