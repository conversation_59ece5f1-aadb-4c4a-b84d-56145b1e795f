<?php

namespace App\Livewire\Admin\Groups;

use App\Users\Group;
use Livewire\Component;
use Livewire\WithPagination;

class GroupIndex extends Component
{
    use WithPagination;

    public $query_term = null;
    public $columns    = [];

    protected $queryString = [
        'page'       => ['except' => 1, 'as' => 'p'],
        'query_term' => ['except' => '', 'as' => 'q'],
    ];

    protected $listeners = [
        'refreshView' => '$refresh',
    ];

    public function mount()
    {
        $this->columns = [];
    }

    public function render()
    {
        $groups = Group::visibleTo(auth()->user())
            ->when($this->query_term, function ($query2) {
                $query2->where('name', 'like', '%' . $this->query_term . '%');
            })
            ->with('messageTypes:message_types.id,name')
            ->withCount(['users', 'senders'])
            ->orderBy('name')
            ->paginate(30);

        return view('livewire.admin.groups.group-index')
            ->with('groups', $groups);
    }
}
