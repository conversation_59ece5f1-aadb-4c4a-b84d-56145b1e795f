<?php

namespace App\Livewire\Admin\Groups;

use App\Users\Group;
use App\Users\Services\UpdateGroup;
use App\Users\Services\UpdateUser;
use App\Users\User;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class ViewUsers extends Component
{
    use WithPagination;

    public $group;
    public $query_term   = null;
    public $columns      = [];
    public $family_roles = [];

    // members, admins or senders
    public $group_user_mode     = 'members';
    public $add_users           = false;
    public $copy_users_view     = false;
    public $group_to_copy_id    = null;
    public $add_user_query_term = null;
    public $user_added          = null;

    protected $queryString = [
        'page'       => ['except' => 1, 'as' => 'p'],
        'query_term' => ['except' => '', 'as' => 'q'],
        'columns'    => ['except' => [], 'as' => 'cols'],
    ];

    protected $listeners = [
        'refreshView'  => '$refresh',
        'toggleFilter' => 'toggleFilter',
    ];

    public function mount($group, $group_user_mode = null)
    {
        $this->group   = $group;
        $this->columns = [];

        if ($group_user_mode) {
            $this->group_user_mode = $group_user_mode;
        }
    }

    public function render()
    {
        $add_user_users = null;

        if ($this->add_user_query_term) {
            if ($this->group_user_mode == 'members') {
                $add_user_query = User::visibleTo(auth()->user());
            } else {
                $add_user_query = $this->group->users();
            }

            $add_user_users = $add_user_query->where(function ($query2) {
                $query2->where('last_name', 'like', '%' . $this->add_user_query_term . '%')
                    ->orWhere('first_name', 'like', '%' . $this->add_user_query_term . '%')
                    ->orWhere('preferred_first_name', 'like', '%' . $this->add_user_query_term . '%');
            })
                ->membersOnly()
                ->IsNotDeceased()
                ->limit(12)
                ->get();
        }

        switch ($this->group_user_mode) {
            case('members'):
                $users = $this->getUsers($this->group->users());
                break;
            case('admins'):
                $users = $this->getUsers($this->group->admins());
                break;
            case('senders'):
                $users = $this->getUsers($this->group->senders());
                break;
            default:
                $users = $this->getUsers($this->group->users());
                break;
        }

        $groups = Group::visibleTo(auth()->user())->get();

        $filter_count = 0;
        if (count($this->columns) > 0) {
            $filter_count++;
        }
        if (count($this->family_roles) > 0) {
            $filter_count++;
        }

        return view('livewire.admin.groups.view-users')
            ->with('groups', $groups)
            ->with('filter_count', $filter_count)
            ->with('users', $users->paginate(15))
            ->with('add_user_users', $add_user_users);
    }

    public function toggleAddUser()
    {
        $this->add_users = !$this->add_users;
    }

    public function toggleCopyUsers()
    {
        $this->copy_users_view = !$this->copy_users_view;
    }

    public function addUser($user_id)
    {
        $user = User::visibleTo(auth()->user())->find($user_id);

        if (!$user) {
            return;
        }

        switch ($this->group_user_mode) {
            case('members'):
                DB::transaction(function () use ($user) {
                    (new UpdateUser($user))
                        ->addGroupsById([$this->group->id])
                        ->update();

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            case('admins'):
                DB::transaction(function () use ($user) {
                    (new UpdateUser($user))
                        ->updateExistingGroup($this->group->id, [
                            'is_admin' => true,
                        ])
                        ->update([]);

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            case('senders'):
                DB::transaction(function () use ($user) {
                    $this->group->senders()->attach($user->id, [
                        'account_id' => auth()->user()->account_id,
                    ]);

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            default:

                break;
        }

        $this->user_added = $user->name;

        $this->dispatch('refreshView');
    }

    public function copyUsers()
    {
        $copy_from_group = Group::visibleTo(auth()->user())->find($this->group_to_copy_id);

        if (!$copy_from_group) {
            return;
        }

        switch ($this->group_user_mode) {
            case('members'):
                DB::transaction(function () use ($copy_from_group) {
                    foreach ($copy_from_group->users as $user) {
                        (new UpdateUser($user))
                            ->addGroupsById([$this->group->id])
                            ->update();
                    }

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            case('admins'):
                DB::transaction(function () use ($copy_from_group) {
                    foreach ($copy_from_group->admins as $user) {
                        (new UpdateUser($user))
                            ->updateExistingGroup($this->group->id, [
                                'is_admin' => true,
                            ])
                            ->update([]);
                    }

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            case('senders'):
                DB::transaction(function () use ($copy_from_group) {
                    foreach ($copy_from_group->senders as $user) {
                        $this->group->senders()->attach($user->id, [
                            'account_id' => auth()->user()->account_id,
                        ]);
                    }

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            default:

                break;
        }

        $this->group_to_copy_id = null;
        $this->copy_users_view  = false;

        $this->dispatch('refreshView');
    }

    public function removeUser($user_id)
    {
        $user = User::visibleTo(auth()->user())->find($user_id);

        if (!$user) {
            return;
        }

        switch ($this->group_user_mode) {
            case('members'):
                DB::transaction(function () use ($user) {
                    $user->groups()->detach($this->group->id);

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            case('admins'):
                DB::transaction(function () use ($user) {
                    (new UpdateUser($user))
                        ->updateExistingGroup($this->group->id, [
                            'is_admin' => false,
                        ])
                        ->update([]);

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            case('senders'):
                DB::transaction(function () use ($user) {
                    $this->group->senders()->detach($user->id);

                    (new UpdateGroup($this->group))->touch();
                });
                break;
            default:

                break;
        }

        $this->dispatch('refreshView');
    }

    protected function getUsers($user_query)
    {
        return $user_query
            ->when($this->query_term, function ($mainQuery) {
                return $mainQuery->BroadSearchByString($this->query_term);
            })
            ->when($this->columns, function ($mainQuery) {
                $mainQuery->when(in_array('is_baptized', $this->columns), function ($query) {
                    $query->whereNotNull('is_baptized');
                });
                $mainQuery->when(in_array('is_not_baptized', $this->columns), function ($query) {
                    $query->whereNull('is_baptized');
                });
                $mainQuery->when(in_array('date_background_check', $this->columns), function ($query) {
                    $query->whereNotNull('date_background_check');
                });
                $mainQuery->when(in_array('can_teach', $this->columns), function ($query) {
                    $query->whereNotNull('can_teach');
                });
                $mainQuery->when(in_array('can_lead', $this->columns), function ($query) {
                    $query->whereNotNull('can_lead');
                });
                $mainQuery->when(in_array('head_of_household_only', $this->columns), function ($query) {
                    $query->headsOfFamily();
                });
                $mainQuery->when(in_array('children_only', $this->columns), function ($query) {
                    $query->childrenOnly();
                });
                $mainQuery->when(in_array('adults_only', $this->columns), function ($query) {
                    $query->adultsOnly();
                });
                $mainQuery->when(in_array('men_only', $this->columns), function ($query) {
                    $query->menOnly();
                });
                $mainQuery->when(in_array('women_only', $this->columns), function ($query) {
                    $query->womenOnly();
                });
            })
            ->when($this->family_roles, function ($mainQuery) {
                $mainQuery->where(function ($subQuery) {
                    foreach (User::$family_roles as $key => $value) {
                        if (in_array($key, $this->family_roles)) {
                            $subQuery->orWhere('users.family_role', $key);
                        }
                    }
                });
            })
            ->withFamilyLastName()
            ->with(['emails'])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->orderBy('preferred_first_name', 'asc');
    }
}
