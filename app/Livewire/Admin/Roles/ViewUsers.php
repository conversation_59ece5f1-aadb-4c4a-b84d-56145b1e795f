<?php

namespace App\Livewire\Admin\Roles;

use App\Users\Group;
use App\Users\Services\UpdateUser;
use App\Users\User;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class ViewUsers extends Component
{
    use WithPagination;

    public $role;
    public $query_term   = null;
    public $columns      = [];
    public $family_roles = [];

    // members, admins or senders
    public $add_users           = false;
    public $add_user_query_term = null;
    public $user_added          = null;

    protected $queryString = [
        'page'       => ['except' => 1, 'as' => 'p'],
        'query_term' => ['except' => '', 'as' => 'q'],
        'columns'    => ['except' => [], 'as' => 'cols'],
    ];

    protected $listeners = [
        'refreshView'  => '$refresh',
        'toggleFilter' => 'toggleFilter',
    ];

    public function mount($role)
    {
        $this->role    = $role;
        $this->columns = [];
    }

    public function render()
    {
        $add_user_users = null;

        if ($this->add_user_query_term) {
            $add_user_users = User::visibleTo(auth()->user())
                ->where(function ($query2) {
                    $query2->where('last_name', 'like', '%' . $this->add_user_query_term . '%')
                        ->orWhere('first_name', 'like', '%' . $this->add_user_query_term . '%')
                        ->orWhere('preferred_first_name', 'like', '%' . $this->add_user_query_term . '%');
                })
                ->membersOnly()
                ->IsNotDeceased()
                ->limit(10)
                ->get();
        }

        $users = $this->getUsers($this->role->users());

        $roles = Group::visibleTo(auth()->user())->get();

        $filter_count = 0;
        if (count($this->columns) > 0) {
            $filter_count++;
        }
        if (count($this->family_roles) > 0) {
            $filter_count++;
        }

        return view('livewire.admin.roles.view-users')
            ->with('roles', $roles)
            ->with('filter_count', $filter_count)
            ->with('users', $users->paginate(15))
            ->with('add_user_users', $add_user_users);
    }

    public function toggleAddUser()
    {
        $this->add_users = !$this->add_users;
    }

    public function addUser($user_id)
    {
        $user = User::visibleTo(auth()->user())->find($user_id);

        if (!$user) {
            return;
        }

        DB::transaction(function () use ($user) {
            $update_service = (new UpdateUser($user));

            if (auth()->user()->can('manageRoles', User::class)) {
                $update_service->addRolesById([$this->role->id]);
            }

            $update_service->update();
        });

        $this->user_added = $user->name;

        $this->dispatch('refreshView');
    }

    public function removeUser($user_id)
    {
        $user = User::visibleTo(auth()->user())->find($user_id);

        if (!$user) {
            return;
        }

        DB::transaction(function () use ($user) {
            $update_service = (new UpdateUser($user));

            if (auth()->user()->can('manageRoles', User::class)) {
                $update_service->removeRolesById([$this->role->id]);
            }

            $update_service->update();
        });

        $this->dispatch('refreshView');
    }

    protected function getUsers($user_query)
    {
        return $user_query
            ->when($this->query_term, function ($mainQuery) {
                return $mainQuery->BroadSearchByString($this->query_term);
            })
            ->when($this->columns, function ($mainQuery) {
                $mainQuery->when(in_array('is_baptized', $this->columns), function ($query) {
                    $query->whereNotNull('is_baptized');
                });
                $mainQuery->when(in_array('is_not_baptized', $this->columns), function ($query) {
                    $query->whereNull('is_baptized');
                });
                $mainQuery->when(in_array('date_background_check', $this->columns), function ($query) {
                    $query->whereNotNull('date_background_check');
                });
                $mainQuery->when(in_array('can_teach', $this->columns), function ($query) {
                    $query->whereNotNull('can_teach');
                });
                $mainQuery->when(in_array('can_lead', $this->columns), function ($query) {
                    $query->whereNotNull('can_lead');
                });
                $mainQuery->when(in_array('head_of_household_only', $this->columns), function ($query) {
                    $query->headsOfFamily();
                });
                $mainQuery->when(in_array('children_only', $this->columns), function ($query) {
                    $query->childrenOnly();
                });
                $mainQuery->when(in_array('adults_only', $this->columns), function ($query) {
                    $query->adultsOnly();
                });
                $mainQuery->when(in_array('men_only', $this->columns), function ($query) {
                    $query->menOnly();
                });
                $mainQuery->when(in_array('women_only', $this->columns), function ($query) {
                    $query->womenOnly();
                });
            })
            ->when($this->family_roles, function ($mainQuery) {
                $mainQuery->where(function ($subQuery) {
                    foreach (User::$family_roles as $key => $value) {
                        if (in_array($key, $this->family_roles)) {
                            $subQuery->orWhere('users.family_role', $key);
                        }
                    }
                });
            })
            ->withFamilyLastName()
            ->with(['emails'])
            ->orderBy('last_name', 'asc')
            ->orderBy('first_name', 'asc')
            ->orderBy('preferred_first_name', 'asc');
    }
}
