<?php

namespace App\Livewire\Admin\Finance\Contributions;

use App\Accounts\FinanceBucket;
use App\Finance\Services\CreateTransaction;
use App\Finance\Transaction;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Component;

class Create extends Component
{
    public $check_account_number = '';
    public $check_routing_number = '';
    public $check_number         = '';

    public $user_search_query   = '';
    public $user_search_results = [];
    public $selected_user_id    = null;
    public $selected_user       = null;
    public $amount_input        = null;
    public $posted_at           = null;
    public $notes               = null;
    public $title               = null;

    public $contribution_type  = 'check';
    public $buckets            = [];
    public $selected_bucket_id = null;

    public $last_contribution = null;

    protected $listeners = [
        'refreshCreateContributionView' => '$refresh',
        'checkScanned'                  => 'checkScanned',
        'resetSearch'                   => 'resetSearch',
        'createContribution'            => 'createContribution',
    ];

    public function render()
    {
        if (!$this->posted_at) {
            $this->posted_at = now()->format('Y-m-d');
        }

        $string_ends_with_number = Str::endsWith($this->user_search_query, ['1', '2', '3', '4', '5', '6', '7', '8']);

        // If we're searching for a user and the user has typed a number, select that user from the list.
        if ($string_ends_with_number && $this->user_search_query) {
            $number = Str::substr($this->user_search_query, -1, 1) - 1; // Because we added one to a zero index array for user input.

            if (Arr::get($this->user_search_results, $number)) {
                $this->selectUser($this->user_search_results[$number]?->id, false);
                $this->resetSearch();
            }
        } elseif ($this->user_search_query) { // Otherwise, search for users.
            $search_array = explode(' ', $this->user_search_query);
            $first_name   = Arr::get($search_array, 0);
            $last_name    = Arr::get($search_array, 1) ?: null;

            $this->user_search_results = User::visibleTo(auth()->user())
                ->where(function ($query) use ($first_name, $last_name) {
                    $query->when($last_name, function ($query) use ($first_name, $last_name) {
                        $query->where('first_name', 'like', $first_name . '%')
                            ->where('last_name', 'like', $last_name . '%');
                    }, function ($query) use ($first_name) {
                        $query->where('first_name', 'like', $first_name . '%')
                            ->orWhere('last_name', 'like', $first_name . '%');
                    });
                })
                ->IsNotDeceased()
                ->limit(8)
                ->get();
        }

        $this->buckets = FinanceBucket::visibleTo(auth()->user())
            ->isIncome()
            ->get();

        if (!$this->selected_bucket_id) {
            $this->selected_bucket_id = $this->buckets->first()?->id;
        }

        $recent_contributions = Transaction::visibleTo(auth()->user())
            ->LastXDays(days: 3, timezone: auth()->user()->account->timezone)
            ->isContribution()
            ->orderBy('created_at', 'desc')
            ->limit(30)
            ->get();

        return view('livewire.admin.finance.contributions.create')
            ->with('recent_contributions', $recent_contributions);
    }

    public function createContribution()
    {
        $source = null;

        if ($this->contribution_type == 'check' && $this->selected_user) {
            $source = 'check';
        } elseif ($this->contribution_type == 'check' && !$this->selected_user) {
            $source = 'check';
        } elseif ($this->contribution_type == 'cash') {
            $source = 'cash';
        } elseif ($this->contribution_type == 'other') {
            $source = 'other';
        }

        DB::transaction(function () use ($source) {
            (new CreateTransaction())
                ->setCheckInformation(
                    check_number        : $this->check_number,
                    check_account_number: $this->check_account_number,
                    check_routing_number: $this->check_routing_number,
                )
                ->setTitle('Contribution')
                ->isContribution()
                ->setSource($source)
                ->setNotes($this->notes)
                ->setPostedAt(Carbon::createFromFormat('Y-m-d', $this->posted_at)->setTimezone(auth()->user()->account->timezone))
                ->createdBy(auth()->user())
                ->forAccount(auth()->user()->account)
                ->forUser($this->selected_user)
                ->setAmount(Transaction::dollarsToCents($this->amount_input ?: '0.00'))
                ->setFinanceBucket(FinanceBucket::visibleTo(auth()->user())->find($this->selected_bucket_id))
                ->create();
        });

        $this->resetAllFields();
    }

    public function checkScanned($check_scanner_input = null)
    {
        // On the first scan of the first page load, the Javascript will sometimes start our string with "undefined".
        if (substr($check_scanner_input, 0, 1) !== 'T') {
            $this->check_account_number = '';
            $this->check_routing_number = '';
            $this->check_number         = '';

            return;
        }

        // Example:  T313085288T39203972389U 0231
        $split                      = explode(' ', $check_scanner_input);
        $check_number               = Arr::get($split, 1);
        $account_and_routing_number = Arr::get($split, 0);
        $split2                     = explode('U', $account_and_routing_number); // Remove the U from the string
        $split3                     = explode('T', Arr::get($split2, 0));
        $routing_number             = Arr::get($split3, 1);
        $account_number             = Arr::get($split3, 2);

        $this->check_account_number = $account_number;
        $this->check_routing_number = $routing_number;
        $this->check_number         = $check_number;

        // Find our last user.
        $txn = Transaction::visibleTo(auth()->user())
            ->where('check_account_number', Transaction::hashEncode($account_number))
            ->orderBy('created_at', 'desc')
            ->first();

        if ($txn && $txn->user_id) {
            $this->selectUser($txn->user_id, false);
        } else {
            $this->user_search_query = null;
        }

        $this->dispatch('refreshCreateContributionView');
    }

    public function getLastContribution($user)
    {
        $this->last_contribution = $user->transactions()->isContribution()->orderBy('created_at', 'desc')->first();
        if ($this->last_contribution) {
            $this->amount_input = strval($this->last_contribution->formatAmount());
        }
    }

    public function resetSearch()
    {
        $this->user_search_query   = null;
        $this->user_search_results = [];
    }

    public function clickAwayFromSearch()
    {
        $this->user_search_query   = null;
        $this->user_search_results = [];
    }

    public function resetSelectedUser()
    {
        $this->selected_user     = null;
        $this->last_contribution = null;
    }

    public function clearScannedCheck()
    {
        $this->check_account_number = null;
        $this->check_routing_number = null;
        $this->check_number         = null;
    }

    public function resetAllFields()
    {
        $this->clearScannedCheck();
        $this->resetSearch();
        $this->resetSelectedUser();
        $this->notes = null;
        $this->title = null;

        $this->amount_input = null;
    }

    public function selectUser($user_id, $clear_scanned_check = true)
    {
        $this->selected_user_id = $user_id;
        $this->selected_user    = User::visibleTo(auth()->user())
            ->find($this->selected_user_id);

        $this->getLastContribution($this->selected_user);

        if ($clear_scanned_check) {
            $this->clearScannedCheck();
        }
    }

}
