<?php

namespace App\Livewire\Admin\Finance\Expenses;

use App\Accounts\FinanceBucket;
use App\Finance\Exports\ExpenseIndexExport;
use App\Finance\Transaction;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class ExpenseIndex extends Component
{
    use WithPagination;

    public $page                         = 1; // Important for pagination!
    public $query_term                   = null;
    public $filter_min_amount            = null;
    public $filter_max_amount            = null;
    public $filter_show_txn_with_no_user = null;
    public $filter_cash_only             = null;
    public $filter_start_at              = null;
    public $filter_end_at                = null;
    public $filter_buckets               = [];

    protected $queryString = [
        'page'                         => ['except' => 1, 'as' => 'p'],
        'query_term'                   => ['except' => '', 'as' => 'q'],
        'filter_min_amount'            => ['except' => '', 'as' => 'min_amt'],
        'filter_max_amount'            => ['except' => '', 'as' => 'max_amt'],
        'filter_show_txn_with_no_user' => ['except' => '', 'as' => 'no_user'],
        'filter_cash_only'             => ['except' => '', 'as' => 'cash_only'],
        'filter_start_at'              => ['except' => '', 'as' => 'start_at'],
        'filter_end_at'                => ['except' => '', 'as' => 'end_at'],
        'filter_buckets'               => ['except' => '', 'as' => 'buckets'],
    ];

    protected $listeners = [
        'refreshView'        => '$refresh',
        'initExcelDownload'  => 'initExcelDownload',
        'toggleFilter'       => 'toggleFilter',
        'queryChanged'       => 'queryChanged',
        'addBucketFilter'    => 'addBucketFilter',
        'removeBucketFilter' => 'removeBucketFilter',
    ];

    public function updated($field)
    {
        if (str_starts_with($field, 'filter_') || $field == 'query_term') {
            $this->resetPage();
        }
    }

    public function render()
    {
        $this->dispatch('contentChanged');

        $filter_count = 0;

        $this->query_term ? $filter_count++ : null;
        $this->filter_show_txn_with_no_user ? $filter_count++ : null;
        $this->filter_min_amount ? $filter_count++ : null;
        $this->filter_max_amount ? $filter_count++ : null;
        $this->filter_cash_only ? $filter_count++ : null;
        $this->filter_start_at ? $filter_count++ : null;
        $this->filter_end_at ? $filter_count++ : null;
        $this->filter_buckets ? $filter_count++ : null;

        $expenses = $this->getContributions();

        $buckets = FinanceBucket::visibleTo(auth()->user())
            ->isExpense()
            ->get();

        $filter_buckets_js = [];
        foreach ($buckets as $bucket) {
            $filter_buckets_js[] = [
                'value'    => $bucket->id,
                'label'    => $bucket->name,
                'selected' => in_array($bucket->id, $this->filter_buckets) ? 'true' : 'false',
            ];
        }

        return view('livewire.admin.finance.expenses.expense-index')
            ->with('expenses', $expenses->paginate(20))
            ->with('buckets', $buckets)
            ->with('filter_buckets_js', $filter_buckets_js)
            ->with('filter_count', $filter_count);
    }

    public function initExcelDownload()
    {
        $expenses = $this->getContributions();

        // Export as Excel file
        return Excel::download(new ExpenseIndexExport($expenses), 'expense-list-export.xlsx');
    }

    public function addBucketFilter($bucket_id)
    {
        $this->filter_buckets = array_unique(array_merge($this->filter_buckets, [$bucket_id]));

        $this->dispatch('refreshView');
    }

    public function removeBucketFilter($bucket_id)
    {
        if (($key = array_search($bucket_id, $this->filter_buckets)) !== false) {
            unset($this->filter_buckets[$key]);
        }

        $this->dispatch('refreshView');
    }

    protected function getContributions()
    {
        return Transaction::visibleTo(auth()->user())
            ->when($this->query_term, function ($query) {
                $query->whereHas('user', function ($query) {
                    $query->SearchNameByString($this->query_term);
                });
            })
            ->when($this->filter_show_txn_with_no_user, function ($query) {
                $query->whereNull('user_id');
            })
            ->when($this->filter_cash_only, function ($query) {
                $query->where('user_id');
            })
            ->when($this->filter_min_amount, function ($query) {
                $query->where('amount', '>=', $this->filter_min_amount * 100);
            })
            ->when($this->filter_max_amount, function ($query) {
                $query->where('amount', '<=', $this->filter_max_amount * 100);
            })
            ->when($this->filter_start_at, function ($query) {
                $query->where('created_at', '>=', Carbon::createFromFormat('Y-m-d', $this->filter_start_at)->setTimezone(auth()->user()->account->timezone)->setTime(0, 0));
            })
            ->when($this->filter_end_at, function ($query) {
                $query->where('created_at', '<=', Carbon::createFromFormat('Y-m-d', $this->filter_end_at)->setTimezone(auth()->user()->account->timezone)->setTime(23, 59, 59));
            })
            ->when($this->filter_buckets, function ($mainQuery) {
                $mainQuery->whereHas('bucket', function ($query) {
                    $query->whereIn('account_finance_buckets.id', $this->filter_buckets ?: []);
                });
            })
            ->isExpense()
            ->with(['account'])
            ->orderBy('posted_at', 'desc');
    }
}
