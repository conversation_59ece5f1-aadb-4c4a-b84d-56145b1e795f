<?php

namespace App\Livewire\Admin\Finance\Expenses;

use App\Accounts\FinanceBucket;
use App\Finance\Services\CreateTransaction;
use App\Finance\Transaction;
use App\Users\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Component;

class Create extends Component
{
    public $check_account_number = '';
    public $check_routing_number = '';
    public $check_number         = '';

    public $user_search_query   = '';
    public $user_search_results = [];
    public $selected_user_id    = null;
    public $selected_user       = null;
    public $amount_input        = null;
    public $posted_at           = null;
    public $notes               = null;
    public $title               = null;

    public $type               = 'check';
    public $buckets            = [];
    public $selected_bucket_id = null;

    protected $listeners = [
        'refreshCreateExpenseView' => '$refresh',
        'resetSearch'              => 'resetSearch',
        'createExpense'            => 'createExpense',
    ];

    public function render()
    {
        if (!$this->posted_at) {
            $this->posted_at = now()->format('Y-m-d');
        }

        $string_ends_with_number = Str::endsWith($this->user_search_query, [1, 2, 3, 4, 5, 6, 7, 8]);

        if ($string_ends_with_number && $this->user_search_query) {
            $number = Str::substr($this->user_search_query, -1, 1) - 1; // Because we added one to a zero index array for user input.

            if (Arr::get($this->user_search_results, $number)) {
                $this->selectUser($this->user_search_results[$number]?->id);
                $this->resetSearch();
            }
        } elseif ($this->user_search_query) {
            $search_array = explode(' ', $this->user_search_query);
            $first_name   = Arr::get($search_array, 0);
            $last_name    = Arr::get($search_array, 1) ?: null;

            $this->user_search_results = User::visibleTo(auth()->user())
                ->where(function ($query) use ($first_name, $last_name) {
                    $query->when($last_name, function ($query) use ($first_name, $last_name) {
                        $query->where('first_name', 'like', $first_name . '%')
                            ->where('last_name', 'like', $last_name . '%');
                    }, function ($query) use ($first_name) {
                        $query->where('first_name', 'like', $first_name . '%')
                            ->orWhere('last_name', 'like', $first_name . '%');
                    });
                })
                ->IsNotDeceased()
                ->limit(8)
                ->get();
        }

        $this->buckets = FinanceBucket::visibleTo(auth()->user())
            ->isExpense()
            ->get();

        if (!$this->selected_bucket_id) {
            $this->selected_bucket_id = $this->buckets->first()?->id;
        }

        $recent_expenses = Transaction::visibleTo(auth()->user())
            ->LastXDays(days: 3, timezone: auth()->user()->account->timezone)
            ->isExpense()
            ->orderBy('created_at', 'desc')
            ->limit(30)
            ->get();

        return view('livewire.admin.finance.expenses.create')
            ->with('recent_expenses', $recent_expenses);
    }

    public function createExpense()
    {
        DB::transaction(function () {
            (new CreateTransaction())
                ->setTitle($this->title ?: 'Expense')
                ->isExpense()
                ->setSource($this->type)
                ->setNotes($this->notes)
                ->setPostedAt(Carbon::createFromFormat('Y-m-d', $this->posted_at)->setTimezone(auth()->user()->account->timezone)->setTimezone('UTC'))
                ->createdBy(auth()->user())
                ->forAccount(auth()->user()->account)
                ->forUser($this->selected_user)
                ->setAmount(Transaction::dollarsToCents($this->amount_input ?: '0.00'))
                ->setFinanceBucket(FinanceBucket::visibleTo(auth()->user())->find($this->selected_bucket_id))
                ->setCheckInformation(
                    check_number        : $this->check_number,
                    check_account_number: $this->check_account_number,
                    check_routing_number: $this->check_routing_number,
                )
                ->create();
        });

        $this->resetAllFields();
    }

    public function resetSearch()
    {
        $this->user_search_query   = null;
        $this->user_search_results = [];
    }

    public function clickAwayFromSearch()
    {
        $this->user_search_query   = null;
        $this->user_search_results = [];
    }

    public function resetSelectedUser()
    {
        $this->selected_user     = null;
        $this->last_contribution = null;
    }

    public function resetAllFields()
    {
        $this->resetSearch();
        $this->resetSelectedUser();
        $this->notes = null;
        $this->title = null;

        $this->amount_input = null;
    }

    public function selectUser($user_id, $clear_scanned_check = true)
    {
        $this->selected_user_id = $user_id;
        $this->selected_user    = User::visibleTo(auth()->user())->find($this->selected_user_id);
    }

}
