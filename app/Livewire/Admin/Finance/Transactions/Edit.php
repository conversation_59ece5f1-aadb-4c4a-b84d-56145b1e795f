<?php

namespace App\Livewire\Admin\Finance\Transactions;

use App\Accounts\FinanceBucket;
use App\Finance\Services\EditTransaction;
use App\Finance\Transaction;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Edit extends Component
{
    public Transaction $transaction;

    protected $rules = [
        'transaction.notes'        => 'sometimes|string',
        'transaction.title'        => 'sometimes|string',
        'transaction.check_number' => 'sometimes|string',
    ];

    public $amount_input = null;
    public $posted_at    = null;
    public $notes        = null;

    public $buckets            = [];
    public $selected_bucket_id = null;

    public function render()
    {
        if (!$this->posted_at && $this->transaction->posted_at) {
            $this->posted_at = $this->transaction->posted_at->format('Y-m-d');
        }

        if (!$this->amount_input) {
            $this->amount_input = number_format($this->transaction->formatAmount()->toFloat(), 2);
        }

        if (!$this->selected_bucket_id) {
            $this->selected_bucket_id = $this->transaction->account_finance_bucket_id;
        }

        if ($this->transaction->is_income) {
            $this->buckets = FinanceBucket::visibleTo(auth()->user())
                ->isIncome()
                ->get();
        } else {
            $this->buckets = FinanceBucket::visibleTo(auth()->user())
                ->isExpense()
                ->get();
        }

        if (!$this->selected_bucket_id) {
            $this->selected_bucket_id = $this->buckets->first()?->id;
        }

        return view('livewire.admin.finance.transactions.edit');
    }

    public function getPostedProperty()
    {
        return $this->transaction->posted_at->format('Y-m-d');
    }

    public function setSelectedBucket()
    {
        $this->selected_bucket = FinanceBucket::visibleTo(auth()->user())->find($this->selected_bucket_id);
    }

    public function saveTransaction()
    {
        $selected_bucket = FinanceBucket::visibleTo(auth()->user())->find($this->selected_bucket_id);

        DB::transaction(function () use ($selected_bucket) {
            // We pull the transaction fresh from the DB, because our $this->transaction is being modified by the user.
            (new EditTransaction(Transaction::visibleTo(auth()->user())->find($this->transaction->id)))
                ->setTitle($this->transaction->title)
                ->setNotes($this->transaction->notes)
                ->setAmount(amount_in_dollars: $this->amount_input)
                ->setCheckNumber($this->transaction->check_number)
                ->setFinanceBucket($selected_bucket)
                ->setPostedAt(Carbon::createFromFormat('Y-m-d', $this->posted_at)->setTimezone(auth()->user()->account->timezone))
                ->save();
        });

        if ($this->transaction->is_expense) {
            return redirect()->to(route('admin.finance.expenses.index'))
                ->with('message.success', 'Changes saved.');
        } else {
            return redirect()->to(route('admin.finance.contributions.index'))
                ->with('message.success', 'Changes saved.');
        }
    }

    public function deleteTransaction()
    {
        DB::transaction(function () {
            $this->transaction->delete();
        });

        return redirect()->to(route('admin.finance.contributions.index'))
            ->with('message.success', 'Contribution deleted.');
    }
}
