<?php

namespace App\Livewire\Admin\Finance\Buckets;

use App\Accounts\FinanceBucket;
use App\Finance\Exports\BucketIndexExport;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class Index extends Component
{
    use WithPagination;

    public $query_term              = null;
    public $filter_min_amount       = null;
    public $filter_max_amount       = null;
    public $filter_year             = null;
    public $filter_is_expense       = null;
    public $filter_is_income        = null;
    public $filter_is_contribution  = null;
    public $filter_is_reimbursement = null;

    protected $queryString = [
        'page'                    => ['except' => 1, 'as' => 'p'],
        'query_term'              => ['except' => '', 'as' => 'q'],
        'filter_min_amount'       => ['except' => '', 'as' => 'min_amt'],
        'filter_max_amount'       => ['except' => '', 'as' => 'max_amt'],
        'filter_is_contribution'  => ['except' => ''],
        'filter_is_reimbursement' => ['except' => ''],
    ];

    protected $listeners = [
        'refreshView'       => '$refresh',
        'initExcelDownload' => 'initExcelDownload',
        'toggleFilter'      => 'toggleFilter',
        'queryChanged'      => 'queryChanged',
    ];

    public function queryChanged()
    {
        $this->resetPage();
    }

    public function render()
    {
        $this->dispatch('contentChanged');

        $filter_count = 0;

        $this->query_term ? $filter_count++ : null;
        $this->filter_min_amount ? $filter_count++ : null;
        $this->filter_max_amount ? $filter_count++ : null;
        $this->filter_is_contribution ? $filter_count++ : null;
        $this->filter_is_reimbursement ? $filter_count++ : null;

        $buckets = $this->getBuckets();

        return view('livewire.admin.finance.buckets.index')
            ->with('buckets', $buckets->paginate(20))
            ->with('filter_count', $filter_count);
    }

    public function initExcelDownload()
    {
        $buckets = $this->getBuckets();

        // Export as Excel file
        return Excel::download(new BucketIndexExport($buckets), 'buckets-list-export.xlsx');
    }

    protected function getBuckets()
    {
        return FinanceBucket::visibleTo(auth()->user())
            ->when($this->query_term, function ($query) {
                $query->where('name', 'like', '%' . $this->query_term . '%');
            })
            ->when($this->filter_year, function ($query) {
                $query->whereHas('budgets', function ($query) {
                    $query->where('year', $this->filter_year);
                });
            })
            ->when($this->filter_min_amount, function ($query) {
                $query->whereHas('budgets', function ($query) {
                    $query->where('yearly_budget', '>=', $this->filter_min_amount * 100); // *100 to get cents.
                });
            })
            ->when($this->filter_max_amount, function ($query) {
                $query->whereHas('budgets', function ($query) {
                    $query->where('yearly_budget', '<=', $this->filter_max_amount * 100);
                });
            })
            ->orderBy('sort_id', 'asc');
    }
}
