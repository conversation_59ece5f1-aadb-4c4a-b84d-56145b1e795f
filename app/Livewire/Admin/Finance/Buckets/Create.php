<?php

namespace App\Livewire\Admin\Finance\Buckets;

use App\Finance\Services\CreateBucket;
use App\Finance\Services\CreateBudget;
use App\Finance\Transaction;
use App\Users\User;
use Illuminate\Support\Arr;
use Livewire\Component;

class Create extends Component
{
    public $name                = null;
    public $description         = null;
    public $is_income           = null;
    public $is_expense          = null;
    public $is_contribution     = null;
    public $is_reimbursement    = null;
    public $account_code        = null;
    public $current_year_budget = null;

    protected $listeners = [
        'createBucket' => 'createBucket',
    ];

    public function render()
    {
        if ($this->is_income) {
            $this->is_expense = null;
        }
        if ($this->is_expense) {
            $this->is_income = null;
        }

        return view('livewire.admin.finance.buckets.create');
    }

    public function checkProperty($property)
    {
        if ($property == 'is_income' && $this->is_income) { // Represents new current state, so if new state is "unchecked", we'll go to the else statement.
            $this->is_income  = true;
            $this->is_expense = false;
        } else {
            $this->is_income = false;
        }
        if ($property == 'is_expense' && $this->is_expense) {
            $this->is_income        = false;
            $this->is_expense       = true;
            $this->is_contribution  = false;
            $this->is_reimbursement = false;
        } else {
            $this->is_expense = false;
        }
        if ($property == 'is_contribution' && $this->is_contribution) {
            $this->is_income        = true;
            $this->is_expense       = false;
            $this->is_reimbursement = false;
        } else {
            $this->is_contribution = false;
        }
        if ($property == 'is_reimbursement' && $this->is_reimbursement) {
            $this->is_income       = false;
            $this->is_expense      = true;
            $this->is_contribution = false;
        } else {
            $this->is_reimbursement = false;
        }
    }

    public function createBucket()
    {
        $new_bucket = (new CreateBucket())
            ->forAccount(auth()->user()->account)
            ->setName($this->name)
            ->setAccountCode($this->account_code)
            ->setIsContribution((bool)$this->is_contribution)
            ->setIsReimbursement((bool)$this->is_reimbursement)
            ->setIsExpense((bool)$this->is_expense)
            ->setIsIncome((bool)$this->is_income)
            ->setDescription($this->description)
            ->create();

        // Create our budget if we set one.
        if ($this->current_year_budget) {
            (new CreateBudget())
                ->forBucket($new_bucket)
                ->setYearlyBudget($this->current_year_budget * 100) // In Cents
                ->forYear(now()->format('Y'))
                ->setName('Budget for ' . now()->format('Y'))
                ->create();
        }

        $this->resetAllFields();

        return redirect()->to(route('admin.finance.buckets.index'));
    }

    public function checkScanned($check_scanner_input)
    {
        // On the first scan of the first page load, the Javascript will sometimes start our string with "undefined".

        if (substr($check_scanner_input, 0, 1) !== 'T') {
            $this->check_account_number = '';
            $this->check_routing_number = '';
            $this->check_number         = '';

            return;
        }

        // Example:  T313085288T39203972389U 0231
        $split                      = explode(' ', $check_scanner_input);
        $check_number               = Arr::get($split, 1);
        $account_and_routing_number = Arr::get($split, 0);
        $split2                     = explode('U', $account_and_routing_number); // Remove the U from the string
        $split3                     = explode('T', Arr::get($split2, 0));
        $routing_number             = Arr::get($split3, 1);
        $account_number             = Arr::get($split3, 2);

        $this->check_account_number = $account_number;
        $this->check_routing_number = $routing_number;
        $this->check_number         = $check_number;

        // Find our last user.
        $txn = Transaction::visibleTo(auth()->user())
            ->where('check_account_number', Transaction::hashEncode($account_number))
            ->orderBy('created_at', 'desc')
            ->first();

        if ($txn && $txn->user_id) {
            $this->selectUser($txn->user_id, false);
        }

        $this->dispatch('refreshCreateContributionView');
    }

    public function getLastContribution($user)
    {
        $this->last_contribution = $user->transactions()->isContribution()->orderBy('created_at', 'desc')->first();
        if ($this->last_contribution) {
            $this->amount_input = strval($this->last_contribution->formatAmount());
        }
    }

    public function resetSearch()
    {
        $this->user_search_query   = null;
        $this->user_search_results = [];
    }

    public function resetSelectedUser()
    {
        $this->selected_user     = null;
        $this->last_contribution = null;
    }

    public function clearScannedCheck()
    {
        $this->check_account_number = '';
        $this->check_routing_number = '';
        $this->check_number         = '';
    }

    public function resetAllFields()
    {
        $this->clearScannedCheck();
        $this->resetSearch();
        $this->resetSelectedUser();

        $this->amount_input = null;
    }

    public function selectUser($user_id, $clear_scanned_check = true)
    {
        $this->selected_user_id = $user_id;
        $this->selected_user    = User::visibleTo(auth()->user())->find($this->selected_user_id);

        $this->getLastContribution($this->selected_user);

        if ($clear_scanned_check) {
            $this->clearScannedCheck();
        }
    }

}
