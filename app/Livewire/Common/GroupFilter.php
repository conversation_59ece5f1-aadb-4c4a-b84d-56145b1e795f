<?php

namespace App\Livewire\Common;

use Livewire\Component;

class GroupFilter extends Component
{
    public $filter_groups         = [];
    public $filter_exclude_groups = [];

    protected $listeners = [
        'refreshGroupFilterView'   => '$refresh',
        'addGroupFilter'           => 'addGroupFilter',
        'removeGroupFilter'        => 'removeGroupFilter',
        'addGroupExcludeFilter'    => 'addGroupExcludeFilter',
        'removeGroupExcludeFilter' => 'removeGroupExcludeFilter',
    ];

    public function addGroupFilter($group_id)
    {
        $this->filter_groups = array_unique(array_merge($this->filter_groups, [$group_id]));

        $this->dispatch('refreshGroupFilterView');
    }

    public function removeGroupFilter($group_id)
    {
        if (($key = array_search($group_id, $this->filter_groups)) !== false) {
            unset($this->filter_groups[$key]);
        }
    }

    public function addGroupExcludeFilter($group_id)
    {
        $this->filter_exclude_groups = array_unique(array_merge($this->filter_exclude_groups, [$group_id]));

        $this->dispatch('refreshGroupFilterView');
    }

    public function removeGroupExcludeFilter($group_id)
    {
        if (($key = array_search($group_id, $this->filter_exclude_groups)) !== false) {
            unset($this->filter_exclude_groups[$key]);
        }
    }
}
