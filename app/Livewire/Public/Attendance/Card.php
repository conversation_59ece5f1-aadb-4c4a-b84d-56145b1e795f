<?php

namespace App\Livewire\Public\Attendance;

use App\Attendance\Services\CreateVisitorAttendanceCard;
use Carbon\Carbon;
use Livewire\Component;

class Card extends Component
{
    public $account;
    public $form_fields    = [
        'visited_at' => null,
        'name'       => '',
        //        'first_name'          => '',
        //        'last_name'           => '',
        //        'spouse_name'         => '',
        //        'family_member_names' => '',
        'address1'   => null,
        'address2'   => null,
        'city'       => null,
        'state'      => null,
        'zip'        => null,
        'country'    => null,
        'phone'      => null,
        'email'      => null,
        'guest_of'   => null,
        'comments'   => null,
        'is_member'  => false,
        'is_visitor' => true,
    ];
    public $submit_success = false;
    public $has_error      = false;

    public function render()
    {
        if (auth()->user()) {
            $this->form_fields['visited_at'] = Carbon::now()->copy()->{in_array(Carbon::now()->dayOfWeek, [Carbon::SUNDAY, Carbon::MONDAY, Carbon::TUESDAY]) ? 'previous' : 'previous'}(Carbon::SUNDAY)->format('Y-m-d');
        }

        return view('livewire.public.attendance.card');
    }

    public function submit()
    {
        $this->has_error = false;

        try {
            $card = (new CreateVisitorAttendanceCard())->create(
                array_merge(
                    ['account_id' => $this->account->id],
                    $this->form_fields
                )
            );

            $this->submit_success = true;
        } catch (\Exception $e) {
            $this->has_error = $e->getMessage();
        }
    }
}
