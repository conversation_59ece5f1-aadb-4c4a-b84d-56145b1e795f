<?php

namespace App\Admin\Services;

use App\Sermons\Sermon;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Dashboard
{
    public function getBirthdaysToday()
    {
        return User::visibleTo(Auth::user())
            ->includeInReports()
            ->membersOnly()
            ->whereMonth('birthdate', Carbon::now()->setTimezone('America/Chicago')->format('m'))
            ->whereDay('birthdate', Carbon::now()->setTimezone('America/Chicago')->format('d'))
            ->whereNotNull('birthdate')
            ->get();
    }

    public function getUpcomingBirthdays()
    {
        $query = User::visibleTo(Auth::user())
            ->select(['id', 'account_id', 'family_id', 'preferred_first_name', 'first_name', 'last_name', 'birthdate'])
            ->includeInReports()
            ->membersOnly()
            ->whereNotNull('birthdate')
            ->where(function ($query2) {
                for ($i = 1; $i < 14; $i++) {
                    $query2->orWhere(function ($query3) use ($i) {
                        $query3->whereMonth('birthdate', Carbon::now()->setTimezone('America/Chicago')->add($i . ' days')->format('m'))
                            ->whereDay('birthdate', Carbon::now()->setTimezone('America/Chicago')->add($i . ' days')->format('d'));
                    });
                }
            })
            ->orderBy(DB::raw('MONTH(birthdate), DAYOFMONTH(birthdate)'));

        return $query->get();
    }

    public function getRecentActiveUsers()
    {
        // Returns an object with:  { id (user_id), account_id, created_at }
        return User::visibleTo(auth()->user())
            ->select(['users.id', 'users.account_id', DB::raw('MAX(user_activity.created_at) created_at')])
            ->join('user_activity', function ($query) {
                $query->on('users.id', '=', 'user_activity.user_id');
            })
            ->groupBy('users.id')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

//        return Activity::visibleTo(auth()->user())
//            ->orderBy('created_at2', 'desc')
//            ->select(['account_id', 'user_id', DB::raw('DATE_FORMAT(created_at, \'%Y-%m-%d %H\') as created_at2')])
////            ->groupBy(['account_id', 'user_id', 'created_at'])
//            ->distinct()
//            ->limit(10)
//            ->get();

//        return User::visibleTo(Auth::user())
//            ->membersOnly()
//            ->includeInReports()
//            ->whereNotNull('last_active')
//            ->orderBy('last_active', 'desc')
//            ->limit(10)
//            ->get();
    }

    public function getLatestSermons()
    {
        return Sermon::visibleTo(Auth::user())
            ->with('files')
            ->orderBy('date_sermon', 'desc')
            ->limit(10)
            ->get();
    }
}
