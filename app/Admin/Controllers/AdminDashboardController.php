<?php

namespace App\Admin\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Users\Activity;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    public function index()
    {
        $baseline  = array_fill(0, 14, 0);
        $iter_date = now()->subWeeks(2);

        foreach ($baseline as $line) {
            $label_fill[] = $iter_date->format('M d');
            $iter_date->addDay();
        }

        return view('admin.dashboard.index')
            ->with('labels', $label_fill)
            ->with('spam_activity', collect());
    }

    public function apiGetActivityChartData()
    {
        $activity = Activity::visibleTo(auth()->user())
            ->select([
                DB::raw('COUNT(distinct user_id) as user_count'),
                DB::raw('account_id'),
                DB::raw('DATE_FORMAT(created_at, \'%Y-%m-%d\') as full_date'),
                //                DB::raw('user_id'),
                //                DB::raw('HOUR(created_at) as hour'),
                //                DB::raw('DATE_FORMAT(created_at, \'%Y-%m-%d-%H\') as full_date'),
            ])
            ->where('created_at', '>=', now()->subWeeks(2))
            ->groupBy(DB::raw('2,3'))
            ->get();

        $baseline  = array_fill(0, 14, 0);
        $iter_date = now()->subWeeks(2);

        foreach ($baseline as $line) {
            $label_fill[] = $iter_date->format('M d');
            $data_fill[]  = $activity->contains('full_date', $iter_date->format('Y-m-d')) ? $activity->where('full_date', $iter_date->format('Y-m-d'))?->first()->user_count : 0;
            $iter_date->addDay();
        }

        return response()->json(
            $data_fill
        );
    }
}
