<?php

namespace App\Admin\Controllers;

use App\Base\Http\Controllers\Controller;

class ChangelogController extends Controller
{
    public function index()
    {
        $changelogs = \App\Base\Models\Changelog::query()
            ->isPublic()
            ->isReleased()
            ->isPosted()
            ->when(request()->get('category'), function($query, $category) {
                return $query->whereJsonContains('categories', $category);
            })
            ->when(request()->get('platform'), function($query, $platform) {
                return $query->whereJsonContains('platforms', $platform);
            })
            ->orderBy('released_at', 'desc')
            ->get()
            ->groupBy(function($item) {
                return $item->released_at->format('F Y');
            });

        return view('admin.changelog.index')
            ->with('changelogs', $changelogs);
    }

    public function createSubmit()
    {
        if (!auth()->user()->is_super) {
            abort(404);
        }
        
        $changelog = \App\Base\Models\Changelog::create([
            'title' => request()->get('title'),
            'subtitle' => request()->get('subtitle'),
            'released_at' => request()->get('released_at'),
            'posted_at' => request()->get('posted_at'),
            'categories' => request()->get('categories', []),
            'platforms' => request()->get('platforms', []),
            'details' => request()->get('details'),
            'is_public' => (bool)request()->get('is_public', false),
            'is_tiny' => (bool)request()->get('is_tiny', false),
        ]);

        return redirect(route('admin.changelog.index'))
            ->with('message.success', 'Success!  New entry created.');
    }
}
