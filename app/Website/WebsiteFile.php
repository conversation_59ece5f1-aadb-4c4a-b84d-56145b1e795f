<?php

namespace App\Website;

use App\AccountFiles\Scopes\AccountFileVisibleToScope;
use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class WebsiteFile extends Model
{
    use SoftDeletes;

    protected $table = 'website_files';

    // The Laravel disk name that we will save content into on the object storage service.
    public static $disk_name = 'website-files';
    // The folder that we will save content into on the object storage service. Preferably the ULID of the account.
    public static $account_folder_identifier = 'ulid';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'starts_at'  => 'datetime',
        'expires_at' => 'datetime',
        'is_public'  => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'starts_at',
        'expires_at',
        'title',
        'url_title',
        'type',
        'storage_service',
        'file_original_name',
        'file_size',
        'data_separator',
        'file_folder',
        'file_id',
        'file_name',
        'file_extension',
        'file_type',
        'file_sha1',
        'sort_id',
        'is_public',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeVisibleToAccount($query, Account|int $account_id)
    {
        if ($account_id instanceof Account) {
            $account_id = $account_id->id;
        }

        return $query->where(function ($query) use ($account_id) {
            $query->where($this->table . '.account_id', $account_id);
        });
    }

    public function getVisibleFilesQuery($user)
    {
        return $this->visibleTo($user)
            ->isNotExpired()
            ->orderBy('sort_id');
    }

    public function scopeIsNotExpired($query)
    {
        return $query->where(function ($query2) {
            $query2->whereNull('expires_at')
                ->orWhere('expires_at', '>=', now()->format('Y-m-d'));
        });
    }

    public function scopeIsPublic($query)
    {
        return $query->whereNotNull('is_public');
    }

    public function getFileSize($unit = 'auto', $get_unit_type = false, $decimals = 0)
    {
        $size = $this->file_size;

        return $this->getSizeFromBytes($size, $unit, $get_unit_type, $decimals);
    }

    protected function getSizeFromBytes($size, $unit = 'auto', $get_unit_type = false, $decimals = 0)
    {
        if ($unit == 'auto') {
            if ($size < 1024) {
                $unit = 'B';
            } elseif ($size < 1024 * 1024) {
                $unit = 'KB';
            } elseif ($size < 1024 * 1024 * 1024) {
                $unit = 'MB';
            } else {
                $unit = 'GB';
            }
        }

        if (strtolower($unit) == 'kb') {
            $size = $size / 1024;
        } elseif (strtolower($unit) == 'mb') {
            $size = $size / 1024 / 1024;
        } elseif (strtolower($unit) == 'gb') {
            $size = $size / 1024 / 1024 / 1024;
        }

        if ($get_unit_type) {
            return $unit;
        }

        if ($size < 1) {
            $decimals = 2;
        }

        return round($size, $decimals);
    }

    public function getUrl($size = null)
    {
        return Storage::disk(self::$disk_name)
            ->url($this->file_folder . '/' . $this->file_name . '.' . $this->file_extension);
    }

    public function getTempUrl($minutes = 30)
    {
        return Storage::disk(self::$disk_name)
            ->temporaryUrl(
                $this->file_folder . '/' . $this->file_name . '.' . $this->file_extension,
                now()->addMinutes($minutes)
            );
    }

    public function deleteFile()
    {
        return Storage::disk(self::$disk_name)
            ->delete($this->file_folder . '/' . $this->file_name . '.' . $this->file_extension);
    }
}
