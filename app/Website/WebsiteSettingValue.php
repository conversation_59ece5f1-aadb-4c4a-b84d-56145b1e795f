<?php

namespace App\Website;

use App\Accounts\Account;
use Illuminate\Database\Eloquent\Model;

class WebsiteSettingValue extends Model
{
    protected $table = 'website_setting_values';

    const UPDATED_AT = null;
    const CREATED_AT = null;

    protected $casts = [
        'created_at'        => 'datetime',
        'updated_at'        => 'datetime',
        'enable_for_member' => 'boolean',
        'enable_for_admin'  => 'boolean',
        'value'             => 'string',
    ];

    protected $fillable = [
        'account_id',
        'website_setting_id',
        'value',
        'enable_for_member',
        'enable_for_admin',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class)->whereNull('account_location_id');
    }

    public function setting()
    {
        return $this->belongsTo(WebsiteSetting::class, 'website_setting_id');
    }

    public function scopeForAccount($query, Account $account)
    {
        $query->where('account_id', $account->id);
    }

    public function scopeForKey($query, $key)
    {
        $query->where('setting.key', $key);
    }
}
