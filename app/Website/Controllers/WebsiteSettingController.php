<?php

namespace App\Website\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Website\Services\CreateWebsiteFile;
use App\Website\Services\DeleteWebsiteFile;
use App\Website\Services\GetWebsiteSettingValue;
use App\Website\Services\SaveOrCreateWebsiteSettingValue;
use App\Website\WebsiteSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WebsiteSettingController extends Controller
{
    public function logos()
    {
        $main_logo = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('logo.main.light.website_file_id');

        $footer_logo = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('logo.footer.light.website_file_id');

        return view('admin.website.settings.logos')
            ->with('main_logo', $main_logo)
            ->with('footer_logo', $footer_logo);
    }

    public function saveLogo(WebsiteSetting $setting)
    {
        request()->validate([
            'file' => [
                'required',
                'file',
                'image',
                'mimes:jpeg,png,jpg,svg',
                'max:2048', // 2MB max size
                'dimensions:min_width=100,min_height=100', // minimum dimensions
            ],
        ], [
            'file.required'   => 'Please select a file to upload.',
            'file.image'      => 'The file must be an image.',
            'file.mimes'      => 'The image must be a file of type: jpeg, png, jpg, svg.',
            'file.max'        => 'The image must not be larger than 2MB.',
            'file.dimensions' => 'The image must be at least 100x100 pixels.',
        ]);

        try {
            DB::transaction(function () use ($setting) {
                $file = (new CreateWebsiteFile())
                    ->forAccount(auth()->user()->account)
                    ->withFile(request()->file('file'), request()->get('file_title'))
                    ->create();

                (new SaveOrCreateWebsiteSettingValue())
                    ->forAccount(auth()->user()->account)
                    ->forSetting($setting)
                    ->save($file->id);
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.website.settings.logos'))
            ->with('message.success', 'Saved successfully.');
    }

    public function domain()
    {
        $domains = DB::table('account_domains')
            ->where('account_id', auth()->user()->account_id)
            ->where('is_active', 1)
            ->get();

        return view('admin.website.settings.domain')
            ->with('domains', $domains);
    }

    public function saveDomain()
    {
        request()->validate([
            'domain' => 'required|string|max:255|regex:/^[a-zA-Z0-9.-]+$/',
        ]);

        $domain = request()->get('domain');

        $account         = auth()->user()->account;
        $account->domain = $domain;
        $account->save();

        return redirect(route('admin.website.settings.domain'))
            ->with('message.success', 'Saved successfully.');
    }

    public function carousel()
    {
        return view('admin.website.settings.carousel');
    }

    public function redirects()
    {
        return view('admin.website.settings.redirects');
    }

    public function quickLinks()
    {
        return view('admin.website.settings.sidebar-links');
    }

    public function socialLinks()
    {
        $facebook = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.facebook');

        $youtube = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.youtube');

        $vimeo = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.vimeo');

        $twitter = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('links.twitter');

        return view('admin.website.settings.social-links')
            ->with('facebook', $facebook)
            ->with('youtube', $youtube)
            ->with('vimeo', $vimeo)
            ->with('twitter', $twitter);
    }

    public function content()
    {
        $leadershipEnabled = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('leadership.enabled');

        $leadershipShowPhotos = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('leadership.show_photos');

        $prayerRequestsEnabled = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('prayer_requests.enabled');

        $prayerRequestsEmail = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('prayer_requests.to_email');

        $contactEmail = (new GetWebsiteSettingValue())
            ->forAccount(auth()->user()->account)
            ->get('contact.to_email');

        return view('admin.website.settings.content')
            ->with('leadershipEnabled', $leadershipEnabled)
            ->with('leadershipShowPhotos', $leadershipShowPhotos)
            ->with('prayerRequestsEnabled', $prayerRequestsEnabled)
            ->with('prayerRequestsEmail', $prayerRequestsEmail)
            ->with('contactEmail', $contactEmail);
    }

    public function specialLinks()
    {
        return view('admin.website.settings.special-links');
    }

    public function deleteLogo($settingKey)
    {
        try {
            // Get the current WebsiteFile before we clear the setting. Our service will return null if the setting is empty.
            $file = (new GetWebsiteSettingValue())
                ->forAccount(auth()->user()->account)
                ->get($settingKey);

            // Delete the file if it exists
            if ($file) {
                if ($file) {
                    (new DeleteWebsiteFile($file))->delete();
                }
            }

            // Clear the setting
            (new SaveOrCreateWebsiteSettingValue())
                ->forAccount(auth()->user()->account)
                ->forSetting($settingKey)
                ->save('');
        } catch (\Exception $e) {
            Log::error('Error removing logo: ' . $e->getMessage());
            return redirect()
                ->back()
                ->with('error', 'Failed to remove logo. Please try again.');
        }

        return redirect()
            ->back()
            ->with('success', 'Logo removed successfully');
    }
}
