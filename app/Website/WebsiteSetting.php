<?php

namespace App\Website;

use App\Accounts\Account;
use Illuminate\Database\Eloquent\Model;

class WebsiteSetting extends Model
{
    protected $table = 'website_settings';

    const UPDATED_AT = null;
    const CREATED_AT = null;

    protected $fillable = [
        'key',
        'name',
        'description',
        'type',
        'default_value',
        'values',
    ];

    public static $types = [
        'bool'    => 'Boolean',
        'integer' => 'Integer',
        'string'  => 'String',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function valueForAccount(Account|int $account_id): ?WebsiteSettingValue
    {
        if ($account_id instanceof Account) {
            $account_id = $account_id->id;
        }

        return $this->hasOne(WebsiteSettingValue::class)
            ->where('account_id', $account_id)
            ->first();
    }

    public function isBool()
    {
        return $this->type == 'bool';
    }

    public function isInteger()
    {
        return $this->type == 'integer';
    }

    public function isString()
    {
        return $this->type == 'string';
    }
}
