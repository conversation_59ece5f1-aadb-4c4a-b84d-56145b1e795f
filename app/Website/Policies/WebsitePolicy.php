<?php

namespace App\Website\Policies;

use App\Users\User;

class WebsitePolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.website')) {
            return false;
        }
    }

    public function manage(User $user)
    {
        return $user->hasPermission('website.manage');
    }

    public function managePages(User $user)
    {
        return $user->hasPermission('website.manage.pages');
    }

    public function viewStats(User $user)
    {
        return $user->hasPermission('website.stats');
    }
}
