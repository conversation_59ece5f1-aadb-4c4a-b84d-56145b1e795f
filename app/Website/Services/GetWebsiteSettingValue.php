<?php

namespace App\Website\Services;

use App\Accounts\Account;
use App\Website\WebsiteFile;
use App\Website\WebsitePage;
use App\Website\WebsiteSetting;
use App\Website\WebsiteSettingValue;

class GetWebsiteSettingValue
{
    protected $account_id = null;
    protected $setting_id = null;

    public function get($key = null): mixed
    {
        // If no account_id is provided, try to infer it from the request.
        if (!$this->account_id) {
            $this->account_id = request()->get('account_id');

            if (!$this->account_id) {
                return null;
            }
        }

        $setting = WebsiteSetting::where('key', $key)->first();

        if (!$setting) {
            return null;
        }

        $value = WebsiteSettingValue::where('website_setting_id', $setting->id)
            ->where('account_id', $this->account_id)
            ->first();

        if (!$value) {
            return null;
        }

        if ($setting->type == 'WebsiteFile') {
            return WebsiteFile::query()->where('id', $value->value)->first();
        } elseif ($setting->type == 'WebsitePage') {
            return WebsitePage::query()->where('id', $value->value)->first();
        } else {
            return $value->value;
        }
    }

    public function forSetting(WebsiteSetting|string|int $setting)
    {
        if ($setting instanceof WebsiteSetting) {
            $this->setting_id = $setting->id;
        } elseif (is_string($setting)) {
            $this->setting_id = WebsiteSetting::where('key', $setting)->first()?->id;
        } else {
            $this->setting_id = $setting;
        }

        return $this;
    }

    public function forAccount(Account|int $account)
    {
        if ($account instanceof Account) {
            $this->account_id = $account->id;
        } else {
            $this->account_id = $account;
        }

        return $this;
    }
}
