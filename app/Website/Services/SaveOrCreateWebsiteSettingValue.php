<?php

namespace App\Website\Services;

use App\Accounts\Account;
use App\Website\WebsiteSetting;
use App\Website\WebsiteSettingValue;

class SaveOrCreateWebsiteSettingValue
{
    protected int $account_id;

    protected string $key;

    public function save(mixed $new_value): WebsiteSettingValue
    {
        $setting = WebsiteSetting::where('key', $this->key)->first();

        $website_setting_value = WebsiteSettingValue::where('website_setting_id', $setting->id)
            ->where('account_id', $this->account_id)
            ->first();

        if (!$website_setting_value) {
            $website_setting_value = WebsiteSettingValue::create([
                'website_setting_id' => $setting->id,
                'account_id'         => $this->account_id,
                'value'              => $new_value,
            ]);
        } else {
            $website_setting_value->update([
                'value' => $new_value,
            ]);
        }

        return $website_setting_value;
    }

    public function forAccount(Account|int $account): self
    {
        if ($account instanceof Account) {
            $this->account_id = $account->id;
        } else {
            $this->account_id = $account;
        }

        return $this;
    }

    public function forSetting(WebsiteSetting|string $key): self
    {
        if (is_string($key)) {
            $this->key = $key;
        } else {
            $this->key = $key->key;
        }

        return $this;
    }
}
