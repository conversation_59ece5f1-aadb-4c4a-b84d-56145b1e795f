<?php

namespace App\Website\Services;

use App\Accounts\Account;
use App\Website\WebsiteFile;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class DeleteWebsiteFile
{
    protected $website_file   = null;

    public function __construct(WebsiteFile $website_file)
    {
        $this->website_file = $website_file;
    }

    public function delete(): bool
    {
        try {
            Storage::disk(WebsiteFile::$disk_name)->delete($this->website_file->file_folder . '/' . $this->website_file->file_name . '.' . $this->website_file->file_extension);

            $this->website_file->delete();

            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting website file: ' . $e->getMessage());

            return false;
        }
    }
}
