<?php

namespace App\Website\Services;

use App\Website\WebsitePage;
use App\Accounts\Account;
class WebsiteSettingService
{
    protected Account $account;

    public function __construct(
        protected int $accountId
    ) {
        $this->account = Account::find($this->accountId);
    }

    public function getSpecialLink(string $key)
    {
        $settingValue = (new GetWebsiteSettingValue())
            ->forAccount($this->account)
            ->get($key);

        if (!$settingValue) {
            return null;
        }

        $decodedValue = json_decode($settingValue, true);

        if (!$decodedValue) {
            return null;
        }

        // If this is a website page type link, let's include the page instance
        if ($decodedValue['link_type'] === 'page' && $decodedValue['website_page_id']) {
            $decodedValue['page'] = WebsitePage::find($decodedValue['website_page_id']);
            $decodedValue['url'] = $decodedValue['page']->getURL();
        }

        return $decodedValue;
    }
} 