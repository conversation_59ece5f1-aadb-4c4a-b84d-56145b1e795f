<?php

namespace App\Website\Services;

use App\Accounts\Account;
use App\Website\WebsiteFile;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class CreateWebsiteFile
{
    protected $account        = null;
    protected $website_file   = null;
    protected $uploaded_file  = null;
    protected $file_title     = null;
    protected $file_type      = null; // "application/pdf"
    protected $type           = null; // "PDF"
    protected $is_public      = true;
    protected $expires_at     = null;
    protected $storage_folder = null;

    public function create(): WebsiteFile
    {
        // Public
        if ($this->is_public) {
            $this->is_public = $this->is_public ? now() : null;
        } else {
            $this->is_public = null;
        }

        $this->website_file = WebsiteFile::create([
            'account_id' => $this->account->id,
            'starts_at'  => Carbon::now()->format('Y-m-d'),
            'expires_at' => $this->expires_at ? Carbon::parse($this->expires_at)->format('Y-m-d 23:59:59') : null,
            'title'      => $this->file_title,
            'url_title'  => Str::slug($this->file_title, '-'),
            'type'       => $this->type,
            'is_public'  => $this->is_public ? now() : null,
        ]);

        $this->saveFile();

        return $this->website_file;
    }

    public function forAccount(Account $account)
    {
        $this->account = $account;

        return $this;
    }

    public function expiresAt($expires_at)
    {
        $this->expires_at = $expires_at;

        return $this;
    }

    /**
     * @param UploadedFile $uploaded_file From request()->file() on the frontend
     * @param              $file_title
     *
     * @return CreateWebsiteFile
     * @throws \Exception
     */
    public function withFile(UploadedFile $uploaded_file, $file_title = null): CreateWebsiteFile
    {
        if ($uploaded_file->isValid()) {
            $this->uploaded_file = $uploaded_file;
            $this->file_title    = $file_title;
        } else {
            throw new \Exception('File was invalid.');
        }

        return $this;
    }

    public function withTitle($file_title): CreateWebsiteFile
    {
        $this->file_title = $file_title;

        return $this;
    }

    public function ofType($type): CreateWebsiteFile
    {
        $this->type = $type;

        return $this;
    }

    public function inStorageFolder($storage_folder = null)
    {
        if ($storage_folder) {
            $this->storage_folder = $storage_folder;
        }

        return $this;
    }

    public function isPublic($is_public = true)
    {
        $this->is_public = $is_public ? now() : null;

        return $this;
    }

    private function saveFile()
    {
        $account_idenfitier = $this->account->{WebsiteFile::$account_folder_identifier};
        $storage_folder     = $this->storage_folder ? $account_idenfitier . '/' . $this->storage_folder : $account_idenfitier;

        if (!$this->uploaded_file) {
            return;
        }

        // $folder    = Config::get('app.user_image_file_path');
        $extension = '.' . $this->uploaded_file->getClientOriginalExtension();

        $new_file_name                   = Str::random(6) . '--' . $this->uploaded_file->getClientOriginalName();
        $new_file_name_without_extension = str_replace($extension, '', $new_file_name);

        // Original -- saved as a JPG
        // We save in the account folder, preferably using the ULID of the account.
        if (!$this->uploaded_file->storeAs($storage_folder, $new_file_name, WebsiteFile::$disk_name)) {
            throw new \Exception('Could not write file to cloud server. The database entry has been saved though.');
        }

        $save_title = ($this->file_title ?: $this->website_file->title) ?: $this->uploaded_file->getClientOriginalName();

        $this->website_file->fill([
            'account_id'         => $this->account->id,
            'title'              => $save_title,
            'url_title'          => \Illuminate\Support\Str::slug($save_title, '-'),
            'storage_service'    => 'linode',
            'data_separator'     => '--',
            'file_original_name' => $this->uploaded_file->getClientOriginalName(),
            'file_size'          => $this->uploaded_file->getSize(),
            'file_folder'        => $storage_folder,
            'file_id'            => null,
            'file_name'          => $new_file_name_without_extension,
            'file_extension'     => $this->uploaded_file->getClientOriginalExtension(),
            'file_type'          => $this->uploaded_file->getClientMimeType(),
            'file_sha1'          => sha1(file_get_contents($this->uploaded_file->getRealPath())),
        ]);

        $this->website_file->save();
    }
}
