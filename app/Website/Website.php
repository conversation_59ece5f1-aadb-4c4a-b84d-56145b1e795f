<?php

namespace App\Website;

use App\Accounts\Account;
use App\Base\Models\Model;
use App\Users\User;

class Website extends Model
{
    protected $table = 'websites';

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}
