<?php

namespace App\Programs;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormField extends Model
{
    use SoftDeletes;

    protected $table = 'program_registration_form_fields';

    protected $casts = [
        'created_at'                    => 'datetime',
        'updated_at'                    => 'datetime',
        'deleted_at'                    => 'datetime',
        'is_required'                   => 'boolean',
        'is_question_answer'            => 'boolean',
        'is_select_program_group'       => 'boolean',
        'allow_multiple_answers'        => 'boolean',
        'is_text_answer'                => 'boolean',
        'is_true_false'                 => 'boolean',
        'is_yes_no'                     => 'boolean',
        'requires_file'                 => 'boolean',
        'allow_multiple_files'          => 'boolean',
        'is_user'                       => 'boolean',
        'for_contact_only'              => 'boolean',
        'question_answer_options'       => 'array',
        'question_correct_answers'      => 'array',
        'select_from_program_group_ids' => 'array',
    ];

    protected $fillable = [
        'uuid',
        'account_id',
        'program_id',
        'program_registration_form_id',
        'title',
        'subtitle',
        'description',
        'is_required',
        'is_question_answer',
        'allow_multiple_answers',
        'is_text_answer',
        'is_true_false',
        'is_yes_no',
        'requires_file',
        'allow_multiple_files',
        'is_user',
        'question_answer_options',
        'question_correct_answers',
        'sort_id',
        'is_select_program_group',
        'select_from_program_group_ids',
        'for_contact_only',
    ];

    public static $field_types = [
        'text'                    => 'Text Question/Answer',
        'multiple'                => 'Multiple Answers',
        'single'                  => 'Multiple Choice (Single Answer)',
        'true_false'              => 'True/False',
        'yes_no'                  => 'Yes/No',
        'is_select_program_group' => 'Select Program Group',
    ];

    protected $hidden = [];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function scopeForContact($query)
    {
        return $query->where('for_contact_only', true);
    }

    public function scopeForRegistrant($query)
    {
        return $query->where('for_contact_only', false);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function registration()
    {
        return $this->belongsTo(Registration::class);
    }
}
