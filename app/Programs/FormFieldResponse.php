<?php

namespace App\Programs;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormFieldResponse extends Model
{
    use SoftDeletes;

    protected $table = 'program_registration_form_field_responses';

    protected $casts = [
        'created_at'     => 'datetime',
        'updated_at'     => 'datetime',
        'deleted_at'     => 'datetime',
        'multi_response' => 'array',
    ];

    protected $fillable = [
        'uuid',
        'account_id',
        'program_id',
        'program_registration_form_field_id',
        'program_registration_form_id',
        'program_registration_id',
        'program_user_id',
        'response',
        'multi_response',
    ];

    protected $hidden = [];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function formField()
    {
        return $this->belongsTo(FormField::class, 'program_registration_form_field_id', 'id');
    }
}
