<?php

namespace App\Programs\Services;

use App\Programs\ProgramUserCheckin;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * Class SubmitUserCheckin
 *
 * @package App\Programs\Services
 *
 *          This class is responsible for submitting a user checkin.
 *          It will either create a new checkin, or update an existing checkin.
 */
class SubmitUserCheckin
{
    protected $attributes       = [];
    protected $program_id       = null;
    protected $program_user_id  = null;
    protected $program_group_id = null;
    protected $account_id       = null;
    protected $check_at         = null;
    protected $created_by_id    = null;

    public function submit(): ProgramUserCheckin
    {
        $existing_checkin = ProgramUserCheckin::where('account_id', $this->account_id)
            ->where('program_id', $this->program_id)
            ->where('program_user_id', $this->program_user_id)
            ->when($this->program_group_id, function ($query) {
                $query->where('program_group_id', $this->program_group_id);
            })
            ->isCheckedIn()
            ->first();

        // @TODO:  We could do group specific checkins here, but we don't user the program_group_id for now.

        // If we have an existing checkin, then we'll update it to be checked out.
        if ($existing_checkin) {
            $existing_checkin->update([
                'checkout_at'         => $this->check_at ?: now(), // If we have not been given a checkin_at, let's assume now.s
                'checkout_by_user_id' => $this->created_by_id,
            ]);

            return $existing_checkin;
        } // Otherwise, we need to create a new checkin.
        else {
            return ProgramUserCheckin::create([
                'account_id'         => $this->account_id,
                'program_id'         => $this->program_id,
                'program_group_id'   => $this->program_group_id,
                'program_user_id'    => $this->program_user_id,
                'checkin_at'         => $this->check_at ?: now(), // If we have not been given a checkin_at, let's assume now.s
                'created_by_user_id' => $this->created_by_id,
                'checkin_by_user_id' => $this->created_by_id,
                'uuid'               => Str::uuid(),
            ]);
        }
    }

    public function createdByUser($user)
    {
        $this->created_by_id = $user->id;

        return $this;
    }

    public function forProgramGroup($program_group_id = null)
    {
        if ($program_group_id) {
            $this->program_group_id = (int) $program_group_id;
        }

        return $this;
    }

    public function forProgramUser($program_user)
    {
        $this->program_user_id = $program_user->id;
        $this->program_id      = $program_user->program_id;
        $this->account_id      = $program_user->account_id;

        return $this;
    }

    public function checkAt($checkin_at_datetime)
    {
        $this->check_at = Carbon::parse($checkin_at_datetime);

        return $this;
    }
}
