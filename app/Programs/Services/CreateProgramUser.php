<?php

namespace App\Programs\Services;

use App\Accounts\Account;
use App\Programs\Form;
use App\Programs\Program;
use App\Programs\ProgramUser;
use App\Programs\Registration;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class CreateProgramUser
{
    protected                   $attributes              = [];
    protected Registration|null $registration            = null;
    protected                   $registration_pivot_data = [];
    protected Form|null         $form                    = null;
    protected                   $registration_data       = [];
    protected ProgramUser|null  $user                    = null;
    protected                   $is_registrant           = false;
    protected                   $is_contact              = false;
    protected                   $status                  = 'active';
    protected                   $roles                   = null;
    protected                   $groups                  = null;

    public function create($attributes): ProgramUser
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        // Trim all string values in attributes
        foreach ($this->attributes as $key => $value) {
            if (is_string($value)) {
                $this->attributes[$key] = trim($value);
            }
        }

        if (!isset($this->attributes['account_id'])) {
            throw new \Exception('Account ID is required.');
        }
        if (!isset($this->attributes['program_id'])) {
            throw new \Exception('Program ID is required.');
        }

        // Add a UUID for this new user.
        $this->attributes['public_token'] = Str::uuid()->toString();
        $this->attributes['uuid']         = Str::uuid()->toString();

        if (Arr::has($this->attributes, 'birthdate')) {
            $this->attributes['birthdate'] = Arr::get($this->attributes, 'birthdate') ? Carbon::parse(Arr::get($this->attributes, 'birthdate'))->startOfDay()->setTimezone('utc') : null;
        }

        $this->user = ProgramUser::create($this->attributes);

        $this->user->refresh();

        // Groups
        if ($this->groups !== null) {
            // Groups
            $this->user->groups()->syncWithPivotValues(
                is_array($this->groups) ? $this->groups : [],
                [
                    'account_id' => $this->user->account_id,
                    'created_at' => now(),
                ]
            );
        }

        // If we already know what registration this user is tied to, use that.
        if ($this->registration) {
            if ($this->is_registrant) {
                $this->registration->program_user_id = $this->user->id;
                $this->registration->is_approved     = now(); // TODO - we might have a waitlist!
                $this->registration->save();
            } elseif ($this->is_contact) {
                $this->user->associatedRegistrations()->syncWithPivotValues(
                    $this->registration->id,
                    array_merge($this->registration_pivot_data, [
                        'account_id'              => $this->user->account_id,
                        'program_id'              => $this->user->program_id,
                        'created_at'              => now(),
                        'program_user_id'         => $this->user->id,
                        'program_registration_id' => $this->registration->id,
                    ]),
                    false
                );
            }

            $this->user->save();
        } elseif ($this->form) {
            $this->registration = Registration::create([
                'account_id'                   => $this->attributes['account_id'],
                'program_id'                   => $this->attributes['program_id'],
                'program_user_id'              => $this->user->id,
                'program_registration_form_id' => $this->form->id,
                'is_approved'                  => now(), // TODO - we might have a waitlist!
            ]);

            // Auto-assign groups if specified by this form.
            if ($this->form->auto_assign_program_group_ids) {
                (new UpdateProgramUser($this->user))
                    ->setGroups($this->form->auto_assign_program_group_ids)
                    ->update();
            }

            if ($this->registration->is_waitlist && $this->form->auto_assign_waitlist_program_group_ids) {
                (new UpdateProgramUser($this->user))
                    ->setGroups($this->form->auto_assign_waitlist_program_group_ids)
                    ->update();
            }

            $this->user->save();
        }

        return $this->user;
    }

    public function forAccount(Account $account): self
    {
        $this->attributes['account_id'] = $account->id;

        return $this;
    }

    public function forProgram(Program $program): self
    {
        $this->attributes['program_id'] = $program->id;

        return $this;
    }

    // Array of INTS of the Group IDs
    public function withGroups($groups = []): self
    {
        $this->groups = $groups === null ? [] : $groups;

        return $this;
    }

    public function forRegistration(Registration $program_registration, $pivot_data = []): self
    {
        $this->attributes['program_registration_id'] = $program_registration->id;
        $this->registration                          = $program_registration;
        $this->registration_pivot_data               = $pivot_data;

        return $this;
    }

    public function isRegistrant(): self
    {
        $this->is_registrant = true;

        return $this;
    }

    public function isContact(): self
    {
        $this->is_contact = true;

        return $this;
    }

    public function withRegistrationForm(Form $registration_form, $registration_data = []): self
    {
        $this->form              = $registration_form;
        $this->registration_data = $registration_data;

        return $this;
    }
}
