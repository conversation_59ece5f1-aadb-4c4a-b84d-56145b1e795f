<?php

namespace App\Programs\Services;

use App\Programs\ProgramUser;
use App\Programs\Registration;

class UpdateProgramUser
{
    protected                   $attributes              = [];
    protected Registration|null $registration            = null;
    protected                   $registration_pivot_data = [];
    protected ProgramUser       $user;
    protected                   $status                  = 'active';
    protected                   $roles                   = null;
    protected                   $groups                  = null;

    public function __construct(ProgramUser $user)
    {
        $this->user = $user;
    }

    public function update($attributes = []): ProgramUser
    {
        if (!$this->user) {
            throw new \Exception('User not set.');
        }

        $this->user->fill($attributes);

        // Groups
        if ($this->groups !== null && is_array($this->groups)) {
            // Groups - sync with pivot values (this will remove groups not in the array)
            $this->user->groups()->syncWithPivotValues(
                $this->groups,
                [
                    'account_id' => $this->user->account_id,
                    'created_at' => now(),
                ],
                true
            );
        }

        if ($this->registration) {
            $this->user->associatedRegistrations()->syncWithPivotValues(
                $this->registration->id,
                array_merge($this->registration_pivot_data, [
                    'account_id' => $this->user->account_id,
                    'program_id' => $this->user->program_id,
                    'created_at' => now(),
                ]),
                false
            );
        }

        $this->user->save();

        return $this->user;
    }

    // Array of INTS of the Group IDs
    public function setGroups($groups = []): self
    {
        $this->groups = $groups === null ? [] : $groups;

        return $this;
    }

    public function attachRegistration(Registration $registration, array $pivot_data = []): self
    {
        $this->registration            = $registration;
        $this->registration_pivot_data = $pivot_data;

        return $this;
    }
}
