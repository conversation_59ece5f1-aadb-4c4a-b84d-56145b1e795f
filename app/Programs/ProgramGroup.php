<?php

namespace App\Programs;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProgramGroup extends Model
{
    use SoftDeletes;

    protected $table = 'program_groups';

    protected $hidden = ['pivot'];

    protected $casts = [
        'created_at'            => 'datetime',
        'updated_at'            => 'datetime',
        'deleted_at'            => 'datetime',
        'enable_checkins'       => 'boolean',
        'is_program_group'      => 'boolean',
        'is_registration_group' => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'program_id',
        'name',
        'url_name',
        'description',
        'bg_color',
        'text_color',
        'tw_bg_color',
        'tw_text_color',
        'enable_checkins',
        'is_program_group',
        'is_registration_group',
        'sort_id',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function currentCheckins()
    {
        return $this->hasMany(ProgramUserCheckin::class, 'program_group_id', 'id')
            ->whereNotNull('checkin_at')
            ->whereNull('checkout_at');
    }

    public function users()
    {
        return $this->belongsToMany(ProgramUser::class, 'program_user_to_group', 'program_group_id', 'program_user_id')
            ->withoutTrashed()
            ->isRegistrant();
//            ->as('settings')
//            ->withPivot([
//                'is_registered',
//            ]);
    }

    public function scopeCheckinsEnabled($query)
    {
        return $query->where('enable_checkins', true);
    }

    public function scopeIsRegistrationGroup($query)
    {
        return $query->where('is_registration_group', true);
    }
}
