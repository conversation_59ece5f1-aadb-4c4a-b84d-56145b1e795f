<?php

namespace App\Programs;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimonHamp\TheOg\Background;
use SimonHamp\TheOg\Image;
use Sqids\Sqids;

class Program extends Model
{
    use SoftDeletes;

    protected $table = 'programs';

    protected $casts = [
        'created_at'                 => 'datetime',
        'updated_at'                 => 'datetime',
        'deleted_at'                 => 'datetime',
        'start_at'                   => 'datetime',
        'end_at'                     => 'datetime',
        'is_active'                  => 'boolean',
        'enable_public_registration' => 'boolean',
    ];

    protected $fillable = [
        'account_id',
        'uuid',
        'squid',
        'name',
        'url_name',
        'overview',
        'description',
        'start_at',
        'end_at',
        'start_at_time',
        'end_at_time',
        'location',
        'location_details',
        'is_active',
        'enable_public_registration',
        'og_image_url',
        'og_image_path',
        'cover_image_url',
        'cover_image_path',
    ];

    protected $hidden = [];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function getUuid()
    {
        if (!$this->uuid) {
            $this->uuid = Str::uuid();
            $this->save();
        }

        return $this->uuid;
    }

    public function getSquid()
    {
        if (!$this->squid) {
            $sqids = new Sqids('0123456789abcdefghijklmnopqrstuvwxyz');
            $id    = $sqids->encode([$this->id]);

            $this->squid = $id;

            $this->save();
        }

        return $this->squid;
    }

    public function getPublicUrl()
    {
        return route('open.programs.view', ['program' => $this->squid]);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function users()
    {
        return $this->hasMany(ProgramUser::class);
    }

    public function forms()
    {
        return $this->hasMany(Form::class);
    }

    public function registeredUsers()
    {
        return $this->hasManyThrough(ProgramUser::class, Registration::class, 'program_id', 'id', 'id', 'program_user_id');
    }

    public function registrations()
    {
        return $this->hasMany(Registration::class);
    }

    public function currentCheckins()
    {
        return $this->hasMany(ProgramUser::class)
            ->whereHas('groups', function ($query) {
                $query->where('is_registration_group', true);
            })
            ->whereHas('currentCheckin');
    }

    public function groups()
    {
        return $this->hasMany(ProgramGroup::class);
    }

    public function scopeIsActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeIsPublic($query)
    {
        return $query->where('enable_public_registration', true);
    }

    public function generateOGImage()
    {
        $og_image = (new Image())
            ->accentColor('#005f78')
            ->border()
            ->url($this->account->church_website ?: '')
            ->title($this->name)
            ->description($this->overview ?: 'View more for dates, details and registration information.')
            ->background(Background::JustWaves, 0.2)
            ->toString();

        $image_path = $this->account->getUlid() . '/programs/' . $this->getUuid() . '/images/og-image.png';

        Storage::disk('public-cdn')
            ->put($image_path, $og_image);

        $this->og_image_path = $image_path;
        $this->og_image_url  = Storage::disk('public-cdn')->url($image_path);
        $this->save();

        return $this->og_image_url;
    }
}
