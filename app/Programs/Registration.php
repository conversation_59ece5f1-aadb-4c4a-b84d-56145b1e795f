<?php

namespace App\Programs;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Registration extends Model
{
    use SoftDeletes;

    protected $table = 'program_registrations';

    protected $casts = [
        'created_at'           => 'datetime',
        'updated_at'           => 'datetime',
        'deleted_at'           => 'datetime',
        'is_waitlist'          => 'datetime',
        'is_approved'          => 'datetime',
        'registration_counter' => 'integer',
    ];

    protected $fillable = [
        'uuid',
        'account_id',
        'program_id',
        'program_registration_form_id',
        'program_user_id',
        'is_waitlist',
        'is_approved',
        'registration_counter',
    ];

    protected $hidden = [];

    static public $attached_user_pivot_fields = [
        'is_contact',
        'is_primary_contact',
        'is_emergency_contact',
        'can_pickup',
        'can_not_pickup',
        'no_contact_allowed',
        'registrant_notes',
        'contact_notes',
        'admin_notes',
        'metadata',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function registrationForm()
    {
        return $this->belongsTo(Form::class, 'program_registration_form_id', 'id');
    }

    public function registrationFormResponses()
    {
        return $this->hasMany(FormFieldResponse::class, 'program_registration_id', 'id')
            ->where('program_registration_form_id', $this->program_registration_form_id)
            ->where('program_user_id', $this->program_user_id);
    }

    public function registrant()
    {
        return $this->belongsTo(ProgramUser::class);
    }

    public function contacts()
    {
        return $this->belongsToMany(ProgramUser::class, 'program_user_to_registration', 'program_registration_id', 'program_user_id')
            ->as('settings')
            ->withPivot(self::$attached_user_pivot_fields)
            ->withTimestamps();
    }

    public function scopeIsWaitlist($query)
    {
        return $query->whereNotNull('is_waitlist');
    }

    public function scopeIsNotWaitlist($query)
    {
        return $query->whereNull('is_waitlist');
    }

    public function scopeIsApproved($query)
    {
        return $query->whereNotNull('is_approved');
    }

    public function scopeIsNotApproved($query)
    {
        return $query->whereNull('is_approved');
    }
}
