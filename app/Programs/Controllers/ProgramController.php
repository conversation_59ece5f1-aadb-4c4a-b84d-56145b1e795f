<?php

namespace App\Programs\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Programs\Program;
use App\Users\Services\DeleteUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProgramController extends Controller
{
    public function index()
    {
        return view('admin.programs.index')
            ->with('programs', Program::visibleTo(auth()->user())->paginate(15));
    }

    public function create()
    {
        return view('admin.programs.programs.modals.create');
    }

    public function createSubmit()
    {
        try {
            $program = Program::create([
                'account_id'                 => auth()->user()->account_id,
                'name'                       => request('name'),
                'url_name'                   => Str::slug(request('name')),
                'overview'                   => request('overview'),
                'description'                => request('description'),
                'location'                   => request('location'),
                'is_active'                  => true,
                'enable_public_registration' => true,
            ]);

            $program->getUuid();
            $program->getSquid();

            if (request()->has('start_at')) {
                $program->start_at = request()->get('start_at');
            }
            if (request()->has('end_at')) {
                $program->end_at = request()->get('end_at');
            }

            if (request()->get('start_at_hour') && request()->get('start_at_hour') > 0) {
                $program->start_at_time = str_pad(request()->get('start_at_hour'), 2, '0', STR_PAD_LEFT) . ':' . str_pad(request()->get('start_at_minute'), 2, '0', STR_PAD_LEFT) . ':00';
            } else {
                $program->start_at_time = null;
            }
            if (request()->get('end_at_hour') && request()->get('end_at_hour') > 0) {
                $program->end_at_time = str_pad(request()->get('end_at_hour'), 2, '0', STR_PAD_LEFT) . ':' . str_pad(request()->get('end_at_minute'), 2, '0', STR_PAD_LEFT) . ':00';
            } else {
                $program->end_at_time = null;
            }

            if (request()->has('cover_image')) {
                $image_folder = $program->account->getUlid() . '/programs/' . $program->getUuid() . '/images';

                $image_path = request()
                    ->file('cover_image')
                    ->store(
                        $image_folder,
                        'public-cdn'
                    );

                $program->cover_image_path = $image_path;
                $program->cover_image_url  = Storage::disk('public-cdn')->url($image_path);

                $program->save();
            }

            try {
                $program->generateOGImage();
            } catch (\Exception $e) {
                Log::error('ProgramController::createSubmit - Could not generate OG image.', [
                    'message' => $e->getMessage(),
                    'e'       => $e,
                    'program' => $program,
                ]);
            }
        } catch (\Exception $e) {
            return back()
                ->with('message.failure', 'Oops! There was an error: ' . $e->getMessage());
        }

        return redirect()
            ->route('admin.programs.view', $program)
            ->with('message.success', 'Program created successfully.');
    }

    public function view(Program $program)
    {
        return view('admin.programs.view')
            ->with('program', $program);
    }

    public function checkins(Program $program)
    {
        return view('admin.programs.checkins.index')
            ->with('program', $program);
    }

    public function checkinsLive(Program $program)
    {
        return view('admin.programs.checkins-live')
            ->with('program', $program);
    }

    public function edit(Program $program)
    {
        return view('admin.programs.programs.modals.edit')
            ->with('program', $program);
    }

    public function save(Program $program)
    {
        try {
            DB::transaction(function () use ($program) {
                $program->update([
                    'name'        => request('name'),
                    'url_name'    => Str::slug(request('name')),
                    'overview'    => request('overview'),
                    'description' => request('description'),
                    'location'    => request('location'),
                ]);

                if (request()->has('start_at')) {
                    $program->start_at = request()->get('start_at');
                } else {
                    $program->start_at = null;
                }
                if (request()->has('end_at')) {
                    $program->end_at = request()->get('end_at');
                } else {
                    $program->end_at = null;
                }

                if (request()->get('start_at_hour') && request()->get('start_at_hour') > 0) {
                    $program->start_at_time = str_pad(request()->get('start_at_hour'), 2, '0', STR_PAD_LEFT) . ':' . str_pad(request()->get('start_at_minute'), 2, '0', STR_PAD_LEFT) . ':00';
                } else {
                    $program->start_at_time = null;
                }
                if (request()->get('end_at_hour') && request()->get('end_at_hour') > 0) {
                    $program->end_at_time = str_pad(request()->get('end_at_hour'), 2, '0', STR_PAD_LEFT) . ':' . str_pad(request()->get('end_at_minute'), 2, '0', STR_PAD_LEFT) . ':00';
                } else {
                    $program->end_at_time = null;
                }

                if (request()->has('cover_image')) {
                    $image_folder = $program->account->getUlid() . '/programs/' . $program->getUuid() . '/images';

                    $image_path = request()
                        ->file('cover_image')
                        ->store(
                            $image_folder,
                            'public-cdn'
                        );

                    // Delete our existing image, if it exists.
                    if ($program->cover_image_path) {
                        Storage::disk('public-cdn')->delete($program->cover_image_path);
                    }

                    $program->cover_image_path = $image_path;
                    $program->cover_image_url  = Storage::disk('public-cdn')->url($image_path);

                    $program->save();
                }

                try {
                    $program->generateOGImage();
                } catch (\Exception $e) {
                    Log::error('ProgramController::save - Could not generate OG image.', [
                        'message' => $e->getMessage(),
                        'e'       => $e,
                        'program' => $program,
                    ]);
                }
            });
        } catch (\Exception $e) {
            return back()
                ->with('message.failure', $e->getMessage());
        }

        return back()
            ->with('message.success', 'Saved successfully.');
    }

    public function delete(Program $program)
    {
        return view('admin.programs.modals.delete')
            ->with('user', $user);
    }

    public function destroy(Program $program)
    {
        try {
            Log::info('User::delete() -- User # ' . auth()->user()->id . ' is deleting User # ' . $user->id);

            (new DeleteUser($user))->delete();
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect(route('admin.programs.index'))->with('message.success', 'Delete successful.');
    }
}
