<?php

namespace App\Programs\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Programs\Program;
use App\Programs\ProgramUser;
use App\Programs\Services\CreateProgramUser;
use App\Programs\Services\UpdateProgramUser;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProgramUserController extends Controller
{
    public function index(Program $program)
    {
        return view('admin.programs.users.index')
            ->with('program', $program);
    }

    public function registered(Program $program)
    {
        return view('admin.programs.users.registered')
            ->with('program', $program);
    }

    public function create(Program $program)
    {
        return view('admin.programs.users.create')
            ->with('program', $program);
    }

    protected function store(Program $program)
    {
        try {
            DB::transaction(function () use ($program) {
                $user = (new CreateProgramUser())
                    ->forAccount(auth()->user()->account)
                    ->forProgram($program)
//                    ->forRegistrationForm($program->registrationForms()->first())
                    ->withGroups(request()->input('groups'))
                    ->create(
                        request()->only([
                            'first_name',
                            'last_name',
                            'family_role',
                            'gender',
                            'birthdate',
                            'marital_status',
                            'email',
                            'mobile_phone',
                            'allergies',
                            'special_needs',
                            'notes',
                        ])
                    );
                Log::info('ProgramUser::store() --  User # ' . auth()->user()->id . ' has created ProgramUser # ' . $user->id);

                if (request('contact1') && Arr::has(request('contact1'), ['first_name'])) {
                    (new CreateProgramUser())
                        ->forAccount(auth()->user()->account)
                        ->forProgram($program)
                        ->forRegistration($user->registration)
                        ->create(request('contact1'));
                }
                if (request('contact2') && Arr::has(request('contact2'), ['first_name'])) {
                    (new CreateProgramUser())
                        ->forAccount(auth()->user()->account)
                        ->forProgram($program)
                        ->forRegistration($user->registration)
                        ->create(request('contact2'));
                }
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.programs.users.index', [$program])
            ->with('message.success', 'User created successfully.');
    }

    public function edit(Program $program, ProgramUser $programUser)
    {
        return view('admin.programs.users.edit')
            ->with('program', $program)
            ->with('programUser', $programUser);
    }

    public function save(Program $program, ProgramUser $programUser)
    {
        dd(request()->input('groups'));
        try {
            DB::transaction(function () use ($programUser) {
                $user = (new UpdateProgramUser($programUser))
                    ->setGroups(request()->input('groups'))
                    ->update(
                        request()->only([
                            'first_name',
                            'last_name',
                            'family_role',
                            'gender',
                            'birthdate',
                            'marital_status',
                            'email',
                            'mobile_phone',
                            'allergies',
                            'special_needs',
                            'notes',
                        ])
                    );
                Log::info('ProgramUser::edit() --  User # ' . auth()->user()->id . ' has updated ProgramUser # ' . $user->id);
            });
        } catch (\Exception $e) {
            return back()->with('message.failure', $e->getMessage());
        }

        return redirect()
            ->route('admin.programs.users.index', [$program])
            ->with('message.success', 'User saved successfully.');
    }
}
