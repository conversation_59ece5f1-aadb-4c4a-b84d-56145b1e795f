<?php

namespace App\Programs\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Programs\Form;
use App\Programs\FormField;
use App\Programs\Program;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ProgramFormController extends Controller
{
    public function index(Program $program)
    {
        return view("admin.programs.forms.index")
            ->with("program", $program);
    }

    public function create(Program $program)
    {
        return view("admin.programs.forms.create")
            ->with("program", $program);
    }

    protected function store(Program $program)
    {
        try {
            DB::transaction(function () use ($program) {
                // Validate form metadata
                $formValidator = Validator::make(request()->all(), [
                    "name"               => "required|string|max:500",
                    "description"        => "nullable|string",
                    "end_at"             => "nullable|date",
                    "registration_limit" => "nullable|integer|min:0",
                    "is_public"          => "required|boolean",
                    "sort_id"            => "nullable|integer|min:0",
                ]);

                if ($formValidator->fails()) {
                    return redirect()
                        ->back()
                        ->withErrors($formValidator)
                        ->withInput();
                }

                // Create the form
                $form = Form::create([
                    'account_id'                  => auth()->user()->account_id,
                    'program_id'                  => $program->id,
                    'created_by_user_id'          => auth()->id(),
                    'name'                        => request()->input('name'),
                    'description'                 => request()->input('description'),
                    'end_at'                      => request()->input('end_at'),
                    'registration_limit'          => request()->input('registration_limit'),
                    'is_public'                   => request()->input('is_public'),
                    'sort_id'                     => request()->input('sort_id'),
                    'required_user_fields'        => request()->input('required_user_fields'),
                    'optional_user_fields'        => request()->input('optional_user_fields'),
                    'require_contact_information' => request()->input('require_contact_information') ? true : false,
                    'required_contact_fields'     => request()->input('required_contact_fields'),
                    'optional_contact_fields'     => request()->input('optional_contact_fields'),
                ]);

                // Validate and process fields - REGISTRANT
                $fields = request()->input('fields', []);

                foreach ($fields as $index => $field) {
                    $fieldValidator = Validator::make($field, [
                        'title'                           => 'required|string|max:500',
                        'description'                     => 'nullable|string|max:500',
                        'is_required'                     => 'required|boolean',
                        'type'                            => 'required|in:text,multiple,single,true_false,yes_no,is_select_program_group',
                        'question_answer_options'         => 'nullable|array',
                        'question_answer_options.*'       => 'required_if:type,multiple,single|max:255',
                        'select_from_program_group_ids'   => 'required_if:type,is_select_program_group|array',
                        'select_from_program_group_ids.*' => 'required_if:type,is_select_program_group|exists:program_groups,id',
                    ]);

                    if ($fieldValidator->fails()) {
                        return redirect()
                            ->back()
                            ->withErrors($fieldValidator)
                            ->withInput();
                    }

                    // Prepare field data
                    $fieldData = [
                        "account_id"                    => auth()->user()->account_id,
                        "program_id"                    => $program->id,
                        "program_registration_form_id"  => $form->id,
                        "title"                         => $field["title"],
                        "description"                   => $field["description"],
                        "is_required"                   => $field["is_required"],
                        "is_text_answer"                => $field["type"] === "text",
                        "is_question_answer"            => in_array($field["type"], [
                            "multiple",
                            "single",
                        ]),
                        "allow_multiple_answers"        =>
                            $field["type"] === "multiple",
                        "is_true_false"                 => $field["type"] === "true_false",
                        "is_yes_no"                     => $field["type"] === "yes_no",
                        "is_select_program_group"       => $field["type"] === "is_select_program_group",
                        "select_from_program_group_ids" => $field["type"] === "is_select_program_group"
                            ? $field["select_from_program_group_ids"]
                            : null,
                        "question_answer_options"       => !empty(
                        $field["question_answer_options"]
                        )
                            ? $field["question_answer_options"]
                            : null,
                        "sort_id"                       => $index,
                    ];

                    // Create the form field
                    FormField::create($fieldData);
                }

                // Validate and process fields - CONTACTS
                $contact_fields = request()->input('contact_fields', []);

                foreach ($contact_fields as $index => $field) {
                    $fieldValidator = Validator::make($field, [
                        'title'                           => 'required|string|max:500',
                        'description'                     => 'nullable|string|max:500',
                        'is_required'                     => 'required|boolean',
                        'type'                            => 'required|in:text,multiple,single,true_false,yes_no,is_select_program_group',
                        'question_answer_options'         => 'nullable|array',
                        'question_answer_options.*'       => 'required_if:type,multiple,single|max:255',
                        'select_from_program_group_ids'   => 'required_if:type,is_select_program_group|array',
                        'select_from_program_group_ids.*' => 'required_if:type,is_select_program_group|exists:program_groups,id',
                    ]);

                    if ($fieldValidator->fails()) {
                        return redirect()
                            ->back()
                            ->withErrors($fieldValidator)
                            ->withInput();
                    }

                    // Prepare field data
                    $fieldData = [
                        "account_id"                    => auth()->user()->account_id,
                        "program_id"                    => $program->id,
                        "program_registration_form_id"  => $form->id,
                        "for_contact_only"              => true,
                        "title"                         => $field["title"],
                        "description"                   => $field["description"],
                        "is_required"                   => $field["is_required"],
                        "is_text_answer"                => $field["type"] === "text",
                        "is_question_answer"            => in_array($field["type"], [
                            "multiple",
                            "single",
                        ]),
                        "allow_multiple_answers"        =>
                            $field["type"] === "multiple",
                        "is_true_false"                 => $field["type"] === "true_false",
                        "is_yes_no"                     => $field["type"] === "yes_no",
                        "is_select_program_group"       => $field["type"] === "is_select_program_group",
                        "select_from_program_group_ids" => $field["type"] === "is_select_program_group"
                            ? $field["select_from_program_group_ids"]
                            : null,
                        "question_answer_options"       => !empty(
                        $field["question_answer_options"]
                        )
                            ? $field["question_answer_options"]
                            : null,
                        "sort_id"                       => $index,
                    ];

                    // Create the form field
                    FormField::create($fieldData);
                }
            });
        } catch (\Exception $e) {
            return back()->with("message.failure", $e->getMessage());
        }

        return redirect()
            ->route("admin.programs.forms.index", [$program])
            ->with("message.success", "Form created successfully.");
    }

    public function edit(Program $program, Form $form)
    {
        return view("admin.programs.forms.edit")
            ->with("program", $program)
            ->with("form", $form);
    }

    public function save(Program $program, Form $form)
    {
        try {
            DB::transaction(function () use ($program, $form) {
                // Validate form metadata
                $formValidator = Validator::make(request()->all(), [
                    "name"                    => "required|string|max:500",
                    "description"             => "nullable|string",
                    "end_at"                  => "nullable|date",
                    "registration_limit"      => "nullable|integer|min:0",
                    "is_public"               => "required|boolean",
                    "required_user_fields"    => "nullable|array",
                    "optional_user_fields"    => "nullable|array",
                    "required_contact_fields" => "nullable|array",
                    "optional_contact_fields" => "nullable|array",
                ]);

                if ($formValidator->fails()) {
                    return redirect()
                        ->back()
                        ->withErrors($formValidator)
                        ->withInput();
                }

                // Update the form
                $form->update([
                    "name"                    => request()->input("name"),
                    "description"             => request()->input("description"),
                    "end_at"                  => request()->input("end_at"),
                    "registration_limit"      => request()->input("registration_limit"),
                    "is_public"               => request()->input("is_public"),
                    "required_user_fields"    => request()->input("required_user_fields"),
                    "optional_user_fields"    => request()->input("optional_user_fields"),
                    "required_contact_fields" => request()->input("required_contact_fields"),
                    "optional_contact_fields" => request()->input("optional_contact_fields"),
                ]);

                // Validate and process new fields
                $newFields = request()->input("new_registrant_fields", []);
                foreach ($newFields as $index => $field) {
                    $fieldValidator = Validator::make($field, [
                        "title"                           => "required|string|max:500",
                        "description"                     => "nullable|string|max:500",
                        "is_required"                     => "required|boolean",
                        "type"                            => "required|in:text,multiple,single,true_false,yes_no,is_select_program_group",
                        "question_answer_options"         => "nullable|array",
                        "question_answer_options.*"       => "required_if:type,multiple,single|max:255",
                        "select_from_program_group_ids"   => "required_if:type,is_select_program_group|array",
                        "select_from_program_group_ids.*" => "required_if:type,is_select_program_group|exists:program_groups,id",
                    ]);

                    if ($fieldValidator->fails()) {
                        return redirect()
                            ->back()
                            ->withErrors($fieldValidator)
                            ->withInput();
                    }

                    // Calculate next sort_id
                    $nextSortId = $form->fields()->max("sort_id") + 1;

                    // Prepare field data
                    $fieldData = [
                        "account_id"                    => auth()->user()->account_id,
                        "program_id"                    => $program->id,
                        "program_registration_form_id"  => $form->id,
                        "title"                         => $field["title"],
                        "description"                   => $field["description"],
                        "is_required"                   => $field["is_required"],
                        "is_text_answer"                => $field["type"] === "text",
                        "is_question_answer"            => in_array($field["type"], [
                            "multiple",
                            "single",
                        ]),
                        "allow_multiple_answers"        => $field["type"] === "multiple",
                        "is_true_false"                 => $field["type"] === "true_false",
                        "is_yes_no"                     => $field["type"] === "yes_no",
                        "is_select_program_group"       => $field["type"] === "is_select_program_group",
                        "select_from_program_group_ids" => $field["type"] === "is_select_program_group"
                            ? $field["select_from_program_group_ids"]
                            : null,
                        "question_answer_options"       => !empty(
                        $field["question_answer_options"]
                        )
                            ? $field["question_answer_options"]
                            : null,
                        "sort_id"                       => $nextSortId,
                    ];

                    // Create the new form field
                    FormField::create($fieldData);
                }

                // Validate and process new fields
                $newContactFields = request()->input("new_contact_fields", []);
                foreach ($newContactFields as $index => $field) {
                    $fieldValidator = Validator::make($field, [
                        "title"                           => "required|string|max:500",
                        "description"                     => "nullable|string|max:500",
                        "is_required"                     => "required|boolean",
                        "type"                            => "required|in:text,multiple,single,true_false,yes_no,is_select_program_group",
                        "question_answer_options"         => "nullable|array",
                        "question_answer_options.*"       => "required_if:type,multiple,single|max:255",
                        "select_from_program_group_ids"   => "required_if:type,is_select_program_group|array",
                        "select_from_program_group_ids.*" => "required_if:type,is_select_program_group|exists:program_groups,id",
                    ]);

                    if ($fieldValidator->fails()) {
                        return redirect()
                            ->back()
                            ->withErrors($fieldValidator)
                            ->withInput();
                    }

                    // Calculate next sort_id
                    $nextSortId = $form->fields()->max("sort_id") + 1;

                    // Prepare field data
                    $fieldData = [
                        "for_contact_only"              => true,
                        "account_id"                    => auth()->user()->account_id,
                        "program_id"                    => $program->id,
                        "program_registration_form_id"  => $form->id,
                        "title"                         => $field["title"],
                        "description"                   => $field["description"],
                        "is_required"                   => $field["is_required"],
                        "is_text_answer"                => $field["type"] === "text",
                        "is_question_answer"            => in_array($field["type"], [
                            "multiple",
                            "single",
                        ]),
                        "allow_multiple_answers"        => $field["type"] === "multiple",
                        "is_true_false"                 => $field["type"] === "true_false",
                        "is_yes_no"                     => $field["type"] === "yes_no",
                        "is_select_program_group"       => $field["type"] === "is_select_program_group",
                        "select_from_program_group_ids" => $field["type"] === "is_select_program_group"
                            ? $field["select_from_program_group_ids"]
                            : null,
                        "question_answer_options"       => !empty(
                        $field["question_answer_options"]
                        )
                            ? $field["question_answer_options"]
                            : null,
                        "sort_id"                       => $nextSortId,
                    ];

                    // Create the new form field
                    FormField::create($fieldData);
                }

                Log::info(
                    "ProgramForm::edit() --  User # " .
                    auth()->user()->id .
                    " has updated Form # " .
                    $form->id
                );
            });
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with("message.failure", $e->getMessage());
        }

        return redirect()
            ->route("admin.programs.forms.index", [$program])
            ->with("message.success", "Form updated successfully.");
    }

    public function delete(Program $program)
    {
        return view("admin.programs.modals.delete")->with("user", $user);
    }

    public function destroy(Program $program)
    {
        try {
            Log::info(
                "User::delete() -- User # " .
                auth()->user()->id .
                " is deleting User # " .
                $user->id
            );
        } catch (\Exception $e) {
            return back()->with("message.failure", $e->getMessage());
        }

        return redirect(route("admin.programs.index"))->with(
            "message.success",
            "Delete successful."
        );
    }

    public function showPasswordResetModal(Program $program)
    {
        return view("admin.programs.modals.password-reset")->withUser($user);
    }
}
