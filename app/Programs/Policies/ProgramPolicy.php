<?php

namespace App\Programs\Policies;

use App\Programs\Program;
use App\Users\User;

class ProgramPolicy
{
    public function before($user, $ability)
    {
        if (!$user->account->getSetting('feature.programs')) {
            return false;
        }
    }

    public function index(User $user)
    {
        return $user->hasPermission('programs.view');
    }

    public function view(User $user, Program $program)
    {
        return $user->hasPermission('programs.view')
            && $user->account_id == $program->account_id;
    }

    public function viewGroups(User $user, Program $program)
    {
        return $user->hasPermission('programs.view')
            && $user->account_id == $program->account_id;
    }

    public function search(User $user)
    {
        return $user->hasPermission('programs.view');
    }

    public function create(User $user)
    {
        return $user->hasPermission('programs.manage');
    }
    
    public function createGroup(User $user, Program $program)
    {
        return $user->hasPermission('programs.manage') && $user->account_id == $program->account_id;
    }

    public function store(User $user)
    {
        return $user->hasPermission('programs.manage');
    }

    public function edit(User $user)
    {
        return $user->hasPermission('programs.manage');
    }

    public function save(User $user)
    {
        return $user->hasPermission('programs.manage');
    }

    public function retrieve(User $user)
    {
        return $user->hasPermission('programs.view');
    }

    public function delete(User $user)
    {
        return $user->hasPermission('programs.delete');
    }
}