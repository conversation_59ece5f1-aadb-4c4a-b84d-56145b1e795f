<?php

namespace App\Visitors;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class History extends Model
{
    use SoftDeletes;

    protected $table = 'visitor_history';

    protected $casts = [
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
        'deleted_at'  => 'datetime',
        'archived_at' => 'datetime',
    ];

    protected $fillable = [
        'created_at',
        'account_id',
        'visitor_id',
        'family_id',
        'created_by_user_id',
        'type',
        'notes',
        'is_contact',
        'is_contact_attempt',
        'is_action',
        'is_info',
        'is_request_for_action',
    ];

    public static $history_types = [
        'is_contact'            => 'Made Contact',
        'is_contact_attempt'    => 'Attempted Contact',
        'is_action'             => 'Performed an Action',
        'is_info'               => 'Just a Note',
        'is_request_for_action' => 'Request for Action',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function visitor()
    {
        return $this->belongsTo(Visitor::class);
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function createdBy()
    {
        return $this->hasOne(User::class, 'id', 'created_by_user_id');
    }

    public function getTypeName()
    {
        return self::$history_types[$this->type];
    }

    public function scopeActionsOnly($query)
    {
        return $query->where(function ($query) {
            $query->where('is_contact', true)
                ->orWhere('is_action', true);
        });
    }
}
