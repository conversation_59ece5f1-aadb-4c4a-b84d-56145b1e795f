<?php

namespace App\Visitors;

use App\Accounts\Account;
use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Visitor extends Model
{
    use SoftDeletes;

    protected $table = 'visitors';

    protected $casts = [
        'created_at'          => 'datetime',
        'updated_at'          => 'datetime',
        'deleted_at'          => 'datetime',
        'archived_at'         => 'datetime',
        'reminder_at'         => 'datetime',
        'first_contact_at'    => 'datetime',
        'last_contact_at'     => 'datetime',
        'account_id'          => 'integer',
        'user_id'             => 'integer',
        'family_id'           => 'integer',
        'created_by_user_id'  => 'integer',
        'archived_by_user_id' => 'integer',
    ];

    protected $fillable = [
        'account_id',
        'user_id',
        'family_id',
        'created_by_user_id',
        'archived_by_user_id',
        'visitor_status_id',
        'background_info',
        'notes',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where('visitors.account_id', $user->account_id);
        });
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function history()
    {
        return $this->hasMany(History::class);
    }

    public function subscribers()
    {
        return $this->belongsToMany(User::class, 'visitor_subscribers')
            // Don't select ALL user attributes for our users list.  This takes up memory.
            ->select([
                DB::raw('`users`.`id`'),
                DB::raw('`users`.`account_id`'),
                DB::raw('`users`.`family_id`'),
                DB::raw('`users`.`first_name`'),
                DB::raw('`users`.`last_name`'),
            ])
            ->as('settings')
            ->withPivot([
                'receive_email_updates',
                'receive_mobile_notification_updates',
            ]);
    }

    public function family()
    {
        return $this->hasOne(User::class, 'id', 'family_id');
    }

    public function createdBy()
    {
        return $this->hasOne(User::class, 'id', 'created_by_user_id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function mainUser()
    {
        return $this->user ?: $this->family;
    }

    public function status()
    {
        return $this->hasOne(Status::class, 'id', 'visitor_status_id');
    }

    public function interactions()
    {
        return $this->hasMany(History::class);
    }

    public function scopeIsNotArchived($query)
    {
        return $query->whereNull('archived_at');
    }

    public function scopeWithStatusId($query, $status_id)
    {
        return $query->where('visitor_status_id', (int)$status_id);
    }

    public function scopeArchivedOnly($query)
    {
        return $query->whereNotNull('archived_at');
    }

    public function isArchived()
    {
        return $this->archived_at ? true : false;
    }

    public function isFamily()
    {
        return $this->family_id ? true : false;
    }

    public function isUser()
    {
        return $this->user_id ? true : false;
    }

    public function userIsSubscribed($user)
    {
        return $this->subscribers()->where('user_id', $user->id)->exists();
    }
}
