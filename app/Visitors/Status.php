<?php

namespace App\Visitors;

use App\Users\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Status extends Model
{
    use SoftDeletes;

    protected $table = 'visitor_statuses';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'account_id',
        'name',
        'url_name',
        'tw_color',
        'color',
        'background_color',
        'border_color',
    ];

    public static $tw_color_options = [
        null,
        'gray',
        'green',
        'blue',
        'yellow',
        'orange',
        'purple',
        'red',
    ];

    public function scopeVisibleTo($query, User $user)
    {
        return $query->where(function ($query) use ($user) {
            $query->where($this->table . '.account_id', $user->account_id);
        });
    }

    public function visitors()
    {
        return $this->hasMany(Visitor::class, 'visitor_status_id', 'id');
    }

    public function getTextColor()
    {
        if (!$this->tw_color) {
            return 'black';
        } else {
            return $this->tw_color;
        }
    }

    public function hasColor()
    {
        return !$this->tw_color ? false : true;
    }
}
