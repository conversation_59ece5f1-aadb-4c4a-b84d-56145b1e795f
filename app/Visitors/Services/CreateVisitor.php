<?php

namespace App\Visitors\Services;

use App\Users\User;
use App\Visitors\Status;
use App\Visitors\Visitor;

class CreateVisitor
{
    protected $attributes = [];
    protected $visitor    = null;
    protected $user_id    = null;
    protected $family_id  = null;
    protected $created_by = null;

    public function create($attributes = [])
    {
        $this->attributes = array_merge($this->attributes, $attributes);

        // Don't create duplicate users, just go to their record.
//        if ($this->user_id && Visitor::visibleTo($this->created_by)->where('user_id', $this->user_id)->exists()) {
//            return Visitor::visibleTo($this->created_by)->where('user_id', $this->user_id)->first();
//        }
//        if ($this->family_id && Visitor::visibleTo($this->created_by)->where('family_id', $this->family_id)->exists()) {
//            return Visitor::visibleTo($this->created_by)->where('family_id', $this->family_id)->first();
//        }

        $this->attributes['user_id']            = $this->user_id;
        $this->attributes['family_id']          = $this->family_id;
        $this->attributes['created_by_user_id'] = $this->created_by->id;
        $this->attributes['account_id']         = $this->created_by->account_id;

        $this->attributes['visitor_status_id'] = Status::visibleTo(auth()->user())->orderBy('sort_id')->first()?->id;

        $this->visitor = Visitor::create($this->attributes);

        return $this->visitor;
    }

    public function forUserId($user)
    {
        $this->user_id = $user;

        return $this;
    }

    public function forFamilyId($user)
    {
        $this->family_id = $user;

        return $this;
    }

    public function createdBy(User $user)
    {
        $this->created_by = $user;

        return $this;
    }
}
