<?php

namespace App\Visitors\Services;

use App\Users\User;
use App\Visitors\Visitor;

class ArchiveVisitor
{
    protected $visitor;
    protected $archived_by_user_id;

    public function __construct(Visitor $visitor)
    {
        $this->visitor = $visitor;
    }

    public function archive()
    {
        $this->visitor->archived_at = now();
        $this->visitor->save();

        return $this->visitor;
    }

    public function byUser(User $user)
    {
        $this->archived_by_user_id = $user->id;

        return $this;
    }
}
