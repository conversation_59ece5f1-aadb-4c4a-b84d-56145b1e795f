<?php

namespace App\Auth\Controllers;

use App\Accounts\Account;
use App\Auth\Requests\LoginUserRequest;
use App\Base\Http\Controllers\Controller;
use App\Users\Activity;
use App\Users\Device;
use App\Users\Email;
use App\Users\Phone;
use App\Users\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LoginController extends Controller
{
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function index()
    {
        return view('auth.login')
            ->with('account', request()->has('id') ? Account::where('static_short_name', request()->get('id'))->first() : null);
    }

    public function loginAs($user_id)
    {
        if (Auth::user()->id === 1 && Auth::user()->isSuper()) {
            Auth::loginUsingId($user_id);

            return redirect('/');
        }

        return abort(403);
    }

    public function login(LoginUserRequest $request)
    {
        Log::info('Login attempt.');

        if ($this->attemptLogin($request)) {
            $request->session()->regenerate();

            // Turn off Kiosk Mode if enabled.
            session()->forget('kiosk_mode');

            // Log our User Activity
            Activity::create([
                'account_id' => auth()->user()->account_id,
                'user_id'    => auth()->user()->id,
                'site'       => Activity::getSiteFromString(request()->root()),
                'method'     => strtoupper(request()->method()),
                'path'       => request()->path(),
            ]);

            // If this is an AJAX / Turbolinks call.
            if ($request->ajax()) {
                Log::info('Login with AJAX attempt and success.', [
                    'path'    => request()->path(),
                    'user_id' => Auth::user()->id,
                ]);

                // See note below in the logout method.
                if (stristr($request->userAgent(), 'Lightpost-iOS') !== false) {
                    session()->put('ios-login-success', 'true');
                }

                $user             = Auth::user();
                $user->last_login = Carbon::now();
                $user->save();

                return response()->json('OK', 200);
            } else {
                Log::info('Login attempt and success.', [
                    'path'    => request()->path(),
                    'user_id' => Auth::user()->id,
                ]);

                $user             = Auth::user();
                $user->last_login = Carbon::now();
                $user->save();

                return redirect('/');
            }
        }

        // Check for inactive account.
        if ($this->checkAccountIsInactive($request)) {
            $errors = [
                'email' => 'Oops! This congregation account is inactive.',
            ];

            if ($request->expectsJson()) {
                return response()->json($errors, 422);
            }

            Log::error('Login request to an account marked as inactive: ' . $request->get('user_name'));

            return redirect()
                ->back()
                ->withInput($request->only('email', 'remember'))
                ->withErrors($errors);
        }

        $errors = [
            'email' => trans('auth.failed'),
        ];

        if ($request->expectsJson()) {
            Log::info('Login request expected JSON, returned 422.', [
                'email' => request('user_name'),
            ]);
            Log::info($errors);

            return response()->json($errors, 422);
        }

        Log::info('Login request completed but with errors.');

        return redirect()
            ->back()
            ->withInput($request->only('email', 'remember'))
            ->withErrors($errors);
    }

    protected function attemptLogin(Request $request)
    {
        $user = User::where('user_name', $request->get('user_name'))
            ->whereNotNull('user_name')
            ->where('user_name', '<>', '')
            ->first();

        if ($user && $user->account->is_active && $user->isMember()) {
            return Auth::guard()->attempt(
                $request->only('user_name', 'password'),
                $request->has('remember')
            );
        }

        $user_email = Email::where('email', 'like', $request->get('user_name'))
            ->whereNotNull('email')
            ->where('email', '<>', '')
            ->first();

        if ($user_email && $user_email->user->account->is_active && $user_email->user->isMember()) {
            return Auth::attempt([
                'id'       => $user_email->user_id,
                'password' => $request->get('password'),
            ]);
        }

        $user_phone = Phone::where('number', 'like', preg_replace('/[^0-9]/', '', $request->get('user_name')))
            ->whereNotNull('number')
            ->where('number', '<>', '')
            ->first();

        if ($user_phone && $user_phone->user->account->is_active && $user_phone->user->isMember()) {
            return Auth::attempt([
                'id'       => $user_phone->user_id,
                'password' => $request->get('password'),
            ]);
        }

        return false;
    }

    protected function checkAccountIsInactive(Request $request)
    {
        $user = User::where('user_name', $request->get('user_name'))
            ->whereNotNull('user_name')
            ->where('user_name', '<>', '')
            ->first();

        if ($user
            && $user->account?->is_active == 0
            && Auth::guard()->attempt($request->only('user_name', 'password'), $request->has('remember'))
        ) {
            return true;
        }

        $user_email = Email::where('email', 'like', $request->get('user_name'))
            ->whereNotNull('email')
            ->where('email', '<>', '')
            ->first();

        if ($user_email
            && $user_email->user
            && $user_email->user->account?->is_active == 0
            && Auth::attempt(['id' => $user_email->user_id, 'password' => $request->get('password')])
        ) {
            return true;
        }

        $user_phone = Phone::where('number', 'like', preg_replace('/[^0-9]/', '', $request->get('user_name')))
            ->whereNotNull('number')
            ->where('number', '<>', '')
            ->first();

        if ($user_phone && $user_phone->user
            && $user_phone->user->account?->is_active == 0
            && Auth::attempt(['id' => $user_phone->user_id, 'password' => $request->get('password')])
        ) {
            return true;
        }

        return false;
    }

    protected function username()
    {
        return 'user_name';
    }

    public function logout(Request $request)
    {
        Log::info('Logout request. ' . request()->path(), [
            'user_id' => optional(Auth::user())->id,
            'name'    => optional(Auth::user())->first_name . ' ' . optional(Auth::user())->last_name,
        ]);

        Activity::create([
            'account_id' => auth()->user()->account_id,
            'user_id'    => auth()->user()->id,
            'site'       => Activity::getSiteFromString(request()->root()),
            'method'     => strtoupper(request()->method()),
            'path'       => request()->path(),
        ]);

        /**
         * This is a "fix" for how Turbolinks works on iOS.
         * After a login, it will refresh the last page it was on... which was the /logout URL... and immediately log the person out again.
         */
        if (!$request->isMethod('DELETE') && stristr($request->userAgent(), 'Lightpost-iOS') !== false && session()->get('ios-login-success') == 'true') {
            session()->remove('ios-login-success');
            Log::info('iOS logout request after a successful login.');
            return redirect('/');
        }

        Auth::guard()->logout();

        if ($request->has('device_token')) {
            $device = Device::where('device_token', 'LIKE', $request->get('device_token'))->first();
            if ($device) {
                $device->delete();
            }
        }

        $request->session()->flush();

        $request->session()->regenerate();

        if ($request->isMethod('DELETE') && $request->ajax()) {
            Log::info('Logout request from iOS being processed.');
            return response()->json('OK', 200);
        }

        Log::info('Logout request from non-iOS processed.');

        return redirect('/');
    }
}
