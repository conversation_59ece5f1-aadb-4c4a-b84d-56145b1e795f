<?php

namespace App\Auth\Controllers;

use App\Base\Http\Controllers\Controller;
use App\Mail\App\ResetPassword;
use App\Users\Email;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ForgotPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset emails and
    | includes a trait which assists in sending these notifications from
    | your application to your users. Feel free to explore this trait.
    |
    */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        $this->middleware('guest');
    }

    public function sendResetLinkEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator);
        }

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.
//        $response = $this->broker()->sendResetLink(
//            $this->credentials($request)
//        );

        $email = trim(request('email'));

        if (!Email::where('email', $email)->exists()) {
//            Log::warning('Password reset attempt, email not found for: ' . $email);
//            abort(404);
            return back()->with('message.failure', 'Oops! That email address was not found. Please make sure you typed the full address correctly.');
        }

        Log::info('ForgotPasswordController::sendResetLinkEmail -- Attempting to email password reset email for email: ' . $email);

        $token = uniqid('', true);

        try {
            // We’re not doing this for now because we’re seeing a lot of people getting stuck on this error. Possible they’re requesting multiple resets, and then clicking on an older reset email.
//            DB::table('password_resets')->where('email', 'LIKE', $email)->delete();

            DB::table('password_resets')->insert([
                'email'      => $email,
                'token'      => $token,
                'pin'        => rand(100000, 999999),
                'created_at' => date('Y-m-d H:i:s'),
            ]);

//            Mail::to($email)->send(new ResetPassword($email, $token));
            Mail::to($email)->queue((new ResetPassword($email, $token))->onQueue('emails'));
        } catch (\Exception $e) {
            Log::error($e);
        }

        return back()->with('message.success', 'A reset link has been emailed to you.');
//        return response()->json('Email sent.', 200);
//        return $$this->sendResetLinkResponse($request);
//        return $response == Password::RESET_LINK_SENT
//            ? $this->sendResetLinkResponse($request, $response)
//            : $this->sendResetLinkFailedResponse($request, $response);
    }
}
