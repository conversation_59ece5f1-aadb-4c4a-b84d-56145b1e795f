<?php

namespace App\Auth\Controllers;

use App\Auth\Requests\RegisterUserRequest;
use App\Base\Http\Controllers\Controller;
use App\Users\Services\CreateUser;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;

class RegisterController extends Controller
{
    public function __construct()
    {
        $this->middleware('guest');
    }

    public function index()
    {
        abort(404);

        return view('auth.register');
    }

    public function register(RegisterUserRequest $request, CreateUser $createUser)
    {
        abort(404);

        $user = $createUser
            ->setName($request->get('user_name'))
            ->setEmail($request->get('email'))
            ->setPassword($request->get('password'))
            ->create();

        event(new Registered($user));

        Auth::guard()->login($user);

        return redirect('');
    }
}
