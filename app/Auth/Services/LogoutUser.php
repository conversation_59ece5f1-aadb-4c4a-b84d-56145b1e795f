<?php

namespace App\Auth\Services;

use App\Users\Activity;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class LogoutUser
{
    protected $user = null;

    public function logout()
    {
        if ($this->user) {
            Log::info('Logout request. ' . request()->path(), [
                'user_id' => $this->user?->id,
                'name'    => $this->user?->first_name . ' ' . $this->user?->last_name,
            ]);

            Activity::create([
                'account_id' => $this->user?->account_id,
                'user_id'    => $this->user?->id,
                'site'       => Activity::getSiteFromString(request()->root()),
                'method'     => strtoupper(request()->method()),
                'path'       => request()->path(),
            ]);
        }

        try {
            Auth::guard()->logout();

            request()->session()->flush();

            request()->session()->regenerate();
        } catch (\Exception $e) {

        }

        return true;
    }

    public function forUser($user)
    {
        $this->user = $user;

        return $this;
    }

}
