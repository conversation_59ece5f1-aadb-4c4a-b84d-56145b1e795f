<?php

namespace App\Base\Http;

use App\Base\Http\Middleware\CheckForAdminAccess;
use App\Base\Http\Middleware\CheckKioskMode;
use App\Base\Http\Middleware\EnableKioskMode;
use App\Base\Http\Middleware\Livewire\Authenticate;
use App\Base\Http\Middleware\LogApiRequest;
use App\Base\Http\Middleware\LogAppRequest;
use App\Base\Http\Middleware\LogMobileApiRequest;
use App\Base\Http\Middleware\LogUserActivity;
use App\Base\Http\Middleware\UpdateLastActive;
use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \App\Base\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Base\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Base\Http\Middleware\TrustProxies::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Base\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Base\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            UpdateLastActive::class,
            LogUserActivity::class,
            \App\Base\Http\Middleware\CheckForActiveAccount::class,
        ],

        'api' => [
            'throttle:30,1',
            //            'bindings',
            'auth:wordpress-guard',
            LogApiRequest::class,
            LogUserActivity::class,
        ],

        'mobile-api' => [
            'throttle:mobile-api-per-user',
            'bindings',
            LogMobileApiRequest::class,
            LogUserActivity::class,
            \App\Base\Http\Middleware\CheckForActiveAccount::class,
            \App\Base\Http\Middleware\CheckForMemberAccount::class,
        ],

        'app' => [
            LogAppRequest::class,
            LogUserActivity::class,
            \App\Base\Http\Middleware\CheckForActiveAccount::class,
            \App\Base\Http\Middleware\CheckForMemberAccount::class,
        ],

        'kiosk_check' => [
            CheckKioskMode::class,
        ],

        'kiosk_enable' => [
            EnableKioskMode::class,
        ],

        'admin' => [
            CheckForAdminAccess::class,
            \App\Base\Http\Middleware\CheckForActiveAccount::class,
            \App\Base\Http\Middleware\CheckForMemberAccount::class,
        ],

        'livewire' => [
            \App\Base\Http\Middleware\EncryptCookies::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Base\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            Authenticate::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $middlewareAliases = [
        'auth'          => \App\Base\Http\Middleware\Authenticate::class,
        'auth.basic'    => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings'      => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can'           => \Illuminate\Auth\Middleware\Authorize::class,
        'guest'         => \App\Base\Http\Middleware\RedirectIfAuthenticated::class,
        'signed'        => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle'      => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified'      => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
    ];
}
