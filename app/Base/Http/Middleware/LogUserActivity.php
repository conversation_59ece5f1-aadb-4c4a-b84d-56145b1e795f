<?php

namespace App\Base\Http\Middleware;

use App\Users\Activity;
use Closure;

class LogUserActivity
{
    public function handle($request, Closure $next)
    {
        return $next($request);
    }

    public function terminate($request, $response)
    {
        if (config('app.env') == 'production'
            && auth()->user()
            && auth()->user()?->account_id) {
            // Filter out some very common API calls that happen with every Dashboard refresh.
            if (!in_array(request()->path(), [
                'broadcasting/auth',
                'v2/bible-classes/available',
                'v2/worship-assignments/current',
                'v2/home/<USER>',
                'v2/home/<USER>',
                'v2/home/<USER>',
                'v2/crises/active',
            ])) {
                Activity::create([
                    'account_id' => auth()->user()->account_id,
                    'user_id'    => auth()->user()->id,
                    'site'       => Activity::getSiteFromString(request()->root()),
                    'method'     => strtoupper(request()->method()),
                    'path'       => request()->path(),
                ]);
            }
        }
    }
}
