<?php

namespace App\Base\Http\Middleware;

use App\Users\User;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Facades\Auth;

class UpdateLastActive
{
    // Keep updating our "last login" time, not just by an actual login, but by app usage.
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        // Perform action
        if (Auth::check() && Auth::user() instanceof User) {
            if (Auth::user()->last_active < Carbon::now()->sub('10 minutes')) {
                Auth::user()->last_active = Carbon::now();
                Auth::user()->save();
            }
        }

        return $response;
    }
}