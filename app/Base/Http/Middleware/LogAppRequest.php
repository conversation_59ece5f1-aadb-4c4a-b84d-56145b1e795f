<?php

namespace App\Base\Http\Middleware;

use Closure;

class LogAppRequest
{
    public function handle($request, Closure $next)
    {
        return $next($request);
    }

    // Keep updating our "last login" time, not just by an actual login, but by app usage.
    public function terminate($request, $response)
    {
        if (config('app.env') == 'production') {
            // StatHat used to be here.
        }
    }
}
