<?php

namespace App\Base\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Support\Facades\Log;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return string
     */
    protected function redirectTo($request)
    {
        if ($request->ajax() || $request->wantsJson()) {
            Log::info('AJAX unauthenticated view attempt. ' . request()->fullUrl() . ' -- ' . $request->ip());
            return response('Unauthorized.', 401);
        } elseif ($request->fullUrlIs('*' . config('app.domains.mobile_api') . '*')) {
            Log::info('Mobile API unauthenticated request. ' . request()->fullUrl() . ' -- ' . $request->ip());
            abort(401);
        } elseif ($request->fullUrlIs('*' . config('app.domains.api') . '*')) {
            Log::info('API unauthenticated request. ' . request()->fullUrl() . ' -- ' . $request->ip());
            abort(401);
        } else {
            Log::info('Unauthenticated view attempt. ' . request()->fullUrl() . ' -- ' . $request->ip());

            return route('login') . (request()->has('id') ? '?id=' . request()->get('id') : null);
        }
    }
}
