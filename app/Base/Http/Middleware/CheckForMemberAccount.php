<?php

namespace App\Base\Http\Middleware;

use App\Auth\Services\LogoutUser;
use Closure;
use Illuminate\Support\Facades\Log;

class CheckForMemberAccount
{
    public function handle($request, Closure $next)
    {
        /**
         * We logout the user and send them to the login page if the account is inactive.
         */
        if (auth()->check() && auth()->user()) {
            if (!auth()->user()?->isMember()) {
                if (request()->wantsJson()) {
                    (new LogoutUser())->forUser(auth()->user())->logout();

                    Log::info('Forced mobile logout for non-member account.', [
                        'path'    => request()->path(),
                        'user_id' => auth()->user()?->id,
                        'name'    => auth()->user()?->first_name . ' ' . auth()->user()?->last_name,
                    ]);

                    // A 401 will force the mobile app to kill their session.  A 403 does not (was previously a 403).
                    return abort(401);
                } else {
                    (new LogoutUser())->forUser(auth()->user())->logout();

                    Log::info('Forced logout for non-member account.', [
                        'path'    => request()->path(),
                        'user_id' => auth()->user()?->id,
                        'name'    => auth()->user()?->first_name . ' ' . auth()->user()?->last_name,
                    ]);

                    return redirect()->route('login')
                        ->with('message.failure', 'Oops! This Lightpost account is not a member for a congregation.');
                }
            } else {
                // Add the user ID to all logs so we know which user is encountering an issue.
                Log::withContext([
                    'user-id'    => auth()->user()?->id,
                    'account-id' => auth()->user()?->account_id,
                ]);
            }
        }

        return $next($request);
    }
}
