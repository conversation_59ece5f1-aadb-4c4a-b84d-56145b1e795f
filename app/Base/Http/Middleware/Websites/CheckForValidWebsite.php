<?php

namespace App\Base\Http\Middleware\Websites;

use App\Accounts\Account;
use Closure;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;

class CheckForValidWebsite
{
    public function handle($request, Closure $next)
    {
        /**
         * We return not found if we do not have a matching domain for the request.
         */

        $domain      = null;
        $domain_full = null;

        if (config('app.env') == 'production' &&
            (!request()->header('WebsitePageHost') || request()->header('LightpostCaddyServer') !== 'yes')
        ) {
            // For later...
            // return redirect('https://' . config('app.domains.frontend'));

            return abort(404, 'Invalid header information.');
        } # In production, get our domain from the header.
        elseif (config('app.env') == 'production') {
            $domain      = Str::after(request()->header('WebsitePageHost'), 'www.');
            $domain_full = request()->header('WebsitePageHost');
        } # In local, we set our own domain.
        elseif (config('app.env') == 'local' && !request()->header('WebsitePageHost')) {
            $domain      = 'katychurch.test';
            $domain_full = 'katychurch.test';
        }

        // March 2025
        // We stopped looking up the account->domain value - we only look at the account_domains table

        // Look up our domains table instead.
        $domain_record = DB::table('account_domains')
            ->where('domain', 'LIKE', $domain_full)
            ->where('is_active', 1)
            ->first();

        if (!$domain_record) {
            return abort(404, 'No account domain found for: ' . $domain_full);
        }

        if ($domain_record->is_forwarder) {
            // Only do forwarding if we're already using HTTPS. Otherwise the Caddy server might need to aquire one first.
            if ($request->secure()) {
                $url = filter_var($domain_record->forward_url, FILTER_VALIDATE_URL);

                if (!$url) {
                    return abort(500, 'Invalid forward URL configuration: ' . $domain_record->forward_url);
                }

                return redirect($url, 301);
            }
        }

        if ($domain_record->account_id) {
            $account = Account::find($domain_record->account_id);
        }

        // If still no account.
        if (!$account) {
            return abort(404, 'No account found for this domain.');
        }

        // If the account is not active or does not have the feature enabled, we return not found.
        if (!$account->is_active) {
            return abort(404, 'Account is inactive.');
        }
        if (!$account->hasFeature('feature.website')) {
            return abort(404, 'Account website feature disabled.');
        }

        View::share('account', $account);

        $request->merge(['account_id' => $account->id]);

        return $next($request);
    }
}
