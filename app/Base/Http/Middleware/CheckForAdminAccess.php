<?php

namespace App\Base\Http\Middleware;

use Closure;
use Illuminate\Support\Str;

class CheckForAdminAccess
{
    public function handle($request, Closure $next)
    {
        /**
         * We redirect back to the login screen if we don't have ANY admin permissions.
         *
         * Check if we:
         *  1. Are on the admin site.
         *  2. Are logged in.
         *  3. Have any admin permissions or not.
         */

        if (Str::contains(request()->url(), config('app.domains.admin')) &&
            auth()->user() &&
            auth()->user()->permissionsQuery()
                ->where('user_role_permissions.key', 'NOT LIKE', 'app.%')
                ->count() == 0) {
            return redirect()->route('login')
                ->with('message.failure', 'Oops! This user does not have any admin permissions.');
        }

        return $next($request);
    }
}
