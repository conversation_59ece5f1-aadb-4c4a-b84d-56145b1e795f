<?php

namespace App\Base\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Paginator::useBootstrap();

        Builder::macro('addSubSelect', function ($column, $query) {
            if (is_null($this->getQuery()->columns)) {
                $this->select($this->getQuery()->from . '.*');
            }

            return $this->selectSub($query->limit(1)->getQuery(), $column);
        });

        // Custom blade directive to allow in a checkbox: @isChecked($email->is_primary)
        Blade::directive('isChecked', function ($expression) {
            return "<?= $expression ? 'checked=\"checked\"' : null; ?>";
        });

        RateLimiter::for('public_form', function (Request $request) {
            return Limit::perMinute(2)
                ->by($request->ip())
                ->response(function (Request $request, array $headers) {
                    return response('Thank you!', 429, $headers);
                });
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
