<?php

namespace App\Base\Providers;

use App\AccountFiles\AccountFile;
use App\AccountFiles\Policies\AccountFilePolicy;
use App\Accounts\Account;
use App\Accounts\ChurchOffice;
use App\Accounts\Grade;
use App\Accounts\Policies\AccountPolicy;
use App\Accounts\Policies\ChurchOfficePolicy;
use App\Accounts\Policies\GradePolicy;
use App\Attendance\Attendance;
use App\Attendance\AttendanceCard;
use App\Attendance\Policies\AttendanceCardPolicy;
use App\Attendance\Policies\AttendancePolicy;
use App\BibleClasses\BibleClass;
use App\BibleClasses\BibleClassGroup;
use App\BibleClasses\Policies\BibleClassGroupPolicy;
use App\BibleClasses\Policies\BibleClassPolicy;
use App\Calendars\Calendar;
use App\Calendars\Policies\CalendarPolicy;
use App\ChildCheckins\ChildCheckin;
use App\ChildCheckins\Policies\ChildCheckinPolicy;
use App\Finance\Policies\FinancePolicy;
use App\Finance\Transaction;
use App\Groups\Policies\PostPolicy;
use App\Groups\Post;
use App\Involvement\Category;
use App\Involvement\Policies\InvolvementPolicy;
use App\Messages\Message;
use App\Messages\Policies\MessagePolicy;
use App\Podcasts\Podcast;
use App\Podcasts\Policies\PodcastPolicy;
use App\Prayers\Policies\PrayerPolicy;
use App\Prayers\Prayer;
use App\Programs\Policies\ProgramGroupPolicy;
use App\Programs\Policies\ProgramPolicy;
use App\Programs\Program;
use App\Programs\ProgramGroup;
use App\Sermons\Policies\SermonPolicy;
use App\Sermons\Sermon;
use App\Users\Address;
use App\Users\Email;
use App\Users\Group;
use App\Users\PaymentSchedule;
use App\Users\Phone;
use App\Users\Photo;
use App\Users\Policies\AddressPolicy;
use App\Users\Policies\EmailPolicy;
use App\Users\Policies\GroupPolicy;
use App\Users\Policies\PaymentSchedulePolicy;
use App\Users\Policies\PhonePolicy;
use App\Users\Policies\PhotoPolicy;
use App\Users\Policies\RolePolicy;
use App\Users\Policies\UserPolicy;
use App\Users\Role;
use App\Users\User;
use App\Visitors\Policies\VisitorPolicy;
use App\Visitors\Visitor;
use App\Website\Policies\WebsitePolicy;
use App\Website\Website;
use App\WorshipAssignments\Group as WorshipAssignmentGroup;
use App\WorshipAssignments\Period;
use App\WorshipAssignments\Policies\GroupPolicy as WorshipAssignmentGroupPolicy;
use App\WorshipAssignments\Policies\PeriodPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        User::class                   => UserPolicy::class,
        Photo::class                  => PhotoPolicy::class,
        Visitor::class                => VisitorPolicy::class,
        Role::class                   => RolePolicy::class,
        Grade::class                  => GradePolicy::class,
        Message::class                => MessagePolicy::class,
        Address::class                => AddressPolicy::class,
        Phone::class                  => PhonePolicy::class,
        Email::class                  => EmailPolicy::class,
        Attendance::class             => AttendancePolicy::class,
        Group::class                  => GroupPolicy::class,
        BibleClass::class             => BibleClassPolicy::class,
        BibleClassGroup::class        => BibleClassGroupPolicy::class,
        Sermon::class                 => SermonPolicy::class,
        Podcast::class                => PodcastPolicy::class,
        AccountFile::class            => AccountFilePolicy::class,
        Category::class               => InvolvementPolicy::class,
        Account::class                => AccountPolicy::class,
        Calendar::class               => CalendarPolicy::class,
        Prayer::class                 => PrayerPolicy::class,
        WorshipAssignmentGroup::class => WorshipAssignmentGroupPolicy::class,
        Period::class                 => PeriodPolicy::class,
        Post::class                   => PostPolicy::class,
        ChildCheckin::class           => ChildCheckinPolicy::class,
        Transaction::class            => FinancePolicy::class,
        PaymentSchedule::class        => PaymentSchedulePolicy::class,
        ChurchOffice::class           => ChurchOfficePolicy::class,
        AttendanceCard::class         => AttendanceCardPolicy::class,
        Website::class                => WebsitePolicy::class,
        Program::class                => ProgramPolicy::class,
        ProgramGroup::class           => ProgramGroupPolicy::class,
    ];

    public function boot()
    {
        // We can't remove this yet because it's not working for all permissions for some reason.
        $this->registerPolicies();// -- Now automatic in Laravel 10.0

        Gate::define('index-reports', 'App\Reports\Policies\ReportPolicy@index');
        Gate::define('view-reports', 'App\Reports\Policies\ReportPolicy@view');

        Auth::viaRequest('wordpress-token', function ($request) {
            return Account::where('api_token', ($request->token ?: $request->header('Authorization')))->first();
        });
        // Passport::routes(); -- Now automatic in Laravel Passport 11.0
    }
}
