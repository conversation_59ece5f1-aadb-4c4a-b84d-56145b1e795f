<?php namespace App\Base\Libraries;

/**
 * DateTime Library
 *
 * Custom date/time library to deal with timezones - accounts for DST using PHP's DateTime class.
 * 
 * @category	Libraries
 * <AUTHOR> <<EMAIL>>
 * @link		http://drew.johnston.us
 */

class DAT {
	/**
	 * Default Timezone in case we can't find/understand what the user's timezone is.
	 * @access private static
	 * @var string
	 */
	public static $_default_timezone = 'America/Chicago';
	/**
	 * An array of all acceptable timezone values.
	 * @access private static
	 * @var array
	 */
	public static $_timezones = array('UTC','Africa/Abidjan','Africa/Accra','Africa/Addis_Ababa','Africa/Algiers','Africa/Asmara','Africa/Asmera','Africa/Bamako','Africa/Bangui','Africa/Banjul','Africa/Bissau','Africa/Blantyre','Africa/Brazzaville','Africa/Bujumbura','Africa/Cairo','Africa/Casablanca','Africa/Ceuta','Africa/Conakry','Africa/Dakar','Africa/Dar_es_Salaam','Africa/Djibouti','Africa/Douala','Africa/El_Aaiun','Africa/Freetown','Africa/Gaborone','Africa/Harare','Africa/Johannesburg','Africa/Juba','Africa/Kampala','Africa/Khartoum','Africa/Kigali','Africa/Kinshasa','Africa/Lagos','Africa/Libreville','Africa/Lome','Africa/Luanda','Africa/Lubumbashi','Africa/Lusaka','Africa/Malabo','Africa/Maputo','Africa/Maseru','Africa/Mbabane','Africa/Mogadishu','Africa/Monrovia','Africa/Nairobi','Africa/Ndjamena','Africa/Niamey','Africa/Nouakchott','Africa/Ouagadougou','Africa/Porto-Novo','Africa/Sao_Tome','Africa/Timbuktu','Africa/Tripoli','Africa/Tunis','Africa/Windhoek','America/Adak','America/Anchorage','America/Anguilla','America/Antigua','America/Araguaina','America/Argentina/Buenos_Aires','America/Argentina/Catamarca','America/Argentina/ComodRivadavia','America/Argentina/Cordoba','America/Argentina/Jujuy','America/Argentina/La_Rioja','America/Argentina/Mendoza','America/Argentina/Rio_Gallegos','America/Argentina/Salta','America/Argentina/San_Juan','America/Argentina/San_Luis','America/Argentina/Tucuman','America/Argentina/Ushuaia','America/Aruba','America/Asuncion','America/Atikokan','America/Atka','America/Bahia','America/Bahia_Banderas','America/Barbados','America/Belem','America/Belize','America/Blanc-Sablon','America/Boa_Vista','America/Bogota','America/Boise','America/Buenos_Aires','America/Cambridge_Bay','America/Campo_Grande','America/Cancun','America/Caracas','America/Catamarca','America/Cayenne','America/Cayman','America/Chicago','America/Chihuahua','America/Coral_Harbour','America/Cordoba','America/Costa_Rica','America/Cuiaba','America/Curacao','America/Danmarkshavn','America/Dawson','America/Dawson_Creek','America/Denver','America/Detroit','America/Dominica','America/Edmonton','America/Eirunepe','America/El_Salvador','America/Ensenada','America/Fort_Wayne','America/Fortaleza','America/Glace_Bay','America/Godthab','America/Goose_Bay','America/Grand_Turk','America/Grenada','America/Guadeloupe','America/Guatemala','America/Guayaquil','America/Guyana','America/Halifax','America/Havana','America/Hermosillo','America/Indiana/Indianapolis','America/Indiana/Knox','America/Indiana/Marengo','America/Indiana/Petersburg','America/Indiana/Tell_City','America/Indiana/Vevay','America/Indiana/Vincennes','America/Indiana/Winamac','America/Indianapolis','America/Inuvik','America/Iqaluit','America/Jamaica','America/Jujuy','America/Juneau','America/Kentucky/Louisville','America/Kentucky/Monticello','America/Knox_IN','America/Kralendijk','America/La_Paz','America/Lima','America/Los_Angeles','America/Louisville','America/Lower_Princes','America/Maceio','America/Managua','America/Manaus','America/Marigot','America/Martinique','America/Matamoros','America/Mazatlan','America/Mendoza','America/Menominee','America/Merida','America/Metlakatla','America/Mexico_City','America/Miquelon','America/Moncton','America/Monterrey','America/Montevideo','America/Montreal','America/Montserrat','America/Nassau','America/New_York','America/Nipigon','America/Nome','America/Noronha','America/North_Dakota/Beulah','America/North_Dakota/Center','America/North_Dakota/New_Salem','America/Ojinaga','America/Panama','America/Pangnirtung','America/Paramaribo','America/Phoenix','America/Port-au-Prince','America/Port_of_Spain','America/Porto_Acre','America/Porto_Velho','America/Puerto_Rico','America/Rainy_River','America/Rankin_Inlet','America/Recife','America/Regina','America/Resolute','America/Rio_Branco','America/Rosario','America/Santa_Isabel','America/Santarem','America/Santiago','America/Santo_Domingo','America/Sao_Paulo','America/Scoresbysund','America/Shiprock','America/Sitka','America/St_Barthelemy','America/St_Johns','America/St_Kitts','America/St_Lucia','America/St_Thomas','America/St_Vincent','America/Swift_Current','America/Tegucigalpa','America/Thule','America/Thunder_Bay','America/Tijuana','America/Toronto','America/Tortola','America/Vancouver','America/Virgin','America/Whitehorse','America/Winnipeg','America/Yakutat','America/Yellowknife','Antarctica/Casey','Antarctica/Davis','Antarctica/DumontDUrville','Antarctica/Macquarie','Antarctica/Mawson','Antarctica/McMurdo','Antarctica/Palmer','Antarctica/Rothera','Antarctica/South_Pole','Antarctica/Syowa','Antarctica/Vostok','Arctic/Longyearbyen','Asia/Aden','Asia/Almaty','Asia/Amman','Asia/Anadyr','Asia/Aqtau','Asia/Aqtobe','Asia/Ashgabat','Asia/Ashkhabad','Asia/Baghdad','Asia/Bahrain','Asia/Baku','Asia/Bangkok','Asia/Beirut','Asia/Bishkek','Asia/Brunei','Asia/Calcutta','Asia/Choibalsan','Asia/Chongqing','Asia/Chungking','Asia/Colombo','Asia/Dacca','Asia/Damascus','Asia/Dhaka','Asia/Dili','Asia/Dubai','Asia/Dushanbe','Asia/Gaza','Asia/Harbin','Asia/Hebron','Asia/Ho_Chi_Minh','Asia/Hong_Kong','Asia/Hovd','Asia/Irkutsk','Asia/Istanbul','Asia/Jakarta','Asia/Jayapura','Asia/Jerusalem','Asia/Kabul','Asia/Kamchatka','Asia/Karachi','Asia/Kashgar','Asia/Kathmandu','Asia/Katmandu','Asia/Kolkata','Asia/Krasnoyarsk','Asia/Kuala_Lumpur','Asia/Kuching','Asia/Kuwait','Asia/Macao','Asia/Macau','Asia/Magadan','Asia/Makassar','Asia/Manila','Asia/Muscat','Asia/Nicosia','Asia/Novokuznetsk','Asia/Novosibirsk','Asia/Omsk','Asia/Oral','Asia/Phnom_Penh','Asia/Pontianak','Asia/Pyongyang','Asia/Qatar','Asia/Qyzylorda','Asia/Rangoon','Asia/Riyadh','Asia/Saigon','Asia/Sakhalin','Asia/Samarkand','Asia/Seoul','Asia/Shanghai','Asia/Singapore','Asia/Taipei','Asia/Tashkent','Asia/Tbilisi','Asia/Tehran','Asia/Tel_Aviv','Asia/Thimbu','Asia/Thimphu','Asia/Tokyo','Asia/Ujung_Pandang','Asia/Ulaanbaatar','Asia/Ulan_Bator','Asia/Urumqi','Asia/Vientiane','Asia/Vladivostok','Asia/Yakutsk','Asia/Yekaterinburg','Asia/Yerevan','Atlantic/Azores','Atlantic/Bermuda','Atlantic/Canary','Atlantic/Cape_Verde','Atlantic/Faeroe','Atlantic/Faroe','Atlantic/Jan_Mayen','Atlantic/Madeira','Atlantic/Reykjavik','Atlantic/South_Georgia','Atlantic/St_Helena','Atlantic/Stanley','Australia/ACT','Australia/Adelaide','Australia/Brisbane','Australia/Broken_Hill','Australia/Canberra','Australia/Currie','Australia/Darwin','Australia/Eucla','Australia/Hobart','Australia/LHI','Australia/Lindeman','Australia/Lord_Howe','Australia/Melbourne','Australia/North','Australia/NSW','Australia/Perth','Australia/Queensland','Australia/South','Australia/Sydney','Australia/Tasmania','Australia/Victoria','Australia/West','Australia/Yancowinna','Europe/Amsterdam','Europe/Andorra','Europe/Athens','Europe/Belfast','Europe/Belgrade','Europe/Berlin','Europe/Bratislava','Europe/Brussels','Europe/Bucharest','Europe/Budapest','Europe/Chisinau','Europe/Copenhagen','Europe/Dublin','Europe/Gibraltar','Europe/Guernsey','Europe/Helsinki','Europe/Isle_of_Man','Europe/Istanbul','Europe/Jersey','Europe/Kaliningrad','Europe/Kiev','Europe/Lisbon','Europe/Ljubljana','Europe/London','Europe/Luxembourg','Europe/Madrid','Europe/Malta','Europe/Mariehamn','Europe/Minsk','Europe/Monaco','Europe/Moscow','Europe/Nicosia','Europe/Oslo','Europe/Paris','Europe/Podgorica','Europe/Prague','Europe/Riga','Europe/Rome','Europe/Samara','Europe/San_Marino','Europe/Sarajevo','Europe/Simferopol','Europe/Skopje','Europe/Sofia','Europe/Stockholm','Europe/Tallinn','Europe/Tirane','Europe/Tiraspol','Europe/Uzhgorod','Europe/Vaduz','Europe/Vatican','Europe/Vienna','Europe/Vilnius','Europe/Volgograd','Europe/Warsaw','Europe/Zagreb','Europe/Zaporozhye','Europe/Zurich','Indian/Antananarivo','Indian/Chagos','Indian/Christmas','Indian/Cocos','Indian/Comoro','Indian/Kerguelen','Indian/Mahe','Indian/Maldives','Indian/Mauritius','Indian/Mayotte','Indian/Reunion','Pacific/Apia','Pacific/Auckland','Pacific/Chatham','Pacific/Chuuk','Pacific/Easter','Pacific/Efate','Pacific/Enderbury','Pacific/Fakaofo','Pacific/Fiji','Pacific/Funafuti','Pacific/Galapagos','Pacific/Gambier','Pacific/Guadalcanal','Pacific/Guam','Pacific/Honolulu','Pacific/Johnston','Pacific/Kiritimati','Pacific/Kosrae','Pacific/Kwajalein','Pacific/Majuro','Pacific/Marquesas','Pacific/Midway','Pacific/Nauru','Pacific/Niue','Pacific/Norfolk','Pacific/Noumea','Pacific/Pago_Pago','Pacific/Palau','Pacific/Pitcairn','Pacific/Pohnpei','Pacific/Ponape','Pacific/Port_Moresby','Pacific/Rarotonga','Pacific/Saipan','Pacific/Samoa','Pacific/Tahiti','Pacific/Tarawa','Pacific/Tongatapu','Pacific/Truk','Pacific/Wake','Pacific/Wallis','Pacific/Yap');

	/**
	 * Returns DaetTime object of current time in UTC/GMT
	 * @return	DateTime
	 */
	static public function now()
	{
		return new \DateTime(gmdate('Y-m-d H:i:s', self::_get_now()), new \DateTimeZone('UTC'));
	}
	
	/**
	 * Returns DateTime object of current time in user's timezone
	 * @param	string		Specific timezone
	 * @return	DateTime
	 */
	static public function user_now($timezone = NULL)
	{
		// If we have an empty timezone, check the users timezone
		if(empty($timezone))
		{
			$timezone = Auth::user()->timezone;
		}
		
		// If our timezone is STILL empty OR we have an invalid timezone, use the default
		if(empty($timezone) OR ! in_array($timezone, self::$_timezones))
			$timezone = self::$_default_timezone;
		
		// First we create a UTC DateTime object
		$return_date = new \DateTime(gmdate('Y-m-d H:i:s', self::_get_now()), new \DateTimeZone('UTC'));
		
		// Now apply the user's timezone
		$return_date->setTimeZone(new \DateTimeZone($timezone));
		
		// Return the DateTime object that has the date given (or current date) in the user's timezone (or default timezone)
		return $return_date;
	}
	
	/**
	 * Returns GMT time in MySQL format using
	 * @param	bool	$quotes option for adding quotes (for use in a MySQL statement); default is TRUE
	 * @return	string	datetime string in mysql format
	 */
	static public function mysql_now($quotes = false)
	{
		if($quotes)
			return gmdate('\'Y-m-d H:i:s\'');
		else
			return gmdate('Y-m-d H:i:s');
	}

	/**
	 * Returns the users time based on their timezone (ACCEPTS ONLY UTC BASED TIMES)
	 * @param	DateTime|string	$datetime	Datetime you would like to change to the user's timezone - Can be a DateTime object, datetime string or a timestamp
	 * @param	string			$timezone	Timezone you would like the time to be returned in (default is the user's session timezone)
	 * @return	DateTime		Returns DateTime object in the user's timezone (or default timezone)
	 */
	static public function user($datetime = NULL, $timezone = NULL)
	{
		// If we have a "null" date from a database, return a null value
		if($datetime == '0000-00-00' OR $datetime == '0000-00-00 00:00:00')
			return new self;
		
		// If we're given a DateTime object, use that
		if(is_object($datetime))
			$datetime = $datetime->format('U');
		elseif(is_string($datetime))
		{
			if(strlen($datetime) == 10)
				$datetime = \DateTime::createFromFormat('Y-m-d H:i:s', $datetime.' 12:00:00');
			else
				$datetime = \DateTime::createFromFormat('Y-m-d H:i:s', $datetime);
			
			$datetime = $datetime->format('U');
		}
		// If we have an empty time, do a now() (UTC TIME)
		elseif(empty($datetime)) 
			$datetime = self::_get_now();
		
		// If we have an empty timezone, check the users timezone
		if(empty($timezone))
		{
			// Auth::user()->timezone;
		}
		
		// If our timezone is STILL empty OR we have an invalid timezone, use the default
		if(empty($timezone) OR ! in_array($timezone, self::$_timezones))
			$timezone = self::$_default_timezone;
		
		// First we create a UTC DateTime object
		$return_date = new \DateTime(date('Y-m-d H:i:s', $datetime), new \DateTimeZone('UTC'));
		
		// Now apply the user's timezone
		$return_date->setTimeZone(new \DateTimeZone($timezone));
		
		// Return the DateTime object that has the date given (or current date) in the user's timezone (or default timezone)
		return $return_date;
	}
	
	
	/**
	 * Returns a datetime object of the given input (ACCEPTS ONLY UTC BASED TIMES)
	 * @param	DateTime|string	$datetime	Datetime you would like returned - Can be a DateTime object, datetime string or a timestamp
	 */
	static public function gmt($datetime = NULL)
	{
		// If we have a "null" date from a database, return a null value
		if($datetime == '0000-00-00' OR $datetime == '0000-00-00 00:00:00')
			return new self;
		
		// If we're given a DateTime object, use that
		if(is_object($datetime))
			$datetime = $datetime->format('U');
		elseif(is_string($datetime))
		{
			if(strlen($datetime) == 10)
				$datetime = \DateTime::createFromFormat('Y-m-d H:i:s', $datetime.' 12:00:00');
			else
				$datetime = \DateTime::createFromFormat('Y-m-d H:i:s', $datetime);
			
			$datetime = $datetime->format('U');
		}
		// If we have an empty time, do a now() (UTC TIME)
		elseif(empty($datetime)) 
			$datetime = self::_get_now();
		
		// First we create a UTC DateTime object
		$return_date = new \DateTime(date('Y-m-d H:i:s', $datetime));
		
		// Return the DateTime object that has the date given (or current date) in the user's timezone (or default timezone)
		return $return_date;
	}
	

	/**
	 * Converts and returns the given datetime UTC based on the user's timezone
	 * @param	DateTime|string	$datetime	The users datetime based on their timezone to turn into GMT time (Can be DateTime object, datetime string or timestamp)
	 * @param	string			$timezone	Timezone you would like the time to be returned in (default is the user's session timezone or default timezone)
	 * @return	DateTime		datetime 	Returns DateTime object in UTC relative to the user's timezone (or default timezone)
	 */ 
	static public function user_to_gmt($datetime, $timezone = NULL)
	{
		/// If we have a "null" date from a database, return a null value
		if($datetime == '0000-00-00' OR $datetime == '0000-00-00 00:00:00' OR $datetime == NULL)
			return new self;
		
		// FIRST - Get our timezone
			// If we have an empty timezone, check the users timezone
			if(empty($timezone))
			{
				Auth::user()->timezone;
			}
			// If our timezone is STILL empty OR we have an invalid timezone, use the default
			if(empty($timezone) OR ! in_array($timezone, self::$_timezones))
				$timezone = self::$_default_timezone;
		
		// Create a new DateTime object in the user's timezone time
		$return_date = new \DateTime(NULL, new \DateTimeZone($timezone));
		
		// Now set the time to the user's time given
		if(is_object($datetime))
			$return_date->setTimestamp($datetime->format('U'));
		elseif(is_numeric($datetime))
			$return_date->setTimestamp($datetime);
		else
			$return_date->modify($datetime);
		
		// Now apply the UTC timezone
		$return_date->setTimeZone(new \DateTimeZone('UTC'));
		
		// Return the DateTime object that has the date given in UTC timezone
		return $return_date;
	}


	/**
	 * Really just an alias of self::user()
	 * @param	DateTime|string		$user_datetime	the datetime in GMT format to be converted based on their timezone to users timezone
	 * @param	string				$user_timezone	the users timezone in the format for CI (ie: UM6)
	 * @return	DateTime			datetime 		Returns DateTime object in the user's timezone (or default timezone)
	 */ 
	static public function gmt_to_user($datetime, $user_timezone = NULL)
	{
		// If we have a "null" date from a database, return a null value
		if($datetime == '0000-00-00' OR $datetime == '0000-00-00 00:00:00' OR $datetime == NULL)
			return new self;
		
		// FIRST - Get our timezone
			// If we have an empty timezone, check the users timezone
			if(empty($timezone))
			{
				Auth::user()->timezone;
			}
			// If our timezone is STILL empty OR we have an invalid timezone, use the default
			if(empty($timezone) OR ! in_array($timezone, self::$_timezones))
				$timezone = self::$_default_timezone;
		
		// Create a new DateTime object in UTC time
		$return_date = new \DateTime(NULL, new \DateTimeZone('UTC'));
		
		// Now set the time to the user's time given
		if(is_object($datetime))
			$return_date->setTimestamp($datetime->format('U'));
		elseif(is_numeric($datetime))
			$return_date->setTimestamp($datetime);
		else
			$return_date->modify($datetime);
		
		// Now apply the user's timezone
		$return_date->setTimeZone(new \DateTimeZone($timezone));
		
		// Return the DateTime object that has the date given in UTC timezone
		return $return_date;
	}

	/**
	 * Return time passed, in years, given a date
	 * 
	 * @param	string	$birthdate		A date (typically in YYYY-MM-DD format, but can also be a timestamp)
	 * @param	string	$user_timezone	users timezone, if not specified, the session timezone will be used
	 * @return	string	integer			number of years between now() and given birthdate
	 */
	static public function get_age($birthdate, $user_timezone = NULL)
	{
		$datetime = self::now();
		$str = null;
		
		// TODO: Take into account what timezone the user is in.  LOW Priority
		
		// If we have an invalid birthdate, return null
		if($birthdate == '0000-00-00' OR $birthdate == '0000-00-00 00:00:00')
			return null;
		
		// If we were not given a unix timestamp, convert it
		if( ! is_numeric($birthdate))
			$birthdate = strtotime($birthdate);
		$time = self::timespan($birthdate, $datetime);
		return @$time['years'];
	}
	
	/**
	 * Return time passed, in years/months/days/hours, between 2 dates given
	 * 
	 * @param	string|datetime	$date1		A date (typically in YYYY-MM-DD format, but can also be a DateTime object)
	 * @param	string|datetime	$date2		Comparing date; leaving empty will use the current date/time
	 * @return	string			string		
	 */
	static public function get_diff($date1, $date2 = null)
	{
		return self::get_difference($date1, $date2);
	}
	static public function get_difference($date1, $date2 = null)
	{
		if( ! is_object($date1))
			$date1 = new \DateTime($date1);
		if( $date2 != null AND ! is_object($date2))
			$date2 = new \DateTime($date2);
		elseif($date2 === null)
			$date2 = new \DateTime();

		$interval	= $date1->diff($date2);
		$years		= $interval->format('%y');
		$months		= $interval->format('%m');
		$days		= $interval->format('%d');
		$hours		= $interval->format('%h');
		$minutes	= $interval->format('%i');
		
		
		if($years > 0)
		{
			$time = $years.' years ';
			if($months > 1)
				$time .= $months.' months ';
		}
		elseif($months > 0)
		{
			$time = $months.' months ';
			if($days > 5)
				$time .= $days.' days ';
		}
		elseif($days > 0)
			$time = $days.' days ';
		elseif($hours > 0)
			$time = $hours.' hours ';
		elseif($minutes > 0)
			$time = $minutes.' minutes ';
		else
			return 'just now';
		
		if($interval->invert)
			$time .= 'left';
		else
			$time .= 'ago';
		
		return $time;
	}
	
	static public function get_age_in_months($birthdate, $user_timezone = NULL)
	{
		if($birthdate == '0000-00-00' OR $birthdate == '0000-00-00 00:00:00' OR strstr($birthdate, '0001') !== false)
			return false;
		
		// Get our users current time
		$datetime = self::user();
		
		// TODO: Get this is the user timezone
		// Get our birthdate as a DateTime object
		$birth = new \DateTime($birthdate);
		
		$interval = $birth->diff($datetime);
		
		if($interval->y < 1)
			return $interval->m;
		elseif($interval->y > 25)
			return false;
		elseif($interval->y >= 1)
			return ($interval->y * 12) + $interval->m;
	}

	// Returns unix timestamp GMT/UTC
	static public function _get_now()
	{
		return gmdate('U');
	}
	
	/**
	 * Returns TRUE or FALSE depending on the date given
	 * @param	DateTime|string	$datetime	Datetime you would like to change to the user's timezone - Can be a DateTime object, datetime string or a timestamp
	 * @param	string			$timezone	Timezone you would like the time to be returned in (default is the user's session timezone)
	 * @return	DateTime		Returns DateTime object in the user's timezone (or default timezone)
	 */
	static public function is_valid($datetime = NULL)
	{
		// If we have a "null" date from a database, return a null value
		if($datetime == '0000-00-00' OR $datetime == '0000-00-00 00:00:00' OR $datetime == '' OR $datetime === null)
			return false;
		else
			return true;
	}
		
	
	// Placeholder for an invalid datetime being given
	public function format($test = NULL)
	{
		return NULL;
	}
	
	static function format_24_to_12_hour($time)
	{
		$time = explode(':', $time);
		
		if($time[0] < 12)
			return trim($time[0], '0').':'.$time[1].'am';
		elseif($time[0] == 12)
			return trim($time[0], '0').':'.$time[1].'pm';
		else
			return trim(($time[0] - 12), '0').':'.$time[1].'pm';
	}
	
	// Return a DateTime object of the date the first full week of the month begins on.
		// $day is DAY OF WEEK; 1 - 7; Mon - Sun; ISO standard
	static public function get_first_full_week_of_month($year, $month, $day = 7)
	{
		// Make this a DateTime object; pad the month in case we only get a "1" instead of a "01"
		$date = new \DateTime($year.'-'.str_pad($month, 2, '0', STR_PAD_LEFT).'-01');
		
		if($date->format('N') == $day)
			return $date;
		else
			while($date->format('N') != $day)
				$date->add(new DateInterval('P01D'));
		
		return $date;
	}
	
	// Return a DateTime object of the date the first day of the week is
	static public function get_first_day_of_week($datetime = NULL)
	{
		$date = self::user($datetime);
		
		$day = $date->format('w');
		
		if($day == 0)
			return $date;
		else
		{
			$date->sub(new DateInterval('P0'.$date->format('w').'D'));
			return $date;
		}
	}
	
	// Produces a dropdown menu with all timezone options for user selection
	static public function timezone_menu($default = 'America/Chicago', $class = "", $name = 'timezone')
	{
		$menu = '<select name="'.$name.'"';

		if ($class != '')
		{
			$menu .= ' class="'.$class.'"';
		}

		$menu .= ">\n";

		foreach (self::$_timezones as $val)
		{
			$selected = ($default == $val ? ' selected="selected"' : '');
			$menu .= "<option value='{$val}'{$selected}>".$val."</option>\n";
		}

		$menu .= "</select>";

		return $menu;
	}
	
	// Return an associative array
	static public function timespan($seconds = 1, $time = '')
	{
		$return = array();
		
		if ( ! is_numeric($seconds))
		{
			$seconds = 1;
		}

		if ( ! is_numeric($time))
		{
			$time = time();
		}

		$seconds = ($time <= $seconds) ? 1 : $time - $seconds;

		$str = '';
		$years = floor($seconds / 31557600);

		if ($years > 0)
			$return['years'] = $years;
		
		$seconds -= $years * 31557600;
		$months = floor($seconds / 2629743);

		if ($years > 0 OR $months > 0)
		{
			if ($months > 0)
				$return['months'] = $months;

			$seconds -= $months * 2629743;
		}
	
		$weeks = floor($seconds / 604800);

		if ($years > 0 OR $months > 0 OR $weeks > 0)
		{
			if ($weeks > 0)
				$return['weeks'] = $weeks;

			$seconds -= $weeks * 604800;
		}
	
		$days = floor($seconds / 86400);

		if ($months > 0 OR $weeks > 0 OR $days > 0)
		{
			if ($days > 0)
				$return['days'] = $days;

			$seconds -= $days * 86400;
		}
	
		$hours = floor($seconds / 3600);

		if ($days > 0 OR $hours > 0)
		{
			if ($hours > 0)
				$return['hours'] = $hours;

			$seconds -= $hours * 3600;
		}
	
		$minutes = floor($seconds / 60);

		if ($days > 0 OR $hours > 0 OR $minutes > 0)
		{
			if ($minutes > 0)
				$return['minutes'] = $minutes;

			$seconds -= $minutes * 60;
		}
	
		if ($str == '')
		{
			$return['seconds'] = $seconds;
		}

		return $return;
	}
}
