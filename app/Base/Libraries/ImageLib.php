<?php

namespace App\Base\Libraries;

class ImageLib
{

	/**
	 * Image processing using GD/GD2
	 *
	 * This function will resize an image, overwriting the old
	 *
	 * @param source_img
	 *         file location of image to be resized
	 * @param resized_width
	 *         size of width to be resized to
	 * @param resized_height
	 *         size of height to be resized to
	 *
	 * @return bool
	 */
	public static function resize($source_img, $resized_width, $resized_height)
	{
		//stores width, height, and type in a list
		[$source_width, $source_height, $type] = getimagesize($source_img);

		$source_gd_image = false;

		//is this a supported filetype of .gif, .jpeg, or .png?
		// if so, we create a new gd_image to pull the source img in
		switch ($type) {
			case IMAGETYPE_GIF:
				$source_gd_image = imagecreatefromgif($source_img);
				break;

			case IMAGETYPE_JPEG:
				$source_gd_image = imagecreatefromjpeg($source_img);
				break;

			case IMAGETYPE_PNG:
				$source_gd_image = imagecreatefrompng($source_img);
				break;
		}

		if (!$source_gd_image) {
			//Filetype not supported
			return false;
		}

		//get aspect ratio of source and resized
		$source_aspect_ratio  = $source_width / $source_height;
		$resized_aspect_ratio = $resized_width / $resized_height;

		//algorithim that decides which way to resize
		if ($source_width <= $resized_width && $source_height <= $resized_width) {
			$resized_width  = $source_width;
			$resized_height = $source_height;
		} elseif ($resized_aspect_ratio > $source_aspect_ratio) {
			$resized_width = ( int )($resized_height * $source_aspect_ratio);
		} else {
			$resized_height = ( int )($resized_width / $source_aspect_ratio);
		}

		//create a new canvas in gd
		$resized_gd_image = imagecreatetruecolor($resized_width, $resized_height);

		//copy old image into new image
		imagecopyresampled($resized_gd_image, $source_gd_image, 0, 0, 0, 0, $resized_width, $resized_height, $source_width, $source_height);
		//depending on the type of image of source, output that type for resized
		switch ($type) {
			case IMAGETYPE_GIF:
				imagegif($resized_gd_image, $source_img);
				break;

			case IMAGETYPE_JPEG:
				imagejpeg($resized_gd_image, $source_img);
				break;

			case IMAGETYPE_PNG:
				imagepng($resized_gd_image, $source_img);
				break;
		}
		//clear memory of the two gd_images we were working on
		imagedestroy($source_gd_image);
		imagedestroy($resized_gd_image);

		return true;
	}

	/**
	 * Image processing using GD/GD2
	 *
	 * Function creates a copy of the image
	 *
	 * @param source_img
	 *         file location of image to be copied
	 * @param dest_img
	 *         location you want new file to be copied to
	 *
	 * @return bool
	 */
	public static function copy($source_img, $dest_img)
	{
		//stores width, height, and type in a list
		[$source_width, $source_height, $type] = getimagesize($source_img);
		//is this a supported filetype of .gif, .jpeg, or .png?
		//if so, we create a new gd_image to pull the source img in
		switch ($type) {
			case IMAGETYPE_GIF:
				$source_gd_image = imagecreatefromgif($source_img);
				break;

			case IMAGETYPE_JPEG:
				$source_gd_image = imagecreatefromjpeg($source_img);
				break;

			case IMAGETYPE_PNG:
				$source_gd_image = imagecreatefrompng($source_img);
				break;
		}

		if ($source_gd_image === false) {
			//File type not supported
			return false;
		}

		//create a new gd_image canvas to prepare for copy
		$dest_gd_image = imagecreatetruecolor($source_width, $source_height);

		//copy that image directly. if copy fails, return false
		if (!imagecopy($dest_gd_image, $source_gd_image, 0, 0, 0, 0, $source_width, $source_height)) {
			return false;
		}

		//create image from gd_image into $dest_img, which is a file location
		imagejpeg($dest_gd_image, $dest_img);

		//clear memory of the two gd_images we were working on
		imagedestroy($dest_gd_image);
		imagedestroy($source_gd_image);

		return true;
	}

	/**
	 * Image processing using GD/GD2
	 *
	 * Function creates a copy of the image, then resizes it
	 *
	 * @param source_img
	 *         file location of image to be copied
	 * @param dest_img
	 *         location you want new file to be copied to
	 * @param resized_width
	 *         size of width to be resized to
	 * @param resized_height
	 *         size of height to be resized to
	 *
	 * @return bool
	 */
	public static function copy_and_resize($source_img, $dest_img, $resized_width, $resized_height)
	{
		//Copies image, exits function if copy fails
		if (!self::copy($source_img, $dest_img)) {
			return false;
		}

		return self::resize($dest_img, $resized_width, $resized_height);
	}

	public static function get_extension($file_name)
	{
		return preg_replace('/.*\./', '.', $file_name);
	}

}

?>
