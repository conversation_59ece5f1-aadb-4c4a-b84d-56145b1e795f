<?php

namespace App\Base\Libraries;

use <PERSON><PERSON>\Config;
use <PERSON><PERSON>\HTML;

/**
 * Gravatar bundle for the Laravel PHP framework.
 *
 * <AUTHOR>
 * @copyright   (c) 2012 - <PERSON>
 *
 * More info at: http://github.com/drewjoh/laravel-gravatar
 */
class Gravatar
{
    /**
     * @var string - A temporary internal cache of the URL parameters to use.
     */
    protected static $config_cache = null;

    /*
     * @var string - URL constants for the avatar images
     */
    const HTTP_URL  = 'http://www.gravatar.com/avatar/';
    const HTTPS_URL = 'https://secure.gravatar.com/avatar/';

    /**
     * Get a specific config option value
     *
     * @return string - the request config option if exists.
     */
    public static function config($item)
    {
        return (env('gravatar.' . $item)) ? env('gravatar.' . $item) : null;
    }

    /**
     * Build the avatar URL based on the provided email address and optional arguments.
     *
     * @param string $email - The email to get the gravatar for.
     *
     * @return string - The XHTML-safe URL to the gravatar.
     */
    public static function buildUrl($email, $size = null, $secure = null)
    {
        // If we haven't used this class yet, pre-load our config cache
        if (self::$config_cache === null) {
            self::$config_cache = env('common::gravatar');
        }

        // Start building the URL, and deciding if we're doing this via HTTPS or HTTP.
        if (
            (self::$config_cache['auto_detect_ssl'] and
             (isset($_SERVER['HTTPS']) and $_SERVER['HTTPS'])
            ) or
            self::$config_cache['use_secure_url']
            or
            $secure == true) {
            $url = static::HTTPS_URL;
        } else {
            $url = static::HTTP_URL;
        }

        // Tack the email hash onto the end of our Gravatar URL, then add our additional options.
        $url .= self::getEmailHash($email)
                . '?'
                . (self::$config_cache['force_default'] != false ? '&f=y' : '')
                . (self::$config_cache['max_rating'] != false ? '&r=' . self::$config_cache['max_rating'] : '')
                . ((self::$config_cache['size'] != false or $size != null) ? '&s=' . ($size !== null ? $size : self::$config_cache['size']) : '')
                . (self::$config_cache['default_image'] != false ? '&d=' . self::$config_cache['default_image'] : '');

        return $url;
    }

    /**
     * Get the email hash to use (after cleaning the string).
     *
     * @param string $email - The email to get the hash for.
     *
     * @return string - The hashed form of the email, post cleaning.
     */
    public static function getEmailHash($email)
    {
        // Hash created based on gravatar docs.
        return hash('md5', strtolower(trim($email)));
    }

    /**
     * Get the gravatar
     */
    public static function get($email, $size = null)
    {
        return self::buildUrl($email, $size);
    }

    /**
     * Get the gravatar with forced secure connection
     */
    public static function getSecure($email, $size = null)
    {
        return self::buildUrl($email, $size, true);
    }

    /**
     * Get the gravatar and return as image
     */
    public static function getImage($email, $size = null, $alt = '', $attributes = [])
    {
        return \HTML::image(self::buildUrl($email, $size), $alt, $attributes);
    }

    /**
     * Get the gravatar with forced secure connection and return as image
     */
    public static function getSecureImage($email, $size = null, $alt = '', $attributes = [])
    {
        return \HTML::image(self::buildUrl($email, $size, true), $alt, $attributes);
    }

}