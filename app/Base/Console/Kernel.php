<?php

namespace App\Base\Console;

use App\Console\Commands\DataImport\EastRidge\ImportUsers;
use App\Console\Commands\DataImport\ImportSpecialDays;
use App\Console\Commands\Finance\ChargeInvoiceCommand;
use App\Console\Commands\Finance\CreateAccountInvoices;
use App\Console\Commands\Finance\MarkInvoicePaidCommand;
use App\Console\Commands\Finance\ReconcilePayouts;
use App\Console\Commands\FormatPhoneNumbers;
use App\Console\Commands\InitNewAccount;
use App\Console\Commands\PickUpNewMessages;
use App\Console\Commands\Users\Notifications\CheckNotificationDeliveries;
use App\Console\Commands\Users\Payments\CheckAndUpdatePaymentStatus;
use App\Console\Commands\Users\Payments\RunPaymentSchedules;
use App\Console\Commands\Utilities\MoveRedisSessionsToDatabase;
use App\Console\Commands\WorshipAssignments\GenerateReminders;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        PickUpNewMessages::class,
        FormatPhoneNumbers::class,
        InitNewAccount::class,
        ReconcilePayouts::class,
        CreateAccountInvoices::class,
        ChargeInvoiceCommand::class,
        MarkInvoicePaidCommand::class,
        GenerateReminders::class,
        MoveRedisSessionsToDatabase::class,
        ImportSpecialDays::class,
        RunPaymentSchedules::class,
        CheckNotificationDeliveries::class,
        CheckAndUpdatePaymentStatus::class,
        ImportUsers::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('horizon:snapshot')
            ->everyFiveMinutes();

        $schedule->command('finance:reconcile-payouts')
            ->daily(); // Midnight

        $schedule->command('wa:generate-reminders')
            ->hourly();

        $schedule->command('lightpost:user-payment-schedules --charge')
            // Laravel 11.0 only
            //            ->purpose('Run all active user payment schedules on a daily basis at 07:00am CST (12:00 UTC).')
            ->dailyAt('12:00') // 7:00 AM CST
            ->withoutOverlapping(120);
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
