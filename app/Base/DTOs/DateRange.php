<?php

namespace App\Base\DTOs;

use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class DateRange
{
    public ?Carbon  $start_at = null;
    public ?Carbon  $end_at   = null;
    public array    $warnings = [];
    protected array $data     = [];

    public function setStartDate(Carbon $date): self
    {
        $this->data['start_at'] = $date;
        return $this;
    }

    public function setEndDate(Carbon $date): self
    {
        $this->data['end_at'] = $date;
        return $this;
    }

    /**
     * Validates the provided dates and finalizes the DTO properties.
     *
     * @throws ValidationException
     */
    public function validate(): self
    {
        $validator = Validator::make($this->data, [
            'start_at' => ['required', 'date'],
            'end_at'   => ['required', 'date'],
        ]);

        // Throw an exception if validation fails. Go no further.
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Ensure both dates are set before comparison
        if (isset($this->data['start_at'], $this->data['end_at'])) {
            // Check if end_at date is before start_at date
            if ($this->data['end_at']->isBefore($this->data['start_at'])) {
                // If it's not, reset the end_at to be an hour after start_at.
                $this->data['end_at'] = $this->data['start_at']->copy()->addHour();

                // Add a warning so we know this change happened.
                $this->warnings[] = "The end date was before start date and has been adjusted to one hour after the start date.";
            }
        }

        $validator = Validator::make($this->data, [
            'start_at' => ['required', 'date'],
            'end_at'   => ['required', 'date', 'after_or_equal:start'],
        ]);

        // Throw an exception if validation fails. Go no further.
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Set the properties that we'll use later.
        $this->start_at = $this->data['start_at'];
        $this->end_at   = $this->data['end_at'];

        // Clear temporary data after validating.
        $this->data = [];

        return $this;
    }

    public function getWarnings(): array
    {
        return $this->warnings;
    }

    public function hasWarnings(): bool
    {
        return !empty($this->warnings);
    }
}