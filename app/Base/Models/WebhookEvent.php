<?php

namespace App\Base\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WebhookEvent extends Model
{
    use SoftDeletes;

    protected $table = 'webhook_events';

    protected $casts = [
        'account_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'is_failure' => 'boolean',
        'is_warning' => 'boolean',
        'is_success' => 'boolean',
        'payload'    => 'json',
    ];

    protected $fillable = [
        'account_id',
        'service',
        'source_event_id',
        'source_event_type',
        'source_status',
        'payload',
        'is_failure',
        'is_warning',
        'is_success',
    ];
}
