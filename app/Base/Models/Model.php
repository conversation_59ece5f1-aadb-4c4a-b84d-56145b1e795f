<?php

namespace App\Base\Models;

use Auth;
use Exception;
use Illuminate\Database\Eloquent\Model as Eloquent;

abstract class Model extends Eloquent
{
    const DELETED_AT = 'deleted_at';

    protected $snapshot;

    protected $memoized;

    public function getLogName()
    {
        if (is_null($this->log_name)) {
            throw new Exception('Log name not defined on ' . get_called_class() . '.');
        }

        return (string) $this->{$this->log_name};
    }

    public function getLogAttributes()
    {
        return \Illuminate\Support\Arr::except($this->attributesToArray(), $this->log_guarded);
    }

    public function getLogModifier($attribute)
    {
        if (is_null($this->log_modifiers)) {
            return [];
        }

        if (array_key_exists($attribute, $this->log_modifiers)) {
            return $this->log_modifiers[$attribute];
        }
    }

    public function snapshot($property = null)
    {
        if ($property) {
            $this->snapshot = $this->$property;
        } else {
            $this->snapshot = clone $this;
        }
    }

    public function getSnapshotAttribute()
    {
        return $this->snapshot;
    }

    public function isNot($other)
    {
        return $this->getKey() !== $other->getKey();
    }
}
