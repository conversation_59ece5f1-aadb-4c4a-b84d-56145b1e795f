<?php

namespace App\Base\Models;

use Illuminate\Support\Str;

class Bible extends Model
{
    protected $table = 'bible_verses';

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $fillable = [
        'version',
        'book',
        'chapter',
        'verse',
        'text',
    ];

    public function lookupByString($verse_string, $version = 'ESV')
    {
        $book                = null;
        $chapter             = null;
        $verses              = [];
        $verse_return_string = null;

        $parts = explode(' ', $verse_string);

        if (count($parts) == 3) {
            $book          = $parts[0] . ' ' . $parts[1];
            $chapter_verse = $parts[2];
        } else {
            $book          = $parts[0];
            $chapter_verse = $parts[1];
        }

        // Exception for Psalm/Psalms
        if ($book == 'Psalm') {
            $book = 'Psalms';
        }

        $chapter_verse_parts = explode(':', $chapter_verse);

        $chapter      = $chapter_verse_parts[0];
        $verse_string = preg_replace('/[a-zA-Z]+/', '', $chapter_verse_parts[1]); // Remove any letters for things like "9a-11"

        if (Str::contains($verse_string, '-')) {
            $start_end_verse = explode('-', $verse_string);

            if ($start_end_verse[0] < $start_end_verse[1]) {
                for ($start = $start_end_verse[0]; $start <= $start_end_verse[1]; $start++) {
                    $verses[] = $this->getText($book, $chapter, $start, $version);
                }
            }

        } elseif (Str::contains($verse_string, ',')) {
            foreach (explode(',', $verse_string) as $verse_number) {
                $verses[] = $this->getText($book, $chapter, $verse_number, $version);
            }
        } else {
            $verses[] = $this->getText($book, $chapter, $verse_string, $version);
        }

        foreach ($verses as $verse) {
            $verse_return_string .= $verse . ' ';
        }

        return trim($verse_return_string);
    }

    public function getText($book, $chapter, $verse, $version = 'ESV')
    {
        return self::where('version', $version)
            ->where('book', 'LIKE', $book)
            ->where('chapter', $chapter)
            ->where('verse', $verse)
            ->first()?->text;
    }
}
