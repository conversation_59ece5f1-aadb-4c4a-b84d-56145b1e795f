<?php

namespace App\Base\Models;

class Audit extends Model
{
    protected $table = 'audits';

    const UPDATED_AT = null;

    protected $casts = [
        'created_at' => 'datetime',
        'deleted_at' => 'datetime',
        'old_values' => 'json',
        'new_values' => 'json',
    ];

    protected $fillable = [
        'audit_model_id',
        'model_id',
        'user_id',
        'event',
        'old_values',
        'new_values',
        'notes',
    ];

    public function model()
    {
        return $this->belongsTo(AuditModel::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
