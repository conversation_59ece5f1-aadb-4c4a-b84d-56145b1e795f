<?php

namespace App\Base\Models\Traits;

use App\Base\Models\Audit;
use App\Base\Models\AuditModel;
use Illuminate\Support\Facades\Log;

trait AuditTrait
{
    protected $__oldAttributesForAudit;

    // Add this property to the model for fields that should not trigger audit records.
    // protected $audit_ignore_fields = [];

    // Helper method to determine if only ignored fields are changed.
    // This is used to prevent audit records from being created when only ignored fields are changed (such as the `last_active` field on the User model).
    protected function hasOnlyIgnoredChanges(array $changedFields): bool
    {
        if (!is_array($this->audit_ignore_fields) || empty($this->audit_ignore_fields)) {
            return false;
        }

        $nonIgnored = array_diff($changedFields, $this->audit_ignore_fields);

        return empty($nonIgnored);
    }

    public static function bootAuditTrait()
    {
        /**
         * When a model is CREATED, create an audit record.
         */
        static::created(function ($model) {
            // We're ONLY going to record WHO deleted this.  No need to record data.

            // Wrap everything in a try/catch, because we don't want this to stop functionality.
            try {
                // If we can't determine who is logged in, don't bother recording it since we just want to know who.
                if (!auth()?->user()->id) {
                    return;
                }

                // Get the model's class name
                $class_name = get_class($model);

                // Find or create a record of what model we're creating an audit record for.
                $audit_model = AuditModel::firstOrCreate([
                    'name' => $class_name,
                ]);

                // Create an audit record.
                Audit::create([
                    'audit_model_id' => $audit_model->id,
                    'model_id'       => $model->id,
                    'user_id'        => auth()?->user()?->id,
                    'event'          => 'created',
                ]);

                // Remove the old attributes so they don't leak anywhere.
                unset($model->__oldAttributesForAudit);
            } catch (\Exception $e) {
                Log::error('AuditTrait::boot - Created failed.', [
                    'class_name' => get_class($model),
                    'model_id'   => $model->id,
                    'error'      => $e->getMessage(),
                    'user_id'    => auth()?->user()?->id,
                ]);
            }
        });

        /**
         * When a model is UPDATING, PREPARE for an audit record in the UPDATED event.
         */
        static::updating(function ($model) {
            // Wrap everything in a try/catch, because we don't want this to stop functionality.
            try {
                // Store the original attributes
                // We're ABOUT to update the model, capture the original values.
                // This will add an `oldAttributes` property to the model with all the old data.
                $model->__oldAttributesForAudit = $model->getOriginal();

                // Ensure '__oldAttributesForAudit' is hidden when the model is serialized
                $model->makeHidden('__oldAttributesForAudit');
            } catch (\Exception $e) {
                Log::error('AuditTrait::boot - Updating failed.', [
                    'class_name' => get_class($model),
                    'model_id'   => $model->id,
                    'error'      => $e->getMessage(),
                    'user_id'    => auth()?->user()?->id,
                ]);
            }
        });

        /**
         * When a model is UPDATED, create an audit record.
         */
        static::updated(function ($model) {
            // Wrap everything in a try/catch, because we don't want this to stop functionality.
            try {
                // Get the model's class name
                $class_name = get_class($model);

                // TODO:
                // 1. If this is the User model, and all that's updated is the `last_active` field, ignore it.

                // Find or create a record of what model we're creating an audit record for.
                $audit_model = AuditModel::firstOrCreate([
                    'name' => $class_name,
                ]);

                // Get only the changed attributes (new values).
                $new_values = $model->getChanges();

                // Use helper to ignore audit creation when only ignored fields changed.
                if ($model->hasOnlyIgnoredChanges(array_keys($new_values))) {
                    unset($model->__oldAttributesForAudit);
                    return;
                }

                // Compute $before by intersecting keys between the old values and changed values.
                $old_values = array_intersect_key($model->__oldAttributesForAudit ?: [], $new_values);

                // Create an audit record.
                Audit::create([
                    'audit_model_id' => $audit_model->id,
                    'model_id'       => $model->id,
                    'user_id'        => auth()?->user()?->id,
                    'event'          => 'updated',
                    'old_values'     => $old_values,
                    'new_values'     => $new_values,
                ]);

                // Remove the old attributes so they don't leak anywhere.
                unset($model->__oldAttributesForAudit);
            } catch (\Exception $e) {
                Log::error('AuditTrait::boot - Updated failed.', [
                    'class_name' => get_class($model),
                    'model_id'   => $model->id,
                    'error'      => $e->getMessage(),
                    'user_id'    => auth()?->user()?->id,
                ]);
            }
        });

        /**
         * When a model is DELETING, create an audit record ONLY of the USER, if there is one.
         */
        static::deleted(function ($model) {
            // We're ONLY going to record WHO deleted this.  No need to record data.

            // Wrap everything in a try/catch, because we don't want this to stop functionality.
            try {
                // Get the model's class name
                $class_name = get_class($model);

                // Find or create a record of what model we're creating an audit record for.
                $audit_model = AuditModel::firstOrCreate([
                    'name' => $class_name,
                ]);

                // Create an audit record.
                Audit::create([
                    'audit_model_id' => $audit_model->id,
                    'model_id'       => $model->id,
                    'user_id'        => auth()?->user()?->id,
                    'event'          => 'deleted',
                ]);

                // Remove the old attributes so they don't leak anywhere.
                unset($model->__oldAttributesForAudit);
            } catch (\Exception $e) {
                Log::error('AuditTrait::boot - Deleted failed.', [
                    'class_name' => get_class($model),
                    'model_id'   => $model->id,
                    'error'      => $e->getMessage(),
                    'user_id'    => auth()?->user()?->id,
                ]);
            }
        });
    }
}