# GitHub Action for <PERSON><PERSON> with MySQL and Redis

name: Test & Deploy

on: [ push ]

jobs:
  laravel:
    name: <PERSON><PERSON> (PHP ${{ matrix.php-versions }})
    runs-on: ubuntu-latest
    env:
      DB_DATABASE: laravel
      DB_USERNAME: root
      DB_PASSWORD: password
      BROADCAST_DRIVER: log
      CACHE_DRIVER: redis
      QUEUE_CONNECTION: redis
      SESSION_DRIVER: redis

    # Docs: https://docs.github.com/en/actions/using-containerized-services
    services:
      mysql:
        image: mysql:latest
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: false
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: laravel
        ports:
          - 3306/tcp
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis
        ports:
          - 6379/tcp
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    strategy:
      fail-fast: true
      matrix:
        php-versions: [ '8.3', '8.4' ]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Docs: https://github.com/shivammathur/setup-php
      - name: Setup PHP
        uses: drewjoh/setup-php@2ee6851e7b8e463a7b614ec25d29f08bdca9fe2f
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, dom, fileinfo, mysql
          coverage: xdebug

      # Local MySQL service in GitHub hosted environments is disabled by default.
      # If you are using it instead of service containers, make sure you start it.
      # - name: Start mysql service
      #   run: sudo systemctl start mysql.service

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          # Use composer.json for key, if composer.lock is not committed.
          # key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Add Flux Pro License
        run: composer config http-basic.composer.fluxui.dev ${{ secrets.FLUX_PRO_EMAIL }} ${{ secrets.FLUX_PRO_KEY }}

      - name: Install Composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: Prepare the application
        run: |
          php -r "file_exists('.env') || copy('.env.testing', '.env');"
          php artisan key:generate
      - name: Clear Config
        run: php artisan config:clear

      - name: Run Migration
        run: php artisan migrate -v
        env:
          DB_PORT: ${{ job.services.mysql.ports['3306'] }}
          REDIS_PORT: ${{ job.services.redis.ports['6379'] }}

      - name: Laravel Passport Setup
        run: php artisan passport:install
        env:
          DB_PORT: ${{ job.services.mysql.ports['3306'] }}
          REDIS_PORT: ${{ job.services.redis.ports['6379'] }}

      - name: Test with phpunit
        run: vendor/bin/phpunit --coverage-text
        env:
          DB_PORT: ${{ job.services.mysql.ports['3306'] }}
          REDIS_PORT: ${{ job.services.redis.ports['6379'] }}

      - name: Test Route Cache
        run: php artisan route:cache

      - name: Test Config Cache
        run: php artisan config:cache

      - name: Test Event Cache
        run: php artisan event:cache

      - name: Test Icons Cache
        run: php artisan icons:cache
  npm:
    name: NPM Test, Cache & Compile
    needs: laravel
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 21
          cache: 'npm'
          cache-dependency-path: 'package-lock.json'

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          # Use composer.json for key, if composer.lock is not committed.
          # key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Add Flux Pro License
        run: composer config http-basic.composer.fluxui.dev ${{ secrets.FLUX_PRO_EMAIL }} ${{ secrets.FLUX_PRO_KEY }}

      - name: Install Composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: Setup Dependencies
        run: |
          npm config set '//npm.fontawesome.com/:_authToken' "${{ secrets.FONTAWESOME_NPM_AUTH_TOKEN }}"

      - name: Install NPM dependencies
        if: steps.npm-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: Test NPM Compile
        run: npm run prod
  deploy-dev:
    name: Deploy Develop
    runs-on: ubuntu-latest
    needs: [ laravel, npm ]
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Trigger Envoyer Deploy
        run: curl ${{ vars.ENVOYER_DEPLOY_DEV }}
  deploy-prod:
    name: Deploy Production
    runs-on: ubuntu-latest
    needs: [ laravel, npm ]
    if: github.ref == 'refs/heads/master'
    steps:
      - name: Trigger Envoyer Deploy
        run: curl ${{ vars.ENVOYER_DEPLOY_PROD }}

