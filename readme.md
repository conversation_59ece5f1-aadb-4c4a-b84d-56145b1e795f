# Lightpost App

## About

Lightpost app is a web application built on the Laravel framework.

## License

Lightpost App and all associated code, documentation and material is owned by Tiny Bit Farm LLC. Do not copy or use, in whole or in part, anything, without express, written permission.

## Special Considerations

1. The **email service stores attachments locally**. So the API server and Horizons server currently needs to **be the same server** (processes queued messages).
    - The API receives incoming emails and stores attachments; Horizon processes outgoing emails and provides attachments.
    - The `.env` variable that decides this location is: `MESSAGE_FILES_ROOT_PATH`
    - The original intent was to store attachments on Digital Ocean Spaces, decoupled.
    - For the sake of saving bandwidth and increasing performance, we do this locally for now.
    - The performance issue was a result of servers being in the NY3 datacenter, and Spaces being in SF1.
2. LogDNA is being sent messages **from the app**, not the server.
    - This is an option in Forge to route server messages, but this gets too busy and messy too quickly.
    - We now only log deliberately.

## Software Services

- PHP 8.3.x
- MySQL 8.1.x
- Redis 7.0
- NodeJS 18.2
- NPM

## Running the app locally

### Setting up local URLs

1. Setup the following local URLs to point to your `{{repo}}/public` folder.
    1. https://lightpost.test
    2. https://app.lightpost.test
    3. https://admin.lightpost.test
    4. https://api.lightpost.test
    5. https://mobile-api.lightpost.test
2. Run `composer install`
3. Run `npm install`
4. Run `php artisan migrate:reset`
5. Run `php artisan db:seed`
6. Run `npm dev`
7. Run `composer test`

## Font Awesome Pro Setup

NPM needs to be configured on any machine Lightpost is deployed to, to be able to pull in the Font Awesome Pro assets. Run this command:

```
npm config set "@fortawesome:registry" https://npm.fontawesome.com/ && \
npm config set "//npm.fontawesome.com/:_authToken" {{ FONT-AWESOME-PRO-KEY }}
```

## Flux Pro Setup

We need to create an `auth.json` to the root of the project that includes the license key for [Flux Pro](https://fluxui.dev/dashboard).  
This file **should not** be committed to the repo.

You can run the following command to create an auth.json file in your project's root directory:

```
composer config http-basic.composer.fluxui.dev {EMAIL} {KEY}
```

- This command should be run after cloning the repo and before a `composer install` in the deployment process.
- This command also needs to be added to our Github Actions, using Actions secrets to store the actual key itself.

## File Storage

Lightpost uses Linode Object Storage for remote file storage.  
Previously, we were using DigitalOcean Spaces, but are migrating away from that.

### Dev

- https://lightpost-user-images-dev.nyc3.digitaloceanspaces.com
- https://lightpost-user-files-dev.nyc3.digitaloceanspaces.com
- https://dev-lightpost-groups.us-southeast-1.linodeobjects.com
- https://dev-lightpost-sermons.us-southeast-1.linodeobjects.com
- https://lightpost-account-files-dev.nyc3.digitaloceanspaces.com
- https://lightpost-message-files-dev.nyc3.digitaloceanspaces.com
- https://dev-lightpost-podcasts.us-southeast-1.linodeobjects.com
- https://dev-lightpost-temp.us-southeast-1.linodeobjects.com

### Production

- https://lightpost-user-images.nyc3.digitaloceanspaces.com
- https://lightpost-user-files.nyc3.digitaloceanspaces.com
- https://lightpost-groups.us-southeast-1.linodeobjects.com
- https://lightpost-sermons.us-southeast-1.linodeobjects.com
- https://lightpost-account-files.nyc3.digitaloceanspaces.com
- https://lightpost-message-files.nyc3.digitaloceanspaces.com
- https://lightpost-podcasts.us-southeast-1.linodeobjects.com
- https://lightpost-temp.us-southeast-1.linodeobjects.com

## Messaging

### Email

- Inbound email is handled through **Postmark**
    - Current inbound address is `*@to.lightpost.email`.
    - We previously used Mailgun & Sparkpost, both of which increased their prices.
        - Sparkpost only supported inbound creation and management through their API.
- Outbound email is handled through **Postmark**
    - Current outbound messages come from `<EMAIL>`
    - The reply to address goes back to the account email.
    - Prevously used Sparkpost; costs were ~17% lower than Mailgun.
    - Sparkpost increased their prices. Moved to Postmark and bought emails in bulk to get great pricing.
    - Ultimate plan will be to move to Amazon SES, after figuring out how to properly use their APIs for message tracking.

### SMS

- Plivo now requires registering each new number manually and getting approval for texting.
    - First aquire a new number (by selecting voice only toll-free numbers)
    - Then fill out this [Plivo Google Form](https://docs.google.com/forms/d/e/1FAIpQLSfR76fSC3fxY1Quva3Xc9Zjje1Z4L-9-oHc50MOSertVNVltg/viewform?fbzx=6323468673872073064).

## Server Setup

### Sites on Forge

The following sites need to be setup on the Forge server:

- `lightpost.app`
- `admin.lightpost.app`
- `app.lightpost.app`
- `api.lightpost.app`
- `mobile-api.lightpost.app`
- `podcasts.lightpost.app`

### Site Setup

We **only** deploy to the `lightpost.app` site via Envoyer.

All other sites should have their Nginx config file modified to change the `root` value from:

`root /home/<USER>/{SUBDOMAIN}.lightpost.app/current/public;`  
**to**  
`root /home/<USER>/lightpost.app/current/public;`

This directs Nginx to serve the site from our primary folder, and we only need to deploy the code once.  
Previously we were using `symlinks` to point to our primary folder from the Nginx folders,
but this requires logging into the server, deleting folders and adding symlinks manually.
Nginx config files are accessible on a per site basis via Laravel Forge.

![image](.readme/laravel-forge-nginx-config-example.png)

### Daemons

The Horizon daemon needs to be setup with `supervisord`.

- `php /home/<USER>/lightpost.app/current/artisan horizon`

### Envoyer

Restart PHP-FPM should only happen for one of the sites (primary).

#### Deployment Hooks

The following deployment hooks must be in place:

#### 1. Migrate DB

Happens **Before Activate New Release**  
Applies to:

- `lightpost.app`

```
cd {{release}}
php artisan migrate --force
```

#### 2. Cache Routes

Happens **Before Activate New Release**  
Applies to:

- `lightpost.app`

```
cd {{release}}
php artisan route:cache
```

#### 3. Copy `node_modules` and compile Javascript

Happens **Before Activate New Release**  
Applies to:

- `admin.lightpost.app`
- `app.lightpost.app`
- `lightpost.app`
- `developer.lightpost.app`

```
# Hook 4 - does not work with symlink for some reason
# ln -nfs /home/<USER>/node_modules {{release}}/node_modules
# cp -r /home/<USER>/node_modules {{release}}
# Compile Assets
cd {{release}}
npm install --save
npm run prod
```

#### 4. Restart Horizon

Happens **AFTER Activate New Release**  
Applies to:

- `db.lightpost.app`

With Supervisord enabled -- Horizon will restart automatically. This terminate command shuts it down gracefully.

```
cd {{release}}
php artisan horizon:terminate
```

## Server Clean-up

The `/tmp` folder needs to be cleaned up occasionally from some libraries using it to store things.

Specifically, these files:

```
__tcpdf_*   // From the TCPDF library when doing the Photo Directory.
cropped*    // Presumably from cropping photos before saving them in the Admin.
```

## Libraries

A list of libraries used in different parts of the app.

- https://tempusdominus.github.io/bootstrap-4/
- https://trix-editor.org/
- https://fullcalendar.io/

## Testing

For integration tests, we needed to do some special things with routing, because Lightpost is designed for subdomains to make it easy to separate our workloads.

For this reason, **only for testing**, we **remove domains** from `Route Groups` and **add prefixes** to URLs.

We use `admin.lightpost.test` for our `Base URL` for all testing (since the largest part of the app is the `Admin`).

For example:

| **`LOCAL`**                          | **`STAGING`**                 | **`PRODUCTION`**              |
|--------------------------------------|-------------------------------|-------------------------------|
| `admin.lightpost.test/frontend`      | `lightpost.dev`               | `lightpost.app`               |
| `admin.lightpost.test/app`           | `app.lightpost.dev`           | `app.lightpost.app`           |
| `admin.lightpost.test`               | `admin.lightpost.dev`         | `admin.lightpost.app`         |
| `admin.lightpost.test/api/v1`        | `api.lightpost.dev/v1`        | `api.lightpost.app/v1`        |
| `admin.lightpost.test/mobile-api/v1` | `mobile-api.lightpost.dev/v1` | `mobile-api.lightpost.app/v1` |

## API

### Error formatting

Starting with v2 of the Mobile API, we use this standard format for errors:

```$json
{
  "error": true,
  "message": "Please make sure required fields are filled out.",
  "type": "error",
  "code": "123",
  "fields": {
    "title": [
      "The title field is required."
    ],
    "body": [
      "The body field is required."
    ]
  }
}
```

## SQLite for Caching

In May 2025 we switched to using SQLite for caching data instead of Redis, as part of a migration to get off Redis for sessions and caching.

The setup involves:

1. `touch storage/database/cache.sqlite` to create the SQLite file
2. Create/Run the migration specific to this database. 
3. Set our `.env` variable `CACHE_DRIVER=database`

Also, configure the SQLite database with the following commands:

```sql
PRAGMA journal_mode = WAL; // Drastically improves concurrent reads and allows writes to happen without blocking reads.
PRAGMA synchronous = OFF; // Reduces durability but increases speed. SQLite skips waiting for disk syncs. Risk of corruption if power fails.
PRAGMA foreign_keys = OFF; // Only if safe to do so.
```

## Analytics

```
D /var/run/nginx-cache 0755 forge root -
D /var/run/nginx-cache/jscache 0755 forge root -
```


