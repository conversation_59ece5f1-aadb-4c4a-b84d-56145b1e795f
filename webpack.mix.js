let mix = require('laravel-mix');

const {exec} = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

// We need to run `npm run prod` ONE time in dev mode to generate the FontAwesome Pro webfonts.
// Then we can run `npm run dev` in dev mode and it will use the existing FontAwesome Pro webfonts.
// This lets us avoid having to re-copy the FontAwesome Pro webfonts each time we run npm run dev, which is a lot of files and takes a long time.

/* Run Tailwind CLI directly before each build */
mix.before(async () => {
    const tailwindCommands = [
        'npx tailwindcss -c tailwind.app.config.js -i ./resources/assets/app/css/tailwind.app.css -o ./public/static/app/css/tailwind.app.css --minify',
        'npx tailwindcss -c tailwind.website.config.js -i ./resources/assets/website/css/tailwind.website.css -o ./public/static/website/css/tailwind.website.css --minify',
        'npx tailwindcss -c tailwind.frontend.config.js -i ./resources/assets/frontend/css/tailwind.frontend.css -o ./public/static/frontend/css/tailwind.frontend.css --minify',
        'npx tailwindcss -c tailwind.admin.config.js -i ./resources/assets/admin/css/tailwind.admin.css -o ./public/static/admin/css/tailwind.admin.css --minify',
        'npx tailwindcss -c tailwind.podcasts.config.js -i ./resources/assets/podcasts/css/tailwind.podcasts.css -o ./public/static/podcasts/css/tailwind.podcasts.css --minify',
        'npx tailwindcss -c tailwind.public.config.js -i ./resources/assets/public/css/tailwind.public.css -o ./public/static/public/css/tailwind.public.css --minify',
        'npx tailwindcss -c tailwind.open.config.js -i ./resources/assets/open/css/tailwind.open.css -o ./public/static/open/css/tailwind.open.css --minify'
    ];

    try {
        if (mix.inProduction() || process.env.APP_ENV === 'staging') {
            // Run with max 2 concurrent tasks to avoid OOM issues
            const maxConcurrent = process.env.APP_ENV === 'staging' ? 1 : 2;
            const chunks = [];
            for (let i = 0; i < tailwindCommands.length; i += maxConcurrent) {
                chunks.push(tailwindCommands.slice(i, i + maxConcurrent));
            }
            
            console.log(`🚀 Running ${tailwindCommands.length} Tailwind builds in chunks of ${maxConcurrent}...`);
            
            for (const [index, chunk] of chunks.entries()) {
                console.log(`📦 Processing chunk ${index + 1}/${chunks.length}...`);
                const results = await Promise.allSettled(chunk.map(cmd =>
                    execAsync(cmd, {stdio: 'inherit'})
                        .then(() => {
                            console.log(`✅ Completed: ${cmd.split(' ')[3]}`);
                            return { success: true, cmd };
                        })
                        .catch(err => {
                            console.error(`❌ Failed: ${cmd.split(' ')[3]}`, err.message);
                            return { success: false, cmd, error: err };
                        })
                ));
                
                // Check if any failed
                const failures = results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && !r.value.success));
                if (failures.length > 0) {
                    throw new Error(`${failures.length} Tailwind builds failed`);
                }
            }
        } else {
            // Run all in parallel for local development (assuming more memory available)
            await Promise.all(tailwindCommands.map(cmd =>
                execAsync(cmd, {stdio: 'inherit'})
                    .catch(err => console.error(`Error executing ${cmd}:`, err))
            ));
        }
        console.log('✅ All Tailwind CSS builds completed successfully.');
    } catch (error) {
        console.error('❌ Error during Tailwind CSS builds:', error);
        throw error; // Re-throw to ensure build fails
    }
});

/* Remaining Laravel Mix Configuration */
const copyOperations = [
    ['node_modules/@selectize/selectize/dist/css/selectize.css', 'public/static/global/css'],
    ['node_modules/moment/min/moment.min.js', 'public/static/global/js/moment.js'],
    ['node_modules/sortablejs/Sortable.min.js', 'public/static/global/js/sortable.js'],
    ['resources/assets/app/js/dymo.connect.framework.min.js', 'public/static/app/js'],
    ['resources/assets/app/js/dymo.connect.framework.min.js', 'public/static/public/js'],
    ['node_modules/fullcalendar-scheduler/index.global.min.js', 'public/static/global/js/fullcalendar.js']
];

/* Copy operations run in parallel */
Promise.all(copyOperations.map(([src, dest]) =>
    new Promise((resolve, reject) => {
        mix.copy(src, dest);
        resolve();
    })
));

/* Ensure Tailwind-generated files get versioning */
mix.version([
    'public/static/app/css/tailwind.app.css',
    'public/static/website/css/tailwind.website.css',
    'public/static/frontend/css/tailwind.frontend.css',
    'public/static/admin/css/tailwind.admin.css',
    'public/static/podcasts/css/tailwind.podcasts.css',
    'public/static/public/css/tailwind.public.css',
    'public/static/open/css/tailwind.open.css'
]);

mix.js([
    'node_modules/autosize/dist/autosize.min.js',
], 'public/static/app/js/app.js');

mix.js([
    'node_modules/alpinejs/dist/cdn.min.js'
], 'public/static/website/js/website.js');

mix.js([
    'node_modules/alpinejs/dist/cdn.min.js'
], 'public/static/frontend/js/app.js');

mix.js([
    'node_modules/choices.js/public/assets/scripts/choices.min.js',
], 'public/static/admin/js/app.js');

mix.js([
    'node_modules/alpinejs/dist/cdn.min.js',
], 'public/static/podcasts/js/app.js');

mix.js([
    'node_modules/alpinejs/dist/cdn.min.js'
], 'public/static/public/js/app.js');

mix.js([
    'node_modules/alpinejs/dist/cdn.min.js'
], 'public/static/open/js/app.js');

if (mix.inProduction()) {
    mix.version();

    // Copy Font Awesome Pro webfonts
    const copyOperations = [
        ['node_modules/@fortawesome/fontawesome-pro/webfonts', 'public/webfonts/webfonts'],
        ['node_modules/@fortawesome/fontawesome-pro/css', 'public/webfonts/css'],
    ];

    /* Copy operations run in parallel */
    Promise.all(copyOperations.map(([src, dest]) =>
        new Promise((resolve) => {
            mix.copy(src, dest);
            resolve();
        })
    ));
} else {
    // In dev mode, check if files exist and add them to the manifest without copying
    // This is because we don't want to re-copy the FontAwesome Pro webfonts each time we run npm run dev, which is a lot of files and takes a long time.
    const fs = require('fs');
    const path = require('path');
    
    // Check if the main CSS file exists
    const fontAwesomeCssPath = 'public/webfonts/css/all.min.css';
    if (fs.existsSync(fontAwesomeCssPath)) {
        // Add to the mix manifest without copying
        mix.version([fontAwesomeCssPath]);
    }
}