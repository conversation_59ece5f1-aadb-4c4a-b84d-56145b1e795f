<?php

namespace Tests;

use App\Accounts\Account;
use App\Accounts\AccountLocation;
use App\Users\Phone;
use App\Users\Role;
use App\Users\User;
use Carbon\Carbon;
use Exception;

trait TestHelpers
{
    public function date($date = null)
    {
        $now = Carbon::now();

        if (is_null($date)) {
            return $now;
        }

        return $now->setTimezone('America/Chicago')->modify($date)->setTimezone('UTC');
    }

    public function superUser(): User
    {
        $user = User::factory()->create([
            'account_id' => Account::factory()->create(),
            'timezone'   => 'America/Chicago',
        ]);

        $user->is_super = true;
        $user->save();

        $phone = new Phone();

        $phone->number  = '**********';
        $phone->user_id = $user->id;
        $phone->save();

        // Make them a member
        $role = Role::create([
            'account_id'           => $user->account_id,
            'name'                 => 'member',
            'key'                  => 'member',
            'indicates_membership' => true,
        ]);

        $user->roles()->attach($role->id);

        return $user;
    }

    public function user(Account $account = null, AccountLocation $accountLocation = null)
    {
        $user = User::factory()->create([
            'account_id' => ($account ? $account->id : Account::factory()->create()->id),
        ]);

        // Make them a member
        $role = Role::create([
            'account_id'           => $user->account_id,
            'name'                 => 'member',
            'key'                  => 'member',
            'indicates_membership' => true,
        ]);

        $user->roles()->attach($role->id);

        return $user;
    }

    public function nonMemberUser(Account $account = null, AccountLocation $accountLocation = null)
    {
        $user = User::factory()->create([
            'account_id' => ($account ? $account->id : Account::factory()->create()->id),
        ]);

        return $user;
    }

    protected function assertException(callable $callback, $expectedException = 'Exception', $expectedCode = null, $expectedMessage = null)
    {
        $expectedException = ltrim((string)$expectedException, '\\');
        if (!class_exists($expectedException) && !interface_exists($expectedException)) {
            $this->fail(sprintf('An exception of type "%s" does not exist.', $expectedException));
        }

        try {
            $callback();
        } catch (Exception $e) {
            $class   = get_class($e);
            $message = $e->getMessage();
            $code    = $e->getCode();

            $errorMessage = 'Failed asserting the class of exception';
            if ($message && $code) {
                $errorMessage .= sprintf(' (message was %s, code was %d)', $message, $code);
            } elseif ($code) {
                $errorMessage .= sprintf(' (code was %d)', $code);
            }
            $errorMessage .= '.';
            $this->assertInstanceOf($expectedException, $e, $errorMessage);

            if ($expectedCode !== null) {
                $this->assertEquals($expectedCode, $code, sprintf('Failed asserting code of thrown %s.', $class));
            }
            if ($expectedMessage !== null) {
                $this->assertContains($expectedMessage, $message, sprintf('Failed asserting the message of thrown %s.', $class));
            }

            return;
        }

        $errorMessage = 'Failed asserting that exception';
        if (strtolower($expectedException) !== 'exception') {
            $errorMessage .= sprintf(' of type %s', $expectedException);
        }
        $errorMessage .= ' was thrown.';
        $this->fail($errorMessage);
    }
}
