<?php

namespace Tests\Feature\MobileApi\Sermons\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class HomeControllerTest extends TestCase
{
    use DatabaseTransactions;

    const API_PREFIX = 'mobile-api/';
    const AUTH_GUARD = 'mapi';

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testIndex()
    {
        $admin = $this->user();

        $response = $this
            ->actingAs($admin, self::AUTH_GUARD)
            ->json('get', self::API_PREFIX . 'v1/home');

        $response->assertOk()
            ->assertJson([
                'birthdays' => [
                    'today'    => [],
                    'upcoming' => [],
                ],
                'sermons'   => [
                    'latest' => [],
                ],
            ]);
    }

    public function testIndexUnauthorized()
    {
        $admin = $this->nonMemberUser();

        $response = $this
            ->actingAs($admin, self::AUTH_GUARD)
            ->json('get', self::API_PREFIX . 'v1/home');

        $response->assertUnauthorized();
    }
}
