<?php

namespace Tests\Feature\MobileApi\V2\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class HomeControllerTest extends TestCase
{
    use DatabaseTransactions;

    const API_PREFIX = 'mobile-api/v2/';
    const AUTH_GUARD = 'mapi';

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testIndex()
    {
        $admin = $this->superUser();

        $response = $this
            ->actingAs($admin, self::AUTH_GUARD)
            ->json('get', self::API_PREFIX . 'home');

        $response->assertOk()
            ->assertJsonStructure([
                'birthdays' => [
                    'today'    => [],
                    'upcoming' => [],
                ],
                'sermons'   => [
                    'latest' => [],
                ],
            ]);
    }

    public function testIndexUnauthorized()
    {
        $admin = $this->nonMemberUser();

        $response = $this
            ->actingAs($admin, self::AUTH_GUARD)
            ->json('get', self::API_PREFIX . 'home');

        $response->assertUnauthorized();
    }
}
