<?php

namespace Tests\Feature\Api\Sermons\Controllers;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class SermonControllerTest extends TestCase
{
    use DatabaseTransactions;

    const API_PREFIX = 'api/v1/';

    public function setUp(): void
    {
        parent::setUp();
    }

    public function testIndex()
    {
        $admin = $this->superUser();

        $response = $this
            ->withHeaders([
                'Authorization' => $admin->account->api_token,
            ])
            ->json('get', self::API_PREFIX . 'sermons');

        $response->assertOk()
            ->assertJson([
                'prev_page_url' => '',
                'to'            => '',
                'total'         => '0',
            ]);
    }
}
