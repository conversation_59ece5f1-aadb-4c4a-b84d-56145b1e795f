<?php

namespace Tests\Feature\Users;

use App\Accounts\AccountSetting;
use App\Accounts\AccountSettingValue;
use App\Users\Group;
use Tests\TestCase;

class GroupTest extends TestCase
{
    public function testIsViewbaleToUser()
    {
        $user1 = $this->user();
        $user2 = $this->user();

        $this->createAccountSettingForPosts($user1->account);

        $group = Group::create([
            'account_id'            => $user1->account_id,
            'creator_id'            => $user1->id,
            'name'                  => 'Test Group',
            'enable_posts'          => true,
            'allow_members_to_post' => true,
        ]);

        $user1->groups()->attach($group->id);

        $this->assertTrue($group->isViewableToUser($user1));
        $this->assertFalse($group->isViewableToUser($user2));
    }

    public function createAccountSettingForPosts($account)
    {
        $setting = AccountSettingValue::firstOrCreate([
            'account_id'         => $account->id,
            'account_setting_id' => AccountSetting::where('key', 'feature.group_posts')->first()->id,
        ]);

        $setting->value             = 1;
        $setting->enable_for_member = 1;
        $setting->enable_for_admin  = 1;

        $setting->save();
    }
}
