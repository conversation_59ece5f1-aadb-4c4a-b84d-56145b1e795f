<?php

namespace Tests\Unit\App\Users\Services;

use App\Users\Services\UpdateUser;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class UpdateUserTest extends TestCase
{
    use DatabaseTransactions;

    public function testUpdatePassword()
    {
        $user = $this->user();

        $user->password = 'old password';
        $user->save();

        $old_password = $user->fresh()->password;

        $attributes = [
            'password' => 'new password',
        ];

        (new UpdateUser($user))->update($attributes);

        $user->refresh();

        $this->assertFalse(Hash::check('new password', $old_password));
        $this->assertTrue(Hash::check('new password', $user->password));
    }
}
