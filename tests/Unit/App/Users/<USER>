<?php

namespace Tests\Unit\App\Users;

use App\Users\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class UserTest extends TestCase
{
    use DatabaseTransactions;

    public function testGetAllFamilies()
    {
        $start_family_count = User::getAllFamilies()->count();

        $user1            = $this->user();
        $user1->family_id = $user1->id;
        $user1->save();
        $user2            = $this->user($user1->account);
        $user2->family_id = $user2->id;
        $user2->save();
        $user3            = $this->user($user1->account);
        $user3->family_id = $user3->id;
        $user3->save();
        $user4            = $this->user();
        $user4->family_id = $user4->id;
        $user4->save();

        $user1->fresh();
        $user2->fresh();
        $user3->fresh();
        $user4->fresh();

        $this->assertEquals(3, User::getAllFamilies()->visibleTo($user1)->count());
        $this->assertEquals(4, User::getAllFamilies()->count() - $start_family_count);
    }
}
