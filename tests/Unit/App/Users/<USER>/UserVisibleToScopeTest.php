<?php

namespace Tests\Unit\App\Users\Scopes;

use App\Users\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class UserVisibleToScopeTest extends TestCase
{
    use DatabaseTransactions;

    public function testGetQuery()
    {
        $user1 = $this->user();
        $user2 = $this->user();
        $user3 = $this->user($user1->account);

        $this->assertFalse(User::visibleTo($user2)->where('id', $user1->id)->exists());
        $this->assertTrue(User::visibleTo($user3)->where('id', $user1->id)->exists());
    }
}
