<?php

namespace Tests\Unit\App\Base\DTOs;

use App\Base\DTOs\DateRange;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;

class DateRangeTest extends TestCase
{
    /** @test */
    public function it_can_be_instantiated_and_dates_can_be_set()
    {
        $startString = '2024-01-01 10:00:00';
        $endString   = '2024-01-05 12:00:00';

        $dto = new DateRange();
        $dto->setStartDate(Carbon::parse($startString));
        $dto->setEndDate(Carbon::parse($endString));
        $dto->validate();

        $this->assertInstanceOf(Carbon::class, $dto->start_at);
        $this->assertEquals($startString, $dto->start_at->toDateTimeString());
        $this->assertInstanceOf(Carbon::class, $dto->end_at);
        $this->assertEquals($endString, $dto->end_at->toDateTimeString());
        $this->assertFalse($dto->hasWarnings());
        $this->assertEmpty($dto->getWarnings());
    }

    /** @test */
    public function it_can_be_instantiated_and_dates_can_be_set_with_carbon_instances()
    {
        $start = Carbon::parse('2024-02-10 08:00:00');
        $end   = Carbon::parse('2024-02-11 09:00:00');

        $dto = new DateRange();
        $dto->setStartDate($start);
        $dto->setEndDate($end);
        $dto->validate();

        $this->assertSame($start, $dto->start_at); // Should be the exact same instance
        $this->assertSame($end, $dto->end_at);     // Should be the exact same instance
        $this->assertFalse($dto->hasWarnings());
    }

    /** @test */
    public function it_throws_validation_exception_if_start_date_is_missing()
    {
        $this->expectException(ValidationException::class);

        $dto = new DateRange();
        $dto->setEndDate(Carbon::parse('2024-01-05 12:00:00'));
        $dto->validate();
    }

    /** @test */
    public function it_throws_validation_exception_if_end_date_is_missing()
    {
        $this->expectException(ValidationException::class);

        $dto = new DateRange();
        $dto->setStartDate(Carbon::parse('2024-01-01 10:00:00'));
        $dto->validate();
    }

    /** @test */
    public function it_adjusts_end_date_and_adds_warning_if_end_date_is_before_start_date()
    {
        $startString = '2024-03-15 14:00:00';
        $endString   = '2024-03-15 12:00:00'; // End is before start

        $dto = new DateRange();
        $dto->setStartDate(Carbon::parse($startString));
        $dto->setEndDate(Carbon::parse($endString));
        $dto->validate();

        $expectedEndDate = Carbon::parse($startString)->addHour();

        $this->assertEquals($expectedEndDate, $dto->end_at);
        $this->assertTrue($dto->hasWarnings());
        $this->assertCount(1, $dto->getWarnings());
        $this->assertEquals("The end date was before start date and has been adjusted to one hour after the start date.", $dto->getWarnings()[0]);
    }
}
