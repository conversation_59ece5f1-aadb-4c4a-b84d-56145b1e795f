<?php

namespace Tests\Unit\Calendars\Services;

use App\Accounts\Account;
use App\Calendars\Calendar;
use App\Calendars\Event;
use App\Calendars\EventOccurrence;
use App\Calendars\Services\UpdateEventOccurrence;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class UpdateEventOccurrenceTest extends TestCase
{
    use DatabaseTransactions;

    protected Account         $account;
    protected Calendar        $calendar;
    protected Event           $event;
    protected EventOccurrence $occurrence;

    public function setUp(): void
    {
        parent::setUp();

        $this->account    = Account::factory()->create(['timezone' => 'America/New_York']);
        $this->calendar   = Calendar::factory()->for($this->account)->create();
        $this->event      = Event::factory()
            ->for($this->account)
            ->for($this->calendar)
            ->create();
        $this->occurrence = EventOccurrence::factory()
            ->for($this->account)
            ->for($this->event)
            ->for($this->calendar)
            ->create([
                'timezone' => 'America/New_York',
                'start_at' => Carbon::parse('2025-04-15 10:00:00', 'America/New_York')->utc(), // 14:00 UTC
                'end_at'   => Carbon::parse('2025-04-15 11:00:00', 'America/New_York')->utc(), // 15:00 UTC
            ]);

        // Ensure parent event reflects the single occurrence initially
        $this->event->update([
            'start_at'    => $this->occurrence->start_at,
            'end_at'      => $this->occurrence->end_at,
            'is_all_day'  => $this->occurrence->is_all_day,
            'title'       => $this->occurrence->title,
            'location'    => $this->occurrence->location,
            'description' => $this->occurrence->description,
        ]);
    }

    /** @test */
    public function it_updates_basic_attributes()
    {
        $service           = new UpdateEventOccurrence($this->occurrence);
        $updatedOccurrence = $service->update([
            'title'       => 'Updated Title',
            'location'    => 'New Location',
            'description' => 'Updated Description',
        ]);

        $this->assertInstanceOf(EventOccurrence::class, $updatedOccurrence);
        $this->assertEquals('Updated Title', $updatedOccurrence->title);
        $this->assertEquals('New Location', $updatedOccurrence->location);
        $this->assertEquals('Updated Description', $updatedOccurrence->description);

        $this->assertDatabaseHas('calendar_event_occurrences', [
            'id'          => $this->occurrence->id,
            'title'       => 'Updated Title',
            'location'    => 'New Location',
            'description' => 'Updated Description',
        ]);
    }

    /** @test */
    public function it_updates_date_and_time_attributes_with_timezone_conversion()
    {
        $service = new UpdateEventOccurrence($this->occurrence);

        $localStartTime = Carbon::parse('2025-04-16 09:00:00', $this->account->timezone);   // 13:00 UTC
        $localEndTime   = Carbon::parse('2025-04-16 10:30:00', $this->account->timezone);   // 14:30 UTC

        // Use setter methods for date/time
        $service->setTimezone($this->account->timezone)
            ->setStartAt($localStartTime)
            ->setEndAt($localEndTime);

        $updatedOccurrence = $service->update(); // Pass empty array as attributes are set via setters

        $this->assertEquals($this->account->timezone, $updatedOccurrence->timezone);
        // Stored as UTC
        $this->assertEquals('2025-04-16 13:00:00', $updatedOccurrence->start_at->format('Y-m-d H:i:s'));
        $this->assertEquals('2025-04-16 14:30:00', $updatedOccurrence->end_at->format('Y-m-d H:i:s'));
        // Check local time helpers
        $this->assertEquals('09:00:00', $updatedOccurrence->local_start_at_time);
        $this->assertEquals('10:30:00', $updatedOccurrence->local_end_at_time);

        $this->assertDatabaseHas('calendar_event_occurrences', [
            'id'       => $this->occurrence->id,
            'timezone' => $this->account->timezone,
            'start_at' => '2025-04-16 13:00:00',
            'end_at'   => '2025-04-16 14:30:00',
        ]);
    }

    /** @test */
    public function it_updates_date_and_time_attributes_with_timezone_conversion_outside_dst()
    {
        $service = new UpdateEventOccurrence($this->occurrence);

        $localStartTime = Carbon::parse('2025-01-01 09:00:00', $this->account->timezone);   // 14:00 UTC
        $localEndTime   = Carbon::parse('2025-01-01 10:30:00', $this->account->timezone);   // 15:30 UTC

        // Use setter methods for date/time
        $service->setTimezone($this->account->timezone)
            ->setStartAt($localStartTime)
            ->setEndAt($localEndTime);

        $updatedOccurrence = $service->update(); // Pass empty array as attributes are set via setters

        $this->assertEquals($this->account->timezone, $updatedOccurrence->timezone);
        // Stored as UTC
        $this->assertEquals('2025-01-01 14:00:00', $updatedOccurrence->start_at->format('Y-m-d H:i:s'));
        $this->assertEquals('2025-01-01 15:30:00', $updatedOccurrence->end_at->format('Y-m-d H:i:s'));
        // Check local time helpers
        $this->assertEquals('09:00:00', $updatedOccurrence->local_start_at_time);
        $this->assertEquals('10:30:00', $updatedOccurrence->local_end_at_time);

        $this->assertDatabaseHas('calendar_event_occurrences', [
            'id'       => $this->occurrence->id,
            'timezone' => $this->account->timezone,
            'start_at' => '2025-01-01 14:00:00',
            'end_at'   => '2025-01-01 15:30:00',
        ]);
    }

    /** @test */
    public function it_defaults_to_account_timezone_if_not_provided()
    {
        $service = new UpdateEventOccurrence($this->occurrence);
        // Don't provide timezone in attributes
        $updatedOccurrence = $service->update(['title' => 'No Timezone Update']);

        $this->assertEquals($this->account->timezone, $updatedOccurrence->timezone);
        $this->assertDatabaseHas('calendar_event_occurrences', [
            'id'       => $this->occurrence->id,
            'timezone' => $this->account->timezone,
        ]);
    }

    /** @test */
    public function it_handles_all_day_event_update()
    {
        $service = new UpdateEventOccurrence($this->occurrence);

        $localDate = Carbon::parse('2025-04-17 12:00:00', $this->account->timezone); // Midday doesn't matter, date does
        $timezone  = $this->account->timezone;

        // Use setters
        $service->setIsAllDay(true)
            ->setTimezone($timezone)
            ->setStartAt($localDate) // Provide a start date
            ->setEndAt($localDate); // Provide an end date (will be adjusted)

        $updatedOccurrence = $service->update();

        $expectedStartUtc = Carbon::parse('2025-04-17 00:00:00', $timezone)->utc();
        $expectedEndUtc   = Carbon::parse('2025-04-17 23:59:59', $timezone)->utc();

        $this->assertTrue($updatedOccurrence->is_all_day);
        $this->assertEquals($expectedStartUtc->format('Y-m-d H:i:s'), $updatedOccurrence->start_at->format('Y-m-d H:i:s'));
        $this->assertEquals($expectedEndUtc->format('Y-m-d H:i:s'), $updatedOccurrence->end_at->format('Y-m-d H:i:s'));

        $this->assertDatabaseHas('calendar_event_occurrences', [
            'id'         => $this->occurrence->id,
            'is_all_day' => true,
            'start_at'   => $expectedStartUtc->format('Y-m-d H:i:s'),
            'end_at'     => $expectedEndUtc->format('Y-m-d H:i:s'),
        ]);
    }

    /** @test */
    public function it_updates_parent_event_when_it_is_the_only_occurrence()
    {
        // Setup ensures only one occurrence exists initially
        $this->assertEquals(1, $this->event->occurrences()->count());

        $service           = new UpdateEventOccurrence($this->occurrence);
        $updatedOccurrence = $service->update([
            'title'       => 'Parent Update Title',
            'location'    => 'Parent Location',
            'description' => 'Parent Description',
            'is_all_day'  => false, // Ensure this is updated too
            'start_at'    => Carbon::parse('2025-04-18 11:00:00', $this->account->timezone),
            'end_at'      => Carbon::parse('2025-04-18 12:00:00', $this->account->timezone),
            'timezone'    => $this->account->timezone,
        ]);

        // Refresh parent event model
        $this->event->refresh();

        $this->assertEquals('Parent Update Title', $this->event->title);
        $this->assertEquals('Parent Location', $this->event->location);
        $this->assertEquals('Parent Description', $this->event->description);
        $this->assertFalse($this->event->is_all_day);
        $this->assertEquals($updatedOccurrence->start_at->format('Y-m-d H:i:s'), $this->event->start_at->format('Y-m-d H:i:s'));
        $this->assertEquals($updatedOccurrence->end_at->format('Y-m-d H:i:s'), $this->event->end_at->format('Y-m-d H:i:s'));

        $this->assertDatabaseHas('calendar_events', [
            'id'          => $this->event->id,
            'title'       => 'Parent Update Title',
            'location'    => 'Parent Location',
            'description' => 'Parent Description',
            'is_all_day'  => false,
            'start_at'    => $updatedOccurrence->start_at->format('Y-m-d H:i:s'),
            'end_at'      => $updatedOccurrence->end_at->format('Y-m-d H:i:s'),
        ]);
    }

    /** @test */
    public function it_does_not_update_parent_event_when_multiple_occurrences_exist()
    {
        // Create a second occurrence for the same event
        $secondOccurrence = EventOccurrence::factory()
            ->for($this->account)
            ->for($this->event)
            ->for($this->calendar)
            ->create([
                'timezone' => $this->occurrence->timezone,
                'start_at' => $this->occurrence->start_at->addDay(),
                'end_at'   => $this->occurrence->end_at->addDay(),
            ]);

        $this->assertEquals(2, $this->event->occurrences()->count());

        // Get original parent event data
        $originalEventData = $this->event->only(['title', 'location', 'description', 'is_all_day', 'start_at', 'end_at']);

        $service = new UpdateEventOccurrence($this->occurrence); // Update the first occurrence
        $service->update([
            'title'    => 'Multi Occurrence Update',
            'location' => 'Multi Location',
        ]);

        // Refresh parent event model
        $this->event->refresh();

        // Assert parent event data hasn't changed
        $this->assertEquals($originalEventData['title'], $this->event->title);
        $this->assertEquals($originalEventData['location'], $this->event->location);
        $this->assertEquals($originalEventData['description'], $this->event->description);
        $this->assertEquals($originalEventData['is_all_day'], $this->event->is_all_day);
        $this->assertEquals($originalEventData['start_at']->format('Y-m-d H:i:s'), $this->event->start_at->format('Y-m-d H:i:s'));
        $this->assertEquals($originalEventData['end_at']->format('Y-m-d H:i:s'), $this->event->end_at->format('Y-m-d H:i:s'));

        $this->assertDatabaseHas('calendar_events', [
            'id'    => $this->event->id,
            'title' => $originalEventData['title'], // Should still be the original title
        ]);
        $this->assertDatabaseMissing('calendar_events', [
            'id'    => $this->event->id,
            'title' => 'Multi Occurrence Update', // Should not have been updated to this
        ]);
    }

    /** @test */
    public function it_only_updates_allowed_attributes()
    {
        $service           = new UpdateEventOccurrence($this->occurrence);
        $updatedOccurrence = $service->update([
            'title'                  => 'Allowed Update',
            'some_invalid_attribute' => 'should_not_be_saved', // Not in $limited_attributes
            'account_id'             => 999, // Also not directly updatable this way
        ]);

        $this->assertEquals('Allowed Update', $updatedOccurrence->title);
        $this->assertNotEquals(999, $updatedOccurrence->account_id); // Ensure account_id didn't change

        $this->assertDatabaseHas('calendar_event_occurrences', [
            'id'    => $this->occurrence->id,
            'title' => 'Allowed Update',
        ]);
        $this->assertDatabaseMissing('calendar_event_occurrences', [
            'id'         => $this->occurrence->id,
            // Cannot directly check for absence of a column, but ensure title was updated and others weren't changed unexpectedly
            'account_id' => 999,
        ]);
    }
}

