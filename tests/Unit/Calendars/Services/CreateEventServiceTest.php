<?php

namespace Tests\Unit\Calendars\Services;

use App\Calendars\Event;
use App\Calendars\EventOccurrence;
use App\Calendars\Services\CreateCalendar;
use App\Calendars\Services\CreateEvent;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class CreateEventServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected $admin;
    protected $calendar;

    public function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->superUser();

        $this->calendar = (new CreateCalendar())
            ->forAccount($this->admin->account)
            ->create([
                'name'      => 'Test Calendar',
                'is_public' => true,
                'is_hidden' => false,
            ]);
    }

    /** @test */
    public function it_creates_a_basic_event_with_required_fields()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(11, 0, 0);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Test Event')
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('Test Event', $event->title);
        $this->assertEquals('test-event', $event->url_title);
        $this->assertEquals($this->calendar->id, $event->calendar_id);
        $this->assertEquals($this->admin->account->id, $event->account_id);
        $this->assertEquals($this->admin->id, $event->created_by_user_id);
        $this->assertEquals($this->admin->account->timezone, $event->timezone);
        $this->assertFalse($event->is_all_day);
        $this->assertFalse((bool)$event->is_recurring);

        // Check that the event has one occurrence
        $this->assertEquals(1, $event->occurrences()->count());
    }

    /** @test */
    public function it_creates_an_event_with_all_optional_fields()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(11, 0, 0);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Test Event with Options')
            ->setOverview('Event overview text')
            ->setDescription('Detailed event description')
            ->setLocation('Event Location')
            ->setIsAllDay(false)
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('Test Event with Options', $event->title);
        $this->assertEquals('test-event-with-options', $event->url_title);
        $this->assertEquals('Event overview text', $event->overview);
        $this->assertEquals('Detailed event description', $event->description);
        $this->assertEquals('Event Location', $event->location);
        $this->assertFalse($event->is_all_day);

        // Check that the event has one occurrence
        $this->assertEquals(1, $event->occurrences()->count());

        // Check that the occurrence has the same data
        $occurrence = $event->occurrences()->first();
        $this->assertEquals($event->title, $occurrence->title);
        $this->assertEquals($event->overview, $occurrence->overview);
        $this->assertEquals($event->description, $occurrence->description);
        $this->assertEquals($event->location, $occurrence->location);
    }

    /** @test */
    public function it_creates_an_all_day_event()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(0, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(23, 59, 59);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('All Day Event')
            ->setIsAllDay(true)
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('All Day Event', $event->title);
        $this->assertTrue($event->is_all_day);

        // Check that the event has one occurrence
        $this->assertEquals(1, $event->occurrences()->count());

        // Check that the occurrence is also an all-day event
        $occurrence = $event->occurrences()->first();
        $this->assertTrue($occurrence->is_all_day);
    }

    /** @test */
    public function it_creates_a_multi_day_event()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 3)
            ->setTime(16, 0, 0);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Multi-day Event')
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('Multi-day Event', $event->title);

        // Check start and end dates are different
        $this->assertNotEquals(
            $event->start_at->format('Y-m-d'),
            $event->end_at->format('Y-m-d')
        );

        // Check that the event spans 3 days
        $this->assertEquals(2, round($event->end_at->diffInDays($event->start_at, true)));
    }

    /** @test */
    public function it_handles_timezone_conversion_correctly()
    {
        // Use a specific timezone for testing
        $timezone = 'America/New_York';
        $this->admin->account->update(['timezone' => $timezone]);

        // Create a time in the account timezone
        $start_at_local = Carbon::parse('2023-01-15 14:30:00', $timezone);
        $end_at_local   = Carbon::parse('2023-01-15 15:45:00', $timezone);

        // Expected UTC times
        $expected_start_utc = (clone $start_at_local)->setTimezone('UTC');
        $expected_end_utc   = (clone $end_at_local)->setTimezone('UTC');

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at_local->setTimezone('UTC'))
            ->setEndAt($end_at_local->setTimezone('UTC'))
            ->setTitle('Timezone Test Event')
            ->create();

        $this->assertInstanceOf(Event::class, $event);

        // Check that times are stored in UTC
        $this->assertEquals($expected_start_utc->format('Y-m-d H:i:s'), $event->start_at->format('Y-m-d H:i:s'));
        $this->assertEquals($expected_end_utc->format('Y-m-d H:i:s'), $event->end_at->format('Y-m-d H:i:s'));

        // Check local times are set correctly
        $this->assertEquals('14:30:00', $event->local_start_at_time);
        $this->assertEquals('15:45:00', $event->local_end_at_time);
    }

    /** @test */
    public function it_creates_a_daily_recurring_event()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(11, 0, 0);

        $recur_end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 10)
            ->setTime(10, 0, 0);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Daily Recurring Event')
            ->setRecurFrequency('DAILY')
            ->setRecurInterval(1)
            ->setRecurEndAt($recur_end_at->setTimezone('UTC'))
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('Daily Recurring Event', $event->title);
        $this->assertTrue($event->is_recurring);
        $this->assertEquals('DAILY', $event->recur_frequency);
        $this->assertEquals(1, $event->recur_interval);
        $this->assertNotNull($event->rrule);

        // Check that multiple occurrences were created (10 days)
        $this->assertEquals(10, $event->occurrences()->count());
    }

    /** @test */
    public function it_creates_a_weekly_recurring_event()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(11, 0, 0);

        $recur_end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 29)
            ->setTime(23, 59, 59);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Weekly Recurring Event')
            ->setRecurFrequency('WEEKLY')
            ->setRecurInterval(1)
            ->setRecurByDay(['MO', 'WE', 'FR']) // Monday, Wednesday, Friday
            ->setRecurEndAt($recur_end_at->setTimezone('UTC'))
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('Weekly Recurring Event', $event->title);
        $this->assertTrue($event->is_recurring);
        $this->assertEquals('WEEKLY', $event->recur_frequency);
        $this->assertEquals(1, $event->recur_interval);
        $this->assertEquals('MO,WE,FR', $event->recur_by_day);
        $this->assertNotNull($event->rrule);

        // The exact count will depend on the calendar, but we should have multiple occurrences
        $this->assertGreaterThan(1, $event->occurrences()->count());
    }

    /** @test */
    public function it_creates_a_monthly_recurring_event()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 15)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 15)
            ->setTime(11, 0, 0);

        $recur_end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 12, 31)
            ->setTime(23, 59, 59);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Monthly Recurring Event')
            ->setRecurFrequency('MONTHLY')
            ->setRecurInterval(1)
            ->setRecurByMonthDay(15) // 15th of each month
            ->setRecurEndAt($recur_end_at->setTimezone('UTC'))
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('Monthly Recurring Event', $event->title);
        $this->assertTrue($event->is_recurring);
        $this->assertEquals('MONTHLY', $event->recur_frequency);
        $this->assertEquals(1, $event->recur_interval);
        $this->assertEquals(15, $event->recur_by_day_of_month);
        $this->assertNotNull($event->rrule);

        // Should have 12 occurrences (one per month for a year)
        $this->assertEquals(12, $event->occurrences()->count());
    }

    /** @test */
    public function it_creates_a_yearly_recurring_event()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 3, 15)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 3, 15)
            ->setTime(11, 0, 0);

        $recur_end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y') + 5, 12, 31) // 5 years from now
            ->setTime(23, 59, 59);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Yearly Recurring Event')
            ->setRecurFrequency('YEARLY')
            ->setRecurInterval(1)
            ->setRecurEndAt($recur_end_at->setTimezone('UTC'))
            ->create();

        $this->assertInstanceOf(Event::class, $event);
        $this->assertEquals('Yearly Recurring Event', $event->title);
        $this->assertTrue($event->is_recurring);
        $this->assertEquals('YEARLY', $event->recur_frequency);
        $this->assertEquals(1, $event->recur_interval);
        $this->assertNotNull($event->rrule);

        // Should have 6 occurrences (one per year for 6 years including current year)
        $this->assertEquals(6, $event->occurrences()->count());
    }

    /** @test */
    public function it_throws_exception_when_required_fields_are_missing()
    {
        $this->expectException(\Exception::class);

        // Missing title
        (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt(now())
            ->setEndAt(now()->addHour())
            ->create();
    }

    /** @test */
    public function it_sets_url_title_from_title_if_not_provided()
    {
        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt(now()->setTimezone('UTC'))
            ->setEndAt(now()->addHour()->setTimezone('UTC'))
            ->setTitle('Special Event: 2023!')
            ->create();

        $this->assertEquals('special-event-2023', $event->url_title);
    }

    /** @test */
    public function it_creates_event_occurrences_correctly()
    {
        $start_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(10, 0, 0);

        $end_at = now()->setTimezone($this->admin->account->timezone)
            ->setDate(now()->format('Y'), 1, 1)
            ->setTime(11, 0, 0);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar)
            ->createdByUser($this->admin)
            ->setStartAt($start_at->setTimezone('UTC'))
            ->setEndAt($end_at->setTimezone('UTC'))
            ->setTitle('Event with Occurrence')
            ->setDescription('Test Description')
            ->setLocation('Test Location')
            ->create();

        $this->assertEquals(1, $event->occurrences()->count());

        $occurrence = $event->occurrences()->first();

        $this->assertInstanceOf(EventOccurrence::class, $occurrence);
        $this->assertEquals($event->id, $occurrence->calendar_event_id);
        $this->assertEquals($event->account_id, $occurrence->account_id);
        $this->assertEquals($event->calendar_id, $occurrence->calendar_id);
        $this->assertEquals($event->title, $occurrence->title);
        $this->assertEquals($event->description, $occurrence->description);
        $this->assertEquals($event->location, $occurrence->location);
        $this->assertEquals($event->start_at->format('Y-m-d H:i:s'), $occurrence->start_at->format('Y-m-d H:i:s'));
        $this->assertEquals($event->end_at->format('Y-m-d H:i:s'), $occurrence->end_at->format('Y-m-d H:i:s'));
    }
}
