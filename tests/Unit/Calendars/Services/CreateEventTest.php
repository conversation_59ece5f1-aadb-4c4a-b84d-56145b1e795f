<?php

namespace Tests\Unit\Calendars\Services;

use App\Calendars\Services\CreateCalendar;
use App\Calendars\Services\CreateEvent;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class CreateEventTest extends TestCase
{
    use DatabaseTransactions;

    public $admin;
    public $calendar1;

    public function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->superUser();

        $this->calendar1 = (new CreateCalendar())
            ->forAccount($this->admin->account)
            ->create([
                'name'      => 'Calendar 1',
                'is_public' => true,
                'is_hidden' => false,
            ]);
    }

    public function test_create_1hour_early_calendar_event()
    {
        // Get the current time in the user's timezone. Set the year to the current year, Month to Jan, Day to 1, and the time to 9:00 AM.
        $start_at_in_user_timezone = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(3, 00, 00);
        $end_at_in_user_timezone   = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(4, 00, 00);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar1)
            ->createdByUser($this->admin)
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt($start_at_in_user_timezone->setTimezone('UTC'))
            ->setEndAt($end_at_in_user_timezone->setTimezone('UTC'))
            ->setTitle('Title 01')
            ->setOverview('Overview 01')
            ->setDescription('Description 01')
            ->setIsAllDay(false)
            ->create();

        $this->assertEquals('Title 01', $event->title);
        $this->assertEquals('America/Chicago', $event->timezone);
        $this->assertFalse($event->is_all_day);
        $this->assertEquals(now()->format('Y') . '-01-01', $event->start_at_date->format('Y-m-d'));
        $this->assertEquals(now()->format('Y') . '-01-01', $event->end_at_date->format('Y-m-d'));
        $this->assertEquals('09:00:00', $event->start_at->format('H:i:s'), 'Test start_at DATETIME has correct time.');
        $this->assertEquals('09:00:00', $event->start_at_time->format('H:i:s'), 'Test start_at_time TIME has correct time.');
        $this->assertEquals('10:00:00', $event->end_at->format('H:i:s'), 'Test end_at DATETIME has correct time.');
        $this->assertEquals('10:00:00', $event->end_at_time->format('H:i:s'), 'Test end_at_time TIME has correct time.');
    }

    public function test_create_1hour_midday_calendar_event()
    {
        // Get the current time in the user's timezone. Set the year to the current year, Month to Jan, Day to 1, and the time to 9:00 AM.
        $start_at_in_user_timezone = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(13, 00, 00);
        $end_at_in_user_timezone   = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(14, 00, 00);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar1)
            ->createdByUser($this->admin)
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt($start_at_in_user_timezone->setTimezone('UTC'))
            ->setEndAt($end_at_in_user_timezone->setTimezone('UTC'))
            ->setTitle('Title 02')
            ->setOverview('Overview 02')
            ->setDescription('Description 02')
            ->setIsAllDay(false)
            ->create();

        $this->assertEquals('Title 02', $event->title);
        $this->assertEquals('America/Chicago', $event->timezone);
        $this->assertFalse($event->is_all_day);
        $this->assertEquals(now()->format('Y') . '-01-01', $event->start_at_date->format('Y-m-d'));
        $this->assertEquals(now()->format('Y') . '-01-01', $event->end_at_date->format('Y-m-d'));
        $this->assertEquals('19:00:00', $event->start_at->format('H:i:s'), 'Test start_at DATETIME has correct time.');
        $this->assertEquals('19:00:00', $event->start_at_time->format('H:i:s'), 'Test start_at_time TIME has correct time.');
        $this->assertEquals('20:00:00', $event->end_at->format('H:i:s'), 'Test end_at DATETIME has correct time.');
        $this->assertEquals('20:00:00', $event->end_at_time->format('H:i:s'), 'Test end_at_time TIME has correct time.');
    }

    public function test_create_1hour_late_calendar_event()
    {
        // Get the current time in the user's timezone. Set the year to the current year, Month to Jan, Day to 1, and the time to 9:00 AM.
        $start_at_in_user_timezone = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(20, 00, 00);
        $end_at_in_user_timezone   = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(21, 00, 00);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar1)
            ->createdByUser($this->admin)
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt($start_at_in_user_timezone->setTimezone('UTC'))
            ->setEndAt($end_at_in_user_timezone->setTimezone('UTC'))
            ->setTitle('Title 03')
            ->setOverview('Overview 03')
            ->setDescription('Description 03')
            ->setIsAllDay(false)
            ->create();

        $this->assertEquals('Title 03', $event->title);
        $this->assertEquals('America/Chicago', $event->timezone);
        $this->assertFalse($event->is_all_day);
        $this->assertEquals(now()->format('Y') . '-01-02', $event->start_at_date->format('Y-m-d'));
        $this->assertEquals(now()->format('Y') . '-01-02', $event->end_at_date->format('Y-m-d'));
        $this->assertEquals('02:00:00', $event->start_at->format('H:i:s'), 'Test start_at DATETIME has correct time.');
        $this->assertEquals('02:00:00', $event->start_at_time->format('H:i:s'), 'Test start_at_time TIME has correct time.');
        $this->assertEquals('03:00:00', $event->end_at->format('H:i:s'), 'Test end_at DATETIME has correct time.');
        $this->assertEquals('03:00:00', $event->end_at_time->format('H:i:s'), 'Test end_at_time TIME has correct time.');
    }

    public function test_create_1day_calendar_event()
    {
        $start_at_in_user_timezone = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(9, 00, 00);
        $end_at_in_user_timezone   = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 2)->setTime(10, 00, 00);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar1)
            ->createdByUser($this->admin)
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt($start_at_in_user_timezone->setTimezone('UTC'))
            ->setEndAt($end_at_in_user_timezone->setTimezone('UTC'))
            ->setTitle('Title 04')
            ->setOverview('Overview 04')
            ->setDescription('Description 04')
            ->setIsAllDay(true)
            ->create();

        $this->assertEquals('Title 04', $event->title);
        $this->assertEquals('America/Chicago', $event->timezone);
        $this->assertTrue($event->is_all_day);
        $this->assertEquals(now()->format('Y') . '-01-01', $event->start_at_date->format('Y-m-d'));
        $this->assertEquals(now()->format('Y') . '-01-02', $event->end_at_date->format('Y-m-d'));
        $this->assertEquals('15:00:00', $event->start_at->format('H:i:s'), 'Test start_at DATETIME has correct time.');
        $this->assertEquals('15:00:00', $event->start_at_time->format('H:i:s'), 'Test start_at_time TIME has correct time.');
        $this->assertEquals('16:00:00', $event->end_at->format('H:i:s'), 'Test end_at DATETIME has correct time.');
        $this->assertEquals('16:00:00', $event->end_at_time->format('H:i:s'), 'Test end_at_time TIME has correct time.');
    }

    public function test_create_3day_calendar_event()
    {
        $start_at_in_user_timezone = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 1)->setTime(9, 00, 00);
        $end_at_in_user_timezone   = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), 1, 4)->setTime(10, 00, 00);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar1)
            ->createdByUser($this->admin)
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt($start_at_in_user_timezone->setTimezone('UTC'))
            ->setEndAt($end_at_in_user_timezone->setTimezone('UTC'))
            ->setTitle('Title 05')
            ->setOverview('Overview 05')
            ->setDescription('Description 05')
            ->setIsAllDay(true)
            ->create();

        $this->assertEquals('Title 05', $event->title);
        $this->assertEquals('America/Chicago', $event->timezone);
        $this->assertTrue($event->is_all_day);
        $this->assertEquals(now()->format('Y') . '-01-01', $event->start_at_date->format('Y-m-d'));
        $this->assertEquals(now()->format('Y') . '-01-04', $event->end_at_date->format('Y-m-d'));
        $this->assertEquals('15:00:00', $event->start_at->format('H:i:s'), 'Test start_at DATETIME has correct time.');
        $this->assertEquals('15:00:00', $event->start_at_time->format('H:i:s'), 'Test start_at_time TIME has correct time.');
        $this->assertEquals('16:00:00', $event->end_at->format('H:i:s'), 'Test end_at DATETIME has correct time.');
        $this->assertEquals('16:00:00', $event->end_at_time->format('H:i:s'), 'Test end_at_time TIME has correct time.');
    }
}
