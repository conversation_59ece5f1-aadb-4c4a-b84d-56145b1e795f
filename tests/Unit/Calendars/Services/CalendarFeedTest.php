<?php

namespace Tests\Unit\Calendars\Services;

use App\Calendars\Services\CalendarFeed;
use App\Calendars\Services\CreateCalendar;
use App\Calendars\Services\CreateEvent;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class CalendarFeedTest extends TestCase
{
    use DatabaseTransactions;

    public $admin;
    public $calendar1;

    public function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->superUser();

        $this->calendar1 = (new CreateCalendar())
            ->forAccount($this->admin->account)
            ->create([
                'name'      => 'Calendar 001',
                'is_public' => true,
                'is_hidden' => false,
            ]);
    }

    public function test_ical_feed_is_accurate_allday_event()
    {
        $start_at_in_user_timezone = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), now()->format('m'), now()->format('d'))->setTime(3, 00, 00);
        $end_at_in_user_timezone   = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), now()->format('m'), now()->format('d'))->setTime(4, 00, 00)
            ->addDays(3);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar1)
            ->createdByUser($this->admin)
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt($start_at_in_user_timezone->setTimezone('UTC'))
            ->setEndAt($end_at_in_user_timezone->setTimezone('UTC'))
            ->setTitle('Title 001')
            ->setOverview('Overview 001')
            ->setDescription('Description 001')
            ->setIsAllDay(true)
            ->create();

        $feed = (new CalendarFeed())
            ->forCalendar($this->calendar1)
            ->forUser($this->admin)
            ->getiCalFeed();

        $this->assertNotEmpty($feed);
        $this->assertStringContainsString('DTSTART;VALUE=DATE:********', $feed);
        $this->assertStringContainsString('DTEND;VALUE=DATE:20240405', $feed);
        $this->assertEquals('Title 001', $event->title);
        $this->assertEquals('America/Chicago', $event->timezone);
        $this->assertEquals(now()->format('Y-m-d'), $event->start_at_date->format('Y-m-d'));
        $this->assertEquals(now()->addDays(3)->format('Y-m-d'), $event->end_at_date->format('Y-m-d'));
        $this->assertEquals('08:00:00', $event->start_at->format('H:i:s'), 'Test start_at DATETIME has correct time.');
        $this->assertEquals('08:00:00', $event->start_at_time->format('H:i:s'), 'Test start_at_time TIME has correct time.');
        $this->assertEquals('09:00:00', $event->end_at->format('H:i:s'), 'Test end_at DATETIME has correct time.');
        $this->assertEquals('09:00:00', $event->end_at_time->format('H:i:s'), 'Test end_at_time TIME has correct time.');
    }

    public function test_ical_feed_is_accurate_1day_event()
    {
        $start_at_in_user_timezone = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), now()->format('m'), now()->format('d'))->setTime(3, 00, 00);
        $end_at_in_user_timezone   = now()->setTimezone($this->admin->account->timezone)->setDate(now()->format('Y'), now()->format('m'), now()->format('d'))->setTime(4, 00, 00);

        $event = (new CreateEvent())
            ->forCalendar($this->calendar1)
            ->createdByUser($this->admin)
            // We set our Timezone here, before we hand it to the CreateEvent service.
            ->setStartAt($start_at_in_user_timezone->setTimezone('UTC'))
            ->setEndAt($end_at_in_user_timezone->setTimezone('UTC'))
            ->setTitle('Title 002')
            ->setOverview('Overview 002')
            ->setDescription('Description 002')
            ->setIsAllDay(false)
            ->create();

        $feed = (new CalendarFeed())
            ->forCalendar($this->calendar1)
            ->forUser($this->admin)
            ->getiCalFeed();

        $this->assertNotEmpty($feed);
        $this->assertStringContainsString('DTSTART:********T080000Z', $feed);
        $this->assertStringContainsString('DTEND:********T090000Z', $feed);
        $this->assertEquals('Title 002', $event->title);
        $this->assertEquals('America/Chicago', $event->timezone);
        $this->assertEquals(now()->format('Y-m-d'), $event->start_at_date->format('Y-m-d'));
        $this->assertEquals(now()->format('Y-m-d'), $event->end_at_date->format('Y-m-d'));
        $this->assertEquals('08:00:00', $event->start_at->format('H:i:s'), 'Test start_at DATETIME has correct time.');
        $this->assertEquals('08:00:00', $event->start_at_time->format('H:i:s'), 'Test start_at_time TIME has correct time.');
        $this->assertEquals('09:00:00', $event->end_at->format('H:i:s'), 'Test end_at DATETIME has correct time.');
        $this->assertEquals('09:00:00', $event->end_at_time->format('H:i:s'), 'Test end_at_time TIME has correct time.');
    }
}
