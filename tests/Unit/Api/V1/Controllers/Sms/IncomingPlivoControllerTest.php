<?php

namespace Tests\Unit\Api\V1\Controllers\Sms;

use App\Api\V1\Controllers\Sms\IncomingPlivoController;
use App\Users\Phone;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class IncomingPlivoControllerTest extends TestCase
{
    use DatabaseTransactions;

    public function testOptUserOut()
    {
        $admin = $this->superUser();
        $phone = Phone::create([
            'number'  => '2815554321',
            'user_id' => $admin->id,
        ]);

        $controller = new IncomingPlivoController();

        $result = $controller->optUserOut($admin, '12815554321');

        $phone->refresh();

        $this->assertTrue($result);
        $this->assertTrue((bool)$phone->messages_opt_out);
    }

    public function testCheckIfOptOut()
    {
        $controller = new IncomingPlivoController();

        $result  = $controller->checkIfOptOut('STOP');
        $result2 = $controller->checkIfOptOut(' SPam');
        $result3 = $controller->checkIfOptOut('  stop');
        $result4 = $controller->checkIfOptOut(' sTop ');

        $this->assertTrue($result);
        $this->assertTrue($result2);
        $this->assertTrue($result3);
        $this->assertTrue($result4);
    }

    public function testCheckIfNotOptOut()
    {
        $controller = new IncomingPlivoController();

        $result = $controller->checkIfOptOut('This is just a reply.');

        $this->assertSame(false, $result);
    }

    public function testGetUserFromPhone()
    {
        $admin = $this->superUser();

        $controller = new IncomingPlivoController();

        $result = $controller->getUserFromPhone($admin->getBestPhone()->number);

        $this->assertSame($admin->id, $result->id);
    }
}
