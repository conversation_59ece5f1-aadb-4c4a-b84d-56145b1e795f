<?php

namespace Tests\Unit\Groups\Services;

use App\Events\Groups\CommentCreatedBroadcast;
use App\Events\Groups\PostCreatedBroadcast;
use App\Groups\Services\CreatePost;
use App\Listeners\Groups\SendCommentCreatedNotification;
use App\Listeners\Groups\SendPostCreatedNotifications;
use App\Users\Services\CreateGroup;
use App\Users\Services\UpdateUser;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class CreatePostTest extends TestCase
{
    use DatabaseTransactions;

    // Verify that Broadcasting a Post and Comment are being monitored so we send mobile notifications also.
    public function test_notifications_sent()
    {
        Event::fake();

        $user  = $this->user();
        $user2 = $this->user();

        $group = (new CreateGroup())
            ->forUser($user)
            ->create([
                'name' => 'Group 1',
            ]);

        // Add a second user to this group who should get a notificat
        (new UpdateUser($user2))
            ->addGroupsById([$group->id])
            ->update();
        (new UpdateUser($user2))
            ->updateExistingGroup($group->id, [
                'receive_group_emails'                                => 1,
                'receive_group_sms'                                   => 1,
                'receive_group_voice'                                 => 1,
                'receive_group_post_email_summaries'                  => 1,
                'receive_group_post_mobile_notifications'             => 1,
                'receive_all_group_post_comment_mobile_notifications' => 1,
                'receive_group_own_post_comment_mobile_notifications' => 1,
            ])
            ->update([]);

        $post = (new CreatePost())
            ->createdBy($user)
            ->forGroup($group)
            ->create([
                'content' => 'This is a test.',
            ]);

        Event::assertListening(PostCreatedBroadcast::class, SendPostCreatedNotifications::class);
        Event::assertListening(CommentCreatedBroadcast::class, SendCommentCreatedNotification::class);
    }
}
