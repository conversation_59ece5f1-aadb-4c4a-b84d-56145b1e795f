<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Stripe, Mailgun, SparkPost and others. This file provides a sane
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
    ],

    'ses' => [
        'key'    => env('SES_KEY'),
        'secret' => env('SES_SECRET'),
        'region' => 'us-east-1',
    ],

    'sparkpost' => [
        'secret' => env('SPARKPOST_SECRET', null),
    ],

    'stripe' => [
        'model'   => App\Users\User::class,
        'key'     => env('STRIPE_KEY'),
        'secret'  => env('STRIPE_SECRET'),
        'connect' => [
            'key'                    => env('STRIPE_CONNECT_ACCOUNT_KEY'),
            'publishable_key'        => env('STRIPE_CONNECT_ACCOUNT_KEY'),
            'secret'                 => env('STRIPE_CONNECT_ACCOUNT_SECRET'),
            'secret_key'             => env('STRIPE_CONNECT_ACCOUNT_SECRET'),
            'reporting_test_key'     => env('STRIPE_CONNECT_ACCOUNT_REPORTING_TEST_KEY'),
            'client_id'              => env('STRIPE_CONNECT_CLIENT_ID'),
            'webhook_signing_secret' => env('STRIPE_CONNECT_WEBHOOK_SIGNING_SECRET'),
        ],
    ],

    'paddle' => [
        'key'                => env('PADDLE_KEY'),
        'secret'             => env('PADDLE_SECRET'),
        'webhook_secret_key' => env('PADDLE_WEBHOOK_SECRET_KEY'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
    ],

    'ipinfo' => [
        'token' => env('IPINFO_TOKEN'),
    ],

    'expo' => [
        'access_token' => env('EXPO_ACCESS_TOKEN'),
    ],

    'logsnag' => [
        'api_key' => env('LOGSNAG_API_KEY'),
    ],

    'zoho' => [
        'zepto' => [
            'api_key' => env('ZOHO_ZEPTO_API_KEY'),
        ],
    ],

    'church_directory' => [
        'domains' => [
            'api' => env('CHURCH_DIRECTORY_API_DOMAIN'),
        ],
    ],

    'cloudflare' => [
        'site_key'   => env('CLOUDFLARE_SITE_KEY'),
        'secret_key' => env('CLOUDFLARE_SECRET_KEY'),
    ],
];
