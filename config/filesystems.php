<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DRIVER', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Default Cloud Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Many applications store files both locally and in the cloud. For this
    | reason, you may specify a default "cloud" driver here. This driver
    | will be bound as the Cloud disk implementation in the container.
    |
    */

    'cloud' => env('FILESYSTEM_CLOUD', 's3'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been setup for each driver as an example of the required options.
    |
    | Supported Drivers: "local", "ftp", "s3", "rackspace"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root'   => storage_path('app'),
            'throw'  => true,
        ],

        'public' => [
            'driver'     => 'local',
            'root'       => storage_path('app/public'),
            'url'        => env('APP_URL') . '/storage',
            'visibility' => 'public',
            'throw'      => true,
        ],

        'temporary-file-uploads-local' => [
            'driver'     => 'local',
            'root'       => storage_path('app/public/uploads'),
            'url'        => env('APP_URL') . '/storage/user-uploads',
            'visibility' => 'public',
            'throw'      => true,
        ],

        's3' => [
            'driver' => 's3',
            'key'    => env('AWS_KEY'),
            'secret' => env('AWS_SECRET'),
            'region' => env('AWS_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'throw'  => true,
        ],

        'temporary-file-uploads' => [
            'driver'       => 's3',
            'endpoint'     => env('TEMP_FILE_UPLOADS_ENDPOINT'),
            'url_endpoint' => env('TEMP_FILE_UPLOADS_URL_ENDPOINT'),
            'cdn_endpoint' => env('TEMP_FILE_UPLOADS_CDN_ENDPOINT'),
            'key'          => env('TEMP_FILE_UPLOADS_KEY'),
            'secret'       => env('TEMP_FILE_UPLOADS_SECRET'),
            'region'       => env('TEMP_FILE_UPLOADS_REGION'),
            'bucket'       => env('TEMP_FILE_UPLOADS_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'temp-files' => [
            'driver'       => 's3',
            'endpoint'     => env('TEMP_FILES_ENDPOINT'),
            'url_endpoint' => env('TEMP_FILES_URL_ENDPOINT'),
            'cdn_endpoint' => env('TEMP_FILES_CDN_ENDPOINT'),
            'key'          => env('TEMP_FILES_KEY'),
            'secret'       => env('TEMP_FILES_SECRET'),
            'region'       => env('TEMP_FILES_REGION'),
            'bucket'       => env('TEMP_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'user-images' => [
            'driver'       => 's3',
            'endpoint'     => 'https://' . env('DO_USER_IMAGES_REGION') . '.digitaloceanspaces.com',
            'url_endpoint' => 'https://' . env('LINODE_PODCAST_FILES_BUCKET') . '.' . env('LINODE_PODCAST_FILES_REGION') . '.linodeobjects.com',
            'cdn_endpoint' => 'https://' . env('LINODE_PODCAST_FILES_BUCKET') . '.' . env('LINODE_PODCAST_FILES_REGION') . '.linodeobjects.com',
            'key'          => env('DO_USER_IMAGES_KEY'),
            'secret'       => env('DO_USER_IMAGES_SECRET'),
            'region'       => env('DO_USER_IMAGES_REGION'),
            'bucket'       => env('DO_USER_IMAGES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'public-cdn' => [
            'driver'                  => 's3',
            'endpoint'                => env('PUBLIC_CDN_S3_ENDPOINT'),
            'url'                     => env('PUBLIC_CDN_URL_ENDPOINT'),
            'url_endpoint'            => env('PUBLIC_CDN_URL_ENDPOINT'),
            'cdn_endpoint'            => env('PUBLIC_CDN_CDN_ENDPOINT'),
            'key'                     => env('PUBLIC_CDN_KEY'),
            'secret'                  => env('PUBLIC_CDN_SECRET'),
            'region'                  => env('PUBLIC_CDN_REGION'),
            'bucket'                  => env('PUBLIC_CDN_BUCKET'),
            'visibility'              => 'public',
            'throw'                   => true,
            'use_path_style_endpoint' => true,
        ],

        'user-files' => [
            'driver'       => 's3',
            'endpoint'     => 'https://' . env('DO_USER_FILES_REGION') . '.digitaloceanspaces.com',
            'url_endpoint' => 'https://' . env('LINODE_PODCAST_FILES_BUCKET') . '.' . env('LINODE_PODCAST_FILES_REGION') . '.linodeobjects.com',
            'cdn_endpoint' => 'https://' . env('LINODE_PODCAST_FILES_BUCKET') . '.' . env('LINODE_PODCAST_FILES_REGION') . '.linodeobjects.com',
            'key'          => env('DO_USER_FILES_KEY'),
            'secret'       => env('DO_USER_FILES_SECRET'),
            'region'       => env('DO_USER_FILES_REGION'),
            'bucket'       => env('DO_USER_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'sermon-files' => [
            'driver'       => 's3',
            'endpoint'     => env('SERMON_FILES_ENDPOINT'),
            'url_endpoint' => env('SERMON_FILES_URL_ENDPOINT'),
            'cdn_endpoint' => env('SERMON_FILES_CDN_ENDPOINT'),
            'key'          => env('SERMON_FILES_KEY'),
            'secret'       => env('SERMON_FILES_SECRET'),
            'region'       => env('SERMON_FILES_REGION'),
            'bucket'       => env('SERMON_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'group-files' => [
            'driver'       => 's3',
            'endpoint'     => env('GROUP_FILES_ENDPOINT'),
            'url_endpoint' => env('GROUP_FILES_URL_ENDPOINT'),
            'cdn_endpoint' => env('GROUP_FILES_CDN_ENDPOINT'),
            'key'          => env('GROUP_FILES_KEY'),
            'secret'       => env('GROUP_FILES_SECRET'),
            'region'       => env('GROUP_FILES_REGION'),
            'bucket'       => env('GROUP_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'account-files' => [
            'driver'       => 's3',
            'endpoint'     => 'https://' . env('DO_ACCOUNT_FILES_REGION') . '.digitaloceanspaces.com',
            'url_endpoint' => 'https://' . env('LINODE_PODCAST_FILES_BUCKET') . '.' . env('LINODE_PODCAST_FILES_REGION') . '.linodeobjects.com',
            'cdn_endpoint' => 'https://' . env('LINODE_PODCAST_FILES_BUCKET') . '.' . env('LINODE_PODCAST_FILES_REGION') . '.linodeobjects.com',
            'key'          => env('DO_ACCOUNT_FILES_KEY'),
            'secret'       => env('DO_ACCOUNT_FILES_SECRET'),
            'region'       => env('DO_ACCOUNT_FILES_REGION'),
            'bucket'       => env('DO_ACCOUNT_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'website-files' => [
            'driver'       => 's3',
            'endpoint'     => 'https://' . env('WEBSITE_FILES_REGION') . '.linodeobjects.com',
            'url_endpoint' => 'https://' . env('WEBSITE_FILES_BUCKET') . '.' . env('WEBSITE_FILES_REGION') . '.linodeobjects.com',
            'cdn_endpoint' => 'https://' . env('WEBSITE_FILES_BUCKET') . '.' . env('WEBSITE_FILES_REGION') . '.linodeobjects.com',
            'key'          => env('WEBSITE_FILES_KEY'),
            'secret'       => env('WEBSITE_FILES_SECRET'),
            'region'       => env('WEBSITE_FILES_REGION'),
            'bucket'       => env('WEBSITE_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'message-files-do' => [
            'driver'       => 's3',
            'endpoint'     => 'https://' . env('DO_MESSAGE_FILES_REGION') . '.digitaloceanspaces.com',
            'url_endpoint' => 'https://' . env('PODCAST_FILES_BUCKET') . '.' . env('PODCAST_FILES_REGION') . '.linodeobjects.com',
            'cdn_endpoint' => 'https://' . env('PODCAST_FILES_BUCKET') . '.' . env('PODCAST_FILES_REGION') . '.linodeobjects.com',
            'key'          => env('DO_MESSAGE_FILES_KEY'),
            'secret'       => env('DO_MESSAGE_FILES_SECRET'),
            'region'       => env('DO_MESSAGE_FILES_REGION'),
            'bucket'       => env('DO_MESSAGE_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

        'message-files' => [
            'driver' => 'local',
            'root'   => storage_path('message-files'),
            'throw'  => true,
        ],

        'podcast-files' => [
            'driver'       => 's3',
            'endpoint'     => env('PODCAST_FILES_ENDPOINT'),
            'url_endpoint' => env('PODCAST_FILES_URL_ENDPOINT'),
            'cdn_endpoint' => env('PODCAST_FILES_CDN_ENDPOINT'),
            'key'          => env('PODCAST_FILES_KEY'),
            'secret'       => env('PODCAST_FILES_SECRET'),
            'region'       => env('PODCAST_FILES_REGION'),
            'bucket'       => env('PODCAST_FILES_BUCKET'),
            'visibility'   => 'public',
            'throw'        => true,
        ],

    ],

];
