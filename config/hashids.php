<?php

/*
 * This file is part of <PERSON>vel Hashids.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

declare(strict_types=1);

return [

    /*
    |--------------------------------------------------------------------------
    | Default Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the connections below you wish to use as
    | your default connection for all work. Of course, you may use many
    | connections at once using the manager class.
    |
    */

    'default' => 'main',

    /*
    |--------------------------------------------------------------------------
    | Hashids Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the connections setup for your application. Example
    | configuration has been included, but you may add as many connections as
    | you would like.
    |
    */

    'connections' => [

        'main' => [
            'salt'   => env('HASH_IDS_SALT', null),
            'length' => intval(env('HASH_IDS_MINIMUM_LENGTH', 1)),
        ],

        'alternative' => [
            'salt'   => 'your-salt-string',
            'length' => 'your-length-integer',
        ],

    ],

];
