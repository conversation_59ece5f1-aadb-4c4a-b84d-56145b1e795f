<?php

return [
    'user_images'   => [
        'key'     => env('DO_USER_IMAGES_KEY', null),
        'secret'  => env('DO_USER_IMAGES_SECRET', null),
        'region'  => env('DO_USER_IMAGES_REGION', null),
        'bucket'  => env('DO_USER_IMAGES_BUCKET', null),
        'cdn_url' => env('DO_USER_IMAGES_CDN_URL', null),
    ],
    'user_files'    => [
        'key'     => env('DO_USER_FILES_KEY', null),
        'secret'  => env('DO_USER_FILES_SECRET', null),
        'region'  => env('DO_USER_FILES_REGION', null),
        'bucket'  => env('DO_USER_FILES_BUCKET', null),
        'cdn_url' => env('DO_USER_FILES_CDN_URL', null),
    ],
    'sermon_files'  => [
        'key'     => env('DO_SERMON_FILES_KEY', null),
        'secret'  => env('DO_SERMON_FILES_SECRET', null),
        'region'  => env('DO_SERMON_FILES_REGION', null),
        'bucket'  => env('DO_SERMON_FILES_BUCKET', null),
        'cdn_url' => env('DO_SERMON_FILES_CDN_URL', null),
    ],
    'account_files' => [
        'key'     => env('DO_ACCOUNT_FILES_KEY', null),
        'secret'  => env('DO_ACCOUNT_FILES_SECRET', null),
        'region'  => env('DO_ACCOUNT_FILES_REGION', null),
        'bucket'  => env('DO_ACCOUNT_FILES_BUCKET', null),
        'cdn_url' => env('DO_ACCOUNT_FILES_CDN_URL', null),
    ],
    'message_files' => [
        'key'       => env('DO_MESSAGE_FILES_KEY', null),
        'secret'    => env('DO_MESSAGE_FILES_SECRET', null),
        'region'    => env('DO_MESSAGE_FILES_REGION', null),
        'bucket'    => env('DO_MESSAGE_FILES_BUCKET', null),
        'root_path' => env('MESSAGE_FILES_ROOT_PATH', null),
        'cdn_url'   => env('MESSAGE_FILES_CDN_URL', null),
    ],
];
