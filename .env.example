APP_NAME="Lightpost App"
APP_ENV=local
APP_DEBUG=true
APP_CONTACT_EMAIL=
APP_SUPPORT_EMAIL=

LOG_LEVEL=debug
LOG_CHANNEL=single
APP_URL=http://localhost
COPYRIGHT_NAME="Tiny Bit Farm LLC"

APP_KEY=
APP_PREVIOUS_KEYS=

NIGHTWATCH_TOKEN=

HASH_IDS_SALT=
HASH_IDS_MINIMUM_LENGTH=1

AWS_DEFAULT_REGION=

PAPERTRAIL_URL=
PAPERTRAIL_PORT=

LOGDNA_KEY=
LOGDNA_HOSTNAME=

NUMERICS_AUTH_HEADER=

API_GROOVE_INTEGRATION_PROFILE_TOKEN=
PLAIN_INTEGRATION_TOKEN=

PADDLE_KEY=
PADDLE_SECRET=
PADDLE_WEBHOOK_SECRET_KEY=

OPENAI_API_KEY=

STRIPE_KEY=
STRIPE_SECRET=
STRIPE_CONNECT_ACCOUNT_KEY=
STRIPE_CONNECT_ACCOUNT_SECRET=
STRIPE_CONNECT_ACCOUNT_REPORTING_TEST_KEY=
STRIPE_CONNECT_CLIENT_ID=
STRIPE_WEBHOOK_SIGNING_SECRET=

FRONTEND_DOMAIN=lightpost.test
APP_DOMAIN=app.lightpost.test
ADMIN_DOMAIN=admin.lightpost.test
EMAIL_DOMAIN=email.lightpost.test
EMAIL_TO_DOMAIN=to.lightpost.test
EMAIL_FROM_DOMAIN=message.lightpost.test
API_DOMAIN=api.lightpost.test
MOBILE_API_DOMAIN=mobile-api.lightpost.test
PODCAST_DOMAIN=podcasts.lightpost.test
ATTENDANCE_CARDS_DOMAIN=open.lightpost.test
OPEN_DOMAIN=open.lightpost.test
PUBLIC_DOMAIN=public.lightpost.test
WEBSITES_DOMAIN=website-hosting.lightpost.test
WEBSITES_CNAME_DOMAIN=website.lightpost.test

EMAIL_STATIC_ASSETS_URL=

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=homestead
DB_USERNAME=homestead
DB_PASSWORD=secret
DB_FOREIGN_KEYS=false

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=sync

SESSION_SECURE_COOKIE=true

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null

MAIL_DEFAULT_FROM=
MAIL_INBOUND_API_KEY=

MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Lightpost App"

POSTMARK_TOKEN=

POSTMARK_STREAM_PASSWORD_RESETS=
POSTMARK_STREAM_CRISIS_CHECKIN=
POSTMARK_STREAM_GROUP_MESSAGES=
POSTMARK_STREAM_VISITOR_NOTIFICATIONS=

BYTESCALE_API_KEY=

IPINFO_TOKEN=

GOOGLE_MAPS_API_KEY=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=
PUSHER_APP_HOST=
PUSHER_APP_PORT=
PUSHER_APP_AUTH_URL=

EXPO_ACCESS_TOKEN=

CLOUDFLARE_SITE_KEY=
CLOUDFLARE_SECRET_KEY=

FIREBASE_CREDENTIALS=
FIREBASE_DATABASE_URL=
FIREBASE_DYNAMIC_LINKS_DEFAULT_DOMAIN=
FIREBASE_STORAGE_DEFAULT_BUCKET=
FIREBASE_CACHE_STORE=
FIREBASE_HTTP_DEBUG_LOG_CHANNEL=
FIREBASE_HTTP_LOG_CHANNEL=
FIREBASE_ENABLE_DEBUG=

YOUTUBE_CONTENT_API_KEY=

DO_USER_IMAGES_KEY=
DO_USER_IMAGES_SECRET=
DO_USER_IMAGES_REGION=
DO_USER_IMAGES_BUCKET=
DO_USER_IMAGES_CDN_URL=
DO_USER_IMAGES_SUBFOLDER=
DO_USER_IMAGES_REQUIRES_AUTH=false

DO_USER_FILES_KEY=
DO_USER_FILES_SECRET=
DO_USER_FILES_REGION=
DO_USER_FILES_BUCKET=
DO_USER_FILES_SUBFOLDER=
DO_USER_FILES_REQUIRES_AUTH=false

SERMON_FILES_ENDPOINT=
SERMON_FILES_URL_ENDPOINT=
SERMON_FILES_CDN_ENDPOINT=
SERMON_FILES_KEY=
SERMON_FILES_SECRET=
SERMON_FILES_REGION=
SERMON_FILES_BUCKET=
SERMON_FILES_SUBFOLDER=
SERMON_FILES_REQUIRES_AUTH=false

DO_ACCOUNT_FILES_KEY=
DO_ACCOUNT_FILES_SECRET=
DO_ACCOUNT_FILES_REGION=
DO_ACCOUNT_FILES_BUCKET=
DO_ACCOUNT_FILES_SUBFOLDER=
DO_ACCOUNT_FILES_REQUIRES_AUTH=false

DO_MESSAGE_FILES_KEY=
DO_MESSAGE_FILES_SECRET=
DO_MESSAGE_FILES_REGION=
DO_MESSAGE_FILES_BUCKET=
DO_MESSAGE_FILES_SUBFOLDER=
DO_MESSAGE_FILES_REQUIRES_AUTH=false

MESSAGE_FILES_ROOT_PATH=

PODCAST_ENDPOINT=
PODCAST_URL_ENDPOINT=
PODCAST_CDN_ENDPOINT=
PODCAST_KEY=
PODCAST_SECRET=
PODCAST_REGION=
PODCAST_BUCKET=
PODCAST_SUBFOLDER=
PODCAST_REQUIRES_AUTH=false

GROUP_FILES_ENDPOINT=
GROUP_FILES_URL_ENDPOINT=
GROUP_FILES_CDN_ENDPOINT=
GROUP_FILES_KEY=
GROUP_FILES_SECRET=
GROUP_FILES_REGION=
GROUP_FILES_BUCKET=
GROUP_FILES_SUBFOLDER=
GROUP_FILES_REQUIRES_AUTH=false

TEMP_FILES_ENDPOINT=
TEMP_FILES_URL_ENDPOINT=
TEMP_FILES_CDN_ENDPOINT=
TEMP_FILES_KEY=
TEMP_FILES_SECRET=
TEMP_FILES_REGION=
TEMP_FILES_BUCKET=
TEMP_FILES_SUBFOLDER=
TEMP_FILES_REQUIRES_AUTH=false

TEMP_FILE_UPLOADS_ENDPOINT=
TEMP_FILE_UPLOADS_URL_ENDPOINT=
TEMP_FILE_UPLOADS_CDN_ENDPOINT=
TEMP_FILE_UPLOADS_KEY=
TEMP_FILE_UPLOADS_SECRET=
TEMP_FILE_UPLOADS_REGION=
TEMP_FILE_UPLOADS_BUCKET=
TEMP_FILE_UPLOADS_SUBFOLDER=
TEMP_FILE_UPLOADS_REQUIRES_AUTH=false

WEBSITE_FILES_KEY=
WEBSITE_FILES_SECRET=
WEBSITE_FILES_REGION=
WEBSITE_FILES_BUCKET=
WEBSITE_FILES_CDN_URL=
WEBSITE_FILES_SUBFOLDER=
WEBSITE_FILES_REQUIRES_AUTH=false

PUBLIC_CDN_S3_ENDPOINT=
PUBLIC_CDN_URL_ENDPOINT=
PUBLIC_CDN_CDN_ENDPOINT=
PUBLIC_CDN_KEY=
PUBLIC_CDN_SECRET=
PUBLIC_CDN_REGION=auto
PUBLIC_CDN_BUCKET=
PUBLIC_CDN_TOKEN=

PASSPORT_PERSONAL_ACCESS_CLIENT_ID=
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET=
PASSPORT_PUBLIC_KEY=
PASSPORT_PRIVATE_KEY=
