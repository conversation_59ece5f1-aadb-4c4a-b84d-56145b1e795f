<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

//Auth::routes();

Route::get('/', 'Base\Http\Controllers\HomeController@welcome')->name('welcome');
Route::get('/about', 'Base\Http\Controllers\HomeController@about')->name('frontend.about');
Route::get('/charge', 'Base\Http\Controllers\HomeController@charge')->name('frontend.charge');
Route::get('/CHARGE', 'Base\Http\Controllers\HomeController@charge')->name('frontend.charge.uppercase');
Route::get('/pricing', 'Base\Http\Controllers\HomeController@pricing')->name('frontend.pricing');
Route::get('/security', 'Base\Http\Controllers\HomeController@security')->name('frontend.security');
Route::get('/roadmap', 'Base\Http\Controllers\HomeController@roadmap')->name('frontend.roadmap');
Route::get('/member-privacy', 'Base\Http\Controllers\HomeController@memberPrivacy')->name('frontend.member-privacy');
Route::get('/subprocessors', 'Base\Http\Controllers\HomeController@subprocessors')->name('frontend.subprocessors');
Route::get('/features', 'Base\Http\Controllers\HomeController@features')->name('frontend.features');
Route::get('/value', 'Base\Http\Controllers\HomeController@value')->name('frontend.value');
Route::get('/mobile', 'Base\Http\Controllers\HomeController@mobile')->name('frontend.mobile');
Route::get('/contact', 'Base\Http\Controllers\HomeController@register')->name('frontend.signup');
Route::post('/sign-up', 'Base\Http\Controllers\HomeController@registerSubmit')->name('frontend.signup.submit');
Route::get('/sign-up/thank-you', 'Base\Http\Controllers\HomeController@registerThankYou')->name('frontend.signup.thank-you');
Route::get('/newsletter/thank-you', 'Base\Http\Controllers\HomeController@newsletterThankYou')->name('frontend.newsletter.thank-you');
Route::get('/privacy', 'Base\Http\Controllers\HomeController@privacy')->name('frontend.privacy');
Route::get('/privacy-policy', 'Base\Http\Controllers\HomeController@privacy')->name('frontend.privacy-policy');
Route::get('/terms', 'Base\Http\Controllers\HomeController@terms')->name('frontend.terms');
Route::get('/terms-of-use', 'Base\Http\Controllers\HomeController@terms')->name('frontend.terms-of-use');

Route::get('/requests/delete', 'Base\Http\Controllers\HomeController@accountDeletionRequest')->name('frontend.compliance.account-deletion-request');
Route::post('/requests/delete', 'Base\Http\Controllers\HomeController@accountDeletionRequestSubmit')->name('frontend.compliance.account-deletion-request.submit');

Route::get('/intro-package', 'Base\Http\Controllers\HomeController@carePackage')->name('frontend.care-package');
Route::post('/intro-package', 'Base\Http\Controllers\HomeController@carePackageSubmit')->name('frontend.care-package.submit');
Route::get('/intro-package/thank-you', 'Base\Http\Controllers\HomeController@carePackageThankYou')->name('frontend.care-package.thank-you');

Route::permanentRedirect('/iphone', 'https://apps.apple.com/us/app/lightpost-mobile/id1458435817');
Route::permanentRedirect('/android', 'https://play.google.com/store/apps/details?id=com.tinybitfarm.lightpost&utm_source=lightpost');