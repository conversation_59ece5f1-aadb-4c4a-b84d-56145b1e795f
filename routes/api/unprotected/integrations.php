<?php

Route::group(['namespace' => 'Controllers\Integrations'], function () {
    Route::get('integrations/groove/profile')
        ->name('api.integrations.groove.profile')
        ->uses('GrooveController@profile');

    Route::get('integrations/caddy/allowed-domains')
        ->name('api.integrations.caddy.allowed-domains')
        ->uses('CaddyController@allowedDomains');

    Route::post('integrations/plain/customer-details')
        ->name('api.integrations.plain.customer-details')
        ->uses('PlainController@customerDetails');

    // NUMERICS
    Route::get('integrations/numerics/counts/account')
        ->name('api.integrations.numerics.counts.account')
        ->uses('NumericsController@accountCount');

    Route::get('integrations/numerics/counts/members')
        ->name('api.integrations.numerics.counts.members')
        ->uses('NumericsController@membersCount');
    Route::get('integrations/numerics/counts/users')
        ->name('api.integrations.numerics.counts.users')
        ->uses('NumericsController@usersCount');
    Route::get('integrations/numerics/counts/visitors')
        ->name('api.integrations.numerics.counts.visitors')
        ->uses('NumericsController@visitorsCount');

    Route::get('integrations/numerics/counts/notifications')
        ->name('api.integrations.numerics.counts.notifications')
        ->uses('NumericsController@notificationsCount');
    Route::get('integrations/numerics/counts/group-posts')
        ->name('api.integrations.numerics.counts.group-posts')
        ->uses('NumericsController@groupPostsCount');
    Route::get('integrations/numerics/counts/group-post-comments')
        ->name('api.integrations.numerics.counts.group-post-comments')
        ->uses('NumericsController@groupPostCommentsCount');

    Route::get('integrations/numerics/message-types-usage')
        ->name('api.integrations.numerics.message-types-usage')
        ->uses('NumericsController@messageTypesUsage');

    Route::get('integrations/numerics/daily-usage/group-posts')
        ->name('api.integrations.numerics.daily-usage.group-posts')
        ->uses('NumericsController@dailyUsageGroupPosts');
    Route::get('integrations/numerics/daily-usage/group-post-comments')
        ->name('api.integrations.numerics.daily-usage.group-post-comments')
        ->uses('NumericsController@dailyUsageGroupPostComments');
    Route::get('integrations/numerics/daily-usage/messages')
        ->name('api.integrations.numerics.daily-usage.messages')
        ->uses('NumericsController@dailyUsageMessages');
    Route::get('integrations/numerics/daily-usage/users-added')
        ->name('api.integrations.numerics.daily-usage.users-added')
        ->uses('NumericsController@dailyUsageUsersAdded');

    Route::get('integrations/numerics/today/messages')
        ->name('api.integrations.numerics.counts.today.messages')
        ->uses('NumericsController@todayMessagesCount');
    Route::get('integrations/numerics/today/notifications')
        ->name('api.integrations.numerics.counts.today.notifications')
        ->uses('NumericsController@todayNotificationsCount');
    Route::get('integrations/numerics/today/group-posts')
        ->name('api.integrations.numerics.counts.today.group-posts')
        ->uses('NumericsController@todayGroupPostsCount');
    Route::get('integrations/numerics/today/group-post-comments')
        ->name('api.integrations.numerics.counts.today.group-post-comments')
        ->uses('NumericsController@todayGroupPostCommentsCount');

    Route::get('integrations/numerics/fin/this-month')
        ->name('api.integrations.numerics.finance.revenue-this-month')
        ->uses('NumericsController@financeBilledThisMonth');
    Route::get('integrations/numerics/fin/outstanding-this-month')
        ->name('api.integrations.numerics.finance.outstanding-this-month')
        ->uses('NumericsController@financeOutstandingThisMonth');
});
