<?php

Route::group(['namespace' => 'Controllers'], function () {

    Route::get('feeds/calendars/user/{token}/{feed_name}')
        ->name('api.public.calendars.feed.user')
        ->uses('Calendars\CalendarController@publicUserFeed');

    Route::get('feeds/calendars/{calendar}/{token}/{feed_name}')
        ->name('api.public.calendars.feed.calendar')
        ->uses('Calendars\CalendarController@publicCalendarFeed');


});
