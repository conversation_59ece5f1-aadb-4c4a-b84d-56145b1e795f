<?php

Route::group(['namespace' => 'Controllers\Email'], function () {
    Route::post('email/events/sparkpost')
        ->name('api.email.events.sparkpost')
        ->uses('SparkpostEventController@handler');

    Route::post('email/inbound/sparkpost')
        ->name('api.email.inbound.sparkpost')
        ->uses('IncomingSparkpostController@inbound');

    Route::post('email/inbound/mailgun')
        ->name('api.email.inbound.mailgun')
        ->uses('IncomingMailgunController@inbound');

    Route::post('email/inbound/postmark')
        ->name('api.email.inbound.postmark')
        ->uses('IncomingPostmarkController@inbound');

    Route::post('email/events/ses/bounces')
        ->name('api.email.events.ses.bounces')
        ->uses('SparkpostEventController@bounces');

    Route::post('email/events/ses/complaints')
        ->name('api.email.events.ses.complaints')
        ->uses('SparkpostEventController@complaints');

    Route::post('email/events/ses/deliveries')
        ->name('api.email.events.ses.deliveries')
        ->uses('SparkpostEventController@deliveries');

    Route::post('email/events/postmark/bounce')
        ->name('api.email.events.postmark.bounce')
        ->uses('PostmarkEventController@handleBounce');

    Route::post('email/events/postmark/delivery')
        ->name('api.email.events.postmark.delivery')
        ->uses('PostmarkEventController@handleDelivery');

    Route::post('email/events/postmark/spam-complaint')
        ->name('api.email.events.postmark.spam-complaint')
        ->uses('PostmarkEventController@handleSpamComplaint');

    Route::post('email/events/postmark/subscription-change')
        ->name('api.email.events.postmark.subscription-change')
        ->uses('PostmarkEventController@handleSubscriptionChange');
});

Route::group(['namespace' => 'Controllers\Sms'], function () {
    Route::post('sms/inbound/messagebird')
        ->name('api.sms.inbound.messagebird')
        ->uses('IncomingMessageBirdController@inbound');

    Route::post('sms/inbound/plivo')
        ->name('api.sms.inbound.plivo')
        ->uses('IncomingPlivoController@inbound');

    Route::post('sms/inbound/twilio')
        ->name('api.sms.inbound.twilio')
        ->uses('IncomingTwilioController@inbound');

    Route::post('sms/callback/plivo')
        ->name('api.sms.callback.plivo')
        ->uses('IncomingPlivoController@callback');

    Route::post('sms/callback/twilio')
        ->name('api.sms.callback.twilio')
        ->uses('IncomingTwilioController@callback');
});

Route::group(['namespace' => 'Controllers\Finance'], function () {
    Route::post('finance/events/stripe-connect')
        ->name('api.finance.events.stripe-connect')
        ->uses('IncomingStripeController@events');
});

Route::group(['namespace' => 'Controllers\Accounts'], function () {
    Route::post('webhooks/paddle/events')
        ->name('api.accounts.events.paddle')
        ->uses('PaddleWebhookController@event');
});
