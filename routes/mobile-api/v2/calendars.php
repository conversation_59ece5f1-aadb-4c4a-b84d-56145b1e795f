<?php

Route::group(['namespace' => 'Controllers'], function () {

    // Calendar index
    Route::get('calendars')
        ->name('mobile-api.v2.calendars.index')
        ->uses('CalendarController@index');

    Route::get('calendars/settings')
        ->name('mobile-api.v2.calendars.settings')
        ->uses('CalendarController@settings');

    // Calendar events
    Route::get('calendars/events/occurrences/all')
        ->name('mobile-api.v2.calendars.events.occurrences.all')
        ->uses('CalendarController@allEventOccurrences');

    // Calendar events this month
    Route::get('calendars/events/occurrences/current-month')
        ->name('mobile-api.v2.calendars.events.occurrences.current-month')
        ->uses('CalendarController@currentMonthEventOccurrences');

    // Calendar events this week
    Route::get('calendars/events/occurrences/current-week')
        ->name('mobile-api.v2.calendars.events.occurrences.current-week')
        ->uses('CalendarController@currentWeekEventOccurrences');

    // Calendar Event Occurrence View
    Route::get('calendars/event-occurrence/{event_occurrence}')
        ->name('mobile-api.v2.calendars.view-event-occurrence')
        ->uses('CalendarController@viewEventOccurrence');

    Route::post('calendars/event-occurrence/{event_occurrence}/responses/update')
        ->name('mobile-api.v2.calendars.events.occurrences.responses.update')
        ->uses('CalendarController@updateEventOccurrenceResponse');

    Route::delete('calendars/event-occurrence/{event_occurrence}/responses/delete')
        ->name('mobile-api.v2.calendars.events.occurrences.responses.delete')
        ->uses('CalendarController@deleteEventOccurrenceResponse');

});
