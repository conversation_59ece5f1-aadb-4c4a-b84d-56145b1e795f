<?php

Route::group(['namespace' => 'Controllers'], function () {

    Route::get('programs')
        ->name('mobile-api.v2.programs.index')
        ->uses('ProgramController@index')
        ->middleware('can:index,App\Programs\Program');

    Route::get('programs/{program}')
        ->name('mobile-api.v2.programs.view')
        ->uses('ProgramController@program')
        ->middleware('can:view,program');

    Route::get('programs/{program}/users/{programUser}')
        ->name('mobile-api.v2.programs.users.view')
        ->uses('ProgramController@user')
        ->middleware('can:viewGroups,program');

    Route::get('programs/{program}/groups')
        ->name('mobile-api.v2.programs.groups.index')
        ->uses('ProgramController@groups')
        ->middleware('can:viewGroups,program');

    Route::get('programs/{program}/groups/{programGroup}')
        ->name('mobile-api.v2.programs.groups.view')
        ->uses('ProgramController@group')
        ->middleware('can:viewGroups,program');

    // Create Post
    Route::post('programs/{program}/users/{programUser}/checkin')
        ->name('mobile-api.v2.programs.checkins.submit')
        ->uses('ProgramController@submitUserCheckin')
        ->middleware('can:viewGroups,program');

});
