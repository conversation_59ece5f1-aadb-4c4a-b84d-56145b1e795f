<?php

Route::group(['namespace' => 'Controllers'], function () {
    // Files
    Route::get('files')
        ->name('mobile-api.v2.files.index')
        ->uses('AccountFileController@index');

    Route::get('files/folder/{account_file_parent_id?}')
        ->name('mobile-api.v2.files.folder.index')
        ->uses('AccountFileController@folder');

    // Files
    Route::get('account/files')
        ->name('mobile-api.v2.account.files.index')
        ->uses('AccountFileController@index');

    Route::get('account/contribution-buckets')
        ->name('mobile-api.v2.account.contribution-buckets')
        ->uses('AccountController@contributionBuckets');
});
