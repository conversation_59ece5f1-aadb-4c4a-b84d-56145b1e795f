<?php

Route::group(['namespace' => 'Controllers'], function () {
    Route::get('directory')
        ->name('mobile-api.v2.directory.index')
        ->uses('DirectoryController@index');

    Route::get('directory/by-family')
        ->name('mobile-api.v2.directory.by_family')
        ->uses('Directory<PERSON>ontroller@index');

    Route::get('directory/by-user')
        ->name('mobile-api.v2.directory.by_user')
        ->uses('DirectoryController@indexByUser');

    Route::get('photo-directory')
        ->name('mobile-api.v2.directory.photo')
        ->uses('DirectoryController@photoDirectory');

    Route::post('directory/family/{user_id}/family-photo')
        ->name('mobile-api.v2.directory.family-photo.submit')
        ->uses('DirectoryController@submitNewFamilyPhoto')
        ->middleware('can:manageFamilyPhotos,App\Users\Photo');

    Route::get('directory/family/{user_id}')
        ->name('mobile-api.v2.directory.family')
        ->uses('<PERSON><PERSON><PERSON>roller@viewFamily');
});
