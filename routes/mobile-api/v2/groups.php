<?php

Route::group(['namespace' => 'Controllers'], function () {
    // List of Groups
    Route::get('groups')
        ->name('mobile-api.v2.groups.index')
        ->uses('GroupController@index')
        ->middleware('can:seeGroupPosts,App\Users\Group');

    // Group Notifications
    Route::get('groups/notifications')
        ->name('mobile-api.v2.groups.notifications')
        ->uses('GroupController@getGroupsNotifications')
        ->middleware('can:seeGroupPosts,App\Users\Group');

    // Group Details
    Route::get('groups/{group}')
        ->name('mobile-api.v2.groups.view')
        ->uses('GroupController@view')
        ->middleware('can:userView,group');

    // Posts
    Route::get('groups/{group}/posts')
        ->name('mobile-api.v2.groups.posts')
        ->uses('GroupController@posts')
        ->middleware('can:userView,group');

    // Get Comments
    Route::get('groups/{group}/posts/{post}')
        ->name('mobile-api.v2.groups.posts.view')
        ->uses('GroupController@post')
        ->middleware('can:userView,group');

    // Create Post
    Route::post('groups/{group}/posts/create')
        ->name('mobile-api.v2.groups.posts.create')
        ->uses('GroupController@submitPost')
        ->middleware('can:post,group');

    // Edit Post
    Route::post('groups/{group}/posts/{post}/edit')
        ->name('mobile-api.v2.groups.posts.edit')
        ->uses('GroupController@submitEditPost')
        ->middleware('can:edit,post');

    // Delete Post
    Route::post('groups/{group}/posts/{post}/delete')
        ->name('mobile-api.v2.groups.posts.delete')
        ->uses('GroupController@deletePost')
        ->middleware('can:post,group');

    // Create Post Comment
    Route::post('groups/{group}/posts/{post}/comment/create')
        ->name('mobile-api.v2.groups.posts.comment.create')
        ->uses('GroupController@submitComment')
        ->middleware('can:comment,post');

    // Delete Post Comment
    Route::post('groups/{group}/posts/{post}/comment/{comment}/delete')
        ->name('mobile-api.v2.groups.posts.comment.delete')
        ->uses('GroupController@deleteComment')
        ->middleware('can:delete,comment');

    // Update Group Settings
    Route::get('groups/{group}/settings')
        ->name('mobile-api.groups.settings')
        ->uses('GroupController@getGroupSettings')
        ->middleware('can:userView,group');

    // Update Group Settings
    Route::post('groups/{group}/settings')
        ->name('mobile-api.v2.groups.settings')
        ->uses('GroupController@submitGroupSettings')
        ->middleware('can:userView,group');

    // Create Post
    Route::post('groups/{group}/send/sms')
        ->name('mobile-api.v2.groups.posts.send.sms')
        ->uses('GroupController@submitSendSMS')
        ->middleware('can:userView,group');

    // PIN - Post
    Route::post('groups/{group}/posts/{post}/pin/{pinned?}')
        ->name('mobile-api.v2.groups.posts.pin')
        ->uses('GroupController@submitTogglePinPost')
        ->middleware('can:edit,post');

    // LIKE - Post
    Route::post('groups/{group}/posts/{post}/react/{type?}')
        ->name('mobile-api.v2.groups.posts.react')
        ->uses('GroupController@submitPostReaction')
        ->middleware('can:userView,group');

    // LIKE - Comment
    Route::post('groups/{group}/posts/{post}/comments/{comment}/react/{type?}')
        ->name('mobile-api.v2.groups.posts.comments.react')
        ->uses('GroupController@submitCommentReaction')
        ->middleware('can:userView,group');

    // Add User
    Route::post('groups/{group}/users/add')
        ->name('mobile-api.v2.groups.users.add')
        ->uses('GroupController@addUser')
        ->middleware('can:addUserToGroup,group');

    // Remove User
    Route::delete('groups/{group}/users/remove')
        ->name('mobile-api.v2.groups.users.remove')
        ->uses('GroupController@removeUser')
        ->middleware('can:removeUserFromGroup,group');
});
