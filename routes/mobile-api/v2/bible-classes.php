<?php

Route::group(['namespace' => 'Controllers'], function () {

    Route::get('bible-classes/available')
        ->name('mobile-api.v2.bible-classes.available')
        ->uses('BibleClassController@available');

    Route::post('bible-classes/register')
        ->name('mobile-api.v2.bible-classes.register')
        ->uses('BibleClassController@register');

    Route::post('bible-classes/unregister')
        ->name('mobile-api.v2.bible-classes.unregister')
        ->uses('BibleClassController@unregister');

});
