<?php

Route::group(['namespace' => 'Controllers'], function () {
    Route::get('attendance/admin/dashboard')
        ->name('mobile-api.v2.attendance.admin.dashboard')
        ->uses('AttendanceController@adminDashboard')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/user/today')
        ->name('mobile-api.v2.attendance.user.today')
        ->uses('AttendanceController@userToday');

    Route::get('attendance/admin/types')
        ->name('mobile-api.v2.attendance.types')
        ->uses('AttendanceController@types');

    Route::get('attendance/admin/records/by-user')
        ->name('mobile-api.v2.attendance.records.by-user')
        ->uses('AttendanceController@recordsByUser')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/admin/list/by-user')
        ->name('mobile-api.v2.attendance.lists.by-user')
        ->uses('AttendanceController@byUser')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/admin/list/by-family')
        ->name('mobile-api.v2.attendance.lists.by-family')
        ->uses('AttendanceController@byFamily')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/admin/list/by-bible-class/{bible_class}')
        ->name('mobile-api.v2.attendance.lists.by-bible-class')
        ->uses('AttendanceController@byBibleClass')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/admin/list/section-list/by-family')
        ->name('mobile-api.v2.attendance.lists.section-list.by-family')
        ->uses('AttendanceController@sectionListByFamily')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/admin/list/section-list/by-bible-class/{bible_class}')
        ->name('mobile-api.v2.attendance.lists.section-list.by-bible-class')
        ->uses('AttendanceController@sectionListByBibleClass')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/admin/list/bible-class/{bible_class}')
        ->name('mobile-api.v2.attendance.bible-class')
        ->uses('AttendanceController@adminGetBibleClass')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::post('attendance/admin/update/user/{user}')
        ->name('mobile-api.v2.attendance.update.user')
        ->uses('AttendanceController@updateUserAttendance')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/self-checkin/types/available')
        ->name('mobile-api.v2.attendance.self-checkin.types.available')
        ->uses('AttendanceController@selfCheckinAvailableTypes')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::get('attendance/self-checkin/records/today')
        ->name('mobile-api.v2.attendance.self-checkin.records.today')
        ->uses('AttendanceController@selfCheckinCurrentRecords')
        ->middleware('can:recordAny,App\Attendance\Attendance');

    Route::post('attendance/self-checkin/records/submit')
        ->name('mobile-api.v2.attendance.self-checkin.records.submit')
        ->uses('AttendanceController@selfCheckinRecordSubmit')
        ->middleware('can:recordAny,App\Attendance\Attendance');
});
