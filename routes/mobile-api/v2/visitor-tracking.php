<?php

Route::group(['namespace' => 'Controllers'], function () {
    Route::get('visitor-tracking/statuses')
        ->name('mobile-api.v2.visitor-tracking.statuses')
        ->uses('VisitorTrackingController@statuses');

    Route::get('visitor-tracking/visitors')
        ->name('mobile-api.v2.visitor-tracking.visitors')
        ->uses('VisitorTrackingController@visitors');

    Route::get('visitor-tracking/history-types')
        ->name('mobile-api.v2.visitor-tracking.visitors.history-types')
        ->uses('VisitorTrackingController@historyTypes');

    Route::get('visitor-tracking/visitors/{visitor}/contact-information')
        ->name('mobile-api.v2.visitor-tracking.view.contact-information')
        ->uses('VisitorTrackingController@contactInformation');

    Route::get('visitor-tracking/visitors/{visitor}')
        ->name('mobile-api.v2.visitor-tracking.view')
        ->uses('VisitorTrackingController@view');

    Route::post('visitor-tracking/visitors/{visitor}/history/create')
        ->name('mobile-api.v2.visitor-tracking.visitors.history.create')
        ->uses('VisitorTrackingController@createHistory');
});
