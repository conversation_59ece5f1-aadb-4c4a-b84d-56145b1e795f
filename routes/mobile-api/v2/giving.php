<?php

Route::group(['namespace' => 'Controllers'], function () {
    // Payment Methods
    Route::get('profile/payment-methods')
        ->name('mobile-api.v2.profile.payment-methods')
        ->uses('GivingController@paymentMethods');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Payment Schedules
    Route::get('profile/payment-schedules')
        ->name('mobile-api.v2.profile.payment-schedules')
        ->uses('GivingController@paymentSchedules')
        ->middleware('can:viewExisting,App\Users\PaymentSchedule');

    // Contribution History
    Route::get('profile/contribution-history')
        ->name('mobile-api.v2.profile.contribution-history')
        ->uses('GivingController@contributionHistory');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Payment Method Add -- Get Payment Intent
    Route::get('giving/payment-methods/create')
        ->name('mobile-api.v2.giving.add-payment-method')
        ->uses('GivingController@addPaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Payment Method Submit
    Route::post('giving/payment-methods/create')
        ->name('mobile-api.v2.giving.add-payment-method.submit')
        ->uses('GivingController@submitAddPaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Payment Schedule Submit
    Route::post('giving/payment-schedules/create')
        ->name('mobile-api.v2.giving.create-payment-schedule.submit')
        ->uses('GivingController@submitCreatePaymentSchedule')
        ->middleware('can:create,App\Users\PaymentSchedule');

    // Payment Schedule Submit
    Route::delete('giving/payment-schedules/{payment_schedule}/delete')
        ->name('mobile-api.v2.giving.create-payment-schedule.delete')
        ->uses('GivingController@submitDeletePaymentSchedule')
        ->middleware('can:delete,payment_schedule');

    // Verify Bank Account
    Route::post('giving/payment-methods/verify/{payment_method}')
        ->name('mobile-api.v2.giving.add-payment-method.verify')
        ->uses('GivingController@verifyPaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Give
    Route::post('giving/give/{payment_method}')
        ->name('mobile-api.v2.giving.give')
        ->uses('GivingController@submitGive');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Delete Payment Method
    Route::delete('giving/payment-method/delete/{payment_method}')
        ->name('mobile-api.v2.giving.delete-payment-method.submit')
        ->uses('GivingController@submitDeletePaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

});
