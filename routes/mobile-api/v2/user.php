<?php

Route::group(['namespace' => 'Controllers'], function () {

    Route::get('user/notifications')
        ->name('mobile-api.v2.user.notifications')
        ->uses('User<PERSON>ontroller@getNotifications');

    Route::post('user/notifications/mark-user-group-read/{user_group_id?}')
        ->middleware('throttle:mark-notifications-as-read')
        ->name('mobile-api.v2.user.notifications.mark-user-group-read')
        ->uses('UserController@markUserGroupRead');

    Route::post('user/notifications/mark-read/{user_notification}')
        ->name('mobile-api.v2.user.notifications.mark-read')
        ->middleware('throttle:mark-notifications-as-read')
        ->uses('UserController@markRead');

    Route::post('user/notifications/read')
        ->name('mobile-api.v2.user.notifications.read')
        ->uses('UserController@readNotifications');

    Route::post('user/photos/submitImgixAvatar')
        ->name('mobile-api.v2.user.photos.submit.imgixAvatar')
        ->uses('User<PERSON><PERSON>roller@submitImgixAvatar');

});
