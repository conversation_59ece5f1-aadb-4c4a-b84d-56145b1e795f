<?php

Route::group(['namespace' => 'Controllers'], function () {
    Route::get('home')
        ->name('mobile-api.v2.home.index')
        ->uses('HomeController@index');

    Route::get('home/birthdays')
        ->name('mobile-api.v2.home.birthdays')
        ->uses('HomeController@birthdays');

    Route::get('home/anniversaries')
        ->name('mobile-api.v2.home.anniversaries')
        ->uses('HomeController@anniversaries');

    Route::get('home/baptism-birthdays')
        ->name('mobile-api.v2.home.baptism-birthdays')
        ->uses('HomeController@baptismBirthdays');

    Route::get('lightpost')
        ->name('mobile-api.v2.lightpost')
        ->uses('HomeController@lightpost');
});
