<?php

Route::group(['namespace' => 'Controllers'], function () {

    // Account
    Route::get('profile')
        ->name('mobile-api.account.index')
        ->uses('ProfileController@index');

    // Permissions
    Route::get('profile/permissions')
        ->name('mobile-api.account.permissions.index')
        ->uses('ProfileController@permissions');


    // Involvement
    Route::get('profile/involvement')
        ->name('mobile-api.account.involvement')
        ->uses('InvolvementController@view');

    // Involvement Save
    Route::post('profile/involvement')
        ->name('mobile-api.account.involvement.save')
        ->uses('InvolvementController@save');


//    // Account Save
    Route::patch('profile')
        ->name('mobile-api.account.save')
        ->uses('ProfileController@save');
    // ->middleware('can:index,App\Messages\Message');

//    // ADDRESSES

    Route::post('profile/address/create')
        ->name('mobile-api.account.address.store')
        ->uses('AccountAddressController@store');
    // ->middleware('can:create,App\Users\Address');

    Route::put('profile/address/{address}/edit')
        ->name('mobile-api.account.address.save')
        ->uses('AccountAddressController@save');
//        ->middleware('can:update,address');

    Route::delete('profile/address/{address}/delete')
        ->name('mobile-api.account.address.delete')
        ->uses('AccountAddressController@delete');
//        ->middleware('can:delete,address');

    // PHONES

    Route::post('profile/phone/create')
        ->name('mobile-api.account.phone.store')
        ->uses('AccountPhoneController@store');
    // ->middleware('can:create,App\Users\Phone');

    Route::put('profile/phone/{phone}/edit')
        ->name('mobile-api.account.phone.save')
        ->uses('AccountPhoneController@save');
//        ->middleware('can:update,phone');

    Route::delete('profile/phone/{phone}/delete')
        ->name('mobile-api.account.phone.delete')
        ->uses('AccountPhoneController@delete');
//        ->middleware('can:delete,phone');

    // EMAILS

    Route::post('profile/email/create')
        ->name('mobile-api.account.email.store')
        ->uses('AccountEmailController@store');
    // ->middleware('can:create,App\Users\Email');

    Route::put('profile/email/{email}/edit')
        ->name('mobile-api.account.email.save')
        ->uses('AccountEmailController@save');
//        ->middleware('can:update,email');

    Route::delete('profile/email/{email}/delete')
        ->name('mobile-api.account.email.delete')
        ->uses('AccountEmailController@delete');
//        ->middleware('can:delete,email');

});
