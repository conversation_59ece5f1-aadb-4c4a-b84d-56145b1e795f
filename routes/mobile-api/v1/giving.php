<?php

Route::group(['namespace' => 'Controllers'], function () {

    // Payment Methods
    Route::get('profile/payment-methods')
        ->name('mobile-api.profile.payment-methods')
        ->uses('GivingController@paymentMethods');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Contribution History
    Route::get('profile/contribution-history')
        ->name('mobile-api.profile.contribution-history')
        ->uses('GivingController@contributionHistory');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Payment Method Add -- Get Payment Intent
    Route::get('giving/payment-methods/create')
        ->name('mobile-api.giving.add-payment-method')
        ->uses('GivingController@addPaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Payment Method Submit
    Route::post('giving/payment-methods/create')
        ->name('mobile-api.giving.add-payment-method.submit')
        ->uses('GivingController@submitAddPaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Verify Bank Account
    Route::post('giving/payment-methods/verify/{payment_method}')
        ->name('mobile-api.giving.add-payment-method.verify')
        ->uses('GivingController@verifyPaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Give
    Route::post('giving/give/{payment_method}')
        ->name('mobile-api.giving.give')
        ->uses('GivingController@submitGive');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

    // Delete Payment Method
    Route::delete('giving/payment-method/delete/{payment_method}')
        ->name('mobile-api.giving.delete-payment-method.submit')
        ->uses('GivingController@submitDeletePaymentMethod');
//        ->middleware('can:index,App\AccountFiles\AccountFile');

});
