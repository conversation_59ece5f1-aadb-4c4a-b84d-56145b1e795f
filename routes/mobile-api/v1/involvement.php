<?php

Route::group(['namespace' => 'Controllers'], function () {

    Route::get('involvement/all')
        ->name('mobile-api.involvement.all')
        ->uses('InvolvementController@all');

    Route::get('involvement/categories')
        ->name('mobile-api.involvement.categories')
        ->uses('InvolvementController@categories');

    Route::get('involvement/categories/{category}')
        ->name('mobile-api.involvement.categories.view')
        ->uses('InvolvementController@category');

    Route::get('involvement/categories/{category}/areas')
        ->name('mobile-api.involvement.areas')
        ->uses('InvolvementController@areas');

    Route::get('involvement/categories/{category}/areas/{area}')
        ->name('mobile-api.involvement.area')
        ->uses('InvolvementController@area');

    Route::get('involvement/categories/{category}/areas/{area}/subareas')
        ->name('mobile-api.involvement.subareas')
        ->uses('InvolvementController@subareas');

    Route::get('involvement/categories/{category}/areas/{area}/subareas/{subarea}')
        ->name('mobile-api.involvement.subarea')
        ->uses('InvolvementController@subarea');

});
