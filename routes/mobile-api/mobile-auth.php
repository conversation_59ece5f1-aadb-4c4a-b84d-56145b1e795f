<?php

Route::group(['namespace' => 'Auth\Controllers'], function () {

    Route::get('health-check')
        ->name('mobile-api.health.check')
        ->uses('LoginController@healthCheck')
        ->middleware('guest');

    # Login
    Route::post('login')
        ->name('mobile-api.login.process')
        ->uses('LoginController@login')
        ->middleware('guest');

    # Remove Token
    # This is for removing a legacy device token from the server when upgrading device tokens to use Expo.
    Route::post('remove-device-token')
        ->name('mobile-api.remove-device-token')
        ->uses('LoginController@removeDeviceToken')
        ->middleware('guest');

    # Mobile Logout
    Route::post('logout')
        ->name('mobile-api.logout')
        ->uses('LoginController@logout')
        ->middleware(['mobile-api', 'auth:mapi']);

    Route::post('password/email')
        ->name('mobile-api.password.email')
        ->uses('ForgotPasswordController@sendResetLinkEmail')
        ->middleware('guest');

    Route::post('password/check-pin')
        ->name('mobile-api.password.check-pin')
        ->uses('ResetPasswordController@checkPin')
        ->middleware('guest');

    Route::put('password/reset')
        ->name('mobile-api.password.reset')
        ->uses('ResetPasswordController@reset')
        ->middleware('guest');

    Route::put('password/reset-with-pin')
        ->name('mobile-api.password.reset-with-pin')
        ->uses('ResetPasswordController@resetWithPin')
        ->middleware('guest');

});
