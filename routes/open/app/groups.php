<?php

Route::group(['namespace' => 'Controllers'], function () {
    // Disable security for looking up a group here.
    Route::bind('group', function ($value) {
        // If we're not allowed to see a user, make it a 404 for the user making the request.
        return \App\Users\Group::where('id', $value)->first() ?? abort(404);
    });

    // Group Emails - WHY
    Route::get('groups/emails/why/{group}/{uuid}')
        ->name('app.public.groups.emails.why')
        ->uses('PublicGroupController@publicEmailsWhy');

    // Group Emails - Unsubscribe
    Route::get('groups/unsubscribe/email/{group}/{user}/{uuid}')
        ->name('app.public.groups.unsubscribe.email')
        ->uses('PublicGroupController@publicUnsubscribeEmail');

    // Group Emails - Unsubscribe POST
    Route::post('groups/unsubscribe/email/{group}/{user}/{uuid}/{resubscribe?}')
        ->name('app.public.groups.unsubscribe.email.save')
        ->uses('PublicGroupController@publicUnsubscribeEmailSave');
});
