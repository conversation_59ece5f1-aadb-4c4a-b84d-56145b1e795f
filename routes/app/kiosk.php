<?php

Route::group(['namespace' => 'Controllers'], function () {

    Route::get('child-checkin')
        ->name('app.kiosk.child-checkin.index')
        ->uses('ChildCheckinController@index');
    // ->middleware('can:edit,crisis');

    Route::get('child-checkin/checkin')
        ->name('app.kiosk.child-checkin.checkin')
        ->uses('ChildCheckinController@checkin');

    Route::get('child-checkin/{child_checkin}')
        ->name('app.kiosk.child-checkin.view')
        ->uses('ChildCheckinController@viewCheckin');

    Route::get('child-checkin/errors/invalid-barcode')
        ->name('app.kiosk.child-checkin.errors.invalid-barcode')
        ->uses('ChildCheckinController@invalidBarcodeError');

});
