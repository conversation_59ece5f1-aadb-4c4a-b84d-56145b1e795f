<?php

Route::group(['namespace' => 'Controllers'], function () {

    // Group index
    Route::get('visitors')
        ->name('app.visitors.index')
        ->uses('VisitorController@index')
        ->middleware('can:view,App\Visitors\Visitor');

    Route::get('visitors/create')
        ->name('app.visitors.create')
        ->uses('VisitorController@create')
        ->middleware('can:create,App\Visitors\Visitor');

    // Group View
    Route::get('visitors/{visitor}')
        ->name('app.visitors.view')
        ->uses('VisitorController@view')
        ->middleware('can:viewVisitor,visitor');

});
