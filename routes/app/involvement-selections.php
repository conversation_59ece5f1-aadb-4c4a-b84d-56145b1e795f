<?php

Route::group(['namespace' => 'Controllers'], function () {
    // Involvement
    Route::get('account/involvement/selections')
        ->name('app.involvement.selections')
        ->uses('InvolvementSelectionController@index')
        ->middleware('can:selections,App\Involvement\Category');

    // Involvement Categories
    Route::get('account/involvement/selections/{category}')
        ->name('app.involvement.selections.category')
        ->uses('InvolvementSelectionController@category')
        ->middleware('can:selections,App\Involvement\Category');

    // Involvement Subarea CSV
    Route::get('account/involvement/selections/{category}/{area}/{subarea}/csv')
        ->name('app.involvement.selections.category.area.subarea.csv')
        ->uses('InvolvementSelectionController@initSubareaCsvDownload')
        ->middleware('can:selections,App\Involvement\Category');

    // Involvement Area CSV
    Route::get('account/involvement/selections/{category}/{area}/csv')
        ->name('app.involvement.selections.category.area.csv')
        ->uses('InvolvementSelectionController@initAreaCsvDownload')
        ->middleware('can:selections,App\Involvement\Category');

    // Involvement Category CSV
    Route::get('account/involvement/selections/{category}/csv')
        ->name('app.involvement.selections.category.csv')
        ->uses('InvolvementSelectionController@initCategoryCsvDownload')
        ->middleware('can:selections,App\Involvement\Category');
});
