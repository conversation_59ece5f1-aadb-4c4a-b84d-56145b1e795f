<?php

Route::group(['namespace' => 'Controllers'], function () {
    // Directory index
    Route::get('directory')
        ->name('app.directory.index')
        ->uses('DirectoryController@index');
    // Photo Directory
    Route::get('directory/photo')
        ->name('app.directory.photo')
        ->uses('Directory<PERSON><PERSON>roll<PERSON>@photo');

    // Directory search
    Route::get('directory/search')
        ->name('app.directory.search')
        ->uses('DirectoryController@search');

    // Directory view family
    Route::get('directory/view/family/{user}')
        ->name('app.directory.view.family')
        ->uses('DirectoryController@viewFamily');
//        ->middleware('can:view,user');

});
