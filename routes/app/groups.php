<?php

Route::group(['namespace' => 'Controllers'], function () {
//     Security for automatic model binding .
    Route::bind('group', function ($value) {
        // If we're not allowed to see a user, make it a 404 for the user making the request.
        return \App\Users\Group::visibleTo(Auth::user())->where('id', $value)->first() ?? abort(404);
    });

    // Group index
    Route::get('groups')
        ->name('app.groups.index')
        ->uses('GroupController@index')
        ->middleware('can:seeGroupPosts,App\Users\Group');

    // Group View
    Route::get('groups/{group}')
        ->name('app.groups.view')
        ->uses('GroupController@view')
        ->middleware('can:userView,group');

    // Group View
    Route::get('groups/{group}/settings')
        ->name('app.groups.settings')
        ->uses('GroupController@settings')
        ->middleware('can:userView,group');

    // Join Group
    Route::post('groups/{group}/join')
        ->name('app.groups.join')
        ->uses('GroupController@join')
        ->middleware('can:addUserToGroup,group');
    // Leave Group
    Route::post('groups/{group}/leave')
        ->name('app.groups.leave')
        ->uses('GroupController@leave')
        ->middleware('can:removeUserFromGroup,group');

//    Route::get('groups/{group}/post/{post}')
//        ->name('app.groups.post.view')
//        ->uses('GroupController@submitPost')
//        ->middleware('can:view,App\Users\Group');

    Route::post('groups/{group}/post/create')
        ->name('app.groups.post.submit')
        ->uses('GroupController@submitPost')
        ->middleware('can:post,group');

//    Route::get('groups/{group}/post/{post}/react')
//        ->name('app.groups.post.react')
//        ->uses('GroupController@submitPost');
//
//    Route::get('groups/{group}/post/{post}/comment/{comment}/react')
//        ->name('app.groups.post.comment.react')
//        ->uses('GroupController@submitPost');

    // Update Group Settings
    Route::post('groups/{group}/settings')
        ->name('app.groups.settings.save')
        ->uses('GroupController@submitSettings')
        ->middleware('can:userView,group');

    // API - Get Posts
    Route::get('groups/{group}/posts')
        ->name('app.groups.api.posts')
        ->uses('\App\MobileApi\V2\Controllers\GroupController@posts');

    // API - Add Comment
    Route::post('groups/{group}/posts/{post}/comment/create')
        ->name('app.groups.api.posts.comment.create')
        ->uses('\App\MobileApi\V2\Controllers\GroupController@submitComment')
        ->middleware('can:comment,post');
});
