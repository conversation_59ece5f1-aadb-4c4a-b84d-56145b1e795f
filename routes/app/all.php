<?php

//include 'calendar.php';
include 'account-files.php';
include 'directory.php';
include 'sermons.php';
include 'groups.php';
include 'visitors.php';
include 'crises.php';
include 'prayers.php';
include 'account.php';
include 'giving.php';
include 'involvement-selections.php';
include 'calendars.php';

Route::group(['namespace' => 'Controllers'], function () {

    // App Home
    Route::get('/')
        ->name('app.home.index')
        ->uses('HomeController@index');

    // Family Attendance Checkin
    Route::post('/attendance/family-checkin')
        ->name('app.attendance.family-checkin.submit')
        ->uses('HomeController@familyAttendanceCheckin');

});