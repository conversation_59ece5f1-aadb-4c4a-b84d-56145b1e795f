<?php

Route::group(['namespace' => 'Controllers'], function () {


    // save
    Route::post('crises/checkin')
        ->name('app.crises.user-checkin.save')
        ->uses('CrisesController@userCheckinSave');
    // ->middleware('can:edit,crisis');

    // User Check-in
    Route::get('crises/checkin')
        ->name('app.crises.user-checkin')
        ->uses('CrisesController@userCheckin');
    // ->middleware('can:view,crisis');

    // view
    Route::get('crises/{crisis}')
        ->name('app.crises.view')
        ->uses('<PERSON><PERSON><PERSON>ontroller@view');
    // ->middleware('can:view,crisis');

    // view
    Route::get('crises/{crisis}/checkins/{checkin}')
        ->name('app.crises.checkins.view')
        ->uses('CrisesController@viewCheckin');
    // ->middleware('can:view,crisis');

    // store
    Route::post('crises/{crisis}/checkins/{checkin}/reply')
        ->name('app.crises.checkins.reply.store')
        ->uses('<PERSON>rises<PERSON>ontroller@checkinReplySave');
    // ->middleware('can:create,App\Crises\Crisis');

});
