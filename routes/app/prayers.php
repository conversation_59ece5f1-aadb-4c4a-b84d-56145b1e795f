<?php

Route::group(['namespace' => 'Controllers'], function () {
    // PrayerList index
    Route::get('prayers')
        ->name('app.prayers.index')
        ->uses('PrayerListController@index');

    Route::get('prayers/request')
        ->name('app.prayers.request')
        ->uses('PrayerListController@request');

    Route::post('prayers/request')
        ->name('app.prayers.request.submit')
        ->uses('PrayerListController@requestSubmit');

    // PrayerList Folder
    Route::get('prayers/folder/{folder}')
        ->name('app.prayers.folders.view')
        ->uses('PrayerListController@folder');

    // PrayerList view family
    Route::get('prayers/{prayer}')
        ->name('app.prayers.view')
        ->uses('PrayerListController@viewPrayer');
});
