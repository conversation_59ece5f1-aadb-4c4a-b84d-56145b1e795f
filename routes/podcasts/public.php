<?php

Route::get('/', 'Podcasts\Controllers\PublicController@home')
    ->name('podcasts.home');

Route::get('/rss/{podcast:id}', 'Podcasts\Controllers\PublicController@rss')
    ->name('podcasts.rss.feed');

Route::get('/{podcast:url_title}/{feed}', 'Podcasts\Controllers\PublicController@rss')
    ->name('podcasts.rss')
    ->where('feed', 'rss|feed');

Route::get('/{account_prefix}/{podcast_url_title}', 'Podcasts\Controllers\PublicController@viewWithAccount')
    ->name('podcasts.podcast.view.withAccount');

Route::get('/{account_prefix}/{podcast_url_title}/{feed}', 'Podcasts\Controllers\PublicController@rssWithAccount')
    ->name('podcasts.rss.withAccount')
    ->where('feed', 'rss|feed|feed.xml');

Route::get('/{account_prefix}/{podcast}/{track:url_title}', 'Podcasts\Controllers\PublicController@viewTrack')
    ->name('podcasts.podcast.viewTrack');

Route::get('/{account_prefix}/{podcast_url_title}/{track:url_title}', 'Podcasts\Controllers\PublicController@viewTrackWithAccount')
    ->name('podcasts.podcast.viewTrackWithPodcastTitle');

Route::get('/{podcast:url_title}', 'Podcasts\Controllers\PublicController@view')
    ->name('podcasts.podcast.view');

Route::get('/mp3/{track}/{uuid}/{track_name}', 'Podcasts\Controllers\PublicController@download')
    ->name('podcasts.podcast.download');
