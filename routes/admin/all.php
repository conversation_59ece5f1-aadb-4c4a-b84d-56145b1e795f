<?php

//include 'announcements.php';
include 'attendance.php';
include 'calendar.php';
include 'changelog.php';
include 'groups.php';
include 'messages.php';
include 'bible-classes.php';
include 'involvement.php';
include 'prayers.php';
include 'reports.php';
include 'roles.php';
include 'grades.php';
include 'schedules.php';
include 'users.php';
include 'visitors.php';
include 'sermons.php';
include 'account-files.php';
include 'account-settings.php';
include 'finance.php';
include 'finance-contributions.php';
include 'finance-expenses.php';
include 'finance-transactions.php';
include 'finance-giving.php';
include 'worship-assignments.php';
include 'crises.php';
include 'podcasts.php';
include 'programs.php';
include 'website.php';
include 'super.php';

Route::group(['middleware' => ['web'], 'namespace' => 'Admin\Controllers'], function () {
    Route::get('/')
        ->name('admin.dashboard.index')
        ->uses('AdminDashboardController@index');

    Route::get('/api-activity-chart-data')
        ->name('admin.dashboard.api.activity-chart-data')
        ->uses('AdminDashboardController@apiGetActivityChartData');
});
