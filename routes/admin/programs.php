<?php

include 'program-groups.php';

Route::group(['namespace' => 'Programs\Controllers'], function () {
    Route::get('programs/create')
        ->name('admin.programs.create')
        ->uses('Program<PERSON>ontroller@create')
        ->middleware('can:create,App\Programs\Program');

    Route::post('programs/create')
        ->name('admin.programs.store')
        ->uses('Program<PERSON>ontroller@createSubmit')
        ->middleware('can:create,App\Programs\Program');

    Route::get('programs')
        ->name('admin.programs.index')
        ->uses('ProgramController@index')
        ->middleware('can:index,App\Programs\Program');

    Route::get('programs/{program}')
        ->name('admin.programs.view')
        ->uses('ProgramController@view')
        ->middleware('can:view,program');

    Route::get('programs/{program}/edit')
        ->name('admin.programs.edit')
        ->uses('Program<PERSON><PERSON>roller@edit')
        ->middleware('can:view,program');

    Route::put('programs/{program}/edit')
        ->name('admin.programs.save')
        ->uses('ProgramController@save')
        ->middleware('can:view,program');

    Route::get('programs/{program}/checkins')
        ->name('admin.programs.checkins')
        ->uses('ProgramController@checkins')
        ->middleware('can:view,program');

    Route::get('programs/{program}/checkins/live')
        ->name('admin.programs.checkins.live')
        ->uses('ProgramController@checkinsLive')
        ->middleware('can:view,program');

    Route::get('programs/{program}/users')
        ->name('admin.programs.users.index')
        ->uses('ProgramUserController@index')
        ->middleware('can:view,program');

    Route::get('programs/{program}/users/registered')
        ->name('admin.programs.users.registered')
        ->uses('ProgramUserController@registered')
        ->middleware('can:view,program');

    Route::get('programs/{program}/users/create')
        ->name('admin.programs.users.create')
        ->uses('ProgramUserController@create')
        ->middleware('can:view,program');

    Route::post('programs/{program}/users/create')
        ->name('admin.programs.users.store')
        ->uses('ProgramUserController@store')
        ->middleware('can:view,program');

    Route::get('programs/{program}/users/{programUser}/edit')
        ->name('admin.programs.users.edit')
        ->uses('ProgramUserController@edit')
        ->middleware('can:view,program');

    Route::post('programs/{program}/users/{programUser}/edit')
        ->name('admin.programs.users.edit.save')
        ->uses('ProgramUserController@save')
        ->middleware('can:view,program');

    Route::get('programs/{program}/forms')
        ->name('admin.programs.forms.index')
        ->uses('ProgramFormController@index')
        ->middleware('can:view,program');

    Route::get('programs/{program}/forms/create')
        ->name('admin.programs.forms.create')
        ->uses('ProgramFormController@create')
        ->middleware('can:view,program');

    Route::post('programs/{program}/forms/create')
        ->name('admin.programs.forms.store')
        ->uses('ProgramFormController@store')
        ->middleware('can:view,program');

    Route::get('programs/{program}/forms/{form}/edit')
        ->name('admin.programs.forms.edit')
        ->uses('ProgramFormController@edit')
        ->middleware('can:view,program');

    Route::post('programs/{program}/forms/{form}/edit')
        ->name('admin.programs.forms.edit.save')
        ->uses('ProgramFormController@save')
        ->middleware('can:view,program');
});
