<?php

Route::group(['namespace' => 'Involvement\Controllers'], function () {
    // Involvement index
    Route::get('involvement')
        ->name('admin.involvement.index')
        ->uses('InvolvementController@index')
        ->middleware('can:index,App\Involvement\Category');

    // Involvement Categories - update sort id
    Route::post('involvement/categories/{category}/update-sort-id')
        ->name('admin.involvement.categories.updateSortId')
        ->uses('InvolvementController@categoryUpdateSortId')
        ->middleware('can:index,App\Involvement\Category');

    // Involvement Areas - update sort id
    Route::post('involvement/areas/{area}/update-sort-id')
        ->name('admin.involvement.areas.updateSortId')
        ->uses('InvolvementController@areaUpdateSortId')
        ->middleware('can:index,App\Involvement\Category');

    Route::get('involvement/volunteers')
        ->name('admin.involvement.volunteers.index')
        ->uses('VolunteerController@index')
        ->middleware('can:manageVolunteers,App\Involvement\Category');

    // --------------------------------------------------------------------

    // Category create
    Route::get('involvement/category/create')
        ->name('admin.involvement.categories.create')
        ->uses('CategoryController@create')
        ->middleware('can:create,App\Involvement\Category');

    // Category store
    Route::post('involvement/category/create')
        ->name('admin.involvement.categories.store')
        ->uses('CategoryController@store')
        ->middleware('can:create,App\Involvement\Category');

    // Category edit
    Route::get('involvement/category/{category}/edit')
        ->name('admin.involvement.categories.edit')
        ->uses('CategoryController@edit')
        ->middleware('can:edit,category');

    // Category save
    Route::put('involvement/category/{category}/edit')
        ->name('admin.involvement.categories.save')
        ->uses('CategoryController@save')
        ->middleware('can:edit,category');

    // Category delete
    Route::get('involvement/category/{category}/delete')
        ->name('admin.involvement.categories.delete')
        ->uses('CategoryController@delete')
        ->middleware('can:index,App\Involvement\Category');

    // Category delete
    Route::delete('involvement/category/{category}/destroy')
        ->name('admin.involvement.categories.destroy')
        ->uses('CategoryController@destroy')
        ->middleware('can:index,App\Involvement\Category');

    // -----------------------------------------------------------------------

    // Area create
    Route::get('involvement/category/{category}/area/create')
        ->name('admin.involvement.areas.create')
        ->uses('AreaController@create')
        ->middleware('can:create,App\Involvement\Category');

    // Area store
    Route::post('involvement/category/{category}/area/create')
        ->name('admin.involvement.areas.store')
        ->uses('AreaController@store')
        ->middleware('can:create,App\Involvement\Category');

    // Area edit
    Route::get('involvement/category/{category}/area/{area}/edit')
        ->name('admin.involvement.areas.edit')
        ->uses('AreaController@edit')
        ->middleware('can:edit,category');

    // Area save
    Route::put('involvement/category/{category}/area/{area}/edit')
        ->name('admin.involvement.areas.save')
        ->uses('AreaController@save')
        ->middleware('can:edit,category');

    // Area delete
    Route::get('involvement/category/{category}/area/{area}/delete')
        ->name('admin.involvement.areas.delete')
        ->uses('AreaController@delete')
        ->middleware('can:index,App\Involvement\Category');

    // Area destroy
    Route::delete('involvement/category/{category}/area/{area}/destroy')
        ->name('admin.involvement.areas.destroy')
        ->uses('AreaController@destroy')
        ->middleware('can:index,App\Involvement\Category');

    // -----------------------------------------------------------------------

    // Subarea create
    Route::get('involvement/category/{category}/area/{area}/subarea/create')
        ->name('admin.involvement.subareas.create')
        ->uses('SubareaController@create')
        ->middleware('can:create,App\Involvement\Category');

    // Subarea store
    Route::post('involvement/category/{category}/area/{area}/subarea/create')
        ->name('admin.involvement.subareas.store')
        ->uses('SubareaController@store')
        ->middleware('can:create,App\Involvement\Category');

    // Subarea edit
    Route::get('involvement/category/{category}/area/{area}/subarea/{subarea}/edit')
        ->name('admin.involvement.subareas.edit')
        ->uses('SubareaController@edit')
        ->middleware('can:edit,category');

    // Subarea save
    Route::put('involvement/category/{category}/area/{area}/subarea/{subarea}/edit')
        ->name('admin.involvement.subareas.save')
        ->uses('SubareaController@save')
        ->middleware('can:edit,category');

    // Category delete
    Route::get('involvement/category/{category}/area/{area}/subarea/{subarea}/delete')
        ->name('admin.involvement.subareas.delete')
        ->uses('SubareaController@delete')
        ->middleware('can:index,App\Involvement\Category');

    // Category destroy
    Route::delete('involvement/category/{category}/area/{area}/subarea/{subarea}/destroy')
        ->name('admin.involvement.subareas.destroy')
        ->uses('SubareaController@destroy')
        ->middleware('can:index,App\Involvement\Category');

    // ----------------------------------------------------------------------

    // Areas JSON
    Route::get('involvement/category/{category}/areas')
        ->name('admin.involvement.categories.areas.json')
        ->uses('CategoryController@getAreas')
        ->middleware('can:index,App\Involvement\Category');

    // Subareas JSON
    Route::get('involvement/category/{category}/areas/{area}/subareas')
        ->name('admin.involvement.areas.subareas.json')
        ->uses('CategoryController@getSubareas')
        ->middleware('can:index,App\Involvement\Category');

    // Involvement Category VIEW
    Route::get('involvement/category/{category}')
        ->name('admin.involvement.categories.view')
        ->uses('InvolvementController@viewCategory')
        ->middleware('can:index,App\Involvement\Category');
});
