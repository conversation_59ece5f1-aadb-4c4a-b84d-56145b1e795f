<?php

Route::group(['namespace' => 'Super\Controllers'], function () {
    // Accounts index
    Route::get('_')
        ->name('super.settings.index-root')
        ->uses('SettingController@index')
        ->middleware('can:index,App\Accounts\Account');

    // Setting index
    Route::get('_/accounts')
        ->name('super.accounts.index')
        ->uses('Setting<PERSON>ontroller@index')
        ->middleware('can:index,App\Accounts\Account');
    Route::get('_/accounts/{account}/settings')
        ->name('super.accounts.view')
        ->uses('SettingController@accountSettings')
        ->middleware('can:index,App\Accounts\Account');
    Route::post('_/accounts/{account}/settings')
        ->name('super.accounts.settings.save')
        ->uses('SettingController@saveAccountSettings')
        ->middleware('can:manage,App\Accounts\Account');

    Route::get('_/utilities/special-days')
        ->name('super.utilities.special-days')
        ->uses('SettingController@specialDays')
        ->middleware('can:index,App\Accounts\Account');
    Route::post('_/utilities/special-days')
        ->name('super.utilities.special-days.submit')
        ->uses('SettingController@specialDaysSubmit')
        ->middleware('can:manage,App\Accounts\Account');

    Route::get('_/users')
        ->name('super.users.index')
        ->uses('UserController@index')
        ->middleware('can:index,App\Accounts\Account');
    Route::get('_/users/{user_id}')
        ->name('super.users.view')
        ->uses('UserController@view')
        ->middleware('can:index,App\Accounts\Account');
    Route::post('_/users/{user_id}')
        ->name('super.users.send.mobile-notification')
        ->uses('UserController@sendMobileNotification')
        ->middleware('can:index,App\Accounts\Account');
});
