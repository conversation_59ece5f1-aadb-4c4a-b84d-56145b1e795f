<?php

Route::group(['namespace' => 'Finance\Controllers'], function () {

    Route::get('finance/expenses')
        ->name('admin.finance.expenses.index')
        ->uses('ExpenseController@index')
        ->middleware('can:index,App\Finance\Transaction');

    Route::get('finance/expenses/create')
        ->name('admin.finance.expenses.create')
        ->uses('ExpenseController@createExpenses')
        ->middleware('can:createExpenses,App\Finance\Transaction');

});
