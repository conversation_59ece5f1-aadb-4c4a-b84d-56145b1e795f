<?php

Route::group(['namespace' => 'BibleClasses\Controllers'], function () {
    // Bible Class index
    Route::get('bible-classes')
        ->name('admin.bible-classes.index')
        ->uses('BibleClassController@index')
        ->middleware('can:index,App\BibleClasses\BibleClass');

    // Bible Class create
    Route::get('bible-classes/groups/{bible_class_group}/classes/create')
        ->name('admin.bible-classes.create')
        ->uses('BibleClassController@create')
        ->middleware('can:create,App\BibleClasses\BibleClass');

    // Bible Class create
    Route::get('bible-classes/groups/{bible_class_group}/classes/copy')
        ->name('admin.bible-classes.copy')
        ->uses('BibleClassController@copy')
        ->middleware('can:create,App\BibleClasses\BibleClass');

    // Bible Class store
    Route::post('bible-classes/groups/{bible_class_group}/classes/create')
        ->name('admin.bible-classes.store')
        ->uses('BibleClassController@store')
        ->middleware('can:create,App\BibleClasses\BibleClass');

    // Bible Class store
    Route::post('bible-classes/groups/{bible_class_group}/classes/copy')
        ->name('admin.bible-classes.copy.submit')
        ->uses('BibleClassController@copySubmit')
        ->middleware('can:create,App\BibleClasses\BibleClass');

    // Bible Class edit
    Route::get('bible-classes/groups/{bible_class_group}/classes/{bible_class}/edit')
        ->name('admin.bible-classes.edit')
        ->uses('BibleClassController@edit')
        ->middleware('can:update,bible_class');

    // Bible Class save
    Route::put('bible-classes/groups/{bible_class_group}/classes/{bible_class}/edit')
        ->name('admin.bible-classes.save')
        ->uses('BibleClassController@save')
        ->middleware('can:update,bible_class');

    // Bible Class Registration
    Route::get('bible-classes/groups/{bible_class_group}/classes/{bible_class}/registration')
        ->name('admin.bible-classes.registration.edit')
        ->uses('BibleClassRegistrationController@view')
        ->middleware('can:update,bible_class');

    // Bible Class Registration Save
    Route::put('bible-classes/groups/{bible_class_group}/classes/{bible_class}/registration')
        ->name('admin.bible-classes.registration.save')
        ->uses('BibleClassRegistrationController@save')
        ->middleware('can:update,bible_class');

    // Bible Class Registration add user
    Route::post('bible-classes/groups/{bible_class_group}/classes/{bible_class}/add-users')
        ->name('admin.bible-classes.registration.add-users')
        ->uses('BibleClassRegistrationController@saveNewUsers')
        ->middleware('can:update,bible_class');

    // Bible Class Registration Save
    Route::delete('bible-classes/groups/{bible_class_group}/classes/{bible_class}/remove-user/{user}')
        ->name('admin.bible-classes.registration.remove-user')
        ->uses('BibleClassRegistrationController@removeUser')
        ->middleware('can:update,bible_class');

    // -------------- PDFs --------------

    // Bible Class Registration
    Route::get('bible-classes/groups/{bible_class_group}/classes/{bible_class}/pdf/registration')
        ->name('admin.bible-classes.pdfs.registration')
        ->uses('BibleClassRegistrationController@pdfRegistrationSheet')
        ->middleware('can:view,bible_class');

    // Bible Class Registration by User/Type
    Route::get('bible-classes/groups/{bible_class_group}/type/{type}/pdf/registration-by-user')
        ->name('admin.bible-classes.pdfs.registration-by-user')
        ->uses('BibleClassGroupController@membershipByUserPdf')
        ->middleware('can:view,bible_class_group');

    // Bible Class Registration by Class
    Route::get('bible-classes/groups/{bible_class_group}/type/{type}/pdf/registration-by-class')
        ->name('admin.bible-classes.pdfs.registration-by-class')
        ->uses('BibleClassGroupController@membershipByClassPdf')
        ->middleware('can:view,bible_class_group');

    // -------------- GROUPS --------------

    // Bible Class Group create
    Route::get('bible-classes/groups/create')
        ->name('admin.bible-classes.groups.create')
        ->uses('BibleClassGroupController@create')
        ->middleware('can:create,App\BibleClasses\BibleClassGroup');

    // Bible Class Group view
    Route::get('bible-classes/groups/{bible_class_group}')
        ->name('admin.bible-classes.groups.view')
        ->uses('BibleClassGroupController@view')
        ->middleware('can:view,bible_class_group');

    // Bible Class Group store
    Route::post('bible-classes/groups/create')
        ->name('admin.bible-classes.groups.store')
        ->uses('BibleClassGroupController@store')
        ->middleware('can:store,App\BibleClasses\BibleClassGroup');

    // Bible Class Group edit
    Route::get('bible-classes/groups/{bible_class_group}/edit')
        ->name('admin.bible-classes.groups.edit')
        ->uses('BibleClassGroupController@edit')
        ->middleware('can:edit,bible_class_group');

    // Bible Class Group save
    Route::post('bible-classes/groups/{bible_class_group}/edit')
        ->name('admin.bible-classes.groups.save')
        ->uses('BibleClassGroupController@save')
        ->middleware('can:update,bible_class_group');

    // Bible Class Group delete
    Route::delete('bible-classes/groups/{bible_class_group}/delete')
        ->name('admin.bible-classes.groups.delete')
        ->uses('BibleClassGroupController@delete')
        ->middleware('can:update,bible_class_group');
});
