<?php

Route::group(['namespace' => 'Programs\Controllers'], function () {

    Route::get('programs/{program}/groups')
        ->name('admin.programs.groups')
        ->uses('ProgramGroupController@index')
        ->middleware('can:view,program');

    Route::get('programs/{program}/groups/create')
        ->name('admin.programs.groups.create')
        ->uses('ProgramGroupController@create')
        ->middleware('can:createGroup,program');

    Route::post('programs/{program}/groups/create')
        ->name('admin.programs.groups.store')
        ->uses('ProgramGroupController@store')
        ->middleware('can:createGroup,program');

    Route::get('programs/{program}/groups/{programGroup}/edit')
        ->name('admin.programs.groups.edit')
        ->uses('ProgramGroupController@edit')
        ->middleware('can:edit,programGroup');

    Route::put('programs/{program}/groups/{programGroup}/edit')
        ->name('admin.programs.groups.update')
        ->uses('ProgramGroupController@update')
        ->middleware('can:edit,programGroup');

    Route::get('programs/{program}/groups/{programGroup}')
        ->name('admin.programs.groups.view')
        ->uses('ProgramController@viewGroup')
        ->middleware('can:view,programGroup');

});
