<?php

use App\Users\User;
use Illuminate\Support\Facades\Auth;

Route::group(['namespace' => 'Users\Controllers'], function () {
    // Security for automatic model binding.
    Route::bind('user', function ($value) {
        // If we're not allowed to see a user, make it a 404 for the user making the request.
        return User::visibleTo(Auth::user())->where('id', $value)->first() ?? abort(404);
    });

    // STANDARD

    Route::get('users/create')
        ->name('admin.users.create')
        ->uses('UserController@create')
        ->middleware('can:create,App\Users\User');

    Route::post('users/create')
        ->name('admin.users.store')
        ->uses('UserController@store')
        ->middleware('can:create,App\Users\User');

    Route::get('users')
        ->name('admin.users.index')
        ->uses('UserController@index')
        ->middleware('can:retrieve,App\Users\User');

    Route::get('users/{user}')
        ->name('admin.users.view')
        ->uses('UserController@view')
        ->middleware('can:view,user');

    Route::get('users/{user}/create/quick')
        ->name('admin.users.create.quick')
        ->uses('UserController@quickCreate')
        ->middleware('can:update,user');

    Route::post('users/{user}/create/quick')
        ->name('admin.users.store.quick')
        ->uses('UserController@quickCreateSubmit')
        ->middleware('can:update,user');

    Route::get('users/{user}/clear-sessions')
        ->name('admin.users.clear-sessions')
        ->uses('UserController@clearSessionsModal')
        ->middleware('can:update,user');

    Route::post('users/{user}/clear-sessions')
        ->name('admin.users.clear-sessions.submit')
        ->uses('UserController@submitClearSessions')
        ->middleware('can:update,user');

    Route::get('users/{user}/edit')
        ->name('admin.users.edit')
        ->uses('UserController@edit')
        ->middleware('can:edit,user');

    Route::put('users/{user}/edit')
        ->name('admin.users.save.put')
        ->uses('UserController@save')
        ->middleware('can:update,user');

    Route::post('users/{user}/edit')
        ->name('admin.users.save')
        ->uses('UserController@save')
        ->middleware('can:update,user');

    Route::get('users/{user}/change-head-of-household')
        ->name('admin.users.change-head-of-household')
        ->uses('UserController@changeHead')
        ->middleware('can:update,user');

    Route::post('users/{user}/change-head-of-household')
        ->name('admin.users.change-head-of-household.submit')
        ->uses('UserController@changeHeadSubmit')
        ->middleware('can:update,user');

    Route::get('users/{user}/delete')
        ->name('admin.users.delete')
        ->uses('UserController@delete')
        ->middleware('can:delete,user');

    Route::delete('users/{user}/destroy')
        ->name('admin.users.destroy')
        ->uses('UserController@destroy')
        ->middleware('can:delete,user');

    Route::get('users/{user}/groups-roles')
        ->name('admin.users.groups-roles')
        ->uses('UserController@groupsAndRoles')
        ->middleware('can:update,user');

    Route::get('users/{user}/visitor-tracking/start')
        ->name('admin.users.visitor-tracking.start')
        ->uses('UserController@startVisitorTracking')
        ->middleware('can:view,user');

    Route::post('users/{user}/visitor-tracking/start')
        ->name('admin.users.visitor-tracking.start.submit')
        ->uses('UserController@startVisitorTrackingSubmit')
        ->middleware('can:view,user');

    // CHURCH OFFICE

    Route::get('users/{user}/church-office/create')
        ->name('admin.users.church-office.create')
        ->uses('ChurchOfficeController@create')
        ->middleware('can:update,user');

    Route::post('users/{user}/church-office/create')
        ->name('admin.users.church-office.store')
        ->uses('ChurchOfficeController@store')
        ->middleware('can:update,user');

    Route::get('users/{user}/church-office/edit/{church_office}')
        ->name('admin.users.church-office.edit')
        ->uses('ChurchOfficeController@edit')
        ->middleware('can:edit,church_office');

    Route::post('users/{user}/church-office/edit/{church_office}')
        ->name('admin.users.church-office.save')
        ->uses('ChurchOfficeController@save')
        ->middleware('can:edit,church_office');

    Route::delete('users/{user}/church-office/delete/{church_office}')
        ->name('admin.users.church-office.delete')
        ->uses('ChurchOfficeController@delete')
        ->middleware('can:delete,church_office');

    // ADDRESSES

    Route::get('users/{user}/address/create')
        ->name('admin.users.address.create')
        ->uses('AddressController@create')
        ->middleware('can:create,App\Users\Address');

    Route::post('users/{user}/address/create')
        ->name('admin.users.address.store')
        ->uses('AddressController@store')
        ->middleware('can:create,App\Users\Address');

    Route::get('users/{user}/address/edit/{address}')
        ->name('admin.users.address.edit')
        ->uses('AddressController@edit')
        ->middleware('can:edit,address');

    Route::post('users/{user}/address/edit/{address}')
        ->name('admin.users.address.save')
        ->uses('AddressController@save')
        ->middleware('can:update,address');

    Route::post('users/{user}/address/delete/{address}')
        ->name('admin.users.address.delete')
        ->uses('AddressController@delete')
        ->middleware('can:delete,address');

    Route::get('users/{user}/address/{address}')
        ->name('admin.users.address.show')
        ->uses('AddressController@show')
        ->middleware('can:view,address');

    // PHONES

    Route::get('users/{user}/phone/create')
        ->name('admin.users.phone.create')
        ->uses('PhoneController@create')
        ->middleware('can:create,App\Users\Phone');

    Route::post('users/{user}/phone/create')
        ->name('admin.users.phone.store')
        ->uses('PhoneController@store')
        ->middleware('can:create,App\Users\Phone');

    Route::get('users/{user}/phone/edit/{phone}')
        ->name('admin.users.phone.edit')
        ->uses('PhoneController@edit')
        ->middleware('can:edit,phone');

    Route::post('users/{user}/phone/edit/{phone}')
        ->name('admin.users.phone.save')
        ->uses('PhoneController@save')
        ->middleware('can:update,phone');

    Route::post('users/{user}/phone/delete/{phone}')
        ->name('admin.users.phone.delete')
        ->uses('PhoneController@delete')
        ->middleware('can:delete,phone');

    Route::get('users/{user}/phone/{phone}')
        ->name('admin.users.phone.show')
        ->uses('PhoneController@show')
        ->middleware('can:view,phone');

    // EMAILS

    Route::get('users/{user}/email/create')
        ->name('admin.users.email.create')
        ->uses('EmailController@create')
        ->middleware('can:create,App\Users\Email');

    Route::post('users/{user}/email/create')
        ->name('admin.users.email.store')
        ->uses('EmailController@store')
        ->middleware('can:create,App\Users\Email');

    Route::get('users/{user}/email/edit/{email}')
        ->name('admin.users.email.edit')
        ->uses('EmailController@edit')
        ->middleware('can:edit,email');

    Route::post('users/{user}/email/edit/{email}')
        ->name('admin.users.email.save')
        ->uses('EmailController@save')
        ->middleware('can:update,email');

    Route::post('users/{user}/email/delete/{email}')
        ->name('admin.users.email.delete')
        ->uses('EmailController@delete')
        ->middleware('can:delete,email');

    Route::get('users/{user}/email/{email}')
        ->name('admin.users.email.show')
        ->uses('EmailController@show')
        ->middleware('can:view,email');

    // Photos

    Route::get('users/{user}/photos')
        ->name('admin.users.photos.index')
        ->uses('PhotoController@index')
        ->middleware('can:view,user');

    Route::get('users/{user}/photos/create')
        ->name('admin.users.photos.create')
        ->uses('PhotoController@create')
        ->middleware('can:create,App\Users\Photo');

    Route::post('users/{user}/photos/create')
        ->name('admin.users.photos.store')
        ->uses('PhotoController@store')
        ->middleware('can:create,App\Users\Photo');

    Route::get('users/{user}/photos/{photo}/edit')
        ->name('admin.users.photos.edit')
        ->uses('PhotoController@edit')
        ->middleware('can:edit,photo');

    Route::post('users/{user}/photos/{photo}/edit')
        ->name('admin.users.photos.save')
        ->uses('PhotoController@save')
        ->middleware('can:update,photo');

    Route::post('users/{user}/photos/{photo}/rotate')
        ->name('admin.users.photos.rotate')
        ->uses('PhotoController@rotate')
        ->middleware('can:update,photo');

    Route::delete('users/{user}/photos/{photo}/delete')
        ->name('admin.users.photos.delete')
        ->uses('PhotoController@destroy')
        ->middleware('can:delete,photo');

    // Attendance

    Route::get('users/{user}/attendance')
        ->name('admin.users.attendance.index')
        ->uses('UserController@attendance')
        ->middleware('can:view,user');

    // Giving

    Route::get('users/{user}/giving')
        ->name('admin.users.giving.index')
        ->uses('UserController@giving')
        ->middleware('can:view,user');

    Route::get('users/{user}/giving/method/{paymentMethod}/delete')
        ->name('admin.users.giving.method.delete')
        ->uses('UserController@showDeletePaymentMethodModal')
        ->middleware('can:delete,user');

    Route::delete('users/{user}/giving/method/{paymentMethod}/delete')
        ->name('admin.users.giving.method.delete.submit')
        ->uses('UserController@deletePaymentMethod')
        ->middleware('can:delete,user');

    // Involvement

    Route::get('users/{user}/involvement')
        ->name('admin.users.involvement.index')
        ->uses('InvolvementController@index')
        ->middleware('can:view,user');

    Route::put('users/{user}/involvement/edit')
        ->name('admin.users.involvement.save')
        ->uses('InvolvementController@save')
        ->middleware('can:update,user');

    // Other

    Route::get('users/{user}/password/reset')
        ->name('admin.users.password.reset')
        ->uses('UserController@showPasswordResetModal')
        ->middleware('auth');

    Route::post('users/{user}/password/reset')
        ->name('admin.users.password.reset.submit')
        ->uses('UserController@sendResetLinkEmail')
        ->middleware('auth');

    Route::get('users/{user}/welcome-email')
        ->name('admin.users.welcome-email')
        ->uses('UserController@showWelcomeEmailModal')
        ->middleware('auth');

    Route::post('users/{user}/welcome-email')
        ->name('admin.users.welcome-email.submit')
        ->uses('UserController@sendWelcomeEmail')
        ->middleware('auth');

    Route::post('users/{user}/mobile-notification/send')
        ->name('admin.users.mobile-notification.send')
        ->uses('UserController@sendMobileNotification')
        ->middleware('auth');
});
