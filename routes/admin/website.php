<?php

Route::group(['namespace' => 'Website\Controllers'], function () {
    Route::get('website')
        ->name('admin.website.index')
        ->uses('WebsiteController@index')
        ->middleware('can:index,App\Accounts\Account');

    Route::get('website/pages')
        ->name('admin.website.pages.index')
        ->uses('WebsitePageController@index')
        ->middleware('can:managePages,App\Website\Website');

    Route::get('website/pages/create')
        ->name('admin.website.pages.create')
        ->uses('WebsitePageController@create')
        ->middleware('can:managePages,App\Website\Website');

    Route::post('website/pages/{page}/update-sort-id')
        ->name('admin.website.pages.updateSortId')
        ->uses('WebsitePageController@updateSortId')
        ->middleware('can:managePages,App\Website\Website');

    Route::get('website/pages/{page}/edit')
        ->name('admin.website.pages.edit')
        ->uses('WebsitePageController@edit')
        ->middleware('can:managePages,App\Website\Website');

    Route::put('website/pages/{page}/edit')
        ->name('admin.website.pages.update')
        ->uses('WebsitePageController@update')
        ->middleware('can:managePages,App\Website\Website');

    Route::get('website/settings/logos')
        ->name('admin.website.settings.logos')
        ->uses('WebsiteSettingController@logos')
        ->middleware('can:manage,App\Website\Website');

    Route::post('website/settings/logos/{setting}')
        ->name('admin.website.settings.logos.update')
        ->uses('WebsiteSettingController@saveLogo')
        ->middleware('can:manage,App\Website\Website');

    Route::get('website/settings/domain')
        ->name('admin.website.settings.domain')
        ->uses('WebsiteSettingController@domain')
        ->middleware('can:manage,App\Website\Website');

    Route::post('website/settings/domain')
        ->name('admin.website.settings.domain.update')
        ->uses('WebsiteSettingController@saveDomain')
        ->middleware('can:manage,App\Website\Website');

    Route::get('website/settings/carousel')
        ->name('admin.website.settings.carousel')
        ->uses('WebsiteSettingController@carousel')
        ->middleware('can:manage,App\Website\Website');

    Route::get('website/settings/quick-links')
        ->name('admin.website.settings.quick-links')
        ->uses('WebsiteSettingController@quickLinks')
        ->middleware('can:manage,App\Website\Website');

    Route::get('website/settings/redirects')
        ->name('admin.website.settings.redirects')
        ->uses('WebsiteSettingController@redirects')
        ->middleware('can:manage,App\Website\Website');

    Route::get('website/settings/social-links')
        ->name('admin.website.settings.social-links')
        ->uses('WebsiteSettingController@socialLinks')
        ->middleware('can:manage,App\Website\Website');

    Route::get('website/settings/content')
        ->name('admin.website.settings.content')
        ->uses('WebsiteSettingController@content')
        ->middleware('can:manage,App\Website\Website');

    Route::get('website/settings/special-links')
        ->name('admin.website.settings.special-links')
        ->uses('WebsiteSettingController@specialLinks')
        ->middleware('can:manage,App\Website\Website');

    Route::delete('website/settings/logos/{setting_key}', 'WebsiteSettingController@deleteLogo')
        ->name('admin.website.settings.logos.delete')
        ->middleware('can:manage,App\Website\Website');
});
