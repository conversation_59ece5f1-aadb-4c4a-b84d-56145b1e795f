<?php

Route::group(['namespace' => 'Reports\Controllers'], function () {
    // Reports index
    Route::get('reports')
        ->name('admin.reports.index')
        ->uses('ReportController@index')
        ->middleware('can:index-reports');

    // Missing Persons Report
    Route::get('reports/missing-persons')
        ->name('admin.reports.missing-persons')
        ->uses('ReportController@missingPersons')
        ->middleware('can:view-reports');

    // Attendance Report
    Route::get('reports/attendance')
        ->name('admin.reports.attendance')
        ->uses('ReportController@attendance')
        ->middleware('can:view-reports');

    // Birthday Report
    Route::get('reports/birthday')
        ->name('admin.reports.birthday')
        ->uses('ReportController@birthday')
        ->middleware('can:view-reports');

    // Anniversary Report
    Route::get('reports/anniversary')
        ->name('admin.reports.anniversary')
        ->uses('ReportController@anniversary')
        ->middleware('can:view-reports');

    // Baptism Birthday Report
    Route::get('reports/baptism-birthday')
        ->name('admin.reports.baptism-birthday')
        ->uses('ReportController@baptismBirthday')
        ->middleware('can:view-reports');

    // PRINTABLES

    Route::get('reports/printables/mailing-labels')
        ->name('admin.reports.printables.mailing-labels.index')
        ->uses('ReportController@mailingLabels')
        ->middleware('can:view-reports');

    Route::get('reports/printables/directory')
        ->name('admin.reports.printables.directory.index')
        ->uses('ReportController@directory')
        ->middleware('can:view-reports');

    Route::get('reports/printables/youth-directory')
        ->name('admin.reports.printables.youth-directory.index')
        ->uses('ReportController@youthDirectory')
        ->middleware('can:view-reports');

    Route::get('reports/printables/photo-directory')
        ->name('admin.reports.printables.photo-directory.index')
        ->uses('ReportController@photoDirectory')
        ->middleware('can:view-reports');
});
