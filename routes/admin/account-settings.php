<?php

Route::group(['namespace' => 'Accounts\Controllers'], function () {
    // Setting index
    Route::get('account/settings')
        ->name('admin.accounts.settings.index')
        ->uses('Setting<PERSON>ontroller@index')
        ->middleware('can:index,App\Accounts\Account');
    //
    Route::get('account/settings/billing')
        ->name('admin.accounts.billing.index')
        ->uses('SettingController@billing')
        ->middleware('can:index,App\Accounts\Account');

    //
    Route::get('account/settings/church-offices')
        ->name('admin.accounts.settings.church-offices.index')
        ->uses('ChurchOfficeController@index')
        ->middleware('can:index,App\Accounts\Account');
    Route::get('account/settings/church-offices/create')
        ->name('admin.accounts.settings.church-offices.create')
        ->uses('ChurchOfficeController@create')
        ->middleware('can:index,App\Accounts\Account');
    Route::get('account/settings/church-offices/{church_office}/users')
        ->name('admin.accounts.settings.church-offices.users.index')
        ->uses('ChurchOfficeController@users')
        ->middleware('can:index,App\Accounts\Account');
    Route::get('account/settings/church-offices/{church_office}/edit')
        ->name('admin.accounts.settings.church-offices.edit')
        ->uses('ChurchOfficeController@edit')
        ->middleware('can:index,App\Accounts\Account');
    Route::post('account/settings/church-offices/create')
        ->name('admin.accounts.settings.church-offices.create.submit')
        ->uses('ChurchOfficeController@createSubmit')
        ->middleware('can:index,App\Accounts\Account');
    Route::post('account/settings/church-offices/{church_office}/edit')
        ->name('admin.accounts.settings.church-offices.edit.submit')
        ->uses('ChurchOfficeController@editSubmit')
        ->middleware('can:index,App\Accounts\Account');
    Route::delete('account/settings/church-offices/{church_office}/delete')
        ->name('admin.accounts.settings.church-offices.delete.submit')
        ->uses('ChurchOfficeController@deleteSubmit')
        ->middleware('can:index,App\Accounts\Account');

    //
    Route::get('account/settings/features')
        ->name('admin.accounts.features.index')
        ->uses('SettingController@features')
        ->middleware('can:index,App\Accounts\Account');
    Route::post('account/settings/features')
        ->name('admin.accounts.features.save')
        ->uses('SettingController@featuresSave')
        ->middleware('can:index,App\Accounts\Account');

    // Congregation Information
    Route::get('account/settings/congregation/information')
        ->name('admin.accounts.settings.congregation-information.index')
        ->uses('SettingController@congregationInformation')
        ->middleware('can:index,App\Accounts\Account');

    Route::post('account/settings/congregation/information')
        ->name('admin.accounts.settings.congregation-information.save')
        ->uses('SettingController@congregationInformationSave')
        ->middleware('can:index,App\Accounts\Account');

    // Account Information
    Route::get('account/settings/account/information')
        ->name('admin.accounts.settings.account-information.index')
        ->uses('SettingController@accountInformation')
        ->middleware('can:index,App\Accounts\Account');

    Route::post('account/settings/account/information')
        ->name('admin.accounts.settings.account-information.save')
        ->uses('SettingController@accountInformationSave')
        ->middleware('can:index,App\Accounts\Account');

    // Setting search
    Route::get('account/settings/search')
        ->name('admin.accounts.settings.search')
        ->uses('SettingController@search')
        ->middleware('can:search,App\Accounts\Account');

    // Setting create
    Route::get('account/settings/create')
        ->name('admin.accounts.settings.create')
        ->uses('SettingController@create')
        ->middleware('can:manage,App\Accounts\Account');

    // Setting store
    Route::post('account/settings/create')
        ->name('admin.accounts.settings.store')
        ->uses('SettingController@store')
        ->middleware('can:manage,App\Accounts\Account');

    // Setting edit
    Route::get('account/settings/{accountFile}/edit')
        ->name('admin.accounts.settings.edit')
        ->uses('SettingController@edit')
        ->middleware('can:edit,accountFile');

    // Setting save
    Route::put('account/settings/{accountFile}/edit')
        ->name('admin.accounts.settings.save')
        ->uses('SettingController@save')
        ->middleware('can:edit,accountFile');

    // Setting save
    Route::delete('account/settings/{accountFile}/delete')
        ->name('admin.accounts.settings.delete')
        ->uses('SettingController@delete')
        ->middleware('can:delete,accountFile');

    // Welcome Emails
    Route::get('account/settings/welcome-emails')
        ->name('admin.accounts.settings.welcome-emails')
        ->uses('SettingController@welcomeEmails')
        ->middleware('can:manage,App\Accounts\Account');

    // Send Welcome Emails
    Route::post('account/settings/welcome-emails/submit')
        ->name('admin.accounts.settings.welcome-emails.submit')
        ->uses('SettingController@sendWelcomeEmails')
        ->middleware('can:manage,App\Accounts\Account');

    // Send Credit Card
    Route::post('account/settings/credit-card/submit')
        ->name('admin.accounts.settings.credit-card.submit')
        ->uses('SettingController@sendCreditCard')
        ->middleware('can:index,App\Accounts\Account');

    // Send Secure Note
    Route::post('account/settings/secure-note/submit')
        ->name('admin.accounts.settings.secure-note.submit')
        ->uses('SettingController@sendSecureNote')
        ->middleware('can:index,App\Accounts\Account');

    Route::get('account/settings/invoice/pdf/{invoice}')
        ->name('admin.accounts.settings.invoice.pdf')
        ->uses('SettingController@downloadInvoicePDF')
        ->middleware('can:index,App\Accounts\Account');

    Route::get('account/settings/partnerships')
        ->name('admin.accounts.settings.partnerships')
        ->uses('SettingController@partnerships')
        ->middleware('can:index,App\Accounts\Account');
});
