<?php

Route::group(['namespace' => 'Announcements\Controllers'], function () {

    // Announcement index
    Route::get('announcements')
        ->name('admin.announcements.index')
        ->uses('AnnouncementController@index')
        ->middleware('can:index,App\Announcements\Announcement');

    // Announcement search
    Route::get('announcements/search')
        ->name('admin.announcements.search')
        ->uses('AnnouncementController@search')
        ->middleware('can:search,App\Announcements\Announcement');

    // Announcement create
    Route::get('announcements/create')
        ->name('admin.announcements.create')
        ->uses('AnnouncementController@create')
        ->middleware('can:create,App\Announcements\Announcement');

    // Announcement store
    Route::post('announcements/create')
        ->name('admin.announcements.store')
        ->uses('AnnouncementController@store')
        ->middleware('can:create,App\Announcements\Announcement');

    // Announcement edit
    Route::get('announcements/{announcement}/edit')
        ->name('admin.announcements.edit')
        ->uses('AnnouncementController@edit')
        ->middleware('can:edit,announcement');

    // Announcement save
    Route::put('announcements/{announcement}/edit')
        ->name('admin.announcements.save')
        ->uses('AnnouncementController@save')
        ->middleware('can:edit,announcement');

    // Announcement save
    Route::delete('announcements/{announcement}/delete')
        ->name('admin.announcements.delete')
        ->uses('AnnouncementController@delete')
        ->middleware('can:delete,announcement');

    // WorshipAssignment Position - update sort id
    Route::post('announcements/{announcement}/update-sort-id')
        ->name('admin.announcements.updateSortId')
        ->uses('AnnouncementController@updateSortId')
        ->middleware('can:edit,announcement');

});
