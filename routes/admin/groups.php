<?php

Route::group(['namespace' => 'Users\Controllers'], function () {

    Route::get('groups')
        ->name('admin.groups.index')
        ->uses('GroupController@index')
        ->middleware('can:manage,App\Users\Group');

    Route::get('groups/create')
        ->name('admin.groups.create')
        ->uses('GroupController@create')
        ->middleware('can:create,App\Users\Group');

    Route::put('groups/create')
        ->name('admin.groups.store')
        ->uses('GroupController@store')
        ->middleware('can:create,App\Users\Group');

    Route::get('groups/{group}')
        ->name('admin.groups.view')
        ->uses('GroupController@view')
        ->middleware('can:view,group');

    Route::get('groups/{group}/settings')
        ->name('admin.groups.settings')
        ->uses('GroupController@settings')
        ->middleware('can:edit,group');

    Route::post('groups/{group}/settings')
        ->name('admin.groups.settings.save')
        ->uses('GroupController@saveSettings')
        ->middleware('can:save,group');

    Route::get('groups/{group}/posts')
        ->name('admin.groups.posts')
        ->uses('GroupController@posts')
        ->middleware('can:edit,group');

    Route::post('groups/{group}/posts')
        ->name('admin.groups.posts.save')
        ->uses('GroupController@savePosts')
        ->middleware('can:save,group');

    Route::get('groups/{group}/messaging')
        ->name('admin.groups.messaging')
        ->uses('GroupController@messaging')
        ->middleware('can:edit,group');

    Route::post('groups/{group}/messaging')
        ->name('admin.groups.messaging.save')
        ->uses('GroupController@saveMessaging')
        ->middleware('can:save,group');

    Route::get('groups/{group}/members')
        ->name('admin.groups.members')
        ->uses('GroupController@members')
        ->middleware('can:edit,group');

    Route::post('groups/{group}/edit')
        ->name('admin.groups.save')
        ->uses('GroupController@save')
        ->middleware('can:save,group');

    Route::post('groups/{group}/detach-user/{user}')
        ->name('admin.groups.detach-user')
        ->uses('GroupController@detachUser')
        ->middleware('can:edit,group');

    Route::delete('groups/{group}/destroy')
        ->name('admin.groups.destroy')
        ->uses('GroupController@destroy')
        ->middleware('can:create,App\Users\Group');

    // ADMINS

    Route::get('groups/{group}/admins')
        ->name('admin.groups.admins.index')
        ->uses('GroupController@admins')
        ->middleware('can:manage,App\Users\Group');

    // SENDERS

    Route::get('groups/{group}/senders')
        ->name('admin.groups.senders.index')
        ->uses('GroupController@senders')
        ->middleware('can:manage,App\Users\Group');

    // MESSAGE HISTORY

    Route::get('groups/{group}/message-history')
        ->name('admin.groups.message-history.index')
        ->uses('GroupMessageHistoryController@index')
        ->middleware('can:manage,App\Users\Group');

    Route::get('groups/{group}/message-history/{message}')
        ->name('admin.groups.message-history.view')
        ->uses('GroupMessageHistoryController@view')
        ->middleware('can:manage,App\Users\Group');

    // SEND SMS

    Route::get('groups/{group}/messaging/sms')
        ->name('admin.groups.messaging.sms')
        ->uses('GroupSendMessageController@sms')
        ->middleware('can:manage,App\Users\Group');

    Route::post('groups/{group}/messaging/sms/send')
        ->name('admin.groups.messaging.sms.send')
        ->uses('GroupSendMessageController@smsSend')
        ->middleware('can:manage,App\Users\Group');

    // SEND VOICE

    Route::get('groups/{group}/messaging/voice')
        ->name('admin.groups.messaging.voice')
        ->uses('GroupSendMessageController@voice')
        ->middleware('can:manage,App\Users\Group');

    Route::post('groups/{group}/messaging/voice/send')
        ->name('admin.groups.messaging.voice.send')
        ->uses('GroupSendMessageController@voiceSend')
        ->middleware('can:manage,App\Users\Group');

});
