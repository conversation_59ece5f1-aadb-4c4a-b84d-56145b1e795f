<?php

Route::group(['namespace' => 'Finance\Controllers'], function () {
    Route::get('finance')
        ->name('admin.finance.index')
        ->uses('FinanceController@index')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');

    // BUCKETS

    Route::get('finance/buckets')
        ->name('admin.finance.buckets.index')
        ->uses('BucketController@index')
        ->middleware('can:indexBuckets,App\Finance\Transaction');

    Route::get('finance/buckets/create')
        ->name('admin.finance.buckets.create')
        ->uses('BucketController@create')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::get('finance/buckets/{bucket}')
        ->name('admin.finance.buckets.view')
        ->uses('BucketController@view')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::get('finance/buckets/{bucket}/edit')
        ->name('admin.finance.buckets.edit')
        ->uses('<PERSON>etController@edit')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::post('finance/buckets/{bucket}/edit')
        ->name('admin.finance.buckets.edit.save')
        ->uses('BucketController@editSave')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::get('finance/buckets/{bucket}/budgets')
        ->name('admin.finance.buckets.budgets')
        ->uses('BucketController@budgets')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::get('finance/buckets/{bucket}/budgets/create')
        ->name('admin.finance.buckets.budgets.createModal')
        ->uses('BucketController@createBudgetModal')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::post('finance/buckets/{bucket}/budgets/create')
        ->name('admin.finance.buckets.budgets.create')
        ->uses('BucketController@createBudget')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::get('finance/buckets/{bucket}/budgets/{budget}/edit')
        ->name('admin.finance.buckets.budgets.editModal')
        ->uses('BucketController@editBudgetModal')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::post('finance/buckets/{bucket}/budgets/{budget}/edit')
        ->name('admin.finance.buckets.budgets.edit')
        ->uses('BucketController@editBudget')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    Route::delete('finance/buckets/{bucket}/budgets/{budget}/delete')
        ->name('admin.finance.buckets.budgets.delete')
        ->uses('BucketController@deleteBudget')
        ->middleware('can:manageBuckets,App\Finance\Transaction');

    // CONTRIBUTION REPORTS

    Route::get('finance/contributions/yearly-reports')
        ->name('admin.finance.contributions.reports.yearly.index')
        ->uses('ContributionReportController@index')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');

    Route::get('finance/contributions/yearly-reports/preview')
        ->name('admin.finance.contributions.reports.yearly.preview')
        ->uses('ContributionReportController@preview')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');

    Route::get('finance/contributions/yearly-reports/preview/{user_id}')
        ->name('admin.finance.contributions.reports.yearly.preview.user')
        ->uses('ContributionReportController@previewUser')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');

    Route::get('finance/contributions/yearly-reports/create')
        ->name('admin.finance.contributions.reports.yearly.create')
        ->uses('ContributionReportController@create')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');
    Route::post('finance/contributions/yearly-reports/create')
        ->name('admin.finance.contributions.reports.yearly.create.submit')
        ->uses('ContributionReportController@createSubmit')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');

    Route::get('finance/contributions/yearly-reports/history')
        ->name('admin.finance.contributions.reports.yearly.history')
        ->uses('ContributionReportController@history')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');

    Route::get('finance/contributions/yearly-reports/history/{year}')
        ->name('admin.finance.contributions.reports.yearly.history.view')
        ->uses('ContributionReportController@historyView')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');

    Route::get('finance/contributions/yearly-reports/history/{year}/{contribution_report_uuid}/download')
        ->name('admin.finance.contributions.reports.yearly.history.download')
        ->uses('ContributionReportController@downloadUserContributionReport')
        ->middleware('can:manageFinancialManagement,App\Finance\Transaction');
});
