<?php

Route::group(['namespace' => 'Sermons\Controllers'], function () {

    // Sermon index
    Route::get('sermons')
        ->name('admin.sermons.index')
        ->uses('SermonController@index')
        ->middleware('can:index,App\Sermons\Sermon');

    // Sermon search
    Route::get('sermons/search')
        ->name('admin.sermons.search')
        ->uses('SermonController@search')
        ->middleware('can:search,App\Sermons\Sermon');

    // Sermon create
    Route::get('sermons/create')
        ->name('admin.sermons.create')
        ->uses('SermonController@create')
        ->middleware('can:create,App\Sermons\Sermon');

    // Sermon store
    Route::post('sermons/create')
        ->name('admin.sermons.store')
        ->uses('SermonController@store')
        ->middleware('can:create,App\Sermons\Sermon');

    // Sermon edit
    Route::get('sermons/{sermon}/edit')
        ->name('admin.sermons.edit')
        ->uses('<PERSON><PERSON><PERSON><PERSON>roll<PERSON>@edit')
        ->middleware('can:edit,sermon');

    // Sermon save
    Route::put('sermons/{sermon}/edit')
        ->name('admin.sermons.save')
        ->uses('SermonController@save')
        ->middleware('can:edit,sermon');

    // Sermon delete
    Route::delete('sermons/{sermon}/delete')
        ->name('admin.sermons.delete')
        ->uses('SermonController@delete')
        ->middleware('can:edit,sermon');

    // TAG index
    Route::get('sermons/tags')
        ->name('admin.sermons.tags.index')
        ->uses('SermonTagController@index')
        ->middleware('can:index,App\Sermons\Sermon');

    // TAG create
    Route::get('sermons/tags/create')
        ->name('admin.sermons.tags.create')
        ->uses('SermonTagController@create')
        ->middleware('can:create,App\Sermons\Sermon');

    // TAG store
    Route::post('sermons/tags/create')
        ->name('admin.sermons.tags.store')
        ->uses('SermonTagController@store')
        ->middleware('can:create,App\Sermons\Sermon');

    // TAG edit
    Route::get('sermons/tags/{tag}/edit')
        ->name('admin.sermons.tags.edit')
        ->uses('SermonTagController@edit')
        ->middleware('can:create,App\Sermons\Sermon');

    // TAG save
    Route::put('sermons/tags/{tag}/edit')
        ->name('admin.sermons.tags.save')
        ->uses('SermonTagController@save')
        ->middleware('can:create,App\Sermons\Sermon');

    // TAG delete
    Route::delete('sermons/tags/{tag}/delete')
        ->name('admin.sermons.tags.delete')
        ->uses('SermonTagController@delete')
        ->middleware('can:create,App\Sermons\Sermon');

//
//    // Sermon Search
//    Route::get('sermons/search')
//        ->name('admin.bible-classes.registration.edit')
//        ->uses('BibleClassRegistrationController@view')
//        ->middleware('can:update,class');
//
//    // Sermon Registration Save
//    Route::put('bible-class/group/{group}/class/{class}/registration')
//        ->name('admin.bible-classes.registration.save')
//        ->uses('BibleClassRegistrationController@save')
//        ->middleware('can:update,class');

    // -------------- GROUPS --------------

    // Sermon Group create
//    Route::get('bible-class/group/create')
//        ->name('admin.bible-classes.groups.create')
//        ->uses('BibleClassGroupController@create')
//        ->middleware('can:create,App\BibleClasses\BibleClassGroup');
//
//    // Sermon Group view
//    Route::get('bible-class/group/{group}')
//        ->name('admin.bible-classes.groups.view')
//        ->uses('BibleClassGroupController@view')
//        ->middleware('can:view,group');
//
//    // Sermon Group store
//    Route::post('bible-class/group/create')
//        ->name('admin.bible-classes.groups.store')
//        ->uses('BibleClassGroupController@store')
//        ->middleware('can:store,App\BibleClasses\BibleClassGroup');
//
//    // Sermon Group edit
//    Route::get('bible-class/group/{group}/edit')
//        ->name('admin.bible-classes.groups.edit')
//        ->uses('BibleClassGroupController@edit')
//        ->middleware('can:edit,group');
//
//    // Sermon Group save
//    Route::post('bible-class/group/{group}/edit')
//        ->name('admin.bible-classes.groups.save')
//        ->uses('BibleClassGroupController@save')
//        ->middleware('can:update,group');

});
