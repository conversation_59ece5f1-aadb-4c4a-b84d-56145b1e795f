<?php

Route::group(['namespace' => 'Users\Controllers'], function () {

    Route::get('roles')
        ->name('admin.roles.index')
        ->uses('RoleController@index')
        ->middleware('can:retrieve,App\Users\Role');

    Route::get('roles/create')
        ->name('admin.roles.create')
        ->uses('RoleController@create')
        ->middleware('can:create,App\Users\Role');

    Route::post('roles/create')
        ->name('admin.roles.store')
        ->uses('RoleController@store')
        ->middleware('can:create,App\Users\Role');

    Route::get('roles/{role}')
        ->name('admin.roles.view')
        ->uses('Role<PERSON>ontroller@view')
        ->middleware('can:retrieve,role');

    Route::get('roles/{role}/edit')
        ->name('admin.roles.edit')
        ->uses('RoleController@edit')
        ->middleware('can:edit,role');

    Route::post('roles/{role}')
        ->name('admin.roles.save')
        ->uses('Role<PERSON>ontroller@save')
        ->middleware('can:save,role');

    Route::get('roles/{role}/permissions')
        ->name('admin.roles.permissions')
        ->uses('RoleController@permissions')
        ->middleware('can:edit,role');

    Route::post('roles/{role}/permissions')
        ->name('admin.roles.permissions.save')
        ->uses('RoleController@savePermissions')
        ->middleware('can:edit,role');

    Route::get('roles/{role}/users')
        ->name('admin.roles.users')
        ->uses('RoleController@users')
        ->middleware('can:edit,role');

    Route::delete('roles/{role}/destroy')
        ->name('admin.roles.destroy')
        ->uses('RoleController@destroy')
        ->middleware('can:delete,role');

    Route::post('roles/{role}/assign')
        ->name('admin.roles.assign')
        ->uses('AssignRoleController@store')
        ->middleware('can:assign,role');

    Route::post('roles/{role}/unassign/{user}')
        ->name('admin.roles.unassign')
        ->uses('AssignRoleController@delete')
        ->middleware('can:unassign,role');
});
