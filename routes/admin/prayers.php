<?php

Route::group(['namespace' => 'Prayers\Controllers'], function () {
    // Prayer Settings
    Route::get('prayers/settings')
        ->name('admin.prayers.settings')
        ->uses('PrayerController@settings')
        ->middleware('can:index,App\Prayers\Prayer');

    // Prayer Settings Save
    Route::put('prayers/settings')
        ->name('admin.prayers.settings.save')
        ->uses('PrayerController@saveSettings')
        ->middleware('can:index,App\Prayers\Prayer');

    // Prayer Notifications - Send Now
    Route::post('prayers/send')
        ->name('admin.prayers.notifications.send')
        ->uses('PrayerController@sendNotifications')
        ->middleware('can:index,App\Prayers\Prayer');

    // --------------------------------------------------------------------

    // Prayers index
    Route::get('prayers')
        ->name('admin.prayers.index')
        ->uses('PrayerController@index')
        ->middleware('can:index,App\Prayers\Prayer');

    // Prayer create
    Route::get('prayers/create')
        ->name('admin.prayers.create')
        ->uses('PrayerController@create')
        ->middleware('can:create,App\Prayers\Prayer');

    // Prayer store
    Route::post('prayers/create')
        ->name('admin.prayers.store')
        ->uses('PrayerController@store')
        ->middleware('can:create,App\Prayers\Prayer');

    // Prayer show
    Route::get('prayers/{prayer}')
        ->name('admin.prayers.view')
        ->uses('PrayerController@view')
        ->middleware('can:edit,prayer');

    // Prayer edit
    Route::get('prayers/{prayer}/edit')
        ->name('admin.prayers.edit')
        ->uses('PrayerController@edit')
        ->middleware('can:edit,prayer');

    // Prayer save
    Route::put('prayers/{prayer}/edit')
        ->name('admin.prayers.save')
        ->uses('PrayerController@save')
        ->middleware('can:edit,prayer');

    // Prayer delete
    Route::get('prayers/{prayer}/delete')
        ->name('admin.prayers.delete')
        ->uses('PrayerController@delete')
        ->middleware('can:edit,prayer');

    // Prayer delete
    Route::delete('prayers/{prayer}/destroy')
        ->name('admin.prayers.destroy')
        ->uses('PrayerController@destroy')
        ->middleware('can:edit,prayer');

    Route::post('prayers/{prayer}/approve')
        ->name('admin.prayers.approve')
        ->uses('PrayerController@approve')
        ->middleware('can:edit,prayer');
    Route::post('prayers/{prayer}/decline')
        ->name('admin.prayers.decline')
        ->uses('PrayerController@decline')
        ->middleware('can:edit,prayer');

    // -----------------------------------------------------------------------

    // PrayerUpdate create
    Route::get('prayers/{prayer}/update/create')
        ->name('admin.prayers.updates.create')
        ->uses('PrayerUpdateController@create')
        ->middleware('can:edit,prayer');

    // PrayerUpdate store
    Route::post('prayers/{prayer}/update/create')
        ->name('admin.prayers.updates.store')
        ->uses('PrayerUpdateController@store')
        ->middleware('can:edit,prayer');

    // PrayerUpdate save
    Route::put('prayers/{prayer}/update/{prayerUpdate}/edit')
        ->name('admin.prayers.updates.save')
        ->uses('PrayerUpdateController@save')
        ->middleware('can:edit,prayer');

    // PrayerUpdate destroy
    Route::delete('prayers/{prayer}/update/{prayerUpdate}/destroy')
        ->name('admin.prayers.updates.destroy')
        ->uses('PrayerUpdateController@destroy')
        ->middleware('can:edit,prayer');

    // ------------------------------------------------------------------------

    // Prayer Folder create
    Route::get('prayers/folders/create')
        ->name('admin.prayers.folders.create')
        ->uses('PrayerFolderController@create')
        ->middleware('can:create,App\Prayers\Prayer');

    // Prayer Folder store
    Route::post('prayers/folders/create')
        ->name('admin.prayers.folders.store')
        ->uses('PrayerFolderController@store')
        ->middleware('can:create,App\Prayers\Prayer');

    // Prayer Folder edit
    Route::get('prayers/folders/{folder}/edit')
        ->name('admin.prayers.folders.edit')
        ->uses('PrayerFolderController@edit')
        ->middleware('can:create,App\Prayers\Prayer');

    // Prayer Folder save
    Route::put('prayers/folders/{folder}/edit')
        ->name('admin.prayers.folders.save')
        ->uses('PrayerFolderController@save')
        ->middleware('can:create,App\Prayers\Prayer');

    // Prayer Folder delete
    Route::delete('prayers/folders/{folder}/destroy')
        ->name('admin.prayers.folders.destroy')
        ->uses('PrayerFolderController@destroy')
        ->middleware('can:create,App\Prayers\Prayer');

    // Prayer Folders index
    Route::get('prayers/folders/{folder?}')
        ->name('admin.prayers.folders.index')
        ->uses('PrayerController@index')
        ->middleware('can:index,App\Prayers\Prayer');
});
