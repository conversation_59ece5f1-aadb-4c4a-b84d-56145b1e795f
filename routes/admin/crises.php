<?php

Route::group(['namespace' => 'Crises\Controllers'], function () {

    // index
    Route::get('crises')
        ->name('admin.crises.index')
        ->uses('CrisesController@index')
        ->middleware('can:index,App\Crises\Crisis');

    // create
    Route::get('crises/create')
        ->name('admin.crises.create')
        ->uses('CrisesController@create')
        ->middleware('can:create,App\Crises\Crisis');

    // store
    Route::post('crises/create')
        ->name('admin.crises.store')
        ->uses('CrisesController@store')
        ->middleware('can:create,App\Crises\Crisis');

    // settings
    Route::get('crises/settings')
        ->name('admin.crises.settings')
        ->uses('CrisesController@settings')
        ->middleware('can:index,App\Crises\Crisis');

    // settings
    Route::put('crises/settings')
        ->name('admin.crises.settings.save')
        ->uses('<PERSON>rises<PERSON>ontroller@saveSettings')
        ->middleware('can:index,App\Crises\Crisis');

    // view
    Route::get('crises/{crisis}')
        ->name('admin.crises.view')
        ->uses('CrisesController@view')
        ->middleware('can:view,crisis');

    // edit
    Route::get('crises/{crisis}/edit')
        ->name('admin.crises.edit')
        ->uses('CrisesController@edit')
        ->middleware('can:edit,crisis');

    // save
    Route::put('crises/{crisis}/edit')
        ->name('admin.crises.save')
        ->uses('CrisesController@save')
        ->middleware('can:edit,crisis');

});
