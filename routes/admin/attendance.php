<?php

Route::group(['namespace' => 'Attendance\Controllers'], function () {
    Route::get('attendance')
        ->name('admin.attendance.index')
        ->uses('AttendanceController@index')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::get('attendance/types')
        ->name('admin.attendance.types.index')
        ->uses('AttendanceTypeController@index')
        ->middleware('can:manageTypes,App\Attendance\Attendance');

    Route::get('attendance/types/create')
        ->name('admin.attendance.types.create')
        ->uses('AttendanceTypeController@create')
        ->middleware('can:manageTypes,App\Attendance\Attendance');
    Route::post('attendance/types/create')
        ->name('admin.attendance.types.saveCreate')
        ->uses('AttendanceTypeController@saveCreate')
        ->middleware('can:manageTypes,App\Attendance\Attendance');

    Route::get('attendance/types/{type}/edit')
        ->name('admin.attendance.types.edit')
        ->uses('AttendanceTypeController@edit')
        ->middleware('can:manageTypes,App\Attendance\Attendance');
    Route::post('attendance/types/{type}/edit')
        ->name('admin.attendance.types.store')
        ->uses('AttendanceTypeController@saveEdit')
        ->middleware('can:manageTypes,App\Attendance\Attendance');

    Route::delete('attendance/types/{type}/delete')
        ->name('admin.attendance.types.destroy')
        ->uses('AttendanceTypeController@destroy')
        ->middleware('can:manageTypes,App\Attendance\Attendance');

    // Record USER Attendance
    Route::get('attendance/user/record')
        ->name('admin.attendance.create')
        ->uses('AttendanceController@create')
        ->middleware('can:create,App\Attendance\Attendance');

    Route::post('attendance/user/record')
        ->name('admin.attendance.store')
        ->uses('AttendanceController@store')
        ->middleware('can:create,App\Attendance\Attendance');

    // Record USER Attendance - LIST
    Route::get('attendance/user/record/list')
        ->name('admin.attendance.create-list')
        ->uses('AttendanceController@createList')
        ->middleware('can:create,App\Attendance\Attendance');

    Route::post('attendance/user/record/list')
        ->name('admin.attendance.store-list')
        ->uses('AttendanceController@storeList')
        ->middleware('can:create,App\Attendance\Attendance');

    Route::delete('attendance/{attendance}/destroy')
        ->name('admin.attendance.destroy')
        ->uses('AttendanceController@destroy')
        ->middleware('can:create,App\Attendance\Attendance');

    Route::put('attendance/toggle')
        ->name('admin.attendance.toggle.ajax')
        ->uses('AttendanceController@toggle')
        ->middleware('can:create,App\Attendance\Attendance');

    // Manage USER Attendance
    Route::get('attendance/user')
        ->name('admin.attendance.user.index')
        ->uses('AttendanceController@userIndex')
        ->middleware('can:index,App\Attendance\Attendance');

    // Manage GENERAL Attendance
    Route::get('attendance/general')
        ->name('admin.attendance.general.index')
        ->uses('AttendanceGeneralController@index')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::get('attendance/manage/{date}')
        ->name('admin.attendance.show')
        ->uses('AttendanceController@show')
        ->middleware('can:index,App\Attendance\Attendance');

    // GENERAL Attendance
    Route::get('attendance/general/create')
        ->name('admin.attendance.general.create')
        ->uses('AttendanceGeneralController@create')
        ->middleware('can:create,App\Attendance\Attendance');

    Route::post('attendance/general/create')
        ->name('admin.attendance.general.store')
        ->uses('AttendanceGeneralController@store')
        ->middleware('can:create,App\Attendance\Attendance');

    Route::delete('attendance/general/{attendance_general_count}/destroy')
        ->name('admin.attendance.general.destroy')
        ->uses('AttendanceGeneralController@destroy')
        ->middleware('can:create,App\Attendance\Attendance');

    // CARDS
    Route::get('attendance/cards')
        ->name('admin.attendance.cards.index')
        ->uses('AttendanceCardController@index')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::get('attendance/cards/visitor-qr-code')
        ->name('admin.attendance.cards.visitors.qrcode')
        ->uses('AttendanceCardController@visitorQRCode')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::get('attendance/cards/{card}')
        ->name('admin.attendance.cards.view')
        ->uses('AttendanceCardController@view')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::post('attendance/cards/{card}/mark-as-unread')
        ->name('admin.attendance.cards.mark-as-unread')
        ->uses('AttendanceCardController@markAsUnread')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::post('attendance/cards/{card}/mark-as-spam')
        ->name('admin.attendance.cards.mark-as-spam')
        ->uses('AttendanceCardController@markAsSpam')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::post('attendance/cards/{card}/unmark-as-spam')
        ->name('admin.attendance.cards.unmark-as-spam')
        ->uses('AttendanceCardController@unmarkAsSpam')
        ->middleware('can:index,App\Attendance\Attendance');

    Route::delete('attendance/cards/{card}/destroy')
        ->name('admin.attendance.cards.destroy')
        ->uses('AttendanceCardController@destroy')
        ->middleware('can:deleteVisitorCard,card');

    Route::get('attendance/general/download')
        ->name('admin.attendance.general.download')
        ->uses('AttendanceGeneralController@downloadGeneralReport')
        ->middleware('can:index,App\Attendance\Attendance');
});
