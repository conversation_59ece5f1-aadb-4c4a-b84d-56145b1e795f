<?php

Route::group(['namespace' => 'AccountFiles\Controllers'], function () {
    // AccountFile index
    Route::get('account-files')
        ->name('admin.account-files.index')
        ->uses('AccountFileController@index')
        ->middleware('can:index,App\AccountFiles\AccountFile');

    // AccountFile search
    Route::get('account-files/search')
        ->name('admin.account-files.search')
        ->uses('AccountFileController@search')
        ->middleware('can:search,App\AccountFiles\AccountFile');

    // AccountFile create
    Route::get('account-files/create')
        ->name('admin.account-files.create')
        ->uses('AccountFileController@create')
        ->middleware('can:create,App\AccountFiles\AccountFile');

    // AccountFile store
    Route::post('account-files/create')
        ->name('admin.account-files.store')
        ->uses('AccountFileController@store')
        ->middleware('can:create,App\AccountFiles\AccountFile');

    // AccountFile edit
    Route::get('account-files/{accountFile}/edit')
        ->name('admin.account-files.edit')
        ->uses('AccountFileController@edit')
        ->middleware('can:edit,accountFile');

    // AccountFile save
    Route::put('account-files/{accountFile}/edit')
        ->name('admin.account-files.save')
        ->uses('AccountFileController@save')
        ->middleware('can:edit,accountFile');

    // AccountFile delete
    Route::delete('account-files/{accountFile}/delete')
        ->name('admin.account-files.delete')
        ->uses('AccountFileController@delete')
        ->middleware('can:delete,accountFile');

    // AccountFile update sort id
    Route::post('account-files/{accountFile}/update-sort-id')
        ->name('admin.account-files.updateSortId')
        ->uses('AccountFileController@updateSortId')
        ->middleware('can:edit,accountFile');

    // AccountFile Folder create
    Route::get('account-files/folders/create')
        ->name('admin.account-files.folders.create')
        ->uses('AccountFileController@createFolder')
        ->middleware('can:create,App\AccountFiles\AccountFile');

    // AccountFile Folder store
    Route::post('account-files/folders/create')
        ->name('admin.account-files.folders.store')
        ->uses('AccountFileController@storeFolder')
        ->middleware('can:create,App\AccountFiles\AccountFile');

    // AccountFile folders
    Route::get('account-files/folders/{accountFile}')
        ->name('admin.account-files.folder')
        ->uses('AccountFileController@folder')
        ->middleware('can:index,App\AccountFiles\AccountFile');

    // AccountFile Folder edit
    Route::get('account-files/folders/{accountFile}/edit')
        ->name('admin.account-files.folders.edit')
        ->uses('AccountFileController@editFolder')
        ->middleware('can:edit,accountFile');

    // AccountFile Folder save
    Route::put('account-files/folders/{accountFile}/edit')
        ->name('admin.account-files.folders.save')
        ->uses('AccountFileController@saveFolder')
        ->middleware('can:edit,accountFile');

    // AccountFile Folder delete
    Route::delete('account-files/folders/{accountFile}/delete')
        ->name('admin.account-files.folders.delete')
        ->uses('AccountFileController@deleteFolder')
        ->middleware('can:delete,accountFile');
});
