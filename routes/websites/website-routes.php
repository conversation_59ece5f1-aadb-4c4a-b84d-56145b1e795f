<?php

Route::group(['namespace' => 'Website'], function () {
    Route::get('')
        ->name('websites.home')
        ->uses('PublicWebsiteController@index');

    Route::get('info')
        ->name('websites.info')
        ->uses('PublicWebsiteController@info');

    Route::get('about')
        ->name('websites.about')
        ->uses('PublicWebsiteController@about');

    Route::get('calendar')
        ->name('websites.calendar')
        ->uses('PublicWebsiteController@calendar');

    Route::get('files')
        ->name('websites.files')
        ->uses('PublicWebsiteController@files');

    Route::get('media')
        ->name('websites.media')
        ->uses('PublicWebsiteController@media');

    Route::get('media/{sermon_title}')
        ->name('websites.media.sermon')
        ->uses('PublicWebsiteController@mediaSermon');

    Route::get('leadership')
        ->name('websites.leadership')
        ->uses('PublicWebsiteController@leadership');

    Route::get('privacy-policy')
        ->name('websites.privacy-policy')
        ->uses('PublicWebsiteController@privacyPolicy');

    Route::get('terms-of-service')
        ->name('websites.terms-of-service')
        ->uses('PublicWebsiteController@termsOfService');

    Route::get('page/{page}')
        ->name('websites.page.view')
        ->uses('PublicWebsiteController@page');

    Route::get('contact')
        ->name('websites.contact')
        ->uses('PublicWebsiteController@contact');

    Route::post('contact')
        ->name('websites.contact.submit')
        ->uses('PublicWebsiteController@contactSubmit')
        ->middleware('throttle:public_form');

    Route::get('prayer-request')
        ->name('websites.prayer-request')
        ->uses('PublicWebsiteController@prayerRequest');

    Route::post('prayer-request')
        ->name('websites.prayer-request.submit')
        ->uses('PublicWebsiteController@prayerRequestSubmit')
        ->middleware('throttle:public_form');

    Route::get('bible-class/registration')
        ->name('websites.bible-class.registration')
        ->uses('PublicWebsiteController@bibleClassRegistration');

    Route::post('bible-class/registration')
        ->name('websites.bible-class.registration.submit')
        ->uses('PublicWebsiteController@bibleClassRegistrationSubmit')
        ->middleware('throttle:public_form');

    Route::get('{unknown}')
        ->name('websites.unknown')
        ->uses('PublicWebsiteRedirectController@handleFinalRoute')
        ->where('unknown', '.*');
});
