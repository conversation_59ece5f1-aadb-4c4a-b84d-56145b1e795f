<?php

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

use App\Broadcasting\Attendance\AttendanceRecordingChannel;
use App\Broadcasting\GroupPostCommentsChannel;

Broadcast::channel('{account_id}.groups.posts.comments', GroupPostCommentsChannel::class);
Broadcast::channel('{account_id}.groups.{user_group_id}.posts.comments', GroupPostCommentsChannel::class);
Broadcast::channel('{account_id}.groups.posts', GroupPostCommentsChannel::class);
Broadcast::channel('{account_id}.groups.{user_group_id}.posts', GroupPostCommentsChannel::class);

Broadcast::channel('{account_id}.user.notifications.{user_id}', GroupPostCommentsChannel::class);

// Attendance
Broadcast::channel('{account_id}.attendance.type.{attendance_type_id}', AttendanceRecordingChannel::class);
