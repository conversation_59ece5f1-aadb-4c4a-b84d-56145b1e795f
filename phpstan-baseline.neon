parameters:
	ignoreErrors:
		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/AccountFiles/Controllers/AccountFileController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withFile\\(\\)\\.$#"
			count: 1
			path: app/AccountFiles/Controllers/AccountFileController.php

		-
			message: "#^Parameter \\#1 \\$content of function response expects array\\|Illuminate\\\\Contracts\\\\View\\\\View\\|string\\|null, int given\\.$#"
			count: 1
			path: app/AccountFiles/Controllers/AccountFileController.php

		-
			message: "#^Access to an undefined property App\\\\Accounts\\\\Payout\\:\\:\\$userPayments\\.$#"
			count: 1
			path: app/Accounts/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/Accounts/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$error\\.$#"
			count: 1
			path: app/Accounts/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$error_description\\.$#"
			count: 1
			path: app/Accounts/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$stripe_user_id\\.$#"
			count: 1
			path: app/Accounts/Controllers/GivingController.php

		-
			message: "#^Relation 'bucket' is not found in App\\\\Users\\\\Payment model\\.$#"
			count: 2
			path: app/Accounts/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property App\\\\Accounts\\\\Grade\\:\\:\\$users\\.$#"
			count: 4
			path: app/Accounts/Controllers/GradeController.php

		-
			message: "#^Relation 'users' is not found in App\\\\Accounts\\\\Grade model\\.$#"
			count: 1
			path: app/Accounts/Controllers/GradeController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 5
			path: app/Accounts/Controllers/SettingController.php

		-
			message: "#^Parameter \\#1 \\$query of method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Sermons\\\\Sermon\\>\\:\\:selectSub\\(\\) expects Closure\\|Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Sermons\\\\Sermon\\>\\|string, Illuminate\\\\Database\\\\Query\\\\Builder given\\.$#"
			count: 1
			path: app/Accounts/Controllers/SettingController.php

		-
			message: "#^Property App\\\\Accounts\\\\AccountSettingValue\\:\\:\\$enable_for_admin \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Accounts/Controllers/SettingController.php

		-
			message: "#^Property App\\\\Accounts\\\\AccountSettingValue\\:\\:\\$enable_for_member \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Accounts/Controllers/SettingController.php

		-
			message: "#^Property App\\\\Accounts\\\\AccountSettingValue\\:\\:\\$value \\(string\\|null\\) does not accept int\\.$#"
			count: 1
			path: app/Accounts/Controllers/SettingController.php

		-
			message: "#^Relation 'emails' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Accounts/Controllers/SettingController.php

		-
			message: "#^Access to an undefined property App\\\\Accounts\\\\Invoice\\:\\:\\$account\\.$#"
			count: 1
			path: app/Accounts/Invoice.php

		-
			message: "#^Access to an undefined property App\\\\Accounts\\\\Invoice\\:\\:\\$items\\.$#"
			count: 1
			path: app/Accounts/Invoice.php

		-
			message: "#^Parameter \\#3 \\$decimal_separator of function number_format expects string\\|null, int given\\.$#"
			count: 1
			path: app/Accounts/Services/AccountInvoice.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Activity\\:\\:\\$user_count\\.$#"
			count: 1
			path: app/Admin/Controllers/AdminDashboardController.php

		-
			message: "#^Using nullsafe method call on non\\-nullable type Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Users\\\\Activity\\>\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Admin/Controllers/AdminDashboardController.php

		-
			message: "#^Relation 'files' is not found in App\\\\Sermons\\\\Sermon model\\.$#"
			count: 1
			path: app/Admin/Services/Dashboard.php

		-
			message: "#^Access to an undefined property App\\\\Announcements\\\\Announcement\\:\\:\\$account\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Access to an undefined property App\\\\Announcements\\\\Announcement\\:\\:\\$allow_individual_to_toggle\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Access to an undefined property App\\\\Announcements\\\\Announcement\\:\\:\\$is_suspended\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Access to an undefined property App\\\\Announcements\\\\Announcement\\:\\:\\$messageTypes\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Access to static property \\$types on an unknown class App\\\\Announcements\\\\Reaction\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Call to an undefined method App\\\\Announcements\\\\Announcement\\:\\:receivers\\(\\)\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Class App\\\\Announcements\\\\Comment not found\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Class App\\\\Announcements\\\\File not found\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Class App\\\\Announcements\\\\Post not found\\.$#"
			count: 1
			path: app/Announcements/Announcement.php

		-
			message: "#^Class App\\\\Announcements\\\\Reaction not found\\.$#"
			count: 6
			path: app/Announcements/Announcement.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Announcements/Controllers/AnnouncementController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withFile\\(\\)\\.$#"
			count: 1
			path: app/Announcements/Controllers/AnnouncementController.php

		-
			message: "#^Parameter \\#1 \\$content of function response expects array\\|Illuminate\\\\Contracts\\\\View\\\\View\\|string\\|null, int given\\.$#"
			count: 1
			path: app/Announcements/Controllers/AnnouncementController.php

		-
			message: "#^Call to static method create\\(\\) on an unknown class App\\\\Announcements\\\\Services\\\\AccountFile\\.$#"
			count: 1
			path: app/Announcements/Services/CreateAnnouncementFile.php

		-
			message: "#^Method App\\\\Announcements\\\\Services\\\\CreateAnnouncementFile\\:\\:withFile\\(\\) has invalid return type App\\\\Announcements\\\\Services\\\\CreateAccountFile\\.$#"
			count: 2
			path: app/Announcements/Services/CreateAnnouncementFile.php

		-
			message: "#^Method App\\\\Announcements\\\\Services\\\\CreateAnnouncementFile\\:\\:withFile\\(\\) should return App\\\\Announcements\\\\Services\\\\CreateAccountFile but returns \\$this\\(App\\\\Announcements\\\\Services\\\\CreateAnnouncementFile\\)\\.$#"
			count: 1
			path: app/Announcements/Services/CreateAnnouncementFile.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$email\\.$#"
			count: 1
			path: app/Api/V1/Controllers/BibleClasses/BibleClassController.php

		-
			message: "#^Call to an undefined method App\\\\Users\\\\User\\:\\:getSetting\\(\\)\\.$#"
			count: 2
			path: app/Api/V1/Controllers/BibleClasses/BibleClassController.php

		-
			message: "#^Parameter \\#1 \\$account of static method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\BibleClasses\\\\BibleClassGroup\\>\\:\\:visibleToAccount\\(\\) expects App\\\\Accounts\\\\Account, App\\\\Users\\\\User\\|null given\\.$#"
			count: 1
			path: app/Api/V1/Controllers/BibleClasses/BibleClassController.php

		-
			message: "#^Relation 'classes' is not found in App\\\\BibleClasses\\\\BibleClassGroup model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/BibleClasses/BibleClassController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Users\\\\User\\>\\:\\:\\$spouse\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Directory/DirectoryController.php

		-
			message: "#^Call to an undefined method App\\\\Users\\\\User\\:\\:getSetting\\(\\)\\.$#"
			count: 2
			path: app/Api/V1/Controllers/Directory/DirectoryController.php

		-
			message: "#^Parameter \\#1 \\$account of static method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Users\\\\User\\>\\:\\:visibleToAccount\\(\\) expects App\\\\Accounts\\\\Account, App\\\\Users\\\\User\\|null given\\.$#"
			count: 2
			path: app/Api/V1/Controllers/Directory/DirectoryController.php

		-
			message: "#^Parameter \\#2 \\$syntax of method Carbon\\\\Carbon\\:\\:diffForHumans\\(\\) expects array\\|int\\|null, false given\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Directory/DirectoryController.php

		-
			message: "#^Relation 'emails' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Directory/DirectoryController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Directory/DirectoryController.php

		-
			message: "#^Relation 'roles' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Directory/DirectoryController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$account\\.$#"
			count: 3
			path: app/Api/V1/Controllers/Email/IncomingMailgunController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$group\\.$#"
			count: 7
			path: app/Api/V1/Controllers/Email/IncomingMailgunController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageProvider\\.$#"
			count: 3
			path: app/Api/V1/Controllers/Email/IncomingMailgunController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageType\\.$#"
			count: 3
			path: app/Api/V1/Controllers/Email/IncomingMailgunController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\GroupSender\\:\\:\\$user\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingMailgunController.php

		-
			message: "#^Relation 'account' is not found in App\\\\Messages\\\\MessageHandler model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingMailgunController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingMailgunController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\GroupSender\\:\\:\\$user\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingPostmarkController.php

		-
			message: "#^Relation 'account' is not found in App\\\\Messages\\\\MessageHandler model\\.$#"
			count: 2
			path: app/Api/V1/Controllers/Email/IncomingPostmarkController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingPostmarkController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$account\\.$#"
			count: 2
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$group\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageProvider\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageType\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\GroupSender\\:\\:\\$user\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^If condition is always false\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Relation 'account' is not found in App\\\\Messages\\\\MessageHandler model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Relation 'user' is not found in App\\\\Users\\\\GroupSender model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/IncomingSparkpostController.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$allow_messages \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Email/PostmarkEventController.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$receives_group_emails \\(bool\\) does not accept int\\.$#"
			count: 2
			path: app/Api/V1/Controllers/Email/PostmarkEventController.php

		-
			message: "#^Call to an undefined method App\\\\Users\\\\User\\:\\:getSetting\\(\\)\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sermons/SermonController.php

		-
			message: "#^Parameter \\#1 \\$account of static method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Sermons\\\\Sermon\\>\\:\\:visibleToAccount\\(\\) expects App\\\\Accounts\\\\Account, App\\\\Users\\\\User\\|null given\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sermons/SermonController.php

		-
			message: "#^Relation 'files' is not found in App\\\\Sermons\\\\Sermon model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sermons/SermonController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$account\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingMessageBirdController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$group\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingMessageBirdController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageProvider\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingMessageBirdController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageType\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingMessageBirdController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\GroupSender\\:\\:\\$user\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingMessageBirdController.php

		-
			message: "#^Relation 'account' is not found in App\\\\Messages\\\\MessageHandler model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingMessageBirdController.php

		-
			message: "#^Relation 'user' is not found in App\\\\Users\\\\GroupSender model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingMessageBirdController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHistory\\:\\:\\$message\\.$#"
			count: 4
			path: app/Api/V1/Controllers/Sms/IncomingPlivoController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 2
			path: app/Api/V1/Controllers/Sms/IncomingPlivoController.php

		-
			message: "#^Method App\\\\Api\\\\V1\\\\Controllers\\\\Sms\\\\IncomingPlivoController\\:\\:getLastMessageSentToUser\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingPlivoController.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingPlivoController.php

		-
			message: "#^Relation 'message' is not found in App\\\\Messages\\\\MessageHistory model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingPlivoController.php

		-
			message: "#^Relation 'phones' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingPlivoController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHistory\\:\\:\\$message\\.$#"
			count: 4
			path: app/Api/V1/Controllers/Sms/IncomingTwilioController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 2
			path: app/Api/V1/Controllers/Sms/IncomingTwilioController.php

		-
			message: "#^Method App\\\\Api\\\\V1\\\\Controllers\\\\Sms\\\\IncomingTwilioController\\:\\:getLastMessageSentToUser\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingTwilioController.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingTwilioController.php

		-
			message: "#^Relation 'message' is not found in App\\\\Messages\\\\MessageHistory model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingTwilioController.php

		-
			message: "#^Relation 'phones' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Sms/IncomingTwilioController.php

		-
			message: "#^Access to an undefined property App\\\\Api\\\\V1\\\\Controllers\\\\Voice\\\\IncomingPlivoVoiceController\\:\\:\\$message\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Voice/IncomingPlivoVoiceController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Voice/IncomingPlivoVoiceController.php

		-
			message: "#^Relation 'user' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Voice/IncomingPlivoVoiceController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Api/V1/Controllers/Voice/IncomingPlivoVoiceController.php

		-
			message: "#^Call to method withoutGlobalScope\\(\\) on an unknown class App\\\\Models\\\\MessageReceiver\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Call to static method withoutGlobalScope\\(\\) on an unknown class App\\\\Models\\\\Message\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Call to static method withoutGlobalScope\\(\\) on an unknown class App\\\\Models\\\\MessageSender\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Caught class CatapultApiException not found\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Dead catch \\- CatapultApiException is never thrown in the try block\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Instantiated class App\\\\Models\\\\MessageReceiver not found\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Instantiated class App\\\\Scopes\\\\AccountScope not found\\.$#"
			count: 3
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Instantiated class Catapult\\\\Client not found\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Instantiated class Catapult\\\\Credentials not found\\.$#"
			count: 1
			path: app/Api/V1/IncomingBandwidthController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Address\\:\\:\\$user\\.$#"
			count: 1
			path: app/App/Controllers/AccountAddressController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 2
			path: app/App/Controllers/AccountAddressController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 5
			path: app/App/Controllers/AccountController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 1
			path: app/App/Controllers/AccountController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 1
			path: app/App/Controllers/AccountEmailController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 2
			path: app/App/Controllers/AccountEmailController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Phone\\:\\:\\$user\\.$#"
			count: 1
			path: app/App/Controllers/AccountPhoneController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 2
			path: app/App/Controllers/AccountPhoneController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 1
			path: app/App/Controllers/CalendarController.php

		-
			message: "#^Access to an undefined property App\\\\ChildCheckins\\\\ChildCheckin\\:\\:\\$child\\.$#"
			count: 1
			path: app/App/Controllers/ChildCheckinController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 5
			path: app/App/Controllers/CrisesController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 4
			path: app/App/Controllers/DirectoryController.php

		-
			message: "#^Relation 'emails' is not found in App\\\\Users\\\\User model\\.$#"
			count: 2
			path: app/App/Controllers/DirectoryController.php

		-
			message: "#^Relation 'familyMembers' is not found in App\\\\Users\\\\User model\\.$#"
			count: 3
			path: app/App/Controllers/DirectoryController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 8
			path: app/App/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$paymentMethods\\.$#"
			count: 1
			path: app/App/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$type\\.$#"
			count: 1
			path: app/App/Controllers/GivingController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/App/Controllers/GivingController.php

		-
			message: "#^Undefined variable\\: \\$charge$#"
			count: 1
			path: app/App/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$groups\\.$#"
			count: 2
			path: app/App/Controllers/GroupController.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 2
			path: app/App/Controllers/GroupController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/App/Controllers/HomeController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Subarea\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Involvement\\\\Subarea\\>\\:\\:\\$area\\.$#"
			count: 1
			path: app/App/Controllers/InvolvementController.php

		-
			message: "#^Relation 'family' is not found in App\\\\Visitors\\\\Visitor model\\.$#"
			count: 1
			path: app/App/Controllers/VisitorController.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 2
			path: app/App/Controllers/WorshipAssignmentController.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:setYear\\(\\) expects int, string given\\.$#"
			count: 1
			path: app/App/Services/Dashboard.php

		-
			message: "#^Relation 'files' is not found in App\\\\Sermons\\\\Sermon model\\.$#"
			count: 1
			path: app/App/Services/Dashboard.php

		-
			message: "#^Access to an undefined property App\\\\Attendance\\\\AttendanceCard\\:\\:\\$date_attendance\\.$#"
			count: 1
			path: app/Attendance/AttendanceCard.php

		-
			message: "#^Access to an undefined property App\\\\Attendance\\\\AttendanceGeneralCount\\:\\:\\$date_attendance\\.$#"
			count: 1
			path: app/Attendance/AttendanceGeneralCount.php

		-
			message: "#^Call to static method createFromFormat\\(\\) on an unknown class App\\\\Attendance\\\\Carbon\\.$#"
			count: 1
			path: app/Attendance/AttendanceGeneralCount.php

		-
			message: "#^Call to static method has\\(\\) on an unknown class App\\\\Attendance\\\\Arr\\.$#"
			count: 1
			path: app/Attendance/AttendanceGeneralCount.php

		-
			message: "#^Call to static method raw\\(\\) on an unknown class App\\\\Attendance\\\\DB\\.$#"
			count: 1
			path: app/Attendance/AttendanceGeneralCount.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 2
			path: app/Attendance/Controllers/AttendanceByDateController.php

		-
			message: "#^Access to static property \\$types on an unknown class App\\\\Users\\\\Attendance\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceByServiceController.php

		-
			message: "#^Call to static method attendanceFromDate\\(\\) on an unknown class App\\\\Users\\\\Attendance\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceByServiceController.php

		-
			message: "#^Call to static method attendanceTrackingWeeks\\(\\) on an unknown class App\\\\Users\\\\Attendance\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceByServiceController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 2
			path: app/Attendance/Controllers/AttendanceByServiceController.php

		-
			message: "#^Access to an undefined property App\\\\Attendance\\\\Attendance\\:\\:\\$user\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceCardController.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceCardController.php

		-
			message: "#^Undefined variable\\: \\$attendanceRecordService$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceCardController.php

		-
			message: "#^Undefined variable\\: \\$attendance_item$#"
			count: 4
			path: app/Attendance/Controllers/AttendanceCardController.php

		-
			message: "#^Variable \\$user might not be defined\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceCardController.php

		-
			message: "#^Access to an undefined property App\\\\Attendance\\\\Attendance\\:\\:\\$user\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceController.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceController.php

		-
			message: "#^Access to an undefined property App\\\\Attendance\\\\AttendanceGeneralCount\\:\\:\\$user\\.$#"
			count: 1
			path: app/Attendance/Controllers/AttendanceGeneralController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 3
			path: app/Auth/Controllers/LoginController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Phone\\:\\:\\$user\\.$#"
			count: 3
			path: app/Auth/Controllers/LoginController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/Auth/Controllers/LoginController.php

		-
			message: "#^Call to an undefined method App\\\\Users\\\\Services\\\\CreateUser\\:\\:setName\\(\\)\\.$#"
			count: 1
			path: app/Auth/Controllers/RegisterController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 1
			path: app/Auth/Controllers/ResetPasswordController.php

		-
			message: "#^Undefined variable\\: \\$php_errormsg$#"
			count: 2
			path: app/Base/Helpers/helpers.php

		-
			message: "#^Variable \\$post_params might not be defined\\.$#"
			count: 1
			path: app/Base/Helpers/helpers.php

		-
			message: "#^Variable \\$post_string in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: app/Base/Helpers/helpers.php

		-
			message: "#^Using nullsafe property access on non\\-nullable type App\\\\Users\\\\User\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Base/Http/Middleware/LogUserActivity.php

		-
			message: "#^Method App\\\\Base\\\\Http\\\\Middleware\\\\MobileApi\\\\Authenticate\\:\\:redirectTo\\(\\) should return string but return statement is missing\\.$#"
			count: 1
			path: app/Base/Http/Middleware/MobileApi/Authenticate.php

		-
			message: "#^Call to an undefined method App\\\\Base\\\\Http\\\\Request\\:\\:rules\\(\\)\\.$#"
			count: 1
			path: app/Base/Http/Request.php

		-
			message: "#^Binary operation \"\\-\" between string and 12 results in an error\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Call to method format\\(\\) on an unknown class App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 5
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Call to method sub\\(\\) on an unknown class App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Call to static method user\\(\\) on an unknown class App\\\\Base\\\\Libraries\\\\Auth\\.$#"
			count: 3
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Comparison operation \"\\>\\=\" between int\\<1, 25\\> and 1 is always true\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Expression \"\\\\App\\\\Base\\\\Libraries\\\\Auth\\:\\:user\\(\\)\\-\\>timezone\" on a separate line does not do anything\\.$#"
			count: 2
			path: app/Base/Libraries/DAT.php

		-
			message: "#^If condition is always true\\.$#"
			count: 2
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Instantiated class App\\\\Base\\\\Libraries\\\\DateInterval not found\\.$#"
			count: 2
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:get_age\\(\\) should return string but returns null\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:gmt_to_user\\(\\) has invalid return type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:gmt_to_user\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns App\\\\Base\\\\Libraries\\\\DAT\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:gmt_to_user\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:is_valid\\(\\) has invalid return type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:is_valid\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns false\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:is_valid\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns true\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:now\\(\\) has invalid return type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:now\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user\\(\\) has invalid return type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns App\\\\Base\\\\Libraries\\\\DAT\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user_now\\(\\) has invalid return type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user_now\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user_to_gmt\\(\\) has invalid return type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user_to_gmt\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns App\\\\Base\\\\Libraries\\\\DAT\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user_to_gmt\\(\\) should return App\\\\Base\\\\Libraries\\\\DateTime but returns DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(string\t\tSpecific timezone\\)\\: Unexpected token \"Specific\", expected variable at offset 86$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$timezone$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$user_datetime$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\#1 \\$datetime of class DateTime constructor expects string, null given\\.$#"
			count: 2
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\#1 \\$interval of method DateTime\\:\\:add\\(\\) expects DateInterval, App\\\\Base\\\\Libraries\\\\DateInterval given\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\#1 \\$targetObject of method DateTime\\:\\:diff\\(\\) expects DateTimeInterface, App\\\\Base\\\\Libraries\\\\DateTime given\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\#1 \\$timestamp of method DateTime\\:\\:setTimestamp\\(\\) expects int, string given\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\$date1 of method App\\\\Base\\\\Libraries\\\\DAT\\:\\:get_diff\\(\\) has invalid type App\\\\Base\\\\Libraries\\\\datetime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\$date2 of method App\\\\Base\\\\Libraries\\\\DAT\\:\\:get_diff\\(\\) has invalid type App\\\\Base\\\\Libraries\\\\datetime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\$datetime of method App\\\\Base\\\\Libraries\\\\DAT\\:\\:gmt\\(\\) has invalid type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\$datetime of method App\\\\Base\\\\Libraries\\\\DAT\\:\\:is_valid\\(\\) has invalid type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\$datetime of method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user\\(\\) has invalid type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Parameter \\$datetime of method App\\\\Base\\\\Libraries\\\\DAT\\:\\:user_to_gmt\\(\\) has invalid type App\\\\Base\\\\Libraries\\\\DateTime\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Variable \\$datetime in empty\\(\\) always exists and is always falsy\\.$#"
			count: 2
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Variable \\$timezone in empty\\(\\) is never defined\\.$#"
			count: 2
			path: app/Base/Libraries/DAT.php

		-
			message: "#^Call to static method image\\(\\) on an unknown class HTML\\.$#"
			count: 2
			path: app/Base/Libraries/Gravatar.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(dest_img
						 location you want new file to be copied to\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 161$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(dest_img
						 location you want new file to be copied to\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 178$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(resized_height 
						 size of height to be resized to\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 248$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(resized_height 
						 size of height to be resized to\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 312$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(resized_width
						 size of width to be resized to\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 186$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(resized_width
						 size of width to be resized to\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 250$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(source_img
						 file location of image to be copied\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 101$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(source_img
						 file location of image to be copied\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 118$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: """
				#^PHPDoc tag @param has invalid value \\(source_img
						 file location of image to be resized\\)\\: Unexpected token "\\\\n\\\\t\\*", expected variable at offset 120$#
			"""
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: "#^Variable \\$source_gd_image might not be defined\\.$#"
			count: 1
			path: app/Base/Libraries/ImageLib.php

		-
			message: "#^Access to an undefined property App\\\\Base\\\\Models\\\\Model\\:\\:\\$log_guarded\\.$#"
			count: 1
			path: app/Base/Models/Model.php

		-
			message: "#^Access to an undefined property App\\\\Base\\\\Models\\\\Model\\:\\:\\$log_modifiers\\.$#"
			count: 1
			path: app/Base/Models/Model.php

		-
			message: "#^Access to an undefined property App\\\\Base\\\\Models\\\\Model\\:\\:\\$log_name\\.$#"
			count: 1
			path: app/Base/Models/Model.php

		-
			message: "#^Call to an undefined method App\\\\Base\\\\Providers\\\\AppServiceProvider\\:\\:getQuery\\(\\)\\.$#"
			count: 2
			path: app/Base/Providers/AppServiceProvider.php

		-
			message: "#^Call to an undefined method App\\\\Base\\\\Providers\\\\AppServiceProvider\\:\\:select\\(\\)\\.$#"
			count: 1
			path: app/Base/Providers/AppServiceProvider.php

		-
			message: "#^Call to an undefined method App\\\\Base\\\\Providers\\\\AppServiceProvider\\:\\:selectSub\\(\\)\\.$#"
			count: 1
			path: app/Base/Providers/AppServiceProvider.php

		-
			message: "#^Access to an undefined property App\\\\Base\\\\Providers\\\\RouteServiceProvider\\:\\:\\$FRONTEND_DOMAIN\\.$#"
			count: 1
			path: app/Base/Providers/RouteServiceProvider.php

		-
			message: "#^Access to an undefined property App\\\\Base\\\\Providers\\\\RouteServiceProvider\\:\\:\\$FRONTEND_PREFIX\\.$#"
			count: 2
			path: app/Base/Providers/RouteServiceProvider.php

		-
			message: "#^Access to an undefined property App\\\\Base\\\\Providers\\\\RouteServiceProvider\\:\\:\\$PODCAST_DOMAIN\\.$#"
			count: 2
			path: app/Base/Providers/RouteServiceProvider.php

		-
			message: "#^Access to an undefined property App\\\\Base\\\\Providers\\\\RouteServiceProvider\\:\\:\\$PODCAST_PREFIX\\.$#"
			count: 2
			path: app/Base/Providers/RouteServiceProvider.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withBibleClassGroups\\(\\)\\.$#"
			count: 1
			path: app/BibleClasses/Controllers/BibleClassController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 2
			path: app/BibleClasses/Controllers/BibleClassController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/BibleClasses/Controllers/BibleClassGroupController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withBibleClassGroups\\(\\)\\.$#"
			count: 1
			path: app/BibleClasses/Controllers/BibleClassGroupController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 2
			path: app/BibleClasses/Controllers/BibleClassGroupController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 1
			path: app/BibleClasses/Controllers/BibleClassRegistrationController.php

		-
			message: "#^Relation 'classes' is not found in App\\\\BibleClasses\\\\BibleClassGroup model\\.$#"
			count: 2
			path: app/BibleClasses/Services/BibleClassMobileApiService.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Calendars/Controllers/CalendarController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Calendars/Controllers/CalendarController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 4
			path: app/Calendars/Controllers/CalendarEventController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$groups\\.$#"
			count: 1
			path: app/Calendars/Scopes/CalendarVisibleToScope.php

		-
			message: "#^Access to an undefined property App\\\\Calendars\\\\Calendar\\:\\:\\$account\\.$#"
			count: 1
			path: app/Calendars/Services/CreateCalendarEvent.php

		-
			message: "#^Access to an undefined property App\\\\Calendars\\\\Event\\:\\:\\$account\\.$#"
			count: 2
			path: app/Calendars/Services/CreateCalendarEventOccurrences.php

		-
			message: "#^Access to an undefined property App\\\\Calendars\\\\Event\\:\\:\\$calendar_type_id\\.$#"
			count: 1
			path: app/Calendars/Services/CreateCalendarEventOccurrences.php

		-
			message: "#^Access to an undefined property App\\\\Calendars\\\\Calendar\\:\\:\\$account\\.$#"
			count: 1
			path: app/Calendars/Services/UpdateCalendarEvent.php

		-
			message: "#^Elseif branch is unreachable because previous condition is always true\\.$#"
			count: 1
			path: app/ChildCheckins/Services/CheckinChild.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/Console/Commands/AndrewsChurch/ImportUsers.php

		-
			message: "#^Access to an undefined property App\\\\Console\\\\Commands\\\\AndrewsChurch\\\\InitGroups\\:\\:\\$account_id\\.$#"
			count: 1
			path: app/Console/Commands/AndrewsChurch/InitGroups.php

		-
			message: "#^Access to an undefined property App\\\\Console\\\\Commands\\\\AndrewsChurch\\\\InitRoles\\:\\:\\$account_id\\.$#"
			count: 1
			path: app/Console/Commands/AndrewsChurch/InitRoles.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 5
			path: app/Console/Commands/AndrewsChurch/InitRoles.php

		-
			message: "#^Access to an undefined property App\\\\Accounts\\\\Invoice\\:\\:\\$account\\.$#"
			count: 12
			path: app/Console/Commands/Finance/CreateAccountInvoices.php

		-
			message: "#^Parameter \\#1 \\$query of method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Sermons\\\\Sermon\\>\\:\\:selectSub\\(\\) expects Closure\\|Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<App\\\\Sermons\\\\Sermon\\>\\|string, Illuminate\\\\Database\\\\Query\\\\Builder given\\.$#"
			count: 1
			path: app/Console/Commands/Finance/CreateAccountInvoices.php

		-
			message: "#^Relation 'message' is not found in App\\\\Messages\\\\MessageHistory model\\.$#"
			count: 6
			path: app/Console/Commands/Finance/CreateAccountInvoices.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$fee\\.$#"
			count: 1
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$id\\.$#"
			count: 3
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$net\\.$#"
			count: 1
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$status\\.$#"
			count: 2
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$type\\.$#"
			count: 1
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Variable \\$current_payout might not be defined\\.$#"
			count: 1
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Variable \\$payments might not be defined\\.$#"
			count: 3
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Variable \\$result might not be defined\\.$#"
			count: 1
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Variable \\$user_payment might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/Finance/ReconcilePayouts.php

		-
			message: "#^Property App\\\\Users\\\\Address\\:\\:\\$is_family \\(bool\\) does not accept int\\.$#"
			count: 2
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$allow_messages \\(bool\\) does not accept int\\.$#"
			count: 2
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$is_primary \\(bool\\) does not accept int\\.$#"
			count: 2
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$receives_group_emails \\(bool\\) does not accept int\\.$#"
			count: 2
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$is_family \\(bool\\) does not accept int\\.$#"
			count: 4
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$is_hidden \\(bool\\) does not accept int\\.$#"
			count: 4
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$is_primary \\(bool\\) does not accept int\\.$#"
			count: 4
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$messages_opt_out \\(bool\\) does not accept int\\.$#"
			count: 4
			path: app/Console/Commands/GraeberChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Address\\:\\:\\$is_family \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$allow_messages \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$is_primary \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Email\\:\\:\\$receives_group_emails \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$is_family \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$is_hidden \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$is_primary \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$messages_opt_out \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Console/Commands/HooverChurch/ImportUsers.php

		-
			message: "#^Variable \\$account might not be defined\\.$#"
			count: 1
			path: app/Console/Commands/ImportPearlandChurch.php

		-
			message: "#^Variable \\$account might not be defined\\.$#"
			count: 1
			path: app/Console/Commands/ImportSouthwestChurch.php

		-
			message: "#^Variable \\$day might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/KarnsChurch/ImportUsers.php

		-
			message: "#^Variable \\$month might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/KarnsChurch/ImportUsers.php

		-
			message: "#^Variable \\$year might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/KarnsChurch/ImportUsers.php

		-
			message: "#^Access to an undefined property App\\\\Sermons\\\\File\\:\\:\\$sermon\\.$#"
			count: 1
			path: app/Console/Commands/KatyChurch/CopySermonFiles.php

		-
			message: "#^Comparison operation \"\\>\" between array\\|bool\\|string\\|null and 0 results in an error\\.$#"
			count: 1
			path: app/Console/Commands/KatyChurch/CopySermonFiles.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Console/Commands/KatyChurch/CopySermonFiles.php

		-
			message: "#^Access to property \\$account_id on an unknown class App\\\\Users\\\\BibleClassTeacher\\.$#"
			count: 2
			path: app/Console/Commands/KatyChurch/ImportBibleClasses.php

		-
			message: "#^Access to property \\$bible_class_id on an unknown class App\\\\Users\\\\BibleClassTeacher\\.$#"
			count: 2
			path: app/Console/Commands/KatyChurch/ImportBibleClasses.php

		-
			message: "#^Access to property \\$user_id on an unknown class App\\\\Users\\\\BibleClassTeacher\\.$#"
			count: 2
			path: app/Console/Commands/KatyChurch/ImportBibleClasses.php

		-
			message: "#^Call to method save\\(\\) on an unknown class App\\\\Users\\\\BibleClassTeacher\\.$#"
			count: 1
			path: app/Console/Commands/KatyChurch/ImportBibleClasses.php

		-
			message: "#^Instantiated class App\\\\Users\\\\BibleClassTeacher not found\\.$#"
			count: 1
			path: app/Console/Commands/KatyChurch/ImportBibleClasses.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 4
			path: app/Console/Commands/KatyChurch/InitRoles.php

		-
			message: "#^Parameter \\#2 \\$month of static method Carbon\\\\Carbon\\:\\:create\\(\\) expects int\\|null, string given\\.$#"
			count: 3
			path: app/Console/Commands/PearlandChurch/ImportUsers.php

		-
			message: "#^Parameter \\#3 \\$day of static method Carbon\\\\Carbon\\:\\:create\\(\\) expects int\\|null, string given\\.$#"
			count: 3
			path: app/Console/Commands/PearlandChurch/ImportUsers.php

		-
			message: "#^Access to an undefined property App\\\\Console\\\\Commands\\\\PearlandChurch\\\\InitGroups\\:\\:\\$account_id\\.$#"
			count: 3
			path: app/Console/Commands/PearlandChurch/InitGroups.php

		-
			message: "#^Access to an undefined property App\\\\Console\\\\Commands\\\\PearlandChurch\\\\InitGroups\\:\\:\\$excel_file_location\\.$#"
			count: 2
			path: app/Console/Commands/PearlandChurch/InitGroups.php

		-
			message: "#^Parameter \\#2 \\$month of static method Carbon\\\\Carbon\\:\\:create\\(\\) expects int\\|null, string given\\.$#"
			count: 4
			path: app/Console/Commands/SWChurch/ImportUsers.php

		-
			message: "#^Parameter \\#3 \\$day of static method Carbon\\\\Carbon\\:\\:create\\(\\) expects int\\|null, string given\\.$#"
			count: 4
			path: app/Console/Commands/SWChurch/ImportUsers.php

		-
			message: "#^Access to an undefined property App\\\\Console\\\\Commands\\\\SWChurch\\\\InitGroups\\:\\:\\$account_id\\.$#"
			count: 1
			path: app/Console/Commands/SWChurch/InitGroups.php

		-
			message: "#^Access to an undefined property App\\\\Console\\\\Commands\\\\SWChurch\\\\InitRoles\\:\\:\\$account_id\\.$#"
			count: 1
			path: app/Console/Commands/SWChurch/InitRoles.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 5
			path: app/Console/Commands/SWChurch/InitRoles.php

		-
			message: "#^Method App\\\\Console\\\\Commands\\\\UniversityChurch\\\\ImportUsers\\:\\:getDateArray\\(\\) is unused\\.$#"
			count: 1
			path: app/Console/Commands/UniversityChurch/ImportUsers.php

		-
			message: "#^Variable \\$day might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/UniversityChurch/ImportUsers.php

		-
			message: "#^Variable \\$month might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/UniversityChurch/ImportUsers.php

		-
			message: "#^Variable \\$year might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/UniversityChurch/ImportUsers.php

		-
			message: "#^Variable \\$day might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/WOChurch/ImportUsers.php

		-
			message: "#^Variable \\$month might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/WOChurch/ImportUsers.php

		-
			message: "#^Variable \\$year might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/WOChurch/ImportUsers.php

		-
			message: "#^Variable \\$day might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/WestsideChurch/ImportUsers.php

		-
			message: "#^Variable \\$month might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/WestsideChurch/ImportUsers.php

		-
			message: "#^Variable \\$year might not be defined\\.$#"
			count: 2
			path: app/Console/Commands/WestsideChurch/ImportUsers.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/Crises/Controllers/CheckinController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/Crises/Controllers/CrisesController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Crises/Services/CreateCrisis.php

		-
			message: "#^Access to an undefined property App\\\\Events\\\\Groups\\\\GroupRead\\:\\:\\$post\\.$#"
			count: 4
			path: app/Events/Groups/GroupRead.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Comment\\:\\:\\$account\\.$#"
			count: 2
			path: app/Groups/Comment.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Comment\\:\\:\\$allow_individual_to_toggle\\.$#"
			count: 1
			path: app/Groups/Comment.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Comment\\:\\:\\$is_suspended\\.$#"
			count: 1
			path: app/Groups/Comment.php

		-
			message: "#^Call to an undefined method App\\\\Groups\\\\Comment\\:\\:receivers\\(\\)\\.$#"
			count: 1
			path: app/Groups/Comment.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Comment\\:\\:\\$group\\.$#"
			count: 2
			path: app/Groups/Policies/CommentPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$group\\.$#"
			count: 5
			path: app/Groups/Policies/PostPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$account\\.$#"
			count: 1
			path: app/Groups/Post.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$allow_individual_to_toggle\\.$#"
			count: 1
			path: app/Groups/Post.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$is_suspended\\.$#"
			count: 1
			path: app/Groups/Post.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$messageTypes\\.$#"
			count: 1
			path: app/Groups/Post.php

		-
			message: "#^Call to an undefined method App\\\\Groups\\\\Post\\:\\:receivers\\(\\)\\.$#"
			count: 1
			path: app/Groups/Post.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$group\\.$#"
			count: 1
			path: app/Groups/Services/CreateComment.php

		-
			message: "#^Relation 'account' is not found in App\\\\Messages\\\\MessageHandler model\\.$#"
			count: 1
			path: app/Groups/Services/SendGroupSMS.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Area\\:\\:\\$subareas\\.$#"
			count: 4
			path: app/Involvement/Area.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Category\\:\\:\\$areas\\.$#"
			count: 4
			path: app/Involvement/Category.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/Involvement/Controllers/CategoryController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withCategory\\(\\)\\.$#"
			count: 1
			path: app/Involvement/Controllers/CategoryController.php

		-
			message: "#^Parameter \\#1 \\$content of function response expects array\\|Illuminate\\\\Contracts\\\\View\\\\View\\|string\\|null, int given\\.$#"
			count: 2
			path: app/Involvement/Controllers/InvolvementController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withCategory\\(\\)\\.$#"
			count: 2
			path: app/Involvement/Controllers/SubareaController.php

		-
			message: "#^Access to an undefined property App\\\\Crises\\\\Checkin\\:\\:\\$crisis\\.$#"
			count: 1
			path: app/Jobs/Crises/ProcessHelpRequestCheckin.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:addSeconds\\(\\) expects int, float given\\.$#"
			count: 1
			path: app/Jobs/Crises/ProcessHelpRequestCheckin.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Jobs/Crises/ProcessHelpRequestCheckin.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:addSeconds\\(\\) expects int, float given\\.$#"
			count: 1
			path: app/Jobs/Crises/ProcessNewCrisisNotifications.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Jobs/Crises/ProcessNewCrisisNotifications.php

		-
			message: "#^Constant id not found\\.$#"
			count: 1
			path: app/Jobs/Podcasts/SyncPodcastSermons.php

		-
			message: "#^Relation 'tags' is not found in App\\\\Sermons\\\\Sermon model\\.$#"
			count: 1
			path: app/Jobs/Podcasts/SyncPodcastSermons.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/Jobs/ProcessMessage.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:addSeconds\\(\\) expects int, float given\\.$#"
			count: 1
			path: app/Jobs/ProcessMessage.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/Jobs/ProcessSms.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/Jobs/ProcessVoice.php

		-
			message: "#^Parameter \\#1 \\$data of static method Kreait\\\\Firebase\\\\Messaging\\\\CloudMessage\\:\\:fromArray\\(\\) expects array\\{token\\?\\: non\\-empty\\-string, topic\\?\\: non\\-empty\\-string, condition\\?\\: non\\-empty\\-string, data\\?\\: array\\<string, string\\>\\|Kreait\\\\Firebase\\\\Messaging\\\\MessageData, notification\\?\\: array\\{title\\?\\: string, body\\?\\: string, image\\?\\: string\\}\\|Kreait\\\\Firebase\\\\Messaging\\\\Notification, android\\?\\: array\\{collapse_key\\?\\: non\\-empty\\-string, priority\\?\\: 'high'\\|'normal', ttl\\?\\: int\\<1, max\\>\\|non\\-empty\\-string\\|null, restricted_package_name\\?\\: non\\-empty\\-string, data\\?\\: array\\<non\\-empty\\-string, non\\-empty\\-string\\>, notification\\?\\: array\\{title\\?\\: non\\-empty\\-string, body\\?\\: non\\-empty\\-string, icon\\?\\: non\\-empty\\-string, color\\?\\: non\\-empty\\-string, sound\\?\\: non\\-empty\\-string, click_action\\?\\: non\\-empty\\-string, body_loc_key\\?\\: non\\-empty\\-string, body_loc_args\\?\\: array\\<int, non\\-empty\\-string\\>, \\.\\.\\.\\}, fcm_options\\?\\: array\\{analytics_label\\?\\: non\\-empty\\-string\\}, direct_boot_ok\\?\\: bool\\}, apns\\?\\: array\\{headers\\?\\: array\\<non\\-empty\\-string, non\\-empty\\-string\\>, payload\\?\\: array\\<non\\-empty\\-string, mixed\\>, fcm_options\\?\\: array\\{analytics_label\\?\\: string, image\\?\\: string\\}\\}\\|Kreait\\\\Firebase\\\\Messaging\\\\ApnsConfig, webpush\\?\\: array\\{headers\\?\\: array\\{TTL\\?\\: int\\<1, max\\>\\|numeric\\-string\\|null, Urgency\\?\\: 'high'\\|'low'\\|'normal'\\|'very\\-low'\\}, data\\?\\: array\\<non\\-empty\\-string, non\\-empty\\-string\\>, notification\\?\\: array\\{title\\: non\\-empty\\-string, options\\?\\: array\\{dir\\?\\: 'auto'\\|'ltr'\\|'rtl', lang\\?\\: string, badge\\?\\: non\\-empty\\-string, body\\?\\: non\\-empty\\-string, tag\\?\\: non\\-empty\\-string, icon\\?\\: non\\-empty\\-string, image\\?\\: non\\-empty\\-string, data\\?\\: mixed, \\.\\.\\.\\}\\}, fcm_options\\?\\: array\\{link\\?\\: non\\-empty\\-string, analytics_label\\?\\: non\\-empty\\-string\\}\\}\\|Kreait\\\\Firebase\\\\Messaging\\\\WebPushConfig, \\.\\.\\.\\}, array\\{token\\: mixed, notification\\: array\\{title\\: 'Lightpost Message', body\\: mixed\\}, data\\: array\\{key\\: 'value'\\}, android\\: array\\{priority\\: 'high', notification\\: array\\{default_vibrate_timings\\: true, default_sound\\: true, notification_count\\: 0, notification_priority\\: 'PRIORITY_HIGH'\\}\\}, apns\\: array\\{headers\\: array\\{apns\\-priority\\: '10'\\}, payload\\: array\\{aps\\: array\\{alert\\: array\\{title\\: 'Lightpost Message', body\\: mixed\\}, sound\\: 'default'\\}\\}\\}\\} given\\.$#"
			count: 1
			path: app/Jobs/SendMobileNotification.php

		-
			message: "#^Parameter \\#1 \\$data of static method Kreait\\\\Firebase\\\\Messaging\\\\CloudMessage\\:\\:fromArray\\(\\) expects array\\{token\\?\\: non\\-empty\\-string, topic\\?\\: non\\-empty\\-string, condition\\?\\: non\\-empty\\-string, data\\?\\: array\\<string, string\\>\\|Kreait\\\\Firebase\\\\Messaging\\\\MessageData, notification\\?\\: array\\{title\\?\\: string, body\\?\\: string, image\\?\\: string\\}\\|Kreait\\\\Firebase\\\\Messaging\\\\Notification, android\\?\\: array\\{collapse_key\\?\\: non\\-empty\\-string, priority\\?\\: 'high'\\|'normal', ttl\\?\\: int\\<1, max\\>\\|non\\-empty\\-string\\|null, restricted_package_name\\?\\: non\\-empty\\-string, data\\?\\: array\\<non\\-empty\\-string, non\\-empty\\-string\\>, notification\\?\\: array\\{title\\?\\: non\\-empty\\-string, body\\?\\: non\\-empty\\-string, icon\\?\\: non\\-empty\\-string, color\\?\\: non\\-empty\\-string, sound\\?\\: non\\-empty\\-string, click_action\\?\\: non\\-empty\\-string, body_loc_key\\?\\: non\\-empty\\-string, body_loc_args\\?\\: array\\<int, non\\-empty\\-string\\>, \\.\\.\\.\\}, fcm_options\\?\\: array\\{analytics_label\\?\\: non\\-empty\\-string\\}, direct_boot_ok\\?\\: bool\\}, apns\\?\\: array\\{headers\\?\\: array\\<non\\-empty\\-string, non\\-empty\\-string\\>, payload\\?\\: array\\<non\\-empty\\-string, mixed\\>, fcm_options\\?\\: array\\{analytics_label\\?\\: string, image\\?\\: string\\}\\}\\|Kreait\\\\Firebase\\\\Messaging\\\\ApnsConfig, webpush\\?\\: array\\{headers\\?\\: array\\{TTL\\?\\: int\\<1, max\\>\\|numeric\\-string\\|null, Urgency\\?\\: 'high'\\|'low'\\|'normal'\\|'very\\-low'\\}, data\\?\\: array\\<non\\-empty\\-string, non\\-empty\\-string\\>, notification\\?\\: array\\{title\\: non\\-empty\\-string, options\\?\\: array\\{dir\\?\\: 'auto'\\|'ltr'\\|'rtl', lang\\?\\: string, badge\\?\\: non\\-empty\\-string, body\\?\\: non\\-empty\\-string, tag\\?\\: non\\-empty\\-string, icon\\?\\: non\\-empty\\-string, image\\?\\: non\\-empty\\-string, data\\?\\: mixed, \\.\\.\\.\\}\\}, fcm_options\\?\\: array\\{link\\?\\: non\\-empty\\-string, analytics_label\\?\\: non\\-empty\\-string\\}\\}\\|Kreait\\\\Firebase\\\\Messaging\\\\WebPushConfig, \\.\\.\\.\\}, array\\{token\\: mixed, notification\\: array\\{title\\: mixed, body\\: mixed\\}, data\\: mixed, android\\: array\\{priority\\: 'high', notification\\: array\\{default_vibrate_timings\\: true, default_sound\\: true, notification_count\\: 0, notification_priority\\: 'PRIORITY_HIGH'\\}\\}, apns\\: array\\{headers\\: array\\{apns\\-priority\\: '10'\\}, payload\\: array\\{aps\\: array\\{alert\\: array\\{title\\: mixed, body\\: mixed\\}, sound\\: 'default', badge\\: 0\\}\\}\\}\\} given\\.$#"
			count: 1
			path: app/Jobs/SendMobileNotification.php

		-
			message: "#^Parameter \\#1 \\$messages of method Kreait\\\\Firebase\\\\Messaging\\:\\:sendAll\\(\\) expects iterable\\<Kreait\\\\Firebase\\\\Messaging\\\\Message\\>&Kreait\\\\Firebase\\\\Messaging\\\\Messages, array\\<int, Kreait\\\\Firebase\\\\Messaging\\\\CloudMessage\\> given\\.$#"
			count: 1
			path: app/Jobs/SendMobileNotification.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Jobs/SendMobileNotification.php

		-
			message: "#^Access to an undefined property Plivo\\\\RestClient\\:\\:\\$messages\\.$#"
			count: 4
			path: app/Jobs/SendSms.php

		-
			message: "#^Relation 'message' is not found in App\\\\Messages\\\\MessageHistory model\\.$#"
			count: 1
			path: app/Jobs/SendSms.php

		-
			message: "#^Access to an undefined property App\\\\Jobs\\\\Visitors\\\\ProcessNewHistoryEvent\\:\\:\\$visitor\\.$#"
			count: 1
			path: app/Jobs/Visitors/ProcessNewHistoryEvent.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:addSeconds\\(\\) expects int, float given\\.$#"
			count: 1
			path: app/Jobs/Visitors/ProcessNewHistoryEvent.php

		-
			message: "#^Parameter \\#3 \\$end of static method Illuminate\\\\Support\\\\Str\\:\\:limit\\(\\) expects string, null given\\.$#"
			count: 2
			path: app/Listeners/Groups/SendCommentCreatedNotification.php

		-
			message: "#^Parameter \\#3 \\$end of static method Illuminate\\\\Support\\\\Str\\:\\:limit\\(\\) expects string, null given\\.$#"
			count: 2
			path: app/Listeners/Groups/SendPostCreatedNotifications.php

		-
			message: "#^Relation 'areas' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 2
			path: app/Livewire/Admin/Involvement/Index.php

		-
			message: "#^Relation 'addresses' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Livewire/Admin/Users/<USER>

		-
			message: "#^Relation 'emails' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Livewire/Admin/Users/<USER>

		-
			message: "#^Relation 'phones' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Livewire/Admin/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Livewire\\\\Admin\\\\Users\\\\UserInvolvement\\:\\:\\$categories\\.$#"
			count: 1
			path: app/Livewire/Admin/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Livewire\\\\Admin\\\\Users\\\\UserInvolvement\\:\\:\\$involvement_records\\.$#"
			count: 1
			path: app/Livewire/Admin/Users/<USER>

		-
			message: "#^Relation 'areas' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 1
			path: app/Livewire/Admin/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$group\\.$#"
			count: 1
			path: app/Livewire/App/Groups/Post.php

		-
			message: "#^Access to an undefined property App\\\\Groups\\\\Post\\:\\:\\$reactions_count\\.$#"
			count: 1
			path: app/Livewire/App/Groups/Post.php

		-
			message: "#^Relation 'comments' is not found in App\\\\Groups\\\\Post model\\.$#"
			count: 1
			path: app/Livewire/App/Groups/Post.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 4
			path: app/Livewire/App/Kiosk/ChildCheckin/NewCheckin.php

		-
			message: "#^Property App\\\\Users\\\\Phone\\:\\:\\$is_primary \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Livewire/App/Kiosk/ChildCheckin/NewCheckin.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/Livewire/App/Visitors/ViewVisitor.php

		-
			message: "#^Call to an undefined method App\\\\Mail\\\\Admin\\\\Groups\\\\ConfirmGroupEmailReceived\\:\\:sentEmailSubject\\(\\)\\.$#"
			count: 1
			path: app/Mail/Admin/Groups/ConfirmGroupEmailReceived.php

		-
			message: "#^Parameter \\#1 \\$view of method Illuminate\\\\Mail\\\\Mailable\\:\\:view\\(\\) expects view\\-string, string given\\.$#"
			count: 1
			path: app/Mail/Admin/Groups/ConfirmGroupEmailReceived.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Mail/App/General.php

		-
			message: "#^Using nullsafe property access on non\\-nullable type App\\\\Users\\\\User\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Mail/App/General.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 1
			path: app/Mail/App/MobileResetPassword.php

		-
			message: "#^Method App\\\\Mail\\\\App\\\\MobileResetPassword\\:\\:build\\(\\) should return \\$this\\(App\\\\Mail\\\\App\\\\MobileResetPassword\\) but empty return statement found\\.$#"
			count: 1
			path: app/Mail/App/MobileResetPassword.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 1
			path: app/Mail/App/ResetPassword.php

		-
			message: "#^Method App\\\\Mail\\\\App\\\\ResetPassword\\:\\:build\\(\\) should return \\$this\\(App\\\\Mail\\\\App\\\\ResetPassword\\) but empty return statement found\\.$#"
			count: 1
			path: app/Mail/App/ResetPassword.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Mail/App/ResetPassword.php

		-
			message: "#^Call to an undefined method App\\\\Messages\\\\MessageReceiver\\:\\:receiver\\(\\)\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withMessage\\(\\)\\.$#"
			count: 5
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withMessages\\(\\)\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Call to method delete\\(\\) on an unknown class App\\\\Messages\\\\Controllers\\\\DeleteMessageService\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Call to method get\\(\\) on an unknown class App\\\\Requests\\\\Messages\\\\CreateMessageRequest\\.$#"
			count: 6
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Call to method get\\(\\) on an unknown class App\\\\Requests\\\\Messages\\\\UpdateMessageRequest\\.$#"
			count: 6
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Call to method setAccount\\(\\) on an unknown class App\\\\Messages\\\\Controllers\\\\UpdateMessageService\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Call to method setAccount\\(\\) on an unknown class App\\\\Services\\\\Messages\\\\CreateMessageService\\.$#"
			count: 2
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Instantiated class App\\\\Messages\\\\Controllers\\\\DeleteMessageService not found\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Instantiated class App\\\\Messages\\\\Controllers\\\\UpdateMessageService not found\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Instantiated class App\\\\Services\\\\Messages\\\\CreateMessageService not found\\.$#"
			count: 2
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Parameter \\#1 \\$content of function response expects array\\|Illuminate\\\\Contracts\\\\View\\\\View\\|string\\|null, int given\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 7
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Parameter \\$request of method App\\\\Messages\\\\Controllers\\\\MessageController\\:\\:destroy\\(\\) has invalid type App\\\\Messages\\\\Controllers\\\\DeleteMessageRequest\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Parameter \\$request of method App\\\\Messages\\\\Controllers\\\\MessageController\\:\\:store\\(\\) has invalid type App\\\\Requests\\\\Messages\\\\CreateMessageRequest\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Parameter \\$request of method App\\\\Messages\\\\Controllers\\\\MessageController\\:\\:update\\(\\) has invalid type App\\\\Requests\\\\Messages\\\\UpdateMessageRequest\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Messages/Controllers/MessageController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageAttachment\\:\\:\\$is_deleted\\.$#"
			count: 1
			path: app/Messages/MessageAttachment.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageProvider\\.$#"
			count: 3
			path: app/Messages/MessageHandler.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$status\\.$#"
			count: 2
			path: app/Messages/MessageHandler.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHistory\\:\\:\\$indicates_error\\.$#"
			count: 1
			path: app/Messages/MessageHistory.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHistory\\:\\:\\$message\\.$#"
			count: 2
			path: app/Messages/MessageHistory.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHistory\\:\\:\\$messageStatus\\.$#"
			count: 2
			path: app/Messages/MessageHistory.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHistory\\:\\:\\$userEmail\\.$#"
			count: 1
			path: app/Messages/MessageHistory.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHistory\\:\\:\\$userPhone\\.$#"
			count: 1
			path: app/Messages/MessageHistory.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageReceiver\\:\\:\\$status\\.$#"
			count: 2
			path: app/Messages/MessageReceiver.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageSender\\:\\:\\$status\\.$#"
			count: 2
			path: app/Messages/MessageSender.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageAttachment\\:\\:\\$account_id\\.$#"
			count: 2
			path: app/Messages/Policies/MessageAttachmentPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\Services\\\\CreateMessage\\:\\:\\$account\\.$#"
			count: 2
			path: app/Messages/Services/CreateMessage.php

		-
			message: "#^Access to an undefined property Plivo\\\\RestClient\\:\\:\\$numbers\\.$#"
			count: 1
			path: app/Messages/Services/CreateMessageHandler.php

		-
			message: "#^Binary operation \"/\" between 'CreateMessageHandle…' and string results in an error\\.$#"
			count: 1
			path: app/Messages/Services/CreateMessageHandler.php

		-
			message: "#^Method App\\\\Messages\\\\Services\\\\CreateMessageHandler\\:\\:purchasePhoneNumber\\(\\) is unused\\.$#"
			count: 1
			path: app/Messages/Services/CreateMessageHandler.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:error\\(\\) expects array\\|Illuminate\\\\Contracts\\\\Support\\\\Arrayable\\|Illuminate\\\\Contracts\\\\Support\\\\Jsonable\\|Illuminate\\\\Support\\\\Stringable\\|string, \\$this\\(App\\\\Messages\\\\Services\\\\CreateMessageHandler\\) given\\.$#"
			count: 1
			path: app/Messages/Services/CreateMessageHandler.php

		-
			message: "#^Parameter \\#1 \\$prefix of function uniqid expects string, true given\\.$#"
			count: 1
			path: app/Messages/Services/CreateMessageHandler.php

		-
			message: "#^Using nullsafe property access on non\\-nullable type Plivo\\\\Resources\\\\PhoneNumber\\\\PhoneNumberListResponse\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Messages/Services/CreateMessageHandler.php

		-
			message: "#^Parameter \\#3 \\$pin of class App\\\\Mail\\\\App\\\\MobileResetPassword constructor expects string\\|null, int\\<100000, 999999\\> given\\.$#"
			count: 1
			path: app/MobileApi/Auth/Controllers/ForgotPasswordController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 2
			path: app/MobileApi/Auth/Controllers/LoginController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Phone\\:\\:\\$user\\.$#"
			count: 2
			path: app/MobileApi/Auth/Controllers/LoginController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/MobileApi/Auth/Controllers/LoginController.php

		-
			message: "#^Relation 'value' is not found in App\\\\Accounts\\\\AccountSetting model\\.$#"
			count: 1
			path: app/MobileApi/Auth/Controllers/LoginController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 2
			path: app/MobileApi/Auth/Controllers/ResetPasswordController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$addresses\\.$#"
			count: 3
			path: app/MobileApi/V1/Controllers/AccountAddressController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/AccountController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$emails\\.$#"
			count: 3
			path: app/MobileApi/V1/Controllers/AccountEmailController.php

		-
			message: "#^Access to an undefined property App\\\\AccountFiles\\\\AccountFile\\:\\:\\$full_url\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/AccountFileController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$phones\\.$#"
			count: 3
			path: app/MobileApi/V1/Controllers/AccountPhoneController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 4
			path: app/MobileApi/V1/Controllers/CalendarController.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:setDay\\(\\) expects int, string given\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/CalendarController.php

		-
			message: "#^Relation 'event' is not found in App\\\\Calendars\\\\EventOccurrence model\\.$#"
			count: 2
			path: app/MobileApi/V1/Controllers/CalendarController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/MobileApi/V1/Controllers/DirectoryController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Users\\\\User\\>\\:\\:\\$spouse\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/DirectoryController.php

		-
			message: "#^Array has 2 duplicate keys with value 'type' \\('type', 'type'\\)\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/DirectoryController.php

		-
			message: "#^Relation 'emails' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/DirectoryController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/DirectoryController.php

		-
			message: "#^Relation 'roles' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/DirectoryController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 8
			path: app/MobileApi/V1/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$type\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/GivingController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Area\\:\\:\\$subareas\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Area\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Involvement\\\\Area\\>\\:\\:\\$category\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Category\\:\\:\\$areas\\.$#"
			count: 2
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Subarea\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Involvement\\\\Subarea\\>\\:\\:\\$area\\.$#"
			count: 2
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Relation 'areas' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 3
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Relation 'users' is not found in App\\\\Involvement\\\\Area model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Relation 'users' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Relation 'users' is not found in App\\\\Involvement\\\\Subarea model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/InvolvementController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/MobileApi/V1/Controllers/ProfileController.php

		-
			message: "#^Relation 'emails' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/ProfileController.php

		-
			message: "#^Relation 'value' is not found in App\\\\Accounts\\\\AccountSetting model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/ProfileController.php

		-
			message: "#^Relation 'files' is not found in App\\\\Sermons\\\\Sermon model\\.$#"
			count: 1
			path: app/MobileApi/V1/Controllers/SermonController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$addresses\\.$#"
			count: 3
			path: app/MobileApi/V2/Controllers/AccountAddressController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/AccountController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$emails\\.$#"
			count: 3
			path: app/MobileApi/V2/Controllers/AccountEmailController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$phones\\.$#"
			count: 3
			path: app/MobileApi/V2/Controllers/AccountPhoneController.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Pick\\:\\:\\$user\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/AttendanceController.php

		-
			message: "#^Relation 'picks' is not found in App\\\\WorshipAssignments\\\\Period model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/AttendanceController.php

		-
			message: "#^Relation 'position' is not found in App\\\\WorshipAssignments\\\\Pick model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/AttendanceController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 12
			path: app/MobileApi/V2/Controllers/CalendarController.php

		-
			message: "#^Parameter \\#1 \\$value of method Carbon\\\\Carbon\\:\\:setDay\\(\\) expects int, string given\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/CalendarController.php

		-
			message: "#^Relation 'event' is not found in App\\\\Calendars\\\\EventOccurrence model\\.$#"
			count: 4
			path: app/MobileApi/V2/Controllers/CalendarController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/MobileApi/V2/Controllers/CrisesController.php

		-
			message: "#^Relation 'checkins' is not found in App\\\\Crises\\\\Crisis model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/CrisesController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 4
			path: app/MobileApi/V2/Controllers/DirectoryController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Users\\\\User\\>\\:\\:\\$avatar\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/DirectoryController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Users\\\\User\\>\\:\\:\\$spouse\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/DirectoryController.php

		-
			message: "#^Array has 2 duplicate keys with value 'type' \\('type', 'type'\\)\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/DirectoryController.php

		-
			message: "#^Relation 'avatar' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/DirectoryController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 8
			path: app/MobileApi/V2/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property Stripe\\\\StripeObject\\:\\:\\$type\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GivingController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GivingController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$groups\\.$#"
			count: 2
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Relation 'creator' is not found in App\\\\Groups\\\\Comment model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Relation 'creator' is not found in App\\\\Groups\\\\Post model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Relation 'messageHandlers' is not found in App\\\\Users\\\\Group model\\.$#"
			count: 3
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Relation 'reactions' is not found in App\\\\Groups\\\\Post model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Relation 'usersWithoutSettings' is not found in App\\\\Users\\\\Group model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/GroupController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Area\\:\\:\\$subareas\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Area\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Involvement\\\\Area\\>\\:\\:\\$category\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Category\\:\\:\\$areas\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Access to an undefined property App\\\\Involvement\\\\Subarea\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Involvement\\\\Subarea\\>\\:\\:\\$area\\.$#"
			count: 2
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Relation 'areas' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 2
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Relation 'users' is not found in App\\\\Involvement\\\\Area model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Relation 'users' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Relation 'users' is not found in App\\\\Involvement\\\\Subarea model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/InvolvementController.php

		-
			message: "#^Relation 'updates' is not found in App\\\\Prayers\\\\Prayer model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/PrayerController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/MobileApi/V2/Controllers/ProfileController.php

		-
			message: "#^Relation 'avatar' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/ProfileController.php

		-
			message: "#^Relation 'value' is not found in App\\\\Accounts\\\\AccountSetting model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/ProfileController.php

		-
			message: "#^Relation 'files' is not found in App\\\\Sermons\\\\Sermon model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/SermonController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 8
			path: app/MobileApi/V2/Controllers/UserController.php

		-
			message: "#^Call to static method error\\(\\) on an unknown class App\\\\MobileApi\\\\V2\\\\Controllers\\\\Log\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/UserController.php

		-
			message: "#^Caught class App\\\\MobileApi\\\\V2\\\\Controllers\\\\Exception not found\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/UserController.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Pick\\:\\:\\$user\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/WorshipAssignmentController.php

		-
			message: "#^Relation 'picks' is not found in App\\\\WorshipAssignments\\\\Period model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/WorshipAssignmentController.php

		-
			message: "#^Relation 'position' is not found in App\\\\WorshipAssignments\\\\Pick model\\.$#"
			count: 1
			path: app/MobileApi/V2/Controllers/WorshipAssignmentController.php

		-
			message: "#^Access to an undefined property App\\\\Podcasts\\\\Track\\:\\:\\$sermon\\.$#"
			count: 1
			path: app/Podcasts/Controllers/PodcastController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Podcasts/Controllers/PodcastController.php

		-
			message: "#^Access to an undefined property App\\\\Podcasts\\\\Track\\:\\:\\$podcast\\.$#"
			count: 1
			path: app/Podcasts/Controllers/PublicController.php

		-
			message: "#^Access to an undefined property ipinfo\\\\ipinfo\\\\Details\\:\\:\\$all\\.$#"
			count: 2
			path: app/Podcasts/Controllers/PublicController.php

		-
			message: "#^Using nullsafe property access on non\\-nullable type ipinfo\\\\ipinfo\\\\Details\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Podcasts/Controllers/PublicController.php

		-
			message: "#^Access to an undefined property App\\\\Podcasts\\\\Services\\\\CreatePodcastXml\\:\\:\\$track\\.$#"
			count: 3
			path: app/Podcasts/Services/CreatePodcastXml.php

		-
			message: "#^Parameter \\#1 \\$prefix of function uniqid expects string, true given\\.$#"
			count: 1
			path: app/Podcasts/Services/CreateTrackFromSermon.php

		-
			message: "#^Parameter \\#1 \\$prefix of function uniqid expects string, true given\\.$#"
			count: 1
			path: app/Podcasts/Services/SyncTrackToSermon.php

		-
			message: "#^Method App\\\\Podcasts\\\\Services\\\\UpdatePodcast\\:\\:__construct\\(\\) with return type void returns \\$this\\(App\\\\Podcasts\\\\Services\\\\UpdatePodcast\\) but should not return anything\\.$#"
			count: 1
			path: app/Podcasts/Services/UpdatePodcast.php

		-
			message: "#^Access to an undefined property App\\\\Podcasts\\\\Track\\:\\:\\$podcast\\.$#"
			count: 1
			path: app/Podcasts/Track.php

		-
			message: "#^If condition is always false\\.$#"
			count: 1
			path: app/Podcasts/Track.php

		-
			message: "#^Parameter \\#1 \\$num of function floor expects float\\|int, string given\\.$#"
			count: 2
			path: app/Podcasts/Track.php

		-
			message: "#^Parameter \\#1 \\$string of function str_pad expects string, float given\\.$#"
			count: 2
			path: app/Podcasts/Track.php

		-
			message: "#^Parameter \\#1 \\$string of function str_pad expects string, float\\|int given\\.$#"
			count: 1
			path: app/Podcasts/Track.php

		-
			message: "#^Parameter \\#2 \\$count of static method Illuminate\\\\Support\\\\Str\\:\\:plural\\(\\) expects array\\|Countable\\|int, string given\\.$#"
			count: 3
			path: app/Podcasts/Track.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/Prayers/Controllers/PrayerController.php

		-
			message: "#^Anonymous function has an unused use \\$request\\.$#"
			count: 1
			path: app/Prayers/Controllers/PrayerUpdateController.php

		-
			message: "#^Dead catch \\- Exception is never thrown in the try block\\.$#"
			count: 1
			path: app/Prayers/Controllers/PrayerUpdateController.php

		-
			message: "#^Access to an undefined property App\\\\Prayers\\\\Services\\\\CreatePrayerUpdate\\:\\:\\$prayer_update\\.$#"
			count: 1
			path: app/Prayers/Services/CreatePrayerUpdate.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/Reports/Controllers/AnniversaryReportController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 2
			path: app/Reports/Controllers/AnniversaryReportController.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 1
			path: app/Reports/Controllers/AttendanceReportController.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: app/Reports/Controllers/AttendanceReportController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/Reports/Controllers/BirthdayReportController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 2
			path: app/Reports/Controllers/BirthdayReportController.php

		-
			message: "#^Anonymous function has an unused use \\$less_days_ago\\.$#"
			count: 1
			path: app/Reports/Controllers/MissingPersonReportController.php

		-
			message: "#^Relation 'attendance' is not found in App\\\\Users\\\\User model\\.$#"
			count: 2
			path: app/Reports/Controllers/MissingPersonReportController.php

		-
			message: "#^Relation 'groups' is not found in App\\\\Users\\\\User model\\.$#"
			count: 2
			path: app/Reports/Controllers/MissingPersonReportController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/Sermons/Controllers/SermonController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withSermon\\(\\)\\.$#"
			count: 1
			path: app/Sermons/Controllers/SermonController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 1
			path: app/Sermons/Controllers/SermonController.php

		-
			message: "#^Parameter \\$user of method App\\\\Sermons\\\\File\\:\\:scopeVisibleTo\\(\\) has invalid type App\\\\Sermons\\\\User\\.$#"
			count: 1
			path: app/Sermons/File.php

		-
			message: "#^Access to an undefined property App\\\\Sermons\\\\Sermon\\:\\:\\$files\\.$#"
			count: 1
			path: app/Sermons/Sermon.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/Sermons/Services/CreateSermon.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: app/Sermons/Services/UpdateSermon.php

		-
			message: "#^Property App\\\\Accounts\\\\AccountSettingValue\\:\\:\\$enable_for_admin \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Super/Controllers/SettingController.php

		-
			message: "#^Property App\\\\Accounts\\\\AccountSettingValue\\:\\:\\$enable_for_member \\(bool\\) does not accept int\\.$#"
			count: 1
			path: app/Super/Controllers/SettingController.php

		-
			message: "#^Property App\\\\Accounts\\\\AccountSettingValue\\:\\:\\$value \\(string\\|null\\) does not accept int\\.$#"
			count: 1
			path: app/Super/Controllers/SettingController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Address\\:\\:\\$user\\.$#"
			count: 3
			path: app/Users/<USER>

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 3
			path: app/Users/<USER>/AddressController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 2
			path: app/Users/<USER>/EmailController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$groups\\.$#"
			count: 1
			path: app/Users/<USER>/GroupAdminController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\|Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<int, App\\\\Users\\\\User\\>\\:\\:\\$groups\\.$#"
			count: 1
			path: app/Users/<USER>/GroupAdminController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 2
			path: app/Users/<USER>/GroupAdminController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 2
			path: app/Users/<USER>/GroupController.php

		-
			message: "#^Relation 'messageTypes' is not found in App\\\\Users\\\\Group model\\.$#"
			count: 1
			path: app/Users/<USER>/GroupController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 1
			path: app/Users/<USER>/GroupMessageHistoryController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withMessage\\(\\)\\.$#"
			count: 1
			path: app/Users/<USER>/GroupMessageHistoryController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$group\\.$#"
			count: 1
			path: app/Users/<USER>/GroupSendMessageController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageProvider\\.$#"
			count: 1
			path: app/Users/<USER>/GroupSendMessageController.php

		-
			message: "#^Access to an undefined property App\\\\Messages\\\\MessageHandler\\:\\:\\$messageType\\.$#"
			count: 1
			path: app/Users/<USER>/GroupSendMessageController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Users/<USER>/GroupSendMessageController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 2
			path: app/Users/<USER>/GroupSendMessageController.php

		-
			message: "#^Relation 'account' is not found in App\\\\Messages\\\\MessageHandler model\\.$#"
			count: 1
			path: app/Users/<USER>/GroupSendMessageController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withGroup\\(\\)\\.$#"
			count: 2
			path: app/Users/<USER>/GroupSenderController.php

		-
			message: "#^Relation 'areas' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 1
			path: app/Users/<USER>/InvolvementController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 2
			path: app/Users/<USER>/PhoneController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$photos\\.$#"
			count: 1
			path: app/Users/<USER>/PhotoController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 1
			path: app/Users/<USER>/ProfileController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 2
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$emails\\.$#"
			count: 1
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$paymentMethods\\.$#"
			count: 1
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\View\\\\View\\:\\:withUser\\(\\)\\.$#"
			count: 1
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Relation 'addresses' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Relation 'areas' is not found in App\\\\Involvement\\\\Category model\\.$#"
			count: 1
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Relation 'emails' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Relation 'phones' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Relation 'type' is not found in App\\\\Attendance\\\\Attendance model\\.$#"
			count: 2
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Relation 'user' is not found in App\\\\Attendance\\\\Attendance model\\.$#"
			count: 2
			path: app/Users/<USER>/UserController.php

		-
			message: "#^Call to method withUsers\\(\\) on an unknown class App\\\\Users\\\\Services\\\\MailingLabels\\.$#"
			count: 1
			path: app/Users/<USER>/UserDownloadController.php

		-
			message: "#^Call to method withUsers\\(\\) on an unknown class App\\\\Users\\\\Services\\\\MemberDirectory\\.$#"
			count: 1
			path: app/Users/<USER>/UserDownloadController.php

		-
			message: "#^Instantiated class App\\\\Users\\\\Services\\\\MailingLabels not found\\.$#"
			count: 1
			path: app/Users/<USER>/UserDownloadController.php

		-
			message: "#^Instantiated class App\\\\Users\\\\Services\\\\MemberDirectory not found\\.$#"
			count: 1
			path: app/Users/<USER>/UserDownloadController.php

		-
			message: "#^Class App\\\\Users\\\\UserPhone not found\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Group\\:\\:\\$messageTypes\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Call to an undefined method App\\\\Users\\\\Group\\:\\:receivers\\(\\)\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Relation 'users' is not found in App\\\\Users\\\\Group model\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Parameter \\#2 \\$replace of function str_ireplace expects array\\|string, int given\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Photo\\:\\:\\$is_family\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Address\\:\\:\\$user\\.$#"
			count: 4
			path: app/Users/<USER>/AddressPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Email\\:\\:\\$user\\.$#"
			count: 4
			path: app/Users/<USER>/EmailPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Phone\\:\\:\\$user\\.$#"
			count: 3
			path: app/Users/<USER>/PhonePolicy.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\Photo\\:\\:\\$user\\.$#"
			count: 5
			path: app/Users/<USER>/PhotoPolicy.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Users/<USER>/CreateContribution.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Users/<USER>/CreatePaymentMethod.php

		-
			message: "#^Undefined variable\\: \\$user$#"
			count: 1
			path: app/Users/<USER>/UpdateUserPhoto.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$email\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$email_for_password_reset\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$family\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$groups\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$parents\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$roles\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to property \\$id on an unknown class App\\\\Users\\\\Traits\\\\Role\\.$#"
			count: 2
			path: app/Users/<USER>

		-
			message: "#^Class App\\\\Users\\\\Traits\\\\Interaction not found\\.$#"
			count: 5
			path: app/Users/<USER>

		-
			message: "#^Parameter \\$role of method App\\\\Users\\\\User\\:\\:scopeDoesntHaveRole\\(\\) has invalid type App\\\\Users\\\\Traits\\\\Role\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Parameter \\$role of method App\\\\Users\\\\User\\:\\:scopeHasRole\\(\\) has invalid type App\\\\Users\\\\Traits\\\\Role\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Relation 'user' is not found in App\\\\Users\\\\Address model\\.$#"
			count: 2
			path: app/Users/<USER>

		-
			message: "#^Relation 'user' is not found in App\\\\Users\\\\Email model\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Using nullsafe method call on non\\-nullable type mixed\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Using nullsafe property access on non\\-nullable type mixed\\. Use \\-\\> instead\\.$#"
			count: 1
			path: app/Users/<USER>

		-
			message: "#^Access to an undefined property App\\\\Visitors\\\\Visitor\\:\\:\\$family\\.$#"
			count: 1
			path: app/Visitors/Visitor.php

		-
			message: "#^Access to an undefined property App\\\\Visitors\\\\Visitor\\:\\:\\$user\\.$#"
			count: 1
			path: app/Visitors/Visitor.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 5
			path: app/WorshipAssignments/Controllers/WorshipAssignmentController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/WorshipAssignments/Controllers/WorshipAssignmentGroupController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 1
			path: app/WorshipAssignments/Controllers/WorshipAssignmentPeriodController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/WorshipAssignments/Controllers/WorshipAssignmentPickController.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Pick\\:\\:\\$user\\.$#"
			count: 1
			path: app/WorshipAssignments/Controllers/WorshipAssignmentPickController.php

		-
			message: "#^Parameter \\#1 \\$content of function response expects array\\|Illuminate\\\\Contracts\\\\View\\\\View\\|string\\|null, int given\\.$#"
			count: 4
			path: app/WorshipAssignments/Controllers/WorshipAssignmentPickController.php

		-
			message: "#^Access to an undefined property App\\\\Users\\\\User\\:\\:\\$account\\.$#"
			count: 3
			path: app/WorshipAssignments/Controllers/WorshipAssignmentPositionController.php

		-
			message: "#^Parameter \\#1 \\$content of function response expects array\\|Illuminate\\\\Contracts\\\\View\\\\View\\|string\\|null, int given\\.$#"
			count: 1
			path: app/WorshipAssignments/Controllers/WorshipAssignmentPositionController.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Pick\\:\\:\\$period\\.$#"
			count: 1
			path: app/WorshipAssignments/Pick.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Pick\\:\\:\\$position\\.$#"
			count: 33
			path: app/WorshipAssignments/Pick.php

		-
			message: "#^Left side of && is always false\\.$#"
			count: 2
			path: app/WorshipAssignments/Pick.php

		-
			message: "#^Negated boolean expression is always true\\.$#"
			count: 4
			path: app/WorshipAssignments/Pick.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Pick\\:\\:\\$user\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Position\\:\\:\\$account\\.$#"
			count: 4
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Position\\:\\:\\$involvementArea\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Position\\:\\:\\$involvementCategory\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Access to an undefined property App\\\\WorshipAssignments\\\\Position\\:\\:\\$involvementSubarea\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Elseif condition is always true\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Relation 'involvementAreas' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Relation 'involvementCategories' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Relation 'involvementSubareas' is not found in App\\\\Users\\\\User model\\.$#"
			count: 1
			path: app/WorshipAssignments/Position.php

		-
			message: "#^Relation 'worshipAssignmentPicks' is not found in App\\\\Users\\\\User model\\.$#"
			count: 2
			path: app/WorshipAssignments/Position.php
